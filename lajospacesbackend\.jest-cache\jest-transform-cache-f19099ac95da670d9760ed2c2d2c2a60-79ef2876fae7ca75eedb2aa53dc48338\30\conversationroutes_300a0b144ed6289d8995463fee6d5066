8dd0ab42bb63abe825b780cb2472ef6f
"use strict";

/* istanbul ignore next */
function cov_fl3ndj0gj() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\conversation.routes.ts";
  var hash = "3e69c77e7151a97bc7ba75982bd242438c651588";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\conversation.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 34
        },
        end: {
          line: 4,
          column: 83
        }
      },
      "3": {
        start: {
          line: 5,
          column: 29
        },
        end: {
          line: 5,
          column: 73
        }
      },
      "4": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 6,
          column: 44
        }
      },
      "5": {
        start: {
          line: 7,
          column: 21
        },
        end: {
          line: 7,
          column: 56
        }
      },
      "6": {
        start: {
          line: 8,
          column: 34
        },
        end: {
          line: 8,
          column: 82
        }
      },
      "7": {
        start: {
          line: 9,
          column: 15
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 32
        }
      },
      "9": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 158
        }
      },
      "10": {
        start: {
          line: 23,
          column: 0
        },
        end: {
          line: 23,
          column: 159
        }
      },
      "11": {
        start: {
          line: 29,
          column: 0
        },
        end: {
          line: 29,
          column: 108
        }
      },
      "12": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 202
        }
      },
      "13": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 117
        }
      },
      "14": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 110
        }
      },
      "15": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 117
        }
      },
      "16": {
        start: {
          line: 60,
          column: 0
        },
        end: {
          line: 60,
          column: 230
        }
      },
      "17": {
        start: {
          line: 66,
          column: 0
        },
        end: {
          line: 66,
          column: 217
        }
      },
      "18": {
        start: {
          line: 72,
          column: 0
        },
        end: {
          line: 72,
          column: 276
        }
      },
      "19": {
        start: {
          line: 78,
          column: 0
        },
        end: {
          line: 78,
          column: 283
        }
      },
      "20": {
        start: {
          line: 84,
          column: 0
        },
        end: {
          line: 84,
          column: 289
        }
      },
      "21": {
        start: {
          line: 90,
          column: 0
        },
        end: {
          line: 90,
          column: 200
        }
      },
      "22": {
        start: {
          line: 96,
          column: 0
        },
        end: {
          line: 96,
          column: 228
        }
      },
      "23": {
        start: {
          line: 102,
          column: 0
        },
        end: {
          line: 102,
          column: 160
        }
      },
      "24": {
        start: {
          line: 103,
          column: 0
        },
        end: {
          line: 103,
          column: 25
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\conversation.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,oFAQgD;AAChD,0EAS2C;AAC3C,6CAAkD;AAClD,yDAA6E;AAC7E,mFAW+C;AAE/C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,iDAAiD;AACjD,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,GAAG,EACH,IAAA,4BAAe,EAAC,kDAAwB,EAAE,MAAM,CAAC,EACjD,4CAAkB,CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,GAAG,EACH,IAAA,4BAAe,EAAC,iDAAuB,EAAE,OAAO,CAAC,EACjD,8CAAoB,CACrB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,MAAM,EACN,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,6CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,MAAM,EACN,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,IAAA,4BAAe,EAAC,kDAAwB,EAAE,MAAM,CAAC,EACjD,4CAAkB,CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,cAAc,EACd,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,6CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,MAAM,EACN,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,4CAAkB,CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,WAAW,EACX,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,gDAAsB,CACvB,CAAC;AAEF,iBAAiB;AAEjB;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,2BAA2B,EAC3B,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,4BAAe,EAAC,4CAAkB,EAAE,OAAO,CAAC,EAC5C,4CAAuB,CACxB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,2BAA2B,EAC3B,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,4BAAe,EAAC,2CAAiB,EAAE,MAAM,CAAC,EAC1C,gCAAW,CACZ,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,sCAAsC,EACtC,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,6BAAgB,EAAC,WAAW,CAAC,EAC7B,IAAA,4BAAe,EAAC,2CAAiB,EAAE,MAAM,CAAC,EAC1C,gCAAW,CACZ,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,sCAAsC,EACtC,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,6BAAgB,EAAC,WAAW,CAAC,EAC7B,IAAA,4BAAe,EAAC,6CAAmB,EAAE,MAAM,CAAC,EAC5C,kCAAa,CACd,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,4CAA4C,EAC5C,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,6BAAgB,EAAC,WAAW,CAAC,EAC7B,IAAA,4BAAe,EAAC,8CAAoB,EAAE,MAAM,CAAC,EAC7C,mCAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,4CAA4C,EAC5C,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,6BAAgB,EAAC,WAAW,CAAC,EAC7B,mCAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,gCAAgC,EAChC,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAA,4BAAe,EAAC,0CAAgB,EAAE,MAAM,CAAC,EACzC,uCAAkB,CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,kBAAkB,EAClB,IAAA,4BAAe,EAAC,8CAAoB,EAAE,OAAO,CAAC,EAC9C,mCAAc,CACf,CAAC;AAEF,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\conversation.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  createConversation,\r\n  getUserConversations,\r\n  getConversationById,\r\n  updateConversation,\r\n  archiveConversation,\r\n  deleteConversation,\r\n  toggleMuteConversation\r\n} from '../controllers/conversation.controller';\r\nimport {\r\n  getConversationMessages,\r\n  sendMessage,\r\n  editMessage,\r\n  deleteMessage,\r\n  reactToMessage,\r\n  removeReaction,\r\n  markMessagesAsRead,\r\n  searchMessages\r\n} from '../controllers/message.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  createConversationSchema,\r\n  updateConversationSchema,\r\n  sendMessageSchema,\r\n  editMessageSchema,\r\n  deleteMessageSchema,\r\n  reactToMessageSchema,\r\n  markAsReadSchema,\r\n  searchMessagesSchema,\r\n  conversationQuerySchema,\r\n  messageQuerySchema\r\n} from '../validators/conversation.validators';\r\n\r\nconst router = Router();\r\n\r\n// All conversation routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   POST /api/conversations\r\n * @desc    Create a new conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/',\r\n  validateRequest(createConversationSchema, 'body'),\r\n  createConversation\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations\r\n * @desc    Get user's conversations\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(conversationQuerySchema, 'query'),\r\n  getUserConversations\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations/:id\r\n * @desc    Get conversation by ID\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getConversationById\r\n);\r\n\r\n/**\r\n * @route   PUT /api/conversations/:id\r\n * @desc    Update conversation\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  validateRequest(updateConversationSchema, 'body'),\r\n  updateConversation\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:id/archive\r\n * @desc    Archive conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:id/archive',\r\n  validateObjectId('id'),\r\n  archiveConversation\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:id\r\n * @desc    Delete conversation\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  deleteConversation\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:id/mute\r\n * @desc    Mute/unmute conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:id/mute',\r\n  validateObjectId('id'),\r\n  toggleMuteConversation\r\n);\r\n\r\n// Message routes\r\n\r\n/**\r\n * @route   GET /api/conversations/:conversationId/messages\r\n * @desc    Get messages for a conversation\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:conversationId/messages',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(messageQuerySchema, 'query'),\r\n  getConversationMessages\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages\r\n * @desc    Send a message\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(sendMessageSchema, 'body'),\r\n  sendMessage\r\n);\r\n\r\n/**\r\n * @route   PUT /api/conversations/:conversationId/messages/:messageId\r\n * @desc    Edit a message\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:conversationId/messages/:messageId',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(editMessageSchema, 'body'),\r\n  editMessage\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:conversationId/messages/:messageId\r\n * @desc    Delete a message\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:conversationId/messages/:messageId',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(deleteMessageSchema, 'body'),\r\n  deleteMessage\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages/:messageId/react\r\n * @desc    React to a message\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages/:messageId/react',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(reactToMessageSchema, 'body'),\r\n  reactToMessage\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:conversationId/messages/:messageId/react\r\n * @desc    Remove reaction from message\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:conversationId/messages/:messageId/react',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  removeReaction\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages/read\r\n * @desc    Mark messages as read\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages/read',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(markAsReadSchema, 'body'),\r\n  markMessagesAsRead\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations/search/messages\r\n * @desc    Search messages across conversations\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/search/messages',\r\n  validateRequest(searchMessagesSchema, 'query'),\r\n  searchMessages\r\n);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3e69c77e7151a97bc7ba75982bd242438c651588"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_fl3ndj0gj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_fl3ndj0gj();
cov_fl3ndj0gj().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[1]++, require("express"));
const conversation_controller_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[2]++, require("../controllers/conversation.controller"));
const message_controller_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[3]++, require("../controllers/message.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[4]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[5]++, require("../middleware/validation"));
const conversation_validators_1 =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[6]++, require("../validators/conversation.validators"));
const router =
/* istanbul ignore next */
(cov_fl3ndj0gj().s[7]++, (0, express_1.Router)());
// All conversation routes require authentication
/* istanbul ignore next */
cov_fl3ndj0gj().s[8]++;
router.use(auth_1.authenticate);
/**
 * @route   POST /api/conversations
 * @desc    Create a new conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[9]++;
router.post('/', (0, validation_1.validateRequest)(conversation_validators_1.createConversationSchema, 'body'), conversation_controller_1.createConversation);
/**
 * @route   GET /api/conversations
 * @desc    Get user's conversations
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[10]++;
router.get('/', (0, validation_1.validateRequest)(conversation_validators_1.conversationQuerySchema, 'query'), conversation_controller_1.getUserConversations);
/**
 * @route   GET /api/conversations/:id
 * @desc    Get conversation by ID
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[11]++;
router.get('/:id', (0, validation_1.validateObjectId)('id'), conversation_controller_1.getConversationById);
/**
 * @route   PUT /api/conversations/:id
 * @desc    Update conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[12]++;
router.put('/:id', (0, validation_1.validateObjectId)('id'), (0, validation_1.validateRequest)(conversation_validators_1.updateConversationSchema, 'body'), conversation_controller_1.updateConversation);
/**
 * @route   POST /api/conversations/:id/archive
 * @desc    Archive conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[13]++;
router.post('/:id/archive', (0, validation_1.validateObjectId)('id'), conversation_controller_1.archiveConversation);
/**
 * @route   DELETE /api/conversations/:id
 * @desc    Delete conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[14]++;
router.delete('/:id', (0, validation_1.validateObjectId)('id'), conversation_controller_1.deleteConversation);
/**
 * @route   POST /api/conversations/:id/mute
 * @desc    Mute/unmute conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[15]++;
router.post('/:id/mute', (0, validation_1.validateObjectId)('id'), conversation_controller_1.toggleMuteConversation);
// Message routes
/**
 * @route   GET /api/conversations/:conversationId/messages
 * @desc    Get messages for a conversation
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[16]++;
router.get('/:conversationId/messages', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateRequest)(conversation_validators_1.messageQuerySchema, 'query'), message_controller_1.getConversationMessages);
/**
 * @route   POST /api/conversations/:conversationId/messages
 * @desc    Send a message
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[17]++;
router.post('/:conversationId/messages', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateRequest)(conversation_validators_1.sendMessageSchema, 'body'), message_controller_1.sendMessage);
/**
 * @route   PUT /api/conversations/:conversationId/messages/:messageId
 * @desc    Edit a message
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[18]++;
router.put('/:conversationId/messages/:messageId', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateObjectId)('messageId'), (0, validation_1.validateRequest)(conversation_validators_1.editMessageSchema, 'body'), message_controller_1.editMessage);
/**
 * @route   DELETE /api/conversations/:conversationId/messages/:messageId
 * @desc    Delete a message
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[19]++;
router.delete('/:conversationId/messages/:messageId', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateObjectId)('messageId'), (0, validation_1.validateRequest)(conversation_validators_1.deleteMessageSchema, 'body'), message_controller_1.deleteMessage);
/**
 * @route   POST /api/conversations/:conversationId/messages/:messageId/react
 * @desc    React to a message
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[20]++;
router.post('/:conversationId/messages/:messageId/react', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateObjectId)('messageId'), (0, validation_1.validateRequest)(conversation_validators_1.reactToMessageSchema, 'body'), message_controller_1.reactToMessage);
/**
 * @route   DELETE /api/conversations/:conversationId/messages/:messageId/react
 * @desc    Remove reaction from message
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[21]++;
router.delete('/:conversationId/messages/:messageId/react', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateObjectId)('messageId'), message_controller_1.removeReaction);
/**
 * @route   POST /api/conversations/:conversationId/messages/read
 * @desc    Mark messages as read
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[22]++;
router.post('/:conversationId/messages/read', (0, validation_1.validateObjectId)('conversationId'), (0, validation_1.validateRequest)(conversation_validators_1.markAsReadSchema, 'body'), message_controller_1.markMessagesAsRead);
/**
 * @route   GET /api/conversations/search/messages
 * @desc    Search messages across conversations
 * @access  Private
 */
/* istanbul ignore next */
cov_fl3ndj0gj().s[23]++;
router.get('/search/messages', (0, validation_1.validateRequest)(conversation_validators_1.searchMessagesSchema, 'query'), message_controller_1.searchMessages);
/* istanbul ignore next */
cov_fl3ndj0gj().s[24]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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