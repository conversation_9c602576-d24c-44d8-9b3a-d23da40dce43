ff126c1d44f3957cb7d3ddc2874fd6b3
"use strict";

/* istanbul ignore next */
function cov_q7nj6mqkg() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\auth.routes.ts";
  var hash = "c5b73176eed9b29902565eef177a95277cdd2ff4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\auth.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 26
        },
        end: {
          line: 5,
          column: 66
        }
      },
      "4": {
        start: {
          line: 6,
          column: 26
        },
        end: {
          line: 6,
          column: 66
        }
      },
      "5": {
        start: {
          line: 7,
          column: 26
        },
        end: {
          line: 7,
          column: 67
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 21,
          column: 7
        }
      },
      "9": {
        start: {
          line: 24,
          column: 0
        },
        end: {
          line: 26,
          column: 102
        }
      },
      "10": {
        start: {
          line: 27,
          column: 0
        },
        end: {
          line: 29,
          column: 96
        }
      },
      "11": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 32,
          column: 110
        }
      },
      "12": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 35,
          column: 114
        }
      },
      "13": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 38,
          column: 112
        }
      },
      "14": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 137
        }
      },
      "15": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 42,
          column: 125
        }
      },
      "16": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 32
        }
      },
      "17": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 49
        }
      },
      "18": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 56
        }
      },
      "19": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 146
        }
      },
      "20": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 48,
          column: 53
        }
      },
      "21": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 137
        }
      },
      "22": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "23": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 22
          },
          end: {
            line: 10,
            column: 23
          }
        },
        loc: {
          start: {
            line: 10,
            column: 37
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\auth.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,6CAAkD;AAClD,mEAAgE;AAChE,mEAUuC;AACvC,oEAcwC;AAExC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,eAAe;AACf,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAClC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,qBAAqB;QAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE;YACT,QAAQ,EAAE,gBAAgB;YAC1B,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,eAAe;YACxB,MAAM,EAAE,cAAc;YACtB,OAAO,EAAE,cAAc;SACxB;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,iFAAiF;AACjF,MAAM,CAAC,IAAI,CAAC,WAAW;AACrB,iEAAiE;AACjE,IAAA,iCAAe,EAAC,gCAAc,CAAC,EAC/B,0BAAQ,CACT,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,QAAQ;AAClB,iEAAiE;AACjE,IAAA,iCAAe,EAAC,6BAAW,CAAC,EAC5B,uBAAK,CACN,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,UAAU;AACpB,mEAAmE;AACnE,IAAA,iCAAe,EAAC,oCAAkB,CAAC,EACnC,8BAAY,CACb,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,kBAAkB;AAC5B,iEAAiE;AACjE,IAAA,iCAAe,EAAC,sCAAoB,CAAC,EACrC,gCAAc,CACf,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,iBAAiB;AAC3B,iEAAiE;AACjE,IAAA,iCAAe,EAAC,qCAAmB,CAAC,EACpC,+BAAa,CACd,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,eAAe,EACzB,IAAA,iCAAe,EAAC,mCAAiB,CAAC,EAClC,6BAAW,CACZ,CAAC;AAEF,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAChC,iEAAiE;AACjE,IAAA,iCAAe,EAAC,0CAAwB,CAAC,EACzC,uCAAqB,CACtB,CAAC;AAEF,6CAA6C;AAC7C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC,CAAC,0CAA0C;AAEpE,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAM,CAAC,CAAC;AAE/B,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,2BAAS,CAAC,CAAC;AAEtC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAC5B,IAAA,iCAAe,EAAC,sCAAoB,CAAC,EACrC,gCAAc,CACf,CAAC;AAEF,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,4BAAU,CAAC,CAAC;AAEnC,MAAM,CAAC,KAAK,CAAC,UAAU,EACrB,IAAA,iCAAe,EAAC,qCAAmB,CAAC,EACpC,+BAAa,CACd,CAAC;AAEF,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,mCAAiB,CAAC,CAAC;AAEhD,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\auth.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest } from '../validators/auth.validators';\r\nimport {\r\n  registerSchema,\r\n  loginSchema,\r\n  forgotPasswordSchema,\r\n  resetPasswordSchema,\r\n  changePasswordSchema,\r\n  verifyEmailSchema,\r\n  resendVerificationSchema,\r\n  refreshTokenSchema,\r\n  updateProfileSchema\r\n} from '../validators/auth.validators';\r\nimport {\r\n  register,\r\n  login,\r\n  refreshToken,\r\n  logout,\r\n  logoutAll,\r\n  sendEmailVerification,\r\n  verifyEmail,\r\n  forgotPassword,\r\n  resetPassword,\r\n  changePassword,\r\n  getProfile,\r\n  updateProfile,\r\n  deactivateAccount\r\n} from '../controllers/auth.controller';\r\n\r\nconst router = Router();\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({\r\n    message: 'Auth routes working',\r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      register: 'POST /register',\r\n      login: 'POST /login',\r\n      refresh: 'POST /refresh',\r\n      logout: 'POST /logout',\r\n      profile: 'GET /profile'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes (no authentication required) - temporarily without rate limiting\r\nrouter.post('/register',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(registerSchema),\r\n  register\r\n);\r\n\r\nrouter.post('/login',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(loginSchema),\r\n  login\r\n);\r\n\r\nrouter.post('/refresh',\r\n  // authRateLimit(10, 15 * 60 * 1000), // 10 attempts per 15 minutes\r\n  validateRequest(refreshTokenSchema),\r\n  refreshToken\r\n);\r\n\r\nrouter.post('/forgot-password',\r\n  // authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes\r\n  validateRequest(forgotPasswordSchema),\r\n  forgotPassword\r\n);\r\n\r\nrouter.post('/reset-password',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(resetPasswordSchema),\r\n  resetPassword\r\n);\r\n\r\nrouter.post('/verify-email',\r\n  validateRequest(verifyEmailSchema),\r\n  verifyEmail\r\n);\r\n\r\nrouter.post('/resend-verification',\r\n  // authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes\r\n  validateRequest(resendVerificationSchema),\r\n  sendEmailVerification\r\n);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate); // All routes below require authentication\r\n\r\nrouter.post('/logout', logout);\r\n\r\nrouter.post('/logout-all', logoutAll);\r\n\r\nrouter.post('/change-password',\r\n  validateRequest(changePasswordSchema),\r\n  changePassword\r\n);\r\n\r\nrouter.get('/profile', getProfile);\r\n\r\nrouter.patch('/profile',\r\n  validateRequest(updateProfileSchema),\r\n  updateProfile\r\n);\r\n\r\nrouter.delete('/deactivate', deactivateAccount);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c5b73176eed9b29902565eef177a95277cdd2ff4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_q7nj6mqkg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_q7nj6mqkg();
cov_q7nj6mqkg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[1]++, require("express"));
const auth_1 =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[2]++, require("../middleware/auth"));
const auth_validators_1 =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[3]++, require("../validators/auth.validators"));
const auth_validators_2 =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[4]++, require("../validators/auth.validators"));
const auth_controller_1 =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[5]++, require("../controllers/auth.controller"));
const router =
/* istanbul ignore next */
(cov_q7nj6mqkg().s[6]++, (0, express_1.Router)());
// Health check
/* istanbul ignore next */
cov_q7nj6mqkg().s[7]++;
router.get('/health', (_req, res) => {
  /* istanbul ignore next */
  cov_q7nj6mqkg().f[0]++;
  cov_q7nj6mqkg().s[8]++;
  res.json({
    message: 'Auth routes working',
    timestamp: new Date().toISOString(),
    endpoints: {
      register: 'POST /register',
      login: 'POST /login',
      refresh: 'POST /refresh',
      logout: 'POST /logout',
      profile: 'GET /profile'
    }
  });
});
// Public routes (no authentication required) - temporarily without rate limiting
/* istanbul ignore next */
cov_q7nj6mqkg().s[9]++;
router.post('/register',
// authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.registerSchema), auth_controller_1.register);
/* istanbul ignore next */
cov_q7nj6mqkg().s[10]++;
router.post('/login',
// authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.loginSchema), auth_controller_1.login);
/* istanbul ignore next */
cov_q7nj6mqkg().s[11]++;
router.post('/refresh',
// authRateLimit(10, 15 * 60 * 1000), // 10 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.refreshTokenSchema), auth_controller_1.refreshToken);
/* istanbul ignore next */
cov_q7nj6mqkg().s[12]++;
router.post('/forgot-password',
// authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.forgotPasswordSchema), auth_controller_1.forgotPassword);
/* istanbul ignore next */
cov_q7nj6mqkg().s[13]++;
router.post('/reset-password',
// authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.resetPasswordSchema), auth_controller_1.resetPassword);
/* istanbul ignore next */
cov_q7nj6mqkg().s[14]++;
router.post('/verify-email', (0, auth_validators_1.validateRequest)(auth_validators_2.verifyEmailSchema), auth_controller_1.verifyEmail);
/* istanbul ignore next */
cov_q7nj6mqkg().s[15]++;
router.post('/resend-verification',
// authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes
(0, auth_validators_1.validateRequest)(auth_validators_2.resendVerificationSchema), auth_controller_1.sendEmailVerification);
// Protected routes (authentication required)
/* istanbul ignore next */
cov_q7nj6mqkg().s[16]++;
router.use(auth_1.authenticate); // All routes below require authentication
/* istanbul ignore next */
cov_q7nj6mqkg().s[17]++;
router.post('/logout', auth_controller_1.logout);
/* istanbul ignore next */
cov_q7nj6mqkg().s[18]++;
router.post('/logout-all', auth_controller_1.logoutAll);
/* istanbul ignore next */
cov_q7nj6mqkg().s[19]++;
router.post('/change-password', (0, auth_validators_1.validateRequest)(auth_validators_2.changePasswordSchema), auth_controller_1.changePassword);
/* istanbul ignore next */
cov_q7nj6mqkg().s[20]++;
router.get('/profile', auth_controller_1.getProfile);
/* istanbul ignore next */
cov_q7nj6mqkg().s[21]++;
router.patch('/profile', (0, auth_validators_1.validateRequest)(auth_validators_2.updateProfileSchema), auth_controller_1.updateProfile);
/* istanbul ignore next */
cov_q7nj6mqkg().s[22]++;
router.delete('/deactivate', auth_controller_1.deactivateAccount);
/* istanbul ignore next */
cov_q7nj6mqkg().s[23]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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