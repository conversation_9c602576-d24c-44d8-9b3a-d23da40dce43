{"version": 3, "names": ["cov_j4xl9pnxc", "actualCoverage", "exports", "bypassRateLimitForTrustedIPs", "s", "getRateLimitStats", "cleanupRateLimitData", "express_rate_limit_1", "__importDefault", "require", "redis_1", "environment_1", "logger_1", "redisClient", "createClient", "url", "config", "REDIS_URL", "socket", "connectTimeout", "lazyConnect", "rateLimitRedisClient", "on", "err", "f", "logger", "error", "info", "redisConnected", "redisInitializing", "initializeRedis", "b", "isOpen", "connect", "rateLimitConfigs", "general", "windowMs", "max", "message", "retryAfter", "standardHeaders", "legacyHeaders", "skip", "req", "path", "auth", "skipSuccessfulRequests", "passwordReset", "email", "upload", "search", "admin", "createRateLimiter", "name", "default", "store", "undefined", "keyGenerator", "userId", "user", "_id", "ip", "connection", "remoteAddress", "handler", "res", "warn", "method", "userAgent", "get", "status", "json", "success", "timestamp", "Date", "toISOString", "generalRateLimit", "authRateLimit", "passwordResetRateLimit", "emailRateLimit", "uploadRateLimit", "searchRateLimit", "adminRateLimit", "SlidingWindowRateLimit", "constructor", "windowSize", "maxRequests", "keyPrefix", "isAllowed", "identifier", "allowed", "remaining", "resetTime", "now", "windowStart", "key", "zRemRangeByScore", "currentRequests", "zCard", "oldestRequest", "zRange", "withScores", "length", "parseInt", "score", "zAdd", "value", "Math", "random", "expire", "ceil", "middleware", "next", "result", "set", "toString", "criticalOperationsRateLimit", "userActionsRateLimit", "trustedIPs", "Set", "clientIP", "has", "timeframe", "keys", "stats", "count", "limiterName", "split", "windowEnd", "del"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\rateLimiting.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport rateLimit from 'express-rate-limit';\r\nimport RedisStore from 'rate-limit-redis';\r\nimport { createClient } from 'redis';\r\nimport { config } from '../config/environment';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\n// Create Redis client for rate limiting\r\nconst redisClient = createClient({\r\n  url: config.REDIS_URL,\r\n  socket: {\r\n    connectTimeout: 5000,\r\n    lazyConnect: true\r\n  }\r\n});\r\n\r\nredisClient.on('error', (err) => {\r\n  logger.error('Redis rate limiting client error:', err);\r\n});\r\n\r\nredisClient.on('connect', () => {\r\n  logger.info('Redis rate limiting client connected');\r\n});\r\n\r\n// Initialize Redis connection (lazy initialization)\r\nlet redisConnected = false;\r\nlet redisInitializing = false;\r\n\r\nconst initializeRedis = async () => {\r\n  if (redisConnected || redisInitializing) return;\r\n\r\n  redisInitializing = true;\r\n  try {\r\n    if (!redisClient.isOpen) {\r\n      await redisClient.connect();\r\n    }\r\n    redisConnected = true;\r\n    logger.info('Redis rate limiting initialized successfully');\r\n  } catch (error) {\r\n    logger.error('Failed to initialize Redis for rate limiting:', error);\r\n    redisConnected = false;\r\n  } finally {\r\n    redisInitializing = false;\r\n  }\r\n};\r\n\r\n// Rate limiting configurations\r\nexport const rateLimitConfigs = {\r\n  // General API rate limiting\r\n  general: {\r\n    windowMs: 15 * 60 * 1000, // 15 minutes\r\n    max: 1000, // Limit each IP to 1000 requests per windowMs\r\n    message: {\r\n      error: 'Too many requests from this IP, please try again later.',\r\n      retryAfter: '15 minutes'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false,\r\n    skip: (req: Request) => {\r\n      // Skip rate limiting for health checks\r\n      return req.path === '/api/health' || req.path === '/health';\r\n    }\r\n  },\r\n\r\n  // Authentication endpoints (stricter)\r\n  auth: {\r\n    windowMs: 15 * 60 * 1000, // 15 minutes\r\n    max: 20, // Limit each IP to 20 auth requests per windowMs\r\n    message: {\r\n      error: 'Too many authentication attempts, please try again later.',\r\n      retryAfter: '15 minutes'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false,\r\n    skipSuccessfulRequests: true // Don't count successful requests\r\n  },\r\n\r\n  // Password reset (very strict)\r\n  passwordReset: {\r\n    windowMs: 60 * 60 * 1000, // 1 hour\r\n    max: 5, // Limit each IP to 5 password reset requests per hour\r\n    message: {\r\n      error: 'Too many password reset attempts, please try again later.',\r\n      retryAfter: '1 hour'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false\r\n  },\r\n\r\n  // Email sending (strict)\r\n  email: {\r\n    windowMs: 60 * 60 * 1000, // 1 hour\r\n    max: 50, // Limit each IP to 50 email requests per hour\r\n    message: {\r\n      error: 'Too many email requests, please try again later.',\r\n      retryAfter: '1 hour'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false\r\n  },\r\n\r\n  // File uploads (moderate)\r\n  upload: {\r\n    windowMs: 15 * 60 * 1000, // 15 minutes\r\n    max: 100, // Limit each IP to 100 upload requests per windowMs\r\n    message: {\r\n      error: 'Too many upload requests, please try again later.',\r\n      retryAfter: '15 minutes'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false\r\n  },\r\n\r\n  // Search endpoints (moderate)\r\n  search: {\r\n    windowMs: 1 * 60 * 1000, // 1 minute\r\n    max: 60, // Limit each IP to 60 search requests per minute\r\n    message: {\r\n      error: 'Too many search requests, please slow down.',\r\n      retryAfter: '1 minute'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false\r\n  },\r\n\r\n  // Admin endpoints (very strict)\r\n  admin: {\r\n    windowMs: 15 * 60 * 1000, // 15 minutes\r\n    max: 100, // Limit each IP to 100 admin requests per windowMs\r\n    message: {\r\n      error: 'Too many admin requests, please try again later.',\r\n      retryAfter: '15 minutes'\r\n    },\r\n    standardHeaders: true,\r\n    legacyHeaders: false\r\n  }\r\n};\r\n\r\n// Create rate limiter with Redis store\r\nfunction createRateLimiter(config: any, name: string) {\r\n  return rateLimit({\r\n    ...config,\r\n    store: undefined, // Use memory store for now to avoid startup issues\r\n    keyGenerator: (req: Request) => {\r\n      // Use user ID if authenticated, otherwise use IP\r\n      const userId = req.user?._id;\r\n      const ip = req.ip || req.connection.remoteAddress || 'unknown';\r\n      return userId ? `user:${userId}` : `ip:${ip}`;\r\n    },\r\n    handler: (req: Request, res: Response) => {\r\n      logger.warn(`Rate limit exceeded for ${name}`, {\r\n        ip: req.ip,\r\n        userId: req.user?._id,\r\n        path: req.path,\r\n        method: req.method,\r\n        userAgent: req.get('User-Agent')\r\n      });\r\n\r\n      res.status(429).json({\r\n        success: false,\r\n        error: config.message.error,\r\n        retryAfter: config.message.retryAfter,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }\r\n    // Removed deprecated onLimitReached - functionality moved to handler\r\n  });\r\n}\r\n\r\n// Export rate limiters\r\nexport const generalRateLimit = createRateLimiter(rateLimitConfigs.general, 'general');\r\nexport const authRateLimit = createRateLimiter(rateLimitConfigs.auth, 'auth');\r\nexport const passwordResetRateLimit = createRateLimiter(rateLimitConfigs.passwordReset, 'password-reset');\r\nexport const emailRateLimit = createRateLimiter(rateLimitConfigs.email, 'email');\r\nexport const uploadRateLimit = createRateLimiter(rateLimitConfigs.upload, 'upload');\r\nexport const searchRateLimit = createRateLimiter(rateLimitConfigs.search, 'search');\r\nexport const adminRateLimit = createRateLimiter(rateLimitConfigs.admin, 'admin');\r\n\r\n// Sliding window rate limiter for more sophisticated rate limiting\r\nexport class SlidingWindowRateLimit {\r\n  private redisClient: any;\r\n  private windowSize: number;\r\n  private maxRequests: number;\r\n  private keyPrefix: string;\r\n\r\n  constructor(windowSize: number, maxRequests: number, keyPrefix: string) {\r\n    this.redisClient = redisClient;\r\n    this.windowSize = windowSize;\r\n    this.maxRequests = maxRequests;\r\n    this.keyPrefix = keyPrefix;\r\n  }\r\n\r\n  async isAllowed(identifier: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {\r\n    if (!redisConnected) {\r\n      // Fallback to allowing requests if Redis is not available\r\n      return { allowed: true, remaining: this.maxRequests, resetTime: Date.now() + this.windowSize };\r\n    }\r\n\r\n    const now = Date.now();\r\n    const windowStart = now - this.windowSize;\r\n    const key = `${this.keyPrefix}:${identifier}`;\r\n\r\n    try {\r\n      // Remove expired entries and count current requests\r\n      await this.redisClient.zRemRangeByScore(key, 0, windowStart);\r\n      const currentRequests = await this.redisClient.zCard(key);\r\n\r\n      if (currentRequests >= this.maxRequests) {\r\n        const oldestRequest = await this.redisClient.zRange(key, 0, 0, { withScores: true });\r\n        const resetTime = oldestRequest.length > 0 ? \r\n          parseInt(oldestRequest[0].score) + this.windowSize : \r\n          now + this.windowSize;\r\n\r\n        return {\r\n          allowed: false,\r\n          remaining: 0,\r\n          resetTime\r\n        };\r\n      }\r\n\r\n      // Add current request\r\n      await this.redisClient.zAdd(key, { score: now, value: `${now}-${Math.random()}` });\r\n      await this.redisClient.expire(key, Math.ceil(this.windowSize / 1000));\r\n\r\n      return {\r\n        allowed: true,\r\n        remaining: this.maxRequests - currentRequests - 1,\r\n        resetTime: now + this.windowSize\r\n      };\r\n    } catch (error) {\r\n      logger.error('Sliding window rate limit error:', error);\r\n      // Fallback to allowing requests if Redis operation fails\r\n      return { allowed: true, remaining: this.maxRequests, resetTime: now + this.windowSize };\r\n    }\r\n  }\r\n\r\n  middleware() {\r\n    return async (req: Request, res: Response, next: NextFunction) => {\r\n      const identifier = req.user?._id || req.ip || 'unknown';\r\n      const result = await this.isAllowed(identifier);\r\n\r\n      // Set rate limit headers\r\n      res.set({\r\n        'X-RateLimit-Limit': this.maxRequests.toString(),\r\n        'X-RateLimit-Remaining': result.remaining.toString(),\r\n        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()\r\n      });\r\n\r\n      if (!result.allowed) {\r\n        logger.warn('Sliding window rate limit exceeded', {\r\n          identifier,\r\n          path: req.path,\r\n          method: req.method,\r\n          remaining: result.remaining,\r\n          resetTime: result.resetTime\r\n        });\r\n\r\n        return res.status(429).json({\r\n          success: false,\r\n          error: 'Rate limit exceeded',\r\n          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),\r\n          timestamp: new Date().toISOString()\r\n        });\r\n      }\r\n\r\n      next();\r\n    };\r\n  }\r\n}\r\n\r\n// Create sliding window rate limiters for specific use cases\r\nexport const criticalOperationsRateLimit = new SlidingWindowRateLimit(\r\n  60 * 1000, // 1 minute window\r\n  10, // 10 requests per minute\r\n  'critical-ops'\r\n);\r\n\r\nexport const userActionsRateLimit = new SlidingWindowRateLimit(\r\n  5 * 60 * 1000, // 5 minute window\r\n  100, // 100 requests per 5 minutes\r\n  'user-actions'\r\n);\r\n\r\n// Rate limit bypass for trusted IPs (admin panel, monitoring services)\r\nexport const trustedIPs = new Set([\r\n  '127.0.0.1',\r\n  '::1',\r\n  // Add your trusted IPs here\r\n]);\r\n\r\nexport function bypassRateLimitForTrustedIPs(req: Request, res: Response, next: NextFunction) {\r\n  const clientIP = req.ip || req.connection.remoteAddress;\r\n  \r\n  if (clientIP && trustedIPs.has(clientIP)) {\r\n    logger.info('Rate limit bypassed for trusted IP', { ip: clientIP });\r\n    return next();\r\n  }\r\n  \r\n  next();\r\n}\r\n\r\n// Rate limit monitoring and analytics\r\nexport async function getRateLimitStats(timeframe: 'hour' | 'day' | 'week' = 'hour'): Promise<any> {\r\n  if (!redisConnected) {\r\n    return { error: 'Redis not connected' };\r\n  }\r\n\r\n  const now = Date.now();\r\n  let windowStart: number;\r\n\r\n  switch (timeframe) {\r\n    case 'hour':\r\n      windowStart = now - (60 * 60 * 1000);\r\n      break;\r\n    case 'day':\r\n      windowStart = now - (24 * 60 * 60 * 1000);\r\n      break;\r\n    case 'week':\r\n      windowStart = now - (7 * 24 * 60 * 60 * 1000);\r\n      break;\r\n    default:\r\n      windowStart = now - (60 * 60 * 1000);\r\n  }\r\n\r\n  try {\r\n    const keys = await redisClient.keys('rl:*');\r\n    const stats: any = {};\r\n\r\n    for (const key of keys) {\r\n      const count = await redisClient.get(key);\r\n      if (count) {\r\n        const [, limiterName] = key.split(':');\r\n        stats[limiterName] = (stats[limiterName] || 0) + parseInt(count);\r\n      }\r\n    }\r\n\r\n    return {\r\n      timeframe,\r\n      windowStart: new Date(windowStart).toISOString(),\r\n      windowEnd: new Date(now).toISOString(),\r\n      stats\r\n    };\r\n  } catch (error) {\r\n    logger.error('Error getting rate limit stats:', error);\r\n    return { error: 'Failed to get stats' };\r\n  }\r\n}\r\n\r\n// Cleanup function\r\nexport async function cleanupRateLimitData(): Promise<void> {\r\n  if (!redisConnected) return;\r\n\r\n  try {\r\n    const keys = await redisClient.keys('rl:*');\r\n    if (keys.length > 0) {\r\n      await redisClient.del(keys);\r\n      logger.info(`Cleaned up ${keys.length} rate limit keys`);\r\n    }\r\n  } catch (error) {\r\n    logger.error('Error cleaning up rate limit data:', error);\r\n  }\r\n}\r\n\r\nexport { redisClient as rateLimitRedisClient };\r\n\r\nexport default {\r\n  generalRateLimit,\r\n  authRateLimit,\r\n  passwordResetRateLimit,\r\n  emailRateLimit,\r\n  uploadRateLimit,\r\n  searchRateLimit,\r\n  adminRateLimit,\r\n  SlidingWindowRateLimit,\r\n  criticalOperationsRateLimit,\r\n  userActionsRateLimit,\r\n  bypassRateLimitForTrustedIPs,\r\n  getRateLimitStats,\r\n  cleanupRateLimitData\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUO;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyRPE,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AASC;AAAAH,aAAA,GAAAI,CAAA;AAGDF,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AA4CC;AAAAL,aAAA,GAAAI,CAAA;AAGDF,OAAA,CAAAI,oBAAA,GAAAA,oBAAA;AA7VA,MAAAC,oBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAI,CAAA,OAAAI,eAAA,CAAAC,OAAA;AAEA,MAAAC,OAAA;AAAA;AAAA,CAAAV,aAAA,GAAAI,CAAA,OAAAK,OAAA;AACA,MAAAE,aAAA;AAAA;AAAA,CAAAX,aAAA,GAAAI,CAAA,OAAAK,OAAA;AACA,MAAAG,QAAA;AAAA;AAAA,CAAAZ,aAAA,GAAAI,CAAA,QAAAK,OAAA;AAGA;AACA,MAAMI,WAAW;AAAA;AAAA,CAAAb,aAAA,GAAAI,CAAA,QAAG,IAAAM,OAAA,CAAAI,YAAY,EAAC;EAC/BC,GAAG,EAAEJ,aAAA,CAAAK,MAAM,CAACC,SAAS;EACrBC,MAAM,EAAE;IACNC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE;;CAEhB,CAAC;AAAC;AAAApB,aAAA,GAAAI,CAAA;AA6VqBF,OAAA,CAAAmB,oBAAA,GAAAR,WAAA;AAAoB;AAAAb,aAAA,GAAAI,CAAA;AA3V5CS,WAAW,CAACS,EAAE,CAAC,OAAO,EAAGC,GAAG,IAAI;EAAA;EAAAvB,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EAC9BQ,QAAA,CAAAa,MAAM,CAACC,KAAK,CAAC,mCAAmC,EAAEH,GAAG,CAAC;AACxD,CAAC,CAAC;AAAC;AAAAvB,aAAA,GAAAI,CAAA;AAEHS,WAAW,CAACS,EAAE,CAAC,SAAS,EAAE,MAAK;EAAA;EAAAtB,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EAC7BQ,QAAA,CAAAa,MAAM,CAACE,IAAI,CAAC,sCAAsC,CAAC;AACrD,CAAC,CAAC;AAEF;AACA,IAAIC,cAAc;AAAA;AAAA,CAAA5B,aAAA,GAAAI,CAAA,QAAG,KAAK;AAC1B,IAAIyB,iBAAiB;AAAA;AAAA,CAAA7B,aAAA,GAAAI,CAAA,QAAG,KAAK;AAAC;AAAAJ,aAAA,GAAAI,CAAA;AAE9B,MAAM0B,eAAe,GAAG,MAAAA,CAAA,KAAW;EAAA;EAAA9B,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EACjC;EAAI;EAAA,CAAAJ,aAAA,GAAA+B,CAAA,UAAAH,cAAc;EAAA;EAAA,CAAA5B,aAAA,GAAA+B,CAAA,UAAIF,iBAAiB,GAAE;IAAA;IAAA7B,aAAA,GAAA+B,CAAA;IAAA/B,aAAA,GAAAI,CAAA;IAAA;EAAA,CAAO;EAAA;EAAA;IAAAJ,aAAA,GAAA+B,CAAA;EAAA;EAAA/B,aAAA,GAAAI,CAAA;EAEhDyB,iBAAiB,GAAG,IAAI;EAAC;EAAA7B,aAAA,GAAAI,CAAA;EACzB,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,IAAI,CAACS,WAAW,CAACmB,MAAM,EAAE;MAAA;MAAAhC,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACvB,MAAMS,WAAW,CAACoB,OAAO,EAAE;IAC7B,CAAC;IAAA;IAAA;MAAAjC,aAAA,GAAA+B,CAAA;IAAA;IAAA/B,aAAA,GAAAI,CAAA;IACDwB,cAAc,GAAG,IAAI;IAAC;IAAA5B,aAAA,GAAAI,CAAA;IACtBQ,QAAA,CAAAa,MAAM,CAACE,IAAI,CAAC,8CAA8C,CAAC;EAC7D,CAAC,CAAC,OAAOD,KAAK,EAAE;IAAA;IAAA1B,aAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAa,MAAM,CAACC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;IAAC;IAAA1B,aAAA,GAAAI,CAAA;IACrEwB,cAAc,GAAG,KAAK;EACxB,CAAC,SAAS;IAAA;IAAA5B,aAAA,GAAAI,CAAA;IACRyB,iBAAiB,GAAG,KAAK;EAC3B;AACF,CAAC;AAED;AAAA;AAAA7B,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAgC,gBAAgB,GAAG;EAC9B;EACAC,OAAO,EAAE;IACPC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,IAAI;IAAE;IACXC,OAAO,EAAE;MACPZ,KAAK,EAAE,yDAAyD;MAChEa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAGC,GAAY,IAAI;MAAA;MAAA3C,aAAA,GAAAwB,CAAA;MAAAxB,aAAA,GAAAI,CAAA;MACrB;MACA,OAAO,2BAAAJ,aAAA,GAAA+B,CAAA,UAAAY,GAAG,CAACC,IAAI,KAAK,aAAa;MAAA;MAAA,CAAA5C,aAAA,GAAA+B,CAAA,UAAIY,GAAG,CAACC,IAAI,KAAK,SAAS;IAC7D;GACD;EAED;EACAC,IAAI,EAAE;IACJT,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,EAAE;IAAE;IACTC,OAAO,EAAE;MACPZ,KAAK,EAAE,2DAA2D;MAClEa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE,KAAK;IACpBK,sBAAsB,EAAE,IAAI,CAAC;GAC9B;EAED;EACAC,aAAa,EAAE;IACbX,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,CAAC;IAAE;IACRC,OAAO,EAAE;MACPZ,KAAK,EAAE,2DAA2D;MAClEa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;GAChB;EAED;EACAO,KAAK,EAAE;IACLZ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,EAAE;IAAE;IACTC,OAAO,EAAE;MACPZ,KAAK,EAAE,kDAAkD;MACzDa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;GAChB;EAED;EACAQ,MAAM,EAAE;IACNb,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,GAAG;IAAE;IACVC,OAAO,EAAE;MACPZ,KAAK,EAAE,mDAAmD;MAC1Da,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;GAChB;EAED;EACAS,MAAM,EAAE;IACNd,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IACzBC,GAAG,EAAE,EAAE;IAAE;IACTC,OAAO,EAAE;MACPZ,KAAK,EAAE,6CAA6C;MACpDa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;GAChB;EAED;EACAU,KAAK,EAAE;IACLf,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,GAAG,EAAE,GAAG;IAAE;IACVC,OAAO,EAAE;MACPZ,KAAK,EAAE,kDAAkD;MACzDa,UAAU,EAAE;KACb;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;;CAElB;AAED;AACA,SAASW,iBAAiBA,CAACpC,MAAW,EAAEqC,IAAY;EAAA;EAAArD,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EAClD,OAAO,IAAAG,oBAAA,CAAA+C,OAAS,EAAC;IACf,GAAGtC,MAAM;IACTuC,KAAK,EAAEC,SAAS;IAAE;IAClBC,YAAY,EAAGd,GAAY,IAAI;MAAA;MAAA3C,aAAA,GAAAwB,CAAA;MAC7B;MACA,MAAMkC,MAAM;MAAA;MAAA,CAAA1D,aAAA,GAAAI,CAAA,QAAGuC,GAAG,CAACgB,IAAI,EAAEC,GAAG;MAC5B,MAAMC,EAAE;MAAA;MAAA,CAAA7D,aAAA,GAAAI,CAAA;MAAG;MAAA,CAAAJ,aAAA,GAAA+B,CAAA,UAAAY,GAAG,CAACkB,EAAE;MAAA;MAAA,CAAA7D,aAAA,GAAA+B,CAAA,UAAIY,GAAG,CAACmB,UAAU,CAACC,aAAa;MAAA;MAAA,CAAA/D,aAAA,GAAA+B,CAAA,UAAI,SAAS;MAAC;MAAA/B,aAAA,GAAAI,CAAA;MAC/D,OAAOsD,MAAM;MAAA;MAAA,CAAA1D,aAAA,GAAA+B,CAAA,UAAG,QAAQ2B,MAAM,EAAE;MAAA;MAAA,CAAA1D,aAAA,GAAA+B,CAAA,UAAG,MAAM8B,EAAE,EAAE;IAC/C,CAAC;IACDG,OAAO,EAAEA,CAACrB,GAAY,EAAEsB,GAAa,KAAI;MAAA;MAAAjE,aAAA,GAAAwB,CAAA;MAAAxB,aAAA,GAAAI,CAAA;MACvCQ,QAAA,CAAAa,MAAM,CAACyC,IAAI,CAAC,2BAA2Bb,IAAI,EAAE,EAAE;QAC7CQ,EAAE,EAAElB,GAAG,CAACkB,EAAE;QACVH,MAAM,EAAEf,GAAG,CAACgB,IAAI,EAAEC,GAAG;QACrBhB,IAAI,EAAED,GAAG,CAACC,IAAI;QACduB,MAAM,EAAExB,GAAG,CAACwB,MAAM;QAClBC,SAAS,EAAEzB,GAAG,CAAC0B,GAAG,CAAC,YAAY;OAChC,CAAC;MAAC;MAAArE,aAAA,GAAAI,CAAA;MAEH6D,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QACnBC,OAAO,EAAE,KAAK;QACd9C,KAAK,EAAEV,MAAM,CAACsB,OAAO,CAACZ,KAAK;QAC3Ba,UAAU,EAAEvB,MAAM,CAACsB,OAAO,CAACC,UAAU;QACrCkC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;OAClC,CAAC;IACJ;IACA;GACD,CAAC;AACJ;AAEA;AAAA;AAAA3E,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAA0E,gBAAgB,GAAGxB,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACC,OAAO,EAAE,SAAS,CAAC;AAAC;AAAAnC,aAAA,GAAAI,CAAA;AAC1EF,OAAA,CAAA2E,aAAa,GAAGzB,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACW,IAAI,EAAE,MAAM,CAAC;AAAC;AAAA7C,aAAA,GAAAI,CAAA;AACjEF,OAAA,CAAA4E,sBAAsB,GAAG1B,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACa,aAAa,EAAE,gBAAgB,CAAC;AAAC;AAAA/C,aAAA,GAAAI,CAAA;AAC7FF,OAAA,CAAA6E,cAAc,GAAG3B,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACc,KAAK,EAAE,OAAO,CAAC;AAAC;AAAAhD,aAAA,GAAAI,CAAA;AACpEF,OAAA,CAAA8E,eAAe,GAAG5B,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACe,MAAM,EAAE,QAAQ,CAAC;AAAC;AAAAjD,aAAA,GAAAI,CAAA;AACvEF,OAAA,CAAA+E,eAAe,GAAG7B,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACgB,MAAM,EAAE,QAAQ,CAAC;AAAC;AAAAlD,aAAA,GAAAI,CAAA;AACvEF,OAAA,CAAAgF,cAAc,GAAG9B,iBAAiB,CAAClD,OAAA,CAAAgC,gBAAgB,CAACiB,KAAK,EAAE,OAAO,CAAC;AAEhF;AACA,MAAagC,sBAAsB;EAMjCC,YAAYC,UAAkB,EAAEC,WAAmB,EAAEC,SAAiB;IAAA;IAAAvF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAI,CAAA;IACpE,IAAI,CAACS,WAAW,GAAGA,WAAW;IAAC;IAAAb,aAAA,GAAAI,CAAA;IAC/B,IAAI,CAACiF,UAAU,GAAGA,UAAU;IAAC;IAAArF,aAAA,GAAAI,CAAA;IAC7B,IAAI,CAACkF,WAAW,GAAGA,WAAW;IAAC;IAAAtF,aAAA,GAAAI,CAAA;IAC/B,IAAI,CAACmF,SAAS,GAAGA,SAAS;EAC5B;EAEA,MAAMC,SAASA,CAACC,UAAkB;IAAA;IAAAzF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAI,CAAA;IAChC,IAAI,CAACwB,cAAc,EAAE;MAAA;MAAA5B,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACnB;MACA,OAAO;QAAEsF,OAAO,EAAE,IAAI;QAAEC,SAAS,EAAE,IAAI,CAACL,WAAW;QAAEM,SAAS,EAAElB,IAAI,CAACmB,GAAG,EAAE,GAAG,IAAI,CAACR;MAAU,CAAE;IAChG,CAAC;IAAA;IAAA;MAAArF,aAAA,GAAA+B,CAAA;IAAA;IAED,MAAM8D,GAAG;IAAA;IAAA,CAAA7F,aAAA,GAAAI,CAAA,QAAGsE,IAAI,CAACmB,GAAG,EAAE;IACtB,MAAMC,WAAW;IAAA;IAAA,CAAA9F,aAAA,GAAAI,CAAA,QAAGyF,GAAG,GAAG,IAAI,CAACR,UAAU;IACzC,MAAMU,GAAG;IAAA;IAAA,CAAA/F,aAAA,GAAAI,CAAA,QAAG,GAAG,IAAI,CAACmF,SAAS,IAAIE,UAAU,EAAE;IAAC;IAAAzF,aAAA,GAAAI,CAAA;IAE9C,IAAI;MAAA;MAAAJ,aAAA,GAAAI,CAAA;MACF;MACA,MAAM,IAAI,CAACS,WAAW,CAACmF,gBAAgB,CAACD,GAAG,EAAE,CAAC,EAAED,WAAW,CAAC;MAC5D,MAAMG,eAAe;MAAA;MAAA,CAAAjG,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAI,CAACS,WAAW,CAACqF,KAAK,CAACH,GAAG,CAAC;MAAC;MAAA/F,aAAA,GAAAI,CAAA;MAE1D,IAAI6F,eAAe,IAAI,IAAI,CAACX,WAAW,EAAE;QAAA;QAAAtF,aAAA,GAAA+B,CAAA;QACvC,MAAMoE,aAAa;QAAA;QAAA,CAAAnG,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAI,CAACS,WAAW,CAACuF,MAAM,CAACL,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;UAAEM,UAAU,EAAE;QAAI,CAAE,CAAC;QACpF,MAAMT,SAAS;QAAA;QAAA,CAAA5F,aAAA,GAAAI,CAAA,QAAG+F,aAAa,CAACG,MAAM,GAAG,CAAC;QAAA;QAAA,CAAAtG,aAAA,GAAA+B,CAAA,WACxCwE,QAAQ,CAACJ,aAAa,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC,GAAG,IAAI,CAACnB,UAAU;QAAA;QAAA,CAAArF,aAAA,GAAA+B,CAAA,WAClD8D,GAAG,GAAG,IAAI,CAACR,UAAU;QAAC;QAAArF,aAAA,GAAAI,CAAA;QAExB,OAAO;UACLsF,OAAO,EAAE,KAAK;UACdC,SAAS,EAAE,CAAC;UACZC;SACD;MACH,CAAC;MAAA;MAAA;QAAA5F,aAAA,GAAA+B,CAAA;MAAA;MAED;MAAA/B,aAAA,GAAAI,CAAA;MACA,MAAM,IAAI,CAACS,WAAW,CAAC4F,IAAI,CAACV,GAAG,EAAE;QAAES,KAAK,EAAEX,GAAG;QAAEa,KAAK,EAAE,GAAGb,GAAG,IAAIc,IAAI,CAACC,MAAM,EAAE;MAAE,CAAE,CAAC;MAAC;MAAA5G,aAAA,GAAAI,CAAA;MACnF,MAAM,IAAI,CAACS,WAAW,CAACgG,MAAM,CAACd,GAAG,EAAEY,IAAI,CAACG,IAAI,CAAC,IAAI,CAACzB,UAAU,GAAG,IAAI,CAAC,CAAC;MAAC;MAAArF,aAAA,GAAAI,CAAA;MAEtE,OAAO;QACLsF,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,IAAI,CAACL,WAAW,GAAGW,eAAe,GAAG,CAAC;QACjDL,SAAS,EAAEC,GAAG,GAAG,IAAI,CAACR;OACvB;IACH,CAAC,CAAC,OAAO3D,KAAK,EAAE;MAAA;MAAA1B,aAAA,GAAAI,CAAA;MACdQ,QAAA,CAAAa,MAAM,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACvD;MAAA;MAAA1B,aAAA,GAAAI,CAAA;MACA,OAAO;QAAEsF,OAAO,EAAE,IAAI;QAAEC,SAAS,EAAE,IAAI,CAACL,WAAW;QAAEM,SAAS,EAAEC,GAAG,GAAG,IAAI,CAACR;MAAU,CAAE;IACzF;EACF;EAEA0B,UAAUA,CAAA;IAAA;IAAA/G,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAI,CAAA;IACR,OAAO,OAAOuC,GAAY,EAAEsB,GAAa,EAAE+C,IAAkB,KAAI;MAAA;MAAAhH,aAAA,GAAAwB,CAAA;MAC/D,MAAMiE,UAAU;MAAA;MAAA,CAAAzF,aAAA,GAAAI,CAAA;MAAG;MAAA,CAAAJ,aAAA,GAAA+B,CAAA,WAAAY,GAAG,CAACgB,IAAI,EAAEC,GAAG;MAAA;MAAA,CAAA5D,aAAA,GAAA+B,CAAA,WAAIY,GAAG,CAACkB,EAAE;MAAA;MAAA,CAAA7D,aAAA,GAAA+B,CAAA,WAAI,SAAS;MACvD,MAAMkF,MAAM;MAAA;MAAA,CAAAjH,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAI,CAACoF,SAAS,CAACC,UAAU,CAAC;MAE/C;MAAA;MAAAzF,aAAA,GAAAI,CAAA;MACA6D,GAAG,CAACiD,GAAG,CAAC;QACN,mBAAmB,EAAE,IAAI,CAAC5B,WAAW,CAAC6B,QAAQ,EAAE;QAChD,uBAAuB,EAAEF,MAAM,CAACtB,SAAS,CAACwB,QAAQ,EAAE;QACpD,mBAAmB,EAAE,IAAIzC,IAAI,CAACuC,MAAM,CAACrB,SAAS,CAAC,CAACjB,WAAW;OAC5D,CAAC;MAAC;MAAA3E,aAAA,GAAAI,CAAA;MAEH,IAAI,CAAC6G,MAAM,CAACvB,OAAO,EAAE;QAAA;QAAA1F,aAAA,GAAA+B,CAAA;QAAA/B,aAAA,GAAAI,CAAA;QACnBQ,QAAA,CAAAa,MAAM,CAACyC,IAAI,CAAC,oCAAoC,EAAE;UAChDuB,UAAU;UACV7C,IAAI,EAAED,GAAG,CAACC,IAAI;UACduB,MAAM,EAAExB,GAAG,CAACwB,MAAM;UAClBwB,SAAS,EAAEsB,MAAM,CAACtB,SAAS;UAC3BC,SAAS,EAAEqB,MAAM,CAACrB;SACnB,CAAC;QAAC;QAAA5F,aAAA,GAAAI,CAAA;QAEH,OAAO6D,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;UAC1BC,OAAO,EAAE,KAAK;UACd9C,KAAK,EAAE,qBAAqB;UAC5Ba,UAAU,EAAEoE,IAAI,CAACG,IAAI,CAAC,CAACG,MAAM,CAACrB,SAAS,GAAGlB,IAAI,CAACmB,GAAG,EAAE,IAAI,IAAI,CAAC;UAC7DpB,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;SAClC,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA3E,aAAA,GAAA+B,CAAA;MAAA;MAAA/B,aAAA,GAAAI,CAAA;MAED4G,IAAI,EAAE;IACR,CAAC;EACH;;AACD;AAAAhH,aAAA,GAAAI,CAAA;AAzFDF,OAAA,CAAAiF,sBAAA,GAAAA,sBAAA;AA2FA;AAAA;AAAAnF,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAkH,2BAA2B,GAAG,IAAIjC,sBAAsB,CACnE,EAAE,GAAG,IAAI;AAAE;AACX,EAAE;AAAE;AACJ,cAAc,CACf;AAAC;AAAAnF,aAAA,GAAAI,CAAA;AAEWF,OAAA,CAAAmH,oBAAoB,GAAG,IAAIlC,sBAAsB,CAC5D,CAAC,GAAG,EAAE,GAAG,IAAI;AAAE;AACf,GAAG;AAAE;AACL,cAAc,CACf;AAED;AAAA;AAAAnF,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAoH,UAAU,GAAG,IAAIC,GAAG,CAAC,CAChC,WAAW,EACX;AACA;AAAA,CACD,CAAC;AAEF,SAAgBpH,4BAA4BA,CAACwC,GAAY,EAAEsB,GAAa,EAAE+C,IAAkB;EAAA;EAAAhH,aAAA,GAAAwB,CAAA;EAC1F,MAAMgG,QAAQ;EAAA;EAAA,CAAAxH,aAAA,GAAAI,CAAA;EAAG;EAAA,CAAAJ,aAAA,GAAA+B,CAAA,WAAAY,GAAG,CAACkB,EAAE;EAAA;EAAA,CAAA7D,aAAA,GAAA+B,CAAA,WAAIY,GAAG,CAACmB,UAAU,CAACC,aAAa;EAAC;EAAA/D,aAAA,GAAAI,CAAA;EAExD;EAAI;EAAA,CAAAJ,aAAA,GAAA+B,CAAA,WAAAyF,QAAQ;EAAA;EAAA,CAAAxH,aAAA,GAAA+B,CAAA,WAAI7B,OAAA,CAAAoH,UAAU,CAACG,GAAG,CAACD,QAAQ,CAAC,GAAE;IAAA;IAAAxH,aAAA,GAAA+B,CAAA;IAAA/B,aAAA,GAAAI,CAAA;IACxCQ,QAAA,CAAAa,MAAM,CAACE,IAAI,CAAC,oCAAoC,EAAE;MAAEkC,EAAE,EAAE2D;IAAQ,CAAE,CAAC;IAAC;IAAAxH,aAAA,GAAAI,CAAA;IACpE,OAAO4G,IAAI,EAAE;EACf,CAAC;EAAA;EAAA;IAAAhH,aAAA,GAAA+B,CAAA;EAAA;EAAA/B,aAAA,GAAAI,CAAA;EAED4G,IAAI,EAAE;AACR;AAEA;AACO,eAAe3G,iBAAiBA,CAACqH,SAAA;AAAA;AAAA,CAAA1H,aAAA,GAAA+B,CAAA,WAAqC,MAAM;EAAA;EAAA/B,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EACjF,IAAI,CAACwB,cAAc,EAAE;IAAA;IAAA5B,aAAA,GAAA+B,CAAA;IAAA/B,aAAA,GAAAI,CAAA;IACnB,OAAO;MAAEsB,KAAK,EAAE;IAAqB,CAAE;EACzC,CAAC;EAAA;EAAA;IAAA1B,aAAA,GAAA+B,CAAA;EAAA;EAED,MAAM8D,GAAG;EAAA;EAAA,CAAA7F,aAAA,GAAAI,CAAA,QAAGsE,IAAI,CAACmB,GAAG,EAAE;EACtB,IAAIC,WAAmB;EAAC;EAAA9F,aAAA,GAAAI,CAAA;EAExB,QAAQsH,SAAS;IACf,KAAK,MAAM;MAAA;MAAA1H,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACT0F,WAAW,GAAGD,GAAG,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK;MAAC;MAAA7F,aAAA,GAAAI,CAAA;MACrC;IACF,KAAK,KAAK;MAAA;MAAAJ,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACR0F,WAAW,GAAGD,GAAG,GAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAK;MAAC;MAAA7F,aAAA,GAAAI,CAAA;MAC1C;IACF,KAAK,MAAM;MAAA;MAAAJ,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACT0F,WAAW,GAAGD,GAAG,GAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAK;MAAC;MAAA7F,aAAA,GAAAI,CAAA;MAC9C;IACF;MAAA;MAAAJ,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACE0F,WAAW,GAAGD,GAAG,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK;EACxC;EAAC;EAAA7F,aAAA,GAAAI,CAAA;EAED,IAAI;IACF,MAAMuH,IAAI;IAAA;IAAA,CAAA3H,aAAA,GAAAI,CAAA,QAAG,MAAMS,WAAW,CAAC8G,IAAI,CAAC,MAAM,CAAC;IAC3C,MAAMC,KAAK;IAAA;IAAA,CAAA5H,aAAA,GAAAI,CAAA,QAAQ,EAAE;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAEtB,KAAK,MAAM2F,GAAG,IAAI4B,IAAI,EAAE;MACtB,MAAME,KAAK;MAAA;MAAA,CAAA7H,aAAA,GAAAI,CAAA,QAAG,MAAMS,WAAW,CAACwD,GAAG,CAAC0B,GAAG,CAAC;MAAC;MAAA/F,aAAA,GAAAI,CAAA;MACzC,IAAIyH,KAAK,EAAE;QAAA;QAAA7H,aAAA,GAAA+B,CAAA;QACT,MAAM,GAAG+F,WAAW,CAAC;QAAA;QAAA,CAAA9H,aAAA,GAAAI,CAAA,SAAG2F,GAAG,CAACgC,KAAK,CAAC,GAAG,CAAC;QAAC;QAAA/H,aAAA,GAAAI,CAAA;QACvCwH,KAAK,CAACE,WAAW,CAAC,GAAG;QAAC;QAAA,CAAA9H,aAAA,GAAA+B,CAAA,WAAA6F,KAAK,CAACE,WAAW,CAAC;QAAA;QAAA,CAAA9H,aAAA,GAAA+B,CAAA,WAAI,CAAC,KAAIwE,QAAQ,CAACsB,KAAK,CAAC;MAClE,CAAC;MAAA;MAAA;QAAA7H,aAAA,GAAA+B,CAAA;MAAA;IACH;IAAC;IAAA/B,aAAA,GAAAI,CAAA;IAED,OAAO;MACLsH,SAAS;MACT5B,WAAW,EAAE,IAAIpB,IAAI,CAACoB,WAAW,CAAC,CAACnB,WAAW,EAAE;MAChDqD,SAAS,EAAE,IAAItD,IAAI,CAACmB,GAAG,CAAC,CAAClB,WAAW,EAAE;MACtCiD;KACD;EACH,CAAC,CAAC,OAAOlG,KAAK,EAAE;IAAA;IAAA1B,aAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAa,MAAM,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAAC;IAAA1B,aAAA,GAAAI,CAAA;IACvD,OAAO;MAAEsB,KAAK,EAAE;IAAqB,CAAE;EACzC;AACF;AAEA;AACO,eAAepB,oBAAoBA,CAAA;EAAA;EAAAN,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAI,CAAA;EACxC,IAAI,CAACwB,cAAc,EAAE;IAAA;IAAA5B,aAAA,GAAA+B,CAAA;IAAA/B,aAAA,GAAAI,CAAA;IAAA;EAAA,CAAO;EAAA;EAAA;IAAAJ,aAAA,GAAA+B,CAAA;EAAA;EAAA/B,aAAA,GAAAI,CAAA;EAE5B,IAAI;IACF,MAAMuH,IAAI;IAAA;IAAA,CAAA3H,aAAA,GAAAI,CAAA,SAAG,MAAMS,WAAW,CAAC8G,IAAI,CAAC,MAAM,CAAC;IAAC;IAAA3H,aAAA,GAAAI,CAAA;IAC5C,IAAIuH,IAAI,CAACrB,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAtG,aAAA,GAAA+B,CAAA;MAAA/B,aAAA,GAAAI,CAAA;MACnB,MAAMS,WAAW,CAACoH,GAAG,CAACN,IAAI,CAAC;MAAC;MAAA3H,aAAA,GAAAI,CAAA;MAC5BQ,QAAA,CAAAa,MAAM,CAACE,IAAI,CAAC,cAAcgG,IAAI,CAACrB,MAAM,kBAAkB,CAAC;IAC1D,CAAC;IAAA;IAAA;MAAAtG,aAAA,GAAA+B,CAAA;IAAA;EACH,CAAC,CAAC,OAAOL,KAAK,EAAE;IAAA;IAAA1B,aAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAa,MAAM,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;EAC3D;AACF;AAAC;AAAA1B,aAAA,GAAAI,CAAA;AAIDF,OAAA,CAAAoD,OAAA,GAAe;EACbsB,gBAAgB,EAAhB1E,OAAA,CAAA0E,gBAAgB;EAChBC,aAAa,EAAb3E,OAAA,CAAA2E,aAAa;EACbC,sBAAsB,EAAtB5E,OAAA,CAAA4E,sBAAsB;EACtBC,cAAc,EAAd7E,OAAA,CAAA6E,cAAc;EACdC,eAAe,EAAf9E,OAAA,CAAA8E,eAAe;EACfC,eAAe,EAAf/E,OAAA,CAAA+E,eAAe;EACfC,cAAc,EAAdhF,OAAA,CAAAgF,cAAc;EACdC,sBAAsB;EACtBiC,2BAA2B,EAA3BlH,OAAA,CAAAkH,2BAA2B;EAC3BC,oBAAoB,EAApBnH,OAAA,CAAAmH,oBAAoB;EACpBlH,4BAA4B;EAC5BE,iBAAiB;EACjBC;CACD", "ignoreList": []}