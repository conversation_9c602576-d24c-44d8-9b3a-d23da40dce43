a8fb2939176ea03e44291541c1a1156a
"use strict";

/* istanbul ignore next */
function cov_2o7l4pzoiz() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\session.routes.ts";
  var hash = "0a1e55e5e7a321e07ba7e28d7fb14224dc9d34ea";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\session.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 25
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "4": {
        start: {
          line: 6,
          column: 23
        },
        end: {
          line: 6,
          column: 58
        }
      },
      "5": {
        start: {
          line: 7,
          column: 23
        },
        end: {
          line: 7,
          column: 60
        }
      },
      "6": {
        start: {
          line: 8,
          column: 23
        },
        end: {
          line: 8,
          column: 60
        }
      },
      "7": {
        start: {
          line: 9,
          column: 15
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 44
        }
      },
      "9": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 32
        }
      },
      "10": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 50
        }
      },
      "11": {
        start: {
          line: 66,
          column: 0
        },
        end: {
          line: 87,
          column: 3
        }
      },
      "12": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 86,
          column: 5
        }
      },
      "13": {
        start: {
          line: 68,
          column: 23
        },
        end: {
          line: 68,
          column: 35
        }
      },
      "14": {
        start: {
          line: 69,
          column: 31
        },
        end: {
          line: 69,
          column: 98
        }
      },
      "15": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 75,
          column: 11
        }
      },
      "16": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 79,
          column: 11
        }
      },
      "17": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 85,
          column: 11
        }
      },
      "18": {
        start: {
          line: 119,
          column: 0
        },
        end: {
          line: 150,
          column: 3
        }
      },
      "19": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "20": {
        start: {
          line: 121,
          column: 23
        },
        end: {
          line: 121,
          column: 35
        }
      },
      "21": {
        start: {
          line: 122,
          column: 33
        },
        end: {
          line: 122,
          column: 48
        }
      },
      "22": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 128,
          column: 9
        }
      },
      "23": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 127,
          column: 15
        }
      },
      "24": {
        start: {
          line: 129,
          column: 32
        },
        end: {
          line: 129,
          column: 118
        }
      },
      "25": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 137,
          column: 11
        }
      },
      "26": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 142,
          column: 11
        }
      },
      "27": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 148,
          column: 11
        }
      },
      "28": {
        start: {
          line: 176,
          column: 0
        },
        end: {
          line: 224,
          column: 3
        }
      },
      "29": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "30": {
        start: {
          line: 178,
          column: 23
        },
        end: {
          line: 178,
          column: 35
        }
      },
      "31": {
        start: {
          line: 179,
          column: 37
        },
        end: {
          line: 179,
          column: 57
        }
      },
      "32": {
        start: {
          line: 180,
          column: 33
        },
        end: {
          line: 180,
          column: 48
        }
      },
      "33": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "34": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 186,
          column: 15
        }
      },
      "35": {
        start: {
          line: 189,
          column: 29
        },
        end: {
          line: 189,
          column: 96
        }
      },
      "36": {
        start: {
          line: 190,
          column: 30
        },
        end: {
          line: 190,
          column: 102
        }
      },
      "37": {
        start: {
          line: 190,
          column: 59
        },
        end: {
          line: 190,
          column: 101
        }
      },
      "38": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 196,
          column: 9
        }
      },
      "39": {
        start: {
          line: 192,
          column: 12
        },
        end: {
          line: 195,
          column: 15
        }
      },
      "40": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 197,
          column: 102
        }
      },
      "41": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "42": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 205,
          column: 15
        }
      },
      "43": {
        start: {
          line: 206,
          column: 12
        },
        end: {
          line: 209,
          column: 15
        }
      },
      "44": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 215,
          column: 15
        }
      },
      "45": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 222,
          column: 11
        }
      },
      "46": {
        start: {
          line: 239,
          column: 0
        },
        end: {
          line: 277,
          column: 3
        }
      },
      "47": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "48": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 241,
          column: 41
        }
      },
      "49": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 247,
          column: 9
        }
      },
      "50": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 246,
          column: 15
        }
      },
      "51": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 248,
          column: 91
        }
      },
      "52": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 254,
          column: 9
        }
      },
      "53": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 253,
          column: 15
        }
      },
      "54": {
        start: {
          line: 256,
          column: 32
        },
        end: {
          line: 265,
          column: 9
        }
      },
      "55": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 269,
          column: 11
        }
      },
      "56": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 275,
          column: 11
        }
      },
      "57": {
        start: {
          line: 312,
          column: 0
        },
        end: {
          line: 355,
          column: 3
        }
      },
      "58": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 354,
          column: 5
        }
      },
      "59": {
        start: {
          line: 314,
          column: 23
        },
        end: {
          line: 314,
          column: 35
        }
      },
      "60": {
        start: {
          line: 315,
          column: 31
        },
        end: {
          line: 315,
          column: 98
        }
      },
      "61": {
        start: {
          line: 316,
          column: 26
        },
        end: {
          line: 316,
          column: 88
        }
      },
      "62": {
        start: {
          line: 316,
          column: 64
        },
        end: {
          line: 316,
          column: 81
        }
      },
      "63": {
        start: {
          line: 317,
          column: 36
        },
        end: {
          line: 317,
          column: 61
        }
      },
      "64": {
        start: {
          line: 318,
          column: 35
        },
        end: {
          line: 318,
          column: 77
        }
      },
      "65": {
        start: {
          line: 319,
          column: 32
        },
        end: {
          line: 319,
          column: 34
        }
      },
      "66": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 322,
          column: 9
        }
      },
      "67": {
        start: {
          line: 321,
          column: 12
        },
        end: {
          line: 321,
          column: 93
        }
      },
      "68": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 325,
          column: 9
        }
      },
      "69": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 324,
          column: 99
        }
      },
      "70": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 328,
          column: 9
        }
      },
      "71": {
        start: {
          line: 327,
          column: 12
        },
        end: {
          line: 327,
          column: 93
        }
      },
      "72": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 337,
          column: 11
        }
      },
      "73": {
        start: {
          line: 338,
          column: 8
        },
        end: {
          line: 347,
          column: 11
        }
      },
      "74": {
        start: {
          line: 350,
          column: 8
        },
        end: {
          line: 353,
          column: 11
        }
      },
      "75": {
        start: {
          line: 356,
          column: 0
        },
        end: {
          line: 356,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 66,
            column: 22
          },
          end: {
            line: 66,
            column: 23
          }
        },
        loc: {
          start: {
            line: 66,
            column: 42
          },
          end: {
            line: 87,
            column: 1
          }
        },
        line: 66
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 119,
            column: 33
          },
          end: {
            line: 119,
            column: 34
          }
        },
        loc: {
          start: {
            line: 119,
            column: 53
          },
          end: {
            line: 150,
            column: 1
          }
        },
        line: 119
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 176,
            column: 39
          },
          end: {
            line: 176,
            column: 40
          }
        },
        loc: {
          start: {
            line: 176,
            column: 59
          },
          end: {
            line: 224,
            column: 1
          }
        },
        line: 176
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 190,
            column: 48
          },
          end: {
            line: 190,
            column: 49
          }
        },
        loc: {
          start: {
            line: 190,
            column: 59
          },
          end: {
            line: 190,
            column: 101
          }
        },
        line: 190
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 239,
            column: 23
          },
          end: {
            line: 239,
            column: 24
          }
        },
        loc: {
          start: {
            line: 239,
            column: 43
          },
          end: {
            line: 277,
            column: 1
          }
        },
        line: 239
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 312,
            column: 30
          },
          end: {
            line: 312,
            column: 31
          }
        },
        loc: {
          start: {
            line: 312,
            column: 50
          },
          end: {
            line: 355,
            column: 1
          }
        },
        line: 312
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 316,
            column: 53
          },
          end: {
            line: 316,
            column: 54
          }
        },
        loc: {
          start: {
            line: 316,
            column: 64
          },
          end: {
            line: 316,
            column: 81
          }
        },
        line: 316
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 128,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 128,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "1": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 187,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "2": {
        loc: {
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 196,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 196,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "3": {
        loc: {
          start: {
            line: 198,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 8
          },
          end: {
            line: 216,
            column: 9
          }
        }, {
          start: {
            line: 211,
            column: 13
          },
          end: {
            line: 216,
            column: 9
          }
        }],
        line: 198
      },
      "4": {
        loc: {
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "5": {
        loc: {
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "6": {
        loc: {
          start: {
            line: 318,
            column: 35
          },
          end: {
            line: 318,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 35
          },
          end: {
            line: 318,
            column: 48
          }
        }, {
          start: {
            line: 318,
            column: 52
          },
          end: {
            line: 318,
            column: 77
          }
        }],
        line: 318
      },
      "7": {
        loc: {
          start: {
            line: 320,
            column: 8
          },
          end: {
            line: 322,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 320,
            column: 8
          },
          end: {
            line: 322,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 320
      },
      "8": {
        loc: {
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "9": {
        loc: {
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 328,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 328,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 326
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\session.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,6CAAoE;AACpE,+DAA4D;AAC5D,2DAAwE;AACxE,6DAA8D;AAC9D,6DAA6D;AAE7D,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,mBAAmB;AACnB,MAAM,CAAC,GAAG,CAAC,+BAAgB,CAAC,CAAC;AAC7B,MAAM,CAAC,GAAG,CAAC,mBAAc,CAAC,CAAC;AAC3B,MAAM,CAAC,GAAG,CAAC,IAAA,8BAAe,GAAE,CAAC,CAAC;AAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACvC,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAC5B,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE1E,qBAAqB;QACrB,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,WAAW,EAAE,GAAG,EAAE;YAC3D,QAAQ,EAAE,eAAe;YACzB,UAAU,EAAE,MAAM;YAClB,QAAQ,EAAE,EAAE,YAAY,EAAE,cAAc,CAAC,MAAM,EAAE;SAClD,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,oCAAoC;SAC5C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAC5B,MAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QAEzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,+BAAc,CAAC,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QAE9F,0BAA0B;QAC1B,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,MAAM,EAAE,GAAG,EAAE;YACtD,QAAQ,EAAE;gBACR,MAAM,EAAE,0BAA0B;gBAClC,eAAe;gBACf,gBAAgB;aACjB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,GAAG,eAAe,mCAAmC;YAC9D,IAAI,EAAE,EAAE,eAAe,EAAE;SAC1B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,oCAAoC;SAC5C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAC5B,MAAM,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAClD,MAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QAEzC,sCAAsC;QACtC,IAAI,oBAAoB,KAAK,gBAAgB,EAAE,CAAC;YAC9C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uDAAuD;aAC/D,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,MAAM,YAAY,GAAG,MAAM,+BAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,CAAC;QAE/F,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8CAA8C;aACtD,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,+BAAc,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;QAE9E,IAAI,OAAO,EAAE,CAAC;YACZ,0BAA0B;YAC1B,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,MAAM,EAAE,GAAG,EAAE;gBACtD,QAAQ,EAAE;oBACR,MAAM,EAAE,4BAA4B;oBACpC,mBAAmB,EAAE,oBAAoB;iBAC1C;aACF,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,iCAAiC;aAC3C,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,6BAA6B;aACrC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,6BAA6B;SACrC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxC,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QAElC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,+BAAc,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAEnE,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,MAAM,eAAe,GAAG;YACtB,SAAS;YACT,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,wCAAwC;SAChD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC/C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAC5B,MAAM,cAAc,GAAG,MAAM,+BAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE1E,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QACjF,MAAM,mBAAmB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QACtD,MAAM,kBAAkB,GAAG,SAAS,GAAG,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtE,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,mBAAmB,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACnF,CAAC;QACD,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,eAAe,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACzF,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACnF,CAAC;QAED,qBAAqB;QACrB,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,WAAW,EAAE,GAAG,EAAE;YAC3D,QAAQ,EAAE,wBAAwB;YAClC,QAAQ,EAAE;gBACR,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,SAAS;gBACT,kBAAkB;aACnB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,mBAAmB;gBACnB,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,SAAS;gBACT,kBAAkB;gBAClB,eAAe;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kCAAkC;SAC1C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\session.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport { authenticate as authMiddleware } from '../middleware/auth';\r\nimport { sessionService } from '../services/sessionService';\r\nimport { auditService, AuditEventType } from '../services/auditService';\r\nimport { generalRateLimit } from '../middleware/rateLimiting';\r\nimport { sanitizeRequest } from '../middleware/sanitization';\r\n\r\nconst router = Router();\r\n\r\n// Apply middleware\r\nrouter.use(generalRateLimit);\r\nrouter.use(authMiddleware);\r\nrouter.use(sanitizeRequest());\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/active:\r\n *   get:\r\n *     summary: Get user's active sessions\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Active sessions retrieved successfully\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                   example: true\r\n *                 data:\r\n *                   type: array\r\n *                   items:\r\n *                     type: object\r\n *                     properties:\r\n *                       sessionId:\r\n *                         type: string\r\n *                       createdAt:\r\n *                         type: string\r\n *                         format: date-time\r\n *                       lastActivity:\r\n *                         type: string\r\n *                         format: date-time\r\n *                       ipAddress:\r\n *                         type: string\r\n *                       userAgent:\r\n *                         type: string\r\n *                       deviceInfo:\r\n *                         type: object\r\n *                         properties:\r\n *                           browser:\r\n *                             type: string\r\n *                           os:\r\n *                             type: string\r\n *                           device:\r\n *                             type: string\r\n *                           isMobile:\r\n *                             type: boolean\r\n *                       isActive:\r\n *                         type: boolean\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.get('/active', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const activeSessions = await sessionService.getUserActiveSessions(userId);\r\n\r\n    // Log session access\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'user_sessions',\r\n      resourceId: userId,\r\n      metadata: { sessionCount: activeSessions.length }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: activeSessions\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve active sessions'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/terminate-others:\r\n *   post:\r\n *     summary: Terminate all other sessions except current\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Other sessions terminated successfully\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                   example: true\r\n *                 message:\r\n *                   type: string\r\n *                   example: \"3 sessions terminated successfully\"\r\n *                 data:\r\n *                   type: object\r\n *                   properties:\r\n *                     terminatedCount:\r\n *                       type: number\r\n *                       example: 3\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.post('/terminate-others', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const currentSessionId = req.session?.id;\r\n\r\n    if (!currentSessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'No active session found'\r\n      });\r\n    }\r\n\r\n    const terminatedCount = await sessionService.terminateOtherSessions(userId, currentSessionId);\r\n\r\n    // Log session termination\r\n    await auditService.logEvent(AuditEventType.LOGOUT, req, {\r\n      metadata: { \r\n        action: 'terminate_other_sessions',\r\n        terminatedCount,\r\n        currentSessionId \r\n      }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      message: `${terminatedCount} sessions terminated successfully`,\r\n      data: { terminatedCount }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to terminate other sessions'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/terminate/{sessionId}:\r\n *   delete:\r\n *     summary: Terminate a specific session\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: path\r\n *         name: sessionId\r\n *         required: true\r\n *         schema:\r\n *           type: string\r\n *         description: Session ID to terminate\r\n *     responses:\r\n *       200:\r\n *         description: Session terminated successfully\r\n *       400:\r\n *         description: Cannot terminate current session\r\n *       404:\r\n *         description: Session not found\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.delete('/terminate/:sessionId', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const sessionIdToTerminate = req.params.sessionId;\r\n    const currentSessionId = req.session?.id;\r\n\r\n    // Prevent terminating current session\r\n    if (sessionIdToTerminate === currentSessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'Cannot terminate current session. Use logout instead.'\r\n      });\r\n    }\r\n\r\n    // Verify session belongs to user\r\n    const userSessions = await sessionService.getUserActiveSessions(userId);\r\n    const sessionExists = userSessions.some(session => session.sessionId === sessionIdToTerminate);\r\n\r\n    if (!sessionExists) {\r\n      return res.status(404).json({\r\n        success: false,\r\n        error: 'Session not found or does not belong to user'\r\n      });\r\n    }\r\n\r\n    const success = await sessionService.destroyUserSession(sessionIdToTerminate);\r\n\r\n    if (success) {\r\n      // Log session termination\r\n      await auditService.logEvent(AuditEventType.LOGOUT, req, {\r\n        metadata: { \r\n          action: 'terminate_specific_session',\r\n          terminatedSessionId: sessionIdToTerminate \r\n        }\r\n      });\r\n\r\n      res.json({\r\n        success: true,\r\n        message: 'Session terminated successfully'\r\n      });\r\n    } else {\r\n      res.status(500).json({\r\n        success: false,\r\n        error: 'Failed to terminate session'\r\n      });\r\n    }\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to terminate session'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/current:\r\n *   get:\r\n *     summary: Get current session information\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Current session information retrieved successfully\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.get('/current', async (req, res) => {\r\n  try {\r\n    const sessionId = req.session?.id;\r\n\r\n    if (!sessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'No active session found'\r\n      });\r\n    }\r\n\r\n    const sessionData = await sessionService.getUserSession(sessionId);\r\n\r\n    if (!sessionData) {\r\n      return res.status(404).json({\r\n        success: false,\r\n        error: 'Session not found'\r\n      });\r\n    }\r\n\r\n    // Remove sensitive data before sending\r\n    const safeSessionData = {\r\n      sessionId,\r\n      userId: sessionData.userId,\r\n      email: sessionData.email,\r\n      role: sessionData.role,\r\n      loginTime: sessionData.loginTime,\r\n      lastActivity: sessionData.lastActivity,\r\n      deviceInfo: sessionData.deviceInfo,\r\n      isActive: sessionData.isActive\r\n    };\r\n\r\n    res.json({\r\n      success: true,\r\n      data: safeSessionData\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve session information'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/security-check:\r\n *   get:\r\n *     summary: Check for security issues with user sessions\r\n *     tags: [Sessions, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Security check completed\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                 data:\r\n *                   type: object\r\n *                   properties:\r\n *                     hasMultipleSessions:\r\n *                       type: boolean\r\n *                     sessionCount:\r\n *                       type: number\r\n *                     uniqueIPs:\r\n *                       type: number\r\n *                     suspiciousActivity:\r\n *                       type: boolean\r\n *                     recommendations:\r\n *                       type: array\r\n *                       items:\r\n *                         type: string\r\n */\r\nrouter.get('/security-check', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const activeSessions = await sessionService.getUserActiveSessions(userId);\r\n    \r\n    const uniqueIPs = new Set(activeSessions.map(session => session.ipAddress)).size;\r\n    const hasMultipleSessions = activeSessions.length > 1;\r\n    const suspiciousActivity = uniqueIPs > 2 || activeSessions.length > 5;\r\n    \r\n    const recommendations = [];\r\n    if (hasMultipleSessions) {\r\n      recommendations.push('Consider terminating unused sessions for better security');\r\n    }\r\n    if (uniqueIPs > 2) {\r\n      recommendations.push('Multiple IP addresses detected - verify all sessions are yours');\r\n    }\r\n    if (activeSessions.length > 5) {\r\n      recommendations.push('Many active sessions detected - consider session cleanup');\r\n    }\r\n\r\n    // Log security check\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'session_security_check',\r\n      metadata: { \r\n        sessionCount: activeSessions.length,\r\n        uniqueIPs,\r\n        suspiciousActivity \r\n      }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        hasMultipleSessions,\r\n        sessionCount: activeSessions.length,\r\n        uniqueIPs,\r\n        suspiciousActivity,\r\n        recommendations\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to perform security check'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0a1e55e5e7a321e07ba7e28d7fb14224dc9d34ea"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2o7l4pzoiz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2o7l4pzoiz();
cov_2o7l4pzoiz().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[1]++, require("express"));
const auth_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[2]++, require("../middleware/auth"));
const sessionService_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[3]++, require("../services/sessionService"));
const auditService_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[4]++, require("../services/auditService"));
const rateLimiting_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[5]++, require("../middleware/rateLimiting"));
const sanitization_1 =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[6]++, require("../middleware/sanitization"));
const router =
/* istanbul ignore next */
(cov_2o7l4pzoiz().s[7]++, (0, express_1.Router)());
// Apply middleware
/* istanbul ignore next */
cov_2o7l4pzoiz().s[8]++;
router.use(rateLimiting_1.generalRateLimit);
/* istanbul ignore next */
cov_2o7l4pzoiz().s[9]++;
router.use(auth_1.authenticate);
/* istanbul ignore next */
cov_2o7l4pzoiz().s[10]++;
router.use((0, sanitization_1.sanitizeRequest)());
/**
 * @swagger
 * /api/sessions/active:
 *   get:
 *     summary: Get user's active sessions
 *     tags: [Sessions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active sessions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       sessionId:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       lastActivity:
 *                         type: string
 *                         format: date-time
 *                       ipAddress:
 *                         type: string
 *                       userAgent:
 *                         type: string
 *                       deviceInfo:
 *                         type: object
 *                         properties:
 *                           browser:
 *                             type: string
 *                           os:
 *                             type: string
 *                           device:
 *                             type: string
 *                           isMobile:
 *                             type: boolean
 *                       isActive:
 *                         type: boolean
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
/* istanbul ignore next */
cov_2o7l4pzoiz().s[11]++;
router.get('/active', async (req, res) => {
  /* istanbul ignore next */
  cov_2o7l4pzoiz().f[0]++;
  cov_2o7l4pzoiz().s[12]++;
  try {
    const userId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[13]++, req.user._id);
    const activeSessions =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[14]++, await sessionService_1.sessionService.getUserActiveSessions(userId));
    // Log session access
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[15]++;
    await auditService_1.auditService.logEvent(auditService_1.AuditEventType.DATA_VIEWED, req, {
      resource: 'user_sessions',
      resourceId: userId,
      metadata: {
        sessionCount: activeSessions.length
      }
    });
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[16]++;
    res.json({
      success: true,
      data: activeSessions
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[17]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve active sessions'
    });
  }
});
/**
 * @swagger
 * /api/sessions/terminate-others:
 *   post:
 *     summary: Terminate all other sessions except current
 *     tags: [Sessions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Other sessions terminated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "3 sessions terminated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     terminatedCount:
 *                       type: number
 *                       example: 3
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
/* istanbul ignore next */
cov_2o7l4pzoiz().s[18]++;
router.post('/terminate-others', async (req, res) => {
  /* istanbul ignore next */
  cov_2o7l4pzoiz().f[1]++;
  cov_2o7l4pzoiz().s[19]++;
  try {
    const userId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[20]++, req.user._id);
    const currentSessionId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[21]++, req.session?.id);
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[22]++;
    if (!currentSessionId) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[0][0]++;
      cov_2o7l4pzoiz().s[23]++;
      return res.status(400).json({
        success: false,
        error: 'No active session found'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[0][1]++;
    }
    const terminatedCount =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[24]++, await sessionService_1.sessionService.terminateOtherSessions(userId, currentSessionId));
    // Log session termination
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[25]++;
    await auditService_1.auditService.logEvent(auditService_1.AuditEventType.LOGOUT, req, {
      metadata: {
        action: 'terminate_other_sessions',
        terminatedCount,
        currentSessionId
      }
    });
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[26]++;
    res.json({
      success: true,
      message: `${terminatedCount} sessions terminated successfully`,
      data: {
        terminatedCount
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[27]++;
    res.status(500).json({
      success: false,
      error: 'Failed to terminate other sessions'
    });
  }
});
/**
 * @swagger
 * /api/sessions/terminate/{sessionId}:
 *   delete:
 *     summary: Terminate a specific session
 *     tags: [Sessions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Session ID to terminate
 *     responses:
 *       200:
 *         description: Session terminated successfully
 *       400:
 *         description: Cannot terminate current session
 *       404:
 *         description: Session not found
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
/* istanbul ignore next */
cov_2o7l4pzoiz().s[28]++;
router.delete('/terminate/:sessionId', async (req, res) => {
  /* istanbul ignore next */
  cov_2o7l4pzoiz().f[2]++;
  cov_2o7l4pzoiz().s[29]++;
  try {
    const userId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[30]++, req.user._id);
    const sessionIdToTerminate =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[31]++, req.params.sessionId);
    const currentSessionId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[32]++, req.session?.id);
    // Prevent terminating current session
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[33]++;
    if (sessionIdToTerminate === currentSessionId) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[1][0]++;
      cov_2o7l4pzoiz().s[34]++;
      return res.status(400).json({
        success: false,
        error: 'Cannot terminate current session. Use logout instead.'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[1][1]++;
    }
    // Verify session belongs to user
    const userSessions =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[35]++, await sessionService_1.sessionService.getUserActiveSessions(userId));
    const sessionExists =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[36]++, userSessions.some(session => {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().f[3]++;
      cov_2o7l4pzoiz().s[37]++;
      return session.sessionId === sessionIdToTerminate;
    }));
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[38]++;
    if (!sessionExists) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[2][0]++;
      cov_2o7l4pzoiz().s[39]++;
      return res.status(404).json({
        success: false,
        error: 'Session not found or does not belong to user'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[2][1]++;
    }
    const success =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[40]++, await sessionService_1.sessionService.destroyUserSession(sessionIdToTerminate));
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[41]++;
    if (success) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[3][0]++;
      cov_2o7l4pzoiz().s[42]++;
      // Log session termination
      await auditService_1.auditService.logEvent(auditService_1.AuditEventType.LOGOUT, req, {
        metadata: {
          action: 'terminate_specific_session',
          terminatedSessionId: sessionIdToTerminate
        }
      });
      /* istanbul ignore next */
      cov_2o7l4pzoiz().s[43]++;
      res.json({
        success: true,
        message: 'Session terminated successfully'
      });
    } else {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[3][1]++;
      cov_2o7l4pzoiz().s[44]++;
      res.status(500).json({
        success: false,
        error: 'Failed to terminate session'
      });
    }
  } catch (error) {
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[45]++;
    res.status(500).json({
      success: false,
      error: 'Failed to terminate session'
    });
  }
});
/**
 * @swagger
 * /api/sessions/current:
 *   get:
 *     summary: Get current session information
 *     tags: [Sessions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current session information retrieved successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
/* istanbul ignore next */
cov_2o7l4pzoiz().s[46]++;
router.get('/current', async (req, res) => {
  /* istanbul ignore next */
  cov_2o7l4pzoiz().f[4]++;
  cov_2o7l4pzoiz().s[47]++;
  try {
    const sessionId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[48]++, req.session?.id);
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[49]++;
    if (!sessionId) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[4][0]++;
      cov_2o7l4pzoiz().s[50]++;
      return res.status(400).json({
        success: false,
        error: 'No active session found'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[4][1]++;
    }
    const sessionData =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[51]++, await sessionService_1.sessionService.getUserSession(sessionId));
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[52]++;
    if (!sessionData) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[5][0]++;
      cov_2o7l4pzoiz().s[53]++;
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[5][1]++;
    }
    // Remove sensitive data before sending
    const safeSessionData =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[54]++, {
      sessionId,
      userId: sessionData.userId,
      email: sessionData.email,
      role: sessionData.role,
      loginTime: sessionData.loginTime,
      lastActivity: sessionData.lastActivity,
      deviceInfo: sessionData.deviceInfo,
      isActive: sessionData.isActive
    });
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[55]++;
    res.json({
      success: true,
      data: safeSessionData
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[56]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve session information'
    });
  }
});
/**
 * @swagger
 * /api/sessions/security-check:
 *   get:
 *     summary: Check for security issues with user sessions
 *     tags: [Sessions, Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     hasMultipleSessions:
 *                       type: boolean
 *                     sessionCount:
 *                       type: number
 *                     uniqueIPs:
 *                       type: number
 *                     suspiciousActivity:
 *                       type: boolean
 *                     recommendations:
 *                       type: array
 *                       items:
 *                         type: string
 */
/* istanbul ignore next */
cov_2o7l4pzoiz().s[57]++;
router.get('/security-check', async (req, res) => {
  /* istanbul ignore next */
  cov_2o7l4pzoiz().f[5]++;
  cov_2o7l4pzoiz().s[58]++;
  try {
    const userId =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[59]++, req.user._id);
    const activeSessions =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[60]++, await sessionService_1.sessionService.getUserActiveSessions(userId));
    const uniqueIPs =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[61]++, new Set(activeSessions.map(session => {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().f[6]++;
      cov_2o7l4pzoiz().s[62]++;
      return session.ipAddress;
    })).size);
    const hasMultipleSessions =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[63]++, activeSessions.length > 1);
    const suspiciousActivity =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[64]++,
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().b[6][0]++, uniqueIPs > 2) ||
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().b[6][1]++, activeSessions.length > 5));
    const recommendations =
    /* istanbul ignore next */
    (cov_2o7l4pzoiz().s[65]++, []);
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[66]++;
    if (hasMultipleSessions) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[7][0]++;
      cov_2o7l4pzoiz().s[67]++;
      recommendations.push('Consider terminating unused sessions for better security');
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[7][1]++;
    }
    cov_2o7l4pzoiz().s[68]++;
    if (uniqueIPs > 2) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[8][0]++;
      cov_2o7l4pzoiz().s[69]++;
      recommendations.push('Multiple IP addresses detected - verify all sessions are yours');
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[8][1]++;
    }
    cov_2o7l4pzoiz().s[70]++;
    if (activeSessions.length > 5) {
      /* istanbul ignore next */
      cov_2o7l4pzoiz().b[9][0]++;
      cov_2o7l4pzoiz().s[71]++;
      recommendations.push('Many active sessions detected - consider session cleanup');
    } else
    /* istanbul ignore next */
    {
      cov_2o7l4pzoiz().b[9][1]++;
    }
    // Log security check
    cov_2o7l4pzoiz().s[72]++;
    await auditService_1.auditService.logEvent(auditService_1.AuditEventType.DATA_VIEWED, req, {
      resource: 'session_security_check',
      metadata: {
        sessionCount: activeSessions.length,
        uniqueIPs,
        suspiciousActivity
      }
    });
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[73]++;
    res.json({
      success: true,
      data: {
        hasMultipleSessions,
        sessionCount: activeSessions.length,
        uniqueIPs,
        suspiciousActivity,
        recommendations
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_2o7l4pzoiz().s[74]++;
    res.status(500).json({
      success: false,
      error: 'Failed to perform security check'
    });
  }
});
/* istanbul ignore next */
cov_2o7l4pzoiz().s[75]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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