9a231bde5a5b226789d6ea13c54a7098
"use strict";

/* istanbul ignore next */
function cov_1twxbrdvh7() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\message.routes.ts";
  var hash = "e715f0eaf79d69454c6c64db99fb18a2d1eddb94";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\message.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 38
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 8,
          column: 3
        }
      },
      "4": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 7,
          column: 89
        }
      },
      "5": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 22
          },
          end: {
            line: 6,
            column: 23
          }
        },
        loc: {
          start: {
            line: 6,
            column: 37
          },
          end: {
            line: 8,
            column: 1
          }
        },
        line: 6
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\message.routes.ts",
      mappings: ";;AAAA,qCAAiC;AAEjC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,sDAAsD;AACtD,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAClC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\message.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\n\r\nconst router = Router();\r\n\r\n// Placeholder routes - will be implemented in Phase 2\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ message: 'Message routes working', timestamp: new Date().toISOString() });\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e715f0eaf79d69454c6c64db99fb18a2d1eddb94"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1twxbrdvh7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1twxbrdvh7();
cov_1twxbrdvh7().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_1twxbrdvh7().s[1]++, require("express"));
const router =
/* istanbul ignore next */
(cov_1twxbrdvh7().s[2]++, (0, express_1.Router)());
// Placeholder routes - will be implemented in Phase 2
/* istanbul ignore next */
cov_1twxbrdvh7().s[3]++;
router.get('/health', (_req, res) => {
  /* istanbul ignore next */
  cov_1twxbrdvh7().f[0]++;
  cov_1twxbrdvh7().s[4]++;
  res.json({
    message: 'Message routes working',
    timestamp: new Date().toISOString()
  });
});
/* istanbul ignore next */
cov_1twxbrdvh7().s[5]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHByZXNzXzEiLCJjb3ZfMXR3eGJyZHZoNyIsInMiLCJyZXF1aXJlIiwicm91dGVyIiwiUm91dGVyIiwiZ2V0IiwiX3JlcSIsInJlcyIsImYiLCJqc29uIiwibWVzc2FnZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImV4cG9ydHMiLCJkZWZhdWx0Il0sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNWSBQQ1xcRGVza3RvcFxcbGFqb3NwYWNlc1xcbGFqb3NwYWNlc2JhY2tlbmRcXHNyY1xccm91dGVzXFxtZXNzYWdlLnJvdXRlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSb3V0ZXIgfSBmcm9tICdleHByZXNzJztcclxuXHJcbmNvbnN0IHJvdXRlciA9IFJvdXRlcigpO1xyXG5cclxuLy8gUGxhY2Vob2xkZXIgcm91dGVzIC0gd2lsbCBiZSBpbXBsZW1lbnRlZCBpbiBQaGFzZSAyXHJcbnJvdXRlci5nZXQoJy9oZWFsdGgnLCAoX3JlcSwgcmVzKSA9PiB7XHJcbiAgcmVzLmpzb24oeyBtZXNzYWdlOiAnTWVzc2FnZSByb3V0ZXMgd29ya2luZycsIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIH0pO1xyXG59KTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IHJvdXRlcjtcclxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLE1BQUFBLFNBQUE7QUFBQTtBQUFBLENBQUFDLGNBQUEsR0FBQUMsQ0FBQSxPQUFBQyxPQUFBO0FBRUEsTUFBTUMsTUFBTTtBQUFBO0FBQUEsQ0FBQUgsY0FBQSxHQUFBQyxDQUFBLE9BQUcsSUFBQUYsU0FBQSxDQUFBSyxNQUFNLEdBQUU7QUFFdkI7QUFBQTtBQUFBSixjQUFBLEdBQUFDLENBQUE7QUFDQUUsTUFBTSxDQUFDRSxHQUFHLENBQUMsU0FBUyxFQUFFLENBQUNDLElBQUksRUFBRUMsR0FBRyxLQUFJO0VBQUE7RUFBQVAsY0FBQSxHQUFBUSxDQUFBO0VBQUFSLGNBQUEsR0FBQUMsQ0FBQTtFQUNsQ00sR0FBRyxDQUFDRSxJQUFJLENBQUM7SUFBRUMsT0FBTyxFQUFFLHdCQUF3QjtJQUFFQyxTQUFTLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUNDLFdBQVc7RUFBRSxDQUFFLENBQUM7QUFDdEYsQ0FBQyxDQUFDO0FBQUM7QUFBQWIsY0FBQSxHQUFBQyxDQUFBO0FBRUhhLE9BQUEsQ0FBQUMsT0FBQSxHQUFlWixNQUFNIiwiaWdub3JlTGlzdCI6W119