{"version": 3, "names": ["cov_<PERSON><PERSON><PERSON><PERSON>", "actualCoverage", "redis_1", "s", "require", "express_session_1", "__importDefault", "connect_redis_1", "environment_1", "logger_1", "appError_1", "crypto_1", "SessionService", "constructor", "f", "connected", "redisClient", "createClient", "url", "config", "REDIS_URL", "socket", "connectTimeout", "lazyConnect", "setupEventHandlers", "on", "logger", "info", "err", "error", "warn", "connect", "b", "store", "default", "client", "prefix", "ttl", "disconnect", "quit", "getSessionConfig", "secret", "SESSION_SECRET", "randomBytes", "toString", "name", "resave", "saveUninitialized", "rolling", "cookie", "secure", "NODE_ENV", "httpOnly", "maxAge", "sameSite", "createSessionMiddleware", "sessionConfig", "createUserSession", "sessionId", "userId", "email", "role", "req", "userSession", "loginTime", "Date", "lastActivity", "ip<PERSON><PERSON><PERSON>", "ip", "connection", "remoteAddress", "userAgent", "get", "deviceInfo", "parseUserAgent", "isActive", "sessionData", "setEx", "JSON", "stringify", "trackActiveSession", "AppError", "getUserSession", "parse", "updateUserSession", "existingSession", "updatedSession", "destroyUserSession", "del", "removeActiveSession", "activeSession", "createdAt", "sAdd", "expire", "sRem", "getUserActiveSessions", "sessionIds", "sMembers", "sessions", "push", "filter", "session", "terminateOtherSessions", "currentSessionId", "activeSessions", "terminatedCount", "cleanupExpiredSessions", "pattern", "keys", "cleanedCount", "key", "getSessionStats", "userSessionKeys", "activeSessionKeys", "totalUserSessions", "length", "totalActiveSessions", "timestamp", "toISOString", "message", "browser", "os", "device", "detectBrowser", "detectOS", "detectDevice", "includes", "trackUserActivity", "res", "next", "id", "user", "hasMultipleSessions", "isConnected", "exports", "sessionService"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\sessionService.ts"], "sourcesContent": ["import { createClient, RedisClientType } from 'redis';\r\nimport { Request, Response, NextFunction } from 'express';\r\nimport session from 'express-session';\r\nimport RedisStore from 'connect-redis';\r\nimport { config } from '../config/environment';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\nimport crypto from 'crypto';\r\n\r\n// Session configuration interface\r\nexport interface SessionConfig {\r\n  secret: string;\r\n  name: string;\r\n  resave: boolean;\r\n  saveUninitialized: boolean;\r\n  rolling: boolean;\r\n  cookie: {\r\n    secure: boolean;\r\n    httpOnly: boolean;\r\n    maxAge: number;\r\n    sameSite: 'strict' | 'lax' | 'none';\r\n  };\r\n  store?: any;\r\n}\r\n\r\n// User session data interface\r\nexport interface UserSession {\r\n  userId: string;\r\n  email: string;\r\n  role: string;\r\n  loginTime: Date;\r\n  lastActivity: Date;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  deviceInfo?: {\r\n    browser: string;\r\n    os: string;\r\n    device: string;\r\n  };\r\n  isActive: boolean;\r\n  sessionData?: Record<string, any>;\r\n}\r\n\r\n// Active session tracking\r\nexport interface ActiveSession {\r\n  sessionId: string;\r\n  userId: string;\r\n  createdAt: Date;\r\n  lastActivity: Date;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  isActive: boolean;\r\n}\r\n\r\nclass SessionService {\r\n  private redisClient: RedisClientType;\r\n  private store: RedisStore | undefined;\r\n  private connected: boolean = false;\r\n\r\n  constructor() {\r\n    // Create Redis client for sessions\r\n    this.redisClient = createClient({\r\n      url: config.REDIS_URL,\r\n      socket: {\r\n        connectTimeout: 5000,\r\n        lazyConnect: true\r\n      }\r\n    });\r\n\r\n    this.setupEventHandlers();\r\n    // Store will be created after connection is established\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    this.redisClient.on('connect', () => {\r\n      logger.info('Redis session client connecting...');\r\n    });\r\n\r\n    this.redisClient.on('ready', () => {\r\n      this.connected = true;\r\n      logger.info('Redis session client connected and ready');\r\n    });\r\n\r\n    this.redisClient.on('error', (err) => {\r\n      this.connected = false;\r\n      logger.error('Redis session client error:', err);\r\n    });\r\n\r\n    this.redisClient.on('end', () => {\r\n      this.connected = false;\r\n      logger.warn('Redis session client connection ended');\r\n    });\r\n  }\r\n\r\n  async connect(): Promise<void> {\r\n    try {\r\n      if (!this.connected) {\r\n        await this.redisClient.connect();\r\n\r\n        // Create Redis store after connection is established\r\n        this.store = new RedisStore({\r\n          client: this.redisClient,\r\n          prefix: 'sess:',\r\n          ttl: 24 * 60 * 60 // 24 hours\r\n        });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Failed to connect to Redis for sessions:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async disconnect(): Promise<void> {\r\n    try {\r\n      if (this.connected) {\r\n        await this.redisClient.quit();\r\n        this.connected = false;\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error disconnecting from Redis sessions:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get session configuration\r\n   */\r\n  getSessionConfig(): SessionConfig {\r\n    return {\r\n      secret: config.SESSION_SECRET || crypto.randomBytes(32).toString('hex'),\r\n      name: 'lajospaces.sid',\r\n      resave: false,\r\n      saveUninitialized: false,\r\n      rolling: true,\r\n      cookie: {\r\n        secure: config.NODE_ENV === 'production',\r\n        httpOnly: true,\r\n        maxAge: 24 * 60 * 60 * 1000, // 24 hours\r\n        sameSite: config.NODE_ENV === 'production' ? 'strict' : 'lax'\r\n      },\r\n      store: this.store // Will be undefined if Redis is not connected\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create session middleware\r\n   */\r\n  createSessionMiddleware() {\r\n    const sessionConfig = this.getSessionConfig();\r\n    return session(sessionConfig);\r\n  }\r\n\r\n  /**\r\n   * Create a new user session\r\n   */\r\n  async createUserSession(\r\n    sessionId: string,\r\n    userId: string,\r\n    email: string,\r\n    role: string,\r\n    req: Request\r\n  ): Promise<UserSession> {\r\n    const userSession: UserSession = {\r\n      userId,\r\n      email,\r\n      role,\r\n      loginTime: new Date(),\r\n      lastActivity: new Date(),\r\n      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',\r\n      userAgent: req.get('User-Agent') || 'unknown',\r\n      deviceInfo: this.parseUserAgent(req.get('User-Agent')),\r\n      isActive: true,\r\n      sessionData: {}\r\n    };\r\n\r\n    try {\r\n      // Store user session data\r\n      await this.redisClient.setEx(\r\n        `user_session:${sessionId}`,\r\n        24 * 60 * 60, // 24 hours\r\n        JSON.stringify(userSession)\r\n      );\r\n\r\n      // Track active session\r\n      await this.trackActiveSession(sessionId, userId, req);\r\n\r\n      logger.info('User session created', {\r\n        sessionId,\r\n        userId,\r\n        email,\r\n        ipAddress: userSession.ipAddress\r\n      });\r\n\r\n      return userSession;\r\n    } catch (error) {\r\n      logger.error('Error creating user session:', error);\r\n      throw new AppError('Failed to create session', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user session\r\n   */\r\n  async getUserSession(sessionId: string): Promise<UserSession | null> {\r\n    if (!this.connected) {\r\n      return null;\r\n    }\r\n\r\n    try {\r\n      const sessionData = await this.redisClient.get(`user_session:${sessionId}`);\r\n      if (!sessionData) {\r\n        return null;\r\n      }\r\n\r\n      const userSession: UserSession = JSON.parse(sessionData);\r\n      \r\n      // Update last activity\r\n      userSession.lastActivity = new Date();\r\n      await this.updateUserSession(sessionId, userSession);\r\n\r\n      return userSession;\r\n    } catch (error) {\r\n      logger.error('Error getting user session:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user session\r\n   */\r\n  async updateUserSession(sessionId: string, sessionData: Partial<UserSession>): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const existingSession = await this.getUserSession(sessionId);\r\n      if (!existingSession) {\r\n        return false;\r\n      }\r\n\r\n      const updatedSession = { ...existingSession, ...sessionData };\r\n      \r\n      await this.redisClient.setEx(\r\n        `user_session:${sessionId}`,\r\n        24 * 60 * 60,\r\n        JSON.stringify(updatedSession)\r\n      );\r\n\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Error updating user session:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroy user session\r\n   */\r\n  async destroyUserSession(sessionId: string): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      // Get session data before destroying\r\n      const sessionData = await this.getUserSession(sessionId);\r\n      \r\n      // Remove user session\r\n      await this.redisClient.del(`user_session:${sessionId}`);\r\n      \r\n      // Remove from active sessions\r\n      if (sessionData) {\r\n        await this.removeActiveSession(sessionId, sessionData.userId);\r\n      }\r\n\r\n      logger.info('User session destroyed', { sessionId });\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Error destroying user session:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Track active session\r\n   */\r\n  private async trackActiveSession(sessionId: string, userId: string, req: Request): Promise<void> {\r\n    const activeSession: ActiveSession = {\r\n      sessionId,\r\n      userId,\r\n      createdAt: new Date(),\r\n      lastActivity: new Date(),\r\n      ipAddress: req.ip || 'unknown',\r\n      userAgent: req.get('User-Agent') || 'unknown',\r\n      isActive: true\r\n    };\r\n\r\n    try {\r\n      // Add to user's active sessions set\r\n      await this.redisClient.sAdd(`active_sessions:${userId}`, sessionId);\r\n      \r\n      // Store session details\r\n      await this.redisClient.setEx(\r\n        `active_session:${sessionId}`,\r\n        24 * 60 * 60,\r\n        JSON.stringify(activeSession)\r\n      );\r\n\r\n      // Set expiration for the set\r\n      await this.redisClient.expire(`active_sessions:${userId}`, 24 * 60 * 60);\r\n    } catch (error) {\r\n      logger.error('Error tracking active session:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove active session\r\n   */\r\n  private async removeActiveSession(sessionId: string, userId: string): Promise<void> {\r\n    try {\r\n      await this.redisClient.sRem(`active_sessions:${userId}`, sessionId);\r\n      await this.redisClient.del(`active_session:${sessionId}`);\r\n    } catch (error) {\r\n      logger.error('Error removing active session:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user's active sessions\r\n   */\r\n  async getUserActiveSessions(userId: string): Promise<ActiveSession[]> {\r\n    if (!this.connected) {\r\n      return [];\r\n    }\r\n\r\n    try {\r\n      const sessionIds = await this.redisClient.sMembers(`active_sessions:${userId}`);\r\n      const sessions: ActiveSession[] = [];\r\n\r\n      for (const sessionId of sessionIds) {\r\n        const sessionData = await this.redisClient.get(`active_session:${sessionId}`);\r\n        if (sessionData) {\r\n          sessions.push(JSON.parse(sessionData));\r\n        }\r\n      }\r\n\r\n      return sessions.filter(session => session.isActive);\r\n    } catch (error) {\r\n      logger.error('Error getting user active sessions:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Terminate all user sessions except current\r\n   */\r\n  async terminateOtherSessions(userId: string, currentSessionId: string): Promise<number> {\r\n    if (!this.connected) {\r\n      return 0;\r\n    }\r\n\r\n    try {\r\n      const activeSessions = await this.getUserActiveSessions(userId);\r\n      let terminatedCount = 0;\r\n\r\n      for (const session of activeSessions) {\r\n        if (session.sessionId !== currentSessionId) {\r\n          await this.destroyUserSession(session.sessionId);\r\n          terminatedCount++;\r\n        }\r\n      }\r\n\r\n      logger.info('Terminated other user sessions', {\r\n        userId,\r\n        currentSessionId,\r\n        terminatedCount\r\n      });\r\n\r\n      return terminatedCount;\r\n    } catch (error) {\r\n      logger.error('Error terminating other sessions:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean up expired sessions\r\n   */\r\n  async cleanupExpiredSessions(): Promise<number> {\r\n    if (!this.connected) {\r\n      return 0;\r\n    }\r\n\r\n    try {\r\n      const pattern = 'user_session:*';\r\n      const keys = await this.redisClient.keys(pattern);\r\n      let cleanedCount = 0;\r\n\r\n      for (const key of keys) {\r\n        const ttl = await this.redisClient.ttl(key);\r\n        if (ttl <= 0) {\r\n          await this.redisClient.del(key);\r\n          cleanedCount++;\r\n        }\r\n      }\r\n\r\n      logger.info(`Cleaned up ${cleanedCount} expired sessions`);\r\n      return cleanedCount;\r\n    } catch (error) {\r\n      logger.error('Error cleaning up expired sessions:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get session statistics\r\n   */\r\n  async getSessionStats(): Promise<any> {\r\n    if (!this.connected) {\r\n      return { connected: false };\r\n    }\r\n\r\n    try {\r\n      const userSessionKeys = await this.redisClient.keys('user_session:*');\r\n      const activeSessionKeys = await this.redisClient.keys('active_session:*');\r\n      \r\n      return {\r\n        connected: this.connected,\r\n        totalUserSessions: userSessionKeys.length,\r\n        totalActiveSessions: activeSessionKeys.length,\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    } catch (error) {\r\n      logger.error('Error getting session stats:', error);\r\n      return { connected: false, error: error.message };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse user agent for device info\r\n   */\r\n  private parseUserAgent(userAgent?: string): any {\r\n    if (!userAgent) {\r\n      return { browser: 'unknown', os: 'unknown', device: 'unknown' };\r\n    }\r\n\r\n    // Simple user agent parsing (you might want to use a library like 'ua-parser-js')\r\n    const browser = this.detectBrowser(userAgent);\r\n    const os = this.detectOS(userAgent);\r\n    const device = this.detectDevice(userAgent);\r\n\r\n    return { browser, os, device };\r\n  }\r\n\r\n  private detectBrowser(userAgent: string): string {\r\n    if (userAgent.includes('Chrome')) return 'Chrome';\r\n    if (userAgent.includes('Firefox')) return 'Firefox';\r\n    if (userAgent.includes('Safari')) return 'Safari';\r\n    if (userAgent.includes('Edge')) return 'Edge';\r\n    if (userAgent.includes('Opera')) return 'Opera';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectOS(userAgent: string): string {\r\n    if (userAgent.includes('Windows')) return 'Windows';\r\n    if (userAgent.includes('Mac OS')) return 'macOS';\r\n    if (userAgent.includes('Linux')) return 'Linux';\r\n    if (userAgent.includes('Android')) return 'Android';\r\n    if (userAgent.includes('iOS')) return 'iOS';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectDevice(userAgent: string): string {\r\n    if (userAgent.includes('Mobile')) return 'mobile';\r\n    if (userAgent.includes('Tablet')) return 'tablet';\r\n    return 'desktop';\r\n  }\r\n\r\n  /**\r\n   * Session middleware for tracking user activity\r\n   */\r\n  trackUserActivity() {\r\n    return async (req: Request, res: Response, next: NextFunction) => {\r\n      if (req.session && req.session.id && req.user) {\r\n        try {\r\n          await this.updateUserSession(req.session.id, {\r\n            lastActivity: new Date()\r\n          });\r\n        } catch (error) {\r\n          logger.error('Error tracking user activity:', error);\r\n        }\r\n      }\r\n      next();\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if user has multiple active sessions\r\n   */\r\n  async hasMultipleSessions(userId: string): Promise<boolean> {\r\n    const activeSessions = await this.getUserActiveSessions(userId);\r\n    return activeSessions.length > 1;\r\n  }\r\n\r\n  isConnected(): boolean {\r\n    return this.connected;\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const sessionService = new SessionService();\r\n\r\nexport default sessionService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyDU;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzDV,MAAAE,OAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,MAAAC,iBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,MAAAG,eAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,MAAAI,aAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAK,QAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAM,UAAA;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAO,QAAA;AAAA;AAAA,CAAAX,cAAA,GAAAG,CAAA,QAAAG,eAAA,CAAAF,OAAA;AA+CA,MAAMQ,cAAc;EAKlBC,YAAA;IAAA;IAAAb,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAFQ,KAAAY,SAAS,GAAY,KAAK;IAGhC;IAAA;IAAAf,cAAA,GAAAG,CAAA;IACA,IAAI,CAACa,WAAW,GAAG,IAAAd,OAAA,CAAAe,YAAY,EAAC;MAC9BC,GAAG,EAAEV,aAAA,CAAAW,MAAM,CAACC,SAAS;MACrBC,MAAM,EAAE;QACNC,cAAc,EAAE,IAAI;QACpBC,WAAW,EAAE;;KAEhB,CAAC;IAAC;IAAAvB,cAAA,GAAAG,CAAA;IAEH,IAAI,CAACqB,kBAAkB,EAAE;IACzB;EACF;EAEQA,kBAAkBA,CAAA;IAAA;IAAAxB,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxB,IAAI,CAACa,WAAW,CAACS,EAAE,CAAC,SAAS,EAAE,MAAK;MAAA;MAAAzB,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MAClCM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,oCAAoC,CAAC;IACnD,CAAC,CAAC;IAAC;IAAA3B,cAAA,GAAAG,CAAA;IAEH,IAAI,CAACa,WAAW,CAACS,EAAE,CAAC,OAAO,EAAE,MAAK;MAAA;MAAAzB,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MAChC,IAAI,CAACY,SAAS,GAAG,IAAI;MAAC;MAAAf,cAAA,GAAAG,CAAA;MACtBM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC;IAAC;IAAA3B,cAAA,GAAAG,CAAA;IAEH,IAAI,CAACa,WAAW,CAACS,EAAE,CAAC,OAAO,EAAGG,GAAG,IAAI;MAAA;MAAA5B,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MACnC,IAAI,CAACY,SAAS,GAAG,KAAK;MAAC;MAAAf,cAAA,GAAAG,CAAA;MACvBM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC;IAClD,CAAC,CAAC;IAAC;IAAA5B,cAAA,GAAAG,CAAA;IAEH,IAAI,CAACa,WAAW,CAACS,EAAE,CAAC,KAAK,EAAE,MAAK;MAAA;MAAAzB,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MAC9B,IAAI,CAACY,SAAS,GAAG,KAAK;MAAC;MAAAf,cAAA,GAAAG,CAAA;MACvBM,QAAA,CAAAiB,MAAM,CAACI,IAAI,CAAC,uCAAuC,CAAC;IACtD,CAAC,CAAC;EACJ;EAEA,MAAMC,OAAOA,CAAA;IAAA;IAAA/B,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACX,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;QAAA;QAAAf,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QACnB,MAAM,IAAI,CAACa,WAAW,CAACe,OAAO,EAAE;QAEhC;QAAA;QAAA/B,cAAA,GAAAG,CAAA;QACA,IAAI,CAAC8B,KAAK,GAAG,IAAI1B,eAAA,CAAA2B,OAAU,CAAC;UAC1BC,MAAM,EAAE,IAAI,CAACnB,WAAW;UACxBoB,MAAM,EAAE,OAAO;UACfC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACnB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAArC,cAAA,GAAAgC,CAAA;MAAA;IACH,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MAChE,MAAM0B,KAAK;IACb;EACF;EAEA,MAAMS,UAAUA,CAAA;IAAA;IAAAtC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACd,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF,IAAI,IAAI,CAACY,SAAS,EAAE;QAAA;QAAAf,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAI,CAACa,WAAW,CAACuB,IAAI,EAAE;QAAC;QAAAvC,cAAA,GAAAG,CAAA;QAC9B,IAAI,CAACY,SAAS,GAAG,KAAK;MACxB,CAAC;MAAA;MAAA;QAAAf,cAAA,GAAAgC,CAAA;MAAA;IACH,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IACjE;EACF;EAEA;;;EAGAW,gBAAgBA,CAAA;IAAA;IAAAxC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACd,OAAO;MACLsC,MAAM;MAAE;MAAA,CAAAzC,cAAA,GAAAgC,CAAA,UAAAxB,aAAA,CAAAW,MAAM,CAACuB,cAAc;MAAA;MAAA,CAAA1C,cAAA,GAAAgC,CAAA,UAAIrB,QAAA,CAAAuB,OAAM,CAACS,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;MACvEC,IAAI,EAAE,gBAAgB;MACtBC,MAAM,EAAE,KAAK;MACbC,iBAAiB,EAAE,KAAK;MACxBC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE;QACNC,MAAM,EAAE1C,aAAA,CAAAW,MAAM,CAACgC,QAAQ,KAAK,YAAY;QACxCC,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAAE;QAC7BC,QAAQ,EAAE9C,aAAA,CAAAW,MAAM,CAACgC,QAAQ,KAAK,YAAY;QAAA;QAAA,CAAAnD,cAAA,GAAAgC,CAAA,UAAG,QAAQ;QAAA;QAAA,CAAAhC,cAAA,GAAAgC,CAAA,UAAG,KAAK;OAC9D;MACDC,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC;KACnB;EACH;EAEA;;;EAGAsB,uBAAuBA,CAAA;IAAA;IAAAvD,cAAA,GAAAc,CAAA;IACrB,MAAM0C,aAAa;IAAA;IAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACqC,gBAAgB,EAAE;IAAC;IAAAxC,cAAA,GAAAG,CAAA;IAC9C,OAAO,IAAAE,iBAAA,CAAA6B,OAAO,EAACsB,aAAa,CAAC;EAC/B;EAEA;;;EAGA,MAAMC,iBAAiBA,CACrBC,SAAiB,EACjBC,MAAc,EACdC,KAAa,EACbC,IAAY,EACZC,GAAY;IAAA;IAAA9D,cAAA,GAAAc,CAAA;IAEZ,MAAMiD,WAAW;IAAA;IAAA,CAAA/D,cAAA,GAAAG,CAAA,QAAgB;MAC/BwD,MAAM;MACNC,KAAK;MACLC,IAAI;MACJG,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,YAAY,EAAE,IAAID,IAAI,EAAE;MACxBE,SAAS;MAAE;MAAA,CAAAnE,cAAA,GAAAgC,CAAA,UAAA8B,GAAG,CAACM,EAAE;MAAA;MAAA,CAAApE,cAAA,GAAAgC,CAAA,UAAI8B,GAAG,CAACO,UAAU,CAACC,aAAa;MAAA;MAAA,CAAAtE,cAAA,GAAAgC,CAAA,UAAI,SAAS;MAC9DuC,SAAS;MAAE;MAAA,CAAAvE,cAAA,GAAAgC,CAAA,UAAA8B,GAAG,CAACU,GAAG,CAAC,YAAY,CAAC;MAAA;MAAA,CAAAxE,cAAA,GAAAgC,CAAA,UAAI,SAAS;MAC7CyC,UAAU,EAAE,IAAI,CAACC,cAAc,CAACZ,GAAG,CAACU,GAAG,CAAC,YAAY,CAAC,CAAC;MACtDG,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd;IAAC;IAAA5E,cAAA,GAAAG,CAAA;IAEF,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF;MACA,MAAM,IAAI,CAACa,WAAW,CAAC6D,KAAK,CAC1B,gBAAgBnB,SAAS,EAAE,EAC3B,EAAE,GAAG,EAAE,GAAG,EAAE;MAAE;MACdoB,IAAI,CAACC,SAAS,CAAChB,WAAW,CAAC,CAC5B;MAED;MAAA;MAAA/D,cAAA,GAAAG,CAAA;MACA,MAAM,IAAI,CAAC6E,kBAAkB,CAACtB,SAAS,EAAEC,MAAM,EAAEG,GAAG,CAAC;MAAC;MAAA9D,cAAA,GAAAG,CAAA;MAEtDM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,sBAAsB,EAAE;QAClC+B,SAAS;QACTC,MAAM;QACNC,KAAK;QACLO,SAAS,EAAEJ,WAAW,CAACI;OACxB,CAAC;MAAC;MAAAnE,cAAA,GAAAG,CAAA;MAEH,OAAO4D,WAAW;IACpB,CAAC,CAAC,OAAOlC,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACpD,MAAM,IAAIO,UAAA,CAAAuE,QAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC;IACrD;EACF;EAEA;;;EAGA,MAAMC,cAAcA,CAACxB,SAAiB;IAAA;IAAA1D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACpC,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAMyE,WAAW;MAAA;MAAA,CAAA5E,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAI,CAACa,WAAW,CAACwD,GAAG,CAAC,gBAAgBd,SAAS,EAAE,CAAC;MAAC;MAAA1D,cAAA,GAAAG,CAAA;MAC5E,IAAI,CAACyE,WAAW,EAAE;QAAA;QAAA5E,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QAChB,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAgC,CAAA;MAAA;MAED,MAAM+B,WAAW;MAAA;MAAA,CAAA/D,cAAA,GAAAG,CAAA,QAAgB2E,IAAI,CAACK,KAAK,CAACP,WAAW,CAAC;MAExD;MAAA;MAAA5E,cAAA,GAAAG,CAAA;MACA4D,WAAW,CAACG,YAAY,GAAG,IAAID,IAAI,EAAE;MAAC;MAAAjE,cAAA,GAAAG,CAAA;MACtC,MAAM,IAAI,CAACiF,iBAAiB,CAAC1B,SAAS,EAAEK,WAAW,CAAC;MAAC;MAAA/D,cAAA,GAAAG,CAAA;MAErD,OAAO4D,WAAW;IACpB,CAAC,CAAC,OAAOlC,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACnD,OAAO,IAAI;IACb;EACF;EAEA;;;EAGA,MAAMiF,iBAAiBA,CAAC1B,SAAiB,EAAEkB,WAAiC;IAAA;IAAA5E,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC1E,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAMkF,eAAe;MAAA;MAAA,CAAArF,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAI,CAAC+E,cAAc,CAACxB,SAAS,CAAC;MAAC;MAAA1D,cAAA,GAAAG,CAAA;MAC7D,IAAI,CAACkF,eAAe,EAAE;QAAA;QAAArF,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QACpB,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAgC,CAAA;MAAA;MAED,MAAMsD,cAAc;MAAA;MAAA,CAAAtF,cAAA,GAAAG,CAAA,QAAG;QAAE,GAAGkF,eAAe;QAAE,GAAGT;MAAW,CAAE;MAAC;MAAA5E,cAAA,GAAAG,CAAA;MAE9D,MAAM,IAAI,CAACa,WAAW,CAAC6D,KAAK,CAC1B,gBAAgBnB,SAAS,EAAE,EAC3B,EAAE,GAAG,EAAE,GAAG,EAAE,EACZoB,IAAI,CAACC,SAAS,CAACO,cAAc,CAAC,CAC/B;MAAC;MAAAtF,cAAA,GAAAG,CAAA;MAEF,OAAO,IAAI;IACb,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACpD,OAAO,KAAK;IACd;EACF;EAEA;;;EAGA,MAAMoF,kBAAkBA,CAAC7B,SAAiB;IAAA;IAAA1D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxC,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF;MACA,MAAMyE,WAAW;MAAA;MAAA,CAAA5E,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAI,CAAC+E,cAAc,CAACxB,SAAS,CAAC;MAExD;MAAA;MAAA1D,cAAA,GAAAG,CAAA;MACA,MAAM,IAAI,CAACa,WAAW,CAACwE,GAAG,CAAC,gBAAgB9B,SAAS,EAAE,CAAC;MAEvD;MAAA;MAAA1D,cAAA,GAAAG,CAAA;MACA,IAAIyE,WAAW,EAAE;QAAA;QAAA5E,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QACf,MAAM,IAAI,CAACsF,mBAAmB,CAAC/B,SAAS,EAAEkB,WAAW,CAACjB,MAAM,CAAC;MAC/D,CAAC;MAAA;MAAA;QAAA3D,cAAA,GAAAgC,CAAA;MAAA;MAAAhC,cAAA,GAAAG,CAAA;MAEDM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAE;QAAE+B;MAAS,CAAE,CAAC;MAAC;MAAA1D,cAAA,GAAAG,CAAA;MACrD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACtD,OAAO,KAAK;IACd;EACF;EAEA;;;EAGQ,MAAM6E,kBAAkBA,CAACtB,SAAiB,EAAEC,MAAc,EAAEG,GAAY;IAAA;IAAA9D,cAAA,GAAAc,CAAA;IAC9E,MAAM4E,aAAa;IAAA;IAAA,CAAA1F,cAAA,GAAAG,CAAA,QAAkB;MACnCuD,SAAS;MACTC,MAAM;MACNgC,SAAS,EAAE,IAAI1B,IAAI,EAAE;MACrBC,YAAY,EAAE,IAAID,IAAI,EAAE;MACxBE,SAAS;MAAE;MAAA,CAAAnE,cAAA,GAAAgC,CAAA,WAAA8B,GAAG,CAACM,EAAE;MAAA;MAAA,CAAApE,cAAA,GAAAgC,CAAA,WAAI,SAAS;MAC9BuC,SAAS;MAAE;MAAA,CAAAvE,cAAA,GAAAgC,CAAA,WAAA8B,GAAG,CAACU,GAAG,CAAC,YAAY,CAAC;MAAA;MAAA,CAAAxE,cAAA,GAAAgC,CAAA,WAAI,SAAS;MAC7C2C,QAAQ,EAAE;KACX;IAAC;IAAA3E,cAAA,GAAAG,CAAA;IAEF,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF;MACA,MAAM,IAAI,CAACa,WAAW,CAAC4E,IAAI,CAAC,mBAAmBjC,MAAM,EAAE,EAAED,SAAS,CAAC;MAEnE;MAAA;MAAA1D,cAAA,GAAAG,CAAA;MACA,MAAM,IAAI,CAACa,WAAW,CAAC6D,KAAK,CAC1B,kBAAkBnB,SAAS,EAAE,EAC7B,EAAE,GAAG,EAAE,GAAG,EAAE,EACZoB,IAAI,CAACC,SAAS,CAACW,aAAa,CAAC,CAC9B;MAED;MAAA;MAAA1F,cAAA,GAAAG,CAAA;MACA,MAAM,IAAI,CAACa,WAAW,CAAC6E,MAAM,CAAC,mBAAmBlC,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAC1E,CAAC,CAAC,OAAO9B,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACvD;EACF;EAEA;;;EAGQ,MAAM4D,mBAAmBA,CAAC/B,SAAiB,EAAEC,MAAc;IAAA;IAAA3D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACjE,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF,MAAM,IAAI,CAACa,WAAW,CAAC8E,IAAI,CAAC,mBAAmBnC,MAAM,EAAE,EAAED,SAAS,CAAC;MAAC;MAAA1D,cAAA,GAAAG,CAAA;MACpE,MAAM,IAAI,CAACa,WAAW,CAACwE,GAAG,CAAC,kBAAkB9B,SAAS,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACvD;EACF;EAEA;;;EAGA,MAAMkE,qBAAqBA,CAACpC,MAAc;IAAA;IAAA3D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxC,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,EAAE;IACX,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAM6F,UAAU;MAAA;MAAA,CAAAhG,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAI,CAACa,WAAW,CAACiF,QAAQ,CAAC,mBAAmBtC,MAAM,EAAE,CAAC;MAC/E,MAAMuC,QAAQ;MAAA;MAAA,CAAAlG,cAAA,GAAAG,CAAA,QAAoB,EAAE;MAAC;MAAAH,cAAA,GAAAG,CAAA;MAErC,KAAK,MAAMuD,SAAS,IAAIsC,UAAU,EAAE;QAClC,MAAMpB,WAAW;QAAA;QAAA,CAAA5E,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAI,CAACa,WAAW,CAACwD,GAAG,CAAC,kBAAkBd,SAAS,EAAE,CAAC;QAAC;QAAA1D,cAAA,GAAAG,CAAA;QAC9E,IAAIyE,WAAW,EAAE;UAAA;UAAA5E,cAAA,GAAAgC,CAAA;UAAAhC,cAAA,GAAAG,CAAA;UACf+F,QAAQ,CAACC,IAAI,CAACrB,IAAI,CAACK,KAAK,CAACP,WAAW,CAAC,CAAC;QACxC,CAAC;QAAA;QAAA;UAAA5E,cAAA,GAAAgC,CAAA;QAAA;MACH;MAAC;MAAAhC,cAAA,GAAAG,CAAA;MAED,OAAO+F,QAAQ,CAACE,MAAM,CAACC,OAAO,IAAI;QAAA;QAAArG,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,OAAAkG,OAAO,CAAC1B,QAAQ;MAAR,CAAQ,CAAC;IACrD,CAAC,CAAC,OAAO9C,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MAC3D,OAAO,EAAE;IACX;EACF;EAEA;;;EAGA,MAAMmG,sBAAsBA,CAAC3C,MAAc,EAAE4C,gBAAwB;IAAA;IAAAvG,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACnE,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,CAAC;IACV,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAMqG,cAAc;MAAA;MAAA,CAAAxG,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAAC4F,qBAAqB,CAACpC,MAAM,CAAC;MAC/D,IAAI8C,eAAe;MAAA;MAAA,CAAAzG,cAAA,GAAAG,CAAA,SAAG,CAAC;MAAC;MAAAH,cAAA,GAAAG,CAAA;MAExB,KAAK,MAAMkG,OAAO,IAAIG,cAAc,EAAE;QAAA;QAAAxG,cAAA,GAAAG,CAAA;QACpC,IAAIkG,OAAO,CAAC3C,SAAS,KAAK6C,gBAAgB,EAAE;UAAA;UAAAvG,cAAA,GAAAgC,CAAA;UAAAhC,cAAA,GAAAG,CAAA;UAC1C,MAAM,IAAI,CAACoF,kBAAkB,CAACc,OAAO,CAAC3C,SAAS,CAAC;UAAC;UAAA1D,cAAA,GAAAG,CAAA;UACjDsG,eAAe,EAAE;QACnB,CAAC;QAAA;QAAA;UAAAzG,cAAA,GAAAgC,CAAA;QAAA;MACH;MAAC;MAAAhC,cAAA,GAAAG,CAAA;MAEDM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,gCAAgC,EAAE;QAC5CgC,MAAM;QACN4C,gBAAgB;QAChBE;OACD,CAAC;MAAC;MAAAzG,cAAA,GAAAG,CAAA;MAEH,OAAOsG,eAAe;IACxB,CAAC,CAAC,OAAO5E,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACzD,OAAO,CAAC;IACV;EACF;EAEA;;;EAGA,MAAMuG,sBAAsBA,CAAA;IAAA;IAAA1G,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO,CAAC;IACV,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAMwG,OAAO;MAAA;MAAA,CAAA3G,cAAA,GAAAG,CAAA,SAAG,gBAAgB;MAChC,MAAMyG,IAAI;MAAA;MAAA,CAAA5G,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAACa,WAAW,CAAC4F,IAAI,CAACD,OAAO,CAAC;MACjD,IAAIE,YAAY;MAAA;MAAA,CAAA7G,cAAA,GAAAG,CAAA,SAAG,CAAC;MAAC;MAAAH,cAAA,GAAAG,CAAA;MAErB,KAAK,MAAM2G,GAAG,IAAIF,IAAI,EAAE;QACtB,MAAMvE,GAAG;QAAA;QAAA,CAAArC,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAACa,WAAW,CAACqB,GAAG,CAACyE,GAAG,CAAC;QAAC;QAAA9G,cAAA,GAAAG,CAAA;QAC5C,IAAIkC,GAAG,IAAI,CAAC,EAAE;UAAA;UAAArC,cAAA,GAAAgC,CAAA;UAAAhC,cAAA,GAAAG,CAAA;UACZ,MAAM,IAAI,CAACa,WAAW,CAACwE,GAAG,CAACsB,GAAG,CAAC;UAAC;UAAA9G,cAAA,GAAAG,CAAA;UAChC0G,YAAY,EAAE;QAChB,CAAC;QAAA;QAAA;UAAA7G,cAAA,GAAAgC,CAAA;QAAA;MACH;MAAC;MAAAhC,cAAA,GAAAG,CAAA;MAEDM,QAAA,CAAAiB,MAAM,CAACC,IAAI,CAAC,cAAckF,YAAY,mBAAmB,CAAC;MAAC;MAAA7G,cAAA,GAAAG,CAAA;MAC3D,OAAO0G,YAAY;IACrB,CAAC,CAAC,OAAOhF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MAC3D,OAAO,CAAC;IACV;EACF;EAEA;;;EAGA,MAAM4G,eAAeA,CAAA;IAAA;IAAA/G,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACnB,IAAI,CAAC,IAAI,CAACY,SAAS,EAAE;MAAA;MAAAf,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACnB,OAAO;QAAEY,SAAS,EAAE;MAAK,CAAE;IAC7B,CAAC;IAAA;IAAA;MAAAf,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAM6G,eAAe;MAAA;MAAA,CAAAhH,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAACa,WAAW,CAAC4F,IAAI,CAAC,gBAAgB,CAAC;MACrE,MAAMK,iBAAiB;MAAA;MAAA,CAAAjH,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAACa,WAAW,CAAC4F,IAAI,CAAC,kBAAkB,CAAC;MAAC;MAAA5G,cAAA,GAAAG,CAAA;MAE1E,OAAO;QACLY,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBmG,iBAAiB,EAAEF,eAAe,CAACG,MAAM;QACzCC,mBAAmB,EAAEH,iBAAiB,CAACE,MAAM;QAC7CE,SAAS,EAAE,IAAIpD,IAAI,EAAE,CAACqD,WAAW;OAClC;IACH,CAAC,CAAC,OAAOzF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAG,CAAA;MACpD,OAAO;QAAEY,SAAS,EAAE,KAAK;QAAEc,KAAK,EAAEA,KAAK,CAAC0F;MAAO,CAAE;IACnD;EACF;EAEA;;;EAGQ7C,cAAcA,CAACH,SAAkB;IAAA;IAAAvE,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACvC,IAAI,CAACoE,SAAS,EAAE;MAAA;MAAAvE,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MACd,OAAO;QAAEqH,OAAO,EAAE,SAAS;QAAEC,EAAE,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE;IACjE,CAAC;IAAA;IAAA;MAAA1H,cAAA,GAAAgC,CAAA;IAAA;IAED;IACA,MAAMwF,OAAO;IAAA;IAAA,CAAAxH,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACwH,aAAa,CAACpD,SAAS,CAAC;IAC7C,MAAMkD,EAAE;IAAA;IAAA,CAAAzH,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACyH,QAAQ,CAACrD,SAAS,CAAC;IACnC,MAAMmD,MAAM;IAAA;IAAA,CAAA1H,cAAA,GAAAG,CAAA,SAAG,IAAI,CAAC0H,YAAY,CAACtD,SAAS,CAAC;IAAC;IAAAvE,cAAA,GAAAG,CAAA;IAE5C,OAAO;MAAEqH,OAAO;MAAEC,EAAE;MAAEC;IAAM,CAAE;EAChC;EAEQC,aAAaA,CAACpD,SAAiB;IAAA;IAAAvE,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACrC,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAClD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IACpD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAClD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,MAAM,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAC9C,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAChD,OAAO,SAAS;EAClB;EAEQyH,QAAQA,CAACrD,SAAiB;IAAA;IAAAvE,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAChC,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IACpD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IACjD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAChD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IACpD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,KAAK,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAC5C,OAAO,SAAS;EAClB;EAEQ0H,YAAYA,CAACtD,SAAiB;IAAA;IAAAvE,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACpC,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAClD,IAAIoE,SAAS,CAACuD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAA9H,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAG,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAgC,CAAA;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IAClD,OAAO,SAAS;EAClB;EAEA;;;EAGA4H,iBAAiBA,CAAA;IAAA;IAAA/H,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACf,OAAO,OAAO2D,GAAY,EAAEkE,GAAa,EAAEC,IAAkB,KAAI;MAAA;MAAAjI,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MAC/D;MAAI;MAAA,CAAAH,cAAA,GAAAgC,CAAA,WAAA8B,GAAG,CAACuC,OAAO;MAAA;MAAA,CAAArG,cAAA,GAAAgC,CAAA,WAAI8B,GAAG,CAACuC,OAAO,CAAC6B,EAAE;MAAA;MAAA,CAAAlI,cAAA,GAAAgC,CAAA,WAAI8B,GAAG,CAACqE,IAAI,GAAE;QAAA;QAAAnI,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAG,CAAA;QAC7C,IAAI;UAAA;UAAAH,cAAA,GAAAG,CAAA;UACF,MAAM,IAAI,CAACiF,iBAAiB,CAACtB,GAAG,CAACuC,OAAO,CAAC6B,EAAE,EAAE;YAC3ChE,YAAY,EAAE,IAAID,IAAI;WACvB,CAAC;QACJ,CAAC,CAAC,OAAOpC,KAAK,EAAE;UAAA;UAAA7B,cAAA,GAAAG,CAAA;UACdM,QAAA,CAAAiB,MAAM,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACtD;MACF,CAAC;MAAA;MAAA;QAAA7B,cAAA,GAAAgC,CAAA;MAAA;MAAAhC,cAAA,GAAAG,CAAA;MACD8H,IAAI,EAAE;IACR,CAAC;EACH;EAEA;;;EAGA,MAAMG,mBAAmBA,CAACzE,MAAc;IAAA;IAAA3D,cAAA,GAAAc,CAAA;IACtC,MAAM0F,cAAc;IAAA;IAAA,CAAAxG,cAAA,GAAAG,CAAA,SAAG,MAAM,IAAI,CAAC4F,qBAAqB,CAACpC,MAAM,CAAC;IAAC;IAAA3D,cAAA,GAAAG,CAAA;IAChE,OAAOqG,cAAc,CAACW,MAAM,GAAG,CAAC;EAClC;EAEAkB,WAAWA,CAAA;IAAA;IAAArI,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACT,OAAO,IAAI,CAACY,SAAS;EACvB;;AAGF;AAAA;AAAAf,cAAA,GAAAG,CAAA;AACamI,OAAA,CAAAC,cAAc,GAAG,IAAI3H,cAAc,EAAE;AAAC;AAAAZ,cAAA,GAAAG,CAAA;AAEnDmI,OAAA,CAAApG,OAAA,GAAeoG,OAAA,CAAAC,cAAc", "ignoreList": []}