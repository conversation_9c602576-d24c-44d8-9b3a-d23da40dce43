44271ed168381265318e4bf44bc731fa
"use strict";

/* istanbul ignore next */
function cov_2o9aax85y3() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\app.ts";
  var hash = "6c8fb581bb7c6633bb44aa0439b783df401ab331";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\app.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "36": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "37": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "38": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 30
        }
      },
      "39": {
        start: {
          line: 40,
          column: 18
        },
        end: {
          line: 40,
          column: 53
        }
      },
      "40": {
        start: {
          line: 41,
          column: 15
        },
        end: {
          line: 41,
          column: 47
        }
      },
      "41": {
        start: {
          line: 42,
          column: 17
        },
        end: {
          line: 42,
          column: 51
        }
      },
      "42": {
        start: {
          line: 43,
          column: 22
        },
        end: {
          line: 43,
          column: 61
        }
      },
      "43": {
        start: {
          line: 44,
          column: 17
        },
        end: {
          line: 44,
          column: 51
        }
      },
      "44": {
        start: {
          line: 45,
          column: 24
        },
        end: {
          line: 45,
          column: 65
        }
      },
      "45": {
        start: {
          line: 46,
          column: 19
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "46": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 32
        }
      },
      "47": {
        start: {
          line: 48,
          column: 22
        },
        end: {
          line: 48,
          column: 53
        }
      },
      "48": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 42
        }
      },
      "49": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 59
        }
      },
      "50": {
        start: {
          line: 52,
          column: 22
        },
        end: {
          line: 52,
          column: 70
        }
      },
      "51": {
        start: {
          line: 53,
          column: 22
        },
        end: {
          line: 53,
          column: 70
        }
      },
      "52": {
        start: {
          line: 54,
          column: 26
        },
        end: {
          line: 54,
          column: 78
        }
      },
      "53": {
        start: {
          line: 55,
          column: 24
        },
        end: {
          line: 55,
          column: 74
        }
      },
      "54": {
        start: {
          line: 56,
          column: 23
        },
        end: {
          line: 56,
          column: 72
        }
      },
      "55": {
        start: {
          line: 57,
          column: 25
        },
        end: {
          line: 57,
          column: 76
        }
      },
      "56": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 74
        }
      },
      "57": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 59,
          column: 72
        }
      },
      "58": {
        start: {
          line: 60,
          column: 30
        },
        end: {
          line: 60,
          column: 86
        }
      },
      "59": {
        start: {
          line: 61,
          column: 23
        },
        end: {
          line: 61,
          column: 72
        }
      },
      "60": {
        start: {
          line: 62,
          column: 25
        },
        end: {
          line: 62,
          column: 76
        }
      },
      "61": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 63,
          column: 82
        }
      },
      "62": {
        start: {
          line: 65,
          column: 23
        },
        end: {
          line: 65,
          column: 59
        }
      },
      "63": {
        start: {
          line: 66,
          column: 23
        },
        end: {
          line: 66,
          column: 59
        }
      },
      "64": {
        start: {
          line: 67,
          column: 18
        },
        end: {
          line: 67,
          column: 45
        }
      },
      "65": {
        start: {
          line: 68,
          column: 23
        },
        end: {
          line: 68,
          column: 57
        }
      },
      "66": {
        start: {
          line: 69,
          column: 25
        },
        end: {
          line: 69,
          column: 61
        }
      },
      "67": {
        start: {
          line: 70,
          column: 23
        },
        end: {
          line: 70,
          column: 57
        }
      },
      "68": {
        start: {
          line: 71,
          column: 23
        },
        end: {
          line: 71,
          column: 57
        }
      },
      "69": {
        start: {
          line: 72,
          column: 32
        },
        end: {
          line: 72,
          column: 77
        }
      },
      "70": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 40
        }
      },
      "71": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 30
        }
      },
      "72": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 87,
          column: 5
        }
      },
      "73": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 103,
          column: 9
        }
      },
      "74": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 99,
          column: 13
        }
      },
      "75": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 60
        }
      },
      "76": {
        start: {
          line: 93,
          column: 16
        },
        end: {
          line: 93,
          column: 64
        }
      },
      "77": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 94,
          column: 60
        }
      },
      "78": {
        start: {
          line: 96,
          column: 57
        },
        end: {
          line: 96,
          column: 157
        }
      },
      "79": {
        start: {
          line: 96,
          column: 92
        },
        end: {
          line: 96,
          column: 156
        }
      },
      "80": {
        start: {
          line: 97,
          column: 16
        },
        end: {
          line: 97,
          column: 63
        }
      },
      "81": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 98,
          column: 78
        }
      },
      "82": {
        start: {
          line: 102,
          column: 12
        },
        end: {
          line: 102,
          column: 75
        }
      },
      "83": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "84": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 55
        }
      },
      "85": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 37
        }
      },
      "86": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 111,
          column: 46
        }
      },
      "87": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 42
        }
      },
      "88": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 55
        }
      },
      "89": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 77
        }
      },
      "90": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 44
        }
      },
      "91": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 119,
          column: 5
        }
      },
      "92": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 75
        }
      },
      "93": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "94": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 122,
          column: 62
        }
      },
      "95": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "96": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 128,
          column: 12
        }
      },
      "97": {
        start: {
          line: 127,
          column: 42
        },
        end: {
          line: 127,
          column: 78
        }
      },
      "98": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 152,
          column: 5
        }
      },
      "99": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 151,
          column: 11
        }
      },
      "100": {
        start: {
          line: 133,
          column: 30
        },
        end: {
          line: 133,
          column: 40
        }
      },
      "101": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 149,
          column: 15
        }
      },
      "102": {
        start: {
          line: 135,
          column: 33
        },
        end: {
          line: 135,
          column: 55
        }
      },
      "103": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 148,
          column: 17
        }
      },
      "104": {
        start: {
          line: 138,
          column: 20
        },
        end: {
          line: 147,
          column: 23
        }
      },
      "105": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 83
        }
      },
      "106": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 19
        }
      },
      "107": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 201,
          column: 7
        }
      },
      "108": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 200,
          column: 9
        }
      },
      "109": {
        start: {
          line: 156,
          column: 45
        },
        end: {
          line: 156,
          column: 137
        }
      },
      "110": {
        start: {
          line: 156,
          column: 80
        },
        end: {
          line: 156,
          column: 136
        }
      },
      "111": {
        start: {
          line: 157,
          column: 53
        },
        end: {
          line: 157,
          column: 153
        }
      },
      "112": {
        start: {
          line: 157,
          column: 88
        },
        end: {
          line: 157,
          column: 152
        }
      },
      "113": {
        start: {
          line: 158,
          column: 32
        },
        end: {
          line: 158,
          column: 71
        }
      },
      "114": {
        start: {
          line: 159,
          column: 38
        },
        end: {
          line: 159,
          column: 84
        }
      },
      "115": {
        start: {
          line: 160,
          column: 32
        },
        end: {
          line: 160,
          column: 53
        }
      },
      "116": {
        start: {
          line: 161,
          column: 34
        },
        end: {
          line: 165,
          column: 31
        }
      },
      "117": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 188,
          column: 15
        }
      },
      "118": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 199,
          column: 15
        }
      },
      "119": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 238,
          column: 7
        }
      },
      "120": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "121": {
        start: {
          line: 205,
          column: 45
        },
        end: {
          line: 205,
          column: 137
        }
      },
      "122": {
        start: {
          line: 205,
          column: 80
        },
        end: {
          line: 205,
          column: 136
        }
      },
      "123": {
        start: {
          line: 206,
          column: 53
        },
        end: {
          line: 206,
          column: 153
        }
      },
      "124": {
        start: {
          line: 206,
          column: 88
        },
        end: {
          line: 206,
          column: 152
        }
      },
      "125": {
        start: {
          line: 207,
          column: 38
        },
        end: {
          line: 207,
          column: 83
        }
      },
      "126": {
        start: {
          line: 208,
          column: 33
        },
        end: {
          line: 208,
          column: 66
        }
      },
      "127": {
        start: {
          line: 209,
          column: 33
        },
        end: {
          line: 209,
          column: 73
        }
      },
      "128": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 230,
          column: 15
        }
      },
      "129": {
        start: {
          line: 215,
          column: 51
        },
        end: {
          line: 221,
          column: 21
        }
      },
      "130": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 236,
          column: 15
        }
      },
      "131": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 150
        }
      },
      "132": {
        start: {
          line: 240,
          column: 119
        },
        end: {
          line: 240,
          column: 125
        }
      },
      "133": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 154
        }
      },
      "134": {
        start: {
          line: 241,
          column: 123
        },
        end: {
          line: 241,
          column: 129
        }
      },
      "135": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 163
        }
      },
      "136": {
        start: {
          line: 242,
          column: 128
        },
        end: {
          line: 242,
          column: 134
        }
      },
      "137": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 243,
          column: 157
        }
      },
      "138": {
        start: {
          line: 243,
          column: 124
        },
        end: {
          line: 243,
          column: 130
        }
      },
      "139": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 157
        }
      },
      "140": {
        start: {
          line: 244,
          column: 125
        },
        end: {
          line: 244,
          column: 131
        }
      },
      "141": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 245,
          column: 160
        }
      },
      "142": {
        start: {
          line: 245,
          column: 126
        },
        end: {
          line: 245,
          column: 132
        }
      },
      "143": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 158
        }
      },
      "144": {
        start: {
          line: 246,
          column: 125
        },
        end: {
          line: 246,
          column: 131
        }
      },
      "145": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 156
        }
      },
      "146": {
        start: {
          line: 247,
          column: 124
        },
        end: {
          line: 247,
          column: 130
        }
      },
      "147": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 248,
          column: 170
        }
      },
      "148": {
        start: {
          line: 248,
          column: 131
        },
        end: {
          line: 248,
          column: 137
        }
      },
      "149": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 249,
          column: 155
        }
      },
      "150": {
        start: {
          line: 249,
          column: 123
        },
        end: {
          line: 249,
          column: 129
        }
      },
      "151": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 160
        }
      },
      "152": {
        start: {
          line: 250,
          column: 126
        },
        end: {
          line: 250,
          column: 132
        }
      },
      "153": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 251,
          column: 165
        }
      },
      "154": {
        start: {
          line: 251,
          column: 128
        },
        end: {
          line: 251,
          column: 134
        }
      },
      "155": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "156": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 41
        }
      },
      "157": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 279,
          column: 7
        }
      },
      "158": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 278,
          column: 11
        }
      },
      "159": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 288,
          column: 7
        }
      },
      "160": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 287,
          column: 11
        }
      },
      "161": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 290,
          column: 41
        }
      },
      "162": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 292,
          column: 48
        }
      },
      "163": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 293,
          column: 15
        }
      },
      "164": {
        start: {
          line: 295,
          column: 0
        },
        end: {
          line: 295,
          column: 28
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 35,
            column: 56
          },
          end: {
            line: 35,
            column: 57
          }
        },
        loc: {
          start: {
            line: 35,
            column: 71
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 35
      },
      "10": {
        name: "createApp",
        decl: {
          start: {
            line: 76,
            column: 9
          },
          end: {
            line: 76,
            column: 18
          }
        },
        loc: {
          start: {
            line: 76,
            column: 21
          },
          end: {
            line: 294,
            column: 1
          }
        },
        line: 76
      },
      "11": {
        name: "initializeServices",
        decl: {
          start: {
            line: 89,
            column: 19
          },
          end: {
            line: 89,
            column: 37
          }
        },
        loc: {
          start: {
            line: 89,
            column: 40
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 89
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 96,
            column: 86
          },
          end: {
            line: 96,
            column: 87
          }
        },
        loc: {
          start: {
            line: 96,
            column: 92
          },
          end: {
            line: 96,
            column: 156
          }
        },
        line: 96
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 127,
            column: 29
          },
          end: {
            line: 127,
            column: 30
          }
        },
        loc: {
          start: {
            line: 127,
            column: 42
          },
          end: {
            line: 127,
            column: 78
          }
        },
        line: 127
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 132,
            column: 16
          },
          end: {
            line: 132,
            column: 17
          }
        },
        loc: {
          start: {
            line: 132,
            column: 36
          },
          end: {
            line: 151,
            column: 9
          }
        },
        line: 132
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 134,
            column: 29
          },
          end: {
            line: 134,
            column: 30
          }
        },
        loc: {
          start: {
            line: 134,
            column: 35
          },
          end: {
            line: 149,
            column: 13
          }
        },
        line: 134
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 30
          }
        },
        loc: {
          start: {
            line: 145,
            column: 38
          },
          end: {
            line: 147,
            column: 21
          }
        },
        line: 145
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 154,
            column: 23
          },
          end: {
            line: 154,
            column: 24
          }
        },
        loc: {
          start: {
            line: 154,
            column: 44
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 154
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 156,
            column: 74
          },
          end: {
            line: 156,
            column: 75
          }
        },
        loc: {
          start: {
            line: 156,
            column: 80
          },
          end: {
            line: 156,
            column: 136
          }
        },
        line: 156
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 157,
            column: 82
          },
          end: {
            line: 157,
            column: 83
          }
        },
        loc: {
          start: {
            line: 157,
            column: 88
          },
          end: {
            line: 157,
            column: 152
          }
        },
        line: 157
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 203,
            column: 32
          },
          end: {
            line: 203,
            column: 33
          }
        },
        loc: {
          start: {
            line: 203,
            column: 53
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 203
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 205,
            column: 74
          },
          end: {
            line: 205,
            column: 75
          }
        },
        loc: {
          start: {
            line: 205,
            column: 80
          },
          end: {
            line: 205,
            column: 136
          }
        },
        line: 205
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 206,
            column: 82
          },
          end: {
            line: 206,
            column: 83
          }
        },
        loc: {
          start: {
            line: 206,
            column: 88
          },
          end: {
            line: 206,
            column: 152
          }
        },
        line: 206
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 215,
            column: 45
          },
          end: {
            line: 215,
            column: 46
          }
        },
        loc: {
          start: {
            line: 215,
            column: 51
          },
          end: {
            line: 221,
            column: 21
          }
        },
        line: 215
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 240,
            column: 99
          },
          end: {
            line: 240,
            column: 100
          }
        },
        loc: {
          start: {
            line: 240,
            column: 119
          },
          end: {
            line: 240,
            column: 125
          }
        },
        line: 240
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 241,
            column: 103
          },
          end: {
            line: 241,
            column: 104
          }
        },
        loc: {
          start: {
            line: 241,
            column: 123
          },
          end: {
            line: 241,
            column: 129
          }
        },
        line: 241
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 242,
            column: 108
          },
          end: {
            line: 242,
            column: 109
          }
        },
        loc: {
          start: {
            line: 242,
            column: 128
          },
          end: {
            line: 242,
            column: 134
          }
        },
        line: 242
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 243,
            column: 104
          },
          end: {
            line: 243,
            column: 105
          }
        },
        loc: {
          start: {
            line: 243,
            column: 124
          },
          end: {
            line: 243,
            column: 130
          }
        },
        line: 243
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 244,
            column: 105
          },
          end: {
            line: 244,
            column: 106
          }
        },
        loc: {
          start: {
            line: 244,
            column: 125
          },
          end: {
            line: 244,
            column: 131
          }
        },
        line: 244
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 245,
            column: 106
          },
          end: {
            line: 245,
            column: 107
          }
        },
        loc: {
          start: {
            line: 245,
            column: 126
          },
          end: {
            line: 245,
            column: 132
          }
        },
        line: 245
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 246,
            column: 105
          },
          end: {
            line: 246,
            column: 106
          }
        },
        loc: {
          start: {
            line: 246,
            column: 125
          },
          end: {
            line: 246,
            column: 131
          }
        },
        line: 246
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 247,
            column: 104
          },
          end: {
            line: 247,
            column: 105
          }
        },
        loc: {
          start: {
            line: 247,
            column: 124
          },
          end: {
            line: 247,
            column: 130
          }
        },
        line: 247
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 248,
            column: 111
          },
          end: {
            line: 248,
            column: 112
          }
        },
        loc: {
          start: {
            line: 248,
            column: 131
          },
          end: {
            line: 248,
            column: 137
          }
        },
        line: 248
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 249,
            column: 103
          },
          end: {
            line: 249,
            column: 104
          }
        },
        loc: {
          start: {
            line: 249,
            column: 123
          },
          end: {
            line: 249,
            column: 129
          }
        },
        line: 249
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 250,
            column: 106
          },
          end: {
            line: 250,
            column: 107
          }
        },
        loc: {
          start: {
            line: 250,
            column: 126
          },
          end: {
            line: 250,
            column: 132
          }
        },
        line: 250
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 251,
            column: 108
          },
          end: {
            line: 251,
            column: 109
          }
        },
        loc: {
          start: {
            line: 251,
            column: 128
          },
          end: {
            line: 251,
            column: 134
          }
        },
        line: 251
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 257,
            column: 20
          },
          end: {
            line: 257,
            column: 21
          }
        },
        loc: {
          start: {
            line: 257,
            column: 35
          },
          end: {
            line: 279,
            column: 5
          }
        },
        line: 257
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 281,
            column: 17
          },
          end: {
            line: 281,
            column: 18
          }
        },
        loc: {
          start: {
            line: 281,
            column: 31
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 281
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "17": {
        loc: {
          start: {
            line: 35,
            column: 22
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 35,
            column: 27
          }
        }, {
          start: {
            line: 35,
            column: 31
          },
          end: {
            line: 35,
            column: 51
          }
        }, {
          start: {
            line: 35,
            column: 56
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 35
      },
      "18": {
        loc: {
          start: {
            line: 36,
            column: 11
          },
          end: {
            line: 36,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 37
          },
          end: {
            line: 36,
            column: 40
          }
        }, {
          start: {
            line: 36,
            column: 43
          },
          end: {
            line: 36,
            column: 61
          }
        }],
        line: 36
      },
      "19": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 15
          }
        }, {
          start: {
            line: 36,
            column: 19
          },
          end: {
            line: 36,
            column: 33
          }
        }],
        line: 36
      },
      "20": {
        loc: {
          start: {
            line: 82,
            column: 16
          },
          end: {
            line: 82,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 16
          },
          end: {
            line: 82,
            column: 48
          }
        }, {
          start: {
            line: 82,
            column: 52
          },
          end: {
            line: 82,
            column: 75
          }
        }],
        line: 82
      },
      "21": {
        loc: {
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 99,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 12
          },
          end: {
            line: 99,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "22": {
        loc: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 106
      },
      "23": {
        loc: {
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "24": {
        loc: {
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "25": {
        loc: {
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "26": {
        loc: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "27": {
        loc: {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 148,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 148,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "28": {
        loc: {
          start: {
            line: 161,
            column: 34
          },
          end: {
            line: 165,
            column: 31
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 162,
            column: 18
          },
          end: {
            line: 162,
            column: 28
          }
        }, {
          start: {
            line: 163,
            column: 18
          },
          end: {
            line: 165,
            column: 31
          }
        }],
        line: 161
      },
      "29": {
        loc: {
          start: {
            line: 161,
            column: 34
          },
          end: {
            line: 161,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 161,
            column: 34
          },
          end: {
            line: 161,
            column: 67
          }
        }, {
          start: {
            line: 161,
            column: 71
          },
          end: {
            line: 161,
            column: 110
          }
        }],
        line: 161
      },
      "30": {
        loc: {
          start: {
            line: 163,
            column: 18
          },
          end: {
            line: 165,
            column: 31
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 164,
            column: 22
          },
          end: {
            line: 164,
            column: 31
          }
        }, {
          start: {
            line: 165,
            column: 22
          },
          end: {
            line: 165,
            column: 31
          }
        }],
        line: 163
      },
      "31": {
        loc: {
          start: {
            line: 163,
            column: 18
          },
          end: {
            line: 163,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 18
          },
          end: {
            line: 163,
            column: 50
          }
        }, {
          start: {
            line: 163,
            column: 54
          },
          end: {
            line: 163,
            column: 92
          }
        }],
        line: 163
      },
      "32": {
        loc: {
          start: {
            line: 166,
            column: 23
          },
          end: {
            line: 166,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 54
          },
          end: {
            line: 166,
            column: 57
          }
        }, {
          start: {
            line: 166,
            column: 60
          },
          end: {
            line: 166,
            column: 63
          }
        }],
        line: 166
      },
      "33": {
        loc: {
          start: {
            line: 170,
            column: 25
          },
          end: {
            line: 170,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 25
          },
          end: {
            line: 170,
            column: 56
          }
        }, {
          start: {
            line: 170,
            column: 60
          },
          end: {
            line: 170,
            column: 67
          }
        }],
        line: 170
      },
      "34": {
        loc: {
          start: {
            line: 173,
            column: 30
          },
          end: {
            line: 173,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 173,
            column: 79
          },
          end: {
            line: 173,
            column: 90
          }
        }, {
          start: {
            line: 173,
            column: 93
          },
          end: {
            line: 173,
            column: 107
          }
        }],
        line: 173
      },
      "35": {
        loc: {
          start: {
            line: 174,
            column: 27
          },
          end: {
            line: 174,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 174,
            column: 71
          },
          end: {
            line: 174,
            column: 82
          }
        }, {
          start: {
            line: 174,
            column: 85
          },
          end: {
            line: 174,
            column: 99
          }
        }],
        line: 174
      },
      "36": {
        loc: {
          start: {
            line: 175,
            column: 30
          },
          end: {
            line: 175,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 78
          },
          end: {
            line: 175,
            column: 89
          }
        }, {
          start: {
            line: 175,
            column: 92
          },
          end: {
            line: 175,
            column: 106
          }
        }],
        line: 175
      },
      "37": {
        loc: {
          start: {
            line: 176,
            column: 28
          },
          end: {
            line: 176,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 176,
            column: 72
          },
          end: {
            line: 176,
            column: 83
          }
        }, {
          start: {
            line: 176,
            column: 86
          },
          end: {
            line: 176,
            column: 100
          }
        }],
        line: 176
      },
      "38": {
        loc: {
          start: {
            line: 196,
            column: 30
          },
          end: {
            line: 196,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 196,
            column: 79
          },
          end: {
            line: 196,
            column: 90
          }
        }, {
          start: {
            line: 196,
            column: 93
          },
          end: {
            line: 196,
            column: 107
          }
        }],
        line: 196
      },
      "39": {
        loc: {
          start: {
            line: 197,
            column: 27
          },
          end: {
            line: 197,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 197,
            column: 71
          },
          end: {
            line: 197,
            column: 82
          }
        }, {
          start: {
            line: 197,
            column: 85
          },
          end: {
            line: 197,
            column: 99
          }
        }],
        line: 197
      },
      "40": {
        loc: {
          start: {
            line: 240,
            column: 25
          },
          end: {
            line: 240,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 68
          },
          end: {
            line: 240,
            column: 96
          }
        }, {
          start: {
            line: 240,
            column: 99
          },
          end: {
            line: 240,
            column: 125
          }
        }],
        line: 240
      },
      "41": {
        loc: {
          start: {
            line: 241,
            column: 26
          },
          end: {
            line: 241,
            column: 129
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 241,
            column: 69
          },
          end: {
            line: 241,
            column: 100
          }
        }, {
          start: {
            line: 241,
            column: 103
          },
          end: {
            line: 241,
            column: 129
          }
        }],
        line: 241
      },
      "42": {
        loc: {
          start: {
            line: 242,
            column: 31
          },
          end: {
            line: 242,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 242,
            column: 74
          },
          end: {
            line: 242,
            column: 105
          }
        }, {
          start: {
            line: 242,
            column: 108
          },
          end: {
            line: 242,
            column: 134
          }
        }],
        line: 242
      },
      "43": {
        loc: {
          start: {
            line: 243,
            column: 27
          },
          end: {
            line: 243,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 70
          },
          end: {
            line: 243,
            column: 101
          }
        }, {
          start: {
            line: 243,
            column: 104
          },
          end: {
            line: 243,
            column: 130
          }
        }],
        line: 243
      },
      "44": {
        loc: {
          start: {
            line: 244,
            column: 28
          },
          end: {
            line: 244,
            column: 131
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 71
          },
          end: {
            line: 244,
            column: 102
          }
        }, {
          start: {
            line: 244,
            column: 105
          },
          end: {
            line: 244,
            column: 131
          }
        }],
        line: 244
      },
      "45": {
        loc: {
          start: {
            line: 245,
            column: 29
          },
          end: {
            line: 245,
            column: 132
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 245,
            column: 72
          },
          end: {
            line: 245,
            column: 103
          }
        }, {
          start: {
            line: 245,
            column: 106
          },
          end: {
            line: 245,
            column: 132
          }
        }],
        line: 245
      },
      "46": {
        loc: {
          start: {
            line: 246,
            column: 28
          },
          end: {
            line: 246,
            column: 131
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 71
          },
          end: {
            line: 246,
            column: 102
          }
        }, {
          start: {
            line: 246,
            column: 105
          },
          end: {
            line: 246,
            column: 131
          }
        }],
        line: 246
      },
      "47": {
        loc: {
          start: {
            line: 247,
            column: 27
          },
          end: {
            line: 247,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 247,
            column: 70
          },
          end: {
            line: 247,
            column: 101
          }
        }, {
          start: {
            line: 247,
            column: 104
          },
          end: {
            line: 247,
            column: 130
          }
        }],
        line: 247
      },
      "48": {
        loc: {
          start: {
            line: 248,
            column: 34
          },
          end: {
            line: 248,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 248,
            column: 77
          },
          end: {
            line: 248,
            column: 108
          }
        }, {
          start: {
            line: 248,
            column: 111
          },
          end: {
            line: 248,
            column: 137
          }
        }],
        line: 248
      },
      "49": {
        loc: {
          start: {
            line: 249,
            column: 26
          },
          end: {
            line: 249,
            column: 129
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 69
          },
          end: {
            line: 249,
            column: 100
          }
        }, {
          start: {
            line: 249,
            column: 103
          },
          end: {
            line: 249,
            column: 129
          }
        }],
        line: 249
      },
      "50": {
        loc: {
          start: {
            line: 250,
            column: 29
          },
          end: {
            line: 250,
            column: 132
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 72
          },
          end: {
            line: 250,
            column: 103
          }
        }, {
          start: {
            line: 250,
            column: 106
          },
          end: {
            line: 250,
            column: 132
          }
        }],
        line: 250
      },
      "51": {
        loc: {
          start: {
            line: 251,
            column: 31
          },
          end: {
            line: 251,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 251,
            column: 74
          },
          end: {
            line: 251,
            column: 105
          }
        }, {
          start: {
            line: 251,
            column: 108
          },
          end: {
            line: 251,
            column: 134
          }
        }],
        line: 251
      },
      "52": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\app.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,8BAwPC;AAhSD,sDAA2C;AAC3C,gDAAwB;AACxB,oDAA4B;AAC5B,8DAAsC;AACtC,oDAA4B;AAC5B,kEAAyC;AACzC,wDAAgC;AAChC,gCAA8B;AAE9B,sDAA8C;AAC9C,2CAAwC;AACxC,4DAAyD;AAEzD,gBAAgB;AAChB,uEAA8C;AAC9C,uEAA8C;AAC9C,+EAAsD;AACtD,2EAAkD;AAClD,yEAAgD;AAChD,6EAAoD;AACpD,2EAAkD;AAClD,yEAAgD;AAChD,uFAA8D;AAC9D,yEAAgD;AAChD,6EAAoD;AACpD,mFAA0D;AAE1D,iCAAiC;AACjC,4DAA4E;AAC5E,4DAA4D;AAC5D,8CAAgD;AAChD,0DAAuD;AACvD,8DAA2D;AAC3D,0DAAuD;AACvD,0DAAuE;AACvE,8EAA0E;AAE1E;;GAEG;AACH,SAAgB,SAAS;IACvB,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;IAEtB,wCAAwC;IACxC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAE1B,qBAAqB;IACrB,MAAM,WAAW,GAAG;QAClB,MAAM,EAAE,oBAAM,CAAC,WAAW,IAAI,uBAAuB;QACrD,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;QAC7D,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,CAAC;QACrE,cAAc,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;KAClD,CAAC;IAEF,sEAAsE;IACtE,KAAK,UAAU,kBAAkB;QAC/B,IAAI,CAAC;YACH,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;gBAC/B,MAAM,2BAAY,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,+BAAc,CAAC,OAAO,EAAE,CAAC;gBAC/B,MAAM,2BAAY,CAAC,OAAO,EAAE,CAAC;gBAE7B,iCAAiC;gBACjC,MAAM,EAAE,4BAA4B,EAAE,GAAG,wDAAa,yCAAyC,GAAC,CAAC;gBACjG,4BAA4B,CAAC,eAAe,EAAE,CAAC;gBAE/C,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,4DAA4D;IAC5D,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,IAAA,8BAAe,GAAE,CAAC,CAAC,CAAC,iCAAiC;IAC/D,CAAC;IAED,sBAAsB;IACtB,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,GAAE,CAAC,CAAC;IAClB,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC,WAAW,CAAC,CAAC,CAAC;IAC3B,GAAG,CAAC,GAAG,CAAC,IAAA,qBAAW,GAAE,CAAC,CAAC;IACvB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACzC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC/D,GAAG,CAAC,GAAG,CAAC,IAAA,uBAAY,GAAE,CAAC,CAAC;IAExB,oDAAoD;IACpD,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,+BAAc,CAAC,uBAAuB,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,mEAAmE;IACnE,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,4CAAoB,CAAC,CAAC;IAChC,CAAC;IAED,qBAAqB;IACrB,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,EAAC,UAAU,EAAE;YACzB,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE;SAC5D,CAAC,CAAC,CAAC;IACN,CAAC;IAED,mEAAmE;IACnE,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAExC,4DAA4D;gBAC5D,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBACjC,2BAAY,CAAC,QAAQ,CACnB,6BAAc,CAAC,WAAW,EAC1B,GAAG,EACH;wBACE,OAAO,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG;wBAC7B,QAAQ;wBACR,QAAQ,EAAE;4BACR,UAAU,EAAE,GAAG,CAAC,UAAU;4BAC1B,YAAY,EAAE,QAAQ;yBACvB;qBACF,CACF,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IACzB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;QACrC,IAAI,CAAC;YACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,wDAAa,iCAAiC,GAAC,CAAC;YACjF,MAAM,EAAE,4BAA4B,EAAE,GAAG,wDAAa,yCAAyC,GAAC,CAAC;YAEjG,MAAM,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;YAC5D,MAAM,iBAAiB,GAAG,4BAA4B,CAAC,eAAe,EAAE,CAAC;YACzE,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAE1C,MAAM,aAAa,GACjB,WAAW,CAAC,MAAM,KAAK,UAAU,IAAI,iBAAiB,CAAC,MAAM,KAAK,UAAU;gBAC1E,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,WAAW,CAAC,MAAM,KAAK,SAAS,IAAI,iBAAiB,CAAC,MAAM,KAAK,SAAS;oBAC5E,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,SAAS,CAAC;YAEhB,GAAG,CAAC,MAAM,CAAC,aAAa,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACxD,MAAM,EAAE,aAAa,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,oBAAM,CAAC,QAAQ;gBAC5B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;gBACnD,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;gBACxB,QAAQ,EAAE;oBACR,QAAQ,EAAE,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;oBAC7E,KAAK,EAAE,2BAAY,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;oBAChE,QAAQ,EAAE,+BAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;oBACrE,MAAM,EAAE,2BAAY,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;oBACjE,KAAK,EAAE,YAAY;iBACpB;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,WAAW;oBACnB,WAAW,EAAE,iBAAiB;iBAC/B;gBACD,MAAM,EAAE;oBACN,QAAQ,EAAE,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACjE,SAAS,EAAE,GAAG,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACnE,GAAG,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;iBACxD;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,MAAM,EAAE,OAAO;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,KAAK,EAAE,qBAAqB;gBAC5B,QAAQ,EAAE;oBACR,QAAQ,EAAE,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;oBAC7E,KAAK,EAAE,2BAAY,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;iBACjE;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,+CAA+C;IAC/C,GAAG,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;QAC9C,IAAI,CAAC;YACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,wDAAa,iCAAiC,GAAC,CAAC;YACjF,MAAM,EAAE,4BAA4B,EAAE,GAAG,wDAAa,yCAAyC,GAAC,CAAC;YAEjG,MAAM,iBAAiB,GAAG,4BAA4B,CAAC,cAAc,EAAE,CAAC;YACxE,MAAM,YAAY,GAAG,oBAAoB,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,YAAY,GAAG,oBAAoB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAE9D,GAAG,CAAC,IAAI,CAAC;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,iBAAiB;gBAC9B,MAAM,EAAE;oBACN,OAAO,EAAE,YAAY;oBACrB,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC7B,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO;wBACxB,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,SAAS,EAAE,CAAC,CAAC,SAAS;wBACtB,OAAO,EAAE,CAAC,CAAC,OAAO;qBACnB,CAAC,CAAC;iBACJ;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;oBACxB,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;oBAC7B,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE;oBACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,WAAW,EAAE,OAAO,CAAC,OAAO;iBAC7B;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,2CAA2C;gBAClD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,yCAAyC;IACzC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,4BAAa,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,qBAAU,CAAC,CAAC;IAC1G,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,qBAAU,CAAC,CAAC;IAC9G,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,yBAAc,CAAC,CAAC;IACvH,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,uBAAY,CAAC,CAAC;IACjH,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,sBAAW,CAAC,CAAC;IACjH,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,wBAAa,CAAC,CAAC;IACpH,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,uBAAY,CAAC,CAAC;IAClH,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,sBAAW,CAAC,CAAC;IAChH,GAAG,CAAC,GAAG,CAAC,oBAAoB,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,6BAAkB,CAAC,CAAC;IAC9H,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,sBAAW,CAAC,CAAC;IAC/G,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,wBAAa,CAAC,CAAC;IACpH,GAAG,CAAC,GAAG,CAAC,iBAAiB,EAAE,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,+BAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,2BAAgB,CAAC,CAAC;IAEzH,6DAA6D;IAC7D,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,IAAA,sBAAY,EAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,6BAA6B;IAC7B,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;QAC5B,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,oBAAM,CAAC,QAAQ;YAC5B,aAAa,EAAE,WAAW;YAC1B,SAAS,EAAE;gBACT,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,YAAY;gBACnB,UAAU,EAAE,iBAAiB;gBAC7B,MAAM,EAAE,aAAa;gBACrB,OAAO,EAAE,cAAc;gBACvB,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,cAAc;gBACvB,MAAM,EAAE,aAAa;gBACrB,aAAa,EAAE,oBAAoB;gBACnC,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,eAAe;gBACzB,UAAU,EAAE,iBAAiB;gBAC7B,IAAI,EAAE,WAAW;aAClB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,cAAc;IACd,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACxB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,iBAAiB;YACxB,IAAI,EAAE,GAAG,CAAC,WAAW;YACrB,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,GAAG,CAAC,GAAG,CAAC,2BAAY,CAAC,CAAC;IAEtB,0DAA0D;IACzD,GAAW,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAErD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,kBAAe,SAAS,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\app.ts"],
      sourcesContent: ["import express, { Express } from 'express';\r\nimport cors from 'cors';\r\nimport helmet from 'helmet';\r\nimport compression from 'compression';\r\nimport morgan from 'morgan';\r\nimport cookieParser from 'cookie-parser';\r\nimport mongoose from 'mongoose';\r\nimport 'express-async-errors';\r\n\r\nimport { config } from './config/environment';\r\nimport { logger } from './utils/logger';\r\nimport { errorHandler } from './middleware/errorHandler';\r\n\r\n// Import routes\r\nimport authRoutes from './routes/auth.routes';\r\nimport userRoutes from './routes/user.routes';\r\nimport propertyRoutes from './routes/property.routes';\r\nimport searchRoutes from './routes/search.routes';\r\nimport matchRoutes from './routes/match.routes';\r\nimport messageRoutes from './routes/message.routes';\r\nimport uploadRoutes from './routes/upload.routes';\r\nimport emailRoutes from './routes/email.routes';\r\nimport notificationRoutes from './routes/notification.routes';\r\nimport adminRoutes from './routes/admin.routes';\r\nimport sessionRoutes from './routes/session.routes';\r\nimport monitoringRoutes from './routes/monitoring.routes';\r\n\r\n// Security & Performance imports\r\nimport { generalRateLimit, authRateLimit } from './middleware/rateLimiting';\r\nimport { sanitizeRequest } from './middleware/sanitization';\r\nimport { setupSwagger } from './config/swagger';\r\nimport { cacheService } from './services/cacheService';\r\nimport { sessionService } from './services/sessionService';\r\nimport { tokenService } from './services/tokenService';\r\nimport { auditService, AuditEventType } from './services/auditService';\r\nimport { monitoringMiddleware } from './middleware/performanceMiddleware';\r\n\r\n/**\r\n * Create Express application\r\n */\r\nexport function createApp(): Express {\r\n  const app = express();\r\n\r\n  // Trust proxy for accurate IP addresses\r\n  app.set('trust proxy', 1);\r\n\r\n  // CORS configuration\r\n  const corsOptions = {\r\n    origin: config.CORS_ORIGIN || 'http://localhost:3000',\r\n    credentials: true,\r\n    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],\r\n    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],\r\n    exposedHeaders: ['X-Total-Count', 'X-Page-Count']\r\n  };\r\n\r\n  // Initialize services function (will be called during server startup)\r\n  async function initializeServices() {\r\n    try {\r\n      if (config.NODE_ENV !== 'test') {\r\n        await cacheService.connect();\r\n        await sessionService.connect();\r\n        await tokenService.connect();\r\n\r\n        // Initialize monitoring services\r\n        const { performanceMonitoringService } = await import('./services/performanceMonitoringService');\r\n        performanceMonitoringService.startMonitoring();\r\n\r\n        logger.info('All services initialized successfully');\r\n      }\r\n    } catch (error) {\r\n      logger.error('Failed to initialize services:', error);\r\n    }\r\n  }\r\n\r\n  // Security & Performance Middleware (applied conditionally)\r\n  if (config.NODE_ENV !== 'test') {\r\n    app.use(sanitizeRequest()); // Apply input sanitization first\r\n  }\r\n\r\n  // Standard Middleware\r\n  app.use(helmet());\r\n  app.use(cors(corsOptions));\r\n  app.use(compression());\r\n  app.use(express.json({ limit: '10mb' }));\r\n  app.use(express.urlencoded({ extended: true, limit: '10mb' }));\r\n  app.use(cookieParser());\r\n\r\n  // Session middleware (only in non-test environment)\r\n  if (config.NODE_ENV !== 'test') {\r\n    app.use(sessionService.createSessionMiddleware());\r\n  }\r\n\r\n  // Performance monitoring middleware (only in non-test environment)\r\n  if (config.NODE_ENV !== 'test') {\r\n    app.use(monitoringMiddleware);\r\n  }\r\n\r\n  // Logging middleware\r\n  if (config.NODE_ENV !== 'test') {\r\n    app.use(morgan('combined', {\r\n      stream: { write: (message) => logger.info(message.trim()) }\r\n    }));\r\n  }\r\n\r\n  // Audit middleware for all requests (only in non-test environment)\r\n  if (config.NODE_ENV !== 'test') {\r\n    app.use((req, res, next) => {\r\n      const startTime = Date.now();\r\n      \r\n      res.on('finish', () => {\r\n        const duration = Date.now() - startTime;\r\n        \r\n        // Log audit event for all requests (only for API endpoints)\r\n        if (req.path.startsWith('/api/')) {\r\n          auditService.logEvent(\r\n            AuditEventType.DATA_VIEWED,\r\n            req,\r\n            {\r\n              success: res.statusCode < 400,\r\n              duration,\r\n              metadata: {\r\n                statusCode: res.statusCode,\r\n                responseTime: duration\r\n              }\r\n            }\r\n          ).catch(error => {\r\n            logger.error('Failed to log audit event:', error);\r\n          });\r\n        }\r\n      });\r\n      \r\n      next();\r\n    });\r\n  }\r\n\r\n  // Health check endpoints\r\n  app.get('/health', async (_req, res) => {\r\n    try {\r\n      const { errorTrackingService } = await import('./services/errorTrackingService');\r\n      const { performanceMonitoringService } = await import('./services/performanceMonitoringService');\r\n\r\n      const errorHealth = errorTrackingService.getHealthSummary();\r\n      const performanceHealth = performanceMonitoringService.getHealthStatus();\r\n      const memoryUsage = process.memoryUsage();\r\n\r\n      const overallStatus =\r\n        errorHealth.status === 'critical' || performanceHealth.status === 'critical'\r\n          ? 'critical'\r\n          : errorHealth.status === 'warning' || performanceHealth.status === 'warning'\r\n          ? 'warning'\r\n          : 'healthy';\r\n\r\n      res.status(overallStatus === 'critical' ? 503 : 200).json({\r\n        status: overallStatus.toUpperCase(),\r\n        timestamp: new Date().toISOString(),\r\n        environment: config.NODE_ENV,\r\n        version: process.env.npm_package_version || '1.0.0',\r\n        uptime: process.uptime(),\r\n        services: {\r\n          database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',\r\n          redis: cacheService.isConnected() ? 'connected' : 'disconnected',\r\n          sessions: sessionService.isConnected() ? 'connected' : 'disconnected',\r\n          tokens: tokenService.isConnected() ? 'connected' : 'disconnected',\r\n          email: 'configured'\r\n        },\r\n        health: {\r\n          errors: errorHealth,\r\n          performance: performanceHealth\r\n        },\r\n        memory: {\r\n          heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n          heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,\r\n          rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`\r\n        }\r\n      });\r\n    } catch (error) {\r\n      res.status(503).json({\r\n        status: 'ERROR',\r\n        timestamp: new Date().toISOString(),\r\n        error: 'Health check failed',\r\n        services: {\r\n          database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',\r\n          redis: cacheService.isConnected() ? 'connected' : 'disconnected'\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  // Detailed health check for monitoring systems\r\n  app.get('/health/detailed', async (_req, res) => {\r\n    try {\r\n      const { errorTrackingService } = await import('./services/errorTrackingService');\r\n      const { performanceMonitoringService } = await import('./services/performanceMonitoringService');\r\n\r\n      const performanceReport = performanceMonitoringService.generateReport();\r\n      const errorMetrics = errorTrackingService.getMetrics();\r\n      const recentErrors = errorTrackingService.getRecentErrors(10);\r\n\r\n      res.json({\r\n        timestamp: new Date().toISOString(),\r\n        performance: performanceReport,\r\n        errors: {\r\n          metrics: errorMetrics,\r\n          recent: recentErrors.map(e => ({\r\n            message: e.error.message,\r\n            severity: e.severity,\r\n            category: e.category,\r\n            timestamp: e.timestamp,\r\n            context: e.context\r\n          }))\r\n        },\r\n        system: {\r\n          uptime: process.uptime(),\r\n          memory: process.memoryUsage(),\r\n          cpu: process.cpuUsage(),\r\n          platform: process.platform,\r\n          nodeVersion: process.version\r\n        }\r\n      });\r\n    } catch (error) {\r\n      res.status(500).json({\r\n        error: 'Failed to generate detailed health report',\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }\r\n  });\r\n\r\n  // API routes with specific rate limiting\r\n  app.use('/api/auth', config.NODE_ENV !== 'test' ? authRateLimit : (req, res, next) => next(), authRoutes);\r\n  app.use('/api/users', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), userRoutes);\r\n  app.use('/api/properties', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), propertyRoutes);\r\n  app.use('/api/search', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), searchRoutes);\r\n  app.use('/api/matches', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), matchRoutes);\r\n  app.use('/api/messages', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), messageRoutes);\r\n  app.use('/api/uploads', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), uploadRoutes);\r\n  app.use('/api/emails', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), emailRoutes);\r\n  app.use('/api/notifications', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), notificationRoutes);\r\n  app.use('/api/admin', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), adminRoutes);\r\n  app.use('/api/sessions', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), sessionRoutes);\r\n  app.use('/api/monitoring', config.NODE_ENV !== 'test' ? generalRateLimit : (req, res, next) => next(), monitoringRoutes);\r\n\r\n  // Setup Swagger documentation (only in non-test environment)\r\n  if (config.NODE_ENV !== 'test') {\r\n    setupSwagger(app);\r\n  }\r\n\r\n  // API documentation endpoint\r\n  app.get('/api', (_req, res) => {\r\n    res.json({\r\n      message: 'LajoSpaces API',\r\n      version: '1.0.0',\r\n      environment: config.NODE_ENV,\r\n      documentation: '/api/docs',\r\n      endpoints: {\r\n        auth: '/api/auth',\r\n        users: '/api/users',\r\n        properties: '/api/properties',\r\n        search: '/api/search',\r\n        matches: '/api/matches',\r\n        messages: '/api/messages',\r\n        uploads: '/api/uploads',\r\n        emails: '/api/emails',\r\n        notifications: '/api/notifications',\r\n        admin: '/api/admin',\r\n        sessions: '/api/sessions',\r\n        monitoring: '/api/monitoring',\r\n        docs: '/api/docs'\r\n      }\r\n    });\r\n  });\r\n\r\n  // 404 handler\r\n  app.use('*', (req, res) => {\r\n    res.status(404).json({\r\n      success: false,\r\n      error: 'Route not found',\r\n      path: req.originalUrl,\r\n      method: req.method\r\n    });\r\n  });\r\n\r\n  // Global error handler\r\n  app.use(errorHandler);\r\n\r\n  // Store initialization function on app for server startup\r\n  (app as any).initializeServices = initializeServices;\r\n\r\n  return app;\r\n}\r\n\r\nexport default createApp;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6c8fb581bb7c6633bb44aa0439b783df401ab331"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2o9aax85y3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2o9aax85y3();
var __createBinding =
/* istanbul ignore next */
(cov_2o9aax85y3().s[0]++,
/* istanbul ignore next */
(cov_2o9aax85y3().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2o9aax85y3().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_2o9aax85y3().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_2o9aax85y3().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[0]++;
  cov_2o9aax85y3().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[2][0]++;
    cov_2o9aax85y3().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_2o9aax85y3().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_2o9aax85y3().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[5][1]++,
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[3][0]++;
    cov_2o9aax85y3().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[1]++;
        cov_2o9aax85y3().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[3][1]++;
  }
  cov_2o9aax85y3().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_2o9aax85y3().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[2]++;
  cov_2o9aax85y3().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[7][0]++;
    cov_2o9aax85y3().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[7][1]++;
  }
  cov_2o9aax85y3().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_2o9aax85y3().s[11]++,
/* istanbul ignore next */
(cov_2o9aax85y3().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2o9aax85y3().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_2o9aax85y3().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_2o9aax85y3().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[3]++;
  cov_2o9aax85y3().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_2o9aax85y3().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[4]++;
  cov_2o9aax85y3().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_2o9aax85y3().s[14]++,
/* istanbul ignore next */
(cov_2o9aax85y3().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_2o9aax85y3().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_2o9aax85y3().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[5]++;
  cov_2o9aax85y3().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[6]++;
    cov_2o9aax85y3().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_2o9aax85y3().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[17]++, []);
      /* istanbul ignore next */
      cov_2o9aax85y3().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_2o9aax85y3().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_2o9aax85y3().b[12][0]++;
          cov_2o9aax85y3().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_2o9aax85y3().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2o9aax85y3().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_2o9aax85y3().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_2o9aax85y3().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[8]++;
    cov_2o9aax85y3().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_2o9aax85y3().b[13][0]++;
      cov_2o9aax85y3().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_2o9aax85y3().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_2o9aax85y3().s[26]++, {});
    /* istanbul ignore next */
    cov_2o9aax85y3().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_2o9aax85y3().b[15][0]++;
      cov_2o9aax85y3().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_2o9aax85y3().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_2o9aax85y3().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_2o9aax85y3().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_2o9aax85y3().b[16][0]++;
          cov_2o9aax85y3().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_2o9aax85y3().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2o9aax85y3().b[15][1]++;
    }
    cov_2o9aax85y3().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_2o9aax85y3().s[34]++;
    return result;
  };
}()));
var __importDefault =
/* istanbul ignore next */
(cov_2o9aax85y3().s[35]++,
/* istanbul ignore next */
(cov_2o9aax85y3().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_2o9aax85y3().b[17][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2o9aax85y3().b[17][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[9]++;
  cov_2o9aax85y3().s[36]++;
  return /* istanbul ignore next */(cov_2o9aax85y3().b[19][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[19][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[18][0]++, mod) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[18][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2o9aax85y3().s[37]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2o9aax85y3().s[38]++;
exports.createApp = createApp;
const express_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[39]++, __importDefault(require("express")));
const cors_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[40]++, __importDefault(require("cors")));
const helmet_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[41]++, __importDefault(require("helmet")));
const compression_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[42]++, __importDefault(require("compression")));
const morgan_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[43]++, __importDefault(require("morgan")));
const cookie_parser_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[44]++, __importDefault(require("cookie-parser")));
const mongoose_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[45]++, __importDefault(require("mongoose")));
/* istanbul ignore next */
cov_2o9aax85y3().s[46]++;
require("express-async-errors");
const environment_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[47]++, require("./config/environment"));
const logger_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[48]++, require("./utils/logger"));
const errorHandler_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[49]++, require("./middleware/errorHandler"));
// Import routes
const auth_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[50]++, __importDefault(require("./routes/auth.routes")));
const user_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[51]++, __importDefault(require("./routes/user.routes")));
const property_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[52]++, __importDefault(require("./routes/property.routes")));
const search_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[53]++, __importDefault(require("./routes/search.routes")));
const match_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[54]++, __importDefault(require("./routes/match.routes")));
const message_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[55]++, __importDefault(require("./routes/message.routes")));
const upload_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[56]++, __importDefault(require("./routes/upload.routes")));
const email_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[57]++, __importDefault(require("./routes/email.routes")));
const notification_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[58]++, __importDefault(require("./routes/notification.routes")));
const admin_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[59]++, __importDefault(require("./routes/admin.routes")));
const session_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[60]++, __importDefault(require("./routes/session.routes")));
const monitoring_routes_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[61]++, __importDefault(require("./routes/monitoring.routes")));
// Security & Performance imports
const rateLimiting_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[62]++, require("./middleware/rateLimiting"));
const sanitization_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[63]++, require("./middleware/sanitization"));
const swagger_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[64]++, require("./config/swagger"));
const cacheService_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[65]++, require("./services/cacheService"));
const sessionService_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[66]++, require("./services/sessionService"));
const tokenService_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[67]++, require("./services/tokenService"));
const auditService_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[68]++, require("./services/auditService"));
const performanceMiddleware_1 =
/* istanbul ignore next */
(cov_2o9aax85y3().s[69]++, require("./middleware/performanceMiddleware"));
/**
 * Create Express application
 */
function createApp() {
  /* istanbul ignore next */
  cov_2o9aax85y3().f[10]++;
  const app =
  /* istanbul ignore next */
  (cov_2o9aax85y3().s[70]++, (0, express_1.default)());
  // Trust proxy for accurate IP addresses
  /* istanbul ignore next */
  cov_2o9aax85y3().s[71]++;
  app.set('trust proxy', 1);
  // CORS configuration
  const corsOptions =
  /* istanbul ignore next */
  (cov_2o9aax85y3().s[72]++, {
    origin:
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[20][0]++, environment_1.config.CORS_ORIGIN) ||
    /* istanbul ignore next */
    (cov_2o9aax85y3().b[20][1]++, 'http://localhost:3000'),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count']
  });
  // Initialize services function (will be called during server startup)
  async function initializeServices() {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[11]++;
    cov_2o9aax85y3().s[73]++;
    try {
      /* istanbul ignore next */
      cov_2o9aax85y3().s[74]++;
      if (environment_1.config.NODE_ENV !== 'test') {
        /* istanbul ignore next */
        cov_2o9aax85y3().b[21][0]++;
        cov_2o9aax85y3().s[75]++;
        await cacheService_1.cacheService.connect();
        /* istanbul ignore next */
        cov_2o9aax85y3().s[76]++;
        await sessionService_1.sessionService.connect();
        /* istanbul ignore next */
        cov_2o9aax85y3().s[77]++;
        await tokenService_1.tokenService.connect();
        // Initialize monitoring services
        const {
          performanceMonitoringService
        } =
        /* istanbul ignore next */
        (cov_2o9aax85y3().s[78]++, await Promise.resolve().then(() => {
          /* istanbul ignore next */
          cov_2o9aax85y3().f[12]++;
          cov_2o9aax85y3().s[79]++;
          return __importStar(require('./services/performanceMonitoringService'));
        }));
        /* istanbul ignore next */
        cov_2o9aax85y3().s[80]++;
        performanceMonitoringService.startMonitoring();
        /* istanbul ignore next */
        cov_2o9aax85y3().s[81]++;
        logger_1.logger.info('All services initialized successfully');
      } else
      /* istanbul ignore next */
      {
        cov_2o9aax85y3().b[21][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_2o9aax85y3().s[82]++;
      logger_1.logger.error('Failed to initialize services:', error);
    }
  }
  // Security & Performance Middleware (applied conditionally)
  /* istanbul ignore next */
  cov_2o9aax85y3().s[83]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[22][0]++;
    cov_2o9aax85y3().s[84]++;
    app.use((0, sanitization_1.sanitizeRequest)()); // Apply input sanitization first
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[22][1]++;
  }
  // Standard Middleware
  cov_2o9aax85y3().s[85]++;
  app.use((0, helmet_1.default)());
  /* istanbul ignore next */
  cov_2o9aax85y3().s[86]++;
  app.use((0, cors_1.default)(corsOptions));
  /* istanbul ignore next */
  cov_2o9aax85y3().s[87]++;
  app.use((0, compression_1.default)());
  /* istanbul ignore next */
  cov_2o9aax85y3().s[88]++;
  app.use(express_1.default.json({
    limit: '10mb'
  }));
  /* istanbul ignore next */
  cov_2o9aax85y3().s[89]++;
  app.use(express_1.default.urlencoded({
    extended: true,
    limit: '10mb'
  }));
  /* istanbul ignore next */
  cov_2o9aax85y3().s[90]++;
  app.use((0, cookie_parser_1.default)());
  // Session middleware (only in non-test environment)
  /* istanbul ignore next */
  cov_2o9aax85y3().s[91]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[23][0]++;
    cov_2o9aax85y3().s[92]++;
    app.use(sessionService_1.sessionService.createSessionMiddleware());
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[23][1]++;
  }
  // Performance monitoring middleware (only in non-test environment)
  cov_2o9aax85y3().s[93]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[24][0]++;
    cov_2o9aax85y3().s[94]++;
    app.use(performanceMiddleware_1.monitoringMiddleware);
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[24][1]++;
  }
  // Logging middleware
  cov_2o9aax85y3().s[95]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[25][0]++;
    cov_2o9aax85y3().s[96]++;
    app.use((0, morgan_1.default)('combined', {
      stream: {
        write: message => {
          /* istanbul ignore next */
          cov_2o9aax85y3().f[13]++;
          cov_2o9aax85y3().s[97]++;
          return logger_1.logger.info(message.trim());
        }
      }
    }));
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[25][1]++;
  }
  // Audit middleware for all requests (only in non-test environment)
  cov_2o9aax85y3().s[98]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[26][0]++;
    cov_2o9aax85y3().s[99]++;
    app.use((req, res, next) => {
      /* istanbul ignore next */
      cov_2o9aax85y3().f[14]++;
      const startTime =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[100]++, Date.now());
      /* istanbul ignore next */
      cov_2o9aax85y3().s[101]++;
      res.on('finish', () => {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[15]++;
        const duration =
        /* istanbul ignore next */
        (cov_2o9aax85y3().s[102]++, Date.now() - startTime);
        // Log audit event for all requests (only for API endpoints)
        /* istanbul ignore next */
        cov_2o9aax85y3().s[103]++;
        if (req.path.startsWith('/api/')) {
          /* istanbul ignore next */
          cov_2o9aax85y3().b[27][0]++;
          cov_2o9aax85y3().s[104]++;
          auditService_1.auditService.logEvent(auditService_1.AuditEventType.DATA_VIEWED, req, {
            success: res.statusCode < 400,
            duration,
            metadata: {
              statusCode: res.statusCode,
              responseTime: duration
            }
          }).catch(error => {
            /* istanbul ignore next */
            cov_2o9aax85y3().f[16]++;
            cov_2o9aax85y3().s[105]++;
            logger_1.logger.error('Failed to log audit event:', error);
          });
        } else
        /* istanbul ignore next */
        {
          cov_2o9aax85y3().b[27][1]++;
        }
      });
      /* istanbul ignore next */
      cov_2o9aax85y3().s[106]++;
      next();
    });
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[26][1]++;
  }
  // Health check endpoints
  cov_2o9aax85y3().s[107]++;
  app.get('/health', async (_req, res) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[17]++;
    cov_2o9aax85y3().s[108]++;
    try {
      const {
        errorTrackingService
      } =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[109]++, await Promise.resolve().then(() => {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[18]++;
        cov_2o9aax85y3().s[110]++;
        return __importStar(require('./services/errorTrackingService'));
      }));
      const {
        performanceMonitoringService
      } =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[111]++, await Promise.resolve().then(() => {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[19]++;
        cov_2o9aax85y3().s[112]++;
        return __importStar(require('./services/performanceMonitoringService'));
      }));
      const errorHealth =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[113]++, errorTrackingService.getHealthSummary());
      const performanceHealth =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[114]++, performanceMonitoringService.getHealthStatus());
      const memoryUsage =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[115]++, process.memoryUsage());
      const overallStatus =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[116]++,
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[29][0]++, errorHealth.status === 'critical') ||
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[29][1]++, performanceHealth.status === 'critical') ?
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[28][0]++, 'critical') :
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[28][1]++,
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[31][0]++, errorHealth.status === 'warning') ||
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[31][1]++, performanceHealth.status === 'warning') ?
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[30][0]++, 'warning') :
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[30][1]++, 'healthy')));
      /* istanbul ignore next */
      cov_2o9aax85y3().s[117]++;
      res.status(overallStatus === 'critical' ?
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[32][0]++, 503) :
      /* istanbul ignore next */
      (cov_2o9aax85y3().b[32][1]++, 200)).json({
        status: overallStatus.toUpperCase(),
        timestamp: new Date().toISOString(),
        environment: environment_1.config.NODE_ENV,
        version:
        /* istanbul ignore next */
        (cov_2o9aax85y3().b[33][0]++, process.env.npm_package_version) ||
        /* istanbul ignore next */
        (cov_2o9aax85y3().b[33][1]++, '1.0.0'),
        uptime: process.uptime(),
        services: {
          database: mongoose_1.default.connection.readyState === 1 ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[34][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[34][1]++, 'disconnected'),
          redis: cacheService_1.cacheService.isConnected() ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[35][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[35][1]++, 'disconnected'),
          sessions: sessionService_1.sessionService.isConnected() ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[36][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[36][1]++, 'disconnected'),
          tokens: tokenService_1.tokenService.isConnected() ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[37][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[37][1]++, 'disconnected'),
          email: 'configured'
        },
        health: {
          errors: errorHealth,
          performance: performanceHealth
        },
        memory: {
          heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
          heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`
        }
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_2o9aax85y3().s[118]++;
      res.status(503).json({
        status: 'ERROR',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        services: {
          database: mongoose_1.default.connection.readyState === 1 ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[38][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[38][1]++, 'disconnected'),
          redis: cacheService_1.cacheService.isConnected() ?
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[39][0]++, 'connected') :
          /* istanbul ignore next */
          (cov_2o9aax85y3().b[39][1]++, 'disconnected')
        }
      });
    }
  });
  // Detailed health check for monitoring systems
  /* istanbul ignore next */
  cov_2o9aax85y3().s[119]++;
  app.get('/health/detailed', async (_req, res) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[20]++;
    cov_2o9aax85y3().s[120]++;
    try {
      const {
        errorTrackingService
      } =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[121]++, await Promise.resolve().then(() => {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[21]++;
        cov_2o9aax85y3().s[122]++;
        return __importStar(require('./services/errorTrackingService'));
      }));
      const {
        performanceMonitoringService
      } =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[123]++, await Promise.resolve().then(() => {
        /* istanbul ignore next */
        cov_2o9aax85y3().f[22]++;
        cov_2o9aax85y3().s[124]++;
        return __importStar(require('./services/performanceMonitoringService'));
      }));
      const performanceReport =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[125]++, performanceMonitoringService.generateReport());
      const errorMetrics =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[126]++, errorTrackingService.getMetrics());
      const recentErrors =
      /* istanbul ignore next */
      (cov_2o9aax85y3().s[127]++, errorTrackingService.getRecentErrors(10));
      /* istanbul ignore next */
      cov_2o9aax85y3().s[128]++;
      res.json({
        timestamp: new Date().toISOString(),
        performance: performanceReport,
        errors: {
          metrics: errorMetrics,
          recent: recentErrors.map(e => {
            /* istanbul ignore next */
            cov_2o9aax85y3().f[23]++;
            cov_2o9aax85y3().s[129]++;
            return {
              message: e.error.message,
              severity: e.severity,
              category: e.category,
              timestamp: e.timestamp,
              context: e.context
            };
          })
        },
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          platform: process.platform,
          nodeVersion: process.version
        }
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_2o9aax85y3().s[130]++;
      res.status(500).json({
        error: 'Failed to generate detailed health report',
        timestamp: new Date().toISOString()
      });
    }
  });
  // API routes with specific rate limiting
  /* istanbul ignore next */
  cov_2o9aax85y3().s[131]++;
  app.use('/api/auth', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[40][0]++, rateLimiting_1.authRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[40][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[24]++;
    cov_2o9aax85y3().s[132]++;
    return next();
  }), auth_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[133]++;
  app.use('/api/users', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[41][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[41][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[25]++;
    cov_2o9aax85y3().s[134]++;
    return next();
  }), user_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[135]++;
  app.use('/api/properties', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[42][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[42][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[26]++;
    cov_2o9aax85y3().s[136]++;
    return next();
  }), property_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[137]++;
  app.use('/api/search', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[43][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[43][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[27]++;
    cov_2o9aax85y3().s[138]++;
    return next();
  }), search_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[139]++;
  app.use('/api/matches', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[44][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[44][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[28]++;
    cov_2o9aax85y3().s[140]++;
    return next();
  }), match_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[141]++;
  app.use('/api/messages', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[45][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[45][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[29]++;
    cov_2o9aax85y3().s[142]++;
    return next();
  }), message_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[143]++;
  app.use('/api/uploads', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[46][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[46][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[30]++;
    cov_2o9aax85y3().s[144]++;
    return next();
  }), upload_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[145]++;
  app.use('/api/emails', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[47][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[47][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[31]++;
    cov_2o9aax85y3().s[146]++;
    return next();
  }), email_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[147]++;
  app.use('/api/notifications', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[48][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[48][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[32]++;
    cov_2o9aax85y3().s[148]++;
    return next();
  }), notification_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[149]++;
  app.use('/api/admin', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[49][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[49][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[33]++;
    cov_2o9aax85y3().s[150]++;
    return next();
  }), admin_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[151]++;
  app.use('/api/sessions', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[50][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[50][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[34]++;
    cov_2o9aax85y3().s[152]++;
    return next();
  }), session_routes_1.default);
  /* istanbul ignore next */
  cov_2o9aax85y3().s[153]++;
  app.use('/api/monitoring', environment_1.config.NODE_ENV !== 'test' ?
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[51][0]++, rateLimiting_1.generalRateLimit) :
  /* istanbul ignore next */
  (cov_2o9aax85y3().b[51][1]++, (req, res, next) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[35]++;
    cov_2o9aax85y3().s[154]++;
    return next();
  }), monitoring_routes_1.default);
  // Setup Swagger documentation (only in non-test environment)
  /* istanbul ignore next */
  cov_2o9aax85y3().s[155]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_2o9aax85y3().b[52][0]++;
    cov_2o9aax85y3().s[156]++;
    (0, swagger_1.setupSwagger)(app);
  } else
  /* istanbul ignore next */
  {
    cov_2o9aax85y3().b[52][1]++;
  }
  // API documentation endpoint
  cov_2o9aax85y3().s[157]++;
  app.get('/api', (_req, res) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[36]++;
    cov_2o9aax85y3().s[158]++;
    res.json({
      message: 'LajoSpaces API',
      version: '1.0.0',
      environment: environment_1.config.NODE_ENV,
      documentation: '/api/docs',
      endpoints: {
        auth: '/api/auth',
        users: '/api/users',
        properties: '/api/properties',
        search: '/api/search',
        matches: '/api/matches',
        messages: '/api/messages',
        uploads: '/api/uploads',
        emails: '/api/emails',
        notifications: '/api/notifications',
        admin: '/api/admin',
        sessions: '/api/sessions',
        monitoring: '/api/monitoring',
        docs: '/api/docs'
      }
    });
  });
  // 404 handler
  /* istanbul ignore next */
  cov_2o9aax85y3().s[159]++;
  app.use('*', (req, res) => {
    /* istanbul ignore next */
    cov_2o9aax85y3().f[37]++;
    cov_2o9aax85y3().s[160]++;
    res.status(404).json({
      success: false,
      error: 'Route not found',
      path: req.originalUrl,
      method: req.method
    });
  });
  // Global error handler
  /* istanbul ignore next */
  cov_2o9aax85y3().s[161]++;
  app.use(errorHandler_1.errorHandler);
  // Store initialization function on app for server startup
  /* istanbul ignore next */
  cov_2o9aax85y3().s[162]++;
  app.initializeServices = initializeServices;
  /* istanbul ignore next */
  cov_2o9aax85y3().s[163]++;
  return app;
}
/* istanbul ignore next */
cov_2o9aax85y3().s[164]++;
exports.default = createApp;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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