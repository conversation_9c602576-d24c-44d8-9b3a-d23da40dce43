{"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Failed to connect to MongoDB: Password cannot be empty\u001b[39m","stack":"MongoInvalidArgumentError: Password cannot be empty\n    at passwordDigest (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:165:15)\n    at continueScramConversation (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:95:96)\n    at executeScram (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-06-17 01:12:33:1233"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUncaught Exception: listen EADDRINUSE: address already in use :::3000\u001b[39m","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\dist\\server.js:113:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-17 13:43:07:437"}
