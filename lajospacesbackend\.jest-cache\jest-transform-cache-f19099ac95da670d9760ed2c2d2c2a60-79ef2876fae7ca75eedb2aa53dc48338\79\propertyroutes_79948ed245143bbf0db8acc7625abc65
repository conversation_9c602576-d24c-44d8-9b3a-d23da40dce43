f38a203bc8b8fa66194fa97af3c309e8
"use strict";

/* istanbul ignore next */
function cov_1il925iyfp() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\property.routes.ts";
  var hash = "4667bd160e92d302c1d1e12f3f584bbba4eab1b2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\property.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 30
        },
        end: {
          line: 4,
          column: 75
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 30
        },
        end: {
          line: 7,
          column: 74
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 57
        }
      },
      "8": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 140
        }
      },
      "9": {
        start: {
          line: 26,
          column: 0
        },
        end: {
          line: 26,
          column: 84
        }
      },
      "10": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 32,
          column: 94
        }
      },
      "11": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 163
        }
      },
      "12": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 96
        }
      },
      "13": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 207
        }
      },
      "14": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 56,
          column: 123
        }
      },
      "15": {
        start: {
          line: 62,
          column: 0
        },
        end: {
          line: 62,
          column: 130
        }
      },
      "16": {
        start: {
          line: 68,
          column: 0
        },
        end: {
          line: 68,
          column: 136
        }
      },
      "17": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 69,
          column: 25
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\property.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,4EAW4C;AAC5C,6CAAkD;AAClD,yDAA6E;AAC7E,2EAI2C;AAE3C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,iCAAW,CAAC,CAAC;AAEnC;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,GAAG,EACH,IAAA,4BAAe,EAAC,yCAAmB,EAAE,OAAO,CAAC,EAC7C,mCAAa,CACd,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,QAAQ,EACR,mBAAY,EACZ,wCAAkB,CACnB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,cAAc,EACd,mBAAY,EACZ,4CAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,GAAG,EACH,mBAAY,EACZ,IAAA,4BAAe,EAAC,0CAAoB,EAAE,MAAM,CAAC,EAC7C,oCAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,MAAM,EACN,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,iCAAW,CACZ,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,MAAM,EACN,mBAAY,EACZ,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,IAAA,4BAAe,EAAC,0CAAoB,EAAE,MAAM,CAAC,EAC7C,oCAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,MAAM,EACN,mBAAY,EACZ,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,oCAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,cAAc,EACd,mBAAY,EACZ,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,qCAAe,CAChB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,gBAAgB,EAChB,mBAAY,EACZ,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,0CAAoB,CACrB,CAAC;AAEF,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\property.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  createProperty,\r\n  getProperties,\r\n  getProperty,\r\n  updateProperty,\r\n  deleteProperty,\r\n  getOwnerProperties,\r\n  publishProperty,\r\n  getPropertyAnalytics,\r\n  getPropertySuggestions,\r\n  healthCheck\r\n} from '../controllers/property.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  createPropertySchema,\r\n  updatePropertySchema,\r\n  propertyQuerySchema\r\n} from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   GET /api/properties/health\r\n * @desc    Health check for property service\r\n * @access  Public\r\n */\r\nrouter.get('/health', healthCheck);\r\n\r\n/**\r\n * @route   GET /api/properties\r\n * @desc    Get all properties with filtering and pagination\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(propertyQuerySchema, 'query'),\r\n  getProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/owner\r\n * @desc    Get properties owned by the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/owner',\r\n  authenticate,\r\n  getOwnerProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/suggestions\r\n * @desc    Get property suggestions for the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/suggestions',\r\n  authenticate,\r\n  getPropertySuggestions\r\n);\r\n\r\n/**\r\n * @route   POST /api/properties\r\n * @desc    Create a new property listing\r\n * @access  Private (Property owners only)\r\n */\r\nrouter.post(\r\n  '/',\r\n  authenticate,\r\n  validateRequest(createPropertySchema, 'body'),\r\n  createProperty\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/:id\r\n * @desc    Get a specific property by ID\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getProperty\r\n);\r\n\r\n/**\r\n * @route   PUT /api/properties/:id\r\n * @desc    Update a property listing\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.put(\r\n  '/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  validateRequest(updatePropertySchema, 'body'),\r\n  updateProperty\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/properties/:id\r\n * @desc    Delete a property listing\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.delete(\r\n  '/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  deleteProperty\r\n);\r\n\r\n/**\r\n * @route   POST /api/properties/:id/publish\r\n * @desc    Publish a draft property\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.post(\r\n  '/:id/publish',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  publishProperty\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/:id/analytics\r\n * @desc    Get analytics for a specific property\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.get(\r\n  '/:id/analytics',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  getPropertyAnalytics\r\n);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4667bd160e92d302c1d1e12f3f584bbba4eab1b2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1il925iyfp = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1il925iyfp();
cov_1il925iyfp().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_1il925iyfp().s[1]++, require("express"));
const property_controller_1 =
/* istanbul ignore next */
(cov_1il925iyfp().s[2]++, require("../controllers/property.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_1il925iyfp().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_1il925iyfp().s[4]++, require("../middleware/validation"));
const property_validators_1 =
/* istanbul ignore next */
(cov_1il925iyfp().s[5]++, require("../validators/property.validators"));
const router =
/* istanbul ignore next */
(cov_1il925iyfp().s[6]++, (0, express_1.Router)());
/**
 * @route   GET /api/properties/health
 * @desc    Health check for property service
 * @access  Public
 */
/* istanbul ignore next */
cov_1il925iyfp().s[7]++;
router.get('/health', property_controller_1.healthCheck);
/**
 * @route   GET /api/properties
 * @desc    Get all properties with filtering and pagination
 * @access  Public
 */
/* istanbul ignore next */
cov_1il925iyfp().s[8]++;
router.get('/', (0, validation_1.validateRequest)(property_validators_1.propertyQuerySchema, 'query'), property_controller_1.getProperties);
/**
 * @route   GET /api/properties/owner
 * @desc    Get properties owned by the authenticated user
 * @access  Private
 */
/* istanbul ignore next */
cov_1il925iyfp().s[9]++;
router.get('/owner', auth_1.authenticate, property_controller_1.getOwnerProperties);
/**
 * @route   GET /api/properties/suggestions
 * @desc    Get property suggestions for the authenticated user
 * @access  Private
 */
/* istanbul ignore next */
cov_1il925iyfp().s[10]++;
router.get('/suggestions', auth_1.authenticate, property_controller_1.getPropertySuggestions);
/**
 * @route   POST /api/properties
 * @desc    Create a new property listing
 * @access  Private (Property owners only)
 */
/* istanbul ignore next */
cov_1il925iyfp().s[11]++;
router.post('/', auth_1.authenticate, (0, validation_1.validateRequest)(property_validators_1.createPropertySchema, 'body'), property_controller_1.createProperty);
/**
 * @route   GET /api/properties/:id
 * @desc    Get a specific property by ID
 * @access  Public
 */
/* istanbul ignore next */
cov_1il925iyfp().s[12]++;
router.get('/:id', (0, validation_1.validateObjectId)('id'), property_controller_1.getProperty);
/**
 * @route   PUT /api/properties/:id
 * @desc    Update a property listing
 * @access  Private (Property owner only)
 */
/* istanbul ignore next */
cov_1il925iyfp().s[13]++;
router.put('/:id', auth_1.authenticate, (0, validation_1.validateObjectId)('id'), (0, validation_1.validateRequest)(property_validators_1.updatePropertySchema, 'body'), property_controller_1.updateProperty);
/**
 * @route   DELETE /api/properties/:id
 * @desc    Delete a property listing
 * @access  Private (Property owner only)
 */
/* istanbul ignore next */
cov_1il925iyfp().s[14]++;
router.delete('/:id', auth_1.authenticate, (0, validation_1.validateObjectId)('id'), property_controller_1.deleteProperty);
/**
 * @route   POST /api/properties/:id/publish
 * @desc    Publish a draft property
 * @access  Private (Property owner only)
 */
/* istanbul ignore next */
cov_1il925iyfp().s[15]++;
router.post('/:id/publish', auth_1.authenticate, (0, validation_1.validateObjectId)('id'), property_controller_1.publishProperty);
/**
 * @route   GET /api/properties/:id/analytics
 * @desc    Get analytics for a specific property
 * @access  Private (Property owner only)
 */
/* istanbul ignore next */
cov_1il925iyfp().s[16]++;
router.get('/:id/analytics', auth_1.authenticate, (0, validation_1.validateObjectId)('id'), property_controller_1.getPropertyAnalytics);
/* istanbul ignore next */
cov_1il925iyfp().s[17]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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