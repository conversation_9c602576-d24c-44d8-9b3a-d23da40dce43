# Coverage configuration for LajoSpaces Backend

[run]
source = src/
omit =
    src/**/__tests__/**
    src/**/*.test.ts
    src/**/*.spec.ts
    src/scripts/**
    src/types/**
    src/server.ts
    tests/**
    node_modules/**
    dist/**
    coverage/**

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = coverage/html-report

[xml]
output = coverage/coverage.xml
