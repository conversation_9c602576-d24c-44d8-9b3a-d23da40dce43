{"file": "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\unit\\models\\User.test.ts", "mappings": ";;AACA,mDAAuD;AACvD,mEAAyG;AACzG,uDAAmD;AAEnD,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,IAAA,gCAAiB,GAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,IAAA,kCAAmB,GAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,IAAA,4BAAa,GAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;YAC1E,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAsB;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC1C,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG;gBACf,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,sBAAS,CAAC,iBAAiB,EAAE;aAC3C,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC;YACtE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACvC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC;YACrE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YACpC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YACxE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,KAAK,GAAG,sBAAS,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAExD,MAAM,KAAK,GAAG,IAAI,WAAI,CAAC,SAAS,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;YAEnB,MAAM,KAAK,GAAG,IAAI,WAAI,CAAC,SAAS,CAAC,CAAC;YAClC,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC;YAC9E,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,WAAW,GAAG;gBAClB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;aACjB,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;gBAChC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC;oBAC1C,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;oBAC9B,WAAW,EAAE,KAAK;iBACnB,CAAC,CAAC;gBACH,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;gBAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEpC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;YACtE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YACjE,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAEhC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAI,IAAW,CAAC;QAEhB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,CAAC;YACzE,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAC1B,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC/D,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,MAAM,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACvC,MAAM,OAAO,GAAG,MAAM,WAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,OAAO,GAAG,MAAM,WAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;YACjF,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC1C,OAAO,EAAE;oBACP,GAAG,EAAE,UAAU;oBACf,UAAU,EAAE,oBAAoB;oBAChC,SAAS,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC;oBAClC,WAAW,EAAE;wBACX,SAAS,EAAE,WAAW;wBACtB,OAAO,EAAE,WAAW;qBACrB;iBACF;aACF,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC1C,mBAAmB,EAAE;oBACnB,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;oBAC9B,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,cAAc;oBAC1B,SAAS,EAAE,OAAO;oBAClB,WAAW,EAAE,YAAY;oBACzB,aAAa,EAAE,YAAY;oBAC3B,SAAS,EAAE,SAAS;oBACpB,WAAW,EAAE,YAAY;iBAC1B;aACF,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAElB,MAAM,SAAS,GAAG,MAAM,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAElB,MAAM,SAAS,GAAG,MAAM,WAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,UAAU,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC5C,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC9C,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,WAAI,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;YAE9C,MAAM,WAAW,GAAG,MAAM,WAAI,CAAC,eAAe,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,YAAY,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAC9C,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YACH,MAAM,cAAc,GAAG,sBAAS,CAAC,gBAAgB,CAAC;gBAChD,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;YAEH,MAAM,WAAI,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YAElD,MAAM,aAAa,GAAG,MAAM,WAAI,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\unit\\models\\User.test.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\nimport { User, IUser } from '../../../src/models/User';\r\nimport { setupTestDatabase, clearTestData, cleanupTestDatabase } from '../../../src/config/testDatabase';\r\nimport { testUtils } from '../../setup/jest.setup';\r\n\r\ndescribe('User Model', () => {\r\n  beforeAll(async () => {\r\n    await setupTestDatabase();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await cleanupTestDatabase();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await clearTestData();\r\n  });\r\n\r\n  describe('User Creation', () => {\r\n    it('should create a valid user', async () => {\r\n      const userData = testUtils.generateTestUser();\r\n      const user = new User(userData);\r\n      const savedUser = await user.save();\r\n\r\n      expect(savedUser._id).toBeDefined();\r\n      expect(savedUser.email).toBe(userData.email);\r\n      expect(savedUser.firstName).toBe(userData.firstName);\r\n      expect(savedUser.lastName).toBe(userData.lastName);\r\n      expect(savedUser.phoneNumber).toBe(userData.phoneNumber);\r\n      expect(savedUser.role).toBe('user');\r\n      expect(savedUser.emailVerified).toBe(true);\r\n      expect(savedUser.isActive).toBe(true);\r\n      expect(savedUser).toHaveValidTimestamps();\r\n    });\r\n\r\n    it('should hash password before saving', async () => {\r\n      const userData = testUtils.generateTestUser({ password: 'plaintext123' });\r\n      const user = new User(userData);\r\n      const savedUser = await user.save();\r\n\r\n      expect(savedUser.password).not.toBe('plaintext123');\r\n      expect(savedUser.password).toMatch(/^\\$2[aby]\\$\\d+\\$/); // bcrypt hash pattern\r\n    });\r\n\r\n    it('should generate full name virtual', async () => {\r\n      const userData = testUtils.generateTestUser({\r\n        firstName: 'John',\r\n        lastName: 'Doe'\r\n      });\r\n      const user = new User(userData);\r\n\r\n      expect(user.fullName).toBe('John Doe');\r\n    });\r\n\r\n    it('should set default values correctly', async () => {\r\n      const userData = {\r\n        firstName: 'Test',\r\n        lastName: 'User',\r\n        email: testUtils.randomEmail(),\r\n        password: 'password123',\r\n        phoneNumber: testUtils.randomPhoneNumber()\r\n      };\r\n      const user = new User(userData);\r\n      const savedUser = await user.save();\r\n\r\n      expect(savedUser.role).toBe('user');\r\n      expect(savedUser.emailVerified).toBe(false);\r\n      expect(savedUser.isActive).toBe(true);\r\n      expect(savedUser.preferences).toBeDefined();\r\n      expect(savedUser.preferences.notifications).toBe(true);\r\n      expect(savedUser.preferences.emailUpdates).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('User Validation', () => {\r\n    it('should require firstName', async () => {\r\n      const userData = testUtils.generateTestUser({ firstName: undefined });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/firstName.*required/);\r\n    });\r\n\r\n    it('should require lastName', async () => {\r\n      const userData = testUtils.generateTestUser({ lastName: undefined });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/lastName.*required/);\r\n    });\r\n\r\n    it('should require email', async () => {\r\n      const userData = testUtils.generateTestUser({ email: undefined });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/email.*required/);\r\n    });\r\n\r\n    it('should validate email format', async () => {\r\n      const userData = testUtils.generateTestUser({ email: 'invalid-email' });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/email.*valid/);\r\n    });\r\n\r\n    it('should require unique email', async () => {\r\n      const email = testUtils.randomEmail();\r\n      const userData1 = testUtils.generateTestUser({ email });\r\n      const userData2 = testUtils.generateTestUser({ email });\r\n\r\n      const user1 = new User(userData1);\r\n      await user1.save();\r\n\r\n      const user2 = new User(userData2);\r\n      await expect(user2.save()).rejects.toThrow(/email.*unique/);\r\n    });\r\n\r\n    it('should validate phone number format', async () => {\r\n      const userData = testUtils.generateTestUser({ phoneNumber: 'invalid-phone' });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/phoneNumber.*valid/);\r\n    });\r\n\r\n    it('should accept valid Nigerian phone numbers', async () => {\r\n      const validPhones = [\r\n        '+2348031234567',\r\n        '+2347031234567',\r\n        '+2349031234567',\r\n        '+2348061234567'\r\n      ];\r\n\r\n      for (const phone of validPhones) {\r\n        const userData = testUtils.generateTestUser({ \r\n          email: testUtils.randomEmail(),\r\n          phoneNumber: phone \r\n        });\r\n        const user = new User(userData);\r\n        const savedUser = await user.save();\r\n\r\n        expect(savedUser.phoneNumber).toBe(phone);\r\n      }\r\n    });\r\n\r\n    it('should validate role enum', async () => {\r\n      const userData = testUtils.generateTestUser({ role: 'invalid-role' });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/role.*enum/);\r\n    });\r\n\r\n    it('should validate password length', async () => {\r\n      const userData = testUtils.generateTestUser({ password: '123' });\r\n      const user = new User(userData);\r\n\r\n      await expect(user.save()).rejects.toThrow(/password.*6/);\r\n    });\r\n  });\r\n\r\n  describe('User Methods', () => {\r\n    let user: IUser;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser({ password: 'password123' });\r\n      user = new User(userData);\r\n      await user.save();\r\n    });\r\n\r\n    it('should compare password correctly', async () => {\r\n      const isMatch = await user.comparePassword('password123');\r\n      expect(isMatch).toBe(true);\r\n\r\n      const isNotMatch = await user.comparePassword('wrongpassword');\r\n      expect(isNotMatch).toBe(false);\r\n    });\r\n\r\n    it('should generate auth token', () => {\r\n      const token = user.generateAuthToken();\r\n      expect(typeof token).toBe('string');\r\n      expect(token.split('.')).toHaveLength(3); // JWT format\r\n    });\r\n\r\n    it('should convert to JSON without password', () => {\r\n      const userJSON = user.toJSON();\r\n      expect(userJSON.password).toBeUndefined();\r\n      expect(userJSON.email).toBe(user.email);\r\n      expect(userJSON.firstName).toBe(user.firstName);\r\n    });\r\n  });\r\n\r\n  describe('User Indexes', () => {\r\n    it('should have email index', async () => {\r\n      const indexes = await User.collection.getIndexes();\r\n      const emailIndex = Object.keys(indexes).find(key => key.includes('email'));\r\n      expect(emailIndex).toBeDefined();\r\n    });\r\n\r\n    it('should have phoneNumber index', async () => {\r\n      const indexes = await User.collection.getIndexes();\r\n      const phoneIndex = Object.keys(indexes).find(key => key.includes('phoneNumber'));\r\n      expect(phoneIndex).toBeDefined();\r\n    });\r\n  });\r\n\r\n  describe('User Profile', () => {\r\n    it('should save profile information', async () => {\r\n      const userData = testUtils.generateTestUser({\r\n        profile: {\r\n          bio: 'Test bio',\r\n          occupation: 'Software Developer',\r\n          interests: ['technology', 'music'],\r\n          socialMedia: {\r\n            instagram: '@testuser',\r\n            twitter: '@testuser'\r\n          }\r\n        }\r\n      });\r\n      const user = new User(userData);\r\n      const savedUser = await user.save();\r\n\r\n      expect(savedUser.profile.bio).toBe('Test bio');\r\n      expect(savedUser.profile.occupation).toBe('Software Developer');\r\n      expect(savedUser.profile.interests).toContain('technology');\r\n      expect(savedUser.profile.socialMedia.instagram).toBe('@testuser');\r\n    });\r\n\r\n    it('should save roommate preferences', async () => {\r\n      const userData = testUtils.generateTestUser({\r\n        roommatePreferences: {\r\n          ageRange: { min: 25, max: 35 },\r\n          gender: 'any',\r\n          occupation: 'professional',\r\n          lifestyle: 'quiet',\r\n          cleanliness: 'very_clean',\r\n          smokingPolicy: 'no_smoking',\r\n          petPolicy: 'no_pets',\r\n          guestPolicy: 'occasional'\r\n        }\r\n      });\r\n      const user = new User(userData);\r\n      const savedUser = await user.save();\r\n\r\n      expect(savedUser.roommatePreferences.ageRange.min).toBe(25);\r\n      expect(savedUser.roommatePreferences.gender).toBe('any');\r\n      expect(savedUser.roommatePreferences.smokingPolicy).toBe('no_smoking');\r\n    });\r\n  });\r\n\r\n  describe('User Statics', () => {\r\n    it('should find user by email', async () => {\r\n      const userData = testUtils.generateTestUser();\r\n      const user = new User(userData);\r\n      await user.save();\r\n\r\n      const foundUser = await User.findByEmail(userData.email);\r\n      expect(foundUser).toBeDefined();\r\n      expect(foundUser!.email).toBe(userData.email);\r\n    });\r\n\r\n    it('should find user by phone number', async () => {\r\n      const userData = testUtils.generateTestUser();\r\n      const user = new User(userData);\r\n      await user.save();\r\n\r\n      const foundUser = await User.findByPhoneNumber(userData.phoneNumber);\r\n      expect(foundUser).toBeDefined();\r\n      expect(foundUser!.phoneNumber).toBe(userData.phoneNumber);\r\n    });\r\n\r\n    it('should find active users', async () => {\r\n      const activeUser = testUtils.generateTestUser({ \r\n        email: testUtils.randomEmail(),\r\n        isActive: true \r\n      });\r\n      const inactiveUser = testUtils.generateTestUser({ \r\n        email: testUtils.randomEmail(),\r\n        isActive: false \r\n      });\r\n\r\n      await User.create([activeUser, inactiveUser]);\r\n\r\n      const activeUsers = await User.findActiveUsers();\r\n      expect(activeUsers).toHaveLength(1);\r\n      expect(activeUsers[0].isActive).toBe(true);\r\n    });\r\n\r\n    it('should find verified users', async () => {\r\n      const verifiedUser = testUtils.generateTestUser({ \r\n        email: testUtils.randomEmail(),\r\n        emailVerified: true \r\n      });\r\n      const unverifiedUser = testUtils.generateTestUser({ \r\n        email: testUtils.randomEmail(),\r\n        emailVerified: false \r\n      });\r\n\r\n      await User.create([verifiedUser, unverifiedUser]);\r\n\r\n      const verifiedUsers = await User.findVerifiedUsers();\r\n      expect(verifiedUsers).toHaveLength(1);\r\n      expect(verifiedUsers[0].emailVerified).toBe(true);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}