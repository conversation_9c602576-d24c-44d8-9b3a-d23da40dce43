{"version": 3, "names": ["cov_chg74iw21", "actualCoverage", "s", "logger_1", "require", "environment_1", "ErrorSeverity", "f", "b", "exports", "Error<PERSON>ate<PERSON><PERSON>", "ErrorTrackingService", "constructor", "errorMetrics", "errorCount", "errorRate", "lastError", "Date", "errorsByType", "errorsByEndpoint", "criticalErrors", "errorHistory", "MAX_HISTORY_SIZE", "CRITICAL_ERROR_THRESHOLD", "ERROR_RATE_WINDOW", "trackError", "error", "context", "severity", "MEDIUM", "category", "SYSTEM", "timestamp", "enhancedContext", "toISOString", "environment", "config", "NODE_ENV", "version", "process", "env", "npm_package_version", "push", "length", "shift", "updateMetrics", "logError", "checkCriticalConditions", "send<PERSON><PERSON><PERSON>", "errorType", "name", "endpoint", "CRITICAL", "calculateErrorRate", "oneMinuteAgo", "now", "recentErrors", "filter", "entry", "logData", "message", "stack", "metrics", "totalErrors", "logger", "HIGH", "warn", "LOW", "info", "oneHourAgo", "criticalErrorsLastHour", "threshold", "SECURITY", "alertType", "requiresImmediateAttention", "getMetrics", "getRecentErrors", "limit", "slice", "reverse", "getErrorsByCategory", "getHealthSummary", "status", "cleanup", "oneDayAgo", "errorTrackingService", "authentication", "AUTHENTICATION", "authorization", "AUTHORIZATION", "validation", "VALIDATION", "database", "DATABASE", "security", "externalService", "EXTERNAL_SERVICE", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\errorTrackingService.ts"], "sourcesContent": ["import { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\n\r\n/**\r\n * Error Tracking Service for LajoSpaces Backend\r\n * Provides comprehensive error tracking, categorization, and alerting\r\n */\r\n\r\nexport interface ErrorContext {\r\n  userId?: string;\r\n  requestId?: string;\r\n  endpoint?: string;\r\n  method?: string;\r\n  ip?: string;\r\n  userAgent?: string;\r\n  timestamp?: string;\r\n  environment?: string;\r\n  version?: string;\r\n  additionalData?: any;\r\n}\r\n\r\nexport interface ErrorMetrics {\r\n  errorCount: number;\r\n  errorRate: number;\r\n  lastError: Date;\r\n  errorsByType: Record<string, number>;\r\n  errorsByEndpoint: Record<string, number>;\r\n  criticalErrors: number;\r\n}\r\n\r\nexport enum ErrorSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\nexport enum ErrorCategory {\r\n  AUTHENTICATION = 'authentication',\r\n  AUTHORIZATION = 'authorization',\r\n  VALIDATION = 'validation',\r\n  DATABASE = 'database',\r\n  EXTERNAL_SERVICE = 'external_service',\r\n  NETWORK = 'network',\r\n  SYSTEM = 'system',\r\n  BUSINESS_LOGIC = 'business_logic',\r\n  SECURITY = 'security',\r\n  PERFORMANCE = 'performance'\r\n}\r\n\r\nclass ErrorTrackingService {\r\n  private errorMetrics: ErrorMetrics = {\r\n    errorCount: 0,\r\n    errorRate: 0,\r\n    lastError: new Date(),\r\n    errorsByType: {},\r\n    errorsByEndpoint: {},\r\n    criticalErrors: 0\r\n  };\r\n\r\n  private errorHistory: Array<{\r\n    error: Error;\r\n    context: ErrorContext;\r\n    severity: ErrorSeverity;\r\n    category: ErrorCategory;\r\n    timestamp: Date;\r\n  }> = [];\r\n\r\n  private readonly MAX_HISTORY_SIZE = 1000;\r\n  private readonly CRITICAL_ERROR_THRESHOLD = 10; // per hour\r\n  private readonly ERROR_RATE_WINDOW = 60 * 1000; // 1 minute\r\n\r\n  /**\r\n   * Track an error with context and categorization\r\n   */\r\n  trackError(\r\n    error: Error,\r\n    context: ErrorContext = {},\r\n    severity: ErrorSeverity = ErrorSeverity.MEDIUM,\r\n    category: ErrorCategory = ErrorCategory.SYSTEM\r\n  ): void {\r\n    const timestamp = new Date();\r\n    const enhancedContext: ErrorContext = {\r\n      ...context,\r\n      timestamp: timestamp.toISOString(),\r\n      environment: config.NODE_ENV,\r\n      version: process.env.npm_package_version || '1.0.0'\r\n    };\r\n\r\n    // Add to history\r\n    this.errorHistory.push({\r\n      error,\r\n      context: enhancedContext,\r\n      severity,\r\n      category,\r\n      timestamp\r\n    });\r\n\r\n    // Maintain history size\r\n    if (this.errorHistory.length > this.MAX_HISTORY_SIZE) {\r\n      this.errorHistory.shift();\r\n    }\r\n\r\n    // Update metrics\r\n    this.updateMetrics(error, context, severity, category);\r\n\r\n    // Log the error\r\n    this.logError(error, enhancedContext, severity, category);\r\n\r\n    // Check for critical conditions\r\n    this.checkCriticalConditions(severity);\r\n\r\n    // Send alerts if necessary\r\n    this.sendAlerts(error, enhancedContext, severity, category);\r\n  }\r\n\r\n  /**\r\n   * Update error metrics\r\n   */\r\n  private updateMetrics(\r\n    error: Error,\r\n    context: ErrorContext,\r\n    severity: ErrorSeverity,\r\n    category: ErrorCategory\r\n  ): void {\r\n    this.errorMetrics.errorCount++;\r\n    this.errorMetrics.lastError = new Date();\r\n\r\n    // Count by type\r\n    const errorType = error.constructor.name;\r\n    this.errorMetrics.errorsByType[errorType] = (this.errorMetrics.errorsByType[errorType] || 0) + 1;\r\n\r\n    // Count by endpoint\r\n    if (context.endpoint) {\r\n      this.errorMetrics.errorsByEndpoint[context.endpoint] = \r\n        (this.errorMetrics.errorsByEndpoint[context.endpoint] || 0) + 1;\r\n    }\r\n\r\n    // Count critical errors\r\n    if (severity === ErrorSeverity.CRITICAL) {\r\n      this.errorMetrics.criticalErrors++;\r\n    }\r\n\r\n    // Calculate error rate\r\n    this.calculateErrorRate();\r\n  }\r\n\r\n  /**\r\n   * Calculate error rate per minute\r\n   */\r\n  private calculateErrorRate(): void {\r\n    const oneMinuteAgo = new Date(Date.now() - this.ERROR_RATE_WINDOW);\r\n    const recentErrors = this.errorHistory.filter(\r\n      entry => entry.timestamp > oneMinuteAgo\r\n    );\r\n    this.errorMetrics.errorRate = recentErrors.length;\r\n  }\r\n\r\n  /**\r\n   * Log error with appropriate level\r\n   */\r\n  private logError(\r\n    error: Error,\r\n    context: ErrorContext,\r\n    severity: ErrorSeverity,\r\n    category: ErrorCategory\r\n  ): void {\r\n    const logData = {\r\n      error: {\r\n        name: error.name,\r\n        message: error.message,\r\n        stack: error.stack\r\n      },\r\n      context,\r\n      severity,\r\n      category,\r\n      metrics: {\r\n        totalErrors: this.errorMetrics.errorCount,\r\n        errorRate: this.errorMetrics.errorRate,\r\n        criticalErrors: this.errorMetrics.criticalErrors\r\n      }\r\n    };\r\n\r\n    switch (severity) {\r\n      case ErrorSeverity.CRITICAL:\r\n        logger.error(`CRITICAL ERROR [${category}]: ${error.message}`, logData);\r\n        break;\r\n      case ErrorSeverity.HIGH:\r\n        logger.error(`HIGH SEVERITY ERROR [${category}]: ${error.message}`, logData);\r\n        break;\r\n      case ErrorSeverity.MEDIUM:\r\n        logger.warn(`MEDIUM SEVERITY ERROR [${category}]: ${error.message}`, logData);\r\n        break;\r\n      case ErrorSeverity.LOW:\r\n        logger.info(`LOW SEVERITY ERROR [${category}]: ${error.message}`, logData);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check for critical error conditions\r\n   */\r\n  private checkCriticalConditions(severity: ErrorSeverity): void {\r\n    // Check critical error threshold\r\n    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\r\n    const criticalErrorsLastHour = this.errorHistory.filter(\r\n      entry => entry.timestamp > oneHourAgo && entry.severity === ErrorSeverity.CRITICAL\r\n    ).length;\r\n\r\n    if (criticalErrorsLastHour >= this.CRITICAL_ERROR_THRESHOLD) {\r\n      logger.error('ALERT: Critical error threshold exceeded', {\r\n        criticalErrorsLastHour,\r\n        threshold: this.CRITICAL_ERROR_THRESHOLD,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }\r\n\r\n    // Check error rate spike\r\n    if (this.errorMetrics.errorRate > 50) { // More than 50 errors per minute\r\n      logger.error('ALERT: High error rate detected', {\r\n        errorRate: this.errorMetrics.errorRate,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send alerts for critical errors\r\n   */\r\n  private sendAlerts(\r\n    error: Error,\r\n    context: ErrorContext,\r\n    severity: ErrorSeverity,\r\n    category: ErrorCategory\r\n  ): void {\r\n    // In production, this would integrate with alerting services\r\n    // For now, we'll use enhanced logging\r\n    if (severity === ErrorSeverity.CRITICAL || category === ErrorCategory.SECURITY) {\r\n      logger.error('ALERT TRIGGERED', {\r\n        alertType: 'critical_error',\r\n        error: error.message,\r\n        severity,\r\n        category,\r\n        context,\r\n        timestamp: new Date().toISOString(),\r\n        requiresImmediateAttention: true\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get error metrics\r\n   */\r\n  getMetrics(): ErrorMetrics {\r\n    this.calculateErrorRate();\r\n    return { ...this.errorMetrics };\r\n  }\r\n\r\n  /**\r\n   * Get recent errors\r\n   */\r\n  getRecentErrors(limit: number = 50): Array<{\r\n    error: Error;\r\n    context: ErrorContext;\r\n    severity: ErrorSeverity;\r\n    category: ErrorCategory;\r\n    timestamp: Date;\r\n  }> {\r\n    return this.errorHistory\r\n      .slice(-limit)\r\n      .reverse(); // Most recent first\r\n  }\r\n\r\n  /**\r\n   * Get errors by category\r\n   */\r\n  getErrorsByCategory(category: ErrorCategory, limit: number = 20): Array<any> {\r\n    return this.errorHistory\r\n      .filter(entry => entry.category === category)\r\n      .slice(-limit)\r\n      .reverse();\r\n  }\r\n\r\n  /**\r\n   * Get error summary for health checks\r\n   */\r\n  getHealthSummary(): {\r\n    status: 'healthy' | 'warning' | 'critical';\r\n    errorCount: number;\r\n    errorRate: number;\r\n    criticalErrors: number;\r\n    lastError: Date;\r\n  } {\r\n    const { errorCount, errorRate, criticalErrors, lastError } = this.errorMetrics;\r\n    \r\n    let status: 'healthy' | 'warning' | 'critical' = 'healthy';\r\n    \r\n    if (criticalErrors > 0 || errorRate > 50) {\r\n      status = 'critical';\r\n    } else if (errorRate > 20) {\r\n      status = 'warning';\r\n    }\r\n\r\n    return {\r\n      status,\r\n      errorCount,\r\n      errorRate,\r\n      criticalErrors,\r\n      lastError\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clear old error history (cleanup)\r\n   */\r\n  cleanup(): void {\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    this.errorHistory = this.errorHistory.filter(\r\n      entry => entry.timestamp > oneDayAgo\r\n    );\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const errorTrackingService = new ErrorTrackingService();\r\n\r\n// Helper functions for common error scenarios\r\nexport const trackError = {\r\n  authentication: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.HIGH,\r\n      ErrorCategory.AUTHENTICATION\r\n    );\r\n  },\r\n\r\n  authorization: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.HIGH,\r\n      ErrorCategory.AUTHORIZATION\r\n    );\r\n  },\r\n\r\n  validation: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.LOW,\r\n      ErrorCategory.VALIDATION\r\n    );\r\n  },\r\n\r\n  database: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.HIGH,\r\n      ErrorCategory.DATABASE\r\n    );\r\n  },\r\n\r\n  security: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.CRITICAL,\r\n      ErrorCategory.SECURITY\r\n    );\r\n  },\r\n\r\n  externalService: (error: Error, context: ErrorContext = {}) => {\r\n    errorTrackingService.trackError(\r\n      error,\r\n      context,\r\n      ErrorSeverity.MEDIUM,\r\n      ErrorCategory.EXTERNAL_SERVICE\r\n    );\r\n  }\r\n};\r\n\r\nexport default errorTrackingService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAvCF,MAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,aAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AA6BA,IAAYE,aAKX;AAAA;AAAAN,aAAA,GAAAE,CAAA;AALD,WAAYI,aAAa;EAAA;EAAAN,aAAA,GAAAO,CAAA;EAAAP,aAAA,GAAAE,CAAA;EACvBI,aAAA,eAAW;EAAA;EAAAN,aAAA,GAAAE,CAAA;EACXI,aAAA,qBAAiB;EAAA;EAAAN,aAAA,GAAAE,CAAA;EACjBI,aAAA,iBAAa;EAAA;EAAAN,aAAA,GAAAE,CAAA;EACbI,aAAA,yBAAqB;AACvB,CAAC;AALW;AAAA,CAAAN,aAAA,GAAAQ,CAAA,UAAAF,aAAa;AAAA;AAAA,CAAAN,aAAA,GAAAQ,CAAA,UAAAC,OAAA,CAAAH,aAAA,GAAbA,aAAa;AAOzB,IAAYI,aAWX;AAAA;AAAAV,aAAA,GAAAE,CAAA;AAXD,WAAYQ,aAAa;EAAA;EAAAV,aAAA,GAAAO,CAAA;EAAAP,aAAA,GAAAE,CAAA;EACvBQ,aAAA,qCAAiC;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACjCQ,aAAA,mCAA+B;EAAA;EAAAV,aAAA,GAAAE,CAAA;EAC/BQ,aAAA,6BAAyB;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACzBQ,aAAA,yBAAqB;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACrBQ,aAAA,yCAAqC;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACrCQ,aAAA,uBAAmB;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACnBQ,aAAA,qBAAiB;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACjBQ,aAAA,qCAAiC;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACjCQ,aAAA,yBAAqB;EAAA;EAAAV,aAAA,GAAAE,CAAA;EACrBQ,aAAA,+BAA2B;AAC7B,CAAC;AAXW;AAAA,CAAAV,aAAA,GAAAQ,CAAA,UAAAE,aAAa;AAAA;AAAA,CAAAV,aAAA,GAAAQ,CAAA,UAAAC,OAAA,CAAAC,aAAA,GAAbA,aAAa;AAazB,MAAMC,oBAAoB;EAA1BC,YAAA;IAAA;IAAAZ,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACU,KAAAW,YAAY,GAAiB;MACnCC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;KACjB;IAAC;IAAApB,aAAA,GAAAE,CAAA;IAEM,KAAAmB,YAAY,GAMf,EAAE;IAAC;IAAArB,aAAA,GAAAE,CAAA;IAES,KAAAoB,gBAAgB,GAAG,IAAI;IAAC;IAAAtB,aAAA,GAAAE,CAAA;IACxB,KAAAqB,wBAAwB,GAAG,EAAE,CAAC,CAAC;IAAA;IAAAvB,aAAA,GAAAE,CAAA;IAC/B,KAAAsB,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EA2PlD;EAzPE;;;EAGAC,UAAUA,CACRC,KAAY,EACZC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,UAAwB,EAAE,GAC1BoB,QAAA;EAAA;EAAA,CAAA5B,aAAA,GAAAQ,CAAA,UAA0BF,aAAa,CAACuB,MAAM,GAC9CC,QAAA;EAAA;EAAA,CAAA9B,aAAA,GAAAQ,CAAA,UAA0BE,aAAa,CAACqB,MAAM;IAAA;IAAA/B,aAAA,GAAAO,CAAA;IAE9C,MAAMyB,SAAS;IAAA;IAAA,CAAAhC,aAAA,GAAAE,CAAA,QAAG,IAAIe,IAAI,EAAE;IAC5B,MAAMgB,eAAe;IAAA;IAAA,CAAAjC,aAAA,GAAAE,CAAA,QAAiB;MACpC,GAAGyB,OAAO;MACVK,SAAS,EAAEA,SAAS,CAACE,WAAW,EAAE;MAClCC,WAAW,EAAE9B,aAAA,CAAA+B,MAAM,CAACC,QAAQ;MAC5BC,OAAO;MAAE;MAAA,CAAAtC,aAAA,GAAAQ,CAAA,UAAA+B,OAAO,CAACC,GAAG,CAACC,mBAAmB;MAAA;MAAA,CAAAzC,aAAA,GAAAQ,CAAA,UAAI,OAAO;KACpD;IAED;IAAA;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI,CAACmB,YAAY,CAACqB,IAAI,CAAC;MACrBhB,KAAK;MACLC,OAAO,EAAEM,eAAe;MACxBL,QAAQ;MACRE,QAAQ;MACRE;KACD,CAAC;IAEF;IAAA;IAAAhC,aAAA,GAAAE,CAAA;IACA,IAAI,IAAI,CAACmB,YAAY,CAACsB,MAAM,GAAG,IAAI,CAACrB,gBAAgB,EAAE;MAAA;MAAAtB,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACpD,IAAI,CAACmB,YAAY,CAACuB,KAAK,EAAE;IAC3B,CAAC;IAAA;IAAA;MAAA5C,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI,CAAC2C,aAAa,CAACnB,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEE,QAAQ,CAAC;IAEtD;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IACA,IAAI,CAAC4C,QAAQ,CAACpB,KAAK,EAAEO,eAAe,EAAEL,QAAQ,EAAEE,QAAQ,CAAC;IAEzD;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IACA,IAAI,CAAC6C,uBAAuB,CAACnB,QAAQ,CAAC;IAEtC;IAAA;IAAA5B,aAAA,GAAAE,CAAA;IACA,IAAI,CAAC8C,UAAU,CAACtB,KAAK,EAAEO,eAAe,EAAEL,QAAQ,EAAEE,QAAQ,CAAC;EAC7D;EAEA;;;EAGQe,aAAaA,CACnBnB,KAAY,EACZC,OAAqB,EACrBC,QAAuB,EACvBE,QAAuB;IAAA;IAAA9B,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAEvB,IAAI,CAACW,YAAY,CAACC,UAAU,EAAE;IAAC;IAAAd,aAAA,GAAAE,CAAA;IAC/B,IAAI,CAACW,YAAY,CAACG,SAAS,GAAG,IAAIC,IAAI,EAAE;IAExC;IACA,MAAMgC,SAAS;IAAA;IAAA,CAAAjD,aAAA,GAAAE,CAAA,QAAGwB,KAAK,CAACd,WAAW,CAACsC,IAAI;IAAC;IAAAlD,aAAA,GAAAE,CAAA;IACzC,IAAI,CAACW,YAAY,CAACK,YAAY,CAAC+B,SAAS,CAAC,GAAG;IAAC;IAAA,CAAAjD,aAAA,GAAAQ,CAAA,cAAI,CAACK,YAAY,CAACK,YAAY,CAAC+B,SAAS,CAAC;IAAA;IAAA,CAAAjD,aAAA,GAAAQ,CAAA,UAAI,CAAC,KAAI,CAAC;IAEhG;IAAA;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAIyB,OAAO,CAACwB,QAAQ,EAAE;MAAA;MAAAnD,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACpB,IAAI,CAACW,YAAY,CAACM,gBAAgB,CAACQ,OAAO,CAACwB,QAAQ,CAAC,GAClD;MAAC;MAAA,CAAAnD,aAAA,GAAAQ,CAAA,cAAI,CAACK,YAAY,CAACM,gBAAgB,CAACQ,OAAO,CAACwB,QAAQ,CAAC;MAAA;MAAA,CAAAnD,aAAA,GAAAQ,CAAA,UAAI,CAAC,KAAI,CAAC;IACnE,CAAC;IAAA;IAAA;MAAAR,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI0B,QAAQ,KAAKtB,aAAa,CAAC8C,QAAQ,EAAE;MAAA;MAAApD,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACvC,IAAI,CAACW,YAAY,CAACO,cAAc,EAAE;IACpC,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI,CAACmD,kBAAkB,EAAE;EAC3B;EAEA;;;EAGQA,kBAAkBA,CAAA;IAAA;IAAArD,aAAA,GAAAO,CAAA;IACxB,MAAM+C,YAAY;IAAA;IAAA,CAAAtD,aAAA,GAAAE,CAAA,QAAG,IAAIe,IAAI,CAACA,IAAI,CAACsC,GAAG,EAAE,GAAG,IAAI,CAAC/B,iBAAiB,CAAC;IAClE,MAAMgC,YAAY;IAAA;IAAA,CAAAxD,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACmB,YAAY,CAACoC,MAAM,CAC3CC,KAAK,IAAI;MAAA;MAAA1D,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAwD,KAAK,CAAC1B,SAAS,GAAGsB,YAAY;IAAZ,CAAY,CACxC;IAAC;IAAAtD,aAAA,GAAAE,CAAA;IACF,IAAI,CAACW,YAAY,CAACE,SAAS,GAAGyC,YAAY,CAACb,MAAM;EACnD;EAEA;;;EAGQG,QAAQA,CACdpB,KAAY,EACZC,OAAqB,EACrBC,QAAuB,EACvBE,QAAuB;IAAA;IAAA9B,aAAA,GAAAO,CAAA;IAEvB,MAAMoD,OAAO;IAAA;IAAA,CAAA3D,aAAA,GAAAE,CAAA,QAAG;MACdwB,KAAK,EAAE;QACLwB,IAAI,EAAExB,KAAK,CAACwB,IAAI;QAChBU,OAAO,EAAElC,KAAK,CAACkC,OAAO;QACtBC,KAAK,EAAEnC,KAAK,CAACmC;OACd;MACDlC,OAAO;MACPC,QAAQ;MACRE,QAAQ;MACRgC,OAAO,EAAE;QACPC,WAAW,EAAE,IAAI,CAAClD,YAAY,CAACC,UAAU;QACzCC,SAAS,EAAE,IAAI,CAACF,YAAY,CAACE,SAAS;QACtCK,cAAc,EAAE,IAAI,CAACP,YAAY,CAACO;;KAErC;IAAC;IAAApB,aAAA,GAAAE,CAAA;IAEF,QAAQ0B,QAAQ;MACd,KAAKtB,aAAa,CAAC8C,QAAQ;QAAA;QAAApD,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACzBC,QAAA,CAAA6D,MAAM,CAACtC,KAAK,CAAC,mBAAmBI,QAAQ,MAAMJ,KAAK,CAACkC,OAAO,EAAE,EAAED,OAAO,CAAC;QAAC;QAAA3D,aAAA,GAAAE,CAAA;QACxE;MACF,KAAKI,aAAa,CAAC2D,IAAI;QAAA;QAAAjE,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACrBC,QAAA,CAAA6D,MAAM,CAACtC,KAAK,CAAC,wBAAwBI,QAAQ,MAAMJ,KAAK,CAACkC,OAAO,EAAE,EAAED,OAAO,CAAC;QAAC;QAAA3D,aAAA,GAAAE,CAAA;QAC7E;MACF,KAAKI,aAAa,CAACuB,MAAM;QAAA;QAAA7B,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACvBC,QAAA,CAAA6D,MAAM,CAACE,IAAI,CAAC,0BAA0BpC,QAAQ,MAAMJ,KAAK,CAACkC,OAAO,EAAE,EAAED,OAAO,CAAC;QAAC;QAAA3D,aAAA,GAAAE,CAAA;QAC9E;MACF,KAAKI,aAAa,CAAC6D,GAAG;QAAA;QAAAnE,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACpBC,QAAA,CAAA6D,MAAM,CAACI,IAAI,CAAC,uBAAuBtC,QAAQ,MAAMJ,KAAK,CAACkC,OAAO,EAAE,EAAED,OAAO,CAAC;QAAC;QAAA3D,aAAA,GAAAE,CAAA;QAC3E;IACJ;EACF;EAEA;;;EAGQ6C,uBAAuBA,CAACnB,QAAuB;IAAA;IAAA5B,aAAA,GAAAO,CAAA;IACrD;IACA,MAAM8D,UAAU;IAAA;IAAA,CAAArE,aAAA,GAAAE,CAAA,QAAG,IAAIe,IAAI,CAACA,IAAI,CAACsC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACxD,MAAMe,sBAAsB;IAAA;IAAA,CAAAtE,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACmB,YAAY,CAACoC,MAAM,CACrDC,KAAK,IAAI;MAAA;MAAA1D,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,kCAAAF,aAAA,GAAAQ,CAAA,WAAAkD,KAAK,CAAC1B,SAAS,GAAGqC,UAAU;MAAA;MAAA,CAAArE,aAAA,GAAAQ,CAAA,WAAIkD,KAAK,CAAC9B,QAAQ,KAAKtB,aAAa,CAAC8C,QAAQ;IAAR,CAAQ,CACnF,CAACT,MAAM;IAAC;IAAA3C,aAAA,GAAAE,CAAA;IAET,IAAIoE,sBAAsB,IAAI,IAAI,CAAC/C,wBAAwB,EAAE;MAAA;MAAAvB,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC3DC,QAAA,CAAA6D,MAAM,CAACtC,KAAK,CAAC,0CAA0C,EAAE;QACvD4C,sBAAsB;QACtBC,SAAS,EAAE,IAAI,CAAChD,wBAAwB;QACxCS,SAAS,EAAE,IAAIf,IAAI,EAAE,CAACiB,WAAW;OAClC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAlC,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI,IAAI,CAACW,YAAY,CAACE,SAAS,GAAG,EAAE,EAAE;MAAA;MAAAf,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAE;MACtCC,QAAA,CAAA6D,MAAM,CAACtC,KAAK,CAAC,iCAAiC,EAAE;QAC9CX,SAAS,EAAE,IAAI,CAACF,YAAY,CAACE,SAAS;QACtCiB,SAAS,EAAE,IAAIf,IAAI,EAAE,CAACiB,WAAW;OAClC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAlC,aAAA,GAAAQ,CAAA;IAAA;EACH;EAEA;;;EAGQwC,UAAUA,CAChBtB,KAAY,EACZC,OAAqB,EACrBC,QAAuB,EACvBE,QAAuB;IAAA;IAAA9B,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAEvB;IACA;IACA;IAAI;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAoB,QAAQ,KAAKtB,aAAa,CAAC8C,QAAQ;IAAA;IAAA,CAAApD,aAAA,GAAAQ,CAAA,WAAIsB,QAAQ,KAAKpB,aAAa,CAAC8D,QAAQ,GAAE;MAAA;MAAAxE,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC9EC,QAAA,CAAA6D,MAAM,CAACtC,KAAK,CAAC,iBAAiB,EAAE;QAC9B+C,SAAS,EAAE,gBAAgB;QAC3B/C,KAAK,EAAEA,KAAK,CAACkC,OAAO;QACpBhC,QAAQ;QACRE,QAAQ;QACRH,OAAO;QACPK,SAAS,EAAE,IAAIf,IAAI,EAAE,CAACiB,WAAW,EAAE;QACnCwC,0BAA0B,EAAE;OAC7B,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA1E,aAAA,GAAAQ,CAAA;IAAA;EACH;EAEA;;;EAGAmE,UAAUA,CAAA;IAAA;IAAA3E,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACR,IAAI,CAACmD,kBAAkB,EAAE;IAAC;IAAArD,aAAA,GAAAE,CAAA;IAC1B,OAAO;MAAE,GAAG,IAAI,CAACW;IAAY,CAAE;EACjC;EAEA;;;EAGA+D,eAAeA,CAACC,KAAA;EAAA;EAAA,CAAA7E,aAAA,GAAAQ,CAAA,WAAgB,EAAE;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAOhC,OAAO,IAAI,CAACmB,YAAY,CACrByD,KAAK,CAAC,CAACD,KAAK,CAAC,CACbE,OAAO,EAAE,CAAC,CAAC;EAChB;EAEA;;;EAGAC,mBAAmBA,CAAClD,QAAuB,EAAE+C,KAAA;EAAA;EAAA,CAAA7E,aAAA,GAAAQ,CAAA,WAAgB,EAAE;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAC7D,OAAO,IAAI,CAACmB,YAAY,CACrBoC,MAAM,CAACC,KAAK,IAAI;MAAA;MAAA1D,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAwD,KAAK,CAAC5B,QAAQ,KAAKA,QAAQ;IAAR,CAAQ,CAAC,CAC5CgD,KAAK,CAAC,CAACD,KAAK,CAAC,CACbE,OAAO,EAAE;EACd;EAEA;;;EAGAE,gBAAgBA,CAAA;IAAA;IAAAjF,aAAA,GAAAO,CAAA;IAOd,MAAM;MAAEO,UAAU;MAAEC,SAAS;MAAEK,cAAc;MAAEJ;IAAS,CAAE;IAAA;IAAA,CAAAhB,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACW,YAAY;IAE9E,IAAIqE,MAAM;IAAA;IAAA,CAAAlF,aAAA,GAAAE,CAAA,QAAuC,SAAS;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAE3D;IAAI;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAY,cAAc,GAAG,CAAC;IAAA;IAAA,CAAApB,aAAA,GAAAQ,CAAA,WAAIO,SAAS,GAAG,EAAE,GAAE;MAAA;MAAAf,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACxCgF,MAAM,GAAG,UAAU;IACrB,CAAC,MAAM;MAAA;MAAAlF,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,IAAIa,SAAS,GAAG,EAAE,EAAE;QAAA;QAAAf,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACzBgF,MAAM,GAAG,SAAS;MACpB,CAAC;MAAA;MAAA;QAAAlF,aAAA,GAAAQ,CAAA;MAAA;IAAD;IAAC;IAAAR,aAAA,GAAAE,CAAA;IAED,OAAO;MACLgF,MAAM;MACNpE,UAAU;MACVC,SAAS;MACTK,cAAc;MACdJ;KACD;EACH;EAEA;;;EAGAmE,OAAOA,CAAA;IAAA;IAAAnF,aAAA,GAAAO,CAAA;IACL,MAAM6E,SAAS;IAAA;IAAA,CAAApF,aAAA,GAAAE,CAAA,QAAG,IAAIe,IAAI,CAACA,IAAI,CAACsC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAC;IAAAvD,aAAA,GAAAE,CAAA;IAC7D,IAAI,CAACmB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACoC,MAAM,CAC1CC,KAAK,IAAI;MAAA;MAAA1D,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAwD,KAAK,CAAC1B,SAAS,GAAGoD,SAAS;IAAT,CAAS,CACrC;EACH;;AAGF;AAAA;AAAApF,aAAA,GAAAE,CAAA;AACaO,OAAA,CAAA4E,oBAAoB,GAAG,IAAI1E,oBAAoB,EAAE;AAE9D;AAAA;AAAAX,aAAA,GAAAE,CAAA;AACaO,OAAA,CAAAgB,UAAU,GAAG;EACxB6D,cAAc,EAAEA,CAAC5D,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAC3DO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAAC2D,IAAI,EAClBvD,aAAa,CAAC6E,cAAc,CAC7B;EACH,CAAC;EAEDC,aAAa,EAAEA,CAAC9D,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAC1DO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAAC2D,IAAI,EAClBvD,aAAa,CAAC+E,aAAa,CAC5B;EACH,CAAC;EAEDC,UAAU,EAAEA,CAAChE,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACvDO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAAC6D,GAAG,EACjBzD,aAAa,CAACiF,UAAU,CACzB;EACH,CAAC;EAEDC,QAAQ,EAAEA,CAAClE,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACrDO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAAC2D,IAAI,EAClBvD,aAAa,CAACmF,QAAQ,CACvB;EACH,CAAC;EAEDC,QAAQ,EAAEA,CAACpE,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACrDO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAAC8C,QAAQ,EACtB1C,aAAa,CAAC8D,QAAQ,CACvB;EACH,CAAC;EAEDuB,eAAe,EAAEA,CAACrE,KAAY,EAAEC,OAAA;EAAA;EAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAwB,EAAE,MAAI;IAAA;IAAAR,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAC5DO,OAAA,CAAA4E,oBAAoB,CAAC5D,UAAU,CAC7BC,KAAK,EACLC,OAAO,EACPrB,aAAa,CAACuB,MAAM,EACpBnB,aAAa,CAACsF,gBAAgB,CAC/B;EACH;CACD;AAAC;AAAAhG,aAAA,GAAAE,CAAA;AAEFO,OAAA,CAAAwF,OAAA,GAAexF,OAAA,CAAA4E,oBAAoB", "ignoreList": []}