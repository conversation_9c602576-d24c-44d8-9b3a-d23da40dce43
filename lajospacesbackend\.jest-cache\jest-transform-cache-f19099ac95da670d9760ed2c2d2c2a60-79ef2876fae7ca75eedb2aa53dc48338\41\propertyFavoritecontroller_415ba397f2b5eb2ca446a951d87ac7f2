7eaccab70747138065e8b6c11afabd82
"use strict";

/* istanbul ignore next */
function cov_2jagxutndh() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\propertyFavorite.controller.ts";
  var hash = "5f864eb4618f7757627bbf35eff5c7a89a51bd8a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\propertyFavorite.controller.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 208
        }
      },
      "2": {
        start: {
          line: 4,
          column: 19
        },
        end: {
          line: 4,
          column: 48
        }
      },
      "3": {
        start: {
          line: 5,
          column: 27
        },
        end: {
          line: 5,
          column: 64
        }
      },
      "4": {
        start: {
          line: 6,
          column: 17
        },
        end: {
          line: 6,
          column: 43
        }
      },
      "5": {
        start: {
          line: 7,
          column: 22
        },
        end: {
          line: 7,
          column: 53
        }
      },
      "6": {
        start: {
          line: 8,
          column: 19
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "7": {
        start: {
          line: 9,
          column: 21
        },
        end: {
          line: 9,
          column: 51
        }
      },
      "8": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 50,
          column: 3
        }
      },
      "9": {
        start: {
          line: 14,
          column: 27
        },
        end: {
          line: 14,
          column: 35
        }
      },
      "10": {
        start: {
          line: 15,
          column: 19
        },
        end: {
          line: 15,
          column: 32
        }
      },
      "11": {
        start: {
          line: 17,
          column: 21
        },
        end: {
          line: 17,
          column: 67
        }
      },
      "12": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 20,
          column: 5
        }
      },
      "13": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 65
        }
      },
      "14": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 24,
          column: 5
        }
      },
      "15": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 87
        }
      },
      "16": {
        start: {
          line: 26,
          column: 29
        },
        end: {
          line: 29,
          column: 6
        }
      },
      "17": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 32,
          column: 5
        }
      },
      "18": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 84
        }
      },
      "19": {
        start: {
          line: 34,
          column: 21
        },
        end: {
          line: 37,
          column: 6
        }
      },
      "20": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 26
        }
      },
      "21": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 38
        }
      },
      "22": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 26
        }
      },
      "23": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 85
        }
      },
      "24": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 49,
          column: 56
        }
      },
      "25": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "26": {
        start: {
          line: 55,
          column: 27
        },
        end: {
          line: 55,
          column: 37
        }
      },
      "27": {
        start: {
          line: 56,
          column: 19
        },
        end: {
          line: 56,
          column: 32
        }
      },
      "28": {
        start: {
          line: 58,
          column: 21
        },
        end: {
          line: 61,
          column: 6
        }
      },
      "29": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 64,
          column: 5
        }
      },
      "30": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 83
        }
      },
      "31": {
        start: {
          line: 66,
          column: 21
        },
        end: {
          line: 66,
          column: 67
        }
      },
      "32": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "33": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 42
        }
      },
      "34": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 30
        }
      },
      "35": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 89
        }
      },
      "36": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 104
        }
      },
      "37": {
        start: {
          line: 77,
          column: 0
        },
        end: {
          line: 119,
          column: 3
        }
      },
      "38": {
        start: {
          line: 78,
          column: 19
        },
        end: {
          line: 78,
          column: 32
        }
      },
      "39": {
        start: {
          line: 79,
          column: 79
        },
        end: {
          line: 79,
          column: 88
        }
      },
      "40": {
        start: {
          line: 81,
          column: 24
        },
        end: {
          line: 81,
          column: 26
        }
      },
      "41": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 56
        }
      },
      "42": {
        start: {
          line: 84,
          column: 17
        },
        end: {
          line: 84,
          column: 51
        }
      },
      "43": {
        start: {
          line: 86,
          column: 31
        },
        end: {
          line: 101,
          column: 6
        }
      },
      "44": {
        start: {
          line: 103,
          column: 27
        },
        end: {
          line: 103,
          column: 66
        }
      },
      "45": {
        start: {
          line: 103,
          column: 51
        },
        end: {
          line: 103,
          column: 65
        }
      },
      "46": {
        start: {
          line: 105,
          column: 23
        },
        end: {
          line: 105,
          column: 55
        }
      },
      "47": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 118,
          column: 53
        }
      },
      "48": {
        start: {
          line: 116,
          column: 62
        },
        end: {
          line: 116,
          column: 144
        }
      },
      "49": {
        start: {
          line: 123,
          column: 0
        },
        end: {
          line: 135,
          column: 3
        }
      },
      "50": {
        start: {
          line: 124,
          column: 27
        },
        end: {
          line: 124,
          column: 37
        }
      },
      "51": {
        start: {
          line: 125,
          column: 19
        },
        end: {
          line: 125,
          column: 32
        }
      },
      "52": {
        start: {
          line: 126,
          column: 21
        },
        end: {
          line: 129,
          column: 6
        }
      },
      "53": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 134,
          column: 49
        }
      },
      "54": {
        start: {
          line: 139,
          column: 0
        },
        end: {
          line: 145,
          column: 3
        }
      },
      "55": {
        start: {
          line: 140,
          column: 19
        },
        end: {
          line: 140,
          column: 32
        }
      },
      "56": {
        start: {
          line: 141,
          column: 18
        },
        end: {
          line: 141,
          column: 86
        }
      },
      "57": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 144,
          column: 49
        }
      },
      "58": {
        start: {
          line: 149,
          column: 0
        },
        end: {
          line: 243,
          column: 3
        }
      },
      "59": {
        start: {
          line: 151,
          column: 34
        },
        end: {
          line: 151,
          column: 43
        }
      },
      "60": {
        start: {
          line: 153,
          column: 21
        },
        end: {
          line: 153,
          column: 23
        }
      },
      "61": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "62": {
        start: {
          line: 155,
          column: 20
        },
        end: {
          line: 155,
          column: 30
        }
      },
      "63": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 34
        }
      },
      "64": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 162,
          column: 9
        }
      },
      "65": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 158,
          column: 49
        }
      },
      "66": {
        start: {
          line: 160,
          column: 13
        },
        end: {
          line: 162,
          column: 9
        }
      },
      "67": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 161,
          column: 51
        }
      },
      "68": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 56
        }
      },
      "69": {
        start: {
          line: 166,
          column: 21
        },
        end: {
          line: 231,
          column: 5
        }
      },
      "70": {
        start: {
          line: 232,
          column: 30
        },
        end: {
          line: 232,
          column: 91
        }
      },
      "71": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 242,
          column: 52
        }
      },
      "72": {
        start: {
          line: 247,
          column: 0
        },
        end: {
          line: 321,
          column: 3
        }
      },
      "73": {
        start: {
          line: 248,
          column: 36
        },
        end: {
          line: 248,
          column: 44
        }
      },
      "74": {
        start: {
          line: 249,
          column: 19
        },
        end: {
          line: 249,
          column: 32
        }
      },
      "75": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "76": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 77
        }
      },
      "77": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "78": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 86
        }
      },
      "79": {
        start: {
          line: 256,
          column: 20
        },
        end: {
          line: 259,
          column: 5
        }
      },
      "80": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 310,
          column: 5
        }
      },
      "81": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "82": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 285,
          column: 13
        }
      },
      "83": {
        start: {
          line: 265,
          column: 33
        },
        end: {
          line: 265,
          column: 79
        }
      },
      "84": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 269,
          column: 17
        }
      },
      "85": {
        start: {
          line: 267,
          column: 20
        },
        end: {
          line: 267,
          column: 90
        }
      },
      "86": {
        start: {
          line: 268,
          column: 20
        },
        end: {
          line: 268,
          column: 29
        }
      },
      "87": {
        start: {
          line: 271,
          column: 41
        },
        end: {
          line: 271,
          column: 114
        }
      },
      "88": {
        start: {
          line: 272,
          column: 16
        },
        end: {
          line: 275,
          column: 17
        }
      },
      "89": {
        start: {
          line: 273,
          column: 20
        },
        end: {
          line: 273,
          column: 85
        }
      },
      "90": {
        start: {
          line: 274,
          column: 20
        },
        end: {
          line: 274,
          column: 29
        }
      },
      "91": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 277,
          column: 89
        }
      },
      "92": {
        start: {
          line: 279,
          column: 16
        },
        end: {
          line: 279,
          column: 50
        }
      },
      "93": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 280,
          column: 38
        }
      },
      "94": {
        start: {
          line: 281,
          column: 16
        },
        end: {
          line: 281,
          column: 52
        }
      },
      "95": {
        start: {
          line: 284,
          column: 16
        },
        end: {
          line: 284,
          column: 78
        }
      },
      "96": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 309,
          column: 9
        }
      },
      "97": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 308,
          column: 13
        }
      },
      "98": {
        start: {
          line: 292,
          column: 33
        },
        end: {
          line: 292,
          column: 115
        }
      },
      "99": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 304,
          column: 17
        }
      },
      "100": {
        start: {
          line: 295,
          column: 37
        },
        end: {
          line: 295,
          column: 83
        }
      },
      "101": {
        start: {
          line: 296,
          column: 20
        },
        end: {
          line: 299,
          column: 21
        }
      },
      "102": {
        start: {
          line: 297,
          column: 24
        },
        end: {
          line: 297,
          column: 58
        }
      },
      "103": {
        start: {
          line: 298,
          column: 24
        },
        end: {
          line: 298,
          column: 46
        }
      },
      "104": {
        start: {
          line: 300,
          column: 20
        },
        end: {
          line: 300,
          column: 56
        }
      },
      "105": {
        start: {
          line: 303,
          column: 20
        },
        end: {
          line: 303,
          column: 84
        }
      },
      "106": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 307,
          column: 78
        }
      },
      "107": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 311,
          column: 142
        }
      },
      "108": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 320,
          column: 45
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 13,
            column: 54
          },
          end: {
            line: 13,
            column: 55
          }
        },
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 50,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 59
          },
          end: {
            line: 54,
            column: 60
          }
        },
        loc: {
          start: {
            line: 54,
            column: 79
          },
          end: {
            line: 73,
            column: 1
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 77,
            column: 56
          },
          end: {
            line: 77,
            column: 57
          }
        },
        loc: {
          start: {
            line: 77,
            column: 76
          },
          end: {
            line: 119,
            column: 1
          }
        },
        line: 77
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 103,
            column: 44
          },
          end: {
            line: 103,
            column: 45
          }
        },
        loc: {
          start: {
            line: 103,
            column: 51
          },
          end: {
            line: 103,
            column: 65
          }
        },
        line: 103
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 116,
            column: 55
          },
          end: {
            line: 116,
            column: 56
          }
        },
        loc: {
          start: {
            line: 116,
            column: 62
          },
          end: {
            line: 116,
            column: 144
          }
        },
        line: 116
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 123,
            column: 59
          },
          end: {
            line: 123,
            column: 60
          }
        },
        loc: {
          start: {
            line: 123,
            column: 79
          },
          end: {
            line: 135,
            column: 1
          }
        },
        line: 123
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 139,
            column: 57
          },
          end: {
            line: 139,
            column: 58
          }
        },
        loc: {
          start: {
            line: 139,
            column: 77
          },
          end: {
            line: 145,
            column: 1
          }
        },
        line: 139
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 149,
            column: 60
          },
          end: {
            line: 149,
            column: 61
          }
        },
        loc: {
          start: {
            line: 149,
            column: 80
          },
          end: {
            line: 243,
            column: 1
          }
        },
        line: 149
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 247,
            column: 59
          },
          end: {
            line: 247,
            column: 60
          }
        },
        loc: {
          start: {
            line: 247,
            column: 79
          },
          end: {
            line: 321,
            column: 1
          }
        },
        line: 247
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "1": {
        loc: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 24,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 24,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "2": {
        loc: {
          start: {
            line: 22,
            column: 8
          },
          end: {
            line: 22,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 8
          },
          end: {
            line: 22,
            column: 29
          }
        }, {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 61
          }
        }],
        line: 22
      },
      "3": {
        loc: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 32,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 32,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "4": {
        loc: {
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 64,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 64,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      },
      "5": {
        loc: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "6": {
        loc: {
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 67,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 67,
            column: 16
          }
        }, {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 52
          }
        }],
        line: 67
      },
      "7": {
        loc: {
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 79,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 79,
            column: 19
          },
          end: {
            line: 79,
            column: 20
          }
        }],
        line: 79
      },
      "8": {
        loc: {
          start: {
            line: 79,
            column: 22
          },
          end: {
            line: 79,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 79,
            column: 30
          },
          end: {
            line: 79,
            column: 32
          }
        }],
        line: 79
      },
      "9": {
        loc: {
          start: {
            line: 79,
            column: 34
          },
          end: {
            line: 79,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 79,
            column: 43
          },
          end: {
            line: 79,
            column: 54
          }
        }],
        line: 79
      },
      "10": {
        loc: {
          start: {
            line: 79,
            column: 56
          },
          end: {
            line: 79,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 79,
            column: 68
          },
          end: {
            line: 79,
            column: 74
          }
        }],
        line: 79
      },
      "11": {
        loc: {
          start: {
            line: 82,
            column: 26
          },
          end: {
            line: 82,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 49
          },
          end: {
            line: 82,
            column: 51
          }
        }, {
          start: {
            line: 82,
            column: 54
          },
          end: {
            line: 82,
            column: 55
          }
        }],
        line: 82
      },
      "12": {
        loc: {
          start: {
            line: 116,
            column: 62
          },
          end: {
            line: 116,
            column: 144
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 62
          },
          end: {
            line: 116,
            column: 76
          }
        }, {
          start: {
            line: 116,
            column: 80
          },
          end: {
            line: 116,
            column: 106
          }
        }, {
          start: {
            line: 116,
            column: 110
          },
          end: {
            line: 116,
            column: 144
          }
        }],
        line: 116
      },
      "13": {
        loc: {
          start: {
            line: 132,
            column: 20
          },
          end: {
            line: 132,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 20
          },
          end: {
            line: 132,
            column: 33
          }
        }, {
          start: {
            line: 132,
            column: 37
          },
          end: {
            line: 132,
            column: 41
          }
        }],
        line: 132
      },
      "14": {
        loc: {
          start: {
            line: 133,
            column: 21
          },
          end: {
            line: 133,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 21
          },
          end: {
            line: 133,
            column: 40
          }
        }, {
          start: {
            line: 133,
            column: 44
          },
          end: {
            line: 133,
            column: 48
          }
        }],
        line: 133
      },
      "15": {
        loc: {
          start: {
            line: 150,
            column: 12
          },
          end: {
            line: 150,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 150,
            column: 20
          },
          end: {
            line: 150,
            column: 22
          }
        }],
        line: 150
      },
      "16": {
        loc: {
          start: {
            line: 150,
            column: 24
          },
          end: {
            line: 150,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 150,
            column: 36
          },
          end: {
            line: 150,
            column: 41
          }
        }],
        line: 150
      },
      "17": {
        loc: {
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "18": {
        loc: {
          start: {
            line: 157,
            column: 8
          },
          end: {
            line: 162,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 8
          },
          end: {
            line: 162,
            column: 9
          }
        }, {
          start: {
            line: 160,
            column: 13
          },
          end: {
            line: 162,
            column: 9
          }
        }],
        line: 157
      },
      "19": {
        loc: {
          start: {
            line: 160,
            column: 13
          },
          end: {
            line: 162,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 13
          },
          end: {
            line: 162,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "20": {
        loc: {
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 197,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 197,
            column: 32
          }
        }, {
          start: {
            line: 197,
            column: 36
          },
          end: {
            line: 197,
            column: 77
          }
        }],
        line: 197
      },
      "21": {
        loc: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 198,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 198,
            column: 24
          }
        }, {
          start: {
            line: 198,
            column: 28
          },
          end: {
            line: 198,
            column: 79
          }
        }],
        line: 198
      },
      "22": {
        loc: {
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 199,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 199,
            column: 20
          },
          end: {
            line: 199,
            column: 25
          }
        }, {
          start: {
            line: 199,
            column: 29
          },
          end: {
            line: 199,
            column: 65
          }
        }],
        line: 199
      },
      "23": {
        loc: {
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 252,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 252,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "24": {
        loc: {
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 250,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 250,
            column: 35
          }
        }, {
          start: {
            line: 250,
            column: 39
          },
          end: {
            line: 250,
            column: 63
          }
        }],
        line: 250
      },
      "25": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "26": {
        loc: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 310,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 310,
            column: 5
          }
        }, {
          start: {
            line: 288,
            column: 9
          },
          end: {
            line: 310,
            column: 5
          }
        }],
        line: 260
      },
      "27": {
        loc: {
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 269,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 269,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "28": {
        loc: {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 266,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 266,
            column: 29
          }
        }, {
          start: {
            line: 266,
            column: 33
          },
          end: {
            line: 266,
            column: 54
          }
        }, {
          start: {
            line: 266,
            column: 58
          },
          end: {
            line: 266,
            column: 86
          }
        }],
        line: 266
      },
      "29": {
        loc: {
          start: {
            line: 272,
            column: 16
          },
          end: {
            line: 275,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 16
          },
          end: {
            line: 275,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "30": {
        loc: {
          start: {
            line: 293,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        }, {
          start: {
            line: 302,
            column: 21
          },
          end: {
            line: 304,
            column: 17
          }
        }],
        line: 293
      },
      "31": {
        loc: {
          start: {
            line: 296,
            column: 20
          },
          end: {
            line: 299,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 20
          },
          end: {
            line: 299,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "32": {
        loc: {
          start: {
            line: 296,
            column: 24
          },
          end: {
            line: 296,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 296,
            column: 24
          },
          end: {
            line: 296,
            column: 32
          }
        }, {
          start: {
            line: 296,
            column: 36
          },
          end: {
            line: 296,
            column: 68
          }
        }],
        line: 296
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0],
      "9": [0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\propertyFavorite.controller.ts",
      mappings: ";;;AACA,iDAA8C;AAC9C,iEAA8D;AAC9D,4CAAyC;AACzC,sDAAmD;AACnD,gDAA6C;AAC7C,oDAAiD;AAEjD;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAChC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,2BAA2B;IAC3B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,iCAAiC;IACjC,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC1D,MAAM,IAAI,mBAAQ,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAED,6BAA6B;IAC7B,MAAM,gBAAgB,GAAG,MAAM,mCAAgB,CAAC,OAAO,CAAC;QACtD,MAAM;QACN,UAAU;KACX,CAAC,CAAC;IAEH,IAAI,gBAAgB,EAAE,CAAC;QACrB,MAAM,IAAI,mBAAQ,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,kBAAkB;IAClB,MAAM,QAAQ,GAAG,IAAI,mCAAgB,CAAC;QACpC,MAAM;QACN,UAAU;KACX,CAAC,CAAC;IAEH,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEtB,4BAA4B;IAC5B,QAAQ,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;IAClC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEtB,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,mBAAmB,UAAU,eAAe,CAAC,CAAC;IAExE,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ,CAAC,GAAG;YAChB,UAAU;YACV,SAAS,EAAE,QAAQ,CAAC,SAAS;SAC9B;KACF,EAAE,0CAA0C,EAAE,GAAG,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClF,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAClC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,2BAA2B;IAC3B,MAAM,QAAQ,GAAG,MAAM,mCAAgB,CAAC,gBAAgB,CAAC;QACvD,MAAM;QACN,UAAU;KACX,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAED,4BAA4B;IAC5B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACrD,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;QACjD,QAAQ,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;QAClC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,qBAAqB,UAAU,iBAAiB,CAAC,CAAC;IAE5E,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,8CAA8C,CAAC,CAAC;AACxF,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC/E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,EACnB,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,qBAAqB;IACrB,MAAM,WAAW,GAAQ,EAAE,CAAC;IAC5B,WAAW,CAAC,MAAgB,CAAC,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,uBAAuB;IACvB,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAEhD,sCAAsC;IACtC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC3C,mCAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;aAC9B,IAAI,CAAC,WAAW,CAAC;aACjB,IAAI,CAAC,IAAI,CAAC;aACV,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACpB,QAAQ,CAAC;YACR,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,oHAAoH;YAC5H,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,sCAAsC;aAC/C;SACF,CAAC;aACD,IAAI,EAAE;QACT,mCAAgB,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;KAC5C,CAAC,CAAC;IAEH,uDAAuD;IACvD,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAE/D,4BAA4B;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAEpD,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,SAAS,EAAE,cAAc;QACzB,UAAU,EAAE;YACV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;YACpB,KAAK;YACL,KAAK,EAAE,UAAU;SAClB;QACD,OAAO,EAAE;YACP,cAAc,EAAE,KAAK;YACrB,mBAAmB,EAAE,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAC/C,GAAG,CAAC,UAAU,IAAK,GAAG,CAAC,UAAkB,CAAC,WAAW,IAAK,GAAG,CAAC,UAAkB,CAAC,MAAM,KAAK,QAAQ,CACrG,CAAC,MAAM;SACT;KACF,EAAE,4CAA4C,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClF,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAClC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mCAAgB,CAAC,OAAO,CAAC;QAC9C,MAAM;QACN,UAAU;KACX,CAAC,CAAC;IAEH,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,WAAW,EAAE,CAAC,CAAC,QAAQ;QACvB,UAAU,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI;QACjC,WAAW,EAAE,QAAQ,EAAE,SAAS,IAAI,IAAI;KACzC,EAAE,wCAAwC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAChF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,KAAK,GAAG,MAAM,mCAAgB,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IAEhE,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,KAAK;KACN,EAAE,wCAAwC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnF,MAAM,EACJ,KAAK,GAAG,EAAE,EACV,SAAS,GAAG,KAAK,EAAE,yBAAyB;IAC5C,YAAY,EACZ,IAAI,EACJ,KAAK,EACN,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,kCAAkC;IAClC,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;QACxB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE3B,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YACzB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YACjC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,UAAU,GAAG,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;IAClD,CAAC;IAED,wDAAwD;IACxD,MAAM,QAAQ,GAAU;QACtB,kBAAkB;QAClB,EAAE,MAAM,EAAE,UAAU,EAAE;QAEtB,wCAAwC;QACxC;YACE,MAAM,EAAE;gBACN,GAAG,EAAE,aAAa;gBAClB,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;gBAC1B,cAAc,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACvC;SACF;QAED,yBAAyB;QACzB,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,EAAE;QAEpD,gBAAgB;QAChB,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAEzB,0BAA0B;QAC1B;YACE,OAAO,EAAE;gBACP,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE,KAAK;gBACjB,YAAY,EAAE,KAAK;gBACnB,EAAE,EAAE,UAAU;aACf;SACF;QAED,kBAAkB;QAClB,EAAE,OAAO,EAAE,WAAW,EAAE;QAExB,yBAAyB;QACzB;YACE,MAAM,EAAE;gBACN,iBAAiB,EAAE,QAAQ;gBAC3B,sBAAsB,EAAE,IAAI;gBAC5B,GAAG,CAAC,YAAY,IAAI,EAAE,uBAAuB,EAAE,YAAY,EAAE,CAAC;gBAC9D,GAAG,CAAC,IAAI,IAAI,EAAE,wBAAwB,EAAE,IAAI,MAAM,CAAC,IAAc,EAAE,GAAG,CAAC,EAAE,CAAC;gBAC1E,GAAG,CAAC,KAAK,IAAI,EAAE,yBAAyB,EAAE,KAAK,EAAE,CAAC;aACnD;SACF;QAED,uBAAuB;QACvB;YACE,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,kBAAkB;gBAC9B,YAAY,EAAE,KAAK;gBACnB,EAAE,EAAE,gBAAgB;gBACpB,QAAQ,EAAE;oBACR;wBACE,QAAQ,EAAE;4BACR,SAAS,EAAE,CAAC;4BACZ,QAAQ,EAAE,CAAC;4BACX,KAAK,EAAE,CAAC;4BACR,WAAW,EAAE,CAAC;yBACf;qBACF;iBACF;aACF;SACF;QAED,eAAe;QACf,EAAE,OAAO,EAAE,iBAAiB,EAAE;QAE9B,0BAA0B;QAC1B;YACE,QAAQ,EAAE;gBACR,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;aAClB;SACF;KACF,CAAC;IAEF,MAAM,iBAAiB,GAAG,MAAM,mCAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAErE,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,UAAU,EAAE,iBAAiB;QAC7B,SAAS;QACT,KAAK,EAAE,iBAAiB,CAAC,MAAM;QAC/B,OAAO,EAAE;YACP,YAAY;YACZ,IAAI;YACJ,KAAK;SACN;KACF,EAAE,2CAA2C,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClF,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,4BAA4B;IACtE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5D,MAAM,IAAI,mBAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,mBAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,OAAO,GAGT;QACF,UAAU,EAAE,EAAE;QACd,MAAM,EAAE,EAAE;KACX,CAAC;IAEF,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;QACrB,wBAAwB;QACxB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,4CAA4C;gBAC5C,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACvE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAC,CAAC;oBACtE,SAAS;gBACX,CAAC;gBAED,6BAA6B;gBAC7B,MAAM,gBAAgB,GAAG,MAAM,mCAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;gBAChF,IAAI,gBAAgB,EAAE,CAAC;oBACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBACjE,SAAS;gBACX,CAAC;gBAED,kBAAkB;gBAClB,MAAM,mCAAgB,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;gBAEtD,mBAAmB;gBACnB,QAAQ,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;gBAClC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEtB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,6BAA6B;QAC7B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,mCAAgB,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;gBAEjF,IAAI,QAAQ,EAAE,CAAC;oBACb,mBAAmB;oBACnB,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACrD,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;wBACjD,QAAQ,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;wBAClC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACxB,CAAC;oBACD,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,SAAS,MAAM,eAAe,OAAO,CAAC,UAAU,CAAC,MAAM,gBAAgB,OAAO,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;IAEjI,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,MAAM;QACN,OAAO;QACP,OAAO,EAAE;YACP,KAAK,EAAE,WAAW,CAAC,MAAM;YACzB,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM;YACrC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;SAC9B;KACF,EAAE,QAAQ,MAAM,sBAAsB,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\propertyFavorite.controller.ts"],
      sourcesContent: ["import { Request, Response } from 'express';\r\nimport { Property } from '../models/Property';\r\nimport { PropertyFavorite } from '../models/PropertyFavorite';\r\nimport { logger } from '../utils/logger';\r\nimport { ApiResponse } from '../utils/apiResponse';\r\nimport { AppError } from '../utils/appError';\r\nimport { catchAsync } from '../utils/catchAsync';\r\n\r\n/**\r\n * Add property to favorites\r\n */\r\nexport const addToFavorites = catchAsync(async (req: Request, res: Response) => {\r\n  const { propertyId } = req.body;\r\n  const userId = req.user?._id;\r\n\r\n  // Check if property exists\r\n  const property = await Property.findById(propertyId);\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check if property is available\r\n  if (!property.isAvailable || property.status !== 'active') {\r\n    throw new AppError('Property is not available for favoriting', 400);\r\n  }\r\n\r\n  // Check if already favorited\r\n  const existingFavorite = await PropertyFavorite.findOne({\r\n    userId,\r\n    propertyId\r\n  });\r\n\r\n  if (existingFavorite) {\r\n    throw new AppError('Property is already in your favorites', 400);\r\n  }\r\n\r\n  // Create favorite\r\n  const favorite = new PropertyFavorite({\r\n    userId,\r\n    propertyId\r\n  });\r\n\r\n  await favorite.save();\r\n\r\n  // Update property analytics\r\n  property.analytics.favorites += 1;\r\n  await property.save();\r\n\r\n  logger.info(`User ${userId} added property ${propertyId} to favorites`);\r\n\r\n  return ApiResponse.success(res, {\r\n    favorite: {\r\n      id: favorite._id,\r\n      propertyId,\r\n      createdAt: favorite.createdAt\r\n    }\r\n  }, 'Property added to favorites successfully', 201);\r\n});\r\n\r\n/**\r\n * Remove property from favorites\r\n */\r\nexport const removeFromFavorites = catchAsync(async (req: Request, res: Response) => {\r\n  const { propertyId } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  // Find and remove favorite\r\n  const favorite = await PropertyFavorite.findOneAndDelete({\r\n    userId,\r\n    propertyId\r\n  });\r\n\r\n  if (!favorite) {\r\n    throw new AppError('Property not found in your favorites', 404);\r\n  }\r\n\r\n  // Update property analytics\r\n  const property = await Property.findById(propertyId);\r\n  if (property && property.analytics.favorites > 0) {\r\n    property.analytics.favorites -= 1;\r\n    await property.save();\r\n  }\r\n\r\n  logger.info(`User ${userId} removed property ${propertyId} from favorites`);\r\n\r\n  return ApiResponse.success(res, null, 'Property removed from favorites successfully');\r\n});\r\n\r\n/**\r\n * Get user's favorite properties\r\n */\r\nexport const getUserFavorites = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const {\r\n    page = 1,\r\n    limit = 20,\r\n    sortBy = 'createdAt',\r\n    sortOrder = 'desc'\r\n  } = req.query;\r\n\r\n  // Build sort options\r\n  const sortOptions: any = {};\r\n  sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;\r\n\r\n  // Calculate pagination\r\n  const skip = (Number(page) - 1) * Number(limit);\r\n\r\n  // Get favorites with property details\r\n  const [favorites, total] = await Promise.all([\r\n    PropertyFavorite.find({ userId })\r\n      .sort(sortOptions)\r\n      .skip(skip)\r\n      .limit(Number(limit))\r\n      .populate({\r\n        path: 'propertyId',\r\n        select: 'title description propertyType listingType bedrooms bathrooms location pricing photos status isAvailable analytics',\r\n        populate: {\r\n          path: 'ownerId',\r\n          select: 'firstName lastName email accountType'\r\n        }\r\n      })\r\n      .lean(),\r\n    PropertyFavorite.countDocuments({ userId })\r\n  ]);\r\n\r\n  // Filter out favorites where property no longer exists\r\n  const validFavorites = favorites.filter(fav => fav.propertyId);\r\n\r\n  // Calculate pagination info\r\n  const totalPages = Math.ceil(total / Number(limit));\r\n\r\n  return ApiResponse.success(res, {\r\n    favorites: validFavorites,\r\n    pagination: {\r\n      page: Number(page),\r\n      limit: Number(limit),\r\n      total,\r\n      pages: totalPages\r\n    },\r\n    summary: {\r\n      totalFavorites: total,\r\n      availableProperties: validFavorites.filter(fav => \r\n        fav.propertyId && (fav.propertyId as any).isAvailable && (fav.propertyId as any).status === 'active'\r\n      ).length\r\n    }\r\n  }, 'Favorite properties retrieved successfully');\r\n});\r\n\r\n/**\r\n * Check if property is favorited by user\r\n */\r\nexport const checkFavoriteStatus = catchAsync(async (req: Request, res: Response) => {\r\n  const { propertyId } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const favorite = await PropertyFavorite.findOne({\r\n    userId,\r\n    propertyId\r\n  });\r\n\r\n  return ApiResponse.success(res, {\r\n    isFavorited: !!favorite,\r\n    favoriteId: favorite?._id || null,\r\n    favoritedAt: favorite?.createdAt || null\r\n  }, 'Favorite status retrieved successfully');\r\n});\r\n\r\n/**\r\n * Get favorite properties count\r\n */\r\nexport const getFavoritesCount = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n\r\n  const count = await PropertyFavorite.countDocuments({ userId });\r\n\r\n  return ApiResponse.success(res, {\r\n    count\r\n  }, 'Favorites count retrieved successfully');\r\n});\r\n\r\n/**\r\n * Get popular properties (most favorited)\r\n */\r\nexport const getPopularProperties = catchAsync(async (req: Request, res: Response) => {\r\n  const {\r\n    limit = 20,\r\n    timeframe = 'all', // 'week', 'month', 'all'\r\n    propertyType,\r\n    city,\r\n    state\r\n  } = req.query;\r\n\r\n  // Build date filter for timeframe\r\n  let dateFilter = {};\r\n  if (timeframe !== 'all') {\r\n    const now = new Date();\r\n    let startDate = new Date();\r\n    \r\n    if (timeframe === 'week') {\r\n      startDate.setDate(now.getDate() - 7);\r\n    } else if (timeframe === 'month') {\r\n      startDate.setMonth(now.getMonth() - 1);\r\n    }\r\n    \r\n    dateFilter = { createdAt: { $gte: startDate } };\r\n  }\r\n\r\n  // Aggregation pipeline to get most favorited properties\r\n  const pipeline: any[] = [\r\n    // Match timeframe\r\n    { $match: dateFilter },\r\n    \r\n    // Group by property and count favorites\r\n    {\r\n      $group: {\r\n        _id: '$propertyId',\r\n        favoriteCount: { $sum: 1 },\r\n        latestFavorite: { $max: '$createdAt' }\r\n      }\r\n    },\r\n    \r\n    // Sort by favorite count\r\n    { $sort: { favoriteCount: -1, latestFavorite: -1 } },\r\n    \r\n    // Limit results\r\n    { $limit: Number(limit) },\r\n    \r\n    // Lookup property details\r\n    {\r\n      $lookup: {\r\n        from: 'properties',\r\n        localField: '_id',\r\n        foreignField: '_id',\r\n        as: 'property'\r\n      }\r\n    },\r\n    \r\n    // Unwind property\r\n    { $unwind: '$property' },\r\n    \r\n    // Match property filters\r\n    {\r\n      $match: {\r\n        'property.status': 'active',\r\n        'property.isAvailable': true,\r\n        ...(propertyType && { 'property.propertyType': propertyType }),\r\n        ...(city && { 'property.location.city': new RegExp(city as string, 'i') }),\r\n        ...(state && { 'property.location.state': state })\r\n      }\r\n    },\r\n    \r\n    // Lookup owner details\r\n    {\r\n      $lookup: {\r\n        from: 'users',\r\n        localField: 'property.ownerId',\r\n        foreignField: '_id',\r\n        as: 'property.owner',\r\n        pipeline: [\r\n          {\r\n            $project: {\r\n              firstName: 1,\r\n              lastName: 1,\r\n              email: 1,\r\n              accountType: 1\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    \r\n    // Unwind owner\r\n    { $unwind: '$property.owner' },\r\n    \r\n    // Project final structure\r\n    {\r\n      $project: {\r\n        property: 1,\r\n        favoriteCount: 1,\r\n        latestFavorite: 1\r\n      }\r\n    }\r\n  ];\r\n\r\n  const popularProperties = await PropertyFavorite.aggregate(pipeline);\r\n\r\n  return ApiResponse.success(res, {\r\n    properties: popularProperties,\r\n    timeframe,\r\n    count: popularProperties.length,\r\n    filters: {\r\n      propertyType,\r\n      city,\r\n      state\r\n    }\r\n  }, 'Popular properties retrieved successfully');\r\n});\r\n\r\n/**\r\n * Bulk add/remove favorites\r\n */\r\nexport const bulkUpdateFavorites = catchAsync(async (req: Request, res: Response) => {\r\n  const { propertyIds, action } = req.body; // action: 'add' or 'remove'\r\n  const userId = req.user?._id;\r\n\r\n  if (!Array.isArray(propertyIds) || propertyIds.length === 0) {\r\n    throw new AppError('Property IDs array is required', 400);\r\n  }\r\n\r\n  if (!['add', 'remove'].includes(action)) {\r\n    throw new AppError('Action must be either \"add\" or \"remove\"', 400);\r\n  }\r\n\r\n  const results: {\r\n    successful: any[];\r\n    failed: any[];\r\n  } = {\r\n    successful: [],\r\n    failed: []\r\n  };\r\n\r\n  if (action === 'add') {\r\n    // Bulk add to favorites\r\n    for (const propertyId of propertyIds) {\r\n      try {\r\n        // Check if property exists and is available\r\n        const property = await Property.findById(propertyId);\r\n        if (!property || !property.isAvailable || property.status !== 'active') {\r\n          results.failed.push({ propertyId, reason: 'Property not available' });\r\n          continue;\r\n        }\r\n\r\n        // Check if already favorited\r\n        const existingFavorite = await PropertyFavorite.findOne({ userId, propertyId });\r\n        if (existingFavorite) {\r\n          results.failed.push({ propertyId, reason: 'Already favorited' });\r\n          continue;\r\n        }\r\n\r\n        // Create favorite\r\n        await PropertyFavorite.create({ userId, propertyId });\r\n        \r\n        // Update analytics\r\n        property.analytics.favorites += 1;\r\n        await property.save();\r\n\r\n        results.successful.push(propertyId);\r\n      } catch (error) {\r\n        results.failed.push({ propertyId, reason: 'Database error' });\r\n      }\r\n    }\r\n  } else {\r\n    // Bulk remove from favorites\r\n    for (const propertyId of propertyIds) {\r\n      try {\r\n        const favorite = await PropertyFavorite.findOneAndDelete({ userId, propertyId });\r\n        \r\n        if (favorite) {\r\n          // Update analytics\r\n          const property = await Property.findById(propertyId);\r\n          if (property && property.analytics.favorites > 0) {\r\n            property.analytics.favorites -= 1;\r\n            await property.save();\r\n          }\r\n          results.successful.push(propertyId);\r\n        } else {\r\n          results.failed.push({ propertyId, reason: 'Not in favorites' });\r\n        }\r\n      } catch (error) {\r\n        results.failed.push({ propertyId, reason: 'Database error' });\r\n      }\r\n    }\r\n  }\r\n\r\n  logger.info(`User ${userId} bulk ${action} favorites: ${results.successful.length} successful, ${results.failed.length} failed`);\r\n\r\n  return ApiResponse.success(res, {\r\n    action,\r\n    results,\r\n    summary: {\r\n      total: propertyIds.length,\r\n      successful: results.successful.length,\r\n      failed: results.failed.length\r\n    }\r\n  }, `Bulk ${action} favorites completed`);\r\n});\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5f864eb4618f7757627bbf35eff5c7a89a51bd8a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2jagxutndh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2jagxutndh();
cov_2jagxutndh().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2jagxutndh().s[1]++;
exports.bulkUpdateFavorites = exports.getPopularProperties = exports.getFavoritesCount = exports.checkFavoriteStatus = exports.getUserFavorites = exports.removeFromFavorites = exports.addToFavorites = void 0;
const Property_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[2]++, require("../models/Property"));
const PropertyFavorite_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[3]++, require("../models/PropertyFavorite"));
const logger_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[4]++, require("../utils/logger"));
const apiResponse_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[5]++, require("../utils/apiResponse"));
const appError_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[6]++, require("../utils/appError"));
const catchAsync_1 =
/* istanbul ignore next */
(cov_2jagxutndh().s[7]++, require("../utils/catchAsync"));
/**
 * Add property to favorites
 */
/* istanbul ignore next */
cov_2jagxutndh().s[8]++;
exports.addToFavorites = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[0]++;
  const {
    propertyId
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[9]++, req.body);
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[10]++, req.user?._id);
  // Check if property exists
  const property =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[11]++, await Property_1.Property.findById(propertyId));
  /* istanbul ignore next */
  cov_2jagxutndh().s[12]++;
  if (!property) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[0][0]++;
    cov_2jagxutndh().s[13]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[0][1]++;
  }
  // Check if property is available
  cov_2jagxutndh().s[14]++;
  if (
  /* istanbul ignore next */
  (cov_2jagxutndh().b[2][0]++, !property.isAvailable) ||
  /* istanbul ignore next */
  (cov_2jagxutndh().b[2][1]++, property.status !== 'active')) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[1][0]++;
    cov_2jagxutndh().s[15]++;
    throw new appError_1.AppError('Property is not available for favoriting', 400);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[1][1]++;
  }
  // Check if already favorited
  const existingFavorite =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[16]++, await PropertyFavorite_1.PropertyFavorite.findOne({
    userId,
    propertyId
  }));
  /* istanbul ignore next */
  cov_2jagxutndh().s[17]++;
  if (existingFavorite) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[3][0]++;
    cov_2jagxutndh().s[18]++;
    throw new appError_1.AppError('Property is already in your favorites', 400);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[3][1]++;
  }
  // Create favorite
  const favorite =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[19]++, new PropertyFavorite_1.PropertyFavorite({
    userId,
    propertyId
  }));
  /* istanbul ignore next */
  cov_2jagxutndh().s[20]++;
  await favorite.save();
  // Update property analytics
  /* istanbul ignore next */
  cov_2jagxutndh().s[21]++;
  property.analytics.favorites += 1;
  /* istanbul ignore next */
  cov_2jagxutndh().s[22]++;
  await property.save();
  /* istanbul ignore next */
  cov_2jagxutndh().s[23]++;
  logger_1.logger.info(`User ${userId} added property ${propertyId} to favorites`);
  /* istanbul ignore next */
  cov_2jagxutndh().s[24]++;
  return apiResponse_1.ApiResponse.success(res, {
    favorite: {
      id: favorite._id,
      propertyId,
      createdAt: favorite.createdAt
    }
  }, 'Property added to favorites successfully', 201);
});
/**
 * Remove property from favorites
 */
/* istanbul ignore next */
cov_2jagxutndh().s[25]++;
exports.removeFromFavorites = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[1]++;
  const {
    propertyId
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[26]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[27]++, req.user?._id);
  // Find and remove favorite
  const favorite =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[28]++, await PropertyFavorite_1.PropertyFavorite.findOneAndDelete({
    userId,
    propertyId
  }));
  /* istanbul ignore next */
  cov_2jagxutndh().s[29]++;
  if (!favorite) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[4][0]++;
    cov_2jagxutndh().s[30]++;
    throw new appError_1.AppError('Property not found in your favorites', 404);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[4][1]++;
  }
  // Update property analytics
  const property =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[31]++, await Property_1.Property.findById(propertyId));
  /* istanbul ignore next */
  cov_2jagxutndh().s[32]++;
  if (
  /* istanbul ignore next */
  (cov_2jagxutndh().b[6][0]++, property) &&
  /* istanbul ignore next */
  (cov_2jagxutndh().b[6][1]++, property.analytics.favorites > 0)) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[5][0]++;
    cov_2jagxutndh().s[33]++;
    property.analytics.favorites -= 1;
    /* istanbul ignore next */
    cov_2jagxutndh().s[34]++;
    await property.save();
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[5][1]++;
  }
  cov_2jagxutndh().s[35]++;
  logger_1.logger.info(`User ${userId} removed property ${propertyId} from favorites`);
  /* istanbul ignore next */
  cov_2jagxutndh().s[36]++;
  return apiResponse_1.ApiResponse.success(res, null, 'Property removed from favorites successfully');
});
/**
 * Get user's favorite properties
 */
/* istanbul ignore next */
cov_2jagxutndh().s[37]++;
exports.getUserFavorites = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[2]++;
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[38]++, req.user?._id);
  const {
    page =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[7][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[8][0]++, 20),
    sortBy =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[9][0]++, 'createdAt'),
    sortOrder =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[10][0]++, 'desc')
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[39]++, req.query);
  // Build sort options
  const sortOptions =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[40]++, {});
  /* istanbul ignore next */
  cov_2jagxutndh().s[41]++;
  sortOptions[sortBy] = sortOrder === 'desc' ?
  /* istanbul ignore next */
  (cov_2jagxutndh().b[11][0]++, -1) :
  /* istanbul ignore next */
  (cov_2jagxutndh().b[11][1]++, 1);
  // Calculate pagination
  const skip =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[42]++, (Number(page) - 1) * Number(limit));
  // Get favorites with property details
  const [favorites, total] =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[43]++, await Promise.all([PropertyFavorite_1.PropertyFavorite.find({
    userId
  }).sort(sortOptions).skip(skip).limit(Number(limit)).populate({
    path: 'propertyId',
    select: 'title description propertyType listingType bedrooms bathrooms location pricing photos status isAvailable analytics',
    populate: {
      path: 'ownerId',
      select: 'firstName lastName email accountType'
    }
  }).lean(), PropertyFavorite_1.PropertyFavorite.countDocuments({
    userId
  })]));
  // Filter out favorites where property no longer exists
  const validFavorites =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[44]++, favorites.filter(fav => {
    /* istanbul ignore next */
    cov_2jagxutndh().f[3]++;
    cov_2jagxutndh().s[45]++;
    return fav.propertyId;
  }));
  // Calculate pagination info
  const totalPages =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[46]++, Math.ceil(total / Number(limit)));
  /* istanbul ignore next */
  cov_2jagxutndh().s[47]++;
  return apiResponse_1.ApiResponse.success(res, {
    favorites: validFavorites,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: totalPages
    },
    summary: {
      totalFavorites: total,
      availableProperties: validFavorites.filter(fav => {
        /* istanbul ignore next */
        cov_2jagxutndh().f[4]++;
        cov_2jagxutndh().s[48]++;
        return /* istanbul ignore next */(cov_2jagxutndh().b[12][0]++, fav.propertyId) &&
        /* istanbul ignore next */
        (cov_2jagxutndh().b[12][1]++, fav.propertyId.isAvailable) &&
        /* istanbul ignore next */
        (cov_2jagxutndh().b[12][2]++, fav.propertyId.status === 'active');
      }).length
    }
  }, 'Favorite properties retrieved successfully');
});
/**
 * Check if property is favorited by user
 */
/* istanbul ignore next */
cov_2jagxutndh().s[49]++;
exports.checkFavoriteStatus = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[5]++;
  const {
    propertyId
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[50]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[51]++, req.user?._id);
  const favorite =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[52]++, await PropertyFavorite_1.PropertyFavorite.findOne({
    userId,
    propertyId
  }));
  /* istanbul ignore next */
  cov_2jagxutndh().s[53]++;
  return apiResponse_1.ApiResponse.success(res, {
    isFavorited: !!favorite,
    favoriteId:
    /* istanbul ignore next */
    (cov_2jagxutndh().b[13][0]++, favorite?._id) ||
    /* istanbul ignore next */
    (cov_2jagxutndh().b[13][1]++, null),
    favoritedAt:
    /* istanbul ignore next */
    (cov_2jagxutndh().b[14][0]++, favorite?.createdAt) ||
    /* istanbul ignore next */
    (cov_2jagxutndh().b[14][1]++, null)
  }, 'Favorite status retrieved successfully');
});
/**
 * Get favorite properties count
 */
/* istanbul ignore next */
cov_2jagxutndh().s[54]++;
exports.getFavoritesCount = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[6]++;
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[55]++, req.user?._id);
  const count =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[56]++, await PropertyFavorite_1.PropertyFavorite.countDocuments({
    userId
  }));
  /* istanbul ignore next */
  cov_2jagxutndh().s[57]++;
  return apiResponse_1.ApiResponse.success(res, {
    count
  }, 'Favorites count retrieved successfully');
});
/**
 * Get popular properties (most favorited)
 */
/* istanbul ignore next */
cov_2jagxutndh().s[58]++;
exports.getPopularProperties = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[7]++;
  const {
    limit =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[15][0]++, 20),
    timeframe =
    /* istanbul ignore next */
    (cov_2jagxutndh().b[16][0]++, 'all'),
    // 'week', 'month', 'all'
    propertyType,
    city,
    state
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[59]++, req.query);
  // Build date filter for timeframe
  let dateFilter =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[60]++, {});
  /* istanbul ignore next */
  cov_2jagxutndh().s[61]++;
  if (timeframe !== 'all') {
    /* istanbul ignore next */
    cov_2jagxutndh().b[17][0]++;
    const now =
    /* istanbul ignore next */
    (cov_2jagxutndh().s[62]++, new Date());
    let startDate =
    /* istanbul ignore next */
    (cov_2jagxutndh().s[63]++, new Date());
    /* istanbul ignore next */
    cov_2jagxutndh().s[64]++;
    if (timeframe === 'week') {
      /* istanbul ignore next */
      cov_2jagxutndh().b[18][0]++;
      cov_2jagxutndh().s[65]++;
      startDate.setDate(now.getDate() - 7);
    } else {
      /* istanbul ignore next */
      cov_2jagxutndh().b[18][1]++;
      cov_2jagxutndh().s[66]++;
      if (timeframe === 'month') {
        /* istanbul ignore next */
        cov_2jagxutndh().b[19][0]++;
        cov_2jagxutndh().s[67]++;
        startDate.setMonth(now.getMonth() - 1);
      } else
      /* istanbul ignore next */
      {
        cov_2jagxutndh().b[19][1]++;
      }
    }
    /* istanbul ignore next */
    cov_2jagxutndh().s[68]++;
    dateFilter = {
      createdAt: {
        $gte: startDate
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[17][1]++;
  }
  // Aggregation pipeline to get most favorited properties
  const pipeline =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[69]++, [
  // Match timeframe
  {
    $match: dateFilter
  },
  // Group by property and count favorites
  {
    $group: {
      _id: '$propertyId',
      favoriteCount: {
        $sum: 1
      },
      latestFavorite: {
        $max: '$createdAt'
      }
    }
  },
  // Sort by favorite count
  {
    $sort: {
      favoriteCount: -1,
      latestFavorite: -1
    }
  },
  // Limit results
  {
    $limit: Number(limit)
  },
  // Lookup property details
  {
    $lookup: {
      from: 'properties',
      localField: '_id',
      foreignField: '_id',
      as: 'property'
    }
  },
  // Unwind property
  {
    $unwind: '$property'
  },
  // Match property filters
  {
    $match: {
      'property.status': 'active',
      'property.isAvailable': true,
      ...(
      /* istanbul ignore next */
      (cov_2jagxutndh().b[20][0]++, propertyType) &&
      /* istanbul ignore next */
      (cov_2jagxutndh().b[20][1]++, {
        'property.propertyType': propertyType
      })),
      ...(
      /* istanbul ignore next */
      (cov_2jagxutndh().b[21][0]++, city) &&
      /* istanbul ignore next */
      (cov_2jagxutndh().b[21][1]++, {
        'property.location.city': new RegExp(city, 'i')
      })),
      ...(
      /* istanbul ignore next */
      (cov_2jagxutndh().b[22][0]++, state) &&
      /* istanbul ignore next */
      (cov_2jagxutndh().b[22][1]++, {
        'property.location.state': state
      }))
    }
  },
  // Lookup owner details
  {
    $lookup: {
      from: 'users',
      localField: 'property.ownerId',
      foreignField: '_id',
      as: 'property.owner',
      pipeline: [{
        $project: {
          firstName: 1,
          lastName: 1,
          email: 1,
          accountType: 1
        }
      }]
    }
  },
  // Unwind owner
  {
    $unwind: '$property.owner'
  },
  // Project final structure
  {
    $project: {
      property: 1,
      favoriteCount: 1,
      latestFavorite: 1
    }
  }]);
  const popularProperties =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[70]++, await PropertyFavorite_1.PropertyFavorite.aggregate(pipeline));
  /* istanbul ignore next */
  cov_2jagxutndh().s[71]++;
  return apiResponse_1.ApiResponse.success(res, {
    properties: popularProperties,
    timeframe,
    count: popularProperties.length,
    filters: {
      propertyType,
      city,
      state
    }
  }, 'Popular properties retrieved successfully');
});
/**
 * Bulk add/remove favorites
 */
/* istanbul ignore next */
cov_2jagxutndh().s[72]++;
exports.bulkUpdateFavorites = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_2jagxutndh().f[8]++;
  const {
    propertyIds,
    action
  } =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[73]++, req.body); // action: 'add' or 'remove'
  const userId =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[74]++, req.user?._id);
  /* istanbul ignore next */
  cov_2jagxutndh().s[75]++;
  if (
  /* istanbul ignore next */
  (cov_2jagxutndh().b[24][0]++, !Array.isArray(propertyIds)) ||
  /* istanbul ignore next */
  (cov_2jagxutndh().b[24][1]++, propertyIds.length === 0)) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[23][0]++;
    cov_2jagxutndh().s[76]++;
    throw new appError_1.AppError('Property IDs array is required', 400);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[23][1]++;
  }
  cov_2jagxutndh().s[77]++;
  if (!['add', 'remove'].includes(action)) {
    /* istanbul ignore next */
    cov_2jagxutndh().b[25][0]++;
    cov_2jagxutndh().s[78]++;
    throw new appError_1.AppError('Action must be either "add" or "remove"', 400);
  } else
  /* istanbul ignore next */
  {
    cov_2jagxutndh().b[25][1]++;
  }
  const results =
  /* istanbul ignore next */
  (cov_2jagxutndh().s[79]++, {
    successful: [],
    failed: []
  });
  /* istanbul ignore next */
  cov_2jagxutndh().s[80]++;
  if (action === 'add') {
    /* istanbul ignore next */
    cov_2jagxutndh().b[26][0]++;
    cov_2jagxutndh().s[81]++;
    // Bulk add to favorites
    for (const propertyId of propertyIds) {
      /* istanbul ignore next */
      cov_2jagxutndh().s[82]++;
      try {
        // Check if property exists and is available
        const property =
        /* istanbul ignore next */
        (cov_2jagxutndh().s[83]++, await Property_1.Property.findById(propertyId));
        /* istanbul ignore next */
        cov_2jagxutndh().s[84]++;
        if (
        /* istanbul ignore next */
        (cov_2jagxutndh().b[28][0]++, !property) ||
        /* istanbul ignore next */
        (cov_2jagxutndh().b[28][1]++, !property.isAvailable) ||
        /* istanbul ignore next */
        (cov_2jagxutndh().b[28][2]++, property.status !== 'active')) {
          /* istanbul ignore next */
          cov_2jagxutndh().b[27][0]++;
          cov_2jagxutndh().s[85]++;
          results.failed.push({
            propertyId,
            reason: 'Property not available'
          });
          /* istanbul ignore next */
          cov_2jagxutndh().s[86]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_2jagxutndh().b[27][1]++;
        }
        // Check if already favorited
        const existingFavorite =
        /* istanbul ignore next */
        (cov_2jagxutndh().s[87]++, await PropertyFavorite_1.PropertyFavorite.findOne({
          userId,
          propertyId
        }));
        /* istanbul ignore next */
        cov_2jagxutndh().s[88]++;
        if (existingFavorite) {
          /* istanbul ignore next */
          cov_2jagxutndh().b[29][0]++;
          cov_2jagxutndh().s[89]++;
          results.failed.push({
            propertyId,
            reason: 'Already favorited'
          });
          /* istanbul ignore next */
          cov_2jagxutndh().s[90]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_2jagxutndh().b[29][1]++;
        }
        // Create favorite
        cov_2jagxutndh().s[91]++;
        await PropertyFavorite_1.PropertyFavorite.create({
          userId,
          propertyId
        });
        // Update analytics
        /* istanbul ignore next */
        cov_2jagxutndh().s[92]++;
        property.analytics.favorites += 1;
        /* istanbul ignore next */
        cov_2jagxutndh().s[93]++;
        await property.save();
        /* istanbul ignore next */
        cov_2jagxutndh().s[94]++;
        results.successful.push(propertyId);
      } catch (error) {
        /* istanbul ignore next */
        cov_2jagxutndh().s[95]++;
        results.failed.push({
          propertyId,
          reason: 'Database error'
        });
      }
    }
  } else {
    /* istanbul ignore next */
    cov_2jagxutndh().b[26][1]++;
    cov_2jagxutndh().s[96]++;
    // Bulk remove from favorites
    for (const propertyId of propertyIds) {
      /* istanbul ignore next */
      cov_2jagxutndh().s[97]++;
      try {
        const favorite =
        /* istanbul ignore next */
        (cov_2jagxutndh().s[98]++, await PropertyFavorite_1.PropertyFavorite.findOneAndDelete({
          userId,
          propertyId
        }));
        /* istanbul ignore next */
        cov_2jagxutndh().s[99]++;
        if (favorite) {
          /* istanbul ignore next */
          cov_2jagxutndh().b[30][0]++;
          // Update analytics
          const property =
          /* istanbul ignore next */
          (cov_2jagxutndh().s[100]++, await Property_1.Property.findById(propertyId));
          /* istanbul ignore next */
          cov_2jagxutndh().s[101]++;
          if (
          /* istanbul ignore next */
          (cov_2jagxutndh().b[32][0]++, property) &&
          /* istanbul ignore next */
          (cov_2jagxutndh().b[32][1]++, property.analytics.favorites > 0)) {
            /* istanbul ignore next */
            cov_2jagxutndh().b[31][0]++;
            cov_2jagxutndh().s[102]++;
            property.analytics.favorites -= 1;
            /* istanbul ignore next */
            cov_2jagxutndh().s[103]++;
            await property.save();
          } else
          /* istanbul ignore next */
          {
            cov_2jagxutndh().b[31][1]++;
          }
          cov_2jagxutndh().s[104]++;
          results.successful.push(propertyId);
        } else {
          /* istanbul ignore next */
          cov_2jagxutndh().b[30][1]++;
          cov_2jagxutndh().s[105]++;
          results.failed.push({
            propertyId,
            reason: 'Not in favorites'
          });
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_2jagxutndh().s[106]++;
        results.failed.push({
          propertyId,
          reason: 'Database error'
        });
      }
    }
  }
  /* istanbul ignore next */
  cov_2jagxutndh().s[107]++;
  logger_1.logger.info(`User ${userId} bulk ${action} favorites: ${results.successful.length} successful, ${results.failed.length} failed`);
  /* istanbul ignore next */
  cov_2jagxutndh().s[108]++;
  return apiResponse_1.ApiResponse.success(res, {
    action,
    results,
    summary: {
      total: propertyIds.length,
      successful: results.successful.length,
      failed: results.failed.length
    }
  }, `Bulk ${action} favorites completed`);
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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