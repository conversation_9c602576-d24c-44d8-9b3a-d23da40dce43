b008489c0f761d6e3532fa26bb12c5c0
"use strict";

/* istanbul ignore next */
function cov_25hlrlnflg() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertySearch.routes.ts";
  var hash = "cedd57b197fbef4f01b948e30884fa8536930a27";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertySearch.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 36
        },
        end: {
          line: 4,
          column: 87
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 36
        },
        end: {
          line: 7,
          column: 86
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 164
        }
      },
      "8": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 174
        }
      },
      "9": {
        start: {
          line: 26,
          column: 0
        },
        end: {
          line: 26,
          column: 78
        }
      },
      "10": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 32,
          column: 84
        }
      },
      "11": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 178
        }
      },
      "12": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 95
        }
      },
      "13": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 145
        }
      },
      "14": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 25
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertySearch.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,wFAQkD;AAClD,6CAAkD;AAClD,yDAA6E;AAC7E,uFAIiD;AAEjD,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,SAAS,EACT,IAAA,4BAAe,EAAC,kDAAsB,EAAE,MAAM,CAAC,EAC/C,4CAAgB,CACjB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,gBAAgB,EAChB,IAAA,4BAAe,EAAC,kDAAsB,EAAE,OAAO,CAAC,EAChD,+CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,8CAAkB,CAAC,CAAC;AAElD;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,gDAAoB,CAAC,CAAC;AAExD;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,cAAc,EACd,mBAAY,EACZ,IAAA,4BAAe,EAAC,4CAAgB,EAAE,MAAM,CAAC,EACzC,sCAAU,CACX,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,eAAe,EACf,mBAAY,EACZ,4CAAgB,CACjB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,mBAAmB,EACnB,mBAAY,EACZ,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,6CAAiB,CAClB,CAAC;AAEF,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertySearch.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  searchProperties,\r\n  getNearbyProperties,\r\n  getPropertyFilters,\r\n  getSearchSuggestions,\r\n  saveSearch,\r\n  getSavedSearches,\r\n  deleteSavedSearch\r\n} from '../controllers/propertySearch.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  searchPropertiesSchema,\r\n  nearbyPropertiesSchema,\r\n  saveSearchSchema\r\n} from '../validators/propertySearch.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   POST /api/properties/search\r\n * @desc    Advanced property search with filters\r\n * @access  Public\r\n */\r\nrouter.post(\r\n  '/search',\r\n  validateRequest(searchPropertiesSchema, 'body'),\r\n  searchProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/nearby\r\n * @desc    Find properties near a location\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/search/nearby',\r\n  validateRequest(nearbyPropertiesSchema, 'query'),\r\n  getNearbyProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/filters\r\n * @desc    Get available search filters and their options\r\n * @access  Public\r\n */\r\nrouter.get('/search/filters', getPropertyFilters);\r\n\r\n/**\r\n * @route   GET /api/properties/search/suggestions\r\n * @desc    Get search suggestions based on query\r\n * @access  Public\r\n */\r\nrouter.get('/search/suggestions', getSearchSuggestions);\r\n\r\n/**\r\n * @route   POST /api/properties/search/save\r\n * @desc    Save a search query for later\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/search/save',\r\n  authenticate,\r\n  validateRequest(saveSearchSchema, 'body'),\r\n  saveSearch\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/saved\r\n * @desc    Get user's saved searches\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/search/saved',\r\n  authenticate,\r\n  getSavedSearches\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/properties/search/saved/:id\r\n * @desc    Delete a saved search\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/search/saved/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  deleteSavedSearch\r\n);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cedd57b197fbef4f01b948e30884fa8536930a27"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25hlrlnflg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25hlrlnflg();
cov_25hlrlnflg().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_25hlrlnflg().s[1]++, require("express"));
const propertySearch_controller_1 =
/* istanbul ignore next */
(cov_25hlrlnflg().s[2]++, require("../controllers/propertySearch.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_25hlrlnflg().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_25hlrlnflg().s[4]++, require("../middleware/validation"));
const propertySearch_validators_1 =
/* istanbul ignore next */
(cov_25hlrlnflg().s[5]++, require("../validators/propertySearch.validators"));
const router =
/* istanbul ignore next */
(cov_25hlrlnflg().s[6]++, (0, express_1.Router)());
/**
 * @route   POST /api/properties/search
 * @desc    Advanced property search with filters
 * @access  Public
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[7]++;
router.post('/search', (0, validation_1.validateRequest)(propertySearch_validators_1.searchPropertiesSchema, 'body'), propertySearch_controller_1.searchProperties);
/**
 * @route   GET /api/properties/search/nearby
 * @desc    Find properties near a location
 * @access  Public
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[8]++;
router.get('/search/nearby', (0, validation_1.validateRequest)(propertySearch_validators_1.nearbyPropertiesSchema, 'query'), propertySearch_controller_1.getNearbyProperties);
/**
 * @route   GET /api/properties/search/filters
 * @desc    Get available search filters and their options
 * @access  Public
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[9]++;
router.get('/search/filters', propertySearch_controller_1.getPropertyFilters);
/**
 * @route   GET /api/properties/search/suggestions
 * @desc    Get search suggestions based on query
 * @access  Public
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[10]++;
router.get('/search/suggestions', propertySearch_controller_1.getSearchSuggestions);
/**
 * @route   POST /api/properties/search/save
 * @desc    Save a search query for later
 * @access  Private
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[11]++;
router.post('/search/save', auth_1.authenticate, (0, validation_1.validateRequest)(propertySearch_validators_1.saveSearchSchema, 'body'), propertySearch_controller_1.saveSearch);
/**
 * @route   GET /api/properties/search/saved
 * @desc    Get user's saved searches
 * @access  Private
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[12]++;
router.get('/search/saved', auth_1.authenticate, propertySearch_controller_1.getSavedSearches);
/**
 * @route   DELETE /api/properties/search/saved/:id
 * @desc    Delete a saved search
 * @access  Private
 */
/* istanbul ignore next */
cov_25hlrlnflg().s[13]++;
router.delete('/search/saved/:id', auth_1.authenticate, (0, validation_1.validateObjectId)('id'), propertySearch_controller_1.deleteSavedSearch);
/* istanbul ignore next */
cov_25hlrlnflg().s[14]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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