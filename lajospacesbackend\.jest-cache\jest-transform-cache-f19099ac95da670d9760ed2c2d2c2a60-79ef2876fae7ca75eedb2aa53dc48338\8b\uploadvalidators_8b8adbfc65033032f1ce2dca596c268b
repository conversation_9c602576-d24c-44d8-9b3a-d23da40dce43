2cfebd4e0d1765ca80c869743080c404
"use strict";

/* istanbul ignore next */
function cov_ki68bzx06() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\upload.validators.ts";
  var hash = "f2f71b40848aebc2bd167bb1af1da6700a5da335";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\upload.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 299
        }
      },
      "4": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 45
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 54,
          column: 3
        }
      },
      "6": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 65,
          column: 3
        }
      },
      "7": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 96,
          column: 3
        }
      },
      "8": {
        start: {
          line: 100,
          column: 0
        },
        end: {
          line: 122,
          column: 3
        }
      },
      "9": {
        start: {
          line: 126,
          column: 0
        },
        end: {
          line: 158,
          column: 3
        }
      },
      "10": {
        start: {
          line: 162,
          column: 0
        },
        end: {
          line: 207,
          column: 3
        }
      },
      "11": {
        start: {
          line: 211,
          column: 0
        },
        end: {
          line: 241,
          column: 3
        }
      },
      "12": {
        start: {
          line: 245,
          column: 0
        },
        end: {
          line: 308,
          column: 3
        }
      },
      "13": {
        start: {
          line: 312,
          column: 0
        },
        end: {
          line: 341,
          column: 3
        }
      },
      "14": {
        start: {
          line: 342,
          column: 0
        },
        end: {
          line: 352,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\upload.validators.ts",
      mappings: ";;;;;;AAAA,8CAAsB;AAEtB;;GAEG;AACU,QAAA,iBAAiB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC1C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;QACxD,YAAY,EAAE,0CAA0C;KACzD,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE;SACd,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACzC,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,yBAAyB;QACtC,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,iCAAiC;KAChD,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4BAA4B;QAC1C,YAAY,EAAE,2BAA2B;KAC1C,CAAC;IAEJ,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,gCAAgC;QAC9C,YAAY,EAAE,iCAAiC;KAChD,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,iCAAiC;QAC/C,YAAY,EAAE,kCAAkC;KACjD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC3C,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE;SACrB,OAAO,CAAC,IAAI,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,mCAAmC;KACpD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,0BAA0B,GAAG,aAAG,CAAC,MAAM,CAAC;IACnD,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE;SACrB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,4BAA4B;KACpD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,OAAO,EAAE;SACvB,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,qCAAqC;KACtD,CAAC;IAEJ,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE;SACpB,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC;SACvF,OAAO,CAAC,OAAO,CAAC;SAChB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,oBAAoB;KACjC,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;KACzD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,6BAA6B,GAAG,aAAG,CAAC,MAAM,CAAC;IACtD,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE;SACzB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,gCAAgC;QACvD,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;SAC5C,OAAO,CAAC,OAAO,CAAC;SAChB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,sBAAsB;KACnC,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,uCAAuC;KACtD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,aAAG,CAAC,MAAM,CAAC;IACzC,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,OAAO,CAAC,iBAAiB,CAAC;SAC1B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;QACxD,YAAY,EAAE,0CAA0C;KACzD,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE;SACd,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACzC,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,yBAAyB;QACtC,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,iCAAiC;KAChD,CAAC;IAEJ,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE;SAC1B,OAAO,CAAC,IAAI,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,wCAAwC;KACzD,CAAC;IAEJ,kBAAkB,EAAE,aAAG,CAAC,OAAO,EAAE;SAC9B,OAAO,CAAC,IAAI,CAAC;SACb,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,4CAA4C;KAC7D,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;QACxD,YAAY,EAAE,0CAA0C;KACzD,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE;SACd,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACzC,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,yBAAyB;QACtC,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,iCAAiC;KAChD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc;SACxB,GAAG,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,eAAe;SACrC,OAAO,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,eAAe;SACzC,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,wCAAwC;QACtD,YAAY,EAAE,sCAAsC;KACrD,CAAC;IAEJ,cAAc,EAAE,aAAG,CAAC,KAAK,EAAE;SACxB,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACnF,GAAG,CAAC,CAAC,CAAC;SACN,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACvC,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,qCAAqC;QAClD,UAAU,EAAE,qBAAqB;KAClC,CAAC;IAEJ,cAAc,EAAE,aAAG,CAAC,MAAM,CAAC;QACzB,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QAC/C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QAChD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC,QAAQ,EAAE;QAC5J,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;QACrH,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE;KAC3F,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC3C,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,gDAAgD;QAC9D,YAAY,EAAE,gDAAgD;QAC9D,cAAc,EAAE,+BAA+B;KAChD,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,OAAO,CAAC,uEAAuE,CAAC;SAChF,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,0BAA0B;QACjD,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,YAAY;SACnC,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,mCAAmC;QACjD,YAAY,EAAE,+BAA+B;QAC7C,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,IAAI,EAAE;SACN,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,gCAAgC;QAC9C,YAAY,EAAE,iCAAiC;KAChD,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,iCAAiC;QAC/C,YAAY,EAAE,kCAAkC;KACjD,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,OAAO,CAAC,EAAE,CAAC;SACX,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4BAA4B;QAC1C,YAAY,EAAE,2BAA2B;KAC1C,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;SACpC,OAAO,CAAC,MAAM,CAAC;SACf,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,sBAAsB;KACnC,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;SACtD,OAAO,CAAC,OAAO,CAAC;SAChB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,mBAAmB;KAChC,CAAC;IAEJ,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE;SACrB,OAAO,CAAC,oCAAoC,CAAC;SAC7C,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,sCAAsC;KAC9D,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,GAAG,CAAC,GAAG,CAAC;SACR,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,2BAA2B;QACzC,YAAY,EAAE,yBAAyB;KACxC,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE;SACnB,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IAEb,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE;SACrB,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IAEb,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE;SAC1B,OAAO,CAAC,IAAI,CAAC;SACb,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;IAEb,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE;SACpB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,OAAO,CAAC,qBAAqB,CAAC;SAC9B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,wFAAwF;KAChH,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE;SACd,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACzC,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;IAEb,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE;SACrB,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IAEb,cAAc,EAAE,aAAG,CAAC,KAAK,EAAE;SACxB,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC;SACnB,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,oCAAoC;KAClD,CAAC;CACL,CAAC,CAAC;AAEH,kBAAe;IACb,iBAAiB,EAAjB,yBAAiB;IACjB,kBAAkB,EAAlB,0BAAkB;IAClB,0BAA0B,EAA1B,kCAA0B;IAC1B,6BAA6B,EAA7B,qCAA6B;IAC7B,gBAAgB,EAAhB,wBAAgB;IAChB,uBAAuB,EAAvB,+BAAuB;IACvB,kBAAkB,EAAlB,0BAAkB;IAClB,uBAAuB,EAAvB,+BAAuB;IACvB,uBAAuB,EAAvB,+BAAuB;CACxB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\upload.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n/**\r\n * Upload single image validation schema\r\n */\r\nexport const uploadImageSchema = Joi.object({\r\n  folder: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Folder name must be at least 1 character',\r\n      'string.max': 'Folder name cannot exceed 100 characters'\r\n    }),\r\n  \r\n  tags: Joi.array()\r\n    .items(Joi.string().trim().min(1).max(50))\r\n    .max(10)\r\n    .optional()\r\n    .messages({\r\n      'array.max': 'Maximum 10 tags allowed',\r\n      'string.min': 'Tag must be at least 1 character',\r\n      'string.max': 'Tag cannot exceed 50 characters'\r\n    }),\r\n  \r\n  quality: Joi.number()\r\n    .min(1)\r\n    .max(100)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Quality must be at least 1',\r\n      'number.max': 'Quality cannot exceed 100'\r\n    }),\r\n  \r\n  width: Joi.number()\r\n    .min(1)\r\n    .max(4000)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Width must be at least 1 pixel',\r\n      'number.max': 'Width cannot exceed 4000 pixels'\r\n    }),\r\n  \r\n  height: Joi.number()\r\n    .min(1)\r\n    .max(4000)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Height must be at least 1 pixel',\r\n      'number.max': 'Height cannot exceed 4000 pixels'\r\n    })\r\n});\r\n\r\n/**\r\n * Upload avatar validation schema\r\n */\r\nexport const uploadAvatarSchema = Joi.object({\r\n  isPrimary: Joi.boolean()\r\n    .default(true)\r\n    .optional()\r\n    .messages({\r\n      'boolean.base': 'isPrimary must be a boolean value'\r\n    })\r\n});\r\n\r\n/**\r\n * Upload property photos validation schema\r\n */\r\nexport const uploadPropertyPhotosSchema = Joi.object({\r\n  propertyId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid property ID format'\r\n    }),\r\n  \r\n  isMainPhoto: Joi.boolean()\r\n    .default(false)\r\n    .optional()\r\n    .messages({\r\n      'boolean.base': 'isMainPhoto must be a boolean value'\r\n    }),\r\n  \r\n  photoType: Joi.string()\r\n    .valid('exterior', 'interior', 'kitchen', 'bathroom', 'bedroom', 'living_room', 'other')\r\n    .default('other')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid photo type'\r\n    }),\r\n  \r\n  description: Joi.string()\r\n    .trim()\r\n    .max(500)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Description cannot exceed 500 characters'\r\n    })\r\n});\r\n\r\n/**\r\n * Upload message attachment validation schema\r\n */\r\nexport const uploadMessageAttachmentSchema = Joi.object({\r\n  conversationId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .required()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid conversation ID format',\r\n      'any.required': 'Conversation ID is required'\r\n    }),\r\n  \r\n  messageType: Joi.string()\r\n    .valid('image', 'document', 'video', 'audio')\r\n    .default('image')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid message type'\r\n    }),\r\n  \r\n  caption: Joi.string()\r\n    .trim()\r\n    .max(1000)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Caption cannot exceed 1000 characters'\r\n    })\r\n});\r\n\r\n/**\r\n * Bulk upload validation schema\r\n */\r\nexport const bulkUploadSchema = Joi.object({\r\n  folder: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .default('lajospaces/bulk')\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Folder name must be at least 1 character',\r\n      'string.max': 'Folder name cannot exceed 100 characters'\r\n    }),\r\n  \r\n  tags: Joi.array()\r\n    .items(Joi.string().trim().min(1).max(50))\r\n    .max(10)\r\n    .optional()\r\n    .messages({\r\n      'array.max': 'Maximum 10 tags allowed',\r\n      'string.min': 'Tag must be at least 1 character',\r\n      'string.max': 'Tag cannot exceed 50 characters'\r\n    }),\r\n  \r\n  optimizeForWeb: Joi.boolean()\r\n    .default(true)\r\n    .optional()\r\n    .messages({\r\n      'boolean.base': 'optimizeForWeb must be a boolean value'\r\n    }),\r\n  \r\n  generateThumbnails: Joi.boolean()\r\n    .default(true)\r\n    .optional()\r\n    .messages({\r\n      'boolean.base': 'generateThumbnails must be a boolean value'\r\n    })\r\n});\r\n\r\n/**\r\n * Generate upload URL validation schema\r\n */\r\nexport const generateUploadUrlSchema = Joi.object({\r\n  folder: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .default('lajospaces/direct')\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Folder name must be at least 1 character',\r\n      'string.max': 'Folder name cannot exceed 100 characters'\r\n    }),\r\n  \r\n  tags: Joi.array()\r\n    .items(Joi.string().trim().min(1).max(50))\r\n    .max(10)\r\n    .optional()\r\n    .messages({\r\n      'array.max': 'Maximum 10 tags allowed',\r\n      'string.min': 'Tag must be at least 1 character',\r\n      'string.max': 'Tag cannot exceed 50 characters'\r\n    }),\r\n  \r\n  maxFileSize: Joi.number()\r\n    .min(1024) // 1KB minimum\r\n    .max(50 * 1024 * 1024) // 50MB maximum\r\n    .default(10 * 1024 * 1024) // 10MB default\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Maximum file size must be at least 1KB',\r\n      'number.max': 'Maximum file size cannot exceed 50MB'\r\n    }),\r\n  \r\n  allowedFormats: Joi.array()\r\n    .items(Joi.string().valid('jpg', 'jpeg', 'png', 'webp', 'gif', 'pdf', 'mp4', 'mov'))\r\n    .min(1)\r\n    .default(['jpg', 'jpeg', 'png', 'webp'])\r\n    .optional()\r\n    .messages({\r\n      'array.min': 'At least one format must be allowed',\r\n      'any.only': 'Invalid file format'\r\n    }),\r\n  \r\n  transformation: Joi.object({\r\n    width: Joi.number().min(1).max(4000).optional(),\r\n    height: Joi.number().min(1).max(4000).optional(),\r\n    crop: Joi.string().valid('scale', 'fit', 'limit', 'mfit', 'fill', 'lfill', 'pad', 'lpad', 'mpad', 'crop', 'thumb', 'imagga_crop', 'imagga_scale').optional(),\r\n    quality: Joi.string().valid('auto', 'auto:best', 'auto:good', 'auto:eco', 'auto:low').default('auto:good').optional(),\r\n    format: Joi.string().valid('auto', 'jpg', 'png', 'webp', 'gif').default('auto').optional()\r\n  }).optional()\r\n});\r\n\r\n/**\r\n * File metadata validation schema\r\n */\r\nexport const fileMetadataSchema = Joi.object({\r\n  originalName: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(255)\r\n    .required()\r\n    .messages({\r\n      'string.min': 'Original filename must be at least 1 character',\r\n      'string.max': 'Original filename cannot exceed 255 characters',\r\n      'any.required': 'Original filename is required'\r\n    }),\r\n  \r\n  mimeType: Joi.string()\r\n    .pattern(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\\-\\^_]*\\/[a-zA-Z0-9][a-zA-Z0-9!#$&\\-\\^_.]*$/)\r\n    .required()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid MIME type format',\r\n      'any.required': 'MIME type is required'\r\n    }),\r\n  \r\n  size: Joi.number()\r\n    .min(1)\r\n    .max(100 * 1024 * 1024) // 100MB max\r\n    .required()\r\n    .messages({\r\n      'number.min': 'File size must be at least 1 byte',\r\n      'number.max': 'File size cannot exceed 100MB',\r\n      'any.required': 'File size is required'\r\n    }),\r\n  \r\n  encoding: Joi.string()\r\n    .trim()\r\n    .optional()\r\n});\r\n\r\n/**\r\n * Image optimization options validation schema\r\n */\r\nexport const imageOptimizationSchema = Joi.object({\r\n  width: Joi.number()\r\n    .min(1)\r\n    .max(4000)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Width must be at least 1 pixel',\r\n      'number.max': 'Width cannot exceed 4000 pixels'\r\n    }),\r\n  \r\n  height: Joi.number()\r\n    .min(1)\r\n    .max(4000)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Height must be at least 1 pixel',\r\n      'number.max': 'Height cannot exceed 4000 pixels'\r\n    }),\r\n  \r\n  quality: Joi.number()\r\n    .min(1)\r\n    .max(100)\r\n    .default(85)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Quality must be at least 1',\r\n      'number.max': 'Quality cannot exceed 100'\r\n    }),\r\n  \r\n  format: Joi.string()\r\n    .valid('jpeg', 'png', 'webp', 'auto')\r\n    .default('auto')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid image format'\r\n    }),\r\n  \r\n  crop: Joi.string()\r\n    .valid('cover', 'contain', 'fill', 'inside', 'outside')\r\n    .default('cover')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid crop mode'\r\n    }),\r\n  \r\n  background: Joi.string()\r\n    .pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Background must be a valid hex color'\r\n    }),\r\n  \r\n  blur: Joi.number()\r\n    .min(0.3)\r\n    .max(1000)\r\n    .optional()\r\n    .messages({\r\n      'number.min': 'Blur must be at least 0.3',\r\n      'number.max': 'Blur cannot exceed 1000'\r\n    }),\r\n  \r\n  sharpen: Joi.boolean()\r\n    .default(false)\r\n    .optional(),\r\n  \r\n  grayscale: Joi.boolean()\r\n    .default(false)\r\n    .optional(),\r\n  \r\n  removeMetadata: Joi.boolean()\r\n    .default(true)\r\n    .optional()\r\n});\r\n\r\n/**\r\n * Cloudinary upload options validation schema\r\n */\r\nexport const cloudinaryOptionsSchema = Joi.object({\r\n  folder: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .optional(),\r\n  \r\n  public_id: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .pattern(/^[a-zA-Z0-9_\\-\\/]+$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Public ID can only contain letters, numbers, underscores, hyphens, and forward slashes'\r\n    }),\r\n  \r\n  tags: Joi.array()\r\n    .items(Joi.string().trim().min(1).max(50))\r\n    .max(10)\r\n    .optional(),\r\n  \r\n  overwrite: Joi.boolean()\r\n    .default(false)\r\n    .optional(),\r\n  \r\n  transformation: Joi.array()\r\n    .items(Joi.object())\r\n    .max(10)\r\n    .optional()\r\n    .messages({\r\n      'array.max': 'Maximum 10 transformations allowed'\r\n    })\r\n});\r\n\r\nexport default {\r\n  uploadImageSchema,\r\n  uploadAvatarSchema,\r\n  uploadPropertyPhotosSchema,\r\n  uploadMessageAttachmentSchema,\r\n  bulkUploadSchema,\r\n  generateUploadUrlSchema,\r\n  fileMetadataSchema,\r\n  imageOptimizationSchema,\r\n  cloudinaryOptionsSchema\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f2f71b40848aebc2bd167bb1af1da6700a5da335"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ki68bzx06 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ki68bzx06();
var __importDefault =
/* istanbul ignore next */
(cov_ki68bzx06().s[0]++,
/* istanbul ignore next */
(cov_ki68bzx06().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_ki68bzx06().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_ki68bzx06().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_ki68bzx06().f[0]++;
  cov_ki68bzx06().s[1]++;
  return /* istanbul ignore next */(cov_ki68bzx06().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_ki68bzx06().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_ki68bzx06().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_ki68bzx06().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_ki68bzx06().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_ki68bzx06().s[3]++;
exports.cloudinaryOptionsSchema = exports.imageOptimizationSchema = exports.fileMetadataSchema = exports.generateUploadUrlSchema = exports.bulkUploadSchema = exports.uploadMessageAttachmentSchema = exports.uploadPropertyPhotosSchema = exports.uploadAvatarSchema = exports.uploadImageSchema = void 0;
const joi_1 =
/* istanbul ignore next */
(cov_ki68bzx06().s[4]++, __importDefault(require("joi")));
/**
 * Upload single image validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[5]++;
exports.uploadImageSchema = joi_1.default.object({
  folder: joi_1.default.string().trim().min(1).max(100).optional().messages({
    'string.min': 'Folder name must be at least 1 character',
    'string.max': 'Folder name cannot exceed 100 characters'
  }),
  tags: joi_1.default.array().items(joi_1.default.string().trim().min(1).max(50)).max(10).optional().messages({
    'array.max': 'Maximum 10 tags allowed',
    'string.min': 'Tag must be at least 1 character',
    'string.max': 'Tag cannot exceed 50 characters'
  }),
  quality: joi_1.default.number().min(1).max(100).optional().messages({
    'number.min': 'Quality must be at least 1',
    'number.max': 'Quality cannot exceed 100'
  }),
  width: joi_1.default.number().min(1).max(4000).optional().messages({
    'number.min': 'Width must be at least 1 pixel',
    'number.max': 'Width cannot exceed 4000 pixels'
  }),
  height: joi_1.default.number().min(1).max(4000).optional().messages({
    'number.min': 'Height must be at least 1 pixel',
    'number.max': 'Height cannot exceed 4000 pixels'
  })
});
/**
 * Upload avatar validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[6]++;
exports.uploadAvatarSchema = joi_1.default.object({
  isPrimary: joi_1.default.boolean().default(true).optional().messages({
    'boolean.base': 'isPrimary must be a boolean value'
  })
});
/**
 * Upload property photos validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[7]++;
exports.uploadPropertyPhotosSchema = joi_1.default.object({
  propertyId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid property ID format'
  }),
  isMainPhoto: joi_1.default.boolean().default(false).optional().messages({
    'boolean.base': 'isMainPhoto must be a boolean value'
  }),
  photoType: joi_1.default.string().valid('exterior', 'interior', 'kitchen', 'bathroom', 'bedroom', 'living_room', 'other').default('other').optional().messages({
    'any.only': 'Invalid photo type'
  }),
  description: joi_1.default.string().trim().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters'
  })
});
/**
 * Upload message attachment validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[8]++;
exports.uploadMessageAttachmentSchema = joi_1.default.object({
  conversationId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': 'Invalid conversation ID format',
    'any.required': 'Conversation ID is required'
  }),
  messageType: joi_1.default.string().valid('image', 'document', 'video', 'audio').default('image').optional().messages({
    'any.only': 'Invalid message type'
  }),
  caption: joi_1.default.string().trim().max(1000).optional().messages({
    'string.max': 'Caption cannot exceed 1000 characters'
  })
});
/**
 * Bulk upload validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[9]++;
exports.bulkUploadSchema = joi_1.default.object({
  folder: joi_1.default.string().trim().min(1).max(100).default('lajospaces/bulk').optional().messages({
    'string.min': 'Folder name must be at least 1 character',
    'string.max': 'Folder name cannot exceed 100 characters'
  }),
  tags: joi_1.default.array().items(joi_1.default.string().trim().min(1).max(50)).max(10).optional().messages({
    'array.max': 'Maximum 10 tags allowed',
    'string.min': 'Tag must be at least 1 character',
    'string.max': 'Tag cannot exceed 50 characters'
  }),
  optimizeForWeb: joi_1.default.boolean().default(true).optional().messages({
    'boolean.base': 'optimizeForWeb must be a boolean value'
  }),
  generateThumbnails: joi_1.default.boolean().default(true).optional().messages({
    'boolean.base': 'generateThumbnails must be a boolean value'
  })
});
/**
 * Generate upload URL validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[10]++;
exports.generateUploadUrlSchema = joi_1.default.object({
  folder: joi_1.default.string().trim().min(1).max(100).default('lajospaces/direct').optional().messages({
    'string.min': 'Folder name must be at least 1 character',
    'string.max': 'Folder name cannot exceed 100 characters'
  }),
  tags: joi_1.default.array().items(joi_1.default.string().trim().min(1).max(50)).max(10).optional().messages({
    'array.max': 'Maximum 10 tags allowed',
    'string.min': 'Tag must be at least 1 character',
    'string.max': 'Tag cannot exceed 50 characters'
  }),
  maxFileSize: joi_1.default.number().min(1024) // 1KB minimum
  .max(50 * 1024 * 1024) // 50MB maximum
  .default(10 * 1024 * 1024) // 10MB default
  .optional().messages({
    'number.min': 'Maximum file size must be at least 1KB',
    'number.max': 'Maximum file size cannot exceed 50MB'
  }),
  allowedFormats: joi_1.default.array().items(joi_1.default.string().valid('jpg', 'jpeg', 'png', 'webp', 'gif', 'pdf', 'mp4', 'mov')).min(1).default(['jpg', 'jpeg', 'png', 'webp']).optional().messages({
    'array.min': 'At least one format must be allowed',
    'any.only': 'Invalid file format'
  }),
  transformation: joi_1.default.object({
    width: joi_1.default.number().min(1).max(4000).optional(),
    height: joi_1.default.number().min(1).max(4000).optional(),
    crop: joi_1.default.string().valid('scale', 'fit', 'limit', 'mfit', 'fill', 'lfill', 'pad', 'lpad', 'mpad', 'crop', 'thumb', 'imagga_crop', 'imagga_scale').optional(),
    quality: joi_1.default.string().valid('auto', 'auto:best', 'auto:good', 'auto:eco', 'auto:low').default('auto:good').optional(),
    format: joi_1.default.string().valid('auto', 'jpg', 'png', 'webp', 'gif').default('auto').optional()
  }).optional()
});
/**
 * File metadata validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[11]++;
exports.fileMetadataSchema = joi_1.default.object({
  originalName: joi_1.default.string().trim().min(1).max(255).required().messages({
    'string.min': 'Original filename must be at least 1 character',
    'string.max': 'Original filename cannot exceed 255 characters',
    'any.required': 'Original filename is required'
  }),
  mimeType: joi_1.default.string().pattern(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/).required().messages({
    'string.pattern.base': 'Invalid MIME type format',
    'any.required': 'MIME type is required'
  }),
  size: joi_1.default.number().min(1).max(100 * 1024 * 1024) // 100MB max
  .required().messages({
    'number.min': 'File size must be at least 1 byte',
    'number.max': 'File size cannot exceed 100MB',
    'any.required': 'File size is required'
  }),
  encoding: joi_1.default.string().trim().optional()
});
/**
 * Image optimization options validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[12]++;
exports.imageOptimizationSchema = joi_1.default.object({
  width: joi_1.default.number().min(1).max(4000).optional().messages({
    'number.min': 'Width must be at least 1 pixel',
    'number.max': 'Width cannot exceed 4000 pixels'
  }),
  height: joi_1.default.number().min(1).max(4000).optional().messages({
    'number.min': 'Height must be at least 1 pixel',
    'number.max': 'Height cannot exceed 4000 pixels'
  }),
  quality: joi_1.default.number().min(1).max(100).default(85).optional().messages({
    'number.min': 'Quality must be at least 1',
    'number.max': 'Quality cannot exceed 100'
  }),
  format: joi_1.default.string().valid('jpeg', 'png', 'webp', 'auto').default('auto').optional().messages({
    'any.only': 'Invalid image format'
  }),
  crop: joi_1.default.string().valid('cover', 'contain', 'fill', 'inside', 'outside').default('cover').optional().messages({
    'any.only': 'Invalid crop mode'
  }),
  background: joi_1.default.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).optional().messages({
    'string.pattern.base': 'Background must be a valid hex color'
  }),
  blur: joi_1.default.number().min(0.3).max(1000).optional().messages({
    'number.min': 'Blur must be at least 0.3',
    'number.max': 'Blur cannot exceed 1000'
  }),
  sharpen: joi_1.default.boolean().default(false).optional(),
  grayscale: joi_1.default.boolean().default(false).optional(),
  removeMetadata: joi_1.default.boolean().default(true).optional()
});
/**
 * Cloudinary upload options validation schema
 */
/* istanbul ignore next */
cov_ki68bzx06().s[13]++;
exports.cloudinaryOptionsSchema = joi_1.default.object({
  folder: joi_1.default.string().trim().min(1).max(100).optional(),
  public_id: joi_1.default.string().trim().min(1).max(100).pattern(/^[a-zA-Z0-9_\-\/]+$/).optional().messages({
    'string.pattern.base': 'Public ID can only contain letters, numbers, underscores, hyphens, and forward slashes'
  }),
  tags: joi_1.default.array().items(joi_1.default.string().trim().min(1).max(50)).max(10).optional(),
  overwrite: joi_1.default.boolean().default(false).optional(),
  transformation: joi_1.default.array().items(joi_1.default.object()).max(10).optional().messages({
    'array.max': 'Maximum 10 transformations allowed'
  })
});
/* istanbul ignore next */
cov_ki68bzx06().s[14]++;
exports.default = {
  uploadImageSchema: exports.uploadImageSchema,
  uploadAvatarSchema: exports.uploadAvatarSchema,
  uploadPropertyPhotosSchema: exports.uploadPropertyPhotosSchema,
  uploadMessageAttachmentSchema: exports.uploadMessageAttachmentSchema,
  bulkUploadSchema: exports.bulkUploadSchema,
  generateUploadUrlSchema: exports.generateUploadUrlSchema,
  fileMetadataSchema: exports.fileMetadataSchema,
  imageOptimizationSchema: exports.imageOptimizationSchema,
  cloudinaryOptionsSchema: exports.cloudinaryOptionsSchema
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3Zfa2k2OGJ6eDA2IiwiYWN0dWFsQ292ZXJhZ2UiLCJqb2lfMSIsInMiLCJfX2ltcG9ydERlZmF1bHQiLCJyZXF1aXJlIiwiZXhwb3J0cyIsInVwbG9hZEltYWdlU2NoZW1hIiwiZGVmYXVsdCIsIm9iamVjdCIsImZvbGRlciIsInN0cmluZyIsInRyaW0iLCJtaW4iLCJtYXgiLCJvcHRpb25hbCIsIm1lc3NhZ2VzIiwidGFncyIsImFycmF5IiwiaXRlbXMiLCJxdWFsaXR5IiwibnVtYmVyIiwid2lkdGgiLCJoZWlnaHQiLCJ1cGxvYWRBdmF0YXJTY2hlbWEiLCJpc1ByaW1hcnkiLCJib29sZWFuIiwidXBsb2FkUHJvcGVydHlQaG90b3NTY2hlbWEiLCJwcm9wZXJ0eUlkIiwicGF0dGVybiIsImlzTWFpblBob3RvIiwicGhvdG9UeXBlIiwidmFsaWQiLCJkZXNjcmlwdGlvbiIsInVwbG9hZE1lc3NhZ2VBdHRhY2htZW50U2NoZW1hIiwiY29udmVyc2F0aW9uSWQiLCJyZXF1aXJlZCIsIm1lc3NhZ2VUeXBlIiwiY2FwdGlvbiIsImJ1bGtVcGxvYWRTY2hlbWEiLCJvcHRpbWl6ZUZvcldlYiIsImdlbmVyYXRlVGh1bWJuYWlscyIsImdlbmVyYXRlVXBsb2FkVXJsU2NoZW1hIiwibWF4RmlsZVNpemUiLCJhbGxvd2VkRm9ybWF0cyIsInRyYW5zZm9ybWF0aW9uIiwiY3JvcCIsImZvcm1hdCIsImZpbGVNZXRhZGF0YVNjaGVtYSIsIm9yaWdpbmFsTmFtZSIsIm1pbWVUeXBlIiwic2l6ZSIsImVuY29kaW5nIiwiaW1hZ2VPcHRpbWl6YXRpb25TY2hlbWEiLCJiYWNrZ3JvdW5kIiwiYmx1ciIsInNoYXJwZW4iLCJncmF5c2NhbGUiLCJyZW1vdmVNZXRhZGF0YSIsImNsb3VkaW5hcnlPcHRpb25zU2NoZW1hIiwicHVibGljX2lkIiwib3ZlcndyaXRlIl0sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNWSBQQ1xcRGVza3RvcFxcbGFqb3NwYWNlc1xcbGFqb3NwYWNlc2JhY2tlbmRcXHNyY1xcdmFsaWRhdG9yc1xcdXBsb2FkLnZhbGlkYXRvcnMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEpvaSBmcm9tICdqb2knO1xyXG5cclxuLyoqXHJcbiAqIFVwbG9hZCBzaW5nbGUgaW1hZ2UgdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCB1cGxvYWRJbWFnZVNjaGVtYSA9IEpvaS5vYmplY3Qoe1xyXG4gIGZvbGRlcjogSm9pLnN0cmluZygpXHJcbiAgICAudHJpbSgpXHJcbiAgICAubWluKDEpXHJcbiAgICAubWF4KDEwMClcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLm1pbic6ICdGb2xkZXIgbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDEgY2hhcmFjdGVyJyxcclxuICAgICAgJ3N0cmluZy5tYXgnOiAnRm9sZGVyIG5hbWUgY2Fubm90IGV4Y2VlZCAxMDAgY2hhcmFjdGVycydcclxuICAgIH0pLFxyXG4gIFxyXG4gIHRhZ3M6IEpvaS5hcnJheSgpXHJcbiAgICAuaXRlbXMoSm9pLnN0cmluZygpLnRyaW0oKS5taW4oMSkubWF4KDUwKSlcclxuICAgIC5tYXgoMTApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FycmF5Lm1heCc6ICdNYXhpbXVtIDEwIHRhZ3MgYWxsb3dlZCcsXHJcbiAgICAgICdzdHJpbmcubWluJzogJ1RhZyBtdXN0IGJlIGF0IGxlYXN0IDEgY2hhcmFjdGVyJyxcclxuICAgICAgJ3N0cmluZy5tYXgnOiAnVGFnIGNhbm5vdCBleGNlZWQgNTAgY2hhcmFjdGVycydcclxuICAgIH0pLFxyXG4gIFxyXG4gIHF1YWxpdHk6IEpvaS5udW1iZXIoKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCgxMDApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ251bWJlci5taW4nOiAnUXVhbGl0eSBtdXN0IGJlIGF0IGxlYXN0IDEnLFxyXG4gICAgICAnbnVtYmVyLm1heCc6ICdRdWFsaXR5IGNhbm5vdCBleGNlZWQgMTAwJ1xyXG4gICAgfSksXHJcbiAgXHJcbiAgd2lkdGg6IEpvaS5udW1iZXIoKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCg0MDAwKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdudW1iZXIubWluJzogJ1dpZHRoIG11c3QgYmUgYXQgbGVhc3QgMSBwaXhlbCcsXHJcbiAgICAgICdudW1iZXIubWF4JzogJ1dpZHRoIGNhbm5vdCBleGNlZWQgNDAwMCBwaXhlbHMnXHJcbiAgICB9KSxcclxuICBcclxuICBoZWlnaHQ6IEpvaS5udW1iZXIoKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCg0MDAwKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdudW1iZXIubWluJzogJ0hlaWdodCBtdXN0IGJlIGF0IGxlYXN0IDEgcGl4ZWwnLFxyXG4gICAgICAnbnVtYmVyLm1heCc6ICdIZWlnaHQgY2Fubm90IGV4Y2VlZCA0MDAwIHBpeGVscydcclxuICAgIH0pXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIFVwbG9hZCBhdmF0YXIgdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCB1cGxvYWRBdmF0YXJTY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBpc1ByaW1hcnk6IEpvaS5ib29sZWFuKClcclxuICAgIC5kZWZhdWx0KHRydWUpXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2Jvb2xlYW4uYmFzZSc6ICdpc1ByaW1hcnkgbXVzdCBiZSBhIGJvb2xlYW4gdmFsdWUnXHJcbiAgICB9KVxyXG59KTtcclxuXHJcbi8qKlxyXG4gKiBVcGxvYWQgcHJvcGVydHkgcGhvdG9zIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgdXBsb2FkUHJvcGVydHlQaG90b3NTY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBwcm9wZXJ0eUlkOiBKb2kuc3RyaW5nKClcclxuICAgIC5wYXR0ZXJuKC9eWzAtOWEtZkEtRl17MjR9JC8pXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ3N0cmluZy5wYXR0ZXJuLmJhc2UnOiAnSW52YWxpZCBwcm9wZXJ0eSBJRCBmb3JtYXQnXHJcbiAgICB9KSxcclxuICBcclxuICBpc01haW5QaG90bzogSm9pLmJvb2xlYW4oKVxyXG4gICAgLmRlZmF1bHQoZmFsc2UpXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2Jvb2xlYW4uYmFzZSc6ICdpc01haW5QaG90byBtdXN0IGJlIGEgYm9vbGVhbiB2YWx1ZSdcclxuICAgIH0pLFxyXG4gIFxyXG4gIHBob3RvVHlwZTogSm9pLnN0cmluZygpXHJcbiAgICAudmFsaWQoJ2V4dGVyaW9yJywgJ2ludGVyaW9yJywgJ2tpdGNoZW4nLCAnYmF0aHJvb20nLCAnYmVkcm9vbScsICdsaXZpbmdfcm9vbScsICdvdGhlcicpXHJcbiAgICAuZGVmYXVsdCgnb3RoZXInKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhbnkub25seSc6ICdJbnZhbGlkIHBob3RvIHR5cGUnXHJcbiAgICB9KSxcclxuICBcclxuICBkZXNjcmlwdGlvbjogSm9pLnN0cmluZygpXHJcbiAgICAudHJpbSgpXHJcbiAgICAubWF4KDUwMClcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLm1heCc6ICdEZXNjcmlwdGlvbiBjYW5ub3QgZXhjZWVkIDUwMCBjaGFyYWN0ZXJzJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogVXBsb2FkIG1lc3NhZ2UgYXR0YWNobWVudCB2YWxpZGF0aW9uIHNjaGVtYVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IHVwbG9hZE1lc3NhZ2VBdHRhY2htZW50U2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgY29udmVyc2F0aW9uSWQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnBhdHRlcm4oL15bMC05YS1mQS1GXXsyNH0kLylcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLnBhdHRlcm4uYmFzZSc6ICdJbnZhbGlkIGNvbnZlcnNhdGlvbiBJRCBmb3JtYXQnLFxyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ0NvbnZlcnNhdGlvbiBJRCBpcyByZXF1aXJlZCdcclxuICAgIH0pLFxyXG4gIFxyXG4gIG1lc3NhZ2VUeXBlOiBKb2kuc3RyaW5nKClcclxuICAgIC52YWxpZCgnaW1hZ2UnLCAnZG9jdW1lbnQnLCAndmlkZW8nLCAnYXVkaW8nKVxyXG4gICAgLmRlZmF1bHQoJ2ltYWdlJylcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYW55Lm9ubHknOiAnSW52YWxpZCBtZXNzYWdlIHR5cGUnXHJcbiAgICB9KSxcclxuICBcclxuICBjYXB0aW9uOiBKb2kuc3RyaW5nKClcclxuICAgIC50cmltKClcclxuICAgIC5tYXgoMTAwMClcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLm1heCc6ICdDYXB0aW9uIGNhbm5vdCBleGNlZWQgMTAwMCBjaGFyYWN0ZXJzJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogQnVsayB1cGxvYWQgdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCBidWxrVXBsb2FkU2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgZm9sZGVyOiBKb2kuc3RyaW5nKClcclxuICAgIC50cmltKClcclxuICAgIC5taW4oMSlcclxuICAgIC5tYXgoMTAwKVxyXG4gICAgLmRlZmF1bHQoJ2xham9zcGFjZXMvYnVsaycpXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ3N0cmluZy5taW4nOiAnRm9sZGVyIG5hbWUgbXVzdCBiZSBhdCBsZWFzdCAxIGNoYXJhY3RlcicsXHJcbiAgICAgICdzdHJpbmcubWF4JzogJ0ZvbGRlciBuYW1lIGNhbm5vdCBleGNlZWQgMTAwIGNoYXJhY3RlcnMnXHJcbiAgICB9KSxcclxuICBcclxuICB0YWdzOiBKb2kuYXJyYXkoKVxyXG4gICAgLml0ZW1zKEpvaS5zdHJpbmcoKS50cmltKCkubWluKDEpLm1heCg1MCkpXHJcbiAgICAubWF4KDEwKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhcnJheS5tYXgnOiAnTWF4aW11bSAxMCB0YWdzIGFsbG93ZWQnLFxyXG4gICAgICAnc3RyaW5nLm1pbic6ICdUYWcgbXVzdCBiZSBhdCBsZWFzdCAxIGNoYXJhY3RlcicsXHJcbiAgICAgICdzdHJpbmcubWF4JzogJ1RhZyBjYW5ub3QgZXhjZWVkIDUwIGNoYXJhY3RlcnMnXHJcbiAgICB9KSxcclxuICBcclxuICBvcHRpbWl6ZUZvcldlYjogSm9pLmJvb2xlYW4oKVxyXG4gICAgLmRlZmF1bHQodHJ1ZSlcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYm9vbGVhbi5iYXNlJzogJ29wdGltaXplRm9yV2ViIG11c3QgYmUgYSBib29sZWFuIHZhbHVlJ1xyXG4gICAgfSksXHJcbiAgXHJcbiAgZ2VuZXJhdGVUaHVtYm5haWxzOiBKb2kuYm9vbGVhbigpXHJcbiAgICAuZGVmYXVsdCh0cnVlKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdib29sZWFuLmJhc2UnOiAnZ2VuZXJhdGVUaHVtYm5haWxzIG11c3QgYmUgYSBib29sZWFuIHZhbHVlJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogR2VuZXJhdGUgdXBsb2FkIFVSTCB2YWxpZGF0aW9uIHNjaGVtYVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGdlbmVyYXRlVXBsb2FkVXJsU2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgZm9sZGVyOiBKb2kuc3RyaW5nKClcclxuICAgIC50cmltKClcclxuICAgIC5taW4oMSlcclxuICAgIC5tYXgoMTAwKVxyXG4gICAgLmRlZmF1bHQoJ2xham9zcGFjZXMvZGlyZWN0JylcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLm1pbic6ICdGb2xkZXIgbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDEgY2hhcmFjdGVyJyxcclxuICAgICAgJ3N0cmluZy5tYXgnOiAnRm9sZGVyIG5hbWUgY2Fubm90IGV4Y2VlZCAxMDAgY2hhcmFjdGVycydcclxuICAgIH0pLFxyXG4gIFxyXG4gIHRhZ3M6IEpvaS5hcnJheSgpXHJcbiAgICAuaXRlbXMoSm9pLnN0cmluZygpLnRyaW0oKS5taW4oMSkubWF4KDUwKSlcclxuICAgIC5tYXgoMTApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FycmF5Lm1heCc6ICdNYXhpbXVtIDEwIHRhZ3MgYWxsb3dlZCcsXHJcbiAgICAgICdzdHJpbmcubWluJzogJ1RhZyBtdXN0IGJlIGF0IGxlYXN0IDEgY2hhcmFjdGVyJyxcclxuICAgICAgJ3N0cmluZy5tYXgnOiAnVGFnIGNhbm5vdCBleGNlZWQgNTAgY2hhcmFjdGVycydcclxuICAgIH0pLFxyXG4gIFxyXG4gIG1heEZpbGVTaXplOiBKb2kubnVtYmVyKClcclxuICAgIC5taW4oMTAyNCkgLy8gMUtCIG1pbmltdW1cclxuICAgIC5tYXgoNTAgKiAxMDI0ICogMTAyNCkgLy8gNTBNQiBtYXhpbXVtXHJcbiAgICAuZGVmYXVsdCgxMCAqIDEwMjQgKiAxMDI0KSAvLyAxME1CIGRlZmF1bHRcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnbnVtYmVyLm1pbic6ICdNYXhpbXVtIGZpbGUgc2l6ZSBtdXN0IGJlIGF0IGxlYXN0IDFLQicsXHJcbiAgICAgICdudW1iZXIubWF4JzogJ01heGltdW0gZmlsZSBzaXplIGNhbm5vdCBleGNlZWQgNTBNQidcclxuICAgIH0pLFxyXG4gIFxyXG4gIGFsbG93ZWRGb3JtYXRzOiBKb2kuYXJyYXkoKVxyXG4gICAgLml0ZW1zKEpvaS5zdHJpbmcoKS52YWxpZCgnanBnJywgJ2pwZWcnLCAncG5nJywgJ3dlYnAnLCAnZ2lmJywgJ3BkZicsICdtcDQnLCAnbW92JykpXHJcbiAgICAubWluKDEpXHJcbiAgICAuZGVmYXVsdChbJ2pwZycsICdqcGVnJywgJ3BuZycsICd3ZWJwJ10pXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FycmF5Lm1pbic6ICdBdCBsZWFzdCBvbmUgZm9ybWF0IG11c3QgYmUgYWxsb3dlZCcsXHJcbiAgICAgICdhbnkub25seSc6ICdJbnZhbGlkIGZpbGUgZm9ybWF0J1xyXG4gICAgfSksXHJcbiAgXHJcbiAgdHJhbnNmb3JtYXRpb246IEpvaS5vYmplY3Qoe1xyXG4gICAgd2lkdGg6IEpvaS5udW1iZXIoKS5taW4oMSkubWF4KDQwMDApLm9wdGlvbmFsKCksXHJcbiAgICBoZWlnaHQ6IEpvaS5udW1iZXIoKS5taW4oMSkubWF4KDQwMDApLm9wdGlvbmFsKCksXHJcbiAgICBjcm9wOiBKb2kuc3RyaW5nKCkudmFsaWQoJ3NjYWxlJywgJ2ZpdCcsICdsaW1pdCcsICdtZml0JywgJ2ZpbGwnLCAnbGZpbGwnLCAncGFkJywgJ2xwYWQnLCAnbXBhZCcsICdjcm9wJywgJ3RodW1iJywgJ2ltYWdnYV9jcm9wJywgJ2ltYWdnYV9zY2FsZScpLm9wdGlvbmFsKCksXHJcbiAgICBxdWFsaXR5OiBKb2kuc3RyaW5nKCkudmFsaWQoJ2F1dG8nLCAnYXV0bzpiZXN0JywgJ2F1dG86Z29vZCcsICdhdXRvOmVjbycsICdhdXRvOmxvdycpLmRlZmF1bHQoJ2F1dG86Z29vZCcpLm9wdGlvbmFsKCksXHJcbiAgICBmb3JtYXQ6IEpvaS5zdHJpbmcoKS52YWxpZCgnYXV0bycsICdqcGcnLCAncG5nJywgJ3dlYnAnLCAnZ2lmJykuZGVmYXVsdCgnYXV0bycpLm9wdGlvbmFsKClcclxuICB9KS5vcHRpb25hbCgpXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIEZpbGUgbWV0YWRhdGEgdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCBmaWxlTWV0YWRhdGFTY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBvcmlnaW5hbE5hbWU6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnRyaW0oKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCgyNTUpXHJcbiAgICAucmVxdWlyZWQoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ3N0cmluZy5taW4nOiAnT3JpZ2luYWwgZmlsZW5hbWUgbXVzdCBiZSBhdCBsZWFzdCAxIGNoYXJhY3RlcicsXHJcbiAgICAgICdzdHJpbmcubWF4JzogJ09yaWdpbmFsIGZpbGVuYW1lIGNhbm5vdCBleGNlZWQgMjU1IGNoYXJhY3RlcnMnLFxyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ09yaWdpbmFsIGZpbGVuYW1lIGlzIHJlcXVpcmVkJ1xyXG4gICAgfSksXHJcbiAgXHJcbiAgbWltZVR5cGU6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnBhdHRlcm4oL15bYS16QS1aMC05XVthLXpBLVowLTkhIyQmXFwtXFxeX10qXFwvW2EtekEtWjAtOV1bYS16QS1aMC05ISMkJlxcLVxcXl8uXSokLylcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLnBhdHRlcm4uYmFzZSc6ICdJbnZhbGlkIE1JTUUgdHlwZSBmb3JtYXQnLFxyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ01JTUUgdHlwZSBpcyByZXF1aXJlZCdcclxuICAgIH0pLFxyXG4gIFxyXG4gIHNpemU6IEpvaS5udW1iZXIoKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCgxMDAgKiAxMDI0ICogMTAyNCkgLy8gMTAwTUIgbWF4XHJcbiAgICAucmVxdWlyZWQoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ251bWJlci5taW4nOiAnRmlsZSBzaXplIG11c3QgYmUgYXQgbGVhc3QgMSBieXRlJyxcclxuICAgICAgJ251bWJlci5tYXgnOiAnRmlsZSBzaXplIGNhbm5vdCBleGNlZWQgMTAwTUInLFxyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ0ZpbGUgc2l6ZSBpcyByZXF1aXJlZCdcclxuICAgIH0pLFxyXG4gIFxyXG4gIGVuY29kaW5nOiBKb2kuc3RyaW5nKClcclxuICAgIC50cmltKClcclxuICAgIC5vcHRpb25hbCgpXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIEltYWdlIG9wdGltaXphdGlvbiBvcHRpb25zIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgaW1hZ2VPcHRpbWl6YXRpb25TY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICB3aWR0aDogSm9pLm51bWJlcigpXHJcbiAgICAubWluKDEpXHJcbiAgICAubWF4KDQwMDApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ251bWJlci5taW4nOiAnV2lkdGggbXVzdCBiZSBhdCBsZWFzdCAxIHBpeGVsJyxcclxuICAgICAgJ251bWJlci5tYXgnOiAnV2lkdGggY2Fubm90IGV4Y2VlZCA0MDAwIHBpeGVscydcclxuICAgIH0pLFxyXG4gIFxyXG4gIGhlaWdodDogSm9pLm51bWJlcigpXHJcbiAgICAubWluKDEpXHJcbiAgICAubWF4KDQwMDApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ251bWJlci5taW4nOiAnSGVpZ2h0IG11c3QgYmUgYXQgbGVhc3QgMSBwaXhlbCcsXHJcbiAgICAgICdudW1iZXIubWF4JzogJ0hlaWdodCBjYW5ub3QgZXhjZWVkIDQwMDAgcGl4ZWxzJ1xyXG4gICAgfSksXHJcbiAgXHJcbiAgcXVhbGl0eTogSm9pLm51bWJlcigpXHJcbiAgICAubWluKDEpXHJcbiAgICAubWF4KDEwMClcclxuICAgIC5kZWZhdWx0KDg1KVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdudW1iZXIubWluJzogJ1F1YWxpdHkgbXVzdCBiZSBhdCBsZWFzdCAxJyxcclxuICAgICAgJ251bWJlci5tYXgnOiAnUXVhbGl0eSBjYW5ub3QgZXhjZWVkIDEwMCdcclxuICAgIH0pLFxyXG4gIFxyXG4gIGZvcm1hdDogSm9pLnN0cmluZygpXHJcbiAgICAudmFsaWQoJ2pwZWcnLCAncG5nJywgJ3dlYnAnLCAnYXV0bycpXHJcbiAgICAuZGVmYXVsdCgnYXV0bycpXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FueS5vbmx5JzogJ0ludmFsaWQgaW1hZ2UgZm9ybWF0J1xyXG4gICAgfSksXHJcbiAgXHJcbiAgY3JvcDogSm9pLnN0cmluZygpXHJcbiAgICAudmFsaWQoJ2NvdmVyJywgJ2NvbnRhaW4nLCAnZmlsbCcsICdpbnNpZGUnLCAnb3V0c2lkZScpXHJcbiAgICAuZGVmYXVsdCgnY292ZXInKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhbnkub25seSc6ICdJbnZhbGlkIGNyb3AgbW9kZSdcclxuICAgIH0pLFxyXG4gIFxyXG4gIGJhY2tncm91bmQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnBhdHRlcm4oL14jKFtBLUZhLWYwLTldezZ9fFtBLUZhLWYwLTldezN9KSQvKVxyXG4gICAgLm9wdGlvbmFsKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdzdHJpbmcucGF0dGVybi5iYXNlJzogJ0JhY2tncm91bmQgbXVzdCBiZSBhIHZhbGlkIGhleCBjb2xvcidcclxuICAgIH0pLFxyXG4gIFxyXG4gIGJsdXI6IEpvaS5udW1iZXIoKVxyXG4gICAgLm1pbigwLjMpXHJcbiAgICAubWF4KDEwMDApXHJcbiAgICAub3B0aW9uYWwoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ251bWJlci5taW4nOiAnQmx1ciBtdXN0IGJlIGF0IGxlYXN0IDAuMycsXHJcbiAgICAgICdudW1iZXIubWF4JzogJ0JsdXIgY2Fubm90IGV4Y2VlZCAxMDAwJ1xyXG4gICAgfSksXHJcbiAgXHJcbiAgc2hhcnBlbjogSm9pLmJvb2xlYW4oKVxyXG4gICAgLmRlZmF1bHQoZmFsc2UpXHJcbiAgICAub3B0aW9uYWwoKSxcclxuICBcclxuICBncmF5c2NhbGU6IEpvaS5ib29sZWFuKClcclxuICAgIC5kZWZhdWx0KGZhbHNlKVxyXG4gICAgLm9wdGlvbmFsKCksXHJcbiAgXHJcbiAgcmVtb3ZlTWV0YWRhdGE6IEpvaS5ib29sZWFuKClcclxuICAgIC5kZWZhdWx0KHRydWUpXHJcbiAgICAub3B0aW9uYWwoKVxyXG59KTtcclxuXHJcbi8qKlxyXG4gKiBDbG91ZGluYXJ5IHVwbG9hZCBvcHRpb25zIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgY2xvdWRpbmFyeU9wdGlvbnNTY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBmb2xkZXI6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnRyaW0oKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCgxMDApXHJcbiAgICAub3B0aW9uYWwoKSxcclxuICBcclxuICBwdWJsaWNfaWQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnRyaW0oKVxyXG4gICAgLm1pbigxKVxyXG4gICAgLm1heCgxMDApXHJcbiAgICAucGF0dGVybigvXlthLXpBLVowLTlfXFwtXFwvXSskLylcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnc3RyaW5nLnBhdHRlcm4uYmFzZSc6ICdQdWJsaWMgSUQgY2FuIG9ubHkgY29udGFpbiBsZXR0ZXJzLCBudW1iZXJzLCB1bmRlcnNjb3JlcywgaHlwaGVucywgYW5kIGZvcndhcmQgc2xhc2hlcydcclxuICAgIH0pLFxyXG4gIFxyXG4gIHRhZ3M6IEpvaS5hcnJheSgpXHJcbiAgICAuaXRlbXMoSm9pLnN0cmluZygpLnRyaW0oKS5taW4oMSkubWF4KDUwKSlcclxuICAgIC5tYXgoMTApXHJcbiAgICAub3B0aW9uYWwoKSxcclxuICBcclxuICBvdmVyd3JpdGU6IEpvaS5ib29sZWFuKClcclxuICAgIC5kZWZhdWx0KGZhbHNlKVxyXG4gICAgLm9wdGlvbmFsKCksXHJcbiAgXHJcbiAgdHJhbnNmb3JtYXRpb246IEpvaS5hcnJheSgpXHJcbiAgICAuaXRlbXMoSm9pLm9iamVjdCgpKVxyXG4gICAgLm1heCgxMClcclxuICAgIC5vcHRpb25hbCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYXJyYXkubWF4JzogJ01heGltdW0gMTAgdHJhbnNmb3JtYXRpb25zIGFsbG93ZWQnXHJcbiAgICB9KVxyXG59KTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IHtcclxuICB1cGxvYWRJbWFnZVNjaGVtYSxcclxuICB1cGxvYWRBdmF0YXJTY2hlbWEsXHJcbiAgdXBsb2FkUHJvcGVydHlQaG90b3NTY2hlbWEsXHJcbiAgdXBsb2FkTWVzc2FnZUF0dGFjaG1lbnRTY2hlbWEsXHJcbiAgYnVsa1VwbG9hZFNjaGVtYSxcclxuICBnZW5lcmF0ZVVwbG9hZFVybFNjaGVtYSxcclxuICBmaWxlTWV0YWRhdGFTY2hlbWEsXHJcbiAgaW1hZ2VPcHRpbWl6YXRpb25TY2hlbWEsXHJcbiAgY2xvdWRpbmFyeU9wdGlvbnNTY2hlbWFcclxufTtcclxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBVUs7SUFBQUEsYUFBQSxZQUFBQSxDQUFBO01BQUEsT0FBQUMsY0FBQTtJQUFBO0VBQUE7RUFBQSxPQUFBQSxjQUFBO0FBQUE7QUFBQUQsYUFBQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVZMLE1BQUFFLEtBQUE7QUFBQTtBQUFBLENBQUFGLGFBQUEsR0FBQUcsQ0FBQSxPQUFBQyxlQUFBLENBQUFDLE9BQUE7QUFFQTs7O0FBQUE7QUFBQUwsYUFBQSxHQUFBRyxDQUFBO0FBR2FHLE9BQUEsQ0FBQUMsaUJBQWlCLEdBQUdMLEtBQUEsQ0FBQU0sT0FBRyxDQUFDQyxNQUFNLENBQUM7RUFDMUNDLE1BQU0sRUFBRVIsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUNqQkMsSUFBSSxFQUFFLENBQ05DLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FDTkMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUNSQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFLDBDQUEwQztJQUN4RCxZQUFZLEVBQUU7R0FDZixDQUFDO0VBRUpDLElBQUksRUFBRWYsS0FBQSxDQUFBTSxPQUFHLENBQUNVLEtBQUssRUFBRSxDQUNkQyxLQUFLLENBQUNqQixLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQUNDLElBQUksRUFBRSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUN6Q0EsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsV0FBVyxFQUFFLHlCQUF5QjtJQUN0QyxZQUFZLEVBQUUsa0NBQWtDO0lBQ2hELFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSkksT0FBTyxFQUFFbEIsS0FBQSxDQUFBTSxPQUFHLENBQUNhLE1BQU0sRUFBRSxDQUNsQlIsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQ1JDLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixZQUFZLEVBQUUsNEJBQTRCO0lBQzFDLFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSk0sS0FBSyxFQUFFcEIsS0FBQSxDQUFBTSxPQUFHLENBQUNhLE1BQU0sRUFBRSxDQUNoQlIsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQ1RDLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixZQUFZLEVBQUUsZ0NBQWdDO0lBQzlDLFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSk8sTUFBTSxFQUFFckIsS0FBQSxDQUFBTSxPQUFHLENBQUNhLE1BQU0sRUFBRSxDQUNqQlIsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQ1RDLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixZQUFZLEVBQUUsaUNBQWlDO0lBQy9DLFlBQVksRUFBRTtHQUNmO0NBQ0osQ0FBQztBQUVGOzs7QUFBQTtBQUFBaEIsYUFBQSxHQUFBRyxDQUFBO0FBR2FHLE9BQUEsQ0FBQWtCLGtCQUFrQixHQUFHdEIsS0FBQSxDQUFBTSxPQUFHLENBQUNDLE1BQU0sQ0FBQztFQUMzQ2dCLFNBQVMsRUFBRXZCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDa0IsT0FBTyxFQUFFLENBQ3JCbEIsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUNiTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsY0FBYyxFQUFFO0dBQ2pCO0NBQ0osQ0FBQztBQUVGOzs7QUFBQTtBQUFBaEIsYUFBQSxHQUFBRyxDQUFBO0FBR2FHLE9BQUEsQ0FBQXFCLDBCQUEwQixHQUFHekIsS0FBQSxDQUFBTSxPQUFHLENBQUNDLE1BQU0sQ0FBQztFQUNuRG1CLFVBQVUsRUFBRTFCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDckJrQixPQUFPLENBQUMsbUJBQW1CLENBQUMsQ0FDNUJkLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixxQkFBcUIsRUFBRTtHQUN4QixDQUFDO0VBRUpjLFdBQVcsRUFBRTVCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDa0IsT0FBTyxFQUFFLENBQ3ZCbEIsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUNkTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsY0FBYyxFQUFFO0dBQ2pCLENBQUM7RUFFSmUsU0FBUyxFQUFFN0IsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUNwQnFCLEtBQUssQ0FBQyxVQUFVLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsU0FBUyxFQUFFLGFBQWEsRUFBRSxPQUFPLENBQUMsQ0FDdkZ4QixPQUFPLENBQUMsT0FBTyxDQUFDLENBQ2hCTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsVUFBVSxFQUFFO0dBQ2IsQ0FBQztFQUVKaUIsV0FBVyxFQUFFL0IsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUN0QkMsSUFBSSxFQUFFLENBQ05FLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUkMsUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFlBQVksRUFBRTtHQUNmO0NBQ0osQ0FBQztBQUVGOzs7QUFBQTtBQUFBaEIsYUFBQSxHQUFBRyxDQUFBO0FBR2FHLE9BQUEsQ0FBQTRCLDZCQUE2QixHQUFHaEMsS0FBQSxDQUFBTSxPQUFHLENBQUNDLE1BQU0sQ0FBQztFQUN0RDBCLGNBQWMsRUFBRWpDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDekJrQixPQUFPLENBQUMsbUJBQW1CLENBQUMsQ0FDNUJPLFFBQVEsRUFBRSxDQUNWcEIsUUFBUSxDQUFDO0lBQ1IscUJBQXFCLEVBQUUsZ0NBQWdDO0lBQ3ZELGNBQWMsRUFBRTtHQUNqQixDQUFDO0VBRUpxQixXQUFXLEVBQUVuQyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQ3RCcUIsS0FBSyxDQUFDLE9BQU8sRUFBRSxVQUFVLEVBQUUsT0FBTyxFQUFFLE9BQU8sQ0FBQyxDQUM1Q3hCLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FDaEJPLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixVQUFVLEVBQUU7R0FDYixDQUFDO0VBRUpzQixPQUFPLEVBQUVwQyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQ2xCQyxJQUFJLEVBQUUsQ0FDTkUsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUNUQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFO0dBQ2Y7Q0FDSixDQUFDO0FBRUY7OztBQUFBO0FBQUFoQixhQUFBLEdBQUFHLENBQUE7QUFHYUcsT0FBQSxDQUFBaUMsZ0JBQWdCLEdBQUdyQyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0MsTUFBTSxDQUFDO0VBQ3pDQyxNQUFNLEVBQUVSLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDakJDLElBQUksRUFBRSxDQUNOQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUk4sT0FBTyxDQUFDLGlCQUFpQixDQUFDLENBQzFCTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFLDBDQUEwQztJQUN4RCxZQUFZLEVBQUU7R0FDZixDQUFDO0VBRUpDLElBQUksRUFBRWYsS0FBQSxDQUFBTSxPQUFHLENBQUNVLEtBQUssRUFBRSxDQUNkQyxLQUFLLENBQUNqQixLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQUNDLElBQUksRUFBRSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUN6Q0EsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsV0FBVyxFQUFFLHlCQUF5QjtJQUN0QyxZQUFZLEVBQUUsa0NBQWtDO0lBQ2hELFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSndCLGNBQWMsRUFBRXRDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDa0IsT0FBTyxFQUFFLENBQzFCbEIsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUNiTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsY0FBYyxFQUFFO0dBQ2pCLENBQUM7RUFFSnlCLGtCQUFrQixFQUFFdkMsS0FBQSxDQUFBTSxPQUFHLENBQUNrQixPQUFPLEVBQUUsQ0FDOUJsQixPQUFPLENBQUMsSUFBSSxDQUFDLENBQ2JPLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixjQUFjLEVBQUU7R0FDakI7Q0FDSixDQUFDO0FBRUY7OztBQUFBO0FBQUFoQixhQUFBLEdBQUFHLENBQUE7QUFHYUcsT0FBQSxDQUFBb0MsdUJBQXVCLEdBQUd4QyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0MsTUFBTSxDQUFDO0VBQ2hEQyxNQUFNLEVBQUVSLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDakJDLElBQUksRUFBRSxDQUNOQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUk4sT0FBTyxDQUFDLG1CQUFtQixDQUFDLENBQzVCTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFLDBDQUEwQztJQUN4RCxZQUFZLEVBQUU7R0FDZixDQUFDO0VBRUpDLElBQUksRUFBRWYsS0FBQSxDQUFBTSxPQUFHLENBQUNVLEtBQUssRUFBRSxDQUNkQyxLQUFLLENBQUNqQixLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQUNDLElBQUksRUFBRSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUN6Q0EsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsV0FBVyxFQUFFLHlCQUF5QjtJQUN0QyxZQUFZLEVBQUUsa0NBQWtDO0lBQ2hELFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSjJCLFdBQVcsRUFBRXpDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDYSxNQUFNLEVBQUUsQ0FDdEJSLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQztFQUFBLENBQ1ZDLEdBQUcsQ0FBQyxFQUFFLEdBQUcsSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDO0VBQUEsQ0FDdEJOLE9BQU8sQ0FBQyxFQUFFLEdBQUcsSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDO0VBQUEsQ0FDMUJPLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixZQUFZLEVBQUUsd0NBQXdDO0lBQ3RELFlBQVksRUFBRTtHQUNmLENBQUM7RUFFSjRCLGNBQWMsRUFBRTFDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDVSxLQUFLLEVBQUUsQ0FDeEJDLEtBQUssQ0FBQ2pCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FBQ3FCLEtBQUssQ0FBQyxLQUFLLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUMsQ0FDbkZuQixHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05MLE9BQU8sQ0FBQyxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQ3ZDTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsV0FBVyxFQUFFLHFDQUFxQztJQUNsRCxVQUFVLEVBQUU7R0FDYixDQUFDO0VBRUo2QixjQUFjLEVBQUUzQyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0MsTUFBTSxDQUFDO0lBQ3pCYSxLQUFLLEVBQUVwQixLQUFBLENBQUFNLE9BQUcsQ0FBQ2EsTUFBTSxFQUFFLENBQUNSLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDQyxRQUFRLEVBQUU7SUFDL0NRLE1BQU0sRUFBRXJCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDYSxNQUFNLEVBQUUsQ0FBQ1IsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUNDLFFBQVEsRUFBRTtJQUNoRCtCLElBQUksRUFBRTVDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FBQ3FCLEtBQUssQ0FBQyxPQUFPLEVBQUUsS0FBSyxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLGFBQWEsRUFBRSxjQUFjLENBQUMsQ0FBQ2pCLFFBQVEsRUFBRTtJQUM1SkssT0FBTyxFQUFFbEIsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUFDcUIsS0FBSyxDQUFDLE1BQU0sRUFBRSxXQUFXLEVBQUUsV0FBVyxFQUFFLFVBQVUsRUFBRSxVQUFVLENBQUMsQ0FBQ3hCLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQ08sUUFBUSxFQUFFO0lBQ3JIZ0MsTUFBTSxFQUFFN0MsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUFDcUIsS0FBSyxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLE1BQU0sRUFBRSxLQUFLLENBQUMsQ0FBQ3hCLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQ08sUUFBUTtHQUN6RixDQUFDLENBQUNBLFFBQVE7Q0FDWixDQUFDO0FBRUY7OztBQUFBO0FBQUFmLGFBQUEsR0FBQUcsQ0FBQTtBQUdhRyxPQUFBLENBQUEwQyxrQkFBa0IsR0FBRzlDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDQyxNQUFNLENBQUM7RUFDM0N3QyxZQUFZLEVBQUUvQyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQ3ZCQyxJQUFJLEVBQUUsQ0FDTkMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQ1JzQixRQUFRLEVBQUUsQ0FDVnBCLFFBQVEsQ0FBQztJQUNSLFlBQVksRUFBRSxnREFBZ0Q7SUFDOUQsWUFBWSxFQUFFLGdEQUFnRDtJQUM5RCxjQUFjLEVBQUU7R0FDakIsQ0FBQztFQUVKa0MsUUFBUSxFQUFFaEQsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUNuQmtCLE9BQU8sQ0FBQyx1RUFBdUUsQ0FBQyxDQUNoRk8sUUFBUSxFQUFFLENBQ1ZwQixRQUFRLENBQUM7SUFDUixxQkFBcUIsRUFBRSwwQkFBMEI7SUFDakQsY0FBYyxFQUFFO0dBQ2pCLENBQUM7RUFFSm1DLElBQUksRUFBRWpELEtBQUEsQ0FBQU0sT0FBRyxDQUFDYSxNQUFNLEVBQUUsQ0FDZlIsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsR0FBRyxHQUFHLElBQUksR0FBRyxJQUFJLENBQUMsQ0FBQztFQUFBLENBQ3ZCc0IsUUFBUSxFQUFFLENBQ1ZwQixRQUFRLENBQUM7SUFDUixZQUFZLEVBQUUsbUNBQW1DO0lBQ2pELFlBQVksRUFBRSwrQkFBK0I7SUFDN0MsY0FBYyxFQUFFO0dBQ2pCLENBQUM7RUFFSm9DLFFBQVEsRUFBRWxELEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDbkJDLElBQUksRUFBRSxDQUNORyxRQUFRO0NBQ1osQ0FBQztBQUVGOzs7QUFBQTtBQUFBZixhQUFBLEdBQUFHLENBQUE7QUFHYUcsT0FBQSxDQUFBK0MsdUJBQXVCLEdBQUduRCxLQUFBLENBQUFNLE9BQUcsQ0FBQ0MsTUFBTSxDQUFDO0VBQ2hEYSxLQUFLLEVBQUVwQixLQUFBLENBQUFNLE9BQUcsQ0FBQ2EsTUFBTSxFQUFFLENBQ2hCUixHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FDVEMsUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFlBQVksRUFBRSxnQ0FBZ0M7SUFDOUMsWUFBWSxFQUFFO0dBQ2YsQ0FBQztFQUVKTyxNQUFNLEVBQUVyQixLQUFBLENBQUFNLE9BQUcsQ0FBQ2EsTUFBTSxFQUFFLENBQ2pCUixHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FDVEMsUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFlBQVksRUFBRSxpQ0FBaUM7SUFDL0MsWUFBWSxFQUFFO0dBQ2YsQ0FBQztFQUVKSSxPQUFPLEVBQUVsQixLQUFBLENBQUFNLE9BQUcsQ0FBQ2EsTUFBTSxFQUFFLENBQ2xCUixHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUk4sT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUNYTyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFLDRCQUE0QjtJQUMxQyxZQUFZLEVBQUU7R0FDZixDQUFDO0VBRUorQixNQUFNLEVBQUU3QyxLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQ2pCcUIsS0FBSyxDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUNwQ3hCLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FDZk8sUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFVBQVUsRUFBRTtHQUNiLENBQUM7RUFFSjhCLElBQUksRUFBRTVDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDZnFCLEtBQUssQ0FBQyxPQUFPLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsU0FBUyxDQUFDLENBQ3REeEIsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUNoQk8sUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFVBQVUsRUFBRTtHQUNiLENBQUM7RUFFSnNDLFVBQVUsRUFBRXBELEtBQUEsQ0FBQU0sT0FBRyxDQUFDRyxNQUFNLEVBQUUsQ0FDckJrQixPQUFPLENBQUMsb0NBQW9DLENBQUMsQ0FDN0NkLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixxQkFBcUIsRUFBRTtHQUN4QixDQUFDO0VBRUp1QyxJQUFJLEVBQUVyRCxLQUFBLENBQUFNLE9BQUcsQ0FBQ2EsTUFBTSxFQUFFLENBQ2ZSLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUkMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUNUQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsWUFBWSxFQUFFLDJCQUEyQjtJQUN6QyxZQUFZLEVBQUU7R0FDZixDQUFDO0VBRUp3QyxPQUFPLEVBQUV0RCxLQUFBLENBQUFNLE9BQUcsQ0FBQ2tCLE9BQU8sRUFBRSxDQUNuQmxCLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FDZE8sUUFBUSxFQUFFO0VBRWIwQyxTQUFTLEVBQUV2RCxLQUFBLENBQUFNLE9BQUcsQ0FBQ2tCLE9BQU8sRUFBRSxDQUNyQmxCLE9BQU8sQ0FBQyxLQUFLLENBQUMsQ0FDZE8sUUFBUSxFQUFFO0VBRWIyQyxjQUFjLEVBQUV4RCxLQUFBLENBQUFNLE9BQUcsQ0FBQ2tCLE9BQU8sRUFBRSxDQUMxQmxCLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FDYk8sUUFBUTtDQUNaLENBQUM7QUFFRjs7O0FBQUE7QUFBQWYsYUFBQSxHQUFBRyxDQUFBO0FBR2FHLE9BQUEsQ0FBQXFELHVCQUF1QixHQUFHekQsS0FBQSxDQUFBTSxPQUFHLENBQUNDLE1BQU0sQ0FBQztFQUNoREMsTUFBTSxFQUFFUixLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQ2pCQyxJQUFJLEVBQUUsQ0FDTkMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUNOQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQ1JDLFFBQVEsRUFBRTtFQUViNkMsU0FBUyxFQUFFMUQsS0FBQSxDQUFBTSxPQUFHLENBQUNHLE1BQU0sRUFBRSxDQUNwQkMsSUFBSSxFQUFFLENBQ05DLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FDTkMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUNSZSxPQUFPLENBQUMscUJBQXFCLENBQUMsQ0FDOUJkLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixxQkFBcUIsRUFBRTtHQUN4QixDQUFDO0VBRUpDLElBQUksRUFBRWYsS0FBQSxDQUFBTSxPQUFHLENBQUNVLEtBQUssRUFBRSxDQUNkQyxLQUFLLENBQUNqQixLQUFBLENBQUFNLE9BQUcsQ0FBQ0csTUFBTSxFQUFFLENBQUNDLElBQUksRUFBRSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUN6Q0EsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxRQUFRLEVBQUU7RUFFYjhDLFNBQVMsRUFBRTNELEtBQUEsQ0FBQU0sT0FBRyxDQUFDa0IsT0FBTyxFQUFFLENBQ3JCbEIsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUNkTyxRQUFRLEVBQUU7RUFFYjhCLGNBQWMsRUFBRTNDLEtBQUEsQ0FBQU0sT0FBRyxDQUFDVSxLQUFLLEVBQUUsQ0FDeEJDLEtBQUssQ0FBQ2pCLEtBQUEsQ0FBQU0sT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FBQyxDQUNuQkssR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsV0FBVyxFQUFFO0dBQ2Q7Q0FDSixDQUFDO0FBQUM7QUFBQWhCLGFBQUEsR0FBQUcsQ0FBQTtBQUVIRyxPQUFBLENBQUFFLE9BQUEsR0FBZTtFQUNiRCxpQkFBaUIsRUFBakJELE9BQUEsQ0FBQUMsaUJBQWlCO0VBQ2pCaUIsa0JBQWtCLEVBQWxCbEIsT0FBQSxDQUFBa0Isa0JBQWtCO0VBQ2xCRywwQkFBMEIsRUFBMUJyQixPQUFBLENBQUFxQiwwQkFBMEI7RUFDMUJPLDZCQUE2QixFQUE3QjVCLE9BQUEsQ0FBQTRCLDZCQUE2QjtFQUM3QkssZ0JBQWdCLEVBQWhCakMsT0FBQSxDQUFBaUMsZ0JBQWdCO0VBQ2hCRyx1QkFBdUIsRUFBdkJwQyxPQUFBLENBQUFvQyx1QkFBdUI7RUFDdkJNLGtCQUFrQixFQUFsQjFDLE9BQUEsQ0FBQTBDLGtCQUFrQjtFQUNsQkssdUJBQXVCLEVBQXZCL0MsT0FBQSxDQUFBK0MsdUJBQXVCO0VBQ3ZCTSx1QkFBdUIsRUFBdkJyRCxPQUFBLENBQUFxRDtDQUNEIiwiaWdub3JlTGlzdCI6W119