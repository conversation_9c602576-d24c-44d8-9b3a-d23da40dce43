{"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Failed to connect to MongoDB: Password cannot be empty\u001b[39m","stack":"MongoInvalidArgumentError: Password cannot be empty\n    at passwordDigest (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:165:15)\n    at continueScramConversation (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:95:96)\n    at executeScram (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\lajospaces\\LajoSpacesBackend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-06-17 01:12:33:1233"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Connected to MongoDB successfully\u001b[39m","timestamp":"2025-06-17 13:43:00:430"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔄 Redis client connecting...\u001b[39m","timestamp":"2025-06-17 13:43:00:430"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Redis client connected and ready\u001b[39m","timestamp":"2025-06-17 13:43:01:431"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏓 Redis ping successful\u001b[39m","timestamp":"2025-06-17 13:43:01:431"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up database indexes...\u001b[39m","timestamp":"2025-06-17 13:43:01:431"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database indexes setup completed successfully\u001b[39m","timestamp":"2025-06-17 13:43:07:437"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Users collection: 7 indexes created\u001b[39m","timestamp":"2025-06-17 13:43:07:437"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Connected to MongoDB successfully\u001b[39m","timestamp":"2025-06-17 14:08:51:851"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔄 Redis client connecting...\u001b[39m","timestamp":"2025-06-17 14:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Redis client connected and ready\u001b[39m","timestamp":"2025-06-17 14:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏓 Redis ping successful\u001b[39m","timestamp":"2025-06-17 14:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up database indexes...\u001b[39m","timestamp":"2025-06-17 14:08:52:852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database indexes setup completed successfully\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Users collection: 7 indexes created\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Profiles collection: 13 indexes created\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 LajoSpaces Backend Server running on port 3000\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📊 Environment: development\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend URL: http://localhost:8080\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💾 Database: Connected to MongoDB with indexes\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔄 Cache: Connected to Redis\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📧 Email: Configured with Zoho Mail\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📁 Storage: Configured with Cloudinary\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Server ready to accept connections\u001b[39m","timestamp":"2025-06-17 14:08:56:856"}
