e913545cab8c4eac98eef4913925d536
"use strict";

/* istanbul ignore next */
function cov_6lpkz6xfi() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\auth.validators.ts";
  var hash = "c9573d521f2d9c52e5ed6400627d78d808658189";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\auth.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 268
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 42
        }
      },
      "5": {
        start: {
          line: 8,
          column: 14
        },
        end: {
          line: 8,
          column: 45
        }
      },
      "6": {
        start: {
          line: 10,
          column: 23
        },
        end: {
          line: 20,
          column: 2
        }
      },
      "7": {
        start: {
          line: 22,
          column: 20
        },
        end: {
          line: 29,
          column: 2
        }
      },
      "8": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 35,
          column: 2
        }
      },
      "9": {
        start: {
          line: 37,
          column: 19
        },
        end: {
          line: 48,
          column: 2
        }
      },
      "10": {
        start: {
          line: 50,
          column: 26
        },
        end: {
          line: 71,
          column: 2
        }
      },
      "11": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "12": {
        start: {
          line: 55,
          column: 22
        },
        end: {
          line: 55,
          column: 62
        }
      },
      "13": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 56,
          column: 58
        }
      },
      "14": {
        start: {
          line: 57,
          column: 20
        },
        end: {
          line: 57,
          column: 23
        }
      },
      "15": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "16": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 20
        }
      },
      "17": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 63,
          column: 5
        }
      },
      "18": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 41
        }
      },
      "19": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 17
        }
      },
      "20": {
        start: {
          line: 75,
          column: 0
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "21": {
        start: {
          line: 111,
          column: 0
        },
        end: {
          line: 119,
          column: 3
        }
      },
      "22": {
        start: {
          line: 123,
          column: 0
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "23": {
        start: {
          line: 129,
          column: 0
        },
        end: {
          line: 143,
          column: 3
        }
      },
      "24": {
        start: {
          line: 147,
          column: 0
        },
        end: {
          line: 161,
          column: 3
        }
      },
      "25": {
        start: {
          line: 165,
          column: 0
        },
        end: {
          line: 171,
          column: 3
        }
      },
      "26": {
        start: {
          line: 175,
          column: 0
        },
        end: {
          line: 177,
          column: 3
        }
      },
      "27": {
        start: {
          line: 181,
          column: 0
        },
        end: {
          line: 187,
          column: 3
        }
      },
      "28": {
        start: {
          line: 191,
          column: 0
        },
        end: {
          line: 206,
          column: 3
        }
      },
      "29": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 238,
          column: 6
        }
      },
      "30": {
        start: {
          line: 212,
          column: 33
        },
        end: {
          line: 216,
          column: 10
        }
      },
      "31": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 234,
          column: 9
        }
      },
      "32": {
        start: {
          line: 218,
          column: 34
        },
        end: {
          line: 221,
          column: 15
        }
      },
      "33": {
        start: {
          line: 218,
          column: 63
        },
        end: {
          line: 221,
          column: 13
        }
      },
      "34": {
        start: {
          line: 222,
          column: 12
        },
        end: {
          line: 233,
          column: 15
        }
      },
      "35": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 25
        }
      },
      "36": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 15
        }
      },
      "37": {
        start: {
          line: 240,
          column: 0
        },
        end: {
          line: 251,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 13
          }
        },
        loc: {
          start: {
            line: 53,
            column: 32
          },
          end: {
            line: 65,
            column: 1
          }
        },
        line: 53
      },
      "2": {
        name: "validateRequest",
        decl: {
          start: {
            line: 210,
            column: 9
          },
          end: {
            line: 210,
            column: 24
          }
        },
        loc: {
          start: {
            line: 210,
            column: 33
          },
          end: {
            line: 239,
            column: 1
          }
        },
        line: 210
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 211,
            column: 11
          },
          end: {
            line: 211,
            column: 12
          }
        },
        loc: {
          start: {
            line: 211,
            column: 31
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 211
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 218,
            column: 52
          },
          end: {
            line: 218,
            column: 53
          }
        },
        loc: {
          start: {
            line: 218,
            column: 63
          },
          end: {
            line: 221,
            column: 13
          }
        },
        line: 218
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "4": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 21
          }
        }, {
          start: {
            line: 58,
            column: 26
          },
          end: {
            line: 58,
            column: 41
          }
        }, {
          start: {
            line: 58,
            column: 45
          },
          end: {
            line: 58,
            column: 56
          }
        }],
        line: 58
      },
      "5": {
        loc: {
          start: {
            line: 61,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "6": {
        loc: {
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 234,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 234,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\auth.validators.ts",
      mappings: ";;;;;;AA4NA,0CAgCC;AA5PD,8CAAsB;AAEtB,6BAA6B;AAC7B,MAAM,cAAc,GAAG,aAAG,CAAC,MAAM,EAAE;KAChC,GAAG,CAAC,CAAC,CAAC;KACN,GAAG,CAAC,GAAG,CAAC;KACR,OAAO,CAAC,iEAAiE,CAAC;KAC1E,QAAQ,EAAE;KACV,QAAQ,CAAC;IACR,YAAY,EAAE,6CAA6C;IAC3D,YAAY,EAAE,uCAAuC;IACrD,qBAAqB,EAAE,kHAAkH;IACzI,cAAc,EAAE,sBAAsB;CACvC,CAAC,CAAC;AAEL,0BAA0B;AAC1B,MAAM,WAAW,GAAG,aAAG,CAAC,MAAM,EAAE;KAC7B,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC;KACjC,SAAS,EAAE;KACX,QAAQ,EAAE;KACV,QAAQ,CAAC;IACR,cAAc,EAAE,sCAAsC;IACtD,cAAc,EAAE,mBAAmB;CACpC,CAAC,CAAC;AAEL,mCAAmC;AACnC,MAAM,WAAW,GAAG,aAAG,CAAC,MAAM,EAAE;KAC7B,OAAO,CAAC,gCAAgC,CAAC;KACzC,QAAQ,CAAC;IACR,qBAAqB,EAAE,kFAAkF;CAC1G,CAAC,CAAC;AAEL,yBAAyB;AACzB,MAAM,UAAU,GAAG,aAAG,CAAC,MAAM,EAAE;KAC5B,GAAG,CAAC,CAAC,CAAC;KACN,GAAG,CAAC,EAAE,CAAC;KACP,OAAO,CAAC,iBAAiB,CAAC;KAC1B,IAAI,EAAE;KACN,QAAQ,EAAE;KACV,QAAQ,CAAC;IACR,YAAY,EAAE,yCAAyC;IACvD,YAAY,EAAE,kCAAkC;IAChD,qBAAqB,EAAE,iEAAiE;IACxF,cAAc,EAAE,kBAAkB;CACnC,CAAC,CAAC;AAEL,8CAA8C;AAC9C,MAAM,iBAAiB,GAAG,aAAG,CAAC,IAAI,EAAE;KACjC,GAAG,CAAC,KAAK,CAAC;KACV,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACnD,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAC3D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC3D,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IAEvD,IAAI,SAAS,GAAG,GAAG,CAAC;IACpB,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC;QACtD,SAAS,EAAE,CAAC;IACd,CAAC;IAED,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;QACnB,OAAO,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;KACD,QAAQ,EAAE;KACV,QAAQ,CAAC;IACR,UAAU,EAAE,uCAAuC;IACnD,UAAU,EAAE,mCAAmC;IAC/C,cAAc,EAAE,2BAA2B;CAC5C,CAAC,CAAC;AAEL;;GAEG;AACU,QAAA,cAAc,GAAG,aAAG,CAAC,MAAM,CAAC;IACvC,KAAK,EAAE,WAAW;IAClB,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,UAAU;IACrB,QAAQ,EAAE,UAAU;IACpB,WAAW,EAAE,iBAAiB;IAC9B,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,mBAAmB,CAAC;SAC1D,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,oEAAoE;QAChF,cAAc,EAAE,oBAAoB;KACrC,CAAC;IACJ,WAAW,EAAE,WAAW,CAAC,QAAQ,EAAE;IACnC,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;SAChC,OAAO,CAAC,QAAQ,CAAC;SACjB,QAAQ,CAAC;QACR,UAAU,EAAE,kDAAkD;KAC/D,CAAC;IACJ,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC9C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;KACzD,CAAC,CAAC,QAAQ,EAAE;IACb,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE;SACxB,KAAK,CAAC,IAAI,CAAC;SACX,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,4CAA4C;QACxD,cAAc,EAAE,gCAAgC;KACjD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,aAAG,CAAC,MAAM,CAAC;IACpC,KAAK,EAAE,WAAW;IAClB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,sBAAsB;KACvC,CAAC;IACJ,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACzC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,WAAW;CACnB,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,yBAAyB;KAC1C,CAAC;IACJ,QAAQ,EAAE,cAAc;IACxB,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE;SAC1B,KAAK,CAAC,aAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAC1B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,wBAAwB;QACpC,cAAc,EAAE,mCAAmC;KACpD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE;SAC1B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,8BAA8B;KAC/C,CAAC;IACJ,WAAW,EAAE,cAAc;IAC3B,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE;SAC1B,KAAK,CAAC,aAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;SAC7B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,wBAAwB;QACpC,cAAc,EAAE,mCAAmC;KACpD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC1C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,gCAAgC;KACjD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,wBAAwB,GAAG,aAAG,CAAC,MAAM,CAAC;IACjD,KAAK,EAAE,WAAW;CACnB,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC3C,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,2BAA2B;KAC5C,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE;IAChC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE;IAC/B,WAAW,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7C,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC9C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;KACjD,CAAC,CAAC,QAAQ,EAAE;IACb,WAAW,EAAE,aAAG,CAAC,MAAM,CAAC;QACtB,kBAAkB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC5C,iBAAiB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC3C,gBAAgB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC1C,eAAe,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;KAC1C,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACH,SAAgB,eAAe,CAAC,MAAwB;IACtD,OAAO,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;QACvC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE;YACjD,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACjD,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC,CAAC,CAAC;YAEJ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,mBAAmB;oBAC5B,IAAI,EAAE,kBAAkB;oBACxB,UAAU,EAAE,GAAG;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,OAAO,EAAE,aAAa;iBACvB;aACF,CAAC,CAAC;QACL,CAAC;QAED,qDAAqD;QACrD,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC;QACjB,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC;AAED,kBAAe;IACb,cAAc,EAAd,sBAAc;IACd,WAAW,EAAX,mBAAW;IACX,oBAAoB,EAApB,4BAAoB;IACpB,mBAAmB,EAAnB,2BAAmB;IACnB,oBAAoB,EAApB,4BAAoB;IACpB,iBAAiB,EAAjB,yBAAiB;IACjB,wBAAwB,EAAxB,gCAAwB;IACxB,kBAAkB,EAAlB,0BAAkB;IAClB,mBAAmB,EAAnB,2BAAmB;IACnB,eAAe;CAChB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\auth.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n// Password validation schema\r\nconst passwordSchema = Joi.string()\r\n  .min(8)\r\n  .max(128)\r\n  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)\r\n  .required()\r\n  .messages({\r\n    'string.min': 'Password must be at least 8 characters long',\r\n    'string.max': 'Password cannot exceed 128 characters',\r\n    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',\r\n    'any.required': 'Password is required'\r\n  });\r\n\r\n// Email validation schema\r\nconst emailSchema = Joi.string()\r\n  .email({ tlds: { allow: false } })\r\n  .lowercase()\r\n  .required()\r\n  .messages({\r\n    'string.email': 'Please provide a valid email address',\r\n    'any.required': 'Email is required'\r\n  });\r\n\r\n// Nigerian phone number validation\r\nconst phoneSchema = Joi.string()\r\n  .pattern(/^(\\+234|234|0)?[789][01]\\d{8}$/)\r\n  .messages({\r\n    'string.pattern.base': 'Please provide a valid Nigerian phone number (e.g., +2348012345678, 08012345678)'\r\n  });\r\n\r\n// Name validation schema\r\nconst nameSchema = Joi.string()\r\n  .min(2)\r\n  .max(50)\r\n  .pattern(/^[a-zA-Z\\s'-]+$/)\r\n  .trim()\r\n  .required()\r\n  .messages({\r\n    'string.min': 'Name must be at least 2 characters long',\r\n    'string.max': 'Name cannot exceed 50 characters',\r\n    'string.pattern.base': 'Name can only contain letters, spaces, hyphens, and apostrophes',\r\n    'any.required': 'Name is required'\r\n  });\r\n\r\n// Date of birth validation (18-100 years old)\r\nconst dateOfBirthSchema = Joi.date()\r\n  .max('now')\r\n  .min(new Date(new Date().getFullYear() - 100, 0, 1))\r\n  .custom((value, helpers) => {\r\n    const age = new Date().getFullYear() - value.getFullYear();\r\n    const monthDiff = new Date().getMonth() - value.getMonth();\r\n    const dayDiff = new Date().getDate() - value.getDate();\r\n    \r\n    let actualAge = age;\r\n    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {\r\n      actualAge--;\r\n    }\r\n    \r\n    if (actualAge < 18) {\r\n      return helpers.error('date.min');\r\n    }\r\n    \r\n    return value;\r\n  })\r\n  .required()\r\n  .messages({\r\n    'date.max': 'Date of birth cannot be in the future',\r\n    'date.min': 'You must be at least 18 years old',\r\n    'any.required': 'Date of birth is required'\r\n  });\r\n\r\n/**\r\n * User registration validation schema\r\n */\r\nexport const registerSchema = Joi.object({\r\n  email: emailSchema,\r\n  password: passwordSchema,\r\n  firstName: nameSchema,\r\n  lastName: nameSchema,\r\n  dateOfBirth: dateOfBirthSchema,\r\n  gender: Joi.string()\r\n    .valid('male', 'female', 'non-binary', 'prefer-not-to-say')\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Gender must be one of: male, female, non-binary, prefer-not-to-say',\r\n      'any.required': 'Gender is required'\r\n    }),\r\n  phoneNumber: phoneSchema.optional(),\r\n  accountType: Joi.string()\r\n    .valid('seeker', 'owner', 'both')\r\n    .default('seeker')\r\n    .messages({\r\n      'any.only': 'Account type must be one of: seeker, owner, both'\r\n    }),\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().trim().max(100).optional(),\r\n    country: Joi.string().trim().max(100).default('Nigeria')\r\n  }).optional(),\r\n  agreeToTerms: Joi.boolean()\r\n    .valid(true)\r\n    .required()\r\n    .messages({\r\n      'any.only': 'You must agree to the terms and conditions',\r\n      'any.required': 'Agreement to terms is required'\r\n    })\r\n});\r\n\r\n/**\r\n * User login validation schema\r\n */\r\nexport const loginSchema = Joi.object({\r\n  email: emailSchema,\r\n  password: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Password is required'\r\n    }),\r\n  rememberMe: Joi.boolean().default(false)\r\n});\r\n\r\n/**\r\n * Forgot password validation schema\r\n */\r\nexport const forgotPasswordSchema = Joi.object({\r\n  email: emailSchema\r\n});\r\n\r\n/**\r\n * Reset password validation schema\r\n */\r\nexport const resetPasswordSchema = Joi.object({\r\n  token: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Reset token is required'\r\n    }),\r\n  password: passwordSchema,\r\n  confirmPassword: Joi.string()\r\n    .valid(Joi.ref('password'))\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Passwords do not match',\r\n      'any.required': 'Password confirmation is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Change password validation schema\r\n */\r\nexport const changePasswordSchema = Joi.object({\r\n  currentPassword: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Current password is required'\r\n    }),\r\n  newPassword: passwordSchema,\r\n  confirmPassword: Joi.string()\r\n    .valid(Joi.ref('newPassword'))\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Passwords do not match',\r\n      'any.required': 'Password confirmation is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Email verification validation schema\r\n */\r\nexport const verifyEmailSchema = Joi.object({\r\n  token: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Verification token is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Resend verification email schema\r\n */\r\nexport const resendVerificationSchema = Joi.object({\r\n  email: emailSchema\r\n});\r\n\r\n/**\r\n * Refresh token validation schema\r\n */\r\nexport const refreshTokenSchema = Joi.object({\r\n  refreshToken: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Refresh token is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Update profile validation schema\r\n */\r\nexport const updateProfileSchema = Joi.object({\r\n  firstName: nameSchema.optional(),\r\n  lastName: nameSchema.optional(),\r\n  phoneNumber: phoneSchema.optional().allow(''),\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().trim().max(100).optional(),\r\n    country: Joi.string().trim().max(100).optional()\r\n  }).optional(),\r\n  preferences: Joi.object({\r\n    emailNotifications: Joi.boolean().optional(),\r\n    pushNotifications: Joi.boolean().optional(),\r\n    smsNotifications: Joi.boolean().optional(),\r\n    marketingEmails: Joi.boolean().optional()\r\n  }).optional()\r\n});\r\n\r\n/**\r\n * Validation middleware factory\r\n */\r\nexport function validateRequest(schema: Joi.ObjectSchema) {\r\n  return (req: any, res: any, next: any) => {\r\n    const { error, value } = schema.validate(req.body, {\r\n      abortEarly: false,\r\n      stripUnknown: true,\r\n      convert: true\r\n    });\r\n\r\n    if (error) {\r\n      const errorMessages = error.details.map(detail => ({\r\n        field: detail.path.join('.'),\r\n        message: detail.message\r\n      }));\r\n\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: {\r\n          message: 'Validation failed',\r\n          code: 'VALIDATION_ERROR',\r\n          statusCode: 400,\r\n          timestamp: new Date().toISOString(),\r\n          path: req.path,\r\n          method: req.method,\r\n          details: errorMessages\r\n        }\r\n      });\r\n    }\r\n\r\n    // Replace req.body with validated and sanitized data\r\n    req.body = value;\r\n    next();\r\n  };\r\n}\r\n\r\nexport default {\r\n  registerSchema,\r\n  loginSchema,\r\n  forgotPasswordSchema,\r\n  resetPasswordSchema,\r\n  changePasswordSchema,\r\n  verifyEmailSchema,\r\n  resendVerificationSchema,\r\n  refreshTokenSchema,\r\n  updateProfileSchema,\r\n  validateRequest\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c9573d521f2d9c52e5ed6400627d78d808658189"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_6lpkz6xfi = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6lpkz6xfi();
var __importDefault =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[0]++,
/* istanbul ignore next */
(cov_6lpkz6xfi().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_6lpkz6xfi().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_6lpkz6xfi().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_6lpkz6xfi().f[0]++;
  cov_6lpkz6xfi().s[1]++;
  return /* istanbul ignore next */(cov_6lpkz6xfi().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_6lpkz6xfi().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_6lpkz6xfi().s[3]++;
exports.updateProfileSchema = exports.refreshTokenSchema = exports.resendVerificationSchema = exports.verifyEmailSchema = exports.changePasswordSchema = exports.resetPasswordSchema = exports.forgotPasswordSchema = exports.loginSchema = exports.registerSchema = void 0;
/* istanbul ignore next */
cov_6lpkz6xfi().s[4]++;
exports.validateRequest = validateRequest;
const joi_1 =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[5]++, __importDefault(require("joi")));
// Password validation schema
const passwordSchema =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[6]++, joi_1.default.string().min(8).max(128).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required().messages({
  'string.min': 'Password must be at least 8 characters long',
  'string.max': 'Password cannot exceed 128 characters',
  'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  'any.required': 'Password is required'
}));
// Email validation schema
const emailSchema =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[7]++, joi_1.default.string().email({
  tlds: {
    allow: false
  }
}).lowercase().required().messages({
  'string.email': 'Please provide a valid email address',
  'any.required': 'Email is required'
}));
// Nigerian phone number validation
const phoneSchema =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[8]++, joi_1.default.string().pattern(/^(\+234|234|0)?[789][01]\d{8}$/).messages({
  'string.pattern.base': 'Please provide a valid Nigerian phone number (e.g., +2348012345678, 08012345678)'
}));
// Name validation schema
const nameSchema =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[9]++, joi_1.default.string().min(2).max(50).pattern(/^[a-zA-Z\s'-]+$/).trim().required().messages({
  'string.min': 'Name must be at least 2 characters long',
  'string.max': 'Name cannot exceed 50 characters',
  'string.pattern.base': 'Name can only contain letters, spaces, hyphens, and apostrophes',
  'any.required': 'Name is required'
}));
// Date of birth validation (18-100 years old)
const dateOfBirthSchema =
/* istanbul ignore next */
(cov_6lpkz6xfi().s[10]++, joi_1.default.date().max('now').min(new Date(new Date().getFullYear() - 100, 0, 1)).custom((value, helpers) => {
  /* istanbul ignore next */
  cov_6lpkz6xfi().f[1]++;
  const age =
  /* istanbul ignore next */
  (cov_6lpkz6xfi().s[11]++, new Date().getFullYear() - value.getFullYear());
  const monthDiff =
  /* istanbul ignore next */
  (cov_6lpkz6xfi().s[12]++, new Date().getMonth() - value.getMonth());
  const dayDiff =
  /* istanbul ignore next */
  (cov_6lpkz6xfi().s[13]++, new Date().getDate() - value.getDate());
  let actualAge =
  /* istanbul ignore next */
  (cov_6lpkz6xfi().s[14]++, age);
  /* istanbul ignore next */
  cov_6lpkz6xfi().s[15]++;
  if (
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[4][0]++, monthDiff < 0) ||
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[4][1]++, monthDiff === 0) &&
  /* istanbul ignore next */
  (cov_6lpkz6xfi().b[4][2]++, dayDiff < 0)) {
    /* istanbul ignore next */
    cov_6lpkz6xfi().b[3][0]++;
    cov_6lpkz6xfi().s[16]++;
    actualAge--;
  } else
  /* istanbul ignore next */
  {
    cov_6lpkz6xfi().b[3][1]++;
  }
  cov_6lpkz6xfi().s[17]++;
  if (actualAge < 18) {
    /* istanbul ignore next */
    cov_6lpkz6xfi().b[5][0]++;
    cov_6lpkz6xfi().s[18]++;
    return helpers.error('date.min');
  } else
  /* istanbul ignore next */
  {
    cov_6lpkz6xfi().b[5][1]++;
  }
  cov_6lpkz6xfi().s[19]++;
  return value;
}).required().messages({
  'date.max': 'Date of birth cannot be in the future',
  'date.min': 'You must be at least 18 years old',
  'any.required': 'Date of birth is required'
}));
/**
 * User registration validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[20]++;
exports.registerSchema = joi_1.default.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  dateOfBirth: dateOfBirthSchema,
  gender: joi_1.default.string().valid('male', 'female', 'non-binary', 'prefer-not-to-say').required().messages({
    'any.only': 'Gender must be one of: male, female, non-binary, prefer-not-to-say',
    'any.required': 'Gender is required'
  }),
  phoneNumber: phoneSchema.optional(),
  accountType: joi_1.default.string().valid('seeker', 'owner', 'both').default('seeker').messages({
    'any.only': 'Account type must be one of: seeker, owner, both'
  }),
  location: joi_1.default.object({
    city: joi_1.default.string().trim().max(100).optional(),
    state: joi_1.default.string().trim().max(100).optional(),
    country: joi_1.default.string().trim().max(100).default('Nigeria')
  }).optional(),
  agreeToTerms: joi_1.default.boolean().valid(true).required().messages({
    'any.only': 'You must agree to the terms and conditions',
    'any.required': 'Agreement to terms is required'
  })
});
/**
 * User login validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[21]++;
exports.loginSchema = joi_1.default.object({
  email: emailSchema,
  password: joi_1.default.string().required().messages({
    'any.required': 'Password is required'
  }),
  rememberMe: joi_1.default.boolean().default(false)
});
/**
 * Forgot password validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[22]++;
exports.forgotPasswordSchema = joi_1.default.object({
  email: emailSchema
});
/**
 * Reset password validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[23]++;
exports.resetPasswordSchema = joi_1.default.object({
  token: joi_1.default.string().required().messages({
    'any.required': 'Reset token is required'
  }),
  password: passwordSchema,
  confirmPassword: joi_1.default.string().valid(joi_1.default.ref('password')).required().messages({
    'any.only': 'Passwords do not match',
    'any.required': 'Password confirmation is required'
  })
});
/**
 * Change password validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[24]++;
exports.changePasswordSchema = joi_1.default.object({
  currentPassword: joi_1.default.string().required().messages({
    'any.required': 'Current password is required'
  }),
  newPassword: passwordSchema,
  confirmPassword: joi_1.default.string().valid(joi_1.default.ref('newPassword')).required().messages({
    'any.only': 'Passwords do not match',
    'any.required': 'Password confirmation is required'
  })
});
/**
 * Email verification validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[25]++;
exports.verifyEmailSchema = joi_1.default.object({
  token: joi_1.default.string().required().messages({
    'any.required': 'Verification token is required'
  })
});
/**
 * Resend verification email schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[26]++;
exports.resendVerificationSchema = joi_1.default.object({
  email: emailSchema
});
/**
 * Refresh token validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[27]++;
exports.refreshTokenSchema = joi_1.default.object({
  refreshToken: joi_1.default.string().required().messages({
    'any.required': 'Refresh token is required'
  })
});
/**
 * Update profile validation schema
 */
/* istanbul ignore next */
cov_6lpkz6xfi().s[28]++;
exports.updateProfileSchema = joi_1.default.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  phoneNumber: phoneSchema.optional().allow(''),
  location: joi_1.default.object({
    city: joi_1.default.string().trim().max(100).optional(),
    state: joi_1.default.string().trim().max(100).optional(),
    country: joi_1.default.string().trim().max(100).optional()
  }).optional(),
  preferences: joi_1.default.object({
    emailNotifications: joi_1.default.boolean().optional(),
    pushNotifications: joi_1.default.boolean().optional(),
    smsNotifications: joi_1.default.boolean().optional(),
    marketingEmails: joi_1.default.boolean().optional()
  }).optional()
});
/**
 * Validation middleware factory
 */
function validateRequest(schema) {
  /* istanbul ignore next */
  cov_6lpkz6xfi().f[2]++;
  cov_6lpkz6xfi().s[29]++;
  return (req, res, next) => {
    /* istanbul ignore next */
    cov_6lpkz6xfi().f[3]++;
    const {
      error,
      value
    } =
    /* istanbul ignore next */
    (cov_6lpkz6xfi().s[30]++, schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    }));
    /* istanbul ignore next */
    cov_6lpkz6xfi().s[31]++;
    if (error) {
      /* istanbul ignore next */
      cov_6lpkz6xfi().b[6][0]++;
      const errorMessages =
      /* istanbul ignore next */
      (cov_6lpkz6xfi().s[32]++, error.details.map(detail => {
        /* istanbul ignore next */
        cov_6lpkz6xfi().f[4]++;
        cov_6lpkz6xfi().s[33]++;
        return {
          field: detail.path.join('.'),
          message: detail.message
        };
      }));
      /* istanbul ignore next */
      cov_6lpkz6xfi().s[34]++;
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          statusCode: 400,
          timestamp: new Date().toISOString(),
          path: req.path,
          method: req.method,
          details: errorMessages
        }
      });
    } else
    /* istanbul ignore next */
    {
      cov_6lpkz6xfi().b[6][1]++;
    }
    // Replace req.body with validated and sanitized data
    cov_6lpkz6xfi().s[35]++;
    req.body = value;
    /* istanbul ignore next */
    cov_6lpkz6xfi().s[36]++;
    next();
  };
}
/* istanbul ignore next */
cov_6lpkz6xfi().s[37]++;
exports.default = {
  registerSchema: exports.registerSchema,
  loginSchema: exports.loginSchema,
  forgotPasswordSchema: exports.forgotPasswordSchema,
  resetPasswordSchema: exports.resetPasswordSchema,
  changePasswordSchema: exports.changePasswordSchema,
  verifyEmailSchema: exports.verifyEmailSchema,
  resendVerificationSchema: exports.resendVerificationSchema,
  refreshTokenSchema: exports.refreshTokenSchema,
  updateProfileSchema: exports.updateProfileSchema,
  validateRequest
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfNmxwa3o2eGZpIiwiYWN0dWFsQ292ZXJhZ2UiLCJleHBvcnRzIiwidmFsaWRhdGVSZXF1ZXN0Iiwiam9pXzEiLCJzIiwiX19pbXBvcnREZWZhdWx0IiwicmVxdWlyZSIsInBhc3N3b3JkU2NoZW1hIiwiZGVmYXVsdCIsInN0cmluZyIsIm1pbiIsIm1heCIsInBhdHRlcm4iLCJyZXF1aXJlZCIsIm1lc3NhZ2VzIiwiZW1haWxTY2hlbWEiLCJlbWFpbCIsInRsZHMiLCJhbGxvdyIsImxvd2VyY2FzZSIsInBob25lU2NoZW1hIiwibmFtZVNjaGVtYSIsInRyaW0iLCJkYXRlT2ZCaXJ0aFNjaGVtYSIsImRhdGUiLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJjdXN0b20iLCJ2YWx1ZSIsImhlbHBlcnMiLCJmIiwiYWdlIiwibW9udGhEaWZmIiwiZ2V0TW9udGgiLCJkYXlEaWZmIiwiZ2V0RGF0ZSIsImFjdHVhbEFnZSIsImIiLCJlcnJvciIsInJlZ2lzdGVyU2NoZW1hIiwib2JqZWN0IiwicGFzc3dvcmQiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImRhdGVPZkJpcnRoIiwiZ2VuZGVyIiwidmFsaWQiLCJwaG9uZU51bWJlciIsIm9wdGlvbmFsIiwiYWNjb3VudFR5cGUiLCJsb2NhdGlvbiIsImNpdHkiLCJzdGF0ZSIsImNvdW50cnkiLCJhZ3JlZVRvVGVybXMiLCJib29sZWFuIiwibG9naW5TY2hlbWEiLCJyZW1lbWJlck1lIiwiZm9yZ290UGFzc3dvcmRTY2hlbWEiLCJyZXNldFBhc3N3b3JkU2NoZW1hIiwidG9rZW4iLCJjb25maXJtUGFzc3dvcmQiLCJyZWYiLCJjaGFuZ2VQYXNzd29yZFNjaGVtYSIsImN1cnJlbnRQYXNzd29yZCIsIm5ld1Bhc3N3b3JkIiwidmVyaWZ5RW1haWxTY2hlbWEiLCJyZXNlbmRWZXJpZmljYXRpb25TY2hlbWEiLCJyZWZyZXNoVG9rZW5TY2hlbWEiLCJyZWZyZXNoVG9rZW4iLCJ1cGRhdGVQcm9maWxlU2NoZW1hIiwicHJlZmVyZW5jZXMiLCJlbWFpbE5vdGlmaWNhdGlvbnMiLCJwdXNoTm90aWZpY2F0aW9ucyIsInNtc05vdGlmaWNhdGlvbnMiLCJtYXJrZXRpbmdFbWFpbHMiLCJzY2hlbWEiLCJyZXEiLCJyZXMiLCJuZXh0IiwidmFsaWRhdGUiLCJib2R5IiwiYWJvcnRFYXJseSIsInN0cmlwVW5rbm93biIsImNvbnZlcnQiLCJlcnJvck1lc3NhZ2VzIiwiZGV0YWlscyIsIm1hcCIsImRldGFpbCIsImZpZWxkIiwicGF0aCIsImpvaW4iLCJtZXNzYWdlIiwic3RhdHVzIiwianNvbiIsInN1Y2Nlc3MiLCJjb2RlIiwic3RhdHVzQ29kZSIsInRpbWVzdGFtcCIsInRvSVNPU3RyaW5nIiwibWV0aG9kIl0sInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNWSBQQ1xcRGVza3RvcFxcbGFqb3NwYWNlc1xcbGFqb3NwYWNlc2JhY2tlbmRcXHNyY1xcdmFsaWRhdG9yc1xcYXV0aC52YWxpZGF0b3JzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBKb2kgZnJvbSAnam9pJztcclxuXHJcbi8vIFBhc3N3b3JkIHZhbGlkYXRpb24gc2NoZW1hXHJcbmNvbnN0IHBhc3N3b3JkU2NoZW1hID0gSm9pLnN0cmluZygpXHJcbiAgLm1pbig4KVxyXG4gIC5tYXgoMTI4KVxyXG4gIC5wYXR0ZXJuKC9eKD89LipbYS16XSkoPz0uKltBLVpdKSg/PS4qXFxkKSg/PS4qW0AkISUqPyZdKVtBLVphLXpcXGRAJCElKj8mXS8pXHJcbiAgLnJlcXVpcmVkKClcclxuICAubWVzc2FnZXMoe1xyXG4gICAgJ3N0cmluZy5taW4nOiAnUGFzc3dvcmQgbXVzdCBiZSBhdCBsZWFzdCA4IGNoYXJhY3RlcnMgbG9uZycsXHJcbiAgICAnc3RyaW5nLm1heCc6ICdQYXNzd29yZCBjYW5ub3QgZXhjZWVkIDEyOCBjaGFyYWN0ZXJzJyxcclxuICAgICdzdHJpbmcucGF0dGVybi5iYXNlJzogJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgdXBwZXJjYXNlIGxldHRlciwgb25lIGxvd2VyY2FzZSBsZXR0ZXIsIG9uZSBudW1iZXIsIGFuZCBvbmUgc3BlY2lhbCBjaGFyYWN0ZXInLFxyXG4gICAgJ2FueS5yZXF1aXJlZCc6ICdQYXNzd29yZCBpcyByZXF1aXJlZCdcclxuICB9KTtcclxuXHJcbi8vIEVtYWlsIHZhbGlkYXRpb24gc2NoZW1hXHJcbmNvbnN0IGVtYWlsU2NoZW1hID0gSm9pLnN0cmluZygpXHJcbiAgLmVtYWlsKHsgdGxkczogeyBhbGxvdzogZmFsc2UgfSB9KVxyXG4gIC5sb3dlcmNhc2UoKVxyXG4gIC5yZXF1aXJlZCgpXHJcbiAgLm1lc3NhZ2VzKHtcclxuICAgICdzdHJpbmcuZW1haWwnOiAnUGxlYXNlIHByb3ZpZGUgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyxcclxuICAgICdhbnkucmVxdWlyZWQnOiAnRW1haWwgaXMgcmVxdWlyZWQnXHJcbiAgfSk7XHJcblxyXG4vLyBOaWdlcmlhbiBwaG9uZSBudW1iZXIgdmFsaWRhdGlvblxyXG5jb25zdCBwaG9uZVNjaGVtYSA9IEpvaS5zdHJpbmcoKVxyXG4gIC5wYXR0ZXJuKC9eKFxcKzIzNHwyMzR8MCk/Wzc4OV1bMDFdXFxkezh9JC8pXHJcbiAgLm1lc3NhZ2VzKHtcclxuICAgICdzdHJpbmcucGF0dGVybi5iYXNlJzogJ1BsZWFzZSBwcm92aWRlIGEgdmFsaWQgTmlnZXJpYW4gcGhvbmUgbnVtYmVyIChlLmcuLCArMjM0ODAxMjM0NTY3OCwgMDgwMTIzNDU2NzgpJ1xyXG4gIH0pO1xyXG5cclxuLy8gTmFtZSB2YWxpZGF0aW9uIHNjaGVtYVxyXG5jb25zdCBuYW1lU2NoZW1hID0gSm9pLnN0cmluZygpXHJcbiAgLm1pbigyKVxyXG4gIC5tYXgoNTApXHJcbiAgLnBhdHRlcm4oL15bYS16QS1aXFxzJy1dKyQvKVxyXG4gIC50cmltKClcclxuICAucmVxdWlyZWQoKVxyXG4gIC5tZXNzYWdlcyh7XHJcbiAgICAnc3RyaW5nLm1pbic6ICdOYW1lIG11c3QgYmUgYXQgbGVhc3QgMiBjaGFyYWN0ZXJzIGxvbmcnLFxyXG4gICAgJ3N0cmluZy5tYXgnOiAnTmFtZSBjYW5ub3QgZXhjZWVkIDUwIGNoYXJhY3RlcnMnLFxyXG4gICAgJ3N0cmluZy5wYXR0ZXJuLmJhc2UnOiAnTmFtZSBjYW4gb25seSBjb250YWluIGxldHRlcnMsIHNwYWNlcywgaHlwaGVucywgYW5kIGFwb3N0cm9waGVzJyxcclxuICAgICdhbnkucmVxdWlyZWQnOiAnTmFtZSBpcyByZXF1aXJlZCdcclxuICB9KTtcclxuXHJcbi8vIERhdGUgb2YgYmlydGggdmFsaWRhdGlvbiAoMTgtMTAwIHllYXJzIG9sZClcclxuY29uc3QgZGF0ZU9mQmlydGhTY2hlbWEgPSBKb2kuZGF0ZSgpXHJcbiAgLm1heCgnbm93JylcclxuICAubWluKG5ldyBEYXRlKG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSAtIDEwMCwgMCwgMSkpXHJcbiAgLmN1c3RvbSgodmFsdWUsIGhlbHBlcnMpID0+IHtcclxuICAgIGNvbnN0IGFnZSA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSAtIHZhbHVlLmdldEZ1bGxZZWFyKCk7XHJcbiAgICBjb25zdCBtb250aERpZmYgPSBuZXcgRGF0ZSgpLmdldE1vbnRoKCkgLSB2YWx1ZS5nZXRNb250aCgpO1xyXG4gICAgY29uc3QgZGF5RGlmZiA9IG5ldyBEYXRlKCkuZ2V0RGF0ZSgpIC0gdmFsdWUuZ2V0RGF0ZSgpO1xyXG4gICAgXHJcbiAgICBsZXQgYWN0dWFsQWdlID0gYWdlO1xyXG4gICAgaWYgKG1vbnRoRGlmZiA8IDAgfHwgKG1vbnRoRGlmZiA9PT0gMCAmJiBkYXlEaWZmIDwgMCkpIHtcclxuICAgICAgYWN0dWFsQWdlLS07XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChhY3R1YWxBZ2UgPCAxOCkge1xyXG4gICAgICByZXR1cm4gaGVscGVycy5lcnJvcignZGF0ZS5taW4nKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHZhbHVlO1xyXG4gIH0pXHJcbiAgLnJlcXVpcmVkKClcclxuICAubWVzc2FnZXMoe1xyXG4gICAgJ2RhdGUubWF4JzogJ0RhdGUgb2YgYmlydGggY2Fubm90IGJlIGluIHRoZSBmdXR1cmUnLFxyXG4gICAgJ2RhdGUubWluJzogJ1lvdSBtdXN0IGJlIGF0IGxlYXN0IDE4IHllYXJzIG9sZCcsXHJcbiAgICAnYW55LnJlcXVpcmVkJzogJ0RhdGUgb2YgYmlydGggaXMgcmVxdWlyZWQnXHJcbiAgfSk7XHJcblxyXG4vKipcclxuICogVXNlciByZWdpc3RyYXRpb24gdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCByZWdpc3RlclNjaGVtYSA9IEpvaS5vYmplY3Qoe1xyXG4gIGVtYWlsOiBlbWFpbFNjaGVtYSxcclxuICBwYXNzd29yZDogcGFzc3dvcmRTY2hlbWEsXHJcbiAgZmlyc3ROYW1lOiBuYW1lU2NoZW1hLFxyXG4gIGxhc3ROYW1lOiBuYW1lU2NoZW1hLFxyXG4gIGRhdGVPZkJpcnRoOiBkYXRlT2ZCaXJ0aFNjaGVtYSxcclxuICBnZW5kZXI6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnZhbGlkKCdtYWxlJywgJ2ZlbWFsZScsICdub24tYmluYXJ5JywgJ3ByZWZlci1ub3QtdG8tc2F5JylcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYW55Lm9ubHknOiAnR2VuZGVyIG11c3QgYmUgb25lIG9mOiBtYWxlLCBmZW1hbGUsIG5vbi1iaW5hcnksIHByZWZlci1ub3QtdG8tc2F5JyxcclxuICAgICAgJ2FueS5yZXF1aXJlZCc6ICdHZW5kZXIgaXMgcmVxdWlyZWQnXHJcbiAgICB9KSxcclxuICBwaG9uZU51bWJlcjogcGhvbmVTY2hlbWEub3B0aW9uYWwoKSxcclxuICBhY2NvdW50VHlwZTogSm9pLnN0cmluZygpXHJcbiAgICAudmFsaWQoJ3NlZWtlcicsICdvd25lcicsICdib3RoJylcclxuICAgIC5kZWZhdWx0KCdzZWVrZXInKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FueS5vbmx5JzogJ0FjY291bnQgdHlwZSBtdXN0IGJlIG9uZSBvZjogc2Vla2VyLCBvd25lciwgYm90aCdcclxuICAgIH0pLFxyXG4gIGxvY2F0aW9uOiBKb2kub2JqZWN0KHtcclxuICAgIGNpdHk6IEpvaS5zdHJpbmcoKS50cmltKCkubWF4KDEwMCkub3B0aW9uYWwoKSxcclxuICAgIHN0YXRlOiBKb2kuc3RyaW5nKCkudHJpbSgpLm1heCgxMDApLm9wdGlvbmFsKCksXHJcbiAgICBjb3VudHJ5OiBKb2kuc3RyaW5nKCkudHJpbSgpLm1heCgxMDApLmRlZmF1bHQoJ05pZ2VyaWEnKVxyXG4gIH0pLm9wdGlvbmFsKCksXHJcbiAgYWdyZWVUb1Rlcm1zOiBKb2kuYm9vbGVhbigpXHJcbiAgICAudmFsaWQodHJ1ZSlcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYW55Lm9ubHknOiAnWW91IG11c3QgYWdyZWUgdG8gdGhlIHRlcm1zIGFuZCBjb25kaXRpb25zJyxcclxuICAgICAgJ2FueS5yZXF1aXJlZCc6ICdBZ3JlZW1lbnQgdG8gdGVybXMgaXMgcmVxdWlyZWQnXHJcbiAgICB9KVxyXG59KTtcclxuXHJcbi8qKlxyXG4gKiBVc2VyIGxvZ2luIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgbG9naW5TY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBlbWFpbDogZW1haWxTY2hlbWEsXHJcbiAgcGFzc3dvcmQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnJlcXVpcmVkKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhbnkucmVxdWlyZWQnOiAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnXHJcbiAgICB9KSxcclxuICByZW1lbWJlck1lOiBKb2kuYm9vbGVhbigpLmRlZmF1bHQoZmFsc2UpXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIEZvcmdvdCBwYXNzd29yZCB2YWxpZGF0aW9uIHNjaGVtYVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGZvcmdvdFBhc3N3b3JkU2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgZW1haWw6IGVtYWlsU2NoZW1hXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIFJlc2V0IHBhc3N3b3JkIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgcmVzZXRQYXNzd29yZFNjaGVtYSA9IEpvaS5vYmplY3Qoe1xyXG4gIHRva2VuOiBKb2kuc3RyaW5nKClcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ1Jlc2V0IHRva2VuIGlzIHJlcXVpcmVkJ1xyXG4gICAgfSksXHJcbiAgcGFzc3dvcmQ6IHBhc3N3b3JkU2NoZW1hLFxyXG4gIGNvbmZpcm1QYXNzd29yZDogSm9pLnN0cmluZygpXHJcbiAgICAudmFsaWQoSm9pLnJlZigncGFzc3dvcmQnKSlcclxuICAgIC5yZXF1aXJlZCgpXHJcbiAgICAubWVzc2FnZXMoe1xyXG4gICAgICAnYW55Lm9ubHknOiAnUGFzc3dvcmRzIGRvIG5vdCBtYXRjaCcsXHJcbiAgICAgICdhbnkucmVxdWlyZWQnOiAnUGFzc3dvcmQgY29uZmlybWF0aW9uIGlzIHJlcXVpcmVkJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogQ2hhbmdlIHBhc3N3b3JkIHZhbGlkYXRpb24gc2NoZW1hXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgY2hhbmdlUGFzc3dvcmRTY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBjdXJyZW50UGFzc3dvcmQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnJlcXVpcmVkKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhbnkucmVxdWlyZWQnOiAnQ3VycmVudCBwYXNzd29yZCBpcyByZXF1aXJlZCdcclxuICAgIH0pLFxyXG4gIG5ld1Bhc3N3b3JkOiBwYXNzd29yZFNjaGVtYSxcclxuICBjb25maXJtUGFzc3dvcmQ6IEpvaS5zdHJpbmcoKVxyXG4gICAgLnZhbGlkKEpvaS5yZWYoJ25ld1Bhc3N3b3JkJykpXHJcbiAgICAucmVxdWlyZWQoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FueS5vbmx5JzogJ1Bhc3N3b3JkcyBkbyBub3QgbWF0Y2gnLFxyXG4gICAgICAnYW55LnJlcXVpcmVkJzogJ1Bhc3N3b3JkIGNvbmZpcm1hdGlvbiBpcyByZXF1aXJlZCdcclxuICAgIH0pXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIEVtYWlsIHZlcmlmaWNhdGlvbiB2YWxpZGF0aW9uIHNjaGVtYVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IHZlcmlmeUVtYWlsU2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgdG9rZW46IEpvaS5zdHJpbmcoKVxyXG4gICAgLnJlcXVpcmVkKClcclxuICAgIC5tZXNzYWdlcyh7XHJcbiAgICAgICdhbnkucmVxdWlyZWQnOiAnVmVyaWZpY2F0aW9uIHRva2VuIGlzIHJlcXVpcmVkJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogUmVzZW5kIHZlcmlmaWNhdGlvbiBlbWFpbCBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCByZXNlbmRWZXJpZmljYXRpb25TY2hlbWEgPSBKb2kub2JqZWN0KHtcclxuICBlbWFpbDogZW1haWxTY2hlbWFcclxufSk7XHJcblxyXG4vKipcclxuICogUmVmcmVzaCB0b2tlbiB2YWxpZGF0aW9uIHNjaGVtYVxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IHJlZnJlc2hUb2tlblNjaGVtYSA9IEpvaS5vYmplY3Qoe1xyXG4gIHJlZnJlc2hUb2tlbjogSm9pLnN0cmluZygpXHJcbiAgICAucmVxdWlyZWQoKVxyXG4gICAgLm1lc3NhZ2VzKHtcclxuICAgICAgJ2FueS5yZXF1aXJlZCc6ICdSZWZyZXNoIHRva2VuIGlzIHJlcXVpcmVkJ1xyXG4gICAgfSlcclxufSk7XHJcblxyXG4vKipcclxuICogVXBkYXRlIHByb2ZpbGUgdmFsaWRhdGlvbiBzY2hlbWFcclxuICovXHJcbmV4cG9ydCBjb25zdCB1cGRhdGVQcm9maWxlU2NoZW1hID0gSm9pLm9iamVjdCh7XHJcbiAgZmlyc3ROYW1lOiBuYW1lU2NoZW1hLm9wdGlvbmFsKCksXHJcbiAgbGFzdE5hbWU6IG5hbWVTY2hlbWEub3B0aW9uYWwoKSxcclxuICBwaG9uZU51bWJlcjogcGhvbmVTY2hlbWEub3B0aW9uYWwoKS5hbGxvdygnJyksXHJcbiAgbG9jYXRpb246IEpvaS5vYmplY3Qoe1xyXG4gICAgY2l0eTogSm9pLnN0cmluZygpLnRyaW0oKS5tYXgoMTAwKS5vcHRpb25hbCgpLFxyXG4gICAgc3RhdGU6IEpvaS5zdHJpbmcoKS50cmltKCkubWF4KDEwMCkub3B0aW9uYWwoKSxcclxuICAgIGNvdW50cnk6IEpvaS5zdHJpbmcoKS50cmltKCkubWF4KDEwMCkub3B0aW9uYWwoKVxyXG4gIH0pLm9wdGlvbmFsKCksXHJcbiAgcHJlZmVyZW5jZXM6IEpvaS5vYmplY3Qoe1xyXG4gICAgZW1haWxOb3RpZmljYXRpb25zOiBKb2kuYm9vbGVhbigpLm9wdGlvbmFsKCksXHJcbiAgICBwdXNoTm90aWZpY2F0aW9uczogSm9pLmJvb2xlYW4oKS5vcHRpb25hbCgpLFxyXG4gICAgc21zTm90aWZpY2F0aW9uczogSm9pLmJvb2xlYW4oKS5vcHRpb25hbCgpLFxyXG4gICAgbWFya2V0aW5nRW1haWxzOiBKb2kuYm9vbGVhbigpLm9wdGlvbmFsKClcclxuICB9KS5vcHRpb25hbCgpXHJcbn0pO1xyXG5cclxuLyoqXHJcbiAqIFZhbGlkYXRpb24gbWlkZGxld2FyZSBmYWN0b3J5XHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVSZXF1ZXN0KHNjaGVtYTogSm9pLk9iamVjdFNjaGVtYSkge1xyXG4gIHJldHVybiAocmVxOiBhbnksIHJlczogYW55LCBuZXh0OiBhbnkpID0+IHtcclxuICAgIGNvbnN0IHsgZXJyb3IsIHZhbHVlIH0gPSBzY2hlbWEudmFsaWRhdGUocmVxLmJvZHksIHtcclxuICAgICAgYWJvcnRFYXJseTogZmFsc2UsXHJcbiAgICAgIHN0cmlwVW5rbm93bjogdHJ1ZSxcclxuICAgICAgY29udmVydDogdHJ1ZVxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKGVycm9yKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSBlcnJvci5kZXRhaWxzLm1hcChkZXRhaWwgPT4gKHtcclxuICAgICAgICBmaWVsZDogZGV0YWlsLnBhdGguam9pbignLicpLFxyXG4gICAgICAgIG1lc3NhZ2U6IGRldGFpbC5tZXNzYWdlXHJcbiAgICAgIH0pKTtcclxuXHJcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7XHJcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgICAgZXJyb3I6IHtcclxuICAgICAgICAgIG1lc3NhZ2U6ICdWYWxpZGF0aW9uIGZhaWxlZCcsXHJcbiAgICAgICAgICBjb2RlOiAnVkFMSURBVElPTl9FUlJPUicsXHJcbiAgICAgICAgICBzdGF0dXNDb2RlOiA0MDAsXHJcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgIHBhdGg6IHJlcS5wYXRoLFxyXG4gICAgICAgICAgbWV0aG9kOiByZXEubWV0aG9kLFxyXG4gICAgICAgICAgZGV0YWlsczogZXJyb3JNZXNzYWdlc1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmVwbGFjZSByZXEuYm9keSB3aXRoIHZhbGlkYXRlZCBhbmQgc2FuaXRpemVkIGRhdGFcclxuICAgIHJlcS5ib2R5ID0gdmFsdWU7XHJcbiAgICBuZXh0KCk7XHJcbiAgfTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQge1xyXG4gIHJlZ2lzdGVyU2NoZW1hLFxyXG4gIGxvZ2luU2NoZW1hLFxyXG4gIGZvcmdvdFBhc3N3b3JkU2NoZW1hLFxyXG4gIHJlc2V0UGFzc3dvcmRTY2hlbWEsXHJcbiAgY2hhbmdlUGFzc3dvcmRTY2hlbWEsXHJcbiAgdmVyaWZ5RW1haWxTY2hlbWEsXHJcbiAgcmVzZW5kVmVyaWZpY2F0aW9uU2NoZW1hLFxyXG4gIHJlZnJlc2hUb2tlblNjaGVtYSxcclxuICB1cGRhdGVQcm9maWxlU2NoZW1hLFxyXG4gIHZhbGlkYXRlUmVxdWVzdFxyXG59O1xyXG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFTSTtJQUFBQSxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxhQUFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFtTkpFLE9BQUEsQ0FBQUMsZUFBQSxHQUFBQSxlQUFBO0FBNU5BLE1BQUFDLEtBQUE7QUFBQTtBQUFBLENBQUFKLGFBQUEsR0FBQUssQ0FBQSxPQUFBQyxlQUFBLENBQUFDLE9BQUE7QUFFQTtBQUNBLE1BQU1DLGNBQWM7QUFBQTtBQUFBLENBQUFSLGFBQUEsR0FBQUssQ0FBQSxPQUFHRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQ2hDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQ05DLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FDUkMsT0FBTyxDQUFDLGlFQUFpRSxDQUFDLENBQzFFQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0VBQ1IsWUFBWSxFQUFFLDZDQUE2QztFQUMzRCxZQUFZLEVBQUUsdUNBQXVDO0VBQ3JELHFCQUFxQixFQUFFLGtIQUFrSDtFQUN6SSxjQUFjLEVBQUU7Q0FDakIsQ0FBQztBQUVKO0FBQ0EsTUFBTUMsV0FBVztBQUFBO0FBQUEsQ0FBQWhCLGFBQUEsR0FBQUssQ0FBQSxPQUFHRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQzdCTyxLQUFLLENBQUM7RUFBRUMsSUFBSSxFQUFFO0lBQUVDLEtBQUssRUFBRTtFQUFLO0FBQUUsQ0FBRSxDQUFDLENBQ2pDQyxTQUFTLEVBQUUsQ0FDWE4sUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztFQUNSLGNBQWMsRUFBRSxzQ0FBc0M7RUFDdEQsY0FBYyxFQUFFO0NBQ2pCLENBQUM7QUFFSjtBQUNBLE1BQU1NLFdBQVc7QUFBQTtBQUFBLENBQUFyQixhQUFBLEdBQUFLLENBQUEsT0FBR0QsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUM3QkcsT0FBTyxDQUFDLGdDQUFnQyxDQUFDLENBQ3pDRSxRQUFRLENBQUM7RUFDUixxQkFBcUIsRUFBRTtDQUN4QixDQUFDO0FBRUo7QUFDQSxNQUFNTyxVQUFVO0FBQUE7QUFBQSxDQUFBdEIsYUFBQSxHQUFBSyxDQUFBLE9BQUdELEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FDNUJDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FDTkMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUNQQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsQ0FDMUJVLElBQUksRUFBRSxDQUNOVCxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0VBQ1IsWUFBWSxFQUFFLHlDQUF5QztFQUN2RCxZQUFZLEVBQUUsa0NBQWtDO0VBQ2hELHFCQUFxQixFQUFFLGlFQUFpRTtFQUN4RixjQUFjLEVBQUU7Q0FDakIsQ0FBQztBQUVKO0FBQ0EsTUFBTVMsaUJBQWlCO0FBQUE7QUFBQSxDQUFBeEIsYUFBQSxHQUFBSyxDQUFBLFFBQUdELEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0IsSUFBSSxFQUFFLENBQ2pDYixHQUFHLENBQUMsS0FBSyxDQUFDLENBQ1ZELEdBQUcsQ0FBQyxJQUFJZSxJQUFJLENBQUMsSUFBSUEsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRSxHQUFHLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FDbkRDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLLEVBQUVDLE9BQU8sS0FBSTtFQUFBO0VBQUE5QixhQUFBLEdBQUErQixDQUFBO0VBQ3pCLE1BQU1DLEdBQUc7RUFBQTtFQUFBLENBQUFoQyxhQUFBLEdBQUFLLENBQUEsUUFBRyxJQUFJcUIsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRSxHQUFHRSxLQUFLLENBQUNGLFdBQVcsRUFBRTtFQUMxRCxNQUFNTSxTQUFTO0VBQUE7RUFBQSxDQUFBakMsYUFBQSxHQUFBSyxDQUFBLFFBQUcsSUFBSXFCLElBQUksRUFBRSxDQUFDUSxRQUFRLEVBQUUsR0FBR0wsS0FBSyxDQUFDSyxRQUFRLEVBQUU7RUFDMUQsTUFBTUMsT0FBTztFQUFBO0VBQUEsQ0FBQW5DLGFBQUEsR0FBQUssQ0FBQSxRQUFHLElBQUlxQixJQUFJLEVBQUUsQ0FBQ1UsT0FBTyxFQUFFLEdBQUdQLEtBQUssQ0FBQ08sT0FBTyxFQUFFO0VBRXRELElBQUlDLFNBQVM7RUFBQTtFQUFBLENBQUFyQyxhQUFBLEdBQUFLLENBQUEsUUFBRzJCLEdBQUc7RUFBQztFQUFBaEMsYUFBQSxHQUFBSyxDQUFBO0VBQ3BCO0VBQUk7RUFBQSxDQUFBTCxhQUFBLEdBQUFzQyxDQUFBLFVBQUFMLFNBQVMsR0FBRyxDQUFDO0VBQUs7RUFBQSxDQUFBakMsYUFBQSxHQUFBc0MsQ0FBQSxVQUFBTCxTQUFTLEtBQUssQ0FBQztFQUFBO0VBQUEsQ0FBQWpDLGFBQUEsR0FBQXNDLENBQUEsVUFBSUgsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFO0lBQUE7SUFBQW5DLGFBQUEsR0FBQXNDLENBQUE7SUFBQXRDLGFBQUEsR0FBQUssQ0FBQTtJQUNyRGdDLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFBQTtFQUFBO0lBQUFyQyxhQUFBLEdBQUFzQyxDQUFBO0VBQUE7RUFBQXRDLGFBQUEsR0FBQUssQ0FBQTtFQUVELElBQUlnQyxTQUFTLEdBQUcsRUFBRSxFQUFFO0lBQUE7SUFBQXJDLGFBQUEsR0FBQXNDLENBQUE7SUFBQXRDLGFBQUEsR0FBQUssQ0FBQTtJQUNsQixPQUFPeUIsT0FBTyxDQUFDUyxLQUFLLENBQUMsVUFBVSxDQUFDO0VBQ2xDLENBQUM7RUFBQTtFQUFBO0lBQUF2QyxhQUFBLEdBQUFzQyxDQUFBO0VBQUE7RUFBQXRDLGFBQUEsR0FBQUssQ0FBQTtFQUVELE9BQU93QixLQUFLO0FBQ2QsQ0FBQyxDQUFDLENBQ0RmLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7RUFDUixVQUFVLEVBQUUsdUNBQXVDO0VBQ25ELFVBQVUsRUFBRSxtQ0FBbUM7RUFDL0MsY0FBYyxFQUFFO0NBQ2pCLENBQUM7QUFFSjs7O0FBQUE7QUFBQWYsYUFBQSxHQUFBSyxDQUFBO0FBR2FILE9BQUEsQ0FBQXNDLGNBQWMsR0FBR3BDLEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0MsTUFBTSxDQUFDO0VBQ3ZDeEIsS0FBSyxFQUFFRCxXQUFXO0VBQ2xCMEIsUUFBUSxFQUFFbEMsY0FBYztFQUN4Qm1DLFNBQVMsRUFBRXJCLFVBQVU7RUFDckJzQixRQUFRLEVBQUV0QixVQUFVO0VBQ3BCdUIsV0FBVyxFQUFFckIsaUJBQWlCO0VBQzlCc0IsTUFBTSxFQUFFMUMsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUNqQnFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsUUFBUSxFQUFFLFlBQVksRUFBRSxtQkFBbUIsQ0FBQyxDQUMxRGpDLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixVQUFVLEVBQUUsb0VBQW9FO0lBQ2hGLGNBQWMsRUFBRTtHQUNqQixDQUFDO0VBQ0ppQyxXQUFXLEVBQUUzQixXQUFXLENBQUM0QixRQUFRLEVBQUU7RUFDbkNDLFdBQVcsRUFBRTlDLEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FDdEJxQyxLQUFLLENBQUMsUUFBUSxFQUFFLE9BQU8sRUFBRSxNQUFNLENBQUMsQ0FDaEN0QyxPQUFPLENBQUMsUUFBUSxDQUFDLENBQ2pCTSxRQUFRLENBQUM7SUFDUixVQUFVLEVBQUU7R0FDYixDQUFDO0VBQ0pvQyxRQUFRLEVBQUUvQyxLQUFBLENBQUFLLE9BQUcsQ0FBQ2dDLE1BQU0sQ0FBQztJQUNuQlcsSUFBSSxFQUFFaEQsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUFDYSxJQUFJLEVBQUUsQ0FBQ1gsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDcUMsUUFBUSxFQUFFO0lBQzdDSSxLQUFLLEVBQUVqRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQUNhLElBQUksRUFBRSxDQUFDWCxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUNxQyxRQUFRLEVBQUU7SUFDOUNLLE9BQU8sRUFBRWxELEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FBQ2EsSUFBSSxFQUFFLENBQUNYLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQ0gsT0FBTyxDQUFDLFNBQVM7R0FDeEQsQ0FBQyxDQUFDd0MsUUFBUSxFQUFFO0VBQ2JNLFlBQVksRUFBRW5ELEtBQUEsQ0FBQUssT0FBRyxDQUFDK0MsT0FBTyxFQUFFLENBQ3hCVCxLQUFLLENBQUMsSUFBSSxDQUFDLENBQ1hqQyxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsVUFBVSxFQUFFLDRDQUE0QztJQUN4RCxjQUFjLEVBQUU7R0FDakI7Q0FDSixDQUFDO0FBRUY7OztBQUFBO0FBQUFmLGFBQUEsR0FBQUssQ0FBQTtBQUdhSCxPQUFBLENBQUF1RCxXQUFXLEdBQUdyRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ2dDLE1BQU0sQ0FBQztFQUNwQ3hCLEtBQUssRUFBRUQsV0FBVztFQUNsQjBCLFFBQVEsRUFBRXRDLEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FDbkJJLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixjQUFjLEVBQUU7R0FDakIsQ0FBQztFQUNKMkMsVUFBVSxFQUFFdEQsS0FBQSxDQUFBSyxPQUFHLENBQUMrQyxPQUFPLEVBQUUsQ0FBQy9DLE9BQU8sQ0FBQyxLQUFLO0NBQ3hDLENBQUM7QUFFRjs7O0FBQUE7QUFBQVQsYUFBQSxHQUFBSyxDQUFBO0FBR2FILE9BQUEsQ0FBQXlELG9CQUFvQixHQUFHdkQsS0FBQSxDQUFBSyxPQUFHLENBQUNnQyxNQUFNLENBQUM7RUFDN0N4QixLQUFLLEVBQUVEO0NBQ1IsQ0FBQztBQUVGOzs7QUFBQTtBQUFBaEIsYUFBQSxHQUFBSyxDQUFBO0FBR2FILE9BQUEsQ0FBQTBELG1CQUFtQixHQUFHeEQsS0FBQSxDQUFBSyxPQUFHLENBQUNnQyxNQUFNLENBQUM7RUFDNUNvQixLQUFLLEVBQUV6RCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQ2hCSSxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsY0FBYyxFQUFFO0dBQ2pCLENBQUM7RUFDSjJCLFFBQVEsRUFBRWxDLGNBQWM7RUFDeEJzRCxlQUFlLEVBQUUxRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQzFCcUMsS0FBSyxDQUFDM0MsS0FBQSxDQUFBSyxPQUFHLENBQUNzRCxHQUFHLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FDMUJqRCxRQUFRLEVBQUUsQ0FDVkMsUUFBUSxDQUFDO0lBQ1IsVUFBVSxFQUFFLHdCQUF3QjtJQUNwQyxjQUFjLEVBQUU7R0FDakI7Q0FDSixDQUFDO0FBRUY7OztBQUFBO0FBQUFmLGFBQUEsR0FBQUssQ0FBQTtBQUdhSCxPQUFBLENBQUE4RCxvQkFBb0IsR0FBRzVELEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0MsTUFBTSxDQUFDO0VBQzdDd0IsZUFBZSxFQUFFN0QsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUMxQkksUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLGNBQWMsRUFBRTtHQUNqQixDQUFDO0VBQ0ptRCxXQUFXLEVBQUUxRCxjQUFjO0VBQzNCc0QsZUFBZSxFQUFFMUQsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUMxQnFDLEtBQUssQ0FBQzNDLEtBQUEsQ0FBQUssT0FBRyxDQUFDc0QsR0FBRyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQzdCakQsUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLFVBQVUsRUFBRSx3QkFBd0I7SUFDcEMsY0FBYyxFQUFFO0dBQ2pCO0NBQ0osQ0FBQztBQUVGOzs7QUFBQTtBQUFBZixhQUFBLEdBQUFLLENBQUE7QUFHYUgsT0FBQSxDQUFBaUUsaUJBQWlCLEdBQUcvRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ2dDLE1BQU0sQ0FBQztFQUMxQ29CLEtBQUssRUFBRXpELEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FDaEJJLFFBQVEsRUFBRSxDQUNWQyxRQUFRLENBQUM7SUFDUixjQUFjLEVBQUU7R0FDakI7Q0FDSixDQUFDO0FBRUY7OztBQUFBO0FBQUFmLGFBQUEsR0FBQUssQ0FBQTtBQUdhSCxPQUFBLENBQUFrRSx3QkFBd0IsR0FBR2hFLEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0MsTUFBTSxDQUFDO0VBQ2pEeEIsS0FBSyxFQUFFRDtDQUNSLENBQUM7QUFFRjs7O0FBQUE7QUFBQWhCLGFBQUEsR0FBQUssQ0FBQTtBQUdhSCxPQUFBLENBQUFtRSxrQkFBa0IsR0FBR2pFLEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0MsTUFBTSxDQUFDO0VBQzNDNkIsWUFBWSxFQUFFbEUsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUN2QkksUUFBUSxFQUFFLENBQ1ZDLFFBQVEsQ0FBQztJQUNSLGNBQWMsRUFBRTtHQUNqQjtDQUNKLENBQUM7QUFFRjs7O0FBQUE7QUFBQWYsYUFBQSxHQUFBSyxDQUFBO0FBR2FILE9BQUEsQ0FBQXFFLG1CQUFtQixHQUFHbkUsS0FBQSxDQUFBSyxPQUFHLENBQUNnQyxNQUFNLENBQUM7RUFDNUNFLFNBQVMsRUFBRXJCLFVBQVUsQ0FBQzJCLFFBQVEsRUFBRTtFQUNoQ0wsUUFBUSxFQUFFdEIsVUFBVSxDQUFDMkIsUUFBUSxFQUFFO0VBQy9CRCxXQUFXLEVBQUUzQixXQUFXLENBQUM0QixRQUFRLEVBQUUsQ0FBQzlCLEtBQUssQ0FBQyxFQUFFLENBQUM7RUFDN0NnQyxRQUFRLEVBQUUvQyxLQUFBLENBQUFLLE9BQUcsQ0FBQ2dDLE1BQU0sQ0FBQztJQUNuQlcsSUFBSSxFQUFFaEQsS0FBQSxDQUFBSyxPQUFHLENBQUNDLE1BQU0sRUFBRSxDQUFDYSxJQUFJLEVBQUUsQ0FBQ1gsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDcUMsUUFBUSxFQUFFO0lBQzdDSSxLQUFLLEVBQUVqRCxLQUFBLENBQUFLLE9BQUcsQ0FBQ0MsTUFBTSxFQUFFLENBQUNhLElBQUksRUFBRSxDQUFDWCxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUNxQyxRQUFRLEVBQUU7SUFDOUNLLE9BQU8sRUFBRWxELEtBQUEsQ0FBQUssT0FBRyxDQUFDQyxNQUFNLEVBQUUsQ0FBQ2EsSUFBSSxFQUFFLENBQUNYLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQ3FDLFFBQVE7R0FDL0MsQ0FBQyxDQUFDQSxRQUFRLEVBQUU7RUFDYnVCLFdBQVcsRUFBRXBFLEtBQUEsQ0FBQUssT0FBRyxDQUFDZ0MsTUFBTSxDQUFDO0lBQ3RCZ0Msa0JBQWtCLEVBQUVyRSxLQUFBLENBQUFLLE9BQUcsQ0FBQytDLE9BQU8sRUFBRSxDQUFDUCxRQUFRLEVBQUU7SUFDNUN5QixpQkFBaUIsRUFBRXRFLEtBQUEsQ0FBQUssT0FBRyxDQUFDK0MsT0FBTyxFQUFFLENBQUNQLFFBQVEsRUFBRTtJQUMzQzBCLGdCQUFnQixFQUFFdkUsS0FBQSxDQUFBSyxPQUFHLENBQUMrQyxPQUFPLEVBQUUsQ0FBQ1AsUUFBUSxFQUFFO0lBQzFDMkIsZUFBZSxFQUFFeEUsS0FBQSxDQUFBSyxPQUFHLENBQUMrQyxPQUFPLEVBQUUsQ0FBQ1AsUUFBUTtHQUN4QyxDQUFDLENBQUNBLFFBQVE7Q0FDWixDQUFDO0FBRUY7OztBQUdBLFNBQWdCOUMsZUFBZUEsQ0FBQzBFLE1BQXdCO0VBQUE7RUFBQTdFLGFBQUEsR0FBQStCLENBQUE7RUFBQS9CLGFBQUEsR0FBQUssQ0FBQTtFQUN0RCxPQUFPLENBQUN5RSxHQUFRLEVBQUVDLEdBQVEsRUFBRUMsSUFBUyxLQUFJO0lBQUE7SUFBQWhGLGFBQUEsR0FBQStCLENBQUE7SUFDdkMsTUFBTTtNQUFFUSxLQUFLO01BQUVWO0lBQUssQ0FBRTtJQUFBO0lBQUEsQ0FBQTdCLGFBQUEsR0FBQUssQ0FBQSxRQUFHd0UsTUFBTSxDQUFDSSxRQUFRLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxFQUFFO01BQ2pEQyxVQUFVLEVBQUUsS0FBSztNQUNqQkMsWUFBWSxFQUFFLElBQUk7TUFDbEJDLE9BQU8sRUFBRTtLQUNWLENBQUM7SUFBQztJQUFBckYsYUFBQSxHQUFBSyxDQUFBO0lBRUgsSUFBSWtDLEtBQUssRUFBRTtNQUFBO01BQUF2QyxhQUFBLEdBQUFzQyxDQUFBO01BQ1QsTUFBTWdELGFBQWE7TUFBQTtNQUFBLENBQUF0RixhQUFBLEdBQUFLLENBQUEsUUFBR2tDLEtBQUssQ0FBQ2dELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxNQUFNLElBQUs7UUFBQTtRQUFBekYsYUFBQSxHQUFBK0IsQ0FBQTtRQUFBL0IsYUFBQSxHQUFBSyxDQUFBO1FBQUE7VUFDakRxRixLQUFLLEVBQUVELE1BQU0sQ0FBQ0UsSUFBSSxDQUFDQyxJQUFJLENBQUMsR0FBRyxDQUFDO1VBQzVCQyxPQUFPLEVBQUVKLE1BQU0sQ0FBQ0k7U0FDakI7T0FBQyxDQUFDO01BQUM7TUFBQTdGLGFBQUEsR0FBQUssQ0FBQTtNQUVKLE9BQU8wRSxHQUFHLENBQUNlLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQ0MsSUFBSSxDQUFDO1FBQzFCQyxPQUFPLEVBQUUsS0FBSztRQUNkekQsS0FBSyxFQUFFO1VBQ0xzRCxPQUFPLEVBQUUsbUJBQW1CO1VBQzVCSSxJQUFJLEVBQUUsa0JBQWtCO1VBQ3hCQyxVQUFVLEVBQUUsR0FBRztVQUNmQyxTQUFTLEVBQUUsSUFBSXpFLElBQUksRUFBRSxDQUFDMEUsV0FBVyxFQUFFO1VBQ25DVCxJQUFJLEVBQUViLEdBQUcsQ0FBQ2EsSUFBSTtVQUNkVSxNQUFNLEVBQUV2QixHQUFHLENBQUN1QixNQUFNO1VBQ2xCZCxPQUFPLEVBQUVEOztPQUVaLENBQUM7SUFDSixDQUFDO0lBQUE7SUFBQTtNQUFBdEYsYUFBQSxHQUFBc0MsQ0FBQTtJQUFBO0lBRUQ7SUFBQXRDLGFBQUEsR0FBQUssQ0FBQTtJQUNBeUUsR0FBRyxDQUFDSSxJQUFJLEdBQUdyRCxLQUFLO0lBQUM7SUFBQTdCLGFBQUEsR0FBQUssQ0FBQTtJQUNqQjJFLElBQUksRUFBRTtFQUNSLENBQUM7QUFDSDtBQUFDO0FBQUFoRixhQUFBLEdBQUFLLENBQUE7QUFFREgsT0FBQSxDQUFBTyxPQUFBLEdBQWU7RUFDYitCLGNBQWMsRUFBZHRDLE9BQUEsQ0FBQXNDLGNBQWM7RUFDZGlCLFdBQVcsRUFBWHZELE9BQUEsQ0FBQXVELFdBQVc7RUFDWEUsb0JBQW9CLEVBQXBCekQsT0FBQSxDQUFBeUQsb0JBQW9CO0VBQ3BCQyxtQkFBbUIsRUFBbkIxRCxPQUFBLENBQUEwRCxtQkFBbUI7RUFDbkJJLG9CQUFvQixFQUFwQjlELE9BQUEsQ0FBQThELG9CQUFvQjtFQUNwQkcsaUJBQWlCLEVBQWpCakUsT0FBQSxDQUFBaUUsaUJBQWlCO0VBQ2pCQyx3QkFBd0IsRUFBeEJsRSxPQUFBLENBQUFrRSx3QkFBd0I7RUFDeEJDLGtCQUFrQixFQUFsQm5FLE9BQUEsQ0FBQW1FLGtCQUFrQjtFQUNsQkUsbUJBQW1CLEVBQW5CckUsT0FBQSxDQUFBcUUsbUJBQW1CO0VBQ25CcEU7Q0FDRCIsImlnbm9yZUxpc3QiOltdfQ==