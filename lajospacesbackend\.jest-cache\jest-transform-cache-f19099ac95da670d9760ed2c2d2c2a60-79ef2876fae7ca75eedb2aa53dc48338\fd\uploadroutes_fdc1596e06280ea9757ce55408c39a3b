6b16ca1f2c25c<PERSON><PERSON>cafefecb51281d5
"use strict";

/* istanbul ignore next */
function cov_os2dsi2td() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts";
  var hash = "2c232287c1e9016b8b0832aeb0aaf2920d547b99";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 28
        },
        end: {
          line: 4,
          column: 71
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 17
        },
        end: {
          line: 7,
          column: 48
        }
      },
      "6": {
        start: {
          line: 8,
          column: 28
        },
        end: {
          line: 8,
          column: 70
        }
      },
      "7": {
        start: {
          line: 9,
          column: 15
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 32
        }
      },
      "9": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 199
        }
      },
      "10": {
        start: {
          line: 23,
          column: 0
        },
        end: {
          line: 23,
          column: 191
        }
      },
      "11": {
        start: {
          line: 29,
          column: 0
        },
        end: {
          line: 29,
          column: 224
        }
      },
      "12": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 230
        }
      },
      "13": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 199
        }
      },
      "14": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 69
        }
      },
      "15": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 154
        }
      },
      "16": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 70,
          column: 3
        }
      },
      "17": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 69,
          column: 7
        }
      },
      "18": {
        start: {
          line: 71,
          column: 0
        },
        end: {
          line: 71,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 23
          }
        },
        loc: {
          start: {
            line: 59,
            column: 36
          },
          end: {
            line: 70,
            column: 1
          }
        },
        line: 59
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,wEAQ0C;AAC1C,6CAAkD;AAClD,yDAA6E;AAC7E,iDAO8B;AAC9B,uEAOyC;AAEzC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,2CAA2C;AAC3C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,QAAQ,EACR,0BAA2B,EAC3B,0BAAiB,EACjB,IAAA,4BAAe,EAAC,qCAAiB,EAAE,MAAM,CAAC,EAC1C,qCAAiB,CAClB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,SAAS,EACT,qBAAsB,EACtB,0BAAiB,EACjB,IAAA,4BAAe,EAAC,sCAAkB,EAAE,MAAM,CAAC,EAC3C,gCAAY,CACb,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,kBAAkB,EAClB,6BAA8B,EAC9B,0BAAiB,EACjB,IAAA,4BAAe,EAAC,8CAA0B,EAAE,MAAM,CAAC,EACnD,wCAAoB,CACrB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,qBAAqB,EACrB,0BAAiB,EACjB,0BAAiB,EACjB,IAAA,4BAAe,EAAC,iDAA6B,EAAE,MAAM,CAAC,EACtD,2CAAuB,CACxB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,OAAO,EACP,6BAAoB,EACpB,0BAAiB,EACjB,IAAA,4BAAe,EAAC,oCAAgB,EAAE,MAAM,CAAC,EACzC,oCAAgB,CACjB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,YAAY,EACZ,uCAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,aAAa,EACb,IAAA,4BAAe,EAAC,2CAAuB,EAAE,MAAM,CAAC,EAChD,qCAAiB,CAClB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,2BAA2B;QACpC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE;YACR,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,WAAW;YAC9B,cAAc,EAAE,QAAQ;SACzB;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  uploadSingleImage,\r\n  uploadAvatar,\r\n  uploadPropertyPhotos,\r\n  uploadMessageAttachment,\r\n  bulkUploadImages,\r\n  deleteUploadedImage,\r\n  generateUploadUrl\r\n} from '../controllers/upload.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  uploadSingleImage as uploadSingleImageMiddleware,\r\n  uploadAvatar as uploadAvatarMiddleware,\r\n  uploadPropertyPhotos as uploadPropertyPhotosMiddleware,\r\n  uploadMessageFile,\r\n  uploadMultipleImages,\r\n  handleUploadError\r\n} from '../middleware/upload';\r\nimport {\r\n  uploadImageSchema,\r\n  uploadAvatarSchema,\r\n  uploadPropertyPhotosSchema,\r\n  uploadMessageAttachmentSchema,\r\n  bulkUploadSchema,\r\n  generateUploadUrlSchema\r\n} from '../validators/upload.validators';\r\n\r\nconst router = Router();\r\n\r\n// All upload routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   POST /api/uploads/image\r\n * @desc    Upload single image\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/image',\r\n  uploadSingleImageMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadImageSchema, 'body'),\r\n  uploadSingleImage\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/avatar\r\n * @desc    Upload user avatar\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/avatar',\r\n  uploadAvatarMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadAvatarSchema, 'body'),\r\n  uploadAvatar\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/property-photos\r\n * @desc    Upload property photos (bulk)\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/property-photos',\r\n  uploadPropertyPhotosMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadPropertyPhotosSchema, 'body'),\r\n  uploadPropertyPhotos\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/message-attachment\r\n * @desc    Upload message attachment\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/message-attachment',\r\n  uploadMessageFile,\r\n  handleUploadError,\r\n  validateRequest(uploadMessageAttachmentSchema, 'body'),\r\n  uploadMessageAttachment\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/bulk\r\n * @desc    Bulk upload images\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/bulk',\r\n  uploadMultipleImages,\r\n  handleUploadError,\r\n  validateRequest(bulkUploadSchema, 'body'),\r\n  bulkUploadImages\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/uploads/:publicId\r\n * @desc    Delete uploaded image\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:publicId',\r\n  deleteUploadedImage\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/signed-url\r\n * @desc    Generate signed upload URL for direct client uploads\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/signed-url',\r\n  validateRequest(generateUploadUrlSchema, 'body'),\r\n  generateUploadUrl\r\n);\r\n\r\n/**\r\n * @route   GET /api/uploads/health\r\n * @desc    Health check for upload service\r\n * @access  Private\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Upload service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    services: {\r\n      cloudinary: 'connected',\r\n      imageOptimization: 'available',\r\n      fileValidation: 'active'\r\n    }\r\n  });\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2c232287c1e9016b8b0832aeb0aaf2920d547b99"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_os2dsi2td = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_os2dsi2td();
cov_os2dsi2td().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[1]++, require("express"));
const upload_controller_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[2]++, require("../controllers/upload.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[4]++, require("../middleware/validation"));
const upload_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[5]++, require("../middleware/upload"));
const upload_validators_1 =
/* istanbul ignore next */
(cov_os2dsi2td().s[6]++, require("../validators/upload.validators"));
const router =
/* istanbul ignore next */
(cov_os2dsi2td().s[7]++, (0, express_1.Router)());
// All upload routes require authentication
/* istanbul ignore next */
cov_os2dsi2td().s[8]++;
router.use(auth_1.authenticate);
/**
 * @route   POST /api/uploads/image
 * @desc    Upload single image
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[9]++;
router.post('/image', upload_1.uploadSingleImage, upload_1.handleUploadError, (0, validation_1.validateRequest)(upload_validators_1.uploadImageSchema, 'body'), upload_controller_1.uploadSingleImage);
/**
 * @route   POST /api/uploads/avatar
 * @desc    Upload user avatar
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[10]++;
router.post('/avatar', upload_1.uploadAvatar, upload_1.handleUploadError, (0, validation_1.validateRequest)(upload_validators_1.uploadAvatarSchema, 'body'), upload_controller_1.uploadAvatar);
/**
 * @route   POST /api/uploads/property-photos
 * @desc    Upload property photos (bulk)
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[11]++;
router.post('/property-photos', upload_1.uploadPropertyPhotos, upload_1.handleUploadError, (0, validation_1.validateRequest)(upload_validators_1.uploadPropertyPhotosSchema, 'body'), upload_controller_1.uploadPropertyPhotos);
/**
 * @route   POST /api/uploads/message-attachment
 * @desc    Upload message attachment
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[12]++;
router.post('/message-attachment', upload_1.uploadMessageFile, upload_1.handleUploadError, (0, validation_1.validateRequest)(upload_validators_1.uploadMessageAttachmentSchema, 'body'), upload_controller_1.uploadMessageAttachment);
/**
 * @route   POST /api/uploads/bulk
 * @desc    Bulk upload images
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[13]++;
router.post('/bulk', upload_1.uploadMultipleImages, upload_1.handleUploadError, (0, validation_1.validateRequest)(upload_validators_1.bulkUploadSchema, 'body'), upload_controller_1.bulkUploadImages);
/**
 * @route   DELETE /api/uploads/:publicId
 * @desc    Delete uploaded image
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[14]++;
router.delete('/:publicId', upload_controller_1.deleteUploadedImage);
/**
 * @route   POST /api/uploads/signed-url
 * @desc    Generate signed upload URL for direct client uploads
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[15]++;
router.post('/signed-url', (0, validation_1.validateRequest)(upload_validators_1.generateUploadUrlSchema, 'body'), upload_controller_1.generateUploadUrl);
/**
 * @route   GET /api/uploads/health
 * @desc    Health check for upload service
 * @access  Private
 */
/* istanbul ignore next */
cov_os2dsi2td().s[16]++;
router.get('/health', (req, res) => {
  /* istanbul ignore next */
  cov_os2dsi2td().f[0]++;
  cov_os2dsi2td().s[17]++;
  res.json({
    success: true,
    message: 'Upload service is healthy',
    timestamp: new Date().toISOString(),
    services: {
      cloudinary: 'connected',
      imageOptimization: 'available',
      fileValidation: 'active'
    }
  });
});
/* istanbul ignore next */
cov_os2dsi2td().s[18]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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