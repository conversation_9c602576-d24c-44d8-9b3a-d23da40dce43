00e30b6a85538d92c0570ad1a4722b2d
"use strict";

/* istanbul ignore next */
function cov_1jyp3dm6tm() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts";
  var hash = "310a8631dbe31d23a936eff928f3a7faac5572df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 42
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 48
        }
      },
      "5": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 46
        }
      },
      "6": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 19
        },
        end: {
          line: 10,
          column: 55
        }
      },
      "8": {
        start: {
          line: 11,
          column: 22
        },
        end: {
          line: 11,
          column: 46
        }
      },
      "9": {
        start: {
          line: 12,
          column: 17
        },
        end: {
          line: 12,
          column: 43
        }
      },
      "10": {
        start: {
          line: 14,
          column: 21
        },
        end: {
          line: 18,
          column: 1
        }
      },
      "11": {
        start: {
          line: 20,
          column: 18
        },
        end: {
          line: 20,
          column: 23
        }
      },
      "12": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 42,
          column: 5
        }
      },
      "13": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 29,
          column: 9
        }
      },
      "14": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 66
        }
      },
      "15": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 19
        }
      },
      "16": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 140
        }
      },
      "17": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 60
        }
      },
      "18": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 65
        }
      },
      "19": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 27
        }
      },
      "20": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "21": {
        start: {
          line: 36,
          column: 23
        },
        end: {
          line: 36,
          column: 69
        }
      },
      "22": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 68
        }
      },
      "23": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 69
        }
      },
      "24": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 20
        }
      },
      "25": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "26": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 52,
          column: 9
        }
      },
      "27": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 50,
          column: 69
        }
      },
      "28": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 51,
          column: 19
        }
      },
      "29": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 46
        }
      },
      "30": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 28
        }
      },
      "31": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 69
        }
      },
      "32": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 72
        }
      },
      "33": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 20
        }
      },
      "34": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 71,
          column: 6
        }
      },
      "35": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 79,
          column: 5
        }
      },
      "36": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 78
        }
      },
      "37": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 65
        }
      },
      "38": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "39": {
        start: {
          line: 82,
          column: 27
        },
        end: {
          line: 82,
          column: 43
        }
      },
      "40": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 40
        }
      },
      "41": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 53
        }
      },
      "42": {
        start: {
          line: 88,
          column: 0
        },
        end: {
          line: 90,
          column: 3
        }
      },
      "43": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 61
        }
      },
      "44": {
        start: {
          line: 91,
          column: 0
        },
        end: {
          line: 94,
          column: 3
        }
      },
      "45": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 65
        }
      },
      "46": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 24
        }
      },
      "47": {
        start: {
          line: 95,
          column: 0
        },
        end: {
          line: 98,
          column: 3
        }
      },
      "48": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 66
        }
      },
      "49": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 24
        }
      },
      "50": {
        start: {
          line: 100,
          column: 0
        },
        end: {
          line: 110,
          column: 3
        }
      },
      "51": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "52": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 35
        }
      },
      "53": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 85
        }
      },
      "54": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 24
        }
      },
      "55": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 78
        }
      },
      "56": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 24
        }
      },
      "57": {
        start: {
          line: 111,
          column: 0
        },
        end: {
          line: 116,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "connectDatabase",
        decl: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 30
          }
        },
        loc: {
          start: {
            line: 24,
            column: 33
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 24
      },
      "2": {
        name: "disconnectDatabase",
        decl: {
          start: {
            line: 47,
            column: 15
          },
          end: {
            line: 47,
            column: 33
          }
        },
        loc: {
          start: {
            line: 47,
            column: 36
          },
          end: {
            line: 61,
            column: 1
          }
        },
        line: 47
      },
      "3": {
        name: "getDatabaseStatus",
        decl: {
          start: {
            line: 65,
            column: 9
          },
          end: {
            line: 65,
            column: 26
          }
        },
        loc: {
          start: {
            line: 65,
            column: 29
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 65
      },
      "4": {
        name: "clearDatabase",
        decl: {
          start: {
            line: 76,
            column: 15
          },
          end: {
            line: 76,
            column: 28
          }
        },
        loc: {
          start: {
            line: 76,
            column: 31
          },
          end: {
            line: 86,
            column: 1
          }
        },
        line: 76
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 88,
            column: 46
          },
          end: {
            line: 88,
            column: 47
          }
        },
        loc: {
          start: {
            line: 88,
            column: 52
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 88
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 91,
            column: 42
          },
          end: {
            line: 91,
            column: 43
          }
        },
        loc: {
          start: {
            line: 91,
            column: 53
          },
          end: {
            line: 94,
            column: 1
          }
        },
        line: 91
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 95,
            column: 49
          },
          end: {
            line: 95,
            column: 50
          }
        },
        loc: {
          start: {
            line: 95,
            column: 55
          },
          end: {
            line: 98,
            column: 1
          }
        },
        line: 95
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 100,
            column: 21
          },
          end: {
            line: 100,
            column: 22
          }
        },
        loc: {
          start: {
            line: 100,
            column: 33
          },
          end: {
            line: 110,
            column: 1
          }
        },
        line: 100
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 26,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "4": {
        loc: {
          start: {
            line: 30,
            column: 25
          },
          end: {
            line: 30,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 30,
            column: 68
          },
          end: {
            line: 30,
            column: 105
          }
        }, {
          start: {
            line: 30,
            column: 108
          },
          end: {
            line: 30,
            column: 140
          }
        }],
        line: 30
      },
      "5": {
        loc: {
          start: {
            line: 49,
            column: 8
          },
          end: {
            line: 52,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 8
          },
          end: {
            line: 52,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "6": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts",
      mappings: ";;;;;AAiBA,0CAwBC;AAKD,gDAcC;AAKD,8CAYC;AAKD,sCAaC;AA/FD,wDAAgC;AAChC,+CAAuC;AACvC,4CAAyC;AAEzC,6BAA6B;AAC7B,MAAM,YAAY,GAA4B;IAC5C,WAAW,EAAE,EAAE,EAAE,uCAAuC;IACxD,wBAAwB,EAAE,IAAI,EAAE,+CAA+C;IAC/E,eAAe,EAAE,KAAK,EAAE,+CAA+C;CACxE,CAAC;AAEF,4BAA4B;AAC5B,IAAI,WAAW,GAAG,KAAK,CAAC;AAExB;;GAEG;AACI,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC;QACH,IAAI,WAAW,EAAE,CAAC;YAChB,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,oBAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,oBAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,oBAAM,CAAC,WAAW,CAAC;QAE3F,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE3C,MAAM,kBAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE/C,WAAW,GAAG,IAAI,CAAC;QACnB,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAEhD,oBAAoB;QACpB,MAAM,MAAM,GAAG,kBAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC;QACpD,eAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;IAErD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kBAAkB;IACtC,IAAI,CAAC;QACH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,MAAM,kBAAQ,CAAC,UAAU,EAAE,CAAC;QAC5B,WAAW,GAAG,KAAK,CAAC;QACpB,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB;IAM/B,OAAO;QACL,WAAW;QACX,UAAU,EAAE,kBAAQ,CAAC,UAAU,CAAC,UAAU;QAC1C,IAAI,EAAE,kBAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,IAAI,EAAE,kBAAQ,CAAC,UAAU,CAAC,IAAI;KAC/B,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa;IACjC,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,WAAW,GAAG,kBAAQ,CAAC,UAAU,CAAC,WAAW,CAAC;IAEpD,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACpC,MAAM,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAC1C,CAAC;AAED,4BAA4B;AAC5B,kBAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;IACvC,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH,kBAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;IACpD,WAAW,GAAG,KAAK,CAAC;AACtB,CAAC,CAAC,CAAC;AAEH,kBAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;IAC1C,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACrD,WAAW,GAAG,KAAK,CAAC;AACtB,CAAC,CAAC,CAAC;AAEH,iCAAiC;AACjC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAC9B,IAAI,CAAC;QACH,MAAM,kBAAkB,EAAE,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe;IACb,OAAO,EAAE,eAAe;IACxB,UAAU,EAAE,kBAAkB;IAC9B,SAAS,EAAE,iBAAiB;IAC5B,KAAK,EAAE,aAAa;CACrB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts"],
      sourcesContent: ["import mongoose from 'mongoose';\r\nimport { config } from './environment';\r\nimport { logger } from '../utils/logger';\r\n\r\n// MongoDB connection options\r\nconst mongoOptions: mongoose.ConnectOptions = {\r\n  maxPoolSize: 10, // Maintain up to 10 socket connections\r\n  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds\r\n  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity\r\n};\r\n\r\n// Database connection state\r\nlet isConnected = false;\r\n\r\n/**\r\n * Connect to MongoDB database\r\n */\r\nexport async function connectDatabase(): Promise<void> {\r\n  try {\r\n    if (isConnected) {\r\n      logger.info('\uD83D\uDCCA Database already connected');\r\n      return;\r\n    }\r\n\r\n    const mongoUri = config.NODE_ENV === 'test' ? config.MONGODB_TEST_URI : config.MONGODB_URI;\r\n    \r\n    logger.info('\uD83D\uDCCA Connecting to MongoDB...');\r\n    \r\n    await mongoose.connect(mongoUri, mongoOptions);\r\n    \r\n    isConnected = true;\r\n    logger.info('\u2705 MongoDB connected successfully');\r\n    \r\n    // Log database name\r\n    const dbName = mongoose.connection.db?.databaseName;\r\n    logger.info(`\uD83D\uDCCA Connected to database: ${dbName}`);\r\n    \r\n  } catch (error) {\r\n    logger.error('\u274C MongoDB connection failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Disconnect from MongoDB database\r\n */\r\nexport async function disconnectDatabase(): Promise<void> {\r\n  try {\r\n    if (!isConnected) {\r\n      logger.info('\uD83D\uDCCA Database already disconnected');\r\n      return;\r\n    }\r\n\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    logger.info('\uD83D\uDCCA MongoDB disconnected successfully');\r\n  } catch (error) {\r\n    logger.error('\u274C MongoDB disconnection failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get database connection status\r\n */\r\nexport function getDatabaseStatus(): {\r\n  isConnected: boolean;\r\n  readyState: number;\r\n  host?: string;\r\n  name?: string;\r\n} {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    name: mongoose.connection.name\r\n  };\r\n}\r\n\r\n/**\r\n * Clear database (for testing purposes only)\r\n */\r\nexport async function clearDatabase(): Promise<void> {\r\n  if (config.NODE_ENV !== 'test') {\r\n    throw new Error('clearDatabase can only be used in test environment');\r\n  }\r\n\r\n  const collections = mongoose.connection.collections;\r\n  \r\n  for (const key in collections) {\r\n    const collection = collections[key];\r\n    await collection.deleteMany({});\r\n  }\r\n  \r\n  logger.info('\uD83E\uDDF9 Test database cleared');\r\n}\r\n\r\n// Connection event handlers\r\nmongoose.connection.on('connected', () => {\r\n  logger.info('\uD83D\uDCCA Mongoose connected to MongoDB');\r\n});\r\n\r\nmongoose.connection.on('error', (error) => {\r\n  logger.error('\u274C Mongoose connection error:', error);\r\n  isConnected = false;\r\n});\r\n\r\nmongoose.connection.on('disconnected', () => {\r\n  logger.info('\uD83D\uDCCA Mongoose disconnected from MongoDB');\r\n  isConnected = false;\r\n});\r\n\r\n// Handle application termination\r\nprocess.on('SIGINT', async () => {\r\n  try {\r\n    await disconnectDatabase();\r\n    logger.info('\uD83D\uDCCA MongoDB connection closed through app termination');\r\n    process.exit(0);\r\n  } catch (error) {\r\n    logger.error('\u274C Error during MongoDB disconnection:', error);\r\n    process.exit(1);\r\n  }\r\n});\r\n\r\nexport default {\r\n  connect: connectDatabase,\r\n  disconnect: disconnectDatabase,\r\n  getStatus: getDatabaseStatus,\r\n  clear: clearDatabase\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "310a8631dbe31d23a936eff928f3a7faac5572df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1jyp3dm6tm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1jyp3dm6tm();
var __importDefault =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[0]++,
/* istanbul ignore next */
(cov_1jyp3dm6tm().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1jyp3dm6tm().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1jyp3dm6tm().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[0]++;
  cov_1jyp3dm6tm().s[1]++;
  return /* istanbul ignore next */(cov_1jyp3dm6tm().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1jyp3dm6tm().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1jyp3dm6tm().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1jyp3dm6tm().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1jyp3dm6tm().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1jyp3dm6tm().s[3]++;
exports.connectDatabase = connectDatabase;
/* istanbul ignore next */
cov_1jyp3dm6tm().s[4]++;
exports.disconnectDatabase = disconnectDatabase;
/* istanbul ignore next */
cov_1jyp3dm6tm().s[5]++;
exports.getDatabaseStatus = getDatabaseStatus;
/* istanbul ignore next */
cov_1jyp3dm6tm().s[6]++;
exports.clearDatabase = clearDatabase;
const mongoose_1 =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[7]++, __importDefault(require("mongoose")));
const environment_1 =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[8]++, require("./environment"));
const logger_1 =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[9]++, require("../utils/logger"));
// MongoDB connection options
const mongoOptions =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[10]++, {
  maxPoolSize: 10,
  // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000,
  // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000 // Close sockets after 45 seconds of inactivity
});
// Database connection state
let isConnected =
/* istanbul ignore next */
(cov_1jyp3dm6tm().s[11]++, false);
/**
 * Connect to MongoDB database
 */
async function connectDatabase() {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[1]++;
  cov_1jyp3dm6tm().s[12]++;
  try {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[13]++;
    if (isConnected) {
      /* istanbul ignore next */
      cov_1jyp3dm6tm().b[3][0]++;
      cov_1jyp3dm6tm().s[14]++;
      logger_1.logger.info('📊 Database already connected');
      /* istanbul ignore next */
      cov_1jyp3dm6tm().s[15]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1jyp3dm6tm().b[3][1]++;
    }
    const mongoUri =
    /* istanbul ignore next */
    (cov_1jyp3dm6tm().s[16]++, environment_1.config.NODE_ENV === 'test' ?
    /* istanbul ignore next */
    (cov_1jyp3dm6tm().b[4][0]++, environment_1.config.MONGODB_TEST_URI) :
    /* istanbul ignore next */
    (cov_1jyp3dm6tm().b[4][1]++, environment_1.config.MONGODB_URI));
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[17]++;
    logger_1.logger.info('📊 Connecting to MongoDB...');
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[18]++;
    await mongoose_1.default.connect(mongoUri, mongoOptions);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[19]++;
    isConnected = true;
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[20]++;
    logger_1.logger.info('✅ MongoDB connected successfully');
    // Log database name
    const dbName =
    /* istanbul ignore next */
    (cov_1jyp3dm6tm().s[21]++, mongoose_1.default.connection.db?.databaseName);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[22]++;
    logger_1.logger.info(`📊 Connected to database: ${dbName}`);
  } catch (error) {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[23]++;
    logger_1.logger.error('❌ MongoDB connection failed:', error);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[24]++;
    throw error;
  }
}
/**
 * Disconnect from MongoDB database
 */
async function disconnectDatabase() {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[2]++;
  cov_1jyp3dm6tm().s[25]++;
  try {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[26]++;
    if (!isConnected) {
      /* istanbul ignore next */
      cov_1jyp3dm6tm().b[5][0]++;
      cov_1jyp3dm6tm().s[27]++;
      logger_1.logger.info('📊 Database already disconnected');
      /* istanbul ignore next */
      cov_1jyp3dm6tm().s[28]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1jyp3dm6tm().b[5][1]++;
    }
    cov_1jyp3dm6tm().s[29]++;
    await mongoose_1.default.disconnect();
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[30]++;
    isConnected = false;
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[31]++;
    logger_1.logger.info('📊 MongoDB disconnected successfully');
  } catch (error) {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[32]++;
    logger_1.logger.error('❌ MongoDB disconnection failed:', error);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[33]++;
    throw error;
  }
}
/**
 * Get database connection status
 */
function getDatabaseStatus() {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[3]++;
  cov_1jyp3dm6tm().s[34]++;
  return {
    isConnected,
    readyState: mongoose_1.default.connection.readyState,
    host: mongoose_1.default.connection.host,
    name: mongoose_1.default.connection.name
  };
}
/**
 * Clear database (for testing purposes only)
 */
async function clearDatabase() {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[4]++;
  cov_1jyp3dm6tm().s[35]++;
  if (environment_1.config.NODE_ENV !== 'test') {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().b[6][0]++;
    cov_1jyp3dm6tm().s[36]++;
    throw new Error('clearDatabase can only be used in test environment');
  } else
  /* istanbul ignore next */
  {
    cov_1jyp3dm6tm().b[6][1]++;
  }
  const collections =
  /* istanbul ignore next */
  (cov_1jyp3dm6tm().s[37]++, mongoose_1.default.connection.collections);
  /* istanbul ignore next */
  cov_1jyp3dm6tm().s[38]++;
  for (const key in collections) {
    const collection =
    /* istanbul ignore next */
    (cov_1jyp3dm6tm().s[39]++, collections[key]);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[40]++;
    await collection.deleteMany({});
  }
  /* istanbul ignore next */
  cov_1jyp3dm6tm().s[41]++;
  logger_1.logger.info('🧹 Test database cleared');
}
// Connection event handlers
/* istanbul ignore next */
cov_1jyp3dm6tm().s[42]++;
mongoose_1.default.connection.on('connected', () => {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[5]++;
  cov_1jyp3dm6tm().s[43]++;
  logger_1.logger.info('📊 Mongoose connected to MongoDB');
});
/* istanbul ignore next */
cov_1jyp3dm6tm().s[44]++;
mongoose_1.default.connection.on('error', error => {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[6]++;
  cov_1jyp3dm6tm().s[45]++;
  logger_1.logger.error('❌ Mongoose connection error:', error);
  /* istanbul ignore next */
  cov_1jyp3dm6tm().s[46]++;
  isConnected = false;
});
/* istanbul ignore next */
cov_1jyp3dm6tm().s[47]++;
mongoose_1.default.connection.on('disconnected', () => {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[7]++;
  cov_1jyp3dm6tm().s[48]++;
  logger_1.logger.info('📊 Mongoose disconnected from MongoDB');
  /* istanbul ignore next */
  cov_1jyp3dm6tm().s[49]++;
  isConnected = false;
});
// Handle application termination
/* istanbul ignore next */
cov_1jyp3dm6tm().s[50]++;
process.on('SIGINT', async () => {
  /* istanbul ignore next */
  cov_1jyp3dm6tm().f[8]++;
  cov_1jyp3dm6tm().s[51]++;
  try {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[52]++;
    await disconnectDatabase();
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[53]++;
    logger_1.logger.info('📊 MongoDB connection closed through app termination');
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[54]++;
    process.exit(0);
  } catch (error) {
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[55]++;
    logger_1.logger.error('❌ Error during MongoDB disconnection:', error);
    /* istanbul ignore next */
    cov_1jyp3dm6tm().s[56]++;
    process.exit(1);
  }
});
/* istanbul ignore next */
cov_1jyp3dm6tm().s[57]++;
exports.default = {
  connect: connectDatabase,
  disconnect: disconnectDatabase,
  getStatus: getDatabaseStatus,
  clear: clearDatabase
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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