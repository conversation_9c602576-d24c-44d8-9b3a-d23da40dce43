0d55caf1ece45405d4fec79973ee1853
"use strict";

/* istanbul ignore next */
function cov_1utmstr375() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\socketService.ts";
  var hash = "c84f691ecba13365824b93f7d2680a189dc51d7d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\socketService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 31
        }
      },
      "4": {
        start: {
          line: 7,
          column: 20
        },
        end: {
          line: 7,
          column: 40
        }
      },
      "5": {
        start: {
          line: 8,
          column: 23
        },
        end: {
          line: 8,
          column: 63
        }
      },
      "6": {
        start: {
          line: 9,
          column: 19
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 21
        },
        end: {
          line: 10,
          column: 52
        }
      },
      "8": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 56
        }
      },
      "9": {
        start: {
          line: 12,
          column: 17
        },
        end: {
          line: 12,
          column: 43
        }
      },
      "10": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 15,
          column: 37
        }
      },
      "11": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 37
        }
      },
      "12": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 17,
          column: 37
        }
      },
      "13": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 25,
          column: 11
        }
      },
      "14": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 31
        }
      },
      "15": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "16": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 36
        }
      },
      "17": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 51,
          column: 11
        }
      },
      "18": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 50,
          column: 13
        }
      },
      "19": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 114
        }
      },
      "20": {
        start: {
          line: 35,
          column: 16
        },
        end: {
          line: 37,
          column: 17
        }
      },
      "21": {
        start: {
          line: 36,
          column: 20
        },
        end: {
          line: 36,
          column: 76
        }
      },
      "22": {
        start: {
          line: 38,
          column: 32
        },
        end: {
          line: 38,
          column: 92
        }
      },
      "23": {
        start: {
          line: 39,
          column: 29
        },
        end: {
          line: 39,
          column: 93
        }
      },
      "24": {
        start: {
          line: 40,
          column: 16
        },
        end: {
          line: 42,
          column: 17
        }
      },
      "25": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 61
        }
      },
      "26": {
        start: {
          line: 43,
          column: 16
        },
        end: {
          line: 43,
          column: 52
        }
      },
      "27": {
        start: {
          line: 44,
          column: 16
        },
        end: {
          line: 44,
          column: 35
        }
      },
      "28": {
        start: {
          line: 45,
          column: 16
        },
        end: {
          line: 45,
          column: 23
        }
      },
      "29": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 77
        }
      },
      "30": {
        start: {
          line: 49,
          column: 16
        },
        end: {
          line: 49,
          column: 57
        }
      },
      "31": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 76,
          column: 11
        }
      },
      "32": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 55,
          column: 93
        }
      },
      "33": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 43
        }
      },
      "34": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 86
        }
      },
      "35": {
        start: {
          line: 59,
          column: 48
        },
        end: {
          line: 59,
          column: 84
        }
      },
      "36": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 96
        }
      },
      "37": {
        start: {
          line: 60,
          column: 53
        },
        end: {
          line: 60,
          column: 94
        }
      },
      "38": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 86
        }
      },
      "39": {
        start: {
          line: 61,
          column: 48
        },
        end: {
          line: 61,
          column: 84
        }
      },
      "40": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 86
        }
      },
      "41": {
        start: {
          line: 62,
          column: 48
        },
        end: {
          line: 62,
          column: 84
        }
      },
      "42": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 90
        }
      },
      "43": {
        start: {
          line: 63,
          column: 50
        },
        end: {
          line: 63,
          column: 88
        }
      },
      "44": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 94
        }
      },
      "45": {
        start: {
          line: 64,
          column: 52
        },
        end: {
          line: 64,
          column: 92
        }
      },
      "46": {
        start: {
          line: 66,
          column: 12
        },
        end: {
          line: 66,
          column: 96
        }
      },
      "47": {
        start: {
          line: 66,
          column: 53
        },
        end: {
          line: 66,
          column: 94
        }
      },
      "48": {
        start: {
          line: 67,
          column: 12
        },
        end: {
          line: 67,
          column: 98
        }
      },
      "49": {
        start: {
          line: 67,
          column: 54
        },
        end: {
          line: 67,
          column: 96
        }
      },
      "50": {
        start: {
          line: 68,
          column: 12
        },
        end: {
          line: 68,
          column: 100
        }
      },
      "51": {
        start: {
          line: 68,
          column: 55
        },
        end: {
          line: 68,
          column: 98
        }
      },
      "52": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 86
        }
      },
      "53": {
        start: {
          line: 70,
          column: 48
        },
        end: {
          line: 70,
          column: 84
        }
      },
      "54": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 71,
          column: 84
        }
      },
      "55": {
        start: {
          line: 71,
          column: 47
        },
        end: {
          line: 71,
          column: 82
        }
      },
      "56": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 73,
          column: 88
        }
      },
      "57": {
        start: {
          line: 73,
          column: 49
        },
        end: {
          line: 73,
          column: 86
        }
      },
      "58": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 77
        }
      },
      "59": {
        start: {
          line: 75,
          column: 42
        },
        end: {
          line: 75,
          column: 75
        }
      },
      "60": {
        start: {
          line: 79,
          column: 35
        },
        end: {
          line: 79,
          column: 39
        }
      },
      "61": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 81,
          column: 19
        }
      },
      "62": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 19
        }
      },
      "63": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "64": {
        start: {
          line: 84,
          column: 33
        },
        end: {
          line: 88,
          column: 14
        }
      },
      "65": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 96,
          column: 13
        }
      },
      "66": {
        start: {
          line: 90,
          column: 16
        },
        end: {
          line: 90,
          column: 62
        }
      },
      "67": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 101
        }
      },
      "68": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 71
        }
      },
      "69": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 95,
          column: 78
        }
      },
      "70": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 72
        }
      },
      "71": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 77
        }
      },
      "72": {
        start: {
          line: 104,
          column: 35
        },
        end: {
          line: 104,
          column: 39
        }
      },
      "73": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 106,
          column: 19
        }
      },
      "74": {
        start: {
          line: 106,
          column: 12
        },
        end: {
          line: 106,
          column: 19
        }
      },
      "75": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 55
        }
      },
      "76": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 91
        }
      },
      "77": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 61
        }
      },
      "78": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 171,
          column: 9
        }
      },
      "79": {
        start: {
          line: 113,
          column: 84
        },
        end: {
          line: 113,
          column: 88
        }
      },
      "80": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 117,
          column: 13
        }
      },
      "81": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 79
        }
      },
      "82": {
        start: {
          line: 116,
          column: 16
        },
        end: {
          line: 116,
          column: 23
        }
      },
      "83": {
        start: {
          line: 119,
          column: 36
        },
        end: {
          line: 119,
          column: 84
        }
      },
      "84": {
        start: {
          line: 121,
          column: 33
        },
        end: {
          line: 151,
          column: 14
        }
      },
      "85": {
        start: {
          line: 122,
          column: 56
        },
        end: {
          line: 122,
          column: 89
        }
      },
      "86": {
        start: {
          line: 123,
          column: 74
        },
        end: {
          line: 131,
          column: 17
        }
      },
      "87": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 38
        }
      },
      "88": {
        start: {
          line: 154,
          column: 12
        },
        end: {
          line: 161,
          column: 15
        }
      },
      "89": {
        start: {
          line: 155,
          column: 38
        },
        end: {
          line: 155,
          column: 73
        }
      },
      "90": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 160,
          column: 17
        }
      },
      "91": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 159,
          column: 23
        }
      },
      "92": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 93
        }
      },
      "93": {
        start: {
          line: 163,
          column: 12
        },
        end: {
          line: 165,
          column: 15
        }
      },
      "94": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 102
        }
      },
      "95": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 73
        }
      },
      "96": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 170,
          column: 79
        }
      },
      "97": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 216,
          column: 9
        }
      },
      "98": {
        start: {
          line: 175,
          column: 43
        },
        end: {
          line: 175,
          column: 47
        }
      },
      "99": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "100": {
        start: {
          line: 177,
          column: 16
        },
        end: {
          line: 177,
          column: 71
        }
      },
      "101": {
        start: {
          line: 178,
          column: 16
        },
        end: {
          line: 178,
          column: 23
        }
      },
      "102": {
        start: {
          line: 180,
          column: 28
        },
        end: {
          line: 185,
          column: 14
        }
      },
      "103": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 189,
          column: 13
        }
      },
      "104": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 91
        }
      },
      "105": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 23
        }
      },
      "106": {
        start: {
          line: 191,
          column: 31
        },
        end: {
          line: 191,
          column: 71
        }
      },
      "107": {
        start: {
          line: 192,
          column: 12
        },
        end: {
          line: 195,
          column: 13
        }
      },
      "108": {
        start: {
          line: 193,
          column: 16
        },
        end: {
          line: 193,
          column: 80
        }
      },
      "109": {
        start: {
          line: 194,
          column: 16
        },
        end: {
          line: 194,
          column: 23
        }
      },
      "110": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 199,
          column: 13
        }
      },
      "111": {
        start: {
          line: 198,
          column: 16
        },
        end: {
          line: 198,
          column: 58
        }
      },
      "112": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 45
        }
      },
      "113": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 36
        }
      },
      "114": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 202,
          column: 42
        }
      },
      "115": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 33
        }
      },
      "116": {
        start: {
          line: 205,
          column: 12
        },
        end: {
          line: 210,
          column: 15
        }
      },
      "117": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 89
        }
      },
      "118": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 67
        }
      },
      "119": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 72
        }
      },
      "120": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 258,
          column: 9
        }
      },
      "121": {
        start: {
          line: 220,
          column: 61
        },
        end: {
          line: 220,
          column: 65
        }
      },
      "122": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 224,
          column: 13
        }
      },
      "123": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 222,
          column: 73
        }
      },
      "124": {
        start: {
          line: 223,
          column: 16
        },
        end: {
          line: 223,
          column: 23
        }
      },
      "125": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 229,
          column: 14
        }
      },
      "126": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 233,
          column: 13
        }
      },
      "127": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 231,
          column: 90
        }
      },
      "128": {
        start: {
          line: 232,
          column: 16
        },
        end: {
          line: 232,
          column: 23
        }
      },
      "129": {
        start: {
          line: 235,
          column: 31
        },
        end: {
          line: 235,
          column: 71
        }
      },
      "130": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 239,
          column: 13
        }
      },
      "131": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 237,
          column: 95
        }
      },
      "132": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 23
        }
      },
      "133": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 240,
          column: 37
        }
      },
      "134": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 43
        }
      },
      "135": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 242,
          column: 77
        }
      },
      "136": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 245,
          column: 13
        }
      },
      "137": {
        start: {
          line: 244,
          column: 16
        },
        end: {
          line: 244,
          column: 61
        }
      },
      "138": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 33
        }
      },
      "139": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 252,
          column: 15
        }
      },
      "140": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 253,
          column: 90
        }
      },
      "141": {
        start: {
          line: 256,
          column: 12
        },
        end: {
          line: 256,
          column: 68
        }
      },
      "142": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 257,
          column: 74
        }
      },
      "143": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 320,
          column: 9
        }
      },
      "144": {
        start: {
          line: 262,
          column: 44
        },
        end: {
          line: 262,
          column: 48
        }
      },
      "145": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 266,
          column: 13
        }
      },
      "146": {
        start: {
          line: 264,
          column: 16
        },
        end: {
          line: 264,
          column: 75
        }
      },
      "147": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 265,
          column: 23
        }
      },
      "148": {
        start: {
          line: 267,
          column: 35
        },
        end: {
          line: 267,
          column: 83
        }
      },
      "149": {
        start: {
          line: 268,
          column: 12
        },
        end: {
          line: 271,
          column: 13
        }
      },
      "150": {
        start: {
          line: 269,
          column: 16
        },
        end: {
          line: 269,
          column: 75
        }
      },
      "151": {
        start: {
          line: 270,
          column: 16
        },
        end: {
          line: 270,
          column: 23
        }
      },
      "152": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 275,
          column: 14
        }
      },
      "153": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 279,
          column: 13
        }
      },
      "154": {
        start: {
          line: 277,
          column: 16
        },
        end: {
          line: 277,
          column: 71
        }
      },
      "155": {
        start: {
          line: 278,
          column: 16
        },
        end: {
          line: 278,
          column: 23
        }
      },
      "156": {
        start: {
          line: 281,
          column: 33
        },
        end: {
          line: 284,
          column: 14
        }
      },
      "157": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 288,
          column: 13
        }
      },
      "158": {
        start: {
          line: 286,
          column: 16
        },
        end: {
          line: 286,
          column: 67
        }
      },
      "159": {
        start: {
          line: 287,
          column: 16
        },
        end: {
          line: 287,
          column: 23
        }
      },
      "160": {
        start: {
          line: 290,
          column: 42
        },
        end: {
          line: 290,
          column: 120
        }
      },
      "161": {
        start: {
          line: 290,
          column: 76
        },
        end: {
          line: 290,
          column: 113
        }
      },
      "162": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 306,
          column: 13
        }
      },
      "163": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 293,
          column: 77
        }
      },
      "164": {
        start: {
          line: 294,
          column: 16
        },
        end: {
          line: 294,
          column: 80
        }
      },
      "165": {
        start: {
          line: 298,
          column: 16
        },
        end: {
          line: 300,
          column: 17
        }
      },
      "166": {
        start: {
          line: 299,
          column: 20
        },
        end: {
          line: 299,
          column: 43
        }
      },
      "167": {
        start: {
          line: 301,
          column: 16
        },
        end: {
          line: 305,
          column: 19
        }
      },
      "168": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 307,
          column: 33
        }
      },
      "169": {
        start: {
          line: 309,
          column: 12
        },
        end: {
          line: 314,
          column: 15
        }
      },
      "170": {
        start: {
          line: 315,
          column: 12
        },
        end: {
          line: 315,
          column: 107
        }
      },
      "171": {
        start: {
          line: 318,
          column: 12
        },
        end: {
          line: 318,
          column: 71
        }
      },
      "172": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 319,
          column: 76
        }
      },
      "173": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 324,
          column: 19
        }
      },
      "174": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 324,
          column: 19
        }
      },
      "175": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 331,
          column: 11
        }
      },
      "176": {
        start: {
          line: 333,
          column: 30
        },
        end: {
          line: 333,
          column: 71
        }
      },
      "177": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 334,
          column: 38
        }
      },
      "178": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 59
        }
      },
      "179": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 337,
          column: 43
        }
      },
      "180": {
        start: {
          line: 339,
          column: 8
        },
        end: {
          line: 339,
          column: 58
        }
      },
      "181": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 341,
          column: 75
        }
      },
      "182": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 345,
          column: 19
        }
      },
      "183": {
        start: {
          line: 345,
          column: 12
        },
        end: {
          line: 345,
          column: 19
        }
      },
      "184": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 358,
          column: 9
        }
      },
      "185": {
        start: {
          line: 347,
          column: 34
        },
        end: {
          line: 350,
          column: 28
        }
      },
      "186": {
        start: {
          line: 351,
          column: 12
        },
        end: {
          line: 353,
          column: 15
        }
      },
      "187": {
        start: {
          line: 352,
          column: 16
        },
        end: {
          line: 352,
          column: 64
        }
      },
      "188": {
        start: {
          line: 354,
          column: 12
        },
        end: {
          line: 354,
          column: 108
        }
      },
      "189": {
        start: {
          line: 357,
          column: 12
        },
        end: {
          line: 357,
          column: 78
        }
      },
      "190": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 417,
          column: 9
        }
      },
      "191": {
        start: {
          line: 362,
          column: 80
        },
        end: {
          line: 362,
          column: 84
        }
      },
      "192": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 366,
          column: 13
        }
      },
      "193": {
        start: {
          line: 364,
          column: 16
        },
        end: {
          line: 364,
          column: 74
        }
      },
      "194": {
        start: {
          line: 365,
          column: 16
        },
        end: {
          line: 365,
          column: 23
        }
      },
      "195": {
        start: {
          line: 368,
          column: 33
        },
        end: {
          line: 368,
          column: 91
        }
      },
      "196": {
        start: {
          line: 369,
          column: 12
        },
        end: {
          line: 372,
          column: 13
        }
      },
      "197": {
        start: {
          line: 370,
          column: 16
        },
        end: {
          line: 370,
          column: 94
        }
      },
      "198": {
        start: {
          line: 371,
          column: 16
        },
        end: {
          line: 371,
          column: 23
        }
      },
      "199": {
        start: {
          line: 374,
          column: 31
        },
        end: {
          line: 374,
          column: 98
        }
      },
      "200": {
        start: {
          line: 374,
          column: 67
        },
        end: {
          line: 374,
          column: 97
        }
      },
      "201": {
        start: {
          line: 376,
          column: 28
        },
        end: {
          line: 384,
          column: 14
        }
      },
      "202": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 385,
          column: 33
        }
      },
      "203": {
        start: {
          line: 387,
          column: 12
        },
        end: {
          line: 387,
          column: 58
        }
      },
      "204": {
        start: {
          line: 389,
          column: 37
        },
        end: {
          line: 391,
          column: 68
        }
      },
      "205": {
        start: {
          line: 393,
          column: 12
        },
        end: {
          line: 396,
          column: 15
        }
      },
      "206": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 401,
          column: 15
        }
      },
      "207": {
        start: {
          line: 403,
          column: 12
        },
        end: {
          line: 411,
          column: 13
        }
      },
      "208": {
        start: {
          line: 404,
          column: 16
        },
        end: {
          line: 404,
          column: 45
        }
      },
      "209": {
        start: {
          line: 405,
          column: 16
        },
        end: {
          line: 405,
          column: 49
        }
      },
      "210": {
        start: {
          line: 406,
          column: 16
        },
        end: {
          line: 406,
          column: 37
        }
      },
      "211": {
        start: {
          line: 407,
          column: 16
        },
        end: {
          line: 410,
          column: 19
        }
      },
      "212": {
        start: {
          line: 412,
          column: 12
        },
        end: {
          line: 412,
          column: 105
        }
      },
      "213": {
        start: {
          line: 415,
          column: 12
        },
        end: {
          line: 415,
          column: 67
        }
      },
      "214": {
        start: {
          line: 416,
          column: 12
        },
        end: {
          line: 416,
          column: 72
        }
      },
      "215": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 435,
          column: 9
        }
      },
      "216": {
        start: {
          line: 421,
          column: 34
        },
        end: {
          line: 421,
          column: 38
        }
      },
      "217": {
        start: {
          line: 422,
          column: 28
        },
        end: {
          line: 425,
          column: 29
        }
      },
      "218": {
        start: {
          line: 426,
          column: 12
        },
        end: {
          line: 431,
          column: 13
        }
      },
      "219": {
        start: {
          line: 427,
          column: 16
        },
        end: {
          line: 430,
          column: 19
        }
      },
      "220": {
        start: {
          line: 434,
          column: 12
        },
        end: {
          line: 434,
          column: 84
        }
      },
      "221": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 463,
          column: 9
        }
      },
      "222": {
        start: {
          line: 439,
          column: 50
        },
        end: {
          line: 439,
          column: 54
        }
      },
      "223": {
        start: {
          line: 440,
          column: 12
        },
        end: {
          line: 441,
          column: 23
        }
      },
      "224": {
        start: {
          line: 441,
          column: 16
        },
        end: {
          line: 441,
          column: 23
        }
      },
      "225": {
        start: {
          line: 443,
          column: 28
        },
        end: {
          line: 446,
          column: 29
        }
      },
      "226": {
        start: {
          line: 447,
          column: 12
        },
        end: {
          line: 459,
          column: 13
        }
      },
      "227": {
        start: {
          line: 449,
          column: 37
        },
        end: {
          line: 449,
          column: 95
        }
      },
      "228": {
        start: {
          line: 450,
          column: 16
        },
        end: {
          line: 452,
          column: 17
        }
      },
      "229": {
        start: {
          line: 451,
          column: 20
        },
        end: {
          line: 451,
          column: 138
        }
      },
      "230": {
        start: {
          line: 454,
          column: 16
        },
        end: {
          line: 458,
          column: 19
        }
      },
      "231": {
        start: {
          line: 462,
          column: 12
        },
        end: {
          line: 462,
          column: 80
        }
      },
      "232": {
        start: {
          line: 466,
          column: 35
        },
        end: {
          line: 466,
          column: 39
        }
      },
      "233": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 468,
          column: 19
        }
      },
      "234": {
        start: {
          line: 468,
          column: 12
        },
        end: {
          line: 468,
          column: 19
        }
      },
      "235": {
        start: {
          line: 469,
          column: 26
        },
        end: {
          line: 469,
          column: 62
        }
      },
      "236": {
        start: {
          line: 470,
          column: 8
        },
        end: {
          line: 474,
          column: 11
        }
      },
      "237": {
        start: {
          line: 476,
          column: 8
        },
        end: {
          line: 480,
          column: 11
        }
      },
      "238": {
        start: {
          line: 483,
          column: 35
        },
        end: {
          line: 483,
          column: 39
        }
      },
      "239": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 485,
          column: 19
        }
      },
      "240": {
        start: {
          line: 485,
          column: 12
        },
        end: {
          line: 485,
          column: 19
        }
      },
      "241": {
        start: {
          line: 486,
          column: 26
        },
        end: {
          line: 486,
          column: 62
        }
      },
      "242": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 487,
          column: 43
        }
      },
      "243": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 493,
          column: 11
        }
      },
      "244": {
        start: {
          line: 496,
          column: 27
        },
        end: {
          line: 496,
          column: 31
        }
      },
      "245": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 498,
          column: 19
        }
      },
      "246": {
        start: {
          line: 498,
          column: 12
        },
        end: {
          line: 498,
          column: 19
        }
      },
      "247": {
        start: {
          line: 499,
          column: 21
        },
        end: {
          line: 499,
          column: 56
        }
      },
      "248": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 505,
          column: 9
        }
      },
      "249": {
        start: {
          line: 501,
          column: 12
        },
        end: {
          line: 501,
          column: 33
        }
      },
      "250": {
        start: {
          line: 502,
          column: 12
        },
        end: {
          line: 502,
          column: 39
        }
      },
      "251": {
        start: {
          line: 503,
          column: 12
        },
        end: {
          line: 503,
          column: 54
        }
      },
      "252": {
        start: {
          line: 504,
          column: 12
        },
        end: {
          line: 504,
          column: 60
        }
      },
      "253": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 509,
          column: 19
        }
      },
      "254": {
        start: {
          line: 509,
          column: 12
        },
        end: {
          line: 509,
          column: 19
        }
      },
      "255": {
        start: {
          line: 510,
          column: 8
        },
        end: {
          line: 510,
          column: 92
        }
      },
      "256": {
        start: {
          line: 512,
          column: 30
        },
        end: {
          line: 512,
          column: 71
        }
      },
      "257": {
        start: {
          line: 513,
          column: 33
        },
        end: {
          line: 513,
          column: 77
        }
      },
      "258": {
        start: {
          line: 513,
          column: 60
        },
        end: {
          line: 513,
          column: 76
        }
      },
      "259": {
        start: {
          line: 514,
          column: 8
        },
        end: {
          line: 523,
          column: 9
        }
      },
      "260": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 515,
          column: 66
        }
      },
      "261": {
        start: {
          line: 519,
          column: 12
        },
        end: {
          line: 519,
          column: 51
        }
      },
      "262": {
        start: {
          line: 520,
          column: 12
        },
        end: {
          line: 520,
          column: 51
        }
      },
      "263": {
        start: {
          line: 522,
          column: 12
        },
        end: {
          line: 522,
          column: 63
        }
      },
      "264": {
        start: {
          line: 525,
          column: 8
        },
        end: {
          line: 534,
          column: 9
        }
      },
      "265": {
        start: {
          line: 526,
          column: 12
        },
        end: {
          line: 533,
          column: 13
        }
      },
      "266": {
        start: {
          line: 527,
          column: 16
        },
        end: {
          line: 527,
          column: 45
        }
      },
      "267": {
        start: {
          line: 528,
          column: 16
        },
        end: {
          line: 532,
          column: 19
        }
      },
      "268": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 541,
          column: 11
        }
      },
      "269": {
        start: {
          line: 545,
          column: 8
        },
        end: {
          line: 557,
          column: 18
        }
      },
      "270": {
        start: {
          line: 546,
          column: 24
        },
        end: {
          line: 546,
          column: 34
        }
      },
      "271": {
        start: {
          line: 547,
          column: 12
        },
        end: {
          line: 556,
          column: 13
        }
      },
      "272": {
        start: {
          line: 548,
          column: 16
        },
        end: {
          line: 555,
          column: 17
        }
      },
      "273": {
        start: {
          line: 549,
          column: 20
        },
        end: {
          line: 549,
          column: 49
        }
      },
      "274": {
        start: {
          line: 550,
          column: 20
        },
        end: {
          line: 554,
          column: 23
        }
      },
      "275": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 561,
          column: 53
        }
      },
      "276": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 564,
          column: 44
        }
      },
      "277": {
        start: {
          line: 567,
          column: 21
        },
        end: {
          line: 567,
          column: 49
        }
      },
      "278": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 568,
          column: 46
        }
      },
      "279": {
        start: {
          line: 571,
          column: 30
        },
        end: {
          line: 571,
          column: 58
        }
      },
      "280": {
        start: {
          line: 572,
          column: 8
        },
        end: {
          line: 576,
          column: 9
        }
      },
      "281": {
        start: {
          line: 573,
          column: 12
        },
        end: {
          line: 575,
          column: 15
        }
      },
      "282": {
        start: {
          line: 574,
          column: 16
        },
        end: {
          line: 574,
          column: 72
        }
      },
      "283": {
        start: {
          line: 579,
          column: 8
        },
        end: {
          line: 579,
          column: 23
        }
      },
      "284": {
        start: {
          line: 582,
          column: 0
        },
        end: {
          line: 582,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 5
          }
        },
        loc: {
          start: {
            line: 14,
            column: 24
          },
          end: {
            line: 29,
            column: 5
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 30
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 21
          }
        },
        loc: {
          start: {
            line: 32,
            column: 44
          },
          end: {
            line: 51,
            column: 9
          }
        },
        line: 32
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 53,
            column: 5
          }
        },
        loc: {
          start: {
            line: 53,
            column: 25
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 53
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 54,
            column: 33
          },
          end: {
            line: 54,
            column: 34
          }
        },
        loc: {
          start: {
            line: 54,
            column: 45
          },
          end: {
            line: 76,
            column: 9
          }
        },
        line: 54
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 59,
            column: 38
          },
          end: {
            line: 59,
            column: 39
          }
        },
        loc: {
          start: {
            line: 59,
            column: 48
          },
          end: {
            line: 59,
            column: 84
          }
        },
        line: 59
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 60,
            column: 43
          },
          end: {
            line: 60,
            column: 44
          }
        },
        loc: {
          start: {
            line: 60,
            column: 53
          },
          end: {
            line: 60,
            column: 94
          }
        },
        line: 60
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 61,
            column: 38
          },
          end: {
            line: 61,
            column: 39
          }
        },
        loc: {
          start: {
            line: 61,
            column: 48
          },
          end: {
            line: 61,
            column: 84
          }
        },
        line: 61
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 62,
            column: 38
          },
          end: {
            line: 62,
            column: 39
          }
        },
        loc: {
          start: {
            line: 62,
            column: 48
          },
          end: {
            line: 62,
            column: 84
          }
        },
        line: 62
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 63,
            column: 40
          },
          end: {
            line: 63,
            column: 41
          }
        },
        loc: {
          start: {
            line: 63,
            column: 50
          },
          end: {
            line: 63,
            column: 88
          }
        },
        line: 63
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 64,
            column: 42
          },
          end: {
            line: 64,
            column: 43
          }
        },
        loc: {
          start: {
            line: 64,
            column: 52
          },
          end: {
            line: 64,
            column: 92
          }
        },
        line: 64
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 66,
            column: 43
          },
          end: {
            line: 66,
            column: 44
          }
        },
        loc: {
          start: {
            line: 66,
            column: 53
          },
          end: {
            line: 66,
            column: 94
          }
        },
        line: 66
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 67,
            column: 44
          },
          end: {
            line: 67,
            column: 45
          }
        },
        loc: {
          start: {
            line: 67,
            column: 54
          },
          end: {
            line: 67,
            column: 96
          }
        },
        line: 67
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 68,
            column: 45
          },
          end: {
            line: 68,
            column: 46
          }
        },
        loc: {
          start: {
            line: 68,
            column: 55
          },
          end: {
            line: 68,
            column: 98
          }
        },
        line: 68
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 70,
            column: 38
          },
          end: {
            line: 70,
            column: 39
          }
        },
        loc: {
          start: {
            line: 70,
            column: 48
          },
          end: {
            line: 70,
            column: 84
          }
        },
        line: 70
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 71,
            column: 37
          },
          end: {
            line: 71,
            column: 38
          }
        },
        loc: {
          start: {
            line: 71,
            column: 47
          },
          end: {
            line: 71,
            column: 82
          }
        },
        line: 71
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 73,
            column: 39
          },
          end: {
            line: 73,
            column: 40
          }
        },
        loc: {
          start: {
            line: 73,
            column: 49
          },
          end: {
            line: 73,
            column: 86
          }
        },
        line: 73
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 75,
            column: 36
          },
          end: {
            line: 75,
            column: 37
          }
        },
        loc: {
          start: {
            line: 75,
            column: 42
          },
          end: {
            line: 75,
            column: 75
          }
        },
        line: 75
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 5
          }
        },
        loc: {
          start: {
            line: 78,
            column: 47
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 78
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 5
          }
        },
        loc: {
          start: {
            line: 103,
            column: 48
          },
          end: {
            line: 110,
            column: 5
          }
        },
        line: 103
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 111,
            column: 5
          }
        },
        loc: {
          start: {
            line: 111,
            column: 49
          },
          end: {
            line: 172,
            column: 5
          }
        },
        line: 111
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 122,
            column: 50
          },
          end: {
            line: 122,
            column: 51
          }
        },
        loc: {
          start: {
            line: 122,
            column: 56
          },
          end: {
            line: 122,
            column: 89
          }
        },
        line: 122
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 123,
            column: 56
          },
          end: {
            line: 123,
            column: 57
          }
        },
        loc: {
          start: {
            line: 123,
            column: 74
          },
          end: {
            line: 131,
            column: 17
          }
        },
        line: 123
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 154,
            column: 36
          },
          end: {
            line: 154,
            column: 37
          }
        },
        loc: {
          start: {
            line: 154,
            column: 53
          },
          end: {
            line: 161,
            column: 13
          }
        },
        line: 154
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 157,
            column: 42
          },
          end: {
            line: 157,
            column: 43
          }
        },
        loc: {
          start: {
            line: 157,
            column: 54
          },
          end: {
            line: 159,
            column: 21
          }
        },
        line: 157
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        loc: {
          start: {
            line: 173,
            column: 42
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 173
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        loc: {
          start: {
            line: 218,
            column: 44
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 218
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 260,
            column: 5
          }
        },
        loc: {
          start: {
            line: 260,
            column: 46
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 260
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 290,
            column: 71
          },
          end: {
            line: 290,
            column: 72
          }
        },
        loc: {
          start: {
            line: 290,
            column: 76
          },
          end: {
            line: 290,
            column: 113
          }
        },
        line: 290
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        loc: {
          start: {
            line: 322,
            column: 30
          },
          end: {
            line: 342,
            column: 5
          }
        },
        line: 322
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 343,
            column: 4
          },
          end: {
            line: 343,
            column: 5
          }
        },
        loc: {
          start: {
            line: 343,
            column: 40
          },
          end: {
            line: 359,
            column: 5
          }
        },
        line: 343
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 351,
            column: 34
          },
          end: {
            line: 351,
            column: 35
          }
        },
        loc: {
          start: {
            line: 351,
            column: 50
          },
          end: {
            line: 353,
            column: 13
          }
        },
        line: 351
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        loc: {
          start: {
            line: 360,
            column: 42
          },
          end: {
            line: 418,
            column: 5
          }
        },
        line: 360
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 374,
            column: 62
          },
          end: {
            line: 374,
            column: 63
          }
        },
        loc: {
          start: {
            line: 374,
            column: 67
          },
          end: {
            line: 374,
            column: 97
          }
        },
        line: 374
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 419,
            column: 4
          },
          end: {
            line: 419,
            column: 5
          }
        },
        loc: {
          start: {
            line: 419,
            column: 48
          },
          end: {
            line: 436,
            column: 5
          }
        },
        line: 419
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 437,
            column: 4
          },
          end: {
            line: 437,
            column: 5
          }
        },
        loc: {
          start: {
            line: 437,
            column: 42
          },
          end: {
            line: 464,
            column: 5
          }
        },
        line: 437
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 465,
            column: 4
          },
          end: {
            line: 465,
            column: 5
          }
        },
        loc: {
          start: {
            line: 465,
            column: 42
          },
          end: {
            line: 481,
            column: 5
          }
        },
        line: 465
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        },
        loc: {
          start: {
            line: 482,
            column: 41
          },
          end: {
            line: 494,
            column: 5
          }
        },
        line: 482
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 495,
            column: 4
          },
          end: {
            line: 495,
            column: 5
          }
        },
        loc: {
          start: {
            line: 495,
            column: 37
          },
          end: {
            line: 506,
            column: 5
          }
        },
        line: 495
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 507,
            column: 4
          },
          end: {
            line: 507,
            column: 5
          }
        },
        loc: {
          start: {
            line: 507,
            column: 33
          },
          end: {
            line: 535,
            column: 5
          }
        },
        line: 507
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 513,
            column: 54
          },
          end: {
            line: 513,
            column: 55
          }
        },
        loc: {
          start: {
            line: 513,
            column: 60
          },
          end: {
            line: 513,
            column: 76
          }
        },
        line: 513
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 536,
            column: 5
          }
        },
        loc: {
          start: {
            line: 536,
            column: 40
          },
          end: {
            line: 542,
            column: 5
          }
        },
        line: 536
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 543,
            column: 4
          },
          end: {
            line: 543,
            column: 5
          }
        },
        loc: {
          start: {
            line: 543,
            column: 27
          },
          end: {
            line: 558,
            column: 5
          }
        },
        line: 543
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 545,
            column: 20
          },
          end: {
            line: 545,
            column: 21
          }
        },
        loc: {
          start: {
            line: 545,
            column: 26
          },
          end: {
            line: 557,
            column: 9
          }
        },
        line: 545
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 560,
            column: 4
          },
          end: {
            line: 560,
            column: 5
          }
        },
        loc: {
          start: {
            line: 560,
            column: 21
          },
          end: {
            line: 562,
            column: 5
          }
        },
        line: 560
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 563,
            column: 4
          },
          end: {
            line: 563,
            column: 5
          }
        },
        loc: {
          start: {
            line: 563,
            column: 25
          },
          end: {
            line: 565,
            column: 5
          }
        },
        line: 563
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 566,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        },
        loc: {
          start: {
            line: 566,
            column: 26
          },
          end: {
            line: 569,
            column: 5
          }
        },
        line: 566
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 570,
            column: 4
          },
          end: {
            line: 570,
            column: 5
          }
        },
        loc: {
          start: {
            line: 570,
            column: 49
          },
          end: {
            line: 577,
            column: 5
          }
        },
        line: 570
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 573,
            column: 34
          },
          end: {
            line: 573,
            column: 35
          }
        },
        loc: {
          start: {
            line: 573,
            column: 46
          },
          end: {
            line: 575,
            column: 13
          }
        },
        line: 573
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 578,
            column: 4
          },
          end: {
            line: 578,
            column: 5
          }
        },
        loc: {
          start: {
            line: 578,
            column: 12
          },
          end: {
            line: 580,
            column: 5
          }
        },
        line: 578
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 20,
            column: 24
          },
          end: {
            line: 20,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 24
          },
          end: {
            line: 20,
            column: 48
          }
        }, {
          start: {
            line: 20,
            column: 52
          },
          end: {
            line: 20,
            column: 75
          }
        }],
        line: 20
      },
      "4": {
        loc: {
          start: {
            line: 34,
            column: 30
          },
          end: {
            line: 34,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 30
          },
          end: {
            line: 34,
            column: 57
          }
        }, {
          start: {
            line: 34,
            column: 61
          },
          end: {
            line: 34,
            column: 114
          }
        }],
        line: 34
      },
      "5": {
        loc: {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 37,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 37,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "6": {
        loc: {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 42,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 42,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "7": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 81,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 81,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "8": {
        loc: {
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 80,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 12
          },
          end: {
            line: 80,
            column: 26
          }
        }, {
          start: {
            line: 80,
            column: 30
          },
          end: {
            line: 80,
            column: 45
          }
        }],
        line: 80
      },
      "9": {
        loc: {
          start: {
            line: 89,
            column: 12
          },
          end: {
            line: 96,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 12
          },
          end: {
            line: 96,
            column: 13
          }
        }, {
          start: {
            line: 94,
            column: 17
          },
          end: {
            line: 96,
            column: 13
          }
        }],
        line: 89
      },
      "10": {
        loc: {
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 106,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 106,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "11": {
        loc: {
          start: {
            line: 113,
            column: 36
          },
          end: {
            line: 113,
            column: 63
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 113,
            column: 55
          },
          end: {
            line: 113,
            column: 63
          }
        }],
        line: 113
      },
      "12": {
        loc: {
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 117,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 12
          },
          end: {
            line: 117,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "13": {
        loc: {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 30
          }
        }, {
          start: {
            line: 114,
            column: 34
          },
          end: {
            line: 114,
            column: 49
          }
        }],
        line: 114
      },
      "14": {
        loc: {
          start: {
            line: 126,
            column: 26
          },
          end: {
            line: 126,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 60
          },
          end: {
            line: 126,
            column: 67
          }
        }, {
          start: {
            line: 126,
            column: 70
          },
          end: {
            line: 126,
            column: 78
          }
        }],
        line: 126
      },
      "15": {
        loc: {
          start: {
            line: 133,
            column: 23
          },
          end: {
            line: 133,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 54
          },
          end: {
            line: 133,
            column: 59
          }
        }, {
          start: {
            line: 133,
            column: 62
          },
          end: {
            line: 133,
            column: 71
          }
        }],
        line: 133
      },
      "16": {
        loc: {
          start: {
            line: 134,
            column: 25
          },
          end: {
            line: 134,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 134,
            column: 35
          },
          end: {
            line: 134,
            column: 73
          }
        }, {
          start: {
            line: 134,
            column: 76
          },
          end: {
            line: 134,
            column: 85
          }
        }],
        line: 134
      },
      "17": {
        loc: {
          start: {
            line: 139,
            column: 37
          },
          end: {
            line: 139,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 139,
            column: 69
          },
          end: {
            line: 139,
            column: 70
          }
        }, {
          start: {
            line: 139,
            column: 73
          },
          end: {
            line: 139,
            column: 75
          }
        }],
        line: 139
      },
      "18": {
        loc: {
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 160,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 160,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "19": {
        loc: {
          start: {
            line: 176,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "20": {
        loc: {
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 176,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 176,
            column: 30
          }
        }, {
          start: {
            line: 176,
            column: 34
          },
          end: {
            line: 176,
            column: 44
          }
        }, {
          start: {
            line: 176,
            column: 48
          },
          end: {
            line: 176,
            column: 56
          }
        }],
        line: 176
      },
      "21": {
        loc: {
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "22": {
        loc: {
          start: {
            line: 192,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 12
          },
          end: {
            line: 195,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 192
      },
      "23": {
        loc: {
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 199,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 199,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "24": {
        loc: {
          start: {
            line: 220,
            column: 31
          },
          end: {
            line: 220,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 220,
            column: 51
          },
          end: {
            line: 220,
            column: 56
          }
        }],
        line: 220
      },
      "25": {
        loc: {
          start: {
            line: 221,
            column: 12
          },
          end: {
            line: 224,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 12
          },
          end: {
            line: 224,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "26": {
        loc: {
          start: {
            line: 221,
            column: 16
          },
          end: {
            line: 221,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 16
          },
          end: {
            line: 221,
            column: 30
          }
        }, {
          start: {
            line: 221,
            column: 34
          },
          end: {
            line: 221,
            column: 44
          }
        }],
        line: 221
      },
      "27": {
        loc: {
          start: {
            line: 230,
            column: 12
          },
          end: {
            line: 233,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 12
          },
          end: {
            line: 233,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "28": {
        loc: {
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "29": {
        loc: {
          start: {
            line: 236,
            column: 16
          },
          end: {
            line: 236,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 16
          },
          end: {
            line: 236,
            column: 33
          }
        }, {
          start: {
            line: 236,
            column: 37
          },
          end: {
            line: 236,
            column: 64
          }
        }],
        line: 236
      },
      "30": {
        loc: {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "31": {
        loc: {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 266,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 266,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "32": {
        loc: {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 263,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 263,
            column: 30
          }
        }, {
          start: {
            line: 263,
            column: 34
          },
          end: {
            line: 263,
            column: 44
          }
        }, {
          start: {
            line: 263,
            column: 48
          },
          end: {
            line: 263,
            column: 57
          }
        }],
        line: 263
      },
      "33": {
        loc: {
          start: {
            line: 268,
            column: 12
          },
          end: {
            line: 271,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 268,
            column: 12
          },
          end: {
            line: 271,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 268
      },
      "34": {
        loc: {
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 279,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 279,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "35": {
        loc: {
          start: {
            line: 285,
            column: 12
          },
          end: {
            line: 288,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 12
          },
          end: {
            line: 288,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 285
      },
      "36": {
        loc: {
          start: {
            line: 290,
            column: 42
          },
          end: {
            line: 290,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 42
          },
          end: {
            line: 290,
            column: 114
          }
        }, {
          start: {
            line: 290,
            column: 118
          },
          end: {
            line: 290,
            column: 120
          }
        }],
        line: 290
      },
      "37": {
        loc: {
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 306,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 306,
            column: 13
          }
        }, {
          start: {
            line: 296,
            column: 17
          },
          end: {
            line: 306,
            column: 13
          }
        }],
        line: 291
      },
      "38": {
        loc: {
          start: {
            line: 298,
            column: 16
          },
          end: {
            line: 300,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 298,
            column: 16
          },
          end: {
            line: 300,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 298
      },
      "39": {
        loc: {
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 324,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 324,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "40": {
        loc: {
          start: {
            line: 333,
            column: 30
          },
          end: {
            line: 333,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 333,
            column: 30
          },
          end: {
            line: 333,
            column: 65
          }
        }, {
          start: {
            line: 333,
            column: 69
          },
          end: {
            line: 333,
            column: 71
          }
        }],
        line: 333
      },
      "41": {
        loc: {
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 345,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 8
          },
          end: {
            line: 345,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "42": {
        loc: {
          start: {
            line: 362,
            column: 45
          },
          end: {
            line: 362,
            column: 65
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 362,
            column: 59
          },
          end: {
            line: 362,
            column: 65
          }
        }],
        line: 362
      },
      "43": {
        loc: {
          start: {
            line: 363,
            column: 12
          },
          end: {
            line: 366,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 363,
            column: 12
          },
          end: {
            line: 366,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 363
      },
      "44": {
        loc: {
          start: {
            line: 363,
            column: 16
          },
          end: {
            line: 363,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 363,
            column: 16
          },
          end: {
            line: 363,
            column: 30
          }
        }, {
          start: {
            line: 363,
            column: 34
          },
          end: {
            line: 363,
            column: 49
          }
        }, {
          start: {
            line: 363,
            column: 53
          },
          end: {
            line: 363,
            column: 61
          }
        }],
        line: 363
      },
      "45": {
        loc: {
          start: {
            line: 369,
            column: 12
          },
          end: {
            line: 372,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 369,
            column: 12
          },
          end: {
            line: 372,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 369
      },
      "46": {
        loc: {
          start: {
            line: 369,
            column: 16
          },
          end: {
            line: 369,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 369,
            column: 16
          },
          end: {
            line: 369,
            column: 29
          }
        }, {
          start: {
            line: 369,
            column: 33
          },
          end: {
            line: 369,
            column: 111
          }
        }],
        line: 369
      },
      "47": {
        loc: {
          start: {
            line: 403,
            column: 12
          },
          end: {
            line: 411,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 403,
            column: 12
          },
          end: {
            line: 411,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 403
      },
      "48": {
        loc: {
          start: {
            line: 403,
            column: 16
          },
          end: {
            line: 403,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 403,
            column: 16
          },
          end: {
            line: 403,
            column: 26
          }
        }, {
          start: {
            line: 403,
            column: 30
          },
          end: {
            line: 403,
            column: 73
          }
        }],
        line: 403
      },
      "49": {
        loc: {
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 431,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 12
          },
          end: {
            line: 431,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "50": {
        loc: {
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 441,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 441,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "51": {
        loc: {
          start: {
            line: 447,
            column: 12
          },
          end: {
            line: 459,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 447,
            column: 12
          },
          end: {
            line: 459,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 447
      },
      "52": {
        loc: {
          start: {
            line: 450,
            column: 16
          },
          end: {
            line: 452,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 450,
            column: 16
          },
          end: {
            line: 452,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 450
      },
      "53": {
        loc: {
          start: {
            line: 467,
            column: 8
          },
          end: {
            line: 468,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 467,
            column: 8
          },
          end: {
            line: 468,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 467
      },
      "54": {
        loc: {
          start: {
            line: 467,
            column: 12
          },
          end: {
            line: 467,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 467,
            column: 12
          },
          end: {
            line: 467,
            column: 26
          }
        }, {
          start: {
            line: 467,
            column: 30
          },
          end: {
            line: 467,
            column: 45
          }
        }],
        line: 467
      },
      "55": {
        loc: {
          start: {
            line: 484,
            column: 8
          },
          end: {
            line: 485,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 484,
            column: 8
          },
          end: {
            line: 485,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 484
      },
      "56": {
        loc: {
          start: {
            line: 484,
            column: 12
          },
          end: {
            line: 484,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 484,
            column: 12
          },
          end: {
            line: 484,
            column: 26
          }
        }, {
          start: {
            line: 484,
            column: 30
          },
          end: {
            line: 484,
            column: 45
          }
        }],
        line: 484
      },
      "57": {
        loc: {
          start: {
            line: 497,
            column: 8
          },
          end: {
            line: 498,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 497,
            column: 8
          },
          end: {
            line: 498,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 497
      },
      "58": {
        loc: {
          start: {
            line: 497,
            column: 12
          },
          end: {
            line: 497,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 497,
            column: 12
          },
          end: {
            line: 497,
            column: 26
          }
        }, {
          start: {
            line: 497,
            column: 30
          },
          end: {
            line: 497,
            column: 74
          }
        }],
        line: 497
      },
      "59": {
        loc: {
          start: {
            line: 500,
            column: 8
          },
          end: {
            line: 505,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 500,
            column: 8
          },
          end: {
            line: 505,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 500
      },
      "60": {
        loc: {
          start: {
            line: 508,
            column: 8
          },
          end: {
            line: 509,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 508,
            column: 8
          },
          end: {
            line: 509,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 508
      },
      "61": {
        loc: {
          start: {
            line: 512,
            column: 30
          },
          end: {
            line: 512,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 512,
            column: 30
          },
          end: {
            line: 512,
            column: 65
          }
        }, {
          start: {
            line: 512,
            column: 69
          },
          end: {
            line: 512,
            column: 71
          }
        }],
        line: 512
      },
      "62": {
        loc: {
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 523,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 523,
            column: 9
          }
        }, {
          start: {
            line: 517,
            column: 13
          },
          end: {
            line: 523,
            column: 9
          }
        }],
        line: 514
      },
      "63": {
        loc: {
          start: {
            line: 526,
            column: 12
          },
          end: {
            line: 533,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 12
          },
          end: {
            line: 533,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      },
      "64": {
        loc: {
          start: {
            line: 548,
            column: 16
          },
          end: {
            line: 555,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 548,
            column: 16
          },
          end: {
            line: 555,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 548
      },
      "65": {
        loc: {
          start: {
            line: 568,
            column: 15
          },
          end: {
            line: 568,
            column: 45
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 568,
            column: 22
          },
          end: {
            line: 568,
            column: 33
          }
        }, {
          start: {
            line: 568,
            column: 36
          },
          end: {
            line: 568,
            column: 45
          }
        }],
        line: 568
      },
      "66": {
        loc: {
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 576,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 576,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 572
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0],
      "43": [0, 0],
      "44": [0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\socketService.ts",
      mappings: ";;;;;;AAAA,yCAA6D;AAE7D,gEAA+B;AAC/B,uCAAiC;AACjC,qDAA4C;AAC5C,yDAA+D;AAC/D,4CAAyC;AAqBzC,MAAa,aAAa;IAMxB,YAAY,MAAkB;QAJtB,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QACjD,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QACjD,gBAAW,GAA0B,IAAI,GAAG,EAAE,CAAC,CAAC,wBAAwB;QAG9E,IAAI,CAAC,EAAE,GAAG,IAAI,kBAAc,CAAC,MAAM,EAAE;YACnC,IAAI,EAAE;gBACJ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;gBAC3D,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;gBACxB,WAAW,EAAE,IAAI;aAClB;YACD,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,eAAe;QACrB,4BAA4B;QAC5B,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,MAAW,EAAE,IAAI,EAAE,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnG,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;gBAC1D,CAAC;gBAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAQ,CAAC;gBAClE,MAAM,IAAI,GAAG,MAAM,iBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAEjE,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC3C,CAAC;gBAED,MAAM,CAAC,MAAM,GAAI,IAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;gBACnB,IAAI,EAAE,CAAC;YACT,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAA2B,EAAE,EAAE;YACvD,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,MAAM,0BAA0B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAExE,yBAAyB;YACzB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE/B,iBAAiB;YACjB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YACpF,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9E,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAElF,sBAAsB;YACtB,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YACpF,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YACtF,MAAM,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAExF,gBAAgB;YAChB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1E,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAExE,gBAAgB;YAChB,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;YAE5E,mBAAmB;YACnB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAA2B,EAAE,IAAS;QACzE,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc;YAAE,OAAO;QAE9C,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;gBAC9C,GAAG,EAAE,cAAc;gBACnB,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC;gBAC9C,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,MAAM,wBAAwB,cAAc,EAAE,CAAC,CAAC;gBAE5E,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAA2B,EAAE,IAAS;QAC1E,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,cAAc;YAAE,OAAO;QAE5B,MAAM,CAAC,KAAK,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC;QAC/C,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,MAAM,sBAAsB,cAAc,EAAE,CAAC,CAAC;QAE1E,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,MAA2B,EAAE,IAAS;QAC3E,IAAI,CAAC;YACH,MAAM,EAAE,cAAc,EAAE,gBAAgB,GAAG,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YAE7E,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YAED,mCAAmC;YACnC,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAEzE,2CAA2C;YAC3C,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC;gBACpC,YAAY,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC/D,kBAAkB,EAAE,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;oBACxD,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACzC,QAAQ,EAAE,IAAI,IAAI,EAAE;oBACpB,IAAI,EAAE,aAAa,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;oBAC1D,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,WAAW,EAAE,CAAC;oBACd,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;gBACH,gBAAgB;gBAChB,KAAK,EAAE,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBACvD,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC1D,QAAQ,EAAE;oBACR,gBAAgB,EAAE,IAAI;oBACtB,oBAAoB,EAAE,IAAI;oBAC1B,oBAAoB,EAAE,IAAI;oBAC1B,eAAe,EAAE,gBAAgB,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACvD,kBAAkB,EAAE,KAAK;oBACzB,4BAA4B,EAAE,KAAK;iBACpC;gBACD,SAAS,EAAE;oBACT,aAAa,EAAE,CAAC;oBAChB,iBAAiB,EAAE,eAAe,CAAC,MAAM;oBACzC,mBAAmB,EAAE,CAAC;oBACtB,cAAc,EAAE,IAAI,IAAI,EAAE;oBAC1B,gBAAgB,EAAE,CAAC;oBACnB,iBAAiB,EAAE,CAAC;iBACrB;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;YAE1B,iDAAiD;YACjD,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACtC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC1D,IAAI,aAAa,EAAE,CAAC;oBAClB,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAC/B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,gBAAgB,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;oBACvE,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,0BAA0B;YAC1B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC1E,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;aACtC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,gBAAgB,YAAY,CAAC,GAAG,oBAAoB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACnF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAA2B,EAAE,IAAS;QACpE,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;gBACpC,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE,MAAM,CAAC,MAAM;gBACvB,WAAW,EAAE,MAAM;gBACnB,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,iDAAiD;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC5D,IAAI,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;gBAChE,OAAO;YACT,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACtB,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;YAC5C,CAAC;YAED,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACjC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;YACxB,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAE9B,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YAErB,iCAAiC;YACjC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1E,SAAS;gBACT,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,mBAAmB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAA2B,EAAE,IAAS;QACtE,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAC;gBACzD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;gBACpC,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE,MAAM,CAAC,MAAM;gBACvB,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC,CAAC;gBAC1E,OAAO;YACT,CAAC;YAED,wDAAwD;YACxD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC5D,IAAI,iBAAiB,IAAI,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC,CAAC;gBAC/E,OAAO;YACT,CAAC;YAED,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,OAAO,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,CAAC,OAAO,GAAG,0BAA0B,CAAC;YAC/C,CAAC;YAED,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YAErB,qCAAqC;YACrC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC3E,SAAS;gBACT,kBAAkB,EAAE,iBAAiB;gBACrC,SAAS,EAAE,MAAM,CAAC,MAAM;aACzB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,oBAAoB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAA2B,EAAE,IAAS;QACxE,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAErC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAC3D,OAAO;YACT,CAAC;YAED,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACxE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAC3D,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;gBACpC,GAAG,EAAE,SAAS;gBACd,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,iDAAiD;YACjD,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;gBAC9C,GAAG,EAAE,OAAO,CAAC,cAAc;gBAC3B,YAAY,EAAE,MAAM,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;gBACnD,OAAO;YACT,CAAC;YAED,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,OAAO,CAAC,SAAS,EAAE,SAAS,CACxD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,MAAM,CAC3C,IAAI,CAAC,CAAC,CAAC;YAER,IAAI,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC/B,2BAA2B;gBAC3B,OAAO,CAAC,SAAU,CAAC,qBAAqB,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC9D,OAAO,CAAC,SAAU,CAAC,qBAAqB,CAAC,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACN,mBAAmB;gBACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;oBACvB,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;gBACzB,CAAC;gBACD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;oBACrB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;oBACzC,QAAQ;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YAErB,qCAAqC;YACrC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5E,SAAS;gBACT,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ;gBACR,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,MAAM,uBAAuB,SAAS,SAAS,QAAQ,EAAE,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAA2B;QACnD,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO;QAE3B,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnB,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,MAAM,EAAE,QAAQ;SACjB,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAChE,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAEnD,wCAAwC;QACxC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEnC,sCAAsC;QACtC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAElD,+CAA+C;QAC/C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAA2B;QAC7D,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO;QAE3B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,2BAAY,CAAC,IAAI,CAAC;gBAC5C,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEjB,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBACnC,MAAM,CAAC,IAAI,CAAC,gBAAgB,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,MAAM,WAAW,aAAa,CAAC,MAAM,qBAAqB,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAA2B,EAAE,IAAS;QACpE,IAAI,CAAC;YACH,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,GAAG,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;gBAC1D,OAAO;YACT,CAAC;YAED,oDAAoD;YACpD,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACzF,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC,CAAC;gBAC9E,OAAO;YACT,CAAC;YAED,6CAA6C;YAC7C,MAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YAEvF,iBAAiB;YACjB,MAAM,OAAO,GAAG,IAAI,sBAAO,CAAC;gBAC1B,cAAc;gBACd,QAAQ,EAAE,MAAM,CAAC,MAAM;gBACvB,UAAU;gBACV,WAAW;gBACX,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;gBACvB,QAAQ;gBACR,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YAErB,mCAAmC;YACnC,MAAM,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE9C,gCAAgC;YAChC,MAAM,gBAAgB,GAAG,MAAM,sBAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;iBACzD,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC;iBACjD,QAAQ,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;YAEvD,4BAA4B;YAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;gBAC/D,OAAO,EAAE,gBAAgB;gBACzB,cAAc;aACf,CAAC,CAAC;YAEH,uCAAuC;YACvC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,gBAAgB;aAC1B,CAAC,CAAC;YAEH,2DAA2D;YAC3D,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;gBAC9D,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;gBAC7B,OAAO,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;gBACjC,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;gBAErB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBACrE,SAAS,EAAE,OAAO,CAAC,GAAG;oBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC,CAAC,CAAC;YACL,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,oBAAoB,cAAc,EAAE,CAAC,CAAC;QACtF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAA4B,EAAE,IAAS;QAC1E,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;YAE3B,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBACzD,MAAM,EAAE,WAAW;gBACnB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;YAElB,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBAC7E,SAAS;oBACT,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAA2B,EAAE,IAAS;QACpE,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;YAE3C,IAAI,CAAC,MAAM,CAAC,MAAM;gBAAE,OAAO;YAE3B,wBAAwB;YACxB,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,iBAAiB,CAAC,SAAS,EAAE;gBACzD,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,IAAI,IAAI,EAAE;aACnB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;YAElB,IAAI,OAAO,EAAE,CAAC;gBACZ,mCAAmC;gBACnC,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;gBACjE,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,YAAY,CAAC,UAAU,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;gBAClG,CAAC;gBAED,kCAAkC;gBAClC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE;oBAChE,SAAS;oBACT,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAA2B,EAAE,IAAS;QACpE,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc;YAAE,OAAO;QAE9C,MAAM,SAAS,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE;YAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,cAAc;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,gDAAgD;QAChD,MAAM,CAAC,EAAE,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;YAC9D,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,cAAc;YACd,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAA2B,EAAE,IAAS;QACnE,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc;YAAE,OAAO;QAE9C,MAAM,SAAS,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEnC,8CAA8C;QAC9C,MAAM,CAAC,EAAE,CAAC,gBAAgB,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;YAC9D,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,cAAc;YACd,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,MAA2B,EAAE,IAAS;QAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO;QAE3E,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE1C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,MAA2B;QACtD,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO;QAE3B,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,MAAM,6BAA6B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAE3E,wCAAwC;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAChE,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;QAEtE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,kCAAkC;YAClC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEvC,2BAA2B;YAC3B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,sBAAsB;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC7B,MAAM,CAAC,EAAE,CAAC,gBAAgB,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;oBACzE,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,cAAc,EAAE,UAAU,CAAC,cAAc;oBACzC,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,MAAc,EAAE,MAAc;QACxD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACjC,MAAM;YACN,MAAM;YACN,QAAQ,EAAE,IAAI,IAAI,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,kDAAkD;QAClD,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC3D,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,aAAa;oBACzE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC7B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;wBAC1E,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,cAAc,EAAE,UAAU,CAAC,cAAc;wBACzC,QAAQ,EAAE,KAAK;qBAChB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAED,kCAAkC;IAC3B,cAAc;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEM,YAAY,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAEM,aAAa,CAAC,MAAc;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IACxC,CAAC;IAEM,sBAAsB,CAAC,MAAc,EAAE,YAAiB;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC/B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;CACF;AA5pBD,sCA4pBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\socketService.ts"],
      sourcesContent: ["import { Server as SocketIOServer, Socket } from 'socket.io';\r\nimport { Server as HTTPServer } from 'http';\r\nimport jwt from 'jsonwebtoken';\r\nimport { Types } from 'mongoose';\r\nimport { User } from '../models/User.model';\r\nimport { Conversation, Message } from '../models/Conversation';\r\nimport { logger } from '../utils/logger';\r\n\r\n\r\ninterface AuthenticatedSocket extends Socket {\r\n  userId?: string;\r\n  user?: any;\r\n}\r\n\r\ninterface OnlineUser {\r\n  userId: string;\r\n  socketId: string;\r\n  lastSeen: Date;\r\n  status: 'online' | 'away' | 'busy' | 'offline';\r\n}\r\n\r\ninterface TypingUser {\r\n  userId: string;\r\n  conversationId: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport class SocketService {\r\n  private io: SocketIOServer;\r\n  private onlineUsers: Map<string, OnlineUser> = new Map();\r\n  private typingUsers: Map<string, TypingUser> = new Map();\r\n  private userSockets: Map<string, string[]> = new Map(); // userId -> socketIds[]\r\n\r\n  constructor(server: HTTPServer) {\r\n    this.io = new SocketIOServer(server, {\r\n      cors: {\r\n        origin: process.env.FRONTEND_URL || \"http://localhost:3000\",\r\n        methods: [\"GET\", \"POST\"],\r\n        credentials: true\r\n      },\r\n      transports: ['websocket', 'polling']\r\n    });\r\n\r\n    this.setupMiddleware();\r\n    this.setupEventHandlers();\r\n    this.startCleanupInterval();\r\n  }\r\n\r\n  private setupMiddleware(): void {\r\n    // Authentication middleware\r\n    this.io.use(async (socket: any, next) => {\r\n      try {\r\n        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];\r\n        \r\n        if (!token) {\r\n          return next(new Error('Authentication token required'));\r\n        }\r\n\r\n        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\r\n        const user = await User.findById(decoded.id).select('-password');\r\n        \r\n        if (!user) {\r\n          return next(new Error('User not found'));\r\n        }\r\n\r\n        socket.userId = (user as any)._id.toString();\r\n        socket.user = user;\r\n        next();\r\n      } catch (error) {\r\n        logger.error('Socket authentication error:', error);\r\n        next(new Error('Authentication failed'));\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    this.io.on('connection', (socket: AuthenticatedSocket) => {\r\n      logger.info(`User ${socket.userId} connected with socket ${socket.id}`);\r\n      \r\n      // Handle user connection\r\n      this.handleUserConnect(socket);\r\n\r\n      // Message events\r\n      socket.on('send_message', (data) => this.handleSendMessage(socket, data));\r\n      socket.on('message_delivered', (data) => this.handleMessageDelivered(socket, data));\r\n      socket.on('message_read', (data) => this.handleMessageRead(socket, data));\r\n      socket.on('edit_message', (data) => this.handleEditMessage(socket, data));\r\n      socket.on('delete_message', (data) => this.handleDeleteMessage(socket, data));\r\n      socket.on('react_to_message', (data) => this.handleMessageReaction(socket, data));\r\n\r\n      // Conversation events\r\n      socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));\r\n      socket.on('leave_conversation', (data) => this.handleLeaveConversation(socket, data));\r\n      socket.on('create_conversation', (data) => this.handleCreateConversation(socket, data));\r\n\r\n      // Typing events\r\n      socket.on('typing_start', (data) => this.handleTypingStart(socket, data));\r\n      socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));\r\n\r\n      // Status events\r\n      socket.on('status_change', (data) => this.handleStatusChange(socket, data));\r\n\r\n      // Disconnect event\r\n      socket.on('disconnect', () => this.handleUserDisconnect(socket));\r\n    });\r\n  }\r\n\r\n  private async handleJoinConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n\r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    try {\r\n      // Verify user is participant in conversation\r\n      const conversation = await Conversation.findOne({\r\n        _id: conversationId,\r\n        participants: socket.userId,\r\n        status: 'active'\r\n      });\r\n\r\n      if (conversation) {\r\n        socket.join(`conversation_${conversationId}`);\r\n        logger.debug(`User ${socket.userId} joined conversation ${conversationId}`);\r\n\r\n        socket.emit('conversation_joined', { conversationId });\r\n      } else {\r\n        socket.emit('error', { message: 'Cannot join conversation' });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error joining conversation:', error);\r\n      socket.emit('error', { message: 'Failed to join conversation' });\r\n    }\r\n  }\r\n\r\n  private async handleLeaveConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n\r\n    if (!conversationId) return;\r\n\r\n    socket.leave(`conversation_${conversationId}`);\r\n    logger.debug(`User ${socket.userId} left conversation ${conversationId}`);\r\n\r\n    socket.emit('conversation_left', { conversationId });\r\n  }\r\n\r\n  private async handleCreateConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { participantIds, conversationType = 'direct', title, matchId } = data;\r\n\r\n      if (!socket.userId || !participantIds) {\r\n        socket.emit('error', { message: 'Invalid conversation data' });\r\n        return;\r\n      }\r\n\r\n      // Add current user to participants\r\n      const allParticipants = [...new Set([socket.userId, ...participantIds])];\r\n\r\n      // Create conversation (simplified version)\r\n      const conversation = new Conversation({\r\n        participants: allParticipants.map(id => new Types.ObjectId(id)),\r\n        participantDetails: allParticipants.map(participantId => ({\r\n          userId: new Types.ObjectId(participantId),\r\n          joinedAt: new Date(),\r\n          role: participantId === socket.userId ? 'admin' : 'member',\r\n          isActive: true,\r\n          lastSeenAt: new Date(),\r\n          unreadCount: 0,\r\n          isMuted: false\r\n        })),\r\n        conversationType,\r\n        title: conversationType === 'group' ? title : undefined,\r\n        matchId: matchId ? new Types.ObjectId(matchId) : undefined,\r\n        settings: {\r\n          allowFileSharing: true,\r\n          allowLocationSharing: true,\r\n          allowPropertySharing: true,\r\n          maxParticipants: conversationType === 'direct' ? 2 : 50,\r\n          autoDeleteMessages: false,\r\n          requireApprovalForNewMembers: false\r\n        },\r\n        analytics: {\r\n          totalMessages: 0,\r\n          totalParticipants: allParticipants.length,\r\n          averageResponseTime: 0,\r\n          lastActivityAt: new Date(),\r\n          messagesThisWeek: 0,\r\n          messagesThisMonth: 0\r\n        }\r\n      });\r\n\r\n      await conversation.save();\r\n\r\n      // Join all participants to the conversation room\r\n      allParticipants.forEach(participantId => {\r\n        const userSocketIds = this.userSockets.get(participantId);\r\n        if (userSocketIds) {\r\n          userSocketIds.forEach(socketId => {\r\n            this.io.to(socketId).socketsJoin(`conversation_${conversation._id}`);\r\n          });\r\n        }\r\n      });\r\n\r\n      // Notify all participants\r\n      this.io.to(`conversation_${conversation._id}`).emit('conversation_created', {\r\n        conversation: conversation.toObject()\r\n      });\r\n\r\n      logger.info(`Conversation ${conversation._id} created by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error creating conversation:', error);\r\n      socket.emit('error', { message: 'Failed to create conversation' });\r\n    }\r\n  }\r\n\r\n  private async handleEditMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, content } = data;\r\n\r\n      if (!socket.userId || !messageId || !content) {\r\n        socket.emit('error', { message: 'Invalid edit data' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        senderId: socket.userId,\r\n        messageType: 'text',\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found or cannot be edited' });\r\n        return;\r\n      }\r\n\r\n      // Check if message is too old to edit (24 hours)\r\n      const messageAge = Date.now() - message.createdAt.getTime();\r\n      if (messageAge > 24 * 60 * 60 * 1000) {\r\n        socket.emit('error', { message: 'Message is too old to edit' });\r\n        return;\r\n      }\r\n\r\n      // Store original content if not already edited\r\n      if (!message.isEdited) {\r\n        message.originalContent = message.content;\r\n      }\r\n\r\n      message.content = content.trim();\r\n      message.isEdited = true;\r\n      message.editedAt = new Date();\r\n\r\n      await message.save();\r\n\r\n      // Broadcast edit to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_edited', {\r\n        messageId,\r\n        content: message.content,\r\n        isEdited: true,\r\n        editedAt: message.editedAt\r\n      });\r\n\r\n      logger.info(`Message ${messageId} edited by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error editing message:', error);\r\n      socket.emit('error', { message: 'Failed to edit message' });\r\n    }\r\n  }\r\n\r\n  private async handleDeleteMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, deleteForEveryone = false } = data;\r\n\r\n      if (!socket.userId || !messageId) {\r\n        socket.emit('error', { message: 'Invalid delete data' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        senderId: socket.userId,\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found or already deleted' });\r\n        return;\r\n      }\r\n\r\n      // Check if user can delete for everyone (within 1 hour)\r\n      const messageAge = Date.now() - message.createdAt.getTime();\r\n      if (deleteForEveryone && messageAge > 60 * 60 * 1000) {\r\n        socket.emit('error', { message: 'Message is too old to delete for everyone' });\r\n        return;\r\n      }\r\n\r\n      message.isDeleted = true;\r\n      message.deletedAt = new Date();\r\n      message.deletedBy = new Types.ObjectId(socket.userId);\r\n\r\n      if (deleteForEveryone) {\r\n        message.content = 'This message was deleted';\r\n      }\r\n\r\n      await message.save();\r\n\r\n      // Broadcast deletion to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_deleted', {\r\n        messageId,\r\n        deletedForEveryone: deleteForEveryone,\r\n        deletedBy: socket.userId\r\n      });\r\n\r\n      logger.info(`Message ${messageId} deleted by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error deleting message:', error);\r\n      socket.emit('error', { message: 'Failed to delete message' });\r\n    }\r\n  }\r\n\r\n  private async handleMessageReaction(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, reaction } = data;\r\n\r\n      if (!socket.userId || !messageId || !reaction) {\r\n        socket.emit('error', { message: 'Invalid reaction data' });\r\n        return;\r\n      }\r\n\r\n      const validReactions = ['like', 'love', 'laugh', 'wow', 'sad', 'angry'];\r\n      if (!validReactions.includes(reaction)) {\r\n        socket.emit('error', { message: 'Invalid reaction type' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found' });\r\n        return;\r\n      }\r\n\r\n      // Verify user is participant in the conversation\r\n      const conversation = await Conversation.findOne({\r\n        _id: message.conversationId,\r\n        participants: socket.userId\r\n      });\r\n\r\n      if (!conversation) {\r\n        socket.emit('error', { message: 'Access denied' });\r\n        return;\r\n      }\r\n\r\n      // Check if user already reacted\r\n      const existingReactionIndex = message.reactions?.findIndex(\r\n        r => r.userId.toString() === socket.userId\r\n      ) ?? -1;\r\n\r\n      if (existingReactionIndex > -1) {\r\n        // Update existing reaction\r\n        message.reactions![existingReactionIndex].reaction = reaction;\r\n        message.reactions![existingReactionIndex].createdAt = new Date();\r\n      } else {\r\n        // Add new reaction\r\n        if (!message.reactions) {\r\n          message.reactions = [];\r\n        }\r\n        message.reactions.push({\r\n          userId: new Types.ObjectId(socket.userId),\r\n          reaction,\r\n          createdAt: new Date()\r\n        });\r\n      }\r\n\r\n      await message.save();\r\n\r\n      // Broadcast reaction to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_reaction', {\r\n        messageId,\r\n        userId: socket.userId,\r\n        reaction,\r\n        reactions: message.reactions\r\n      });\r\n\r\n      logger.info(`User ${socket.userId} reacted to message ${messageId} with ${reaction}`);\r\n    } catch (error) {\r\n      logger.error('Error reacting to message:', error);\r\n      socket.emit('error', { message: 'Failed to react to message' });\r\n    }\r\n  }\r\n\r\n  private handleUserConnect(socket: AuthenticatedSocket): void {\r\n    if (!socket.userId) return;\r\n\r\n    // Add to online users\r\n    this.onlineUsers.set(socket.userId, {\r\n      userId: socket.userId,\r\n      socketId: socket.id,\r\n      lastSeen: new Date(),\r\n      status: 'online'\r\n    });\r\n\r\n    // Track multiple sockets per user\r\n    const userSocketIds = this.userSockets.get(socket.userId) || [];\r\n    userSocketIds.push(socket.id);\r\n    this.userSockets.set(socket.userId, userSocketIds);\r\n\r\n    // Join user to their conversation rooms\r\n    this.joinUserConversations(socket);\r\n\r\n    // Broadcast online status to contacts\r\n    this.broadcastUserStatus(socket.userId, 'online');\r\n\r\n    // Send online users list to the connected user\r\n    socket.emit('online_users', Array.from(this.onlineUsers.values()));\r\n  }\r\n\r\n  private async joinUserConversations(socket: AuthenticatedSocket): Promise<void> {\r\n    if (!socket.userId) return;\r\n\r\n    try {\r\n      const conversations = await Conversation.find({\r\n        participants: socket.userId,\r\n        status: 'active'\r\n      }).select('_id');\r\n\r\n      conversations.forEach(conversation => {\r\n        socket.join(`conversation_${conversation._id}`);\r\n      });\r\n\r\n      logger.info(`User ${socket.userId} joined ${conversations.length} conversation rooms`);\r\n    } catch (error) {\r\n      logger.error('Error joining user conversations:', error);\r\n    }\r\n  }\r\n\r\n  private async handleSendMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { conversationId, content, messageType = 'text', metadata } = data;\r\n\r\n      if (!socket.userId || !conversationId || !content) {\r\n        socket.emit('error', { message: 'Invalid message data' });\r\n        return;\r\n      }\r\n\r\n      // Verify user can send message to this conversation\r\n      const conversation = await Conversation.findById(conversationId);\r\n      if (!conversation || !conversation.canUserSendMessage(new Types.ObjectId(socket.userId))) {\r\n        socket.emit('error', { message: 'Cannot send message to this conversation' });\r\n        return;\r\n      }\r\n\r\n      // Get receiver ID (for direct conversations)\r\n      const receiverId = conversation.participants.find(p => p.toString() !== socket.userId);\r\n\r\n      // Create message\r\n      const message = new Message({\r\n        conversationId,\r\n        senderId: socket.userId,\r\n        receiverId,\r\n        messageType,\r\n        content: content.trim(),\r\n        metadata,\r\n        status: 'sent'\r\n      });\r\n\r\n      await message.save();\r\n\r\n      // Update conversation last message\r\n      await conversation.updateLastMessage(message);\r\n\r\n      // Populate message for response\r\n      const populatedMessage = await Message.findById(message._id)\r\n        .populate('senderId', 'firstName lastName avatar')\r\n        .populate('receiverId', 'firstName lastName avatar');\r\n\r\n      // Emit to conversation room\r\n      this.io.to(`conversation_${conversationId}`).emit('new_message', {\r\n        message: populatedMessage,\r\n        conversationId\r\n      });\r\n\r\n      // Send delivery confirmation to sender\r\n      socket.emit('message_sent', {\r\n        tempId: data.tempId,\r\n        message: populatedMessage\r\n      });\r\n\r\n      // Update message status to delivered if receiver is online\r\n      if (receiverId && this.onlineUsers.has(receiverId.toString())) {\r\n        message.status = 'delivered';\r\n        message.deliveredAt = new Date();\r\n        await message.save();\r\n\r\n        this.io.to(`conversation_${conversationId}`).emit('message_delivered', {\r\n          messageId: message._id,\r\n          deliveredAt: message.deliveredAt\r\n        });\r\n      }\r\n\r\n      logger.info(`Message sent from ${socket.userId} to conversation ${conversationId}`);\r\n    } catch (error) {\r\n      logger.error('Error sending message:', error);\r\n      socket.emit('error', { message: 'Failed to send message' });\r\n    }\r\n  }\r\n\r\n  private async handleMessageDelivered(_socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId } = data;\r\n\r\n      const message = await Message.findByIdAndUpdate(messageId, {\r\n        status: 'delivered',\r\n        deliveredAt: new Date()\r\n      }, { new: true });\r\n\r\n      if (message) {\r\n        this.io.to(`conversation_${message.conversationId}`).emit('message_delivered', {\r\n          messageId,\r\n          deliveredAt: message.deliveredAt\r\n        });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error updating message delivery status:', error);\r\n    }\r\n  }\r\n\r\n  private async handleMessageRead(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, conversationId } = data;\r\n\r\n      if (!socket.userId) return;\r\n\r\n      // Update message status\r\n      const message = await Message.findByIdAndUpdate(messageId, {\r\n        status: 'read',\r\n        readAt: new Date()\r\n      }, { new: true });\r\n\r\n      if (message) {\r\n        // Update conversation unread count\r\n        const conversation = await Conversation.findById(conversationId);\r\n        if (conversation) {\r\n          await conversation.markAsRead(new Types.ObjectId(socket.userId), new Types.ObjectId(messageId));\r\n        }\r\n\r\n        // Notify sender about read status\r\n        this.io.to(`conversation_${conversationId}`).emit('message_read', {\r\n          messageId,\r\n          readAt: message.readAt,\r\n          readBy: socket.userId\r\n        });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error updating message read status:', error);\r\n    }\r\n  }\r\n\r\n  private async handleTypingStart(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n    \r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    const typingKey = `${socket.userId}_${conversationId}`;\r\n    this.typingUsers.set(typingKey, {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      timestamp: new Date()\r\n    });\r\n\r\n    // Broadcast typing status to other participants\r\n    socket.to(`conversation_${conversationId}`).emit('user_typing', {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      isTyping: true\r\n    });\r\n  }\r\n\r\n  private async handleTypingStop(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n    \r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    const typingKey = `${socket.userId}_${conversationId}`;\r\n    this.typingUsers.delete(typingKey);\r\n\r\n    // Broadcast typing stop to other participants\r\n    socket.to(`conversation_${conversationId}`).emit('user_typing', {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      isTyping: false\r\n    });\r\n  }\r\n\r\n  private handleStatusChange(socket: AuthenticatedSocket, data: any): void {\r\n    const { status } = data;\r\n    \r\n    if (!socket.userId || !['online', 'away', 'busy'].includes(status)) return;\r\n\r\n    const user = this.onlineUsers.get(socket.userId);\r\n    if (user) {\r\n      user.status = status;\r\n      user.lastSeen = new Date();\r\n      this.onlineUsers.set(socket.userId, user);\r\n      \r\n      this.broadcastUserStatus(socket.userId, status);\r\n    }\r\n  }\r\n\r\n  private handleUserDisconnect(socket: AuthenticatedSocket): void {\r\n    if (!socket.userId) return;\r\n\r\n    logger.info(`User ${socket.userId} disconnected from socket ${socket.id}`);\r\n\r\n    // Remove socket from user's socket list\r\n    const userSocketIds = this.userSockets.get(socket.userId) || [];\r\n    const updatedSocketIds = userSocketIds.filter(id => id !== socket.id);\r\n    \r\n    if (updatedSocketIds.length > 0) {\r\n      this.userSockets.set(socket.userId, updatedSocketIds);\r\n    } else {\r\n      // User has no more active sockets\r\n      this.userSockets.delete(socket.userId);\r\n      this.onlineUsers.delete(socket.userId);\r\n      \r\n      // Broadcast offline status\r\n      this.broadcastUserStatus(socket.userId, 'offline');\r\n    }\r\n\r\n    // Clear typing status\r\n    for (const [key, typingUser] of this.typingUsers.entries()) {\r\n      if (typingUser.userId === socket.userId) {\r\n        this.typingUsers.delete(key);\r\n        socket.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {\r\n          userId: socket.userId,\r\n          conversationId: typingUser.conversationId,\r\n          isTyping: false\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  private broadcastUserStatus(userId: string, status: string): void {\r\n    this.io.emit('user_status_change', {\r\n      userId,\r\n      status,\r\n      lastSeen: new Date()\r\n    });\r\n  }\r\n\r\n  private startCleanupInterval(): void {\r\n    // Clean up old typing indicators every 30 seconds\r\n    setInterval(() => {\r\n      const now = new Date();\r\n      for (const [key, typingUser] of this.typingUsers.entries()) {\r\n        if (now.getTime() - typingUser.timestamp.getTime() > 30000) { // 30 seconds\r\n          this.typingUsers.delete(key);\r\n          this.io.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {\r\n            userId: typingUser.userId,\r\n            conversationId: typingUser.conversationId,\r\n            isTyping: false\r\n          });\r\n        }\r\n      }\r\n    }, 30000);\r\n  }\r\n\r\n  // Public methods for external use\r\n  public getOnlineUsers(): OnlineUser[] {\r\n    return Array.from(this.onlineUsers.values());\r\n  }\r\n\r\n  public isUserOnline(userId: string): boolean {\r\n    return this.onlineUsers.has(userId);\r\n  }\r\n\r\n  public getUserStatus(userId: string): string {\r\n    const user = this.onlineUsers.get(userId);\r\n    return user ? user.status : 'offline';\r\n  }\r\n\r\n  public sendNotificationToUser(userId: string, notification: any): void {\r\n    const userSocketIds = this.userSockets.get(userId);\r\n    if (userSocketIds) {\r\n      userSocketIds.forEach(socketId => {\r\n        this.io.to(socketId).emit('notification', notification);\r\n      });\r\n    }\r\n  }\r\n\r\n  public getIO(): SocketIOServer {\r\n    return this.io;\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c84f691ecba13365824b93f7d2680a189dc51d7d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1utmstr375 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1utmstr375();
var __importDefault =
/* istanbul ignore next */
(cov_1utmstr375().s[0]++,
/* istanbul ignore next */
(cov_1utmstr375().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1utmstr375().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1utmstr375().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1utmstr375().f[0]++;
  cov_1utmstr375().s[1]++;
  return /* istanbul ignore next */(cov_1utmstr375().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1utmstr375().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1utmstr375().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1utmstr375().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1utmstr375().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1utmstr375().s[3]++;
exports.SocketService = void 0;
const socket_io_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[4]++, require("socket.io"));
const jsonwebtoken_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[5]++, __importDefault(require("jsonwebtoken")));
const mongoose_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[6]++, require("mongoose"));
const User_model_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[7]++, require("../models/User.model"));
const Conversation_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[8]++, require("../models/Conversation"));
const logger_1 =
/* istanbul ignore next */
(cov_1utmstr375().s[9]++, require("../utils/logger"));
class SocketService {
  constructor(server) {
    /* istanbul ignore next */
    cov_1utmstr375().f[1]++;
    cov_1utmstr375().s[10]++;
    this.onlineUsers = new Map();
    /* istanbul ignore next */
    cov_1utmstr375().s[11]++;
    this.typingUsers = new Map();
    /* istanbul ignore next */
    cov_1utmstr375().s[12]++;
    this.userSockets = new Map(); // userId -> socketIds[]
    /* istanbul ignore next */
    cov_1utmstr375().s[13]++;
    this.io = new socket_io_1.Server(server, {
      cors: {
        origin:
        /* istanbul ignore next */
        (cov_1utmstr375().b[3][0]++, process.env.FRONTEND_URL) ||
        /* istanbul ignore next */
        (cov_1utmstr375().b[3][1]++, "http://localhost:3000"),
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });
    /* istanbul ignore next */
    cov_1utmstr375().s[14]++;
    this.setupMiddleware();
    /* istanbul ignore next */
    cov_1utmstr375().s[15]++;
    this.setupEventHandlers();
    /* istanbul ignore next */
    cov_1utmstr375().s[16]++;
    this.startCleanupInterval();
  }
  setupMiddleware() {
    /* istanbul ignore next */
    cov_1utmstr375().f[2]++;
    cov_1utmstr375().s[17]++;
    // Authentication middleware
    this.io.use(async (socket, next) => {
      /* istanbul ignore next */
      cov_1utmstr375().f[3]++;
      cov_1utmstr375().s[18]++;
      try {
        const token =
        /* istanbul ignore next */
        (cov_1utmstr375().s[19]++,
        /* istanbul ignore next */
        (cov_1utmstr375().b[4][0]++, socket.handshake.auth.token) ||
        /* istanbul ignore next */
        (cov_1utmstr375().b[4][1]++, socket.handshake.headers.authorization?.split(' ')[1]));
        /* istanbul ignore next */
        cov_1utmstr375().s[20]++;
        if (!token) {
          /* istanbul ignore next */
          cov_1utmstr375().b[5][0]++;
          cov_1utmstr375().s[21]++;
          return next(new Error('Authentication token required'));
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[5][1]++;
        }
        const decoded =
        /* istanbul ignore next */
        (cov_1utmstr375().s[22]++, jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET));
        const user =
        /* istanbul ignore next */
        (cov_1utmstr375().s[23]++, await User_model_1.User.findById(decoded.id).select('-password'));
        /* istanbul ignore next */
        cov_1utmstr375().s[24]++;
        if (!user) {
          /* istanbul ignore next */
          cov_1utmstr375().b[6][0]++;
          cov_1utmstr375().s[25]++;
          return next(new Error('User not found'));
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[6][1]++;
        }
        cov_1utmstr375().s[26]++;
        socket.userId = user._id.toString();
        /* istanbul ignore next */
        cov_1utmstr375().s[27]++;
        socket.user = user;
        /* istanbul ignore next */
        cov_1utmstr375().s[28]++;
        next();
      } catch (error) {
        /* istanbul ignore next */
        cov_1utmstr375().s[29]++;
        logger_1.logger.error('Socket authentication error:', error);
        /* istanbul ignore next */
        cov_1utmstr375().s[30]++;
        next(new Error('Authentication failed'));
      }
    });
  }
  setupEventHandlers() {
    /* istanbul ignore next */
    cov_1utmstr375().f[4]++;
    cov_1utmstr375().s[31]++;
    this.io.on('connection', socket => {
      /* istanbul ignore next */
      cov_1utmstr375().f[5]++;
      cov_1utmstr375().s[32]++;
      logger_1.logger.info(`User ${socket.userId} connected with socket ${socket.id}`);
      // Handle user connection
      /* istanbul ignore next */
      cov_1utmstr375().s[33]++;
      this.handleUserConnect(socket);
      // Message events
      /* istanbul ignore next */
      cov_1utmstr375().s[34]++;
      socket.on('send_message', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[6]++;
        cov_1utmstr375().s[35]++;
        return this.handleSendMessage(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[36]++;
      socket.on('message_delivered', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[7]++;
        cov_1utmstr375().s[37]++;
        return this.handleMessageDelivered(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[38]++;
      socket.on('message_read', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[8]++;
        cov_1utmstr375().s[39]++;
        return this.handleMessageRead(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[40]++;
      socket.on('edit_message', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[9]++;
        cov_1utmstr375().s[41]++;
        return this.handleEditMessage(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[42]++;
      socket.on('delete_message', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[10]++;
        cov_1utmstr375().s[43]++;
        return this.handleDeleteMessage(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[44]++;
      socket.on('react_to_message', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[11]++;
        cov_1utmstr375().s[45]++;
        return this.handleMessageReaction(socket, data);
      });
      // Conversation events
      /* istanbul ignore next */
      cov_1utmstr375().s[46]++;
      socket.on('join_conversation', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[12]++;
        cov_1utmstr375().s[47]++;
        return this.handleJoinConversation(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[48]++;
      socket.on('leave_conversation', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[13]++;
        cov_1utmstr375().s[49]++;
        return this.handleLeaveConversation(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[50]++;
      socket.on('create_conversation', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[14]++;
        cov_1utmstr375().s[51]++;
        return this.handleCreateConversation(socket, data);
      });
      // Typing events
      /* istanbul ignore next */
      cov_1utmstr375().s[52]++;
      socket.on('typing_start', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[15]++;
        cov_1utmstr375().s[53]++;
        return this.handleTypingStart(socket, data);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[54]++;
      socket.on('typing_stop', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[16]++;
        cov_1utmstr375().s[55]++;
        return this.handleTypingStop(socket, data);
      });
      // Status events
      /* istanbul ignore next */
      cov_1utmstr375().s[56]++;
      socket.on('status_change', data => {
        /* istanbul ignore next */
        cov_1utmstr375().f[17]++;
        cov_1utmstr375().s[57]++;
        return this.handleStatusChange(socket, data);
      });
      // Disconnect event
      /* istanbul ignore next */
      cov_1utmstr375().s[58]++;
      socket.on('disconnect', () => {
        /* istanbul ignore next */
        cov_1utmstr375().f[18]++;
        cov_1utmstr375().s[59]++;
        return this.handleUserDisconnect(socket);
      });
    });
  }
  async handleJoinConversation(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[19]++;
    const {
      conversationId
    } =
    /* istanbul ignore next */
    (cov_1utmstr375().s[60]++, data);
    /* istanbul ignore next */
    cov_1utmstr375().s[61]++;
    if (
    /* istanbul ignore next */
    (cov_1utmstr375().b[8][0]++, !socket.userId) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[8][1]++, !conversationId)) {
      /* istanbul ignore next */
      cov_1utmstr375().b[7][0]++;
      cov_1utmstr375().s[62]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[7][1]++;
    }
    cov_1utmstr375().s[63]++;
    try {
      // Verify user is participant in conversation
      const conversation =
      /* istanbul ignore next */
      (cov_1utmstr375().s[64]++, await Conversation_1.Conversation.findOne({
        _id: conversationId,
        participants: socket.userId,
        status: 'active'
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[65]++;
      if (conversation) {
        /* istanbul ignore next */
        cov_1utmstr375().b[9][0]++;
        cov_1utmstr375().s[66]++;
        socket.join(`conversation_${conversationId}`);
        /* istanbul ignore next */
        cov_1utmstr375().s[67]++;
        logger_1.logger.debug(`User ${socket.userId} joined conversation ${conversationId}`);
        /* istanbul ignore next */
        cov_1utmstr375().s[68]++;
        socket.emit('conversation_joined', {
          conversationId
        });
      } else {
        /* istanbul ignore next */
        cov_1utmstr375().b[9][1]++;
        cov_1utmstr375().s[69]++;
        socket.emit('error', {
          message: 'Cannot join conversation'
        });
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[70]++;
      logger_1.logger.error('Error joining conversation:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[71]++;
      socket.emit('error', {
        message: 'Failed to join conversation'
      });
    }
  }
  async handleLeaveConversation(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[20]++;
    const {
      conversationId
    } =
    /* istanbul ignore next */
    (cov_1utmstr375().s[72]++, data);
    /* istanbul ignore next */
    cov_1utmstr375().s[73]++;
    if (!conversationId) {
      /* istanbul ignore next */
      cov_1utmstr375().b[10][0]++;
      cov_1utmstr375().s[74]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[10][1]++;
    }
    cov_1utmstr375().s[75]++;
    socket.leave(`conversation_${conversationId}`);
    /* istanbul ignore next */
    cov_1utmstr375().s[76]++;
    logger_1.logger.debug(`User ${socket.userId} left conversation ${conversationId}`);
    /* istanbul ignore next */
    cov_1utmstr375().s[77]++;
    socket.emit('conversation_left', {
      conversationId
    });
  }
  async handleCreateConversation(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[21]++;
    cov_1utmstr375().s[78]++;
    try {
      const {
        participantIds,
        conversationType =
        /* istanbul ignore next */
        (cov_1utmstr375().b[11][0]++, 'direct'),
        title,
        matchId
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[79]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[80]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[13][0]++, !socket.userId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[13][1]++, !participantIds)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[12][0]++;
        cov_1utmstr375().s[81]++;
        socket.emit('error', {
          message: 'Invalid conversation data'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[82]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[12][1]++;
      }
      // Add current user to participants
      const allParticipants =
      /* istanbul ignore next */
      (cov_1utmstr375().s[83]++, [...new Set([socket.userId, ...participantIds])]);
      // Create conversation (simplified version)
      const conversation =
      /* istanbul ignore next */
      (cov_1utmstr375().s[84]++, new Conversation_1.Conversation({
        participants: allParticipants.map(id => {
          /* istanbul ignore next */
          cov_1utmstr375().f[22]++;
          cov_1utmstr375().s[85]++;
          return new mongoose_1.Types.ObjectId(id);
        }),
        participantDetails: allParticipants.map(participantId => {
          /* istanbul ignore next */
          cov_1utmstr375().f[23]++;
          cov_1utmstr375().s[86]++;
          return {
            userId: new mongoose_1.Types.ObjectId(participantId),
            joinedAt: new Date(),
            role: participantId === socket.userId ?
            /* istanbul ignore next */
            (cov_1utmstr375().b[14][0]++, 'admin') :
            /* istanbul ignore next */
            (cov_1utmstr375().b[14][1]++, 'member'),
            isActive: true,
            lastSeenAt: new Date(),
            unreadCount: 0,
            isMuted: false
          };
        }),
        conversationType,
        title: conversationType === 'group' ?
        /* istanbul ignore next */
        (cov_1utmstr375().b[15][0]++, title) :
        /* istanbul ignore next */
        (cov_1utmstr375().b[15][1]++, undefined),
        matchId: matchId ?
        /* istanbul ignore next */
        (cov_1utmstr375().b[16][0]++, new mongoose_1.Types.ObjectId(matchId)) :
        /* istanbul ignore next */
        (cov_1utmstr375().b[16][1]++, undefined),
        settings: {
          allowFileSharing: true,
          allowLocationSharing: true,
          allowPropertySharing: true,
          maxParticipants: conversationType === 'direct' ?
          /* istanbul ignore next */
          (cov_1utmstr375().b[17][0]++, 2) :
          /* istanbul ignore next */
          (cov_1utmstr375().b[17][1]++, 50),
          autoDeleteMessages: false,
          requireApprovalForNewMembers: false
        },
        analytics: {
          totalMessages: 0,
          totalParticipants: allParticipants.length,
          averageResponseTime: 0,
          lastActivityAt: new Date(),
          messagesThisWeek: 0,
          messagesThisMonth: 0
        }
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[87]++;
      await conversation.save();
      // Join all participants to the conversation room
      /* istanbul ignore next */
      cov_1utmstr375().s[88]++;
      allParticipants.forEach(participantId => {
        /* istanbul ignore next */
        cov_1utmstr375().f[24]++;
        const userSocketIds =
        /* istanbul ignore next */
        (cov_1utmstr375().s[89]++, this.userSockets.get(participantId));
        /* istanbul ignore next */
        cov_1utmstr375().s[90]++;
        if (userSocketIds) {
          /* istanbul ignore next */
          cov_1utmstr375().b[18][0]++;
          cov_1utmstr375().s[91]++;
          userSocketIds.forEach(socketId => {
            /* istanbul ignore next */
            cov_1utmstr375().f[25]++;
            cov_1utmstr375().s[92]++;
            this.io.to(socketId).socketsJoin(`conversation_${conversation._id}`);
          });
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[18][1]++;
        }
      });
      // Notify all participants
      /* istanbul ignore next */
      cov_1utmstr375().s[93]++;
      this.io.to(`conversation_${conversation._id}`).emit('conversation_created', {
        conversation: conversation.toObject()
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[94]++;
      logger_1.logger.info(`Conversation ${conversation._id} created by user ${socket.userId}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[95]++;
      logger_1.logger.error('Error creating conversation:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[96]++;
      socket.emit('error', {
        message: 'Failed to create conversation'
      });
    }
  }
  async handleEditMessage(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[26]++;
    cov_1utmstr375().s[97]++;
    try {
      const {
        messageId,
        content
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[98]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[99]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[20][0]++, !socket.userId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[20][1]++, !messageId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[20][2]++, !content)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[19][0]++;
        cov_1utmstr375().s[100]++;
        socket.emit('error', {
          message: 'Invalid edit data'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[101]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[19][1]++;
      }
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[102]++, await Conversation_1.Message.findOne({
        _id: messageId,
        senderId: socket.userId,
        messageType: 'text',
        isDeleted: false
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[103]++;
      if (!message) {
        /* istanbul ignore next */
        cov_1utmstr375().b[21][0]++;
        cov_1utmstr375().s[104]++;
        socket.emit('error', {
          message: 'Message not found or cannot be edited'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[105]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[21][1]++;
      }
      // Check if message is too old to edit (24 hours)
      const messageAge =
      /* istanbul ignore next */
      (cov_1utmstr375().s[106]++, Date.now() - message.createdAt.getTime());
      /* istanbul ignore next */
      cov_1utmstr375().s[107]++;
      if (messageAge > 24 * 60 * 60 * 1000) {
        /* istanbul ignore next */
        cov_1utmstr375().b[22][0]++;
        cov_1utmstr375().s[108]++;
        socket.emit('error', {
          message: 'Message is too old to edit'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[109]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[22][1]++;
      }
      // Store original content if not already edited
      cov_1utmstr375().s[110]++;
      if (!message.isEdited) {
        /* istanbul ignore next */
        cov_1utmstr375().b[23][0]++;
        cov_1utmstr375().s[111]++;
        message.originalContent = message.content;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[23][1]++;
      }
      cov_1utmstr375().s[112]++;
      message.content = content.trim();
      /* istanbul ignore next */
      cov_1utmstr375().s[113]++;
      message.isEdited = true;
      /* istanbul ignore next */
      cov_1utmstr375().s[114]++;
      message.editedAt = new Date();
      /* istanbul ignore next */
      cov_1utmstr375().s[115]++;
      await message.save();
      // Broadcast edit to conversation
      /* istanbul ignore next */
      cov_1utmstr375().s[116]++;
      this.io.to(`conversation_${message.conversationId}`).emit('message_edited', {
        messageId,
        content: message.content,
        isEdited: true,
        editedAt: message.editedAt
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[117]++;
      logger_1.logger.info(`Message ${messageId} edited by user ${socket.userId}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[118]++;
      logger_1.logger.error('Error editing message:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[119]++;
      socket.emit('error', {
        message: 'Failed to edit message'
      });
    }
  }
  async handleDeleteMessage(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[27]++;
    cov_1utmstr375().s[120]++;
    try {
      const {
        messageId,
        deleteForEveryone =
        /* istanbul ignore next */
        (cov_1utmstr375().b[24][0]++, false)
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[121]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[122]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[26][0]++, !socket.userId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[26][1]++, !messageId)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[25][0]++;
        cov_1utmstr375().s[123]++;
        socket.emit('error', {
          message: 'Invalid delete data'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[124]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[25][1]++;
      }
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[125]++, await Conversation_1.Message.findOne({
        _id: messageId,
        senderId: socket.userId,
        isDeleted: false
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[126]++;
      if (!message) {
        /* istanbul ignore next */
        cov_1utmstr375().b[27][0]++;
        cov_1utmstr375().s[127]++;
        socket.emit('error', {
          message: 'Message not found or already deleted'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[128]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[27][1]++;
      }
      // Check if user can delete for everyone (within 1 hour)
      const messageAge =
      /* istanbul ignore next */
      (cov_1utmstr375().s[129]++, Date.now() - message.createdAt.getTime());
      /* istanbul ignore next */
      cov_1utmstr375().s[130]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[29][0]++, deleteForEveryone) &&
      /* istanbul ignore next */
      (cov_1utmstr375().b[29][1]++, messageAge > 60 * 60 * 1000)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[28][0]++;
        cov_1utmstr375().s[131]++;
        socket.emit('error', {
          message: 'Message is too old to delete for everyone'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[132]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[28][1]++;
      }
      cov_1utmstr375().s[133]++;
      message.isDeleted = true;
      /* istanbul ignore next */
      cov_1utmstr375().s[134]++;
      message.deletedAt = new Date();
      /* istanbul ignore next */
      cov_1utmstr375().s[135]++;
      message.deletedBy = new mongoose_1.Types.ObjectId(socket.userId);
      /* istanbul ignore next */
      cov_1utmstr375().s[136]++;
      if (deleteForEveryone) {
        /* istanbul ignore next */
        cov_1utmstr375().b[30][0]++;
        cov_1utmstr375().s[137]++;
        message.content = 'This message was deleted';
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[30][1]++;
      }
      cov_1utmstr375().s[138]++;
      await message.save();
      // Broadcast deletion to conversation
      /* istanbul ignore next */
      cov_1utmstr375().s[139]++;
      this.io.to(`conversation_${message.conversationId}`).emit('message_deleted', {
        messageId,
        deletedForEveryone: deleteForEveryone,
        deletedBy: socket.userId
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[140]++;
      logger_1.logger.info(`Message ${messageId} deleted by user ${socket.userId}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[141]++;
      logger_1.logger.error('Error deleting message:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[142]++;
      socket.emit('error', {
        message: 'Failed to delete message'
      });
    }
  }
  async handleMessageReaction(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[28]++;
    cov_1utmstr375().s[143]++;
    try {
      const {
        messageId,
        reaction
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[144]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[145]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[32][0]++, !socket.userId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[32][1]++, !messageId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[32][2]++, !reaction)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[31][0]++;
        cov_1utmstr375().s[146]++;
        socket.emit('error', {
          message: 'Invalid reaction data'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[147]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[31][1]++;
      }
      const validReactions =
      /* istanbul ignore next */
      (cov_1utmstr375().s[148]++, ['like', 'love', 'laugh', 'wow', 'sad', 'angry']);
      /* istanbul ignore next */
      cov_1utmstr375().s[149]++;
      if (!validReactions.includes(reaction)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[33][0]++;
        cov_1utmstr375().s[150]++;
        socket.emit('error', {
          message: 'Invalid reaction type'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[151]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[33][1]++;
      }
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[152]++, await Conversation_1.Message.findOne({
        _id: messageId,
        isDeleted: false
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[153]++;
      if (!message) {
        /* istanbul ignore next */
        cov_1utmstr375().b[34][0]++;
        cov_1utmstr375().s[154]++;
        socket.emit('error', {
          message: 'Message not found'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[155]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[34][1]++;
      }
      // Verify user is participant in the conversation
      const conversation =
      /* istanbul ignore next */
      (cov_1utmstr375().s[156]++, await Conversation_1.Conversation.findOne({
        _id: message.conversationId,
        participants: socket.userId
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[157]++;
      if (!conversation) {
        /* istanbul ignore next */
        cov_1utmstr375().b[35][0]++;
        cov_1utmstr375().s[158]++;
        socket.emit('error', {
          message: 'Access denied'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[159]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[35][1]++;
      }
      // Check if user already reacted
      const existingReactionIndex =
      /* istanbul ignore next */
      (cov_1utmstr375().s[160]++,
      /* istanbul ignore next */
      (cov_1utmstr375().b[36][0]++, message.reactions?.findIndex(r => {
        /* istanbul ignore next */
        cov_1utmstr375().f[29]++;
        cov_1utmstr375().s[161]++;
        return r.userId.toString() === socket.userId;
      })) ??
      /* istanbul ignore next */
      (cov_1utmstr375().b[36][1]++, -1));
      /* istanbul ignore next */
      cov_1utmstr375().s[162]++;
      if (existingReactionIndex > -1) {
        /* istanbul ignore next */
        cov_1utmstr375().b[37][0]++;
        cov_1utmstr375().s[163]++;
        // Update existing reaction
        message.reactions[existingReactionIndex].reaction = reaction;
        /* istanbul ignore next */
        cov_1utmstr375().s[164]++;
        message.reactions[existingReactionIndex].createdAt = new Date();
      } else {
        /* istanbul ignore next */
        cov_1utmstr375().b[37][1]++;
        cov_1utmstr375().s[165]++;
        // Add new reaction
        if (!message.reactions) {
          /* istanbul ignore next */
          cov_1utmstr375().b[38][0]++;
          cov_1utmstr375().s[166]++;
          message.reactions = [];
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[38][1]++;
        }
        cov_1utmstr375().s[167]++;
        message.reactions.push({
          userId: new mongoose_1.Types.ObjectId(socket.userId),
          reaction,
          createdAt: new Date()
        });
      }
      /* istanbul ignore next */
      cov_1utmstr375().s[168]++;
      await message.save();
      // Broadcast reaction to conversation
      /* istanbul ignore next */
      cov_1utmstr375().s[169]++;
      this.io.to(`conversation_${message.conversationId}`).emit('message_reaction', {
        messageId,
        userId: socket.userId,
        reaction,
        reactions: message.reactions
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[170]++;
      logger_1.logger.info(`User ${socket.userId} reacted to message ${messageId} with ${reaction}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[171]++;
      logger_1.logger.error('Error reacting to message:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[172]++;
      socket.emit('error', {
        message: 'Failed to react to message'
      });
    }
  }
  handleUserConnect(socket) {
    /* istanbul ignore next */
    cov_1utmstr375().f[30]++;
    cov_1utmstr375().s[173]++;
    if (!socket.userId) {
      /* istanbul ignore next */
      cov_1utmstr375().b[39][0]++;
      cov_1utmstr375().s[174]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[39][1]++;
    }
    // Add to online users
    cov_1utmstr375().s[175]++;
    this.onlineUsers.set(socket.userId, {
      userId: socket.userId,
      socketId: socket.id,
      lastSeen: new Date(),
      status: 'online'
    });
    // Track multiple sockets per user
    const userSocketIds =
    /* istanbul ignore next */
    (cov_1utmstr375().s[176]++,
    /* istanbul ignore next */
    (cov_1utmstr375().b[40][0]++, this.userSockets.get(socket.userId)) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[40][1]++, []));
    /* istanbul ignore next */
    cov_1utmstr375().s[177]++;
    userSocketIds.push(socket.id);
    /* istanbul ignore next */
    cov_1utmstr375().s[178]++;
    this.userSockets.set(socket.userId, userSocketIds);
    // Join user to their conversation rooms
    /* istanbul ignore next */
    cov_1utmstr375().s[179]++;
    this.joinUserConversations(socket);
    // Broadcast online status to contacts
    /* istanbul ignore next */
    cov_1utmstr375().s[180]++;
    this.broadcastUserStatus(socket.userId, 'online');
    // Send online users list to the connected user
    /* istanbul ignore next */
    cov_1utmstr375().s[181]++;
    socket.emit('online_users', Array.from(this.onlineUsers.values()));
  }
  async joinUserConversations(socket) {
    /* istanbul ignore next */
    cov_1utmstr375().f[31]++;
    cov_1utmstr375().s[182]++;
    if (!socket.userId) {
      /* istanbul ignore next */
      cov_1utmstr375().b[41][0]++;
      cov_1utmstr375().s[183]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[41][1]++;
    }
    cov_1utmstr375().s[184]++;
    try {
      const conversations =
      /* istanbul ignore next */
      (cov_1utmstr375().s[185]++, await Conversation_1.Conversation.find({
        participants: socket.userId,
        status: 'active'
      }).select('_id'));
      /* istanbul ignore next */
      cov_1utmstr375().s[186]++;
      conversations.forEach(conversation => {
        /* istanbul ignore next */
        cov_1utmstr375().f[32]++;
        cov_1utmstr375().s[187]++;
        socket.join(`conversation_${conversation._id}`);
      });
      /* istanbul ignore next */
      cov_1utmstr375().s[188]++;
      logger_1.logger.info(`User ${socket.userId} joined ${conversations.length} conversation rooms`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[189]++;
      logger_1.logger.error('Error joining user conversations:', error);
    }
  }
  async handleSendMessage(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[33]++;
    cov_1utmstr375().s[190]++;
    try {
      const {
        conversationId,
        content,
        messageType =
        /* istanbul ignore next */
        (cov_1utmstr375().b[42][0]++, 'text'),
        metadata
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[191]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[192]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[44][0]++, !socket.userId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[44][1]++, !conversationId) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[44][2]++, !content)) {
        /* istanbul ignore next */
        cov_1utmstr375().b[43][0]++;
        cov_1utmstr375().s[193]++;
        socket.emit('error', {
          message: 'Invalid message data'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[194]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[43][1]++;
      }
      // Verify user can send message to this conversation
      const conversation =
      /* istanbul ignore next */
      (cov_1utmstr375().s[195]++, await Conversation_1.Conversation.findById(conversationId));
      /* istanbul ignore next */
      cov_1utmstr375().s[196]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[46][0]++, !conversation) ||
      /* istanbul ignore next */
      (cov_1utmstr375().b[46][1]++, !conversation.canUserSendMessage(new mongoose_1.Types.ObjectId(socket.userId)))) {
        /* istanbul ignore next */
        cov_1utmstr375().b[45][0]++;
        cov_1utmstr375().s[197]++;
        socket.emit('error', {
          message: 'Cannot send message to this conversation'
        });
        /* istanbul ignore next */
        cov_1utmstr375().s[198]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[45][1]++;
      }
      // Get receiver ID (for direct conversations)
      const receiverId =
      /* istanbul ignore next */
      (cov_1utmstr375().s[199]++, conversation.participants.find(p => {
        /* istanbul ignore next */
        cov_1utmstr375().f[34]++;
        cov_1utmstr375().s[200]++;
        return p.toString() !== socket.userId;
      }));
      // Create message
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[201]++, new Conversation_1.Message({
        conversationId,
        senderId: socket.userId,
        receiverId,
        messageType,
        content: content.trim(),
        metadata,
        status: 'sent'
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[202]++;
      await message.save();
      // Update conversation last message
      /* istanbul ignore next */
      cov_1utmstr375().s[203]++;
      await conversation.updateLastMessage(message);
      // Populate message for response
      const populatedMessage =
      /* istanbul ignore next */
      (cov_1utmstr375().s[204]++, await Conversation_1.Message.findById(message._id).populate('senderId', 'firstName lastName avatar').populate('receiverId', 'firstName lastName avatar'));
      // Emit to conversation room
      /* istanbul ignore next */
      cov_1utmstr375().s[205]++;
      this.io.to(`conversation_${conversationId}`).emit('new_message', {
        message: populatedMessage,
        conversationId
      });
      // Send delivery confirmation to sender
      /* istanbul ignore next */
      cov_1utmstr375().s[206]++;
      socket.emit('message_sent', {
        tempId: data.tempId,
        message: populatedMessage
      });
      // Update message status to delivered if receiver is online
      /* istanbul ignore next */
      cov_1utmstr375().s[207]++;
      if (
      /* istanbul ignore next */
      (cov_1utmstr375().b[48][0]++, receiverId) &&
      /* istanbul ignore next */
      (cov_1utmstr375().b[48][1]++, this.onlineUsers.has(receiverId.toString()))) {
        /* istanbul ignore next */
        cov_1utmstr375().b[47][0]++;
        cov_1utmstr375().s[208]++;
        message.status = 'delivered';
        /* istanbul ignore next */
        cov_1utmstr375().s[209]++;
        message.deliveredAt = new Date();
        /* istanbul ignore next */
        cov_1utmstr375().s[210]++;
        await message.save();
        /* istanbul ignore next */
        cov_1utmstr375().s[211]++;
        this.io.to(`conversation_${conversationId}`).emit('message_delivered', {
          messageId: message._id,
          deliveredAt: message.deliveredAt
        });
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[47][1]++;
      }
      cov_1utmstr375().s[212]++;
      logger_1.logger.info(`Message sent from ${socket.userId} to conversation ${conversationId}`);
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[213]++;
      logger_1.logger.error('Error sending message:', error);
      /* istanbul ignore next */
      cov_1utmstr375().s[214]++;
      socket.emit('error', {
        message: 'Failed to send message'
      });
    }
  }
  async handleMessageDelivered(_socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[35]++;
    cov_1utmstr375().s[215]++;
    try {
      const {
        messageId
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[216]++, data);
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[217]++, await Conversation_1.Message.findByIdAndUpdate(messageId, {
        status: 'delivered',
        deliveredAt: new Date()
      }, {
        new: true
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[218]++;
      if (message) {
        /* istanbul ignore next */
        cov_1utmstr375().b[49][0]++;
        cov_1utmstr375().s[219]++;
        this.io.to(`conversation_${message.conversationId}`).emit('message_delivered', {
          messageId,
          deliveredAt: message.deliveredAt
        });
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[49][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[220]++;
      logger_1.logger.error('Error updating message delivery status:', error);
    }
  }
  async handleMessageRead(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[36]++;
    cov_1utmstr375().s[221]++;
    try {
      const {
        messageId,
        conversationId
      } =
      /* istanbul ignore next */
      (cov_1utmstr375().s[222]++, data);
      /* istanbul ignore next */
      cov_1utmstr375().s[223]++;
      if (!socket.userId) {
        /* istanbul ignore next */
        cov_1utmstr375().b[50][0]++;
        cov_1utmstr375().s[224]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[50][1]++;
      }
      // Update message status
      const message =
      /* istanbul ignore next */
      (cov_1utmstr375().s[225]++, await Conversation_1.Message.findByIdAndUpdate(messageId, {
        status: 'read',
        readAt: new Date()
      }, {
        new: true
      }));
      /* istanbul ignore next */
      cov_1utmstr375().s[226]++;
      if (message) {
        /* istanbul ignore next */
        cov_1utmstr375().b[51][0]++;
        // Update conversation unread count
        const conversation =
        /* istanbul ignore next */
        (cov_1utmstr375().s[227]++, await Conversation_1.Conversation.findById(conversationId));
        /* istanbul ignore next */
        cov_1utmstr375().s[228]++;
        if (conversation) {
          /* istanbul ignore next */
          cov_1utmstr375().b[52][0]++;
          cov_1utmstr375().s[229]++;
          await conversation.markAsRead(new mongoose_1.Types.ObjectId(socket.userId), new mongoose_1.Types.ObjectId(messageId));
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[52][1]++;
        }
        // Notify sender about read status
        cov_1utmstr375().s[230]++;
        this.io.to(`conversation_${conversationId}`).emit('message_read', {
          messageId,
          readAt: message.readAt,
          readBy: socket.userId
        });
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[51][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1utmstr375().s[231]++;
      logger_1.logger.error('Error updating message read status:', error);
    }
  }
  async handleTypingStart(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[37]++;
    const {
      conversationId
    } =
    /* istanbul ignore next */
    (cov_1utmstr375().s[232]++, data);
    /* istanbul ignore next */
    cov_1utmstr375().s[233]++;
    if (
    /* istanbul ignore next */
    (cov_1utmstr375().b[54][0]++, !socket.userId) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[54][1]++, !conversationId)) {
      /* istanbul ignore next */
      cov_1utmstr375().b[53][0]++;
      cov_1utmstr375().s[234]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[53][1]++;
    }
    const typingKey =
    /* istanbul ignore next */
    (cov_1utmstr375().s[235]++, `${socket.userId}_${conversationId}`);
    /* istanbul ignore next */
    cov_1utmstr375().s[236]++;
    this.typingUsers.set(typingKey, {
      userId: socket.userId,
      conversationId,
      timestamp: new Date()
    });
    // Broadcast typing status to other participants
    /* istanbul ignore next */
    cov_1utmstr375().s[237]++;
    socket.to(`conversation_${conversationId}`).emit('user_typing', {
      userId: socket.userId,
      conversationId,
      isTyping: true
    });
  }
  async handleTypingStop(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[38]++;
    const {
      conversationId
    } =
    /* istanbul ignore next */
    (cov_1utmstr375().s[238]++, data);
    /* istanbul ignore next */
    cov_1utmstr375().s[239]++;
    if (
    /* istanbul ignore next */
    (cov_1utmstr375().b[56][0]++, !socket.userId) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[56][1]++, !conversationId)) {
      /* istanbul ignore next */
      cov_1utmstr375().b[55][0]++;
      cov_1utmstr375().s[240]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[55][1]++;
    }
    const typingKey =
    /* istanbul ignore next */
    (cov_1utmstr375().s[241]++, `${socket.userId}_${conversationId}`);
    /* istanbul ignore next */
    cov_1utmstr375().s[242]++;
    this.typingUsers.delete(typingKey);
    // Broadcast typing stop to other participants
    /* istanbul ignore next */
    cov_1utmstr375().s[243]++;
    socket.to(`conversation_${conversationId}`).emit('user_typing', {
      userId: socket.userId,
      conversationId,
      isTyping: false
    });
  }
  handleStatusChange(socket, data) {
    /* istanbul ignore next */
    cov_1utmstr375().f[39]++;
    const {
      status
    } =
    /* istanbul ignore next */
    (cov_1utmstr375().s[244]++, data);
    /* istanbul ignore next */
    cov_1utmstr375().s[245]++;
    if (
    /* istanbul ignore next */
    (cov_1utmstr375().b[58][0]++, !socket.userId) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[58][1]++, !['online', 'away', 'busy'].includes(status))) {
      /* istanbul ignore next */
      cov_1utmstr375().b[57][0]++;
      cov_1utmstr375().s[246]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[57][1]++;
    }
    const user =
    /* istanbul ignore next */
    (cov_1utmstr375().s[247]++, this.onlineUsers.get(socket.userId));
    /* istanbul ignore next */
    cov_1utmstr375().s[248]++;
    if (user) {
      /* istanbul ignore next */
      cov_1utmstr375().b[59][0]++;
      cov_1utmstr375().s[249]++;
      user.status = status;
      /* istanbul ignore next */
      cov_1utmstr375().s[250]++;
      user.lastSeen = new Date();
      /* istanbul ignore next */
      cov_1utmstr375().s[251]++;
      this.onlineUsers.set(socket.userId, user);
      /* istanbul ignore next */
      cov_1utmstr375().s[252]++;
      this.broadcastUserStatus(socket.userId, status);
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[59][1]++;
    }
  }
  handleUserDisconnect(socket) {
    /* istanbul ignore next */
    cov_1utmstr375().f[40]++;
    cov_1utmstr375().s[253]++;
    if (!socket.userId) {
      /* istanbul ignore next */
      cov_1utmstr375().b[60][0]++;
      cov_1utmstr375().s[254]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[60][1]++;
    }
    cov_1utmstr375().s[255]++;
    logger_1.logger.info(`User ${socket.userId} disconnected from socket ${socket.id}`);
    // Remove socket from user's socket list
    const userSocketIds =
    /* istanbul ignore next */
    (cov_1utmstr375().s[256]++,
    /* istanbul ignore next */
    (cov_1utmstr375().b[61][0]++, this.userSockets.get(socket.userId)) ||
    /* istanbul ignore next */
    (cov_1utmstr375().b[61][1]++, []));
    const updatedSocketIds =
    /* istanbul ignore next */
    (cov_1utmstr375().s[257]++, userSocketIds.filter(id => {
      /* istanbul ignore next */
      cov_1utmstr375().f[41]++;
      cov_1utmstr375().s[258]++;
      return id !== socket.id;
    }));
    /* istanbul ignore next */
    cov_1utmstr375().s[259]++;
    if (updatedSocketIds.length > 0) {
      /* istanbul ignore next */
      cov_1utmstr375().b[62][0]++;
      cov_1utmstr375().s[260]++;
      this.userSockets.set(socket.userId, updatedSocketIds);
    } else {
      /* istanbul ignore next */
      cov_1utmstr375().b[62][1]++;
      cov_1utmstr375().s[261]++;
      // User has no more active sockets
      this.userSockets.delete(socket.userId);
      /* istanbul ignore next */
      cov_1utmstr375().s[262]++;
      this.onlineUsers.delete(socket.userId);
      // Broadcast offline status
      /* istanbul ignore next */
      cov_1utmstr375().s[263]++;
      this.broadcastUserStatus(socket.userId, 'offline');
    }
    // Clear typing status
    /* istanbul ignore next */
    cov_1utmstr375().s[264]++;
    for (const [key, typingUser] of this.typingUsers.entries()) {
      /* istanbul ignore next */
      cov_1utmstr375().s[265]++;
      if (typingUser.userId === socket.userId) {
        /* istanbul ignore next */
        cov_1utmstr375().b[63][0]++;
        cov_1utmstr375().s[266]++;
        this.typingUsers.delete(key);
        /* istanbul ignore next */
        cov_1utmstr375().s[267]++;
        socket.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingUser.conversationId,
          isTyping: false
        });
      } else
      /* istanbul ignore next */
      {
        cov_1utmstr375().b[63][1]++;
      }
    }
  }
  broadcastUserStatus(userId, status) {
    /* istanbul ignore next */
    cov_1utmstr375().f[42]++;
    cov_1utmstr375().s[268]++;
    this.io.emit('user_status_change', {
      userId,
      status,
      lastSeen: new Date()
    });
  }
  startCleanupInterval() {
    /* istanbul ignore next */
    cov_1utmstr375().f[43]++;
    cov_1utmstr375().s[269]++;
    // Clean up old typing indicators every 30 seconds
    setInterval(() => {
      /* istanbul ignore next */
      cov_1utmstr375().f[44]++;
      const now =
      /* istanbul ignore next */
      (cov_1utmstr375().s[270]++, new Date());
      /* istanbul ignore next */
      cov_1utmstr375().s[271]++;
      for (const [key, typingUser] of this.typingUsers.entries()) {
        /* istanbul ignore next */
        cov_1utmstr375().s[272]++;
        if (now.getTime() - typingUser.timestamp.getTime() > 30000) {
          /* istanbul ignore next */
          cov_1utmstr375().b[64][0]++;
          cov_1utmstr375().s[273]++;
          // 30 seconds
          this.typingUsers.delete(key);
          /* istanbul ignore next */
          cov_1utmstr375().s[274]++;
          this.io.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {
            userId: typingUser.userId,
            conversationId: typingUser.conversationId,
            isTyping: false
          });
        } else
        /* istanbul ignore next */
        {
          cov_1utmstr375().b[64][1]++;
        }
      }
    }, 30000);
  }
  // Public methods for external use
  getOnlineUsers() {
    /* istanbul ignore next */
    cov_1utmstr375().f[45]++;
    cov_1utmstr375().s[275]++;
    return Array.from(this.onlineUsers.values());
  }
  isUserOnline(userId) {
    /* istanbul ignore next */
    cov_1utmstr375().f[46]++;
    cov_1utmstr375().s[276]++;
    return this.onlineUsers.has(userId);
  }
  getUserStatus(userId) {
    /* istanbul ignore next */
    cov_1utmstr375().f[47]++;
    const user =
    /* istanbul ignore next */
    (cov_1utmstr375().s[277]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_1utmstr375().s[278]++;
    return user ?
    /* istanbul ignore next */
    (cov_1utmstr375().b[65][0]++, user.status) :
    /* istanbul ignore next */
    (cov_1utmstr375().b[65][1]++, 'offline');
  }
  sendNotificationToUser(userId, notification) {
    /* istanbul ignore next */
    cov_1utmstr375().f[48]++;
    const userSocketIds =
    /* istanbul ignore next */
    (cov_1utmstr375().s[279]++, this.userSockets.get(userId));
    /* istanbul ignore next */
    cov_1utmstr375().s[280]++;
    if (userSocketIds) {
      /* istanbul ignore next */
      cov_1utmstr375().b[66][0]++;
      cov_1utmstr375().s[281]++;
      userSocketIds.forEach(socketId => {
        /* istanbul ignore next */
        cov_1utmstr375().f[49]++;
        cov_1utmstr375().s[282]++;
        this.io.to(socketId).emit('notification', notification);
      });
    } else
    /* istanbul ignore next */
    {
      cov_1utmstr375().b[66][1]++;
    }
  }
  getIO() {
    /* istanbul ignore next */
    cov_1utmstr375().f[50]++;
    cov_1utmstr375().s[283]++;
    return this.io;
  }
}
/* istanbul ignore next */
cov_1utmstr375().s[284]++;
exports.SocketService = SocketService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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