{"version": 3, "names": ["User_model_1", "cov_t778pbqqd", "s", "require", "Property_1", "Profile_model_1", "logger_1", "MatchingHelpers", "getRoommateCandidates", "userId", "userPrefs", "f", "query", "_id", "$ne", "accountType", "isEmailVerified", "isActive", "genderPreference", "b", "currentYear", "Date", "getFullYear", "minBirthYear", "<PERSON><PERSON><PERSON><PERSON>", "max", "maxBirthYear", "min", "$gte", "$lte", "candidates", "User", "find", "populate", "limit", "lean", "error", "logger", "getPropertyCandidates", "status", "isAvailable", "ownerId", "budgetRange", "propertyPreferences", "propertyTypes", "length", "propertyType", "$in", "minimumBedrooms", "bedrooms", "minimumBathrooms", "bathrooms", "preferredStates", "preferredCities", "amenities", "for<PERSON>ach", "amenity", "properties", "Property", "calculateDistance", "targetUserId", "userProfile", "targetProfile", "Promise", "all", "Profile", "findOne", "location", "coordinates", "userCoords", "targetCoords", "calculateDistanceBetweenCoordinates", "calculatePropertyDistance", "propertyId", "property", "findById", "propertyCoords", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "toRadians", "dLon", "a", "Math", "sin", "cos", "c", "atan2", "sqrt", "distance", "round", "checkStateMatch", "targetId", "targetType", "state", "generateMatchReasons", "factors", "reasons", "push", "budget", "lifestyle", "schedule", "cleanliness", "socialLevel", "overall", "generatePropertyMatchReasons", "city", "preferences", "wifi", "parking", "security", "generator", "isCompatibleLifestyle", "pref1", "pref2", "compatibilityMap", "includes", "isCompatibleSchedule", "degrees", "PI", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchingHelpers.ts"], "sourcesContent": ["import { Types } from 'mongoose';\r\nimport { User } from '../models/User.model';\r\nimport { Property } from '../models/Property';\r\nimport { Profile } from '../models/Profile.model';\r\nimport { IMatchPreferences, CompatibilityFactors } from './matchingService';\r\nimport { logger } from '../utils/logger';\r\n\r\nexport class MatchingHelpers {\r\n  \r\n  /**\r\n   * Get potential roommate candidates for a user\r\n   */\r\n  static async getRoommateCandidates(\r\n    userId: Types.ObjectId,\r\n    userPrefs: IMatchPreferences\r\n  ): Promise<any[]> {\r\n    try {\r\n      const query: any = {\r\n        _id: { $ne: userId }, // Exclude self\r\n        accountType: 'tenant', // Only tenants looking for roommates\r\n        isEmailVerified: true,\r\n        isActive: true\r\n      };\r\n\r\n      // Add gender filter if specified\r\n      if (userPrefs.genderPreference !== 'any') {\r\n        query['profile.gender'] = userPrefs.genderPreference;\r\n      }\r\n\r\n      // Add age filter\r\n      const currentYear = new Date().getFullYear();\r\n      const minBirthYear = currentYear - userPrefs.ageRange.max;\r\n      const maxBirthYear = currentYear - userPrefs.ageRange.min;\r\n      \r\n      query['profile.dateOfBirth'] = {\r\n        $gte: new Date(`${minBirthYear}-01-01`),\r\n        $lte: new Date(`${maxBirthYear}-12-31`)\r\n      };\r\n\r\n      const candidates = await User.find(query)\r\n        .populate('profile')\r\n        .limit(100) // Limit to prevent performance issues\r\n        .lean();\r\n\r\n      return candidates;\r\n    } catch (error) {\r\n      logger.error('Error getting roommate candidates:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get potential property candidates for a user\r\n   */\r\n  static async getPropertyCandidates(\r\n    userId: Types.ObjectId,\r\n    userPrefs: IMatchPreferences\r\n  ): Promise<any[]> {\r\n    try {\r\n      const query: any = {\r\n        status: 'active',\r\n        isAvailable: true,\r\n        ownerId: { $ne: userId }, // Exclude user's own properties\r\n        'pricing.rentPerMonth': {\r\n          $gte: userPrefs.budgetRange.min,\r\n          $lte: userPrefs.budgetRange.max\r\n        }\r\n      };\r\n\r\n      // Add property type filter\r\n      if (userPrefs.propertyPreferences.propertyTypes.length > 0) {\r\n        query.propertyType = { $in: userPrefs.propertyPreferences.propertyTypes };\r\n      }\r\n\r\n      // Add bedroom/bathroom filters\r\n      if (userPrefs.propertyPreferences.minimumBedrooms > 0) {\r\n        query.bedrooms = { $gte: userPrefs.propertyPreferences.minimumBedrooms };\r\n      }\r\n\r\n      if (userPrefs.propertyPreferences.minimumBathrooms > 0) {\r\n        query.bathrooms = { $gte: userPrefs.propertyPreferences.minimumBathrooms };\r\n      }\r\n\r\n      // Add location filters\r\n      if (userPrefs.preferredStates.length > 0) {\r\n        query['location.state'] = { $in: userPrefs.preferredStates };\r\n      }\r\n\r\n      if (userPrefs.preferredCities.length > 0) {\r\n        query['location.city'] = { $in: userPrefs.preferredCities };\r\n      }\r\n\r\n      // Add amenity filters\r\n      if (userPrefs.propertyPreferences.amenities.length > 0) {\r\n        userPrefs.propertyPreferences.amenities.forEach(amenity => {\r\n          query[`amenities.${amenity}`] = true;\r\n        });\r\n      }\r\n\r\n      const properties = await Property.find(query)\r\n        .limit(100)\r\n        .lean();\r\n\r\n      return properties;\r\n    } catch (error) {\r\n      logger.error('Error getting property candidates:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between two users\r\n   */\r\n  static async calculateDistance(\r\n    userId: Types.ObjectId,\r\n    targetUserId: Types.ObjectId\r\n  ): Promise<number> {\r\n    try {\r\n      const [userProfile, targetProfile] = await Promise.all([\r\n        Profile.findOne({ userId }),\r\n        Profile.findOne({ userId: targetUserId })\r\n      ]);\r\n\r\n      if (!(userProfile as any)?.location?.coordinates || !(targetProfile as any)?.location?.coordinates) {\r\n        return 999; // Return high distance if coordinates not available\r\n      }\r\n\r\n      const userCoords = (userProfile as any).location.coordinates;\r\n      const targetCoords = (targetProfile as any).location.coordinates;\r\n\r\n      return this.calculateDistanceBetweenCoordinates(\r\n        userCoords.coordinates[1], // latitude\r\n        userCoords.coordinates[0], // longitude\r\n        targetCoords.coordinates[1],\r\n        targetCoords.coordinates[0]\r\n      );\r\n    } catch (error) {\r\n      logger.error('Error calculating distance between users:', error);\r\n      return 999;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between user and property\r\n   */\r\n  static async calculatePropertyDistance(\r\n    userId: Types.ObjectId,\r\n    propertyId: Types.ObjectId\r\n  ): Promise<number> {\r\n    try {\r\n      const [userProfile, property] = await Promise.all([\r\n        Profile.findOne({ userId }),\r\n        Property.findById(propertyId)\r\n      ]);\r\n\r\n      if (!(userProfile as any)?.location?.coordinates || !property?.location?.coordinates) {\r\n        return 999;\r\n      }\r\n\r\n      const userCoords = (userProfile as any).location.coordinates;\r\n      const propertyCoords = property.location.coordinates;\r\n\r\n      return this.calculateDistanceBetweenCoordinates(\r\n        userCoords.coordinates[1],\r\n        userCoords.coordinates[0],\r\n        propertyCoords.coordinates[1],\r\n        propertyCoords.coordinates[0]\r\n      );\r\n    } catch (error) {\r\n      logger.error('Error calculating distance to property:', error);\r\n      return 999;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between two coordinates using Haversine formula\r\n   */\r\n  static calculateDistanceBetweenCoordinates(\r\n    lat1: number,\r\n    lon1: number,\r\n    lat2: number,\r\n    lon2: number\r\n  ): number {\r\n    const R = 6371; // Earth's radius in kilometers\r\n    const dLat = this.toRadians(lat2 - lat1);\r\n    const dLon = this.toRadians(lon2 - lon1);\r\n    \r\n    const a = \r\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *\r\n      Math.sin(dLon / 2) * Math.sin(dLon / 2);\r\n    \r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    const distance = R * c;\r\n    \r\n    return Math.round(distance * 100) / 100; // Round to 2 decimal places\r\n  }\r\n\r\n  /**\r\n   * Check if two users are in the same state\r\n   */\r\n  static async checkStateMatch(\r\n    userId: Types.ObjectId,\r\n    targetId: Types.ObjectId,\r\n    targetType: 'user' | 'property'\r\n  ): Promise<boolean> {\r\n    try {\r\n      const userProfile = await Profile.findOne({ userId });\r\n      \r\n      if (targetType === 'user') {\r\n        const targetProfile = await Profile.findOne({ userId: targetId });\r\n        return (userProfile as any)?.location?.state === (targetProfile as any)?.location?.state;\r\n      } else {\r\n        const property = await Property.findById(targetId);\r\n        return (userProfile as any)?.location?.state === property?.location?.state;\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error checking state match:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate match reasons based on compatibility factors\r\n   */\r\n  static generateMatchReasons(factors: CompatibilityFactors): string[] {\r\n    const reasons: string[] = [];\r\n\r\n    if (factors.location >= 80) {\r\n      reasons.push('Great location compatibility');\r\n    }\r\n    if (factors.budget >= 80) {\r\n      reasons.push('Perfect budget match');\r\n    }\r\n    if (factors.lifestyle >= 80) {\r\n      reasons.push('Similar lifestyle preferences');\r\n    }\r\n    if (factors.schedule >= 80) {\r\n      reasons.push('Compatible schedules');\r\n    }\r\n    if (factors.cleanliness >= 80) {\r\n      reasons.push('Matching cleanliness standards');\r\n    }\r\n    if (factors.socialLevel >= 80) {\r\n      reasons.push('Similar social preferences');\r\n    }\r\n    if (factors.overall >= 90) {\r\n      reasons.push('Exceptional overall compatibility');\r\n    }\r\n\r\n    return reasons.length > 0 ? reasons : ['Good potential match'];\r\n  }\r\n\r\n  /**\r\n   * Generate property-specific match reasons\r\n   */\r\n  static generatePropertyMatchReasons(factors: CompatibilityFactors, property: any): string[] {\r\n    const reasons: string[] = [];\r\n\r\n    if (factors.location >= 80) {\r\n      reasons.push(`Great location in ${property.location.city}`);\r\n    }\r\n    if (factors.budget >= 80) {\r\n      reasons.push('Within your budget range');\r\n    }\r\n    if (factors.preferences >= 80) {\r\n      reasons.push('Matches your property preferences');\r\n    }\r\n    if (property.amenities?.wifi) {\r\n      reasons.push('Has WiFi');\r\n    }\r\n    if (property.amenities?.parking) {\r\n      reasons.push('Parking available');\r\n    }\r\n    if (property.amenities?.security) {\r\n      reasons.push('Secure building');\r\n    }\r\n    if (property.amenities?.generator) {\r\n      reasons.push('Backup power available');\r\n    }\r\n\r\n    return reasons.length > 0 ? reasons : ['Good property match'];\r\n  }\r\n\r\n  /**\r\n   * Check if two lifestyle preferences are compatible\r\n   */\r\n  static isCompatibleLifestyle(pref1: string, pref2: string): boolean {\r\n    const compatibilityMap: { [key: string]: string[] } = {\r\n      'yes': ['yes', 'occasionally'],\r\n      'no': ['no', 'rarely', 'never'],\r\n      'occasionally': ['yes', 'occasionally', 'rarely'],\r\n      'rarely': ['no', 'occasionally', 'rarely'],\r\n      'never': ['no', 'rarely', 'never'],\r\n      'love': ['love', 'okay'],\r\n      'okay': ['love', 'okay', 'rarely'],\r\n      'allergic': ['no', 'never'],\r\n      'frequent': ['frequent', 'occasional'],\r\n      'occasional': ['frequent', 'occasional', 'rare'],\r\n      'rare': ['occasional', 'rare', 'never']\r\n    };\r\n\r\n    return compatibilityMap[pref1]?.includes(pref2) || false;\r\n  }\r\n\r\n  /**\r\n   * Check if two schedule preferences are compatible\r\n   */\r\n  static isCompatibleSchedule(pref1: string, pref2: string): boolean {\r\n    const compatibilityMap: { [key: string]: string[] } = {\r\n      'day_shift': ['day_shift', 'flexible'],\r\n      'night_shift': ['night_shift', 'flexible'],\r\n      'flexible': ['day_shift', 'night_shift', 'flexible', 'student'],\r\n      'student': ['student', 'flexible'],\r\n      'early_bird': ['early_bird', 'flexible'],\r\n      'night_owl': ['night_owl', 'flexible'],\r\n      'very_social': ['very_social', 'social'],\r\n      'social': ['very_social', 'social', 'moderate'],\r\n      'moderate': ['social', 'moderate', 'private'],\r\n      'private': ['moderate', 'private']\r\n    };\r\n\r\n    return compatibilityMap[pref1]?.includes(pref2) || false;\r\n  }\r\n\r\n  /**\r\n   * Convert degrees to radians\r\n   */\r\n  private static toRadians(degrees: number): number {\r\n    return degrees * (Math.PI / 180);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,MAAAA,YAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,UAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAE,eAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAAG,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAaI,eAAe;EAE1B;;;EAGA,aAAaC,qBAAqBA,CAChCC,MAAsB,EACtBC,SAA4B;IAAA;IAAAT,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAE5B,IAAI;MACF,MAAMU,KAAK;MAAA;MAAA,CAAAX,aAAA,GAAAC,CAAA,OAAQ;QACjBW,GAAG,EAAE;UAAEC,GAAG,EAAEL;QAAM,CAAE;QAAE;QACtBM,WAAW,EAAE,QAAQ;QAAE;QACvBC,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAE;OACX;MAED;MAAA;MAAAhB,aAAA,GAAAC,CAAA;MACA,IAAIQ,SAAS,CAACQ,gBAAgB,KAAK,KAAK,EAAE;QAAA;QAAAjB,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACxCU,KAAK,CAAC,gBAAgB,CAAC,GAAGF,SAAS,CAACQ,gBAAgB;MACtD,CAAC;MAAA;MAAA;QAAAjB,aAAA,GAAAkB,CAAA;MAAA;MAED;MACA,MAAMC,WAAW;MAAA;MAAA,CAAAnB,aAAA,GAAAC,CAAA,QAAG,IAAImB,IAAI,EAAE,CAACC,WAAW,EAAE;MAC5C,MAAMC,YAAY;MAAA;MAAA,CAAAtB,aAAA,GAAAC,CAAA,QAAGkB,WAAW,GAAGV,SAAS,CAACc,QAAQ,CAACC,GAAG;MACzD,MAAMC,YAAY;MAAA;MAAA,CAAAzB,aAAA,GAAAC,CAAA,QAAGkB,WAAW,GAAGV,SAAS,CAACc,QAAQ,CAACG,GAAG;MAAC;MAAA1B,aAAA,GAAAC,CAAA;MAE1DU,KAAK,CAAC,qBAAqB,CAAC,GAAG;QAC7BgB,IAAI,EAAE,IAAIP,IAAI,CAAC,GAAGE,YAAY,QAAQ,CAAC;QACvCM,IAAI,EAAE,IAAIR,IAAI,CAAC,GAAGK,YAAY,QAAQ;OACvC;MAED,MAAMI,UAAU;MAAA;MAAA,CAAA7B,aAAA,GAAAC,CAAA,QAAG,MAAMF,YAAA,CAAA+B,IAAI,CAACC,IAAI,CAACpB,KAAK,CAAC,CACtCqB,QAAQ,CAAC,SAAS,CAAC,CACnBC,KAAK,CAAC,GAAG,CAAC,CAAC;MAAA,CACXC,IAAI,EAAE;MAAC;MAAAlC,aAAA,GAAAC,CAAA;MAEV,OAAO4B,UAAU;IACnB,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MACdI,QAAA,CAAA+B,MAAM,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAAC;MAAAnC,aAAA,GAAAC,CAAA;MAC1D,OAAO,EAAE;IACX;EACF;EAEA;;;EAGA,aAAaoC,qBAAqBA,CAChC7B,MAAsB,EACtBC,SAA4B;IAAA;IAAAT,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAE5B,IAAI;MACF,MAAMU,KAAK;MAAA;MAAA,CAAAX,aAAA,GAAAC,CAAA,QAAQ;QACjBqC,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;UAAE3B,GAAG,EAAEL;QAAM,CAAE;QAAE;QAC1B,sBAAsB,EAAE;UACtBmB,IAAI,EAAElB,SAAS,CAACgC,WAAW,CAACf,GAAG;UAC/BE,IAAI,EAAEnB,SAAS,CAACgC,WAAW,CAACjB;;OAE/B;MAED;MAAA;MAAAxB,aAAA,GAAAC,CAAA;MACA,IAAIQ,SAAS,CAACiC,mBAAmB,CAACC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA5C,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QAC1DU,KAAK,CAACkC,YAAY,GAAG;UAAEC,GAAG,EAAErC,SAAS,CAACiC,mBAAmB,CAACC;QAAa,CAAE;MAC3E,CAAC;MAAA;MAAA;QAAA3C,aAAA,GAAAkB,CAAA;MAAA;MAED;MAAAlB,aAAA,GAAAC,CAAA;MACA,IAAIQ,SAAS,CAACiC,mBAAmB,CAACK,eAAe,GAAG,CAAC,EAAE;QAAA;QAAA/C,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACrDU,KAAK,CAACqC,QAAQ,GAAG;UAAErB,IAAI,EAAElB,SAAS,CAACiC,mBAAmB,CAACK;QAAe,CAAE;MAC1E,CAAC;MAAA;MAAA;QAAA/C,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAC,CAAA;MAED,IAAIQ,SAAS,CAACiC,mBAAmB,CAACO,gBAAgB,GAAG,CAAC,EAAE;QAAA;QAAAjD,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACtDU,KAAK,CAACuC,SAAS,GAAG;UAAEvB,IAAI,EAAElB,SAAS,CAACiC,mBAAmB,CAACO;QAAgB,CAAE;MAC5E,CAAC;MAAA;MAAA;QAAAjD,aAAA,GAAAkB,CAAA;MAAA;MAED;MAAAlB,aAAA,GAAAC,CAAA;MACA,IAAIQ,SAAS,CAAC0C,eAAe,CAACP,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA5C,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACxCU,KAAK,CAAC,gBAAgB,CAAC,GAAG;UAAEmC,GAAG,EAAErC,SAAS,CAAC0C;QAAe,CAAE;MAC9D,CAAC;MAAA;MAAA;QAAAnD,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAC,CAAA;MAED,IAAIQ,SAAS,CAAC2C,eAAe,CAACR,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA5C,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACxCU,KAAK,CAAC,eAAe,CAAC,GAAG;UAAEmC,GAAG,EAAErC,SAAS,CAAC2C;QAAe,CAAE;MAC7D,CAAC;MAAA;MAAA;QAAApD,aAAA,GAAAkB,CAAA;MAAA;MAED;MAAAlB,aAAA,GAAAC,CAAA;MACA,IAAIQ,SAAS,CAACiC,mBAAmB,CAACW,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;QAAA;QAAA5C,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACtDQ,SAAS,CAACiC,mBAAmB,CAACW,SAAS,CAACC,OAAO,CAACC,OAAO,IAAG;UAAA;UAAAvD,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UACxDU,KAAK,CAAC,aAAa4C,OAAO,EAAE,CAAC,GAAG,IAAI;QACtC,CAAC,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAvD,aAAA,GAAAkB,CAAA;MAAA;MAED,MAAMsC,UAAU;MAAA;MAAA,CAAAxD,aAAA,GAAAC,CAAA,QAAG,MAAME,UAAA,CAAAsD,QAAQ,CAAC1B,IAAI,CAACpB,KAAK,CAAC,CAC1CsB,KAAK,CAAC,GAAG,CAAC,CACVC,IAAI,EAAE;MAAC;MAAAlC,aAAA,GAAAC,CAAA;MAEV,OAAOuD,UAAU;IACnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MACdI,QAAA,CAAA+B,MAAM,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAAC;MAAAnC,aAAA,GAAAC,CAAA;MAC1D,OAAO,EAAE;IACX;EACF;EAEA;;;EAGA,aAAayD,iBAAiBA,CAC5BlD,MAAsB,EACtBmD,YAA4B;IAAA;IAAA3D,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAE5B,IAAI;MACF,MAAM,CAAC2D,WAAW,EAAEC,aAAa,CAAC;MAAA;MAAA,CAAA7D,aAAA,GAAAC,CAAA,QAAG,MAAM6D,OAAO,CAACC,GAAG,CAAC,CACrD3D,eAAA,CAAA4D,OAAO,CAACC,OAAO,CAAC;QAAEzD;MAAM,CAAE,CAAC,EAC3BJ,eAAA,CAAA4D,OAAO,CAACC,OAAO,CAAC;QAAEzD,MAAM,EAAEmD;MAAY,CAAE,CAAC,CAC1C,CAAC;MAAC;MAAA3D,aAAA,GAAAC,CAAA;MAEH;MAAI;MAAA,CAAAD,aAAA,GAAAkB,CAAA,WAAE0C,WAAmB,EAAEM,QAAQ,EAAEC,WAAW;MAAA;MAAA,CAAAnE,aAAA,GAAAkB,CAAA,UAAI,CAAE2C,aAAqB,EAAEK,QAAQ,EAAEC,WAAW,GAAE;QAAA;QAAAnE,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QAClG,OAAO,GAAG,CAAC,CAAC;MACd,CAAC;MAAA;MAAA;QAAAD,aAAA,GAAAkB,CAAA;MAAA;MAED,MAAMkD,UAAU;MAAA;MAAA,CAAApE,aAAA,GAAAC,CAAA,QAAI2D,WAAmB,CAACM,QAAQ,CAACC,WAAW;MAC5D,MAAME,YAAY;MAAA;MAAA,CAAArE,aAAA,GAAAC,CAAA,QAAI4D,aAAqB,CAACK,QAAQ,CAACC,WAAW;MAAC;MAAAnE,aAAA,GAAAC,CAAA;MAEjE,OAAO,IAAI,CAACqE,mCAAmC,CAC7CF,UAAU,CAACD,WAAW,CAAC,CAAC,CAAC;MAAE;MAC3BC,UAAU,CAACD,WAAW,CAAC,CAAC,CAAC;MAAE;MAC3BE,YAAY,CAACF,WAAW,CAAC,CAAC,CAAC,EAC3BE,YAAY,CAACF,WAAW,CAAC,CAAC,CAAC,CAC5B;IACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MACdI,QAAA,CAAA+B,MAAM,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MAAC;MAAAnC,aAAA,GAAAC,CAAA;MACjE,OAAO,GAAG;IACZ;EACF;EAEA;;;EAGA,aAAasE,yBAAyBA,CACpC/D,MAAsB,EACtBgE,UAA0B;IAAA;IAAAxE,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAE1B,IAAI;MACF,MAAM,CAAC2D,WAAW,EAAEa,QAAQ,CAAC;MAAA;MAAA,CAAAzE,aAAA,GAAAC,CAAA,QAAG,MAAM6D,OAAO,CAACC,GAAG,CAAC,CAChD3D,eAAA,CAAA4D,OAAO,CAACC,OAAO,CAAC;QAAEzD;MAAM,CAAE,CAAC,EAC3BL,UAAA,CAAAsD,QAAQ,CAACiB,QAAQ,CAACF,UAAU,CAAC,CAC9B,CAAC;MAAC;MAAAxE,aAAA,GAAAC,CAAA;MAEH;MAAI;MAAA,CAAAD,aAAA,GAAAkB,CAAA,YAAE0C,WAAmB,EAAEM,QAAQ,EAAEC,WAAW;MAAA;MAAA,CAAAnE,aAAA,GAAAkB,CAAA,WAAI,CAACuD,QAAQ,EAAEP,QAAQ,EAAEC,WAAW,GAAE;QAAA;QAAAnE,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAC,CAAA;QACpF,OAAO,GAAG;MACZ,CAAC;MAAA;MAAA;QAAAD,aAAA,GAAAkB,CAAA;MAAA;MAED,MAAMkD,UAAU;MAAA;MAAA,CAAApE,aAAA,GAAAC,CAAA,QAAI2D,WAAmB,CAACM,QAAQ,CAACC,WAAW;MAC5D,MAAMQ,cAAc;MAAA;MAAA,CAAA3E,aAAA,GAAAC,CAAA,QAAGwE,QAAQ,CAACP,QAAQ,CAACC,WAAW;MAAC;MAAAnE,aAAA,GAAAC,CAAA;MAErD,OAAO,IAAI,CAACqE,mCAAmC,CAC7CF,UAAU,CAACD,WAAW,CAAC,CAAC,CAAC,EACzBC,UAAU,CAACD,WAAW,CAAC,CAAC,CAAC,EACzBQ,cAAc,CAACR,WAAW,CAAC,CAAC,CAAC,EAC7BQ,cAAc,CAACR,WAAW,CAAC,CAAC,CAAC,CAC9B;IACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MACdI,QAAA,CAAA+B,MAAM,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAAC;MAAAnC,aAAA,GAAAC,CAAA;MAC/D,OAAO,GAAG;IACZ;EACF;EAEA;;;EAGA,OAAOqE,mCAAmCA,CACxCM,IAAY,EACZC,IAAY,EACZC,IAAY,EACZC,IAAY;IAAA;IAAA/E,aAAA,GAAAU,CAAA;IAEZ,MAAMsE,CAAC;IAAA;IAAA,CAAAhF,aAAA,GAAAC,CAAA,QAAG,IAAI,EAAC,CAAC;IAChB,MAAMgF,IAAI;IAAA;IAAA,CAAAjF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACiF,SAAS,CAACJ,IAAI,GAAGF,IAAI,CAAC;IACxC,MAAMO,IAAI;IAAA;IAAA,CAAAnF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACiF,SAAS,CAACH,IAAI,GAAGF,IAAI,CAAC;IAExC,MAAMO,CAAC;IAAA;IAAA,CAAApF,aAAA,GAAAC,CAAA,QACLoF,IAAI,CAACC,GAAG,CAACL,IAAI,GAAG,CAAC,CAAC,GAAGI,IAAI,CAACC,GAAG,CAACL,IAAI,GAAG,CAAC,CAAC,GACvCI,IAAI,CAACE,GAAG,CAAC,IAAI,CAACL,SAAS,CAACN,IAAI,CAAC,CAAC,GAAGS,IAAI,CAACE,GAAG,CAAC,IAAI,CAACL,SAAS,CAACJ,IAAI,CAAC,CAAC,GAC/DO,IAAI,CAACC,GAAG,CAACH,IAAI,GAAG,CAAC,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,IAAI,GAAG,CAAC,CAAC;IAEzC,MAAMK,CAAC;IAAA;IAAA,CAAAxF,aAAA,GAAAC,CAAA,QAAG,CAAC,GAAGoF,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,IAAI,CAACN,CAAC,CAAC,EAAEC,IAAI,CAACK,IAAI,CAAC,CAAC,GAAGN,CAAC,CAAC,CAAC;IACxD,MAAMO,QAAQ;IAAA;IAAA,CAAA3F,aAAA,GAAAC,CAAA,QAAG+E,CAAC,GAAGQ,CAAC;IAAC;IAAAxF,aAAA,GAAAC,CAAA;IAEvB,OAAOoF,IAAI,CAACO,KAAK,CAACD,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;EAC3C;EAEA;;;EAGA,aAAaE,eAAeA,CAC1BrF,MAAsB,EACtBsF,QAAwB,EACxBC,UAA+B;IAAA;IAAA/F,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAE/B,IAAI;MACF,MAAM2D,WAAW;MAAA;MAAA,CAAA5D,aAAA,GAAAC,CAAA,QAAG,MAAMG,eAAA,CAAA4D,OAAO,CAACC,OAAO,CAAC;QAAEzD;MAAM,CAAE,CAAC;MAAC;MAAAR,aAAA,GAAAC,CAAA;MAEtD,IAAI8F,UAAU,KAAK,MAAM,EAAE;QAAA;QAAA/F,aAAA,GAAAkB,CAAA;QACzB,MAAM2C,aAAa;QAAA;QAAA,CAAA7D,aAAA,GAAAC,CAAA,QAAG,MAAMG,eAAA,CAAA4D,OAAO,CAACC,OAAO,CAAC;UAAEzD,MAAM,EAAEsF;QAAQ,CAAE,CAAC;QAAC;QAAA9F,aAAA,GAAAC,CAAA;QAClE,OAAQ2D,WAAmB,EAAEM,QAAQ,EAAE8B,KAAK,KAAMnC,aAAqB,EAAEK,QAAQ,EAAE8B,KAAK;MAC1F,CAAC,MAAM;QAAA;QAAAhG,aAAA,GAAAkB,CAAA;QACL,MAAMuD,QAAQ;QAAA;QAAA,CAAAzE,aAAA,GAAAC,CAAA,QAAG,MAAME,UAAA,CAAAsD,QAAQ,CAACiB,QAAQ,CAACoB,QAAQ,CAAC;QAAC;QAAA9F,aAAA,GAAAC,CAAA;QACnD,OAAQ2D,WAAmB,EAAEM,QAAQ,EAAE8B,KAAK,KAAKvB,QAAQ,EAAEP,QAAQ,EAAE8B,KAAK;MAC5E;IACF,CAAC,CAAC,OAAO7D,KAAK,EAAE;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MACdI,QAAA,CAAA+B,MAAM,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAAnC,aAAA,GAAAC,CAAA;MACnD,OAAO,KAAK;IACd;EACF;EAEA;;;EAGA,OAAOgG,oBAAoBA,CAACC,OAA6B;IAAA;IAAAlG,aAAA,GAAAU,CAAA;IACvD,MAAMyF,OAAO;IAAA;IAAA,CAAAnG,aAAA,GAAAC,CAAA,QAAa,EAAE;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAE7B,IAAIiG,OAAO,CAAChC,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAAlE,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC1BkG,OAAO,CAACC,IAAI,CAAC,8BAA8B,CAAC;IAC9C,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACG,MAAM,IAAI,EAAE,EAAE;MAAA;MAAArG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MACxBkG,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;IACtC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACI,SAAS,IAAI,EAAE,EAAE;MAAA;MAAAtG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC3BkG,OAAO,CAACC,IAAI,CAAC,+BAA+B,CAAC;IAC/C,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACK,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAAvG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC1BkG,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;IACtC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACM,WAAW,IAAI,EAAE,EAAE;MAAA;MAAAxG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC7BkG,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;IAChD,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACO,WAAW,IAAI,EAAE,EAAE;MAAA;MAAAzG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC7BkG,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC;IAC5C,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACQ,OAAO,IAAI,EAAE,EAAE;MAAA;MAAA1G,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MACzBkG,OAAO,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACnD,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IAED,OAAOkG,OAAO,CAACvD,MAAM,GAAG,CAAC;IAAA;IAAA,CAAA5C,aAAA,GAAAkB,CAAA,WAAGiF,OAAO;IAAA;IAAA,CAAAnG,aAAA,GAAAkB,CAAA,WAAG,CAAC,sBAAsB,CAAC;EAChE;EAEA;;;EAGA,OAAOyF,4BAA4BA,CAACT,OAA6B,EAAEzB,QAAa;IAAA;IAAAzE,aAAA,GAAAU,CAAA;IAC9E,MAAMyF,OAAO;IAAA;IAAA,CAAAnG,aAAA,GAAAC,CAAA,QAAa,EAAE;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAE7B,IAAIiG,OAAO,CAAChC,QAAQ,IAAI,EAAE,EAAE;MAAA;MAAAlE,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC1BkG,OAAO,CAACC,IAAI,CAAC,qBAAqB3B,QAAQ,CAACP,QAAQ,CAAC0C,IAAI,EAAE,CAAC;IAC7D,CAAC;IAAA;IAAA;MAAA5G,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACG,MAAM,IAAI,EAAE,EAAE;MAAA;MAAArG,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MACxBkG,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;IAC1C,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIiG,OAAO,CAACW,WAAW,IAAI,EAAE,EAAE;MAAA;MAAA7G,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC7BkG,OAAO,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACnD,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIwE,QAAQ,CAACpB,SAAS,EAAEyD,IAAI,EAAE;MAAA;MAAA9G,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC5BkG,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;IAC1B,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIwE,QAAQ,CAACpB,SAAS,EAAE0D,OAAO,EAAE;MAAA;MAAA/G,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAC/BkG,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;IACnC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIwE,QAAQ,CAACpB,SAAS,EAAE2D,QAAQ,EAAE;MAAA;MAAAhH,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MAChCkG,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAC;IACjC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IACD,IAAIwE,QAAQ,CAACpB,SAAS,EAAE4D,SAAS,EAAE;MAAA;MAAAjH,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAC,CAAA;MACjCkG,OAAO,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACxC,CAAC;IAAA;IAAA;MAAApG,aAAA,GAAAkB,CAAA;IAAA;IAAAlB,aAAA,GAAAC,CAAA;IAED,OAAOkG,OAAO,CAACvD,MAAM,GAAG,CAAC;IAAA;IAAA,CAAA5C,aAAA,GAAAkB,CAAA,WAAGiF,OAAO;IAAA;IAAA,CAAAnG,aAAA,GAAAkB,CAAA,WAAG,CAAC,qBAAqB,CAAC;EAC/D;EAEA;;;EAGA,OAAOgG,qBAAqBA,CAACC,KAAa,EAAEC,KAAa;IAAA;IAAApH,aAAA,GAAAU,CAAA;IACvD,MAAM2G,gBAAgB;IAAA;IAAA,CAAArH,aAAA,GAAAC,CAAA,SAAgC;MACpD,KAAK,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC;MAC9B,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;MAC/B,cAAc,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,CAAC;MACjD,QAAQ,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,CAAC;MAC1C,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;MAClC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MACxB,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;MAClC,UAAU,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;MAC3B,UAAU,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;MACtC,YAAY,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,CAAC;MAChD,MAAM,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO;KACvC;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAkB,CAAA,WAAAmG,gBAAgB,CAACF,KAAK,CAAC,EAAEG,QAAQ,CAACF,KAAK,CAAC;IAAA;IAAA,CAAApH,aAAA,GAAAkB,CAAA,WAAI,KAAK;EAC1D;EAEA;;;EAGA,OAAOqG,oBAAoBA,CAACJ,KAAa,EAAEC,KAAa;IAAA;IAAApH,aAAA,GAAAU,CAAA;IACtD,MAAM2G,gBAAgB;IAAA;IAAA,CAAArH,aAAA,GAAAC,CAAA,SAAgC;MACpD,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;MACtC,aAAa,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;MAC1C,UAAU,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC;MAC/D,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;MAClC,YAAY,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;MACxC,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;MACtC,aAAa,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;MACxC,QAAQ,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC;MAC/C,UAAU,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;MAC7C,SAAS,EAAE,CAAC,UAAU,EAAE,SAAS;KAClC;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAkB,CAAA,WAAAmG,gBAAgB,CAACF,KAAK,CAAC,EAAEG,QAAQ,CAACF,KAAK,CAAC;IAAA;IAAA,CAAApH,aAAA,GAAAkB,CAAA,WAAI,KAAK;EAC1D;EAEA;;;EAGQ,OAAOgE,SAASA,CAACsC,OAAe;IAAA;IAAAxH,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IACtC,OAAOuH,OAAO,IAAInC,IAAI,CAACoC,EAAE,GAAG,GAAG,CAAC;EAClC;;AACD;AAAAzH,aAAA,GAAAC,CAAA;AApUDyH,OAAA,CAAApH,eAAA,GAAAA,eAAA", "ignoreList": []}