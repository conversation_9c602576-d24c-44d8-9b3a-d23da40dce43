38de6ad50d1d10eb99626171eba1041d
"use strict";

/* istanbul ignore next */
function cov_124l034041() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\PropertyFavorite.ts";
  var hash = "edd602163a59e61bdd9dd078167086c14b1a7585";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\PropertyFavorite.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 62
        }
      },
      "36": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 34
        }
      },
      "37": {
        start: {
          line: 37,
          column: 19
        },
        end: {
          line: 37,
          column: 52
        }
      },
      "38": {
        start: {
          line: 38,
          column: 31
        },
        end: {
          line: 53,
          column: 2
        }
      },
      "39": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 77
        }
      },
      "40": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 59
        }
      },
      "41": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 63
        }
      },
      "42": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 59,
          column: 96
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\PropertyFavorite.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA6D;AAS7D,MAAM,sBAAsB,GAAG,IAAI,iBAAM,CAAoB;IAC3D,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,UAAU,EAAE;QACV,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,UAAU;QACf,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;CACF,EAAE;IACD,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AAEH,6DAA6D;AAC7D,sBAAsB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAE7E,8BAA8B;AAC9B,sBAAsB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3D,sBAAsB,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAElD,QAAA,gBAAgB,GAAG,kBAAQ,CAAC,KAAK,CAAoB,kBAAkB,EAAE,sBAAsB,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\PropertyFavorite.ts"],
      sourcesContent: ["import mongoose, { Document, Schema, Types } from 'mongoose';\r\n\r\nexport interface IPropertyFavorite extends Document {\r\n  userId: Types.ObjectId;\r\n  propertyId: Types.ObjectId;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PropertyFavoriteSchema = new Schema<IPropertyFavorite>({\r\n  userId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'User',\r\n    required: true,\r\n    index: true\r\n  },\r\n  propertyId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'Property',\r\n    required: true,\r\n    index: true\r\n  }\r\n}, {\r\n  timestamps: true\r\n});\r\n\r\n// Compound index to ensure unique user-property combinations\r\nPropertyFavoriteSchema.index({ userId: 1, propertyId: 1 }, { unique: true });\r\n\r\n// Index for efficient queries\r\nPropertyFavoriteSchema.index({ userId: 1, createdAt: -1 });\r\nPropertyFavoriteSchema.index({ propertyId: 1, createdAt: -1 });\r\n\r\nexport const PropertyFavorite = mongoose.model<IPropertyFavorite>('PropertyFavorite', PropertyFavoriteSchema);\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "edd602163a59e61bdd9dd078167086c14b1a7585"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_124l034041 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_124l034041();
var __createBinding =
/* istanbul ignore next */
(cov_124l034041().s[0]++,
/* istanbul ignore next */
(cov_124l034041().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_124l034041().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_124l034041().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_124l034041().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_124l034041().f[0]++;
  cov_124l034041().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_124l034041().b[2][0]++;
    cov_124l034041().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_124l034041().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_124l034041().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_124l034041().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_124l034041().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_124l034041().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_124l034041().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_124l034041().b[5][1]++,
  /* istanbul ignore next */
  (cov_124l034041().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_124l034041().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_124l034041().b[3][0]++;
    cov_124l034041().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_124l034041().f[1]++;
        cov_124l034041().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_124l034041().b[3][1]++;
  }
  cov_124l034041().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_124l034041().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_124l034041().f[2]++;
  cov_124l034041().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_124l034041().b[7][0]++;
    cov_124l034041().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_124l034041().b[7][1]++;
  }
  cov_124l034041().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_124l034041().s[11]++,
/* istanbul ignore next */
(cov_124l034041().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_124l034041().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_124l034041().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_124l034041().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_124l034041().f[3]++;
  cov_124l034041().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_124l034041().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_124l034041().f[4]++;
  cov_124l034041().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_124l034041().s[14]++,
/* istanbul ignore next */
(cov_124l034041().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_124l034041().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_124l034041().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_124l034041().f[5]++;
  cov_124l034041().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_124l034041().f[6]++;
    cov_124l034041().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_124l034041().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_124l034041().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_124l034041().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_124l034041().s[17]++, []);
      /* istanbul ignore next */
      cov_124l034041().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_124l034041().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_124l034041().b[12][0]++;
          cov_124l034041().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_124l034041().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_124l034041().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_124l034041().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_124l034041().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_124l034041().f[8]++;
    cov_124l034041().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_124l034041().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_124l034041().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_124l034041().b[13][0]++;
      cov_124l034041().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_124l034041().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_124l034041().s[26]++, {});
    /* istanbul ignore next */
    cov_124l034041().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_124l034041().b[15][0]++;
      cov_124l034041().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_124l034041().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_124l034041().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_124l034041().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_124l034041().b[16][0]++;
          cov_124l034041().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_124l034041().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_124l034041().b[15][1]++;
    }
    cov_124l034041().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_124l034041().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_124l034041().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_124l034041().s[36]++;
exports.PropertyFavorite = void 0;
const mongoose_1 =
/* istanbul ignore next */
(cov_124l034041().s[37]++, __importStar(require("mongoose")));
const PropertyFavoriteSchema =
/* istanbul ignore next */
(cov_124l034041().s[38]++, new mongoose_1.Schema({
  userId: {
    type: mongoose_1.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  propertyId: {
    type: mongoose_1.Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
    index: true
  }
}, {
  timestamps: true
}));
// Compound index to ensure unique user-property combinations
/* istanbul ignore next */
cov_124l034041().s[39]++;
PropertyFavoriteSchema.index({
  userId: 1,
  propertyId: 1
}, {
  unique: true
});
// Index for efficient queries
/* istanbul ignore next */
cov_124l034041().s[40]++;
PropertyFavoriteSchema.index({
  userId: 1,
  createdAt: -1
});
/* istanbul ignore next */
cov_124l034041().s[41]++;
PropertyFavoriteSchema.index({
  propertyId: 1,
  createdAt: -1
});
/* istanbul ignore next */
cov_124l034041().s[42]++;
exports.PropertyFavorite = mongoose_1.default.model('PropertyFavorite', PropertyFavoriteSchema);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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