{"version": 3, "names": ["cov_1<PERSON><PERSON><PERSON><PERSON>", "actualCoverage", "s", "express_1", "require", "propertyFavorite_controller_1", "auth_1", "validation_1", "property_validators_1", "router", "Router", "get", "getPopularProperties", "use", "authenticate", "post", "validateRequest", "favoritePropertySchema", "addToFavorites", "delete", "validateObjectId", "removeFromFavorites", "getUserFavorites", "getFavoritesCount", "checkFavoriteStatus", "bulkUpdateFavorites", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyFavorite.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  addToFavorites,\r\n  removeFromFavorites,\r\n  getUserFavorites,\r\n  checkFavoriteStatus,\r\n  getFavoritesCount,\r\n  getPopularProperties,\r\n  bulkUpdateFavorites\r\n} from '../controllers/propertyFavorite.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport { favoritePropertySchema } from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n// Public routes\r\nrouter.get('/popular', getPopularProperties);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Favorite management\r\nrouter.post('/add', validateRequest(favoritePropertySchema), addToFavorites);\r\nrouter.delete('/:propertyId', validateObjectId('propertyId'), removeFromFavorites);\r\n\r\n// User favorites\r\nrouter.get('/', getUserFavorites);\r\nrouter.get('/count', getFavoritesCount);\r\n\r\n// Check favorite status\r\nrouter.get('/:propertyId/status', validateObjectId('propertyId'), checkFavoriteStatus);\r\n\r\n// Bulk operations\r\nrouter.post('/bulk', bulkUpdateFavorites);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AA1BA,MAAAC,SAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,6BAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAAE,MAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,qBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA,MAAMK,MAAM;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAO,MAAM,GAAE;AAEvB;AAAA;AAAAV,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,UAAU,EAAEN,6BAAA,CAAAO,oBAAoB,CAAC;AAE5C;AAAA;AAAAZ,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACI,GAAG,CAACP,MAAA,CAAAQ,YAAY,CAAC;AAExB;AAAA;AAAAd,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACM,IAAI,CAAC,MAAM,EAAE,IAAAR,YAAA,CAAAS,eAAe,EAACR,qBAAA,CAAAS,sBAAsB,CAAC,EAAEZ,6BAAA,CAAAa,cAAc,CAAC;AAAC;AAAAlB,cAAA,GAAAE,CAAA;AAC7EO,MAAM,CAACU,MAAM,CAAC,cAAc,EAAE,IAAAZ,YAAA,CAAAa,gBAAgB,EAAC,YAAY,CAAC,EAAEf,6BAAA,CAAAgB,mBAAmB,CAAC;AAElF;AAAA;AAAArB,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,GAAG,EAAEN,6BAAA,CAAAiB,gBAAgB,CAAC;AAAC;AAAAtB,cAAA,GAAAE,CAAA;AAClCO,MAAM,CAACE,GAAG,CAAC,QAAQ,EAAEN,6BAAA,CAAAkB,iBAAiB,CAAC;AAEvC;AAAA;AAAAvB,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,qBAAqB,EAAE,IAAAJ,YAAA,CAAAa,gBAAgB,EAAC,YAAY,CAAC,EAAEf,6BAAA,CAAAmB,mBAAmB,CAAC;AAEtF;AAAA;AAAAxB,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACM,IAAI,CAAC,OAAO,EAAEV,6BAAA,CAAAoB,mBAAmB,CAAC;AAAC;AAAAzB,cAAA,GAAAE,CAAA;AAE1CwB,OAAA,CAAAC,OAAA,GAAelB,MAAM", "ignoreList": []}