{"version": 3, "names": ["cov_ditq4oyys", "actualCoverage", "s", "appError_1", "require", "validateRequest", "schema", "source", "b", "f", "req", "_res", "next", "dataToValidate", "error", "value", "validate", "abort<PERSON><PERSON><PERSON>", "allowUnknown", "stripUnknown", "validationErrors", "details", "map", "detail", "field", "path", "join", "message", "context", "AppError", "type", "exports", "validate<PERSON><PERSON><PERSON><PERSON>", "schemas", "errors", "Object", "entries", "for<PERSON>ach", "sourceErrors", "push", "length", "validateFileUpload", "options", "maxSize", "allowedTypes", "required", "maxFiles", "files", "file", "filesToValidate", "uploadedFile", "size", "originalname", "Math", "round", "includes", "mimetype", "validateObjectId", "paramName", "id", "params", "objectIdRegex", "test", "sanitizeInput", "sanitizeValue", "replace", "trim", "Array", "isArray", "sanitized", "key", "val", "body", "query", "validateRateLimit", "requests", "Map", "identifier", "ip", "connection", "remoteAddress", "now", "Date", "windowStart", "windowMs", "resetTime", "delete", "userRequests", "get", "set", "count", "maxRequests"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\validation.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport <PERSON><PERSON> from 'joi';\r\nimport { AppError } from '../utils/appError';\r\n\r\n/**\r\n * Validation middleware factory\r\n * @param schema - Joi validation schema\r\n * @param source - Where to validate (body, query, params)\r\n */\r\nexport const validateRequest = (\r\n  schema: Joi.ObjectSchema,\r\n  source: 'body' | 'query' | 'params' = 'body'\r\n) => {\r\n  return (req: Request, _res: Response, next: NextFunction) => {\r\n    const dataToValidate = req[source];\r\n    \r\n    const { error, value } = schema.validate(dataToValidate, {\r\n      abortEarly: false, // Return all validation errors\r\n      allowUnknown: false, // Don't allow unknown fields\r\n      stripUnknown: true // Remove unknown fields\r\n    });\r\n\r\n    if (error) {\r\n      const validationErrors = error.details.map(detail => ({\r\n        field: detail.path.join('.'),\r\n        message: detail.message,\r\n        value: detail.context?.value\r\n      }));\r\n\r\n      return next(new AppError('Validation failed', 400, {\r\n        type: 'VALIDATION_ERROR',\r\n        details: validationErrors\r\n      }));\r\n    }\r\n\r\n    // Replace the original data with validated and sanitized data\r\n    req[source] = value;\r\n    next();\r\n  };\r\n};\r\n\r\n/**\r\n * Validate multiple sources (body, query, params)\r\n */\r\nexport const validateMultiple = (schemas: {\r\n  body?: Joi.ObjectSchema;\r\n  query?: Joi.ObjectSchema;\r\n  params?: Joi.ObjectSchema;\r\n}) => {\r\n  return (req: Request, _res: Response, next: NextFunction) => {\r\n    const errors: any[] = [];\r\n\r\n    // Validate each source\r\n    Object.entries(schemas).forEach(([source, schema]) => {\r\n      if (schema) {\r\n        const { error, value } = schema.validate(req[source as keyof Request], {\r\n          abortEarly: false,\r\n          allowUnknown: false,\r\n          stripUnknown: true\r\n        });\r\n\r\n        if (error) {\r\n          const sourceErrors = error.details.map(detail => ({\r\n            source,\r\n            field: detail.path.join('.'),\r\n            message: detail.message,\r\n            value: detail.context?.value\r\n          }));\r\n          errors.push(...sourceErrors);\r\n        } else {\r\n          // Replace with validated data\r\n          (req as any)[source] = value;\r\n        }\r\n      }\r\n    });\r\n\r\n    if (errors.length > 0) {\r\n      return next(new AppError('Validation failed', 400, {\r\n        type: 'VALIDATION_ERROR',\r\n        details: errors\r\n      }));\r\n    }\r\n\r\n    next();\r\n  };\r\n};\r\n\r\n/**\r\n * Validate file uploads\r\n */\r\nexport const validateFileUpload = (options: {\r\n  maxSize?: number; // in bytes\r\n  allowedTypes?: string[];\r\n  required?: boolean;\r\n  maxFiles?: number;\r\n}) => {\r\n  return (req: Request, _res: Response, next: NextFunction) => {\r\n    const {\r\n      maxSize = 10 * 1024 * 1024, // 10MB default\r\n      allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],\r\n      required = false,\r\n      maxFiles = 1\r\n    } = options;\r\n\r\n    const files = req.files as Express.Multer.File[] | undefined;\r\n    const file = req.file as Express.Multer.File | undefined;\r\n\r\n    // Check if file is required\r\n    if (required && !file && (!files || files.length === 0)) {\r\n      return next(new AppError('File upload is required', 400));\r\n    }\r\n\r\n    // If no files and not required, continue\r\n    if (!file && (!files || files.length === 0)) {\r\n      return next();\r\n    }\r\n\r\n    const filesToValidate = files || (file ? [file] : []);\r\n\r\n    // Check number of files\r\n    if (filesToValidate.length > maxFiles) {\r\n      return next(new AppError(`Cannot upload more than ${maxFiles} files`, 400));\r\n    }\r\n\r\n    // Validate each file\r\n    for (const uploadedFile of filesToValidate) {\r\n      // Check file size\r\n      if (uploadedFile.size > maxSize) {\r\n        return next(new AppError(\r\n          `File ${uploadedFile.originalname} is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB`,\r\n          400\r\n        ));\r\n      }\r\n\r\n      // Check file type\r\n      if (!allowedTypes.includes(uploadedFile.mimetype)) {\r\n        return next(new AppError(\r\n          `File ${uploadedFile.originalname} has invalid type. Allowed types: ${allowedTypes.join(', ')}`,\r\n          400\r\n        ));\r\n      }\r\n    }\r\n\r\n    next();\r\n  };\r\n};\r\n\r\n/**\r\n * Validate MongoDB ObjectId\r\n */\r\nexport const validateObjectId = (paramName: string = 'id') => {\r\n  return (req: Request, _res: Response, next: NextFunction) => {\r\n    const id = req.params[paramName];\r\n    \r\n    if (!id) {\r\n      return next(new AppError(`${paramName} parameter is required`, 400));\r\n    }\r\n\r\n    // MongoDB ObjectId validation regex\r\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\r\n    \r\n    if (!objectIdRegex.test(id)) {\r\n      return next(new AppError(`Invalid ${paramName} format`, 400));\r\n    }\r\n\r\n    next();\r\n  };\r\n};\r\n\r\n/**\r\n * Sanitize input to prevent XSS and injection attacks\r\n */\r\nexport const sanitizeInput = (req: Request, _res: Response, next: NextFunction) => {\r\n  const sanitizeValue = (value: any): any => {\r\n    if (typeof value === 'string') {\r\n      // Remove potentially dangerous characters\r\n      return value\r\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '') // Remove script tags\r\n        .replace(/javascript:/gi, '') // Remove javascript: protocol\r\n        .replace(/on\\w+\\s*=/gi, '') // Remove event handlers\r\n        .trim();\r\n    }\r\n    \r\n    if (Array.isArray(value)) {\r\n      return value.map(sanitizeValue);\r\n    }\r\n    \r\n    if (value && typeof value === 'object') {\r\n      const sanitized: any = {};\r\n      for (const [key, val] of Object.entries(value)) {\r\n        sanitized[key] = sanitizeValue(val);\r\n      }\r\n      return sanitized;\r\n    }\r\n    \r\n    return value;\r\n  };\r\n\r\n  // Sanitize body, query, and params\r\n  if (req.body) {\r\n    req.body = sanitizeValue(req.body);\r\n  }\r\n  \r\n  if (req.query) {\r\n    req.query = sanitizeValue(req.query);\r\n  }\r\n  \r\n  if (req.params) {\r\n    req.params = sanitizeValue(req.params);\r\n  }\r\n\r\n  next();\r\n};\r\n\r\n/**\r\n * Rate limiting validation\r\n */\r\nexport const validateRateLimit = (options: {\r\n  windowMs: number;\r\n  maxRequests: number;\r\n  message?: string;\r\n}) => {\r\n  const requests = new Map<string, { count: number; resetTime: number }>();\r\n  \r\n  return (req: Request, _res: Response, next: NextFunction) => {\r\n    const identifier = req.ip || req.connection.remoteAddress || 'unknown';\r\n    const now = Date.now();\r\n    const windowStart = now - options.windowMs;\r\n    \r\n    // Clean up old entries\r\n    for (const [key, value] of requests.entries()) {\r\n      if (value.resetTime < windowStart) {\r\n        requests.delete(key);\r\n      }\r\n    }\r\n    \r\n    const userRequests = requests.get(identifier);\r\n    \r\n    if (!userRequests) {\r\n      requests.set(identifier, { count: 1, resetTime: now });\r\n      return next();\r\n    }\r\n    \r\n    if (userRequests.resetTime < windowStart) {\r\n      requests.set(identifier, { count: 1, resetTime: now });\r\n      return next();\r\n    }\r\n    \r\n    if (userRequests.count >= options.maxRequests) {\r\n      return next(new AppError(\r\n        options.message || 'Too many requests, please try again later',\r\n        429\r\n      ));\r\n    }\r\n    \r\n    userRequests.count++;\r\n    next();\r\n  };\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAjBN,MAAAC,UAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA;;;;;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAKO,MAAMG,eAAe,GAAGA,CAC7BC,MAAwB,EACxBC,MAAA;AAAA;AAAA,CAAAP,aAAA,GAAAQ,CAAA,UAAsC,MAAM,MAC1C;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EACF,OAAO,CAACQ,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;IAAA;IAAAZ,aAAA,GAAAS,CAAA;IAC1D,MAAMI,cAAc;IAAA;IAAA,CAAAb,aAAA,GAAAE,CAAA,OAAGQ,GAAG,CAACH,MAAM,CAAC;IAElC,MAAM;MAAEO,KAAK;MAAEC;IAAK,CAAE;IAAA;IAAA,CAAAf,aAAA,GAAAE,CAAA,OAAGI,MAAM,CAACU,QAAQ,CAACH,cAAc,EAAE;MACvDI,UAAU,EAAE,KAAK;MAAE;MACnBC,YAAY,EAAE,KAAK;MAAE;MACrBC,YAAY,EAAE,IAAI,CAAC;KACpB,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAEH,IAAIY,KAAK,EAAE;MAAA;MAAAd,aAAA,GAAAQ,CAAA;MACT,MAAMY,gBAAgB;MAAA;MAAA,CAAApB,aAAA,GAAAE,CAAA,OAAGY,KAAK,CAACO,OAAO,CAACC,GAAG,CAACC,MAAM,IAAK;QAAA;QAAAvB,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAE,CAAA;QAAA;UACpDsB,KAAK,EAAED,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;UAC5BC,OAAO,EAAEJ,MAAM,CAACI,OAAO;UACvBZ,KAAK,EAAEQ,MAAM,CAACK,OAAO,EAAEb;SACxB;OAAC,CAAC;MAAC;MAAAf,aAAA,GAAAE,CAAA;MAEJ,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjDC,IAAI,EAAE,kBAAkB;QACxBT,OAAO,EAAED;OACV,CAAC,CAAC;IACL,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACAQ,GAAG,CAACH,MAAM,CAAC,GAAGQ,KAAK;IAAC;IAAAf,aAAA,GAAAE,CAAA;IACpBU,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AA9BW6B,OAAA,CAAA1B,eAAe,GAAAA,eAAA;AAgC5B;;;AAAA;AAAAL,aAAA,GAAAE,CAAA;AAGO,MAAM8B,gBAAgB,GAAIC,OAIhC,IAAI;EAAA;EAAAjC,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EACH,OAAO,CAACQ,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;IAAA;IAAAZ,aAAA,GAAAS,CAAA;IAC1D,MAAMyB,MAAM;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAU,EAAE;IAExB;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACAiC,MAAM,CAACC,OAAO,CAACH,OAAO,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC9B,MAAM,EAAED,MAAM,CAAC,KAAI;MAAA;MAAAN,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAE,CAAA;MACnD,IAAII,MAAM,EAAE;QAAA;QAAAN,aAAA,GAAAQ,CAAA;QACV,MAAM;UAAEM,KAAK;UAAEC;QAAK,CAAE;QAAA;QAAA,CAAAf,aAAA,GAAAE,CAAA,QAAGI,MAAM,CAACU,QAAQ,CAACN,GAAG,CAACH,MAAuB,CAAC,EAAE;UACrEU,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBC,YAAY,EAAE;SACf,CAAC;QAAC;QAAAnB,aAAA,GAAAE,CAAA;QAEH,IAAIY,KAAK,EAAE;UAAA;UAAAd,aAAA,GAAAQ,CAAA;UACT,MAAM8B,YAAY;UAAA;UAAA,CAAAtC,aAAA,GAAAE,CAAA,QAAGY,KAAK,CAACO,OAAO,CAACC,GAAG,CAACC,MAAM,IAAK;YAAA;YAAAvB,aAAA,GAAAS,CAAA;YAAAT,aAAA,GAAAE,CAAA;YAAA;cAChDK,MAAM;cACNiB,KAAK,EAAED,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;cAC5BC,OAAO,EAAEJ,MAAM,CAACI,OAAO;cACvBZ,KAAK,EAAEQ,MAAM,CAACK,OAAO,EAAEb;aACxB;WAAC,CAAC;UAAC;UAAAf,aAAA,GAAAE,CAAA;UACJgC,MAAM,CAACK,IAAI,CAAC,GAAGD,YAAY,CAAC;QAC9B,CAAC,MAAM;UAAA;UAAAtC,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAE,CAAA;UACL;UACCQ,GAAW,CAACH,MAAM,CAAC,GAAGQ,KAAK;QAC9B;MACF,CAAC;MAAA;MAAA;QAAAf,aAAA,GAAAQ,CAAA;MAAA;IACH,CAAC,CAAC;IAAC;IAAAR,aAAA,GAAAE,CAAA;IAEH,IAAIgC,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAxC,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACrB,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjDC,IAAI,EAAE,kBAAkB;QACxBT,OAAO,EAAEa;OACV,CAAC,CAAC;IACL,CAAC;IAAA;IAAA;MAAAlC,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAEDU,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AAzCW6B,OAAA,CAAAC,gBAAgB,GAAAA,gBAAA;AA2C7B;;;AAAA;AAAAhC,aAAA,GAAAE,CAAA;AAGO,MAAMuC,kBAAkB,GAAIC,OAKlC,IAAI;EAAA;EAAA1C,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EACH,OAAO,CAACQ,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;IAAA;IAAAZ,aAAA,GAAAS,CAAA;IAC1D,MAAM;MACJkC,OAAO;MAAA;MAAA,CAAA3C,aAAA,GAAAQ,CAAA,UAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MAAE;MAC5BoC,YAAY;MAAA;MAAA,CAAA5C,aAAA,GAAAQ,CAAA,UAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MACxDqC,QAAQ;MAAA;MAAA,CAAA7C,aAAA,GAAAQ,CAAA,UAAG,KAAK;MAChBsC,QAAQ;MAAA;MAAA,CAAA9C,aAAA,GAAAQ,CAAA,UAAG,CAAC;IAAA,CACb;IAAA;IAAA,CAAAR,aAAA,GAAAE,CAAA,QAAGwC,OAAO;IAEX,MAAMK,KAAK;IAAA;IAAA,CAAA/C,aAAA,GAAAE,CAAA,QAAGQ,GAAG,CAACqC,KAA0C;IAC5D,MAAMC,IAAI;IAAA;IAAA,CAAAhD,aAAA,GAAAE,CAAA,QAAGQ,GAAG,CAACsC,IAAuC;IAExD;IAAA;IAAAhD,aAAA,GAAAE,CAAA;IACA;IAAI;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAqC,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAQ,CAAA,WAAI,CAACwC,IAAI;IAAK;IAAA,CAAAhD,aAAA,GAAAQ,CAAA,YAACuC,KAAK;IAAA;IAAA,CAAA/C,aAAA,GAAAQ,CAAA,WAAIuC,KAAK,CAACP,MAAM,KAAK,CAAC,EAAC,EAAE;MAAA;MAAAxC,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACvD,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;IAAA;IAAA;MAAA7B,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA;IAAI;IAAA,CAAAF,aAAA,GAAAQ,CAAA,YAACwC,IAAI;IAAK;IAAA,CAAAhD,aAAA,GAAAQ,CAAA,YAACuC,KAAK;IAAA;IAAA,CAAA/C,aAAA,GAAAQ,CAAA,WAAIuC,KAAK,CAACP,MAAM,KAAK,CAAC,EAAC,EAAE;MAAA;MAAAxC,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC3C,OAAOU,IAAI,EAAE;IACf,CAAC;IAAA;IAAA;MAAAZ,aAAA,GAAAQ,CAAA;IAAA;IAED,MAAMyC,eAAe;IAAA;IAAA,CAAAjD,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAuC,KAAK;IAAA;IAAA,CAAA/C,aAAA,GAAAQ,CAAA,WAAKwC,IAAI;IAAA;IAAA,CAAAhD,aAAA,GAAAQ,CAAA,WAAG,CAACwC,IAAI,CAAC;IAAA;IAAA,CAAAhD,aAAA,GAAAQ,CAAA,WAAG,EAAE,EAAC;IAErD;IAAA;IAAAR,aAAA,GAAAE,CAAA;IACA,IAAI+C,eAAe,CAACT,MAAM,GAAGM,QAAQ,EAAE;MAAA;MAAA9C,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACrC,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,2BAA2BiB,QAAQ,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC7E,CAAC;IAAA;IAAA;MAAA9C,aAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,aAAA,GAAAE,CAAA;IACA,KAAK,MAAMgD,YAAY,IAAID,eAAe,EAAE;MAAA;MAAAjD,aAAA,GAAAE,CAAA;MAC1C;MACA,IAAIgD,YAAY,CAACC,IAAI,GAAGR,OAAO,EAAE;QAAA;QAAA3C,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QAC/B,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CACtB,QAAQqB,YAAY,CAACE,YAAY,kCAAkCC,IAAI,CAACC,KAAK,CAACX,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,EACxG,GAAG,CACJ,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA3C,aAAA,GAAAQ,CAAA;MAAA;MAED;MAAAR,aAAA,GAAAE,CAAA;MACA,IAAI,CAAC0C,YAAY,CAACW,QAAQ,CAACL,YAAY,CAACM,QAAQ,CAAC,EAAE;QAAA;QAAAxD,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACjD,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CACtB,QAAQqB,YAAY,CAACE,YAAY,qCAAqCR,YAAY,CAAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAC/F,GAAG,CACJ,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA1B,aAAA,GAAAQ,CAAA;MAAA;IACH;IAAC;IAAAR,aAAA,GAAAE,CAAA;IAEDU,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AAvDW6B,OAAA,CAAAU,kBAAkB,GAAAA,kBAAA;AAyD/B;;;AAAA;AAAAzC,aAAA,GAAAE,CAAA;AAGO,MAAMuD,gBAAgB,GAAGA,CAACC,SAAA;AAAA;AAAA,CAAA1D,aAAA,GAAAQ,CAAA,WAAoB,IAAI,MAAI;EAAA;EAAAR,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAC3D,OAAO,CAACQ,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;IAAA;IAAAZ,aAAA,GAAAS,CAAA;IAC1D,MAAMkD,EAAE;IAAA;IAAA,CAAA3D,aAAA,GAAAE,CAAA,QAAGQ,GAAG,CAACkD,MAAM,CAACF,SAAS,CAAC;IAAC;IAAA1D,aAAA,GAAAE,CAAA;IAEjC,IAAI,CAACyD,EAAE,EAAE;MAAA;MAAA3D,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACP,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,GAAG6B,SAAS,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAAA;IAAA;MAAA1D,aAAA,GAAAQ,CAAA;IAAA;IAED;IACA,MAAMqD,aAAa;IAAA;IAAA,CAAA7D,aAAA,GAAAE,CAAA,QAAG,mBAAmB;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAE1C,IAAI,CAAC2D,aAAa,CAACC,IAAI,CAACH,EAAE,CAAC,EAAE;MAAA;MAAA3D,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC3B,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ,CAAC,WAAW6B,SAAS,SAAS,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;IAAA;IAAA;MAAA1D,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAEDU,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AAjBW6B,OAAA,CAAA0B,gBAAgB,GAAAA,gBAAA;AAmB7B;;;AAAA;AAAAzD,aAAA,GAAAE,CAAA;AAGO,MAAM6D,aAAa,GAAGA,CAACrD,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;EAAA;EAAAZ,aAAA,GAAAS,CAAA;EAAAT,aAAA,GAAAE,CAAA;EAChF,MAAM8D,aAAa,GAAIjD,KAAU,IAAS;IAAA;IAAAf,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAE,CAAA;IACxC,IAAI,OAAOa,KAAK,KAAK,QAAQ,EAAE;MAAA;MAAAf,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC7B;MACA,OAAOa,KAAK,CACTkD,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAAC;MAAA,CACnEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;MAAA,CAC7BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;MAAA,CAC3BC,IAAI,EAAE;IACX,CAAC;IAAA;IAAA;MAAAlE,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAED,IAAIiE,KAAK,CAACC,OAAO,CAACrD,KAAK,CAAC,EAAE;MAAA;MAAAf,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACxB,OAAOa,KAAK,CAACO,GAAG,CAAC0C,aAAa,CAAC;IACjC,CAAC;IAAA;IAAA;MAAAhE,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAED;IAAI;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAO,KAAK;IAAA;IAAA,CAAAf,aAAA,GAAAQ,CAAA,WAAI,OAAOO,KAAK,KAAK,QAAQ,GAAE;MAAA;MAAAf,aAAA,GAAAQ,CAAA;MACtC,MAAM6D,SAAS;MAAA;MAAA,CAAArE,aAAA,GAAAE,CAAA,QAAQ,EAAE;MAAC;MAAAF,aAAA,GAAAE,CAAA;MAC1B,KAAK,MAAM,CAACoE,GAAG,EAAEC,GAAG,CAAC,IAAIpC,MAAM,CAACC,OAAO,CAACrB,KAAK,CAAC,EAAE;QAAA;QAAAf,aAAA,GAAAE,CAAA;QAC9CmE,SAAS,CAACC,GAAG,CAAC,GAAGN,aAAa,CAACO,GAAG,CAAC;MACrC;MAAC;MAAAvE,aAAA,GAAAE,CAAA;MACD,OAAOmE,SAAS;IAClB,CAAC;IAAA;IAAA;MAAArE,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAED,OAAOa,KAAK;EACd,CAAC;EAED;EAAA;EAAAf,aAAA,GAAAE,CAAA;EACA,IAAIQ,GAAG,CAAC8D,IAAI,EAAE;IAAA;IAAAxE,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACZQ,GAAG,CAAC8D,IAAI,GAAGR,aAAa,CAACtD,GAAG,CAAC8D,IAAI,CAAC;EACpC,CAAC;EAAA;EAAA;IAAAxE,aAAA,GAAAQ,CAAA;EAAA;EAAAR,aAAA,GAAAE,CAAA;EAED,IAAIQ,GAAG,CAAC+D,KAAK,EAAE;IAAA;IAAAzE,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACbQ,GAAG,CAAC+D,KAAK,GAAGT,aAAa,CAACtD,GAAG,CAAC+D,KAAK,CAAC;EACtC,CAAC;EAAA;EAAA;IAAAzE,aAAA,GAAAQ,CAAA;EAAA;EAAAR,aAAA,GAAAE,CAAA;EAED,IAAIQ,GAAG,CAACkD,MAAM,EAAE;IAAA;IAAA5D,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACdQ,GAAG,CAACkD,MAAM,GAAGI,aAAa,CAACtD,GAAG,CAACkD,MAAM,CAAC;EACxC,CAAC;EAAA;EAAA;IAAA5D,aAAA,GAAAQ,CAAA;EAAA;EAAAR,aAAA,GAAAE,CAAA;EAEDU,IAAI,EAAE;AACR,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AAxCW6B,OAAA,CAAAgC,aAAa,GAAAA,aAAA;AA0C1B;;;AAAA;AAAA/D,aAAA,GAAAE,CAAA;AAGO,MAAMwE,iBAAiB,GAAIhC,OAIjC,IAAI;EAAA;EAAA1C,aAAA,GAAAS,CAAA;EACH,MAAMkE,QAAQ;EAAA;EAAA,CAAA3E,aAAA,GAAAE,CAAA,QAAG,IAAI0E,GAAG,EAAgD;EAAC;EAAA5E,aAAA,GAAAE,CAAA;EAEzE,OAAO,CAACQ,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAI;IAAA;IAAAZ,aAAA,GAAAS,CAAA;IAC1D,MAAMoE,UAAU;IAAA;IAAA,CAAA7E,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAAQ,CAAA,WAAAE,GAAG,CAACoE,EAAE;IAAA;IAAA,CAAA9E,aAAA,GAAAQ,CAAA,WAAIE,GAAG,CAACqE,UAAU,CAACC,aAAa;IAAA;IAAA,CAAAhF,aAAA,GAAAQ,CAAA,WAAI,SAAS;IACtE,MAAMyE,GAAG;IAAA;IAAA,CAAAjF,aAAA,GAAAE,CAAA,QAAGgF,IAAI,CAACD,GAAG,EAAE;IACtB,MAAME,WAAW;IAAA;IAAA,CAAAnF,aAAA,GAAAE,CAAA,QAAG+E,GAAG,GAAGvC,OAAO,CAAC0C,QAAQ;IAE1C;IAAA;IAAApF,aAAA,GAAAE,CAAA;IACA,KAAK,MAAM,CAACoE,GAAG,EAAEvD,KAAK,CAAC,IAAI4D,QAAQ,CAACvC,OAAO,EAAE,EAAE;MAAA;MAAApC,aAAA,GAAAE,CAAA;MAC7C,IAAIa,KAAK,CAACsE,SAAS,GAAGF,WAAW,EAAE;QAAA;QAAAnF,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QACjCyE,QAAQ,CAACW,MAAM,CAAChB,GAAG,CAAC;MACtB,CAAC;MAAA;MAAA;QAAAtE,aAAA,GAAAQ,CAAA;MAAA;IACH;IAEA,MAAM+E,YAAY;IAAA;IAAA,CAAAvF,aAAA,GAAAE,CAAA,QAAGyE,QAAQ,CAACa,GAAG,CAACX,UAAU,CAAC;IAAC;IAAA7E,aAAA,GAAAE,CAAA;IAE9C,IAAI,CAACqF,YAAY,EAAE;MAAA;MAAAvF,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACjByE,QAAQ,CAACc,GAAG,CAACZ,UAAU,EAAE;QAAEa,KAAK,EAAE,CAAC;QAAEL,SAAS,EAAEJ;MAAG,CAAE,CAAC;MAAC;MAAAjF,aAAA,GAAAE,CAAA;MACvD,OAAOU,IAAI,EAAE;IACf,CAAC;IAAA;IAAA;MAAAZ,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAED,IAAIqF,YAAY,CAACF,SAAS,GAAGF,WAAW,EAAE;MAAA;MAAAnF,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACxCyE,QAAQ,CAACc,GAAG,CAACZ,UAAU,EAAE;QAAEa,KAAK,EAAE,CAAC;QAAEL,SAAS,EAAEJ;MAAG,CAAE,CAAC;MAAC;MAAAjF,aAAA,GAAAE,CAAA;MACvD,OAAOU,IAAI,EAAE;IACf,CAAC;IAAA;IAAA;MAAAZ,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAED,IAAIqF,YAAY,CAACG,KAAK,IAAIhD,OAAO,CAACiD,WAAW,EAAE;MAAA;MAAA3F,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAC7C,OAAOU,IAAI,CAAC,IAAIT,UAAA,CAAA0B,QAAQ;MACtB;MAAA,CAAA7B,aAAA,GAAAQ,CAAA,WAAAkC,OAAO,CAACf,OAAO;MAAA;MAAA,CAAA3B,aAAA,GAAAQ,CAAA,WAAI,2CAA2C,GAC9D,GAAG,CACJ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAR,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAE,CAAA;IAEDqF,YAAY,CAACG,KAAK,EAAE;IAAC;IAAA1F,aAAA,GAAAE,CAAA;IACrBU,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAAC;AAAAZ,aAAA,GAAAE,CAAA;AAzCW6B,OAAA,CAAA2C,iBAAiB,GAAAA,iBAAA", "ignoreList": []}