16210b7e0b5f5e3c5c83a5435850d3f8
"use strict";

/* istanbul ignore next */
function cov_13ysamrg2a() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\property.controller.ts";
  var hash = "38c39f65fcc0e6ed43109304318f1ea5c851e5e7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\property.controller.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 326
        }
      },
      "4": {
        start: {
          line: 7,
          column: 19
        },
        end: {
          line: 7,
          column: 48
        }
      },
      "5": {
        start: {
          line: 8,
          column: 21
        },
        end: {
          line: 8,
          column: 69
        }
      },
      "6": {
        start: {
          line: 9,
          column: 17
        },
        end: {
          line: 9,
          column: 43
        }
      },
      "7": {
        start: {
          line: 10,
          column: 22
        },
        end: {
          line: 10,
          column: 53
        }
      },
      "8": {
        start: {
          line: 11,
          column: 19
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "9": {
        start: {
          line: 12,
          column: 21
        },
        end: {
          line: 12,
          column: 51
        }
      },
      "10": {
        start: {
          line: 13,
          column: 19
        },
        end: {
          line: 13,
          column: 38
        }
      },
      "11": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 54,
          column: 3
        }
      },
      "12": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 18,
          column: 32
        }
      },
      "13": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "14": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 75
        }
      },
      "15": {
        start: {
          line: 23,
          column: 17
        },
        end: {
          line: 23,
          column: 60
        }
      },
      "16": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "17": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 61
        }
      },
      "18": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "19": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 29,
          column: 107
        }
      },
      "20": {
        start: {
          line: 31,
          column: 25
        },
        end: {
          line: 36,
          column: 5
        }
      },
      "21": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "22": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 92
        }
      },
      "23": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 47,
          column: 5
        }
      },
      "24": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 46,
          column: 10
        }
      },
      "25": {
        start: {
          line: 48,
          column: 21
        },
        end: {
          line: 48,
          column: 58
        }
      },
      "26": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 26
        }
      },
      "27": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 81
        }
      },
      "28": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 53,
          column: 45
        }
      },
      "29": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 169,
          column: 3
        }
      },
      "30": {
        start: {
          line: 60,
          column: 9
        },
        end: {
          line: 60,
          column: 18
        }
      },
      "31": {
        start: {
          line: 62,
          column: 19
        },
        end: {
          line: 65,
          column: 5
        }
      },
      "32": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 69,
          column: 5
        }
      },
      "33": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 43
        }
      },
      "34": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "35": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 41
        }
      },
      "36": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "37": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 44
        }
      },
      "38": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 78,
          column: 67
        }
      },
      "39": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 67
        }
      },
      "40": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 80,
          column: 67
        }
      },
      "41": {
        start: {
          line: 80,
          column: 12
        },
        end: {
          line: 80,
          column: 67
        }
      },
      "42": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 85,
          column: 5
        }
      },
      "43": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 43
        }
      },
      "44": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "45": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 45
        }
      },
      "46": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "47": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 56
        }
      },
      "48": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "49": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 58
        }
      },
      "50": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 99,
          column: 5
        }
      },
      "51": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 56
        }
      },
      "52": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "53": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 48
        }
      },
      "54": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 105,
          column: 11
        }
      },
      "55": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 104,
          column: 57
        }
      },
      "56": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 110,
          column: 5
        }
      },
      "57": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 43
        }
      },
      "58": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 48
        }
      },
      "59": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "60": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 125,
          column: 11
        }
      },
      "61": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 26
        }
      },
      "62": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 129,
          column: 56
        }
      },
      "63": {
        start: {
          line: 131,
          column: 17
        },
        end: {
          line: 131,
          column: 51
        }
      },
      "64": {
        start: {
          line: 132,
          column: 32
        },
        end: {
          line: 140,
          column: 6
        }
      },
      "65": {
        start: {
          line: 142,
          column: 23
        },
        end: {
          line: 142,
          column: 55
        }
      },
      "66": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 49
        }
      },
      "67": {
        start: {
          line: 144,
          column: 24
        },
        end: {
          line: 144,
          column: 40
        }
      },
      "68": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 168,
          column: 44
        }
      },
      "69": {
        start: {
          line: 173,
          column: 0
        },
        end: {
          line: 193,
          column: 3
        }
      },
      "70": {
        start: {
          line: 174,
          column: 19
        },
        end: {
          line: 174,
          column: 29
        }
      },
      "71": {
        start: {
          line: 175,
          column: 19
        },
        end: {
          line: 175,
          column: 32
        }
      },
      "72": {
        start: {
          line: 176,
          column: 21
        },
        end: {
          line: 178,
          column: 53
        }
      },
      "73": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 181,
          column: 5
        }
      },
      "74": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 65
        }
      },
      "75": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "76": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 65
        }
      },
      "77": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 189,
          column: 5
        }
      },
      "78": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 40
        }
      },
      "79": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 192,
          column: 42
        }
      },
      "80": {
        start: {
          line: 197,
          column: 0
        },
        end: {
          line: 222,
          column: 3
        }
      },
      "81": {
        start: {
          line: 198,
          column: 19
        },
        end: {
          line: 198,
          column: 29
        }
      },
      "82": {
        start: {
          line: 199,
          column: 19
        },
        end: {
          line: 199,
          column: 32
        }
      },
      "83": {
        start: {
          line: 200,
          column: 21
        },
        end: {
          line: 200,
          column: 59
        }
      },
      "84": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "85": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 65
        }
      },
      "86": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "87": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 86
        }
      },
      "88": {
        start: {
          line: 209,
          column: 23
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "89": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 30
        }
      },
      "90": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 32
        }
      },
      "91": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 216,
          column: 32
        }
      },
      "92": {
        start: {
          line: 217,
          column: 28
        },
        end: {
          line: 217,
          column: 183
        }
      },
      "93": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 218,
          column: 71
        }
      },
      "94": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 221,
          column: 40
        }
      },
      "95": {
        start: {
          line: 226,
          column: 0
        },
        end: {
          line: 240,
          column: 3
        }
      },
      "96": {
        start: {
          line: 227,
          column: 19
        },
        end: {
          line: 227,
          column: 29
        }
      },
      "97": {
        start: {
          line: 228,
          column: 19
        },
        end: {
          line: 228,
          column: 32
        }
      },
      "98": {
        start: {
          line: 229,
          column: 21
        },
        end: {
          line: 229,
          column: 59
        }
      },
      "99": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 232,
          column: 5
        }
      },
      "100": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 65
        }
      },
      "101": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 236,
          column: 5
        }
      },
      "102": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 86
        }
      },
      "103": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 52
        }
      },
      "104": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 71
        }
      },
      "105": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 89
        }
      },
      "106": {
        start: {
          line: 244,
          column: 0
        },
        end: {
          line: 282,
          column: 3
        }
      },
      "107": {
        start: {
          line: 245,
          column: 19
        },
        end: {
          line: 245,
          column: 32
        }
      },
      "108": {
        start: {
          line: 246,
          column: 87
        },
        end: {
          line: 246,
          column: 96
        }
      },
      "109": {
        start: {
          line: 248,
          column: 19
        },
        end: {
          line: 248,
          column: 38
        }
      },
      "110": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "111": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 31
        }
      },
      "112": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 26
        }
      },
      "113": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 56
        }
      },
      "114": {
        start: {
          line: 256,
          column: 17
        },
        end: {
          line: 256,
          column: 51
        }
      },
      "115": {
        start: {
          line: 257,
          column: 32
        },
        end: {
          line: 264,
          column: 6
        }
      },
      "116": {
        start: {
          line: 266,
          column: 23
        },
        end: {
          line: 266,
          column: 55
        }
      },
      "117": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 281,
          column: 50
        }
      },
      "118": {
        start: {
          line: 286,
          column: 0
        },
        end: {
          line: 312,
          column: 3
        }
      },
      "119": {
        start: {
          line: 287,
          column: 19
        },
        end: {
          line: 287,
          column: 29
        }
      },
      "120": {
        start: {
          line: 288,
          column: 19
        },
        end: {
          line: 288,
          column: 32
        }
      },
      "121": {
        start: {
          line: 289,
          column: 21
        },
        end: {
          line: 289,
          column: 59
        }
      },
      "122": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 292,
          column: 5
        }
      },
      "123": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 291,
          column: 65
        }
      },
      "124": {
        start: {
          line: 294,
          column: 4
        },
        end: {
          line: 296,
          column: 5
        }
      },
      "125": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 87
        }
      },
      "126": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 300,
          column: 5
        }
      },
      "127": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 299,
          column: 102
        }
      },
      "128": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 303,
          column: 5
        }
      },
      "129": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 103
        }
      },
      "130": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 305,
          column: 31
        }
      },
      "131": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 306,
          column: 68
        }
      },
      "132": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 307,
          column: 26
        }
      },
      "133": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 308,
          column: 73
        }
      },
      "134": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 311,
          column: 42
        }
      },
      "135": {
        start: {
          line: 316,
          column: 0
        },
        end: {
          line: 338,
          column: 3
        }
      },
      "136": {
        start: {
          line: 317,
          column: 19
        },
        end: {
          line: 317,
          column: 29
        }
      },
      "137": {
        start: {
          line: 318,
          column: 19
        },
        end: {
          line: 318,
          column: 32
        }
      },
      "138": {
        start: {
          line: 319,
          column: 21
        },
        end: {
          line: 319,
          column: 59
        }
      },
      "139": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "140": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 321,
          column: 65
        }
      },
      "141": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 326,
          column: 5
        }
      },
      "142": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 98
        }
      },
      "143": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 337,
          column: 52
        }
      },
      "144": {
        start: {
          line: 342,
          column: 0
        },
        end: {
          line: 487,
          column: 3
        }
      },
      "145": {
        start: {
          line: 343,
          column: 133
        },
        end: {
          line: 343,
          column: 141
        }
      },
      "146": {
        start: {
          line: 345,
          column: 21
        },
        end: {
          line: 353,
          column: 5
        }
      },
      "147": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 366,
          column: 5
        }
      },
      "148": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 360,
          column: 11
        }
      },
      "149": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 365,
          column: 11
        }
      },
      "150": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 372,
          column: 5
        }
      },
      "151": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 371,
          column: 11
        }
      },
      "152": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 378,
          column: 5
        }
      },
      "153": {
        start: {
          line: 375,
          column: 8
        },
        end: {
          line: 377,
          column: 11
        }
      },
      "154": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 391,
          column: 5
        }
      },
      "155": {
        start: {
          line: 381,
          column: 27
        },
        end: {
          line: 381,
          column: 29
        }
      },
      "156": {
        start: {
          line: 382,
          column: 8
        },
        end: {
          line: 383,
          column: 47
        }
      },
      "157": {
        start: {
          line: 383,
          column: 12
        },
        end: {
          line: 383,
          column: 47
        }
      },
      "158": {
        start: {
          line: 384,
          column: 8
        },
        end: {
          line: 385,
          column: 47
        }
      },
      "159": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 385,
          column: 47
        }
      },
      "160": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 390,
          column: 11
        }
      },
      "161": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 397,
          column: 5
        }
      },
      "162": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 396,
          column: 11
        }
      },
      "163": {
        start: {
          line: 398,
          column: 4
        },
        end: {
          line: 402,
          column: 5
        }
      },
      "164": {
        start: {
          line: 399,
          column: 8
        },
        end: {
          line: 401,
          column: 11
        }
      },
      "165": {
        start: {
          line: 404,
          column: 4
        },
        end: {
          line: 415,
          column: 5
        }
      },
      "166": {
        start: {
          line: 405,
          column: 30
        },
        end: {
          line: 405,
          column: 32
        }
      },
      "167": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 407,
          column: 76
        }
      },
      "168": {
        start: {
          line: 407,
          column: 12
        },
        end: {
          line: 407,
          column: 76
        }
      },
      "169": {
        start: {
          line: 408,
          column: 8
        },
        end: {
          line: 409,
          column: 78
        }
      },
      "170": {
        start: {
          line: 409,
          column: 12
        },
        end: {
          line: 409,
          column: 78
        }
      },
      "171": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 411,
          column: 76
        }
      },
      "172": {
        start: {
          line: 411,
          column: 12
        },
        end: {
          line: 411,
          column: 76
        }
      },
      "173": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 414,
          column: 11
        }
      },
      "174": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 425,
          column: 5
        }
      },
      "175": {
        start: {
          line: 418,
          column: 29
        },
        end: {
          line: 418,
          column: 31
        }
      },
      "176": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 421,
          column: 11
        }
      },
      "177": {
        start: {
          line: 420,
          column: 12
        },
        end: {
          line: 420,
          column: 56
        }
      },
      "178": {
        start: {
          line: 422,
          column: 8
        },
        end: {
          line: 424,
          column: 11
        }
      },
      "179": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 445,
          column: 7
        }
      },
      "180": {
        start: {
          line: 446,
          column: 4
        },
        end: {
          line: 448,
          column: 7
        }
      },
      "181": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 459,
          column: 5
        }
      },
      "182": {
        start: {
          line: 451,
          column: 8
        },
        end: {
          line: 453,
          column: 11
        }
      },
      "183": {
        start: {
          line: 456,
          column: 8
        },
        end: {
          line: 458,
          column: 11
        }
      },
      "184": {
        start: {
          line: 461,
          column: 17
        },
        end: {
          line: 461,
          column: 51
        }
      },
      "185": {
        start: {
          line: 462,
          column: 4
        },
        end: {
          line: 462,
          column: 62
        }
      },
      "186": {
        start: {
          line: 464,
          column: 37
        },
        end: {
          line: 470,
          column: 6
        }
      },
      "187": {
        start: {
          line: 471,
          column: 18
        },
        end: {
          line: 471,
          column: 43
        }
      },
      "188": {
        start: {
          line: 472,
          column: 23
        },
        end: {
          line: 472,
          column: 55
        }
      },
      "189": {
        start: {
          line: 473,
          column: 4
        },
        end: {
          line: 486,
          column: 49
        }
      },
      "190": {
        start: {
          line: 491,
          column: 0
        },
        end: {
          line: 541,
          column: 3
        }
      },
      "191": {
        start: {
          line: 492,
          column: 19
        },
        end: {
          line: 492,
          column: 32
        }
      },
      "192": {
        start: {
          line: 493,
          column: 27
        },
        end: {
          line: 493,
          column: 36
        }
      },
      "193": {
        start: {
          line: 495,
          column: 17
        },
        end: {
          line: 495,
          column: 80
        }
      },
      "194": {
        start: {
          line: 496,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "195": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 497,
          column: 61
        }
      },
      "196": {
        start: {
          line: 500,
          column: 31
        },
        end: {
          line: 503,
          column: 5
        }
      },
      "197": {
        start: {
          line: 506,
          column: 20
        },
        end: {
          line: 506,
          column: 62
        }
      },
      "198": {
        start: {
          line: 507,
          column: 24
        },
        end: {
          line: 507,
          column: 57
        }
      },
      "199": {
        start: {
          line: 508,
          column: 4
        },
        end: {
          line: 529,
          column: 5
        }
      },
      "200": {
        start: {
          line: 509,
          column: 22
        },
        end: {
          line: 509,
          column: 52
        }
      },
      "201": {
        start: {
          line: 511,
          column: 8
        },
        end: {
          line: 513,
          column: 9
        }
      },
      "202": {
        start: {
          line: 512,
          column: 12
        },
        end: {
          line: 512,
          column: 75
        }
      },
      "203": {
        start: {
          line: 515,
          column: 8
        },
        end: {
          line: 520,
          column: 9
        }
      },
      "204": {
        start: {
          line: 516,
          column: 12
        },
        end: {
          line: 519,
          column: 14
        }
      },
      "205": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 524,
          column: 9
        }
      },
      "206": {
        start: {
          line: 523,
          column: 12
        },
        end: {
          line: 523,
          column: 80
        }
      },
      "207": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 528,
          column: 9
        }
      },
      "208": {
        start: {
          line: 527,
          column: 12
        },
        end: {
          line: 527,
          column: 56
        }
      },
      "209": {
        start: {
          line: 531,
          column: 24
        },
        end: {
          line: 535,
          column: 15
        }
      },
      "210": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 540,
          column: 54
        }
      },
      "211": {
        start: {
          line: 545,
          column: 0
        },
        end: {
          line: 563,
          column: 3
        }
      },
      "212": {
        start: {
          line: 546,
          column: 63
        },
        end: {
          line: 546,
          column: 72
        }
      },
      "213": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 549,
          column: 5
        }
      },
      "214": {
        start: {
          line: 548,
          column: 8
        },
        end: {
          line: 548,
          column: 82
        }
      },
      "215": {
        start: {
          line: 550,
          column: 29
        },
        end: {
          line: 553,
          column: 15
        }
      },
      "216": {
        start: {
          line: 554,
          column: 4
        },
        end: {
          line: 562,
          column: 51
        }
      },
      "217": {
        start: {
          line: 567,
          column: 0
        },
        end: {
          line: 592,
          column: 3
        }
      },
      "218": {
        start: {
          line: 568,
          column: 26
        },
        end: {
          line: 568,
          column: 68
        }
      },
      "219": {
        start: {
          line: 569,
          column: 29
        },
        end: {
          line: 569,
          column: 91
        }
      },
      "220": {
        start: {
          line: 570,
          column: 4
        },
        end: {
          line: 591,
          column: 38
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 17,
            column: 54
          },
          end: {
            line: 17,
            column: 55
          }
        },
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 54,
            column: 1
          }
        },
        line: 17
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 58,
            column: 53
          },
          end: {
            line: 58,
            column: 54
          }
        },
        loc: {
          start: {
            line: 58,
            column: 73
          },
          end: {
            line: 169,
            column: 1
          }
        },
        line: 58
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 103,
            column: 29
          }
        },
        loc: {
          start: {
            line: 103,
            column: 39
          },
          end: {
            line: 105,
            column: 9
          }
        },
        line: 103
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 173,
            column: 51
          },
          end: {
            line: 173,
            column: 52
          }
        },
        loc: {
          start: {
            line: 173,
            column: 71
          },
          end: {
            line: 193,
            column: 1
          }
        },
        line: 173
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 197,
            column: 54
          },
          end: {
            line: 197,
            column: 55
          }
        },
        loc: {
          start: {
            line: 197,
            column: 74
          },
          end: {
            line: 222,
            column: 1
          }
        },
        line: 197
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 226,
            column: 54
          },
          end: {
            line: 226,
            column: 55
          }
        },
        loc: {
          start: {
            line: 226,
            column: 74
          },
          end: {
            line: 240,
            column: 1
          }
        },
        line: 226
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 244,
            column: 58
          },
          end: {
            line: 244,
            column: 59
          }
        },
        loc: {
          start: {
            line: 244,
            column: 78
          },
          end: {
            line: 282,
            column: 1
          }
        },
        line: 244
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 286,
            column: 55
          },
          end: {
            line: 286,
            column: 56
          }
        },
        loc: {
          start: {
            line: 286,
            column: 75
          },
          end: {
            line: 312,
            column: 1
          }
        },
        line: 286
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 316,
            column: 60
          },
          end: {
            line: 316,
            column: 61
          }
        },
        loc: {
          start: {
            line: 316,
            column: 80
          },
          end: {
            line: 338,
            column: 1
          }
        },
        line: 316
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 342,
            column: 56
          },
          end: {
            line: 342,
            column: 57
          }
        },
        loc: {
          start: {
            line: 342,
            column: 76
          },
          end: {
            line: 487,
            column: 1
          }
        },
        line: 342
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 419,
            column: 26
          },
          end: {
            line: 419,
            column: 27
          }
        },
        loc: {
          start: {
            line: 419,
            column: 39
          },
          end: {
            line: 421,
            column: 9
          }
        },
        line: 419
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 491,
            column: 62
          },
          end: {
            line: 491,
            column: 63
          }
        },
        loc: {
          start: {
            line: 491,
            column: 82
          },
          end: {
            line: 541,
            column: 1
          }
        },
        line: 491
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 545,
            column: 59
          },
          end: {
            line: 545,
            column: 60
          }
        },
        loc: {
          start: {
            line: 545,
            column: 79
          },
          end: {
            line: 563,
            column: 1
          }
        },
        line: 545
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 567,
            column: 51
          },
          end: {
            line: 567,
            column: 52
          }
        },
        loc: {
          start: {
            line: 567,
            column: 72
          },
          end: {
            line: 592,
            column: 1
          }
        },
        line: 567
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "4": {
        loc: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "5": {
        loc: {
          start: {
            line: 28,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 4
          },
          end: {
            line: 30,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "6": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "7": {
        loc: {
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 56
          }
        }, {
          start: {
            line: 38,
            column: 60
          },
          end: {
            line: 38,
            column: 82
          }
        }],
        line: 38
      },
      "8": {
        loc: {
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 47,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 47,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "9": {
        loc: {
          start: {
            line: 59,
            column: 12
          },
          end: {
            line: 59,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 19
          },
          end: {
            line: 59,
            column: 20
          }
        }],
        line: 59
      },
      "10": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 30
          },
          end: {
            line: 59,
            column: 32
          }
        }],
        line: 59
      },
      "11": {
        loc: {
          start: {
            line: 59,
            column: 132
          },
          end: {
            line: 59,
            column: 149
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 141
          },
          end: {
            line: 59,
            column: 149
          }
        }],
        line: 59
      },
      "12": {
        loc: {
          start: {
            line: 59,
            column: 151
          },
          end: {
            line: 59,
            column: 171
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 160
          },
          end: {
            line: 59,
            column: 171
          }
        }],
        line: 59
      },
      "13": {
        loc: {
          start: {
            line: 59,
            column: 173
          },
          end: {
            line: 59,
            column: 191
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 185
          },
          end: {
            line: 59,
            column: 191
          }
        }],
        line: 59
      },
      "14": {
        loc: {
          start: {
            line: 59,
            column: 222
          },
          end: {
            line: 59,
            column: 235
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 59,
            column: 231
          },
          end: {
            line: 59,
            column: 235
          }
        }],
        line: 59
      },
      "15": {
        loc: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 69,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 69,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "16": {
        loc: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "17": {
        loc: {
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "18": {
        loc: {
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 75,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 75,
            column: 16
          }
        }, {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 28
          }
        }],
        line: 75
      },
      "19": {
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 78,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 78,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "20": {
        loc: {
          start: {
            line: 79,
            column: 8
          },
          end: {
            line: 80,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 8
          },
          end: {
            line: 80,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "21": {
        loc: {
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 85,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 85,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "22": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "23": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "24": {
        loc: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "25": {
        loc: {
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "26": {
        loc: {
          start: {
            line: 101,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 101,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 101
      },
      "27": {
        loc: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 110,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 110,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "28": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "29": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 16
          }
        }, {
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 113,
            column: 29
          }
        }],
        line: 113
      },
      "30": {
        loc: {
          start: {
            line: 129,
            column: 26
          },
          end: {
            line: 129,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 129,
            column: 49
          },
          end: {
            line: 129,
            column: 51
          }
        }, {
          start: {
            line: 129,
            column: 54
          },
          end: {
            line: 129,
            column: 55
          }
        }],
        line: 129
      },
      "31": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "32": {
        loc: {
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "33": {
        loc: {
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 183,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 183,
            column: 35
          }
        }, {
          start: {
            line: 183,
            column: 39
          },
          end: {
            line: 183,
            column: 81
          }
        }],
        line: 183
      },
      "34": {
        loc: {
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "35": {
        loc: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "36": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "37": {
        loc: {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 232,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 232,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "38": {
        loc: {
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "39": {
        loc: {
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 246,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 246,
            column: 19
          },
          end: {
            line: 246,
            column: 20
          }
        }],
        line: 246
      },
      "40": {
        loc: {
          start: {
            line: 246,
            column: 22
          },
          end: {
            line: 246,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 246,
            column: 30
          },
          end: {
            line: 246,
            column: 32
          }
        }],
        line: 246
      },
      "41": {
        loc: {
          start: {
            line: 246,
            column: 42
          },
          end: {
            line: 246,
            column: 62
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 246,
            column: 51
          },
          end: {
            line: 246,
            column: 62
          }
        }],
        line: 246
      },
      "42": {
        loc: {
          start: {
            line: 246,
            column: 64
          },
          end: {
            line: 246,
            column: 82
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 246,
            column: 76
          },
          end: {
            line: 246,
            column: 82
          }
        }],
        line: 246
      },
      "43": {
        loc: {
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "44": {
        loc: {
          start: {
            line: 254,
            column: 26
          },
          end: {
            line: 254,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 254,
            column: 49
          },
          end: {
            line: 254,
            column: 51
          }
        }, {
          start: {
            line: 254,
            column: 54
          },
          end: {
            line: 254,
            column: 55
          }
        }],
        line: 254
      },
      "45": {
        loc: {
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 292,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 292,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "46": {
        loc: {
          start: {
            line: 294,
            column: 4
          },
          end: {
            line: 296,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 294,
            column: 4
          },
          end: {
            line: 296,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 294
      },
      "47": {
        loc: {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 300,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 300,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 298
      },
      "48": {
        loc: {
          start: {
            line: 298,
            column: 8
          },
          end: {
            line: 298,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 8
          },
          end: {
            line: 298,
            column: 24
          }
        }, {
          start: {
            line: 298,
            column: 28
          },
          end: {
            line: 298,
            column: 56
          }
        }],
        line: 298
      },
      "49": {
        loc: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 303,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 303,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 301
      },
      "50": {
        loc: {
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 301,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 301,
            column: 38
          }
        }, {
          start: {
            line: 301,
            column: 42
          },
          end: {
            line: 301,
            column: 76
          }
        }],
        line: 301
      },
      "51": {
        loc: {
          start: {
            line: 320,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 320,
            column: 4
          },
          end: {
            line: 322,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 320
      },
      "52": {
        loc: {
          start: {
            line: 324,
            column: 4
          },
          end: {
            line: 326,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 4
          },
          end: {
            line: 326,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "53": {
        loc: {
          start: {
            line: 343,
            column: 108
          },
          end: {
            line: 343,
            column: 116
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 343,
            column: 115
          },
          end: {
            line: 343,
            column: 116
          }
        }],
        line: 343
      },
      "54": {
        loc: {
          start: {
            line: 343,
            column: 118
          },
          end: {
            line: 343,
            column: 128
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 343,
            column: 126
          },
          end: {
            line: 343,
            column: 128
          }
        }],
        line: 343
      },
      "55": {
        loc: {
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 366,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 366,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      },
      "56": {
        loc: {
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 372,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 372,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "57": {
        loc: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 378,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 378,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      },
      "58": {
        loc: {
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 380
      },
      "59": {
        loc: {
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 380,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 380,
            column: 16
          }
        }, {
          start: {
            line: 380,
            column: 20
          },
          end: {
            line: 380,
            column: 28
          }
        }],
        line: 380
      },
      "60": {
        loc: {
          start: {
            line: 382,
            column: 8
          },
          end: {
            line: 383,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 382,
            column: 8
          },
          end: {
            line: 383,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 382
      },
      "61": {
        loc: {
          start: {
            line: 384,
            column: 8
          },
          end: {
            line: 385,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 8
          },
          end: {
            line: 385,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "62": {
        loc: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 397,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 397,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      },
      "63": {
        loc: {
          start: {
            line: 398,
            column: 4
          },
          end: {
            line: 402,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 398,
            column: 4
          },
          end: {
            line: 402,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 398
      },
      "64": {
        loc: {
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 415,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 415,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "65": {
        loc: {
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 407,
            column: 76
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 8
          },
          end: {
            line: 407,
            column: 76
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "66": {
        loc: {
          start: {
            line: 408,
            column: 8
          },
          end: {
            line: 409,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 408,
            column: 8
          },
          end: {
            line: 409,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 408
      },
      "67": {
        loc: {
          start: {
            line: 410,
            column: 8
          },
          end: {
            line: 411,
            column: 76
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 8
          },
          end: {
            line: 411,
            column: 76
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "68": {
        loc: {
          start: {
            line: 417,
            column: 4
          },
          end: {
            line: 425,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 4
          },
          end: {
            line: 425,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "69": {
        loc: {
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 417,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 417,
            column: 8
          },
          end: {
            line: 417,
            column: 17
          }
        }, {
          start: {
            line: 417,
            column: 21
          },
          end: {
            line: 417,
            column: 41
          }
        }],
        line: 417
      },
      "70": {
        loc: {
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 459,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 450,
            column: 4
          },
          end: {
            line: 459,
            column: 5
          }
        }, {
          start: {
            line: 455,
            column: 9
          },
          end: {
            line: 459,
            column: 5
          }
        }],
        line: 450
      },
      "71": {
        loc: {
          start: {
            line: 471,
            column: 18
          },
          end: {
            line: 471,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 471,
            column: 18
          },
          end: {
            line: 471,
            column: 38
          }
        }, {
          start: {
            line: 471,
            column: 42
          },
          end: {
            line: 471,
            column: 43
          }
        }],
        line: 471
      },
      "72": {
        loc: {
          start: {
            line: 493,
            column: 12
          },
          end: {
            line: 493,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 493,
            column: 20
          },
          end: {
            line: 493,
            column: 22
          }
        }],
        line: 493
      },
      "73": {
        loc: {
          start: {
            line: 496,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "74": {
        loc: {
          start: {
            line: 508,
            column: 4
          },
          end: {
            line: 529,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 508,
            column: 4
          },
          end: {
            line: 529,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 508
      },
      "75": {
        loc: {
          start: {
            line: 511,
            column: 8
          },
          end: {
            line: 513,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 511,
            column: 8
          },
          end: {
            line: 513,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 511
      },
      "76": {
        loc: {
          start: {
            line: 511,
            column: 12
          },
          end: {
            line: 511,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 511,
            column: 12
          },
          end: {
            line: 511,
            column: 31
          }
        }, {
          start: {
            line: 511,
            column: 35
          },
          end: {
            line: 511,
            column: 65
          }
        }],
        line: 511
      },
      "77": {
        loc: {
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 520,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 520,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 515
      },
      "78": {
        loc: {
          start: {
            line: 522,
            column: 8
          },
          end: {
            line: 524,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 522,
            column: 8
          },
          end: {
            line: 524,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 522
      },
      "79": {
        loc: {
          start: {
            line: 522,
            column: 12
          },
          end: {
            line: 522,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 522,
            column: 12
          },
          end: {
            line: 522,
            column: 32
          }
        }, {
          start: {
            line: 522,
            column: 36
          },
          end: {
            line: 522,
            column: 67
          }
        }],
        line: 522
      },
      "80": {
        loc: {
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 528,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 8
          },
          end: {
            line: 528,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      },
      "81": {
        loc: {
          start: {
            line: 546,
            column: 33
          },
          end: {
            line: 546,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 546,
            column: 42
          },
          end: {
            line: 546,
            column: 46
          }
        }],
        line: 546
      },
      "82": {
        loc: {
          start: {
            line: 546,
            column: 48
          },
          end: {
            line: 546,
            column: 58
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 546,
            column: 56
          },
          end: {
            line: 546,
            column: 58
          }
        }],
        line: 546
      },
      "83": {
        loc: {
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 549,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 549,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 547
      },
      "84": {
        loc: {
          start: {
            line: 547,
            column: 8
          },
          end: {
            line: 547,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 547,
            column: 8
          },
          end: {
            line: 547,
            column: 17
          }
        }, {
          start: {
            line: 547,
            column: 21
          },
          end: {
            line: 547,
            column: 31
          }
        }],
        line: 547
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0],
      "10": [0],
      "11": [0],
      "12": [0],
      "13": [0],
      "14": [0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0],
      "40": [0],
      "41": [0],
      "42": [0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0],
      "54": [0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0],
      "82": [0],
      "83": [0, 0],
      "84": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\property.controller.ts",
      mappings: ";;;;;;AACA,iDAA8C;AAC9C,sEAAwC;AACxC,4CAAyC;AACzC,sDAAmD;AACnD,gDAA6C;AAC7C,oDAAiD;AACjD,uCAAiC;AAEjC;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED,iDAAiD;IACjD,MAAM,IAAI,GAAG,MAAM,oBAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,mBAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,sDAAsD;IACtD,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAQ,CAAC,8DAA8D,EAAE,GAAG,CAAC,CAAC;IAC1F,CAAC;IAED,MAAM,YAAY,GAAG;QACnB,GAAG,GAAG,CAAC,IAAI;QACX,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;QACnC,cAAc,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC1C,MAAM,EAAE,OAAO,CAAC,gCAAgC;KACjD,CAAC;IAEF,2BAA2B;IAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC/E,MAAM,IAAI,mBAAQ,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC;IAC3E,CAAC;IAED,qEAAqE;IACrE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACvC,YAAY,CAAC,QAAQ,CAAC,WAAW,GAAG;YAClC,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,oBAAoB;SACnD,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEtB,eAAM,CAAC,IAAI,CAAC,qBAAqB,QAAQ,CAAC,GAAG,aAAa,MAAM,EAAE,CAAC,CAAC;IAEpE,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,QAAQ,EAAE,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,sCAAsC,CAAC;KACrF,EAAE,+BAA+B,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,aAAa,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC5E,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,GAAG,QAAQ,EACjB,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,EAClB,MAAM,EACN,QAAQ,EACR,SAAS,EACT,MAAM,GAAG,IAAI,CAAC,qBAAqB;MACpC,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,sBAAsB;IACtB,MAAM,MAAM,GAAQ;QAClB,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,IAAI;KAClB,CAAC;IAEF,uBAAuB;IACvB,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAED,sBAAsB;IACtB,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,CAAC;IAED,qBAAqB;IACrB,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;QACzB,MAAM,CAAC,sBAAsB,CAAC,GAAG,EAAE,CAAC;QACpC,IAAI,QAAQ;YAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,QAAQ;YAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED,iBAAiB;IACjB,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED,kBAAkB;IAClB,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,mBAAmB;IACnB,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI,MAAM,CAAC,IAAc,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IACD,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,CAAC,gBAAgB,CAAC,GAAG,IAAI,MAAM,CAAC,KAAe,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI,MAAM,CAAC,IAAc,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,mBAAmB;IACnB,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,WAAW,GAAI,SAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrD,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5B,MAAM,CAAC,aAAa,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,cAAc;IACd,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,KAAK,GAAG,EAAE,OAAO,EAAE,MAAgB,EAAE,CAAC;IAC/C,CAAC;IAED,oBAAoB;IACpB,IAAI,KAAK,GAAG,mBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAElC,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;QAC1B,KAAK,GAAG,mBAAQ,CAAC,IAAI,CAAC;YACpB,GAAG,MAAM;YACT,sBAAsB,EAAE;gBACtB,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;qBACnD;oBACD,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC;iBAC7B;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACV,MAAM,WAAW,GAAQ,EAAE,CAAC;IAC5B,WAAW,CAAC,MAAgB,CAAC,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,gCAAgC;IAChC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAEhD,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC5C,KAAK;aACF,IAAI,CAAC,WAAW,CAAC;aACjB,IAAI,CAAC,IAAI,CAAC;aACV,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACpB,QAAQ,CAAC,SAAS,EAAE,sDAAsD,CAAC;aAC3E,IAAI,EAAE;QACT,mBAAQ,CAAC,cAAc,CAAC,MAAM,CAAC;KAChC,CAAC,CAAC;IAEH,4BAA4B;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACpD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;IAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAErC,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,UAAU;QACV,UAAU,EAAE;YACV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;YACpB,KAAK;YACL,KAAK,EAAE,UAAU;YACjB,WAAW;YACX,WAAW;SACZ;QACD,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,iCAAiC;YAC1E,SAAS,EAAE;gBACT,aAAa,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;gBACzF,YAAY,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;gBAC5C,SAAS,EAAE;oBACT,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB;oBACzE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI;oBACrE,gBAAgB,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,YAAY;oBACjE,cAAc,EAAE,SAAS,EAAE,iBAAiB;iBAC7C;aACF;SACF;KACF,EAAE,mCAAmC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC1E,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;SACzC,QAAQ,CAAC,SAAS,EAAE,kEAAkE,CAAC;SACvF,QAAQ,CAAC,YAAY,EAAE,oBAAoB,CAAC,CAAC;IAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,kCAAkC;IAClC,IAAI,QAAQ,CAAC,MAAM,KAAK,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC9E,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,+CAA+C;IAC/C,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC/C,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,QAAQ;KACT,EAAE,iCAAiC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC3C,MAAM,IAAI,mBAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED,kBAAkB;IAClB,MAAM,UAAU,GAAG;QACjB,GAAG,GAAG,CAAC,IAAI;QACX,cAAc,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;KAC3C,CAAC;IAEF,mDAAmD;IACnD,OAAO,UAAU,CAAC,OAAO,CAAC;IAC1B,OAAO,UAAU,CAAC,SAAS,CAAC;IAC5B,OAAO,UAAU,CAAC,SAAS,CAAC;IAE5B,MAAM,eAAe,GAAG,MAAM,mBAAQ,CAAC,iBAAiB,CACtD,EAAE,EACF,UAAU,EACV,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CACnC,CAAC,QAAQ,CAAC,SAAS,EAAE,sCAAsC,CAAC,CAAC;IAE9D,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,aAAa,MAAM,EAAE,CAAC,CAAC;IAE1D,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,QAAQ,EAAE,eAAe;KAC1B,EAAE,+BAA+B,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC3C,MAAM,IAAI,mBAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,mBAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAErC,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,aAAa,MAAM,EAAE,CAAC,CAAC;IAE1D,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,+BAA+B,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACjF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,EACN,MAAM,GAAG,WAAW,EACpB,SAAS,GAAG,MAAM,EACnB,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,eAAe;IACf,MAAM,MAAM,GAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACxC,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,UAAU;IACV,MAAM,WAAW,GAAQ,EAAE,CAAC;IAC5B,WAAW,CAAC,MAAgB,CAAC,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,gCAAgC;IAChC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAEhD,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC5C,mBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;aAClB,IAAI,CAAC,WAAW,CAAC;aACjB,IAAI,CAAC,IAAI,CAAC;aACV,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACpB,IAAI,EAAE;QACT,mBAAQ,CAAC,cAAc,CAAC,MAAM,CAAC;KAChC,CAAC,CAAC;IAEH,4BAA4B;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAEpD,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,UAAU;QACV,UAAU,EAAE;YACV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;YACpB,KAAK;YACL,KAAK,EAAE,UAAU;SAClB;QACD,OAAO,EAAE;YACP,KAAK;YACL,MAAM,EAAE,MAAM,mBAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YAC5E,KAAK,EAAE,MAAM,mBAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;YAC1E,MAAM,EAAE,MAAM,mBAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;SAC7E;KACF,EAAE,yCAAyC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,eAAe,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC9E,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC3C,MAAM,IAAI,mBAAQ,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAED,8CAA8C;IAC9C,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,mBAAQ,CAAC,yDAAyD,EAAE,GAAG,CAAC,CAAC;IACrF,CAAC;IAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;QACzE,MAAM,IAAI,mBAAQ,CAAC,0DAA0D,EAAE,GAAG,CAAC,CAAC;IACtF,CAAC;IAED,0BAA0B;IAC1B,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,QAAQ,CAAC,cAAc,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrD,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEtB,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,MAAM,EAAE,CAAC,CAAC;IAE5D,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,QAAQ;KACT,EAAE,iCAAiC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnF,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,mBAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,mBAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;QAC3C,MAAM,IAAI,mBAAQ,CAAC,qDAAqD,EAAE,GAAG,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,SAAS,EAAE,QAAQ,CAAC,SAAS;QAC7B,OAAO,EAAE;YACP,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK;YACpC,cAAc,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS;YAC5C,cAAc,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS;YAC5C,iBAAiB,EAAE,QAAQ,CAAC,SAAS,CAAC,YAAY;YAClD,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,YAAY;YAC7C,mBAAmB,EAAE,QAAQ,CAAC,SAAS,CAAC,mBAAmB;SAC5D;KACF,EAAE,2CAA2C,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC/E,MAAM,EACJ,KAAK,EACL,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACX,GAAG,GAAG,CAAC,IAAI,CAAC;IAEb,6BAA6B;IAC7B,MAAM,QAAQ,GAAU;QACtB,sBAAsB;QACtB;YACE,MAAM,EAAE;gBACN,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI;aAClB;SACF;KACF,CAAC;IAEF,cAAc;IACd,IAAI,KAAK,EAAE,CAAC;QACV,QAAQ,CAAC,OAAO,CAAC;YACf,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;aAC1B;SACF,CAAC,CAAC;QACH,QAAQ,CAAC,IAAI,CAAC;YACZ,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;aAC9B;SACF,CAAC,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,IAAI,YAAY,EAAE,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,EAAE,YAAY,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;IAED,sBAAsB;IACtB,IAAI,WAAW,EAAE,CAAC;QAChB,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,EAAE,WAAW,EAAE;SACxB,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB;IACrB,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;QACzB,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,IAAI,QAAQ;YAAE,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,QAAQ;YAAE,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEjD,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE;gBACN,sBAAsB,EAAE,UAAU;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;SACvC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,SAAS,EAAE,CAAC;QACd,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,aAAa,GAAQ,EAAE,CAAC;QAC9B,IAAI,QAAQ,CAAC,IAAI;YAAE,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACnF,IAAI,QAAQ,CAAC,KAAK;YAAE,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACtF,IAAI,QAAQ,CAAC,IAAI;YAAE,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAEnF,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB;IACnB,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,MAAM,YAAY,GAAQ,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,OAAe,EAAE,EAAE;YACpC,YAAY,CAAC,aAAa,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,IAAI,CAAC;YACZ,MAAM,EAAE,YAAY;SACrB,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,QAAQ,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE;YACP,IAAI,EAAE,OAAO;YACb,UAAU,EAAE,SAAS;YACrB,YAAY,EAAE,KAAK;YACnB,EAAE,EAAE,OAAO;YACX,QAAQ,EAAE;gBACR;oBACE,QAAQ,EAAE;wBACR,SAAS,EAAE,CAAC;wBACZ,QAAQ,EAAE,CAAC;wBACX,KAAK,EAAE,CAAC;wBACR,WAAW,EAAE,CAAC;wBACd,eAAe,EAAE,CAAC;qBACnB;iBACF;aACF;SACF;KACF,CAAC,CAAC;IAEH,QAAQ,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE,QAAQ;KAClB,CAAC,CAAC;IAEH,6CAA6C;IAC7C,IAAI,KAAK,EAAE,CAAC;QACV,QAAQ,CAAC,IAAI,CAAC;YACZ,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE;SACxD,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,QAAQ,CAAC,IAAI,CAAC;YACZ,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;IAED,aAAa;IACb,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAChD,QAAQ,CAAC,IAAI,CACX,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAC1B,CAAC;IAEF,sBAAsB;IACtB,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACjD,mBAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;QAC5B,mBAAQ,CAAC,SAAS,CAAC;YACjB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,kCAAkC;YAC5D,EAAE,MAAM,EAAE,OAAO,EAAE;SACpB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC;IACxC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAEpD,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,UAAU;QACV,UAAU,EAAE;YACV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;YACpB,KAAK;YACL,KAAK,EAAE,UAAU;SAClB;QACD,UAAU,EAAE;YACV,KAAK;YACL,YAAY,EAAE,UAAU,CAAC,MAAM;YAC/B,YAAY,EAAE,KAAK;SACpB;KACF,EAAE,wCAAwC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACrF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IAEjC,6CAA6C;IAC7C,MAAM,IAAI,GAAG,MAAM,oBAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAE7D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,mBAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,kDAAkD;IAClD,MAAM,kBAAkB,GAAQ;QAC9B,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,IAAI;KAClB,CAAC;IAEF,4CAA4C;IAC5C,2CAA2C;IAC3C,MAAM,OAAO,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAC;IAC3D,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACtD,IAAI,WAAW,EAAE,kBAAkB,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,WAAW,CAAC,kBAAkB,CAAC;QAE7C,iBAAiB;QACjB,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,kBAAkB,CAAC,YAAY,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC;QACjE,CAAC;QAED,eAAe;QACf,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,kBAAkB,CAAC,sBAAsB,CAAC,GAAG;gBAC3C,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG;gBAC3B,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG;aAC5B,CAAC;QACJ,CAAC;QAED,kBAAkB;QAClB,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,kBAAkB,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,cAAc,EAAE,CAAC;QACtE,CAAC;QAED,YAAY;QACZ,IAAI,KAAK,CAAC,QAAQ,KAAK,cAAc,EAAE,CAAC;YACtC,kBAAkB,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,MAAM,WAAW,GAAG,MAAM,mBAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;SACxD,IAAI,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;SAC9C,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACpB,QAAQ,CAAC,SAAS,EAAE,sCAAsC,CAAC;SAC3D,IAAI,EAAE,CAAC;IAEV,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,WAAW;QACX,QAAQ,EAAE,kBAAkB;QAC5B,KAAK,EAAE,WAAW,CAAC,MAAM;KAC1B,EAAE,6CAA6C,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAClF,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IAErE,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,IAAI,mBAAQ,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC;IACjE,CAAC;IAED,MAAM,gBAAgB,GAAG,MAAM,mBAAQ,CAAC,UAAU,CAChD,MAAM,CAAC,SAAS,CAAC,EACjB,MAAM,CAAC,QAAQ,CAAC,EAChB,MAAM,CAAC,MAAM,CAAC,CACf;SACE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACpB,QAAQ,CAAC,SAAS,EAAE,sCAAsC,CAAC;SAC3D,IAAI,EAAE,CAAC;IAEV,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,UAAU,EAAE,gBAAgB;QAC5B,YAAY,EAAE;YACZ,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;YAC1B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;SAC7B;QACD,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;QACtB,KAAK,EAAE,gBAAgB,CAAC,MAAM;KAC/B,EAAE,0CAA0C,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,IAAa,EAAE,GAAa,EAAE,EAAE;IAC3E,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,cAAc,EAAE,CAAC;IACtD,MAAM,gBAAgB,GAAG,MAAM,mBAAQ,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAE7E,OAAO,yBAAW,CAAC,OAAO,CAAC,GAAG,EAAE;QAC9B,OAAO,EAAE,yBAAyB;QAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,UAAU,EAAE;YACV,eAAe,EAAE,aAAa;YAC9B,gBAAgB;YAChB,mBAAmB,EAAE,MAAM,mBAAQ,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;SAC5F;QACD,SAAS,EAAE;YACT,cAAc,EAAE,QAAQ;YACxB,aAAa,EAAE,OAAO;YACtB,WAAW,EAAE,UAAU;YACvB,cAAc,EAAE,YAAY;YAC5B,cAAc,EAAE,aAAa;YAC7B,kBAAkB,EAAE,YAAY;YAChC,eAAe,EAAE,oBAAoB;YACrC,oBAAoB,EAAE,oBAAoB;YAC1C,gBAAgB,EAAE,cAAc;YAChC,sBAAsB,EAAE,kBAAkB;YAC1C,mBAAmB,EAAE,aAAa;SACnC;KACF,EAAE,6BAA6B,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\property.controller.ts"],
      sourcesContent: ["import { Request, Response } from 'express';\r\nimport { Property } from '../models/Property';\r\nimport User from '../models/User.model';\r\nimport { logger } from '../utils/logger';\r\nimport { ApiResponse } from '../utils/apiResponse';\r\nimport { AppError } from '../utils/appError';\r\nimport { catchAsync } from '../utils/catchAsync';\r\nimport { Types } from 'mongoose';\r\n\r\n/**\r\n * Create a new property listing\r\n */\r\nexport const createProperty = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  \r\n  if (!userId) {\r\n    throw new AppError('User authentication required', 401);\r\n  }\r\n\r\n  // Validate user exists and can create properties\r\n  const user = await User.findById(userId);\r\n  if (!user) {\r\n    throw new AppError('User not found', 404);\r\n  }\r\n\r\n  // Check if user account type allows property creation\r\n  if (user.accountType === 'seeker') {\r\n    throw new AppError('Only property owners and agents can create property listings', 403);\r\n  }\r\n\r\n  const propertyData = {\r\n    ...req.body,\r\n    ownerId: new Types.ObjectId(userId),\r\n    lastModifiedBy: new Types.ObjectId(userId),\r\n    status: 'draft' // New properties start as draft\r\n  };\r\n\r\n  // Validate required fields\r\n  if (!propertyData.title || !propertyData.description || !propertyData.location) {\r\n    throw new AppError('Title, description, and location are required', 400);\r\n  }\r\n\r\n  // Set default coordinates if not provided (Lagos center as fallback)\r\n  if (!propertyData.location.coordinates) {\r\n    propertyData.location.coordinates = {\r\n      type: 'Point',\r\n      coordinates: [3.3792, 6.5244] // Lagos coordinates\r\n    };\r\n  }\r\n\r\n  const property = new Property(propertyData);\r\n  await property.save();\r\n\r\n  logger.info(`Property created: ${property._id} by user: ${userId}`);\r\n\r\n  return ApiResponse.success(res, {\r\n    property: await property.populate('ownerId', 'firstName lastName email accountType')\r\n  }, 'Property created successfully', 201);\r\n});\r\n\r\n/**\r\n * Get all properties with filtering and pagination\r\n */\r\nexport const getProperties = catchAsync(async (req: Request, res: Response) => {\r\n  const {\r\n    page = 1,\r\n    limit = 20,\r\n    propertyType,\r\n    listingType,\r\n    minPrice,\r\n    maxPrice,\r\n    bedrooms,\r\n    bathrooms,\r\n    city,\r\n    state,\r\n    area,\r\n    amenities,\r\n    status = 'active',\r\n    sortBy = 'createdAt',\r\n    sortOrder = 'desc',\r\n    search,\r\n    latitude,\r\n    longitude,\r\n    radius = 5000 // 5km default radius\r\n  } = req.query;\r\n\r\n  // Build filter object\r\n  const filter: any = {\r\n    status: status,\r\n    isAvailable: true\r\n  };\r\n\r\n  // Property type filter\r\n  if (propertyType) {\r\n    filter.propertyType = propertyType;\r\n  }\r\n\r\n  // Listing type filter\r\n  if (listingType) {\r\n    filter.listingType = listingType;\r\n  }\r\n\r\n  // Price range filter\r\n  if (minPrice || maxPrice) {\r\n    filter['pricing.rentPerMonth'] = {};\r\n    if (minPrice) filter['pricing.rentPerMonth'].$gte = Number(minPrice);\r\n    if (maxPrice) filter['pricing.rentPerMonth'].$lte = Number(maxPrice);\r\n  }\r\n\r\n  // Bedroom filter\r\n  if (bedrooms) {\r\n    filter.bedrooms = Number(bedrooms);\r\n  }\r\n\r\n  // Bathroom filter\r\n  if (bathrooms) {\r\n    filter.bathrooms = Number(bathrooms);\r\n  }\r\n\r\n  // Location filters\r\n  if (city) {\r\n    filter['location.city'] = new RegExp(city as string, 'i');\r\n  }\r\n  if (state) {\r\n    filter['location.state'] = new RegExp(state as string, 'i');\r\n  }\r\n  if (area) {\r\n    filter['location.area'] = new RegExp(area as string, 'i');\r\n  }\r\n\r\n  // Amenities filter\r\n  if (amenities) {\r\n    const amenityList = (amenities as string).split(',');\r\n    amenityList.forEach(amenity => {\r\n      filter[`amenities.${amenity.trim()}`] = true;\r\n    });\r\n  }\r\n\r\n  // Text search\r\n  if (search) {\r\n    filter.$text = { $search: search as string };\r\n  }\r\n\r\n  // Geospatial search\r\n  let query = Property.find(filter);\r\n  \r\n  if (latitude && longitude) {\r\n    query = Property.find({\r\n      ...filter,\r\n      'location.coordinates': {\r\n        $near: {\r\n          $geometry: {\r\n            type: 'Point',\r\n            coordinates: [Number(longitude), Number(latitude)]\r\n          },\r\n          $maxDistance: Number(radius)\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Sorting\r\n  const sortOptions: any = {};\r\n  sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;\r\n\r\n  // Execute query with pagination\r\n  const skip = (Number(page) - 1) * Number(limit);\r\n  \r\n  const [properties, total] = await Promise.all([\r\n    query\r\n      .sort(sortOptions)\r\n      .skip(skip)\r\n      .limit(Number(limit))\r\n      .populate('ownerId', 'firstName lastName email accountType isEmailVerified')\r\n      .lean(),\r\n    Property.countDocuments(filter)\r\n  ]);\r\n\r\n  // Calculate pagination info\r\n  const totalPages = Math.ceil(total / Number(limit));\r\n  const hasNextPage = Number(page) < totalPages;\r\n  const hasPrevPage = Number(page) > 1;\r\n\r\n  return ApiResponse.success(res, {\r\n    properties,\r\n    pagination: {\r\n      page: Number(page),\r\n      limit: Number(limit),\r\n      total,\r\n      pages: totalPages,\r\n      hasNextPage,\r\n      hasPrevPage\r\n    },\r\n    filters: {\r\n      applied: Object.keys(filter).length - 2, // Exclude status and isAvailable\r\n      available: {\r\n        propertyTypes: ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion'],\r\n        listingTypes: ['rent', 'roommate', 'sublet'],\r\n        amenities: [\r\n          'wifi', 'parking', 'security', 'generator', 'borehole', 'airConditioning',\r\n          'kitchen', 'refrigerator', 'microwave', 'gasStove', 'furnished', 'tv',\r\n          'washingMachine', 'elevator', 'gym', 'swimmingPool', 'playground',\r\n          'prepaidMeter', 'cableTV', 'cleaningService'\r\n        ]\r\n      }\r\n    }\r\n  }, 'Properties retrieved successfully');\r\n});\r\n\r\n/**\r\n * Get a single property by ID\r\n */\r\nexport const getProperty = catchAsync(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const property = await Property.findById(id)\r\n    .populate('ownerId', 'firstName lastName email accountType isEmailVerified phoneNumber')\r\n    .populate('verifiedBy', 'firstName lastName');\r\n\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check if property is accessible\r\n  if (property.status === 'draft' && property.ownerId._id.toString() !== userId) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Increment view count (but not for the owner)\r\n  if (property.ownerId._id.toString() !== userId) {\r\n    await property.incrementViews();\r\n  }\r\n\r\n  return ApiResponse.success(res, {\r\n    property\r\n  }, 'Property retrieved successfully');\r\n});\r\n\r\n/**\r\n * Update a property\r\n */\r\nexport const updateProperty = catchAsync(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const property = await Property.findById(id);\r\n\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check ownership\r\n  if (property.ownerId.toString() !== userId) {\r\n    throw new AppError('You can only update your own properties', 403);\r\n  }\r\n\r\n  // Update property\r\n  const updateData = {\r\n    ...req.body,\r\n    lastModifiedBy: new Types.ObjectId(userId)\r\n  };\r\n\r\n  // Remove fields that shouldn't be updated directly\r\n  delete updateData.ownerId;\r\n  delete updateData.analytics;\r\n  delete updateData.createdAt;\r\n\r\n  const updatedProperty = await Property.findByIdAndUpdate(\r\n    id,\r\n    updateData,\r\n    { new: true, runValidators: true }\r\n  ).populate('ownerId', 'firstName lastName email accountType');\r\n\r\n  logger.info(`Property updated: ${id} by user: ${userId}`);\r\n\r\n  return ApiResponse.success(res, {\r\n    property: updatedProperty\r\n  }, 'Property updated successfully');\r\n});\r\n\r\n/**\r\n * Delete a property\r\n */\r\nexport const deleteProperty = catchAsync(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const property = await Property.findById(id);\r\n\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check ownership\r\n  if (property.ownerId.toString() !== userId) {\r\n    throw new AppError('You can only delete your own properties', 403);\r\n  }\r\n\r\n  await Property.findByIdAndDelete(id);\r\n\r\n  logger.info(`Property deleted: ${id} by user: ${userId}`);\r\n\r\n  return ApiResponse.success(res, null, 'Property deleted successfully');\r\n});\r\n\r\n/**\r\n * Get properties by owner\r\n */\r\nexport const getOwnerProperties = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const {\r\n    page = 1,\r\n    limit = 20,\r\n    status,\r\n    sortBy = 'createdAt',\r\n    sortOrder = 'desc'\r\n  } = req.query;\r\n\r\n  // Build filter\r\n  const filter: any = { ownerId: userId };\r\n  if (status) {\r\n    filter.status = status;\r\n  }\r\n\r\n  // Sorting\r\n  const sortOptions: any = {};\r\n  sortOptions[sortBy as string] = sortOrder === 'desc' ? -1 : 1;\r\n\r\n  // Execute query with pagination\r\n  const skip = (Number(page) - 1) * Number(limit);\r\n  \r\n  const [properties, total] = await Promise.all([\r\n    Property.find(filter)\r\n      .sort(sortOptions)\r\n      .skip(skip)\r\n      .limit(Number(limit))\r\n      .lean(),\r\n    Property.countDocuments(filter)\r\n  ]);\r\n\r\n  // Calculate pagination info\r\n  const totalPages = Math.ceil(total / Number(limit));\r\n\r\n  return ApiResponse.success(res, {\r\n    properties,\r\n    pagination: {\r\n      page: Number(page),\r\n      limit: Number(limit),\r\n      total,\r\n      pages: totalPages\r\n    },\r\n    summary: {\r\n      total,\r\n      active: await Property.countDocuments({ ownerId: userId, status: 'active' }),\r\n      draft: await Property.countDocuments({ ownerId: userId, status: 'draft' }),\r\n      rented: await Property.countDocuments({ ownerId: userId, status: 'rented' })\r\n    }\r\n  }, 'Owner properties retrieved successfully');\r\n});\r\n\r\n/**\r\n * Publish a draft property\r\n */\r\nexport const publishProperty = catchAsync(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const property = await Property.findById(id);\r\n\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check ownership\r\n  if (property.ownerId.toString() !== userId) {\r\n    throw new AppError('You can only publish your own properties', 403);\r\n  }\r\n\r\n  // Check if property has minimum required data\r\n  if (!property.photos || property.photos.length === 0) {\r\n    throw new AppError('Property must have at least one photo before publishing', 400);\r\n  }\r\n\r\n  if (!property.pricing.rentPerMonth || property.pricing.rentPerMonth <= 0) {\r\n    throw new AppError('Property must have a valid rent amount before publishing', 400);\r\n  }\r\n\r\n  // Update status to active\r\n  property.status = 'active';\r\n  property.lastModifiedBy = new Types.ObjectId(userId);\r\n  await property.save();\r\n\r\n  logger.info(`Property published: ${id} by user: ${userId}`);\r\n\r\n  return ApiResponse.success(res, {\r\n    property\r\n  }, 'Property published successfully');\r\n});\r\n\r\n/**\r\n * Get property analytics\r\n */\r\nexport const getPropertyAnalytics = catchAsync(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const userId = req.user?._id;\r\n\r\n  const property = await Property.findById(id);\r\n\r\n  if (!property) {\r\n    throw new AppError('Property not found', 404);\r\n  }\r\n\r\n  // Check ownership\r\n  if (property.ownerId.toString() !== userId) {\r\n    throw new AppError('You can only view analytics for your own properties', 403);\r\n  }\r\n\r\n  return ApiResponse.success(res, {\r\n    analytics: property.analytics,\r\n    summary: {\r\n      totalViews: property.analytics.views,\r\n      totalFavorites: property.analytics.favorites,\r\n      totalInquiries: property.analytics.inquiries,\r\n      totalApplications: property.analytics.applications,\r\n      lastViewedAt: property.analytics.lastViewedAt,\r\n      averageViewDuration: property.analytics.averageViewDuration\r\n    }\r\n  }, 'Property analytics retrieved successfully');\r\n});\r\n\r\n/**\r\n * Search properties with advanced filters\r\n */\r\nexport const searchProperties = catchAsync(async (req: Request, res: Response) => {\r\n  const {\r\n    query,\r\n    propertyType,\r\n    listingType,\r\n    minPrice,\r\n    maxPrice,\r\n    bedrooms,\r\n    bathrooms,\r\n    location,\r\n    amenities,\r\n    page = 1,\r\n    limit = 20\r\n  } = req.body;\r\n\r\n  // Build aggregation pipeline\r\n  const pipeline: any[] = [\r\n    // Match basic filters\r\n    {\r\n      $match: {\r\n        status: 'active',\r\n        isAvailable: true\r\n      }\r\n    }\r\n  ];\r\n\r\n  // Text search\r\n  if (query) {\r\n    pipeline.unshift({\r\n      $match: {\r\n        $text: { $search: query }\r\n      }\r\n    });\r\n    pipeline.push({\r\n      $addFields: {\r\n        score: { $meta: 'textScore' }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Property type filter\r\n  if (propertyType) {\r\n    pipeline.push({\r\n      $match: { propertyType }\r\n    });\r\n  }\r\n\r\n  // Listing type filter\r\n  if (listingType) {\r\n    pipeline.push({\r\n      $match: { listingType }\r\n    });\r\n  }\r\n\r\n  // Price range filter\r\n  if (minPrice || maxPrice) {\r\n    const priceMatch: any = {};\r\n    if (minPrice) priceMatch.$gte = Number(minPrice);\r\n    if (maxPrice) priceMatch.$lte = Number(maxPrice);\r\n\r\n    pipeline.push({\r\n      $match: {\r\n        'pricing.rentPerMonth': priceMatch\r\n      }\r\n    });\r\n  }\r\n\r\n  // Bedroom/bathroom filters\r\n  if (bedrooms) {\r\n    pipeline.push({\r\n      $match: { bedrooms: Number(bedrooms) }\r\n    });\r\n  }\r\n\r\n  if (bathrooms) {\r\n    pipeline.push({\r\n      $match: { bathrooms: Number(bathrooms) }\r\n    });\r\n  }\r\n\r\n  // Location filter\r\n  if (location) {\r\n    const locationMatch: any = {};\r\n    if (location.city) locationMatch['location.city'] = new RegExp(location.city, 'i');\r\n    if (location.state) locationMatch['location.state'] = new RegExp(location.state, 'i');\r\n    if (location.area) locationMatch['location.area'] = new RegExp(location.area, 'i');\r\n\r\n    pipeline.push({\r\n      $match: locationMatch\r\n    });\r\n  }\r\n\r\n  // Amenities filter\r\n  if (amenities && amenities.length > 0) {\r\n    const amenityMatch: any = {};\r\n    amenities.forEach((amenity: string) => {\r\n      amenityMatch[`amenities.${amenity}`] = true;\r\n    });\r\n\r\n    pipeline.push({\r\n      $match: amenityMatch\r\n    });\r\n  }\r\n\r\n  // Add owner information\r\n  pipeline.push({\r\n    $lookup: {\r\n      from: 'users',\r\n      localField: 'ownerId',\r\n      foreignField: '_id',\r\n      as: 'owner',\r\n      pipeline: [\r\n        {\r\n          $project: {\r\n            firstName: 1,\r\n            lastName: 1,\r\n            email: 1,\r\n            accountType: 1,\r\n            isEmailVerified: 1\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  });\r\n\r\n  pipeline.push({\r\n    $unwind: '$owner'\r\n  });\r\n\r\n  // Sort by relevance (if text search) or date\r\n  if (query) {\r\n    pipeline.push({\r\n      $sort: { score: { $meta: 'textScore' }, createdAt: -1 }\r\n    });\r\n  } else {\r\n    pipeline.push({\r\n      $sort: { createdAt: -1 }\r\n    });\r\n  }\r\n\r\n  // Pagination\r\n  const skip = (Number(page) - 1) * Number(limit);\r\n  pipeline.push(\r\n    { $skip: skip },\r\n    { $limit: Number(limit) }\r\n  );\r\n\r\n  // Execute aggregation\r\n  const [properties, totalCount] = await Promise.all([\r\n    Property.aggregate(pipeline),\r\n    Property.aggregate([\r\n      ...pipeline.slice(0, -2), // Remove skip and limit for count\r\n      { $count: 'total' }\r\n    ])\r\n  ]);\r\n\r\n  const total = totalCount[0]?.total || 0;\r\n  const totalPages = Math.ceil(total / Number(limit));\r\n\r\n  return ApiResponse.success(res, {\r\n    properties,\r\n    pagination: {\r\n      page: Number(page),\r\n      limit: Number(limit),\r\n      total,\r\n      pages: totalPages\r\n    },\r\n    searchInfo: {\r\n      query,\r\n      resultsFound: properties.length,\r\n      totalMatches: total\r\n    }\r\n  }, 'Property search completed successfully');\r\n});\r\n\r\n/**\r\n * Get property suggestions based on user preferences\r\n */\r\nexport const getPropertySuggestions = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { limit = 10 } = req.query;\r\n\r\n  // Get user profile to understand preferences\r\n  const user = await User.findById(userId).populate('profile');\r\n\r\n  if (!user) {\r\n    throw new AppError('User not found', 404);\r\n  }\r\n\r\n  // Build suggestion criteria based on user profile\r\n  const suggestionCriteria: any = {\r\n    status: 'active',\r\n    isAvailable: true\r\n  };\r\n\r\n  // If user has housing preferences, use them\r\n  // Get user profile for housing preferences\r\n  const Profile = require('../models/Profile.model').default;\r\n  const userProfile = await Profile.findOne({ userId });\r\n  if (userProfile?.housingPreferences) {\r\n    const prefs = userProfile.housingPreferences;\r\n\r\n    // Property types\r\n    if (prefs.propertyTypes && prefs.propertyTypes.length > 0) {\r\n      suggestionCriteria.propertyType = { $in: prefs.propertyTypes };\r\n    }\r\n\r\n    // Budget range\r\n    if (prefs.budgetRange) {\r\n      suggestionCriteria['pricing.rentPerMonth'] = {\r\n        $gte: prefs.budgetRange.min,\r\n        $lte: prefs.budgetRange.max\r\n      };\r\n    }\r\n\r\n    // Preferred areas\r\n    if (prefs.preferredAreas && prefs.preferredAreas.length > 0) {\r\n      suggestionCriteria['location.area'] = { $in: prefs.preferredAreas };\r\n    }\r\n\r\n    // Room type\r\n    if (prefs.roomType === 'private-room') {\r\n      suggestionCriteria.listingType = 'roommate';\r\n    }\r\n  }\r\n\r\n  // Get suggested properties\r\n  const suggestions = await Property.find(suggestionCriteria)\r\n    .sort({ 'analytics.views': -1, createdAt: -1 })\r\n    .limit(Number(limit))\r\n    .populate('ownerId', 'firstName lastName email accountType')\r\n    .lean();\r\n\r\n  return ApiResponse.success(res, {\r\n    suggestions,\r\n    criteria: suggestionCriteria,\r\n    count: suggestions.length\r\n  }, 'Property suggestions retrieved successfully');\r\n});\r\n\r\n/**\r\n * Get nearby properties\r\n */\r\nexport const getNearbyProperties = catchAsync(async (req: Request, res: Response) => {\r\n  const { latitude, longitude, radius = 5000, limit = 20 } = req.query;\r\n\r\n  if (!latitude || !longitude) {\r\n    throw new AppError('Latitude and longitude are required', 400);\r\n  }\r\n\r\n  const nearbyProperties = await Property.findNearby(\r\n    Number(longitude),\r\n    Number(latitude),\r\n    Number(radius)\r\n  )\r\n    .limit(Number(limit))\r\n    .populate('ownerId', 'firstName lastName email accountType')\r\n    .lean();\r\n\r\n  return ApiResponse.success(res, {\r\n    properties: nearbyProperties,\r\n    searchCenter: {\r\n      latitude: Number(latitude),\r\n      longitude: Number(longitude)\r\n    },\r\n    radius: Number(radius),\r\n    count: nearbyProperties.length\r\n  }, 'Nearby properties retrieved successfully');\r\n});\r\n\r\n/**\r\n * Health check for property routes\r\n */\r\nexport const healthCheck = catchAsync(async (_req: Request, res: Response) => {\r\n  const propertyCount = await Property.countDocuments();\r\n  const activeProperties = await Property.countDocuments({ status: 'active' });\r\n\r\n  return ApiResponse.success(res, {\r\n    message: 'Property routes working',\r\n    timestamp: new Date().toISOString(),\r\n    statistics: {\r\n      totalProperties: propertyCount,\r\n      activeProperties,\r\n      availableProperties: await Property.countDocuments({ status: 'active', isAvailable: true })\r\n    },\r\n    endpoints: {\r\n      createProperty: 'POST /',\r\n      getProperties: 'GET /',\r\n      getProperty: 'GET /:id',\r\n      updateProperty: 'PATCH /:id',\r\n      deleteProperty: 'DELETE /:id',\r\n      getOwnerProperties: 'GET /owner',\r\n      publishProperty: 'PATCH /:id/publish',\r\n      getPropertyAnalytics: 'GET /:id/analytics',\r\n      searchProperties: 'POST /search',\r\n      getPropertySuggestions: 'GET /suggestions',\r\n      getNearbyProperties: 'GET /nearby'\r\n    }\r\n  }, 'Property service is healthy');\r\n});\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "38c39f65fcc0e6ed43109304318f1ea5c851e5e7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_13ysamrg2a = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13ysamrg2a();
var __importDefault =
/* istanbul ignore next */
(cov_13ysamrg2a().s[0]++,
/* istanbul ignore next */
(cov_13ysamrg2a().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_13ysamrg2a().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_13ysamrg2a().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[0]++;
  cov_13ysamrg2a().s[1]++;
  return /* istanbul ignore next */(cov_13ysamrg2a().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_13ysamrg2a().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_13ysamrg2a().s[3]++;
exports.healthCheck = exports.getNearbyProperties = exports.getPropertySuggestions = exports.searchProperties = exports.getPropertyAnalytics = exports.publishProperty = exports.getOwnerProperties = exports.deleteProperty = exports.updateProperty = exports.getProperty = exports.getProperties = exports.createProperty = void 0;
const Property_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[4]++, require("../models/Property"));
const User_model_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[5]++, __importDefault(require("../models/User.model")));
const logger_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[6]++, require("../utils/logger"));
const apiResponse_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[7]++, require("../utils/apiResponse"));
const appError_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[8]++, require("../utils/appError"));
const catchAsync_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[9]++, require("../utils/catchAsync"));
const mongoose_1 =
/* istanbul ignore next */
(cov_13ysamrg2a().s[10]++, require("mongoose"));
/**
 * Create a new property listing
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[11]++;
exports.createProperty = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[1]++;
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[12]++, req.user?._id);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[13]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[3][0]++;
    cov_13ysamrg2a().s[14]++;
    throw new appError_1.AppError('User authentication required', 401);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[3][1]++;
  }
  // Validate user exists and can create properties
  const user =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[15]++, await User_model_1.default.findById(userId));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[16]++;
  if (!user) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[4][0]++;
    cov_13ysamrg2a().s[17]++;
    throw new appError_1.AppError('User not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[4][1]++;
  }
  // Check if user account type allows property creation
  cov_13ysamrg2a().s[18]++;
  if (user.accountType === 'seeker') {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[5][0]++;
    cov_13ysamrg2a().s[19]++;
    throw new appError_1.AppError('Only property owners and agents can create property listings', 403);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[5][1]++;
  }
  const propertyData =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[20]++, {
    ...req.body,
    ownerId: new mongoose_1.Types.ObjectId(userId),
    lastModifiedBy: new mongoose_1.Types.ObjectId(userId),
    status: 'draft' // New properties start as draft
  });
  // Validate required fields
  /* istanbul ignore next */
  cov_13ysamrg2a().s[21]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[7][0]++, !propertyData.title) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[7][1]++, !propertyData.description) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[7][2]++, !propertyData.location)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[6][0]++;
    cov_13ysamrg2a().s[22]++;
    throw new appError_1.AppError('Title, description, and location are required', 400);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[6][1]++;
  }
  // Set default coordinates if not provided (Lagos center as fallback)
  cov_13ysamrg2a().s[23]++;
  if (!propertyData.location.coordinates) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[8][0]++;
    cov_13ysamrg2a().s[24]++;
    propertyData.location.coordinates = {
      type: 'Point',
      coordinates: [3.3792, 6.5244] // Lagos coordinates
    };
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[8][1]++;
  }
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[25]++, new Property_1.Property(propertyData));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[26]++;
  await property.save();
  /* istanbul ignore next */
  cov_13ysamrg2a().s[27]++;
  logger_1.logger.info(`Property created: ${property._id} by user: ${userId}`);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[28]++;
  return apiResponse_1.ApiResponse.success(res, {
    property: await property.populate('ownerId', 'firstName lastName email accountType')
  }, 'Property created successfully', 201);
});
/**
 * Get all properties with filtering and pagination
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[29]++;
exports.getProperties = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[2]++;
  const {
    page =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[9][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[10][0]++, 20),
    propertyType,
    listingType,
    minPrice,
    maxPrice,
    bedrooms,
    bathrooms,
    city,
    state,
    area,
    amenities,
    status =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[11][0]++, 'active'),
    sortBy =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[12][0]++, 'createdAt'),
    sortOrder =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[13][0]++, 'desc'),
    search,
    latitude,
    longitude,
    radius =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[14][0]++, 5000) // 5km default radius
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[30]++, req.query);
  // Build filter object
  const filter =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[31]++, {
    status: status,
    isAvailable: true
  });
  // Property type filter
  /* istanbul ignore next */
  cov_13ysamrg2a().s[32]++;
  if (propertyType) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[15][0]++;
    cov_13ysamrg2a().s[33]++;
    filter.propertyType = propertyType;
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[15][1]++;
  }
  // Listing type filter
  cov_13ysamrg2a().s[34]++;
  if (listingType) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[16][0]++;
    cov_13ysamrg2a().s[35]++;
    filter.listingType = listingType;
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[16][1]++;
  }
  // Price range filter
  cov_13ysamrg2a().s[36]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[18][0]++, minPrice) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[18][1]++, maxPrice)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[17][0]++;
    cov_13ysamrg2a().s[37]++;
    filter['pricing.rentPerMonth'] = {};
    /* istanbul ignore next */
    cov_13ysamrg2a().s[38]++;
    if (minPrice) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[19][0]++;
      cov_13ysamrg2a().s[39]++;
      filter['pricing.rentPerMonth'].$gte = Number(minPrice);
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[19][1]++;
    }
    cov_13ysamrg2a().s[40]++;
    if (maxPrice) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[20][0]++;
      cov_13ysamrg2a().s[41]++;
      filter['pricing.rentPerMonth'].$lte = Number(maxPrice);
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[20][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[17][1]++;
  }
  // Bedroom filter
  cov_13ysamrg2a().s[42]++;
  if (bedrooms) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[21][0]++;
    cov_13ysamrg2a().s[43]++;
    filter.bedrooms = Number(bedrooms);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[21][1]++;
  }
  // Bathroom filter
  cov_13ysamrg2a().s[44]++;
  if (bathrooms) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[22][0]++;
    cov_13ysamrg2a().s[45]++;
    filter.bathrooms = Number(bathrooms);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[22][1]++;
  }
  // Location filters
  cov_13ysamrg2a().s[46]++;
  if (city) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[23][0]++;
    cov_13ysamrg2a().s[47]++;
    filter['location.city'] = new RegExp(city, 'i');
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[23][1]++;
  }
  cov_13ysamrg2a().s[48]++;
  if (state) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[24][0]++;
    cov_13ysamrg2a().s[49]++;
    filter['location.state'] = new RegExp(state, 'i');
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[24][1]++;
  }
  cov_13ysamrg2a().s[50]++;
  if (area) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[25][0]++;
    cov_13ysamrg2a().s[51]++;
    filter['location.area'] = new RegExp(area, 'i');
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[25][1]++;
  }
  // Amenities filter
  cov_13ysamrg2a().s[52]++;
  if (amenities) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[26][0]++;
    const amenityList =
    /* istanbul ignore next */
    (cov_13ysamrg2a().s[53]++, amenities.split(','));
    /* istanbul ignore next */
    cov_13ysamrg2a().s[54]++;
    amenityList.forEach(amenity => {
      /* istanbul ignore next */
      cov_13ysamrg2a().f[3]++;
      cov_13ysamrg2a().s[55]++;
      filter[`amenities.${amenity.trim()}`] = true;
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[26][1]++;
  }
  // Text search
  cov_13ysamrg2a().s[56]++;
  if (search) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[27][0]++;
    cov_13ysamrg2a().s[57]++;
    filter.$text = {
      $search: search
    };
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[27][1]++;
  }
  // Geospatial search
  let query =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[58]++, Property_1.Property.find(filter));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[59]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[29][0]++, latitude) &&
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[29][1]++, longitude)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[28][0]++;
    cov_13ysamrg2a().s[60]++;
    query = Property_1.Property.find({
      ...filter,
      'location.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [Number(longitude), Number(latitude)]
          },
          $maxDistance: Number(radius)
        }
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[28][1]++;
  }
  // Sorting
  const sortOptions =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[61]++, {});
  /* istanbul ignore next */
  cov_13ysamrg2a().s[62]++;
  sortOptions[sortBy] = sortOrder === 'desc' ?
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[30][0]++, -1) :
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[30][1]++, 1);
  // Execute query with pagination
  const skip =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[63]++, (Number(page) - 1) * Number(limit));
  const [properties, total] =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[64]++, await Promise.all([query.sort(sortOptions).skip(skip).limit(Number(limit)).populate('ownerId', 'firstName lastName email accountType isEmailVerified').lean(), Property_1.Property.countDocuments(filter)]));
  // Calculate pagination info
  const totalPages =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[65]++, Math.ceil(total / Number(limit)));
  const hasNextPage =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[66]++, Number(page) < totalPages);
  const hasPrevPage =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[67]++, Number(page) > 1);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[68]++;
  return apiResponse_1.ApiResponse.success(res, {
    properties,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: totalPages,
      hasNextPage,
      hasPrevPage
    },
    filters: {
      applied: Object.keys(filter).length - 2,
      // Exclude status and isAvailable
      available: {
        propertyTypes: ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion'],
        listingTypes: ['rent', 'roommate', 'sublet'],
        amenities: ['wifi', 'parking', 'security', 'generator', 'borehole', 'airConditioning', 'kitchen', 'refrigerator', 'microwave', 'gasStove', 'furnished', 'tv', 'washingMachine', 'elevator', 'gym', 'swimmingPool', 'playground', 'prepaidMeter', 'cableTV', 'cleaningService']
      }
    }
  }, 'Properties retrieved successfully');
});
/**
 * Get a single property by ID
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[69]++;
exports.getProperty = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[4]++;
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[70]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[71]++, req.user?._id);
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[72]++, await Property_1.Property.findById(id).populate('ownerId', 'firstName lastName email accountType isEmailVerified phoneNumber').populate('verifiedBy', 'firstName lastName'));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[73]++;
  if (!property) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[31][0]++;
    cov_13ysamrg2a().s[74]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[31][1]++;
  }
  // Check if property is accessible
  cov_13ysamrg2a().s[75]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[33][0]++, property.status === 'draft') &&
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[33][1]++, property.ownerId._id.toString() !== userId)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[32][0]++;
    cov_13ysamrg2a().s[76]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[32][1]++;
  }
  // Increment view count (but not for the owner)
  cov_13ysamrg2a().s[77]++;
  if (property.ownerId._id.toString() !== userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[34][0]++;
    cov_13ysamrg2a().s[78]++;
    await property.incrementViews();
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[34][1]++;
  }
  cov_13ysamrg2a().s[79]++;
  return apiResponse_1.ApiResponse.success(res, {
    property
  }, 'Property retrieved successfully');
});
/**
 * Update a property
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[80]++;
exports.updateProperty = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[5]++;
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[81]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[82]++, req.user?._id);
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[83]++, await Property_1.Property.findById(id));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[84]++;
  if (!property) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[35][0]++;
    cov_13ysamrg2a().s[85]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[35][1]++;
  }
  // Check ownership
  cov_13ysamrg2a().s[86]++;
  if (property.ownerId.toString() !== userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[36][0]++;
    cov_13ysamrg2a().s[87]++;
    throw new appError_1.AppError('You can only update your own properties', 403);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[36][1]++;
  }
  // Update property
  const updateData =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[88]++, {
    ...req.body,
    lastModifiedBy: new mongoose_1.Types.ObjectId(userId)
  });
  // Remove fields that shouldn't be updated directly
  /* istanbul ignore next */
  cov_13ysamrg2a().s[89]++;
  delete updateData.ownerId;
  /* istanbul ignore next */
  cov_13ysamrg2a().s[90]++;
  delete updateData.analytics;
  /* istanbul ignore next */
  cov_13ysamrg2a().s[91]++;
  delete updateData.createdAt;
  const updatedProperty =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[92]++, await Property_1.Property.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true
  }).populate('ownerId', 'firstName lastName email accountType'));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[93]++;
  logger_1.logger.info(`Property updated: ${id} by user: ${userId}`);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[94]++;
  return apiResponse_1.ApiResponse.success(res, {
    property: updatedProperty
  }, 'Property updated successfully');
});
/**
 * Delete a property
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[95]++;
exports.deleteProperty = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[6]++;
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[96]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[97]++, req.user?._id);
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[98]++, await Property_1.Property.findById(id));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[99]++;
  if (!property) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[37][0]++;
    cov_13ysamrg2a().s[100]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[37][1]++;
  }
  // Check ownership
  cov_13ysamrg2a().s[101]++;
  if (property.ownerId.toString() !== userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[38][0]++;
    cov_13ysamrg2a().s[102]++;
    throw new appError_1.AppError('You can only delete your own properties', 403);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[38][1]++;
  }
  cov_13ysamrg2a().s[103]++;
  await Property_1.Property.findByIdAndDelete(id);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[104]++;
  logger_1.logger.info(`Property deleted: ${id} by user: ${userId}`);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[105]++;
  return apiResponse_1.ApiResponse.success(res, null, 'Property deleted successfully');
});
/**
 * Get properties by owner
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[106]++;
exports.getOwnerProperties = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[7]++;
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[107]++, req.user?._id);
  const {
    page =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[39][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[40][0]++, 20),
    status,
    sortBy =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[41][0]++, 'createdAt'),
    sortOrder =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[42][0]++, 'desc')
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[108]++, req.query);
  // Build filter
  const filter =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[109]++, {
    ownerId: userId
  });
  /* istanbul ignore next */
  cov_13ysamrg2a().s[110]++;
  if (status) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[43][0]++;
    cov_13ysamrg2a().s[111]++;
    filter.status = status;
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[43][1]++;
  }
  // Sorting
  const sortOptions =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[112]++, {});
  /* istanbul ignore next */
  cov_13ysamrg2a().s[113]++;
  sortOptions[sortBy] = sortOrder === 'desc' ?
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[44][0]++, -1) :
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[44][1]++, 1);
  // Execute query with pagination
  const skip =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[114]++, (Number(page) - 1) * Number(limit));
  const [properties, total] =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[115]++, await Promise.all([Property_1.Property.find(filter).sort(sortOptions).skip(skip).limit(Number(limit)).lean(), Property_1.Property.countDocuments(filter)]));
  // Calculate pagination info
  const totalPages =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[116]++, Math.ceil(total / Number(limit)));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[117]++;
  return apiResponse_1.ApiResponse.success(res, {
    properties,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: totalPages
    },
    summary: {
      total,
      active: await Property_1.Property.countDocuments({
        ownerId: userId,
        status: 'active'
      }),
      draft: await Property_1.Property.countDocuments({
        ownerId: userId,
        status: 'draft'
      }),
      rented: await Property_1.Property.countDocuments({
        ownerId: userId,
        status: 'rented'
      })
    }
  }, 'Owner properties retrieved successfully');
});
/**
 * Publish a draft property
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[118]++;
exports.publishProperty = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[8]++;
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[119]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[120]++, req.user?._id);
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[121]++, await Property_1.Property.findById(id));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[122]++;
  if (!property) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[45][0]++;
    cov_13ysamrg2a().s[123]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[45][1]++;
  }
  // Check ownership
  cov_13ysamrg2a().s[124]++;
  if (property.ownerId.toString() !== userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[46][0]++;
    cov_13ysamrg2a().s[125]++;
    throw new appError_1.AppError('You can only publish your own properties', 403);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[46][1]++;
  }
  // Check if property has minimum required data
  cov_13ysamrg2a().s[126]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[48][0]++, !property.photos) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[48][1]++, property.photos.length === 0)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[47][0]++;
    cov_13ysamrg2a().s[127]++;
    throw new appError_1.AppError('Property must have at least one photo before publishing', 400);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[47][1]++;
  }
  cov_13ysamrg2a().s[128]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[50][0]++, !property.pricing.rentPerMonth) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[50][1]++, property.pricing.rentPerMonth <= 0)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[49][0]++;
    cov_13ysamrg2a().s[129]++;
    throw new appError_1.AppError('Property must have a valid rent amount before publishing', 400);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[49][1]++;
  }
  // Update status to active
  cov_13ysamrg2a().s[130]++;
  property.status = 'active';
  /* istanbul ignore next */
  cov_13ysamrg2a().s[131]++;
  property.lastModifiedBy = new mongoose_1.Types.ObjectId(userId);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[132]++;
  await property.save();
  /* istanbul ignore next */
  cov_13ysamrg2a().s[133]++;
  logger_1.logger.info(`Property published: ${id} by user: ${userId}`);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[134]++;
  return apiResponse_1.ApiResponse.success(res, {
    property
  }, 'Property published successfully');
});
/**
 * Get property analytics
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[135]++;
exports.getPropertyAnalytics = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[9]++;
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[136]++, req.params);
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[137]++, req.user?._id);
  const property =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[138]++, await Property_1.Property.findById(id));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[139]++;
  if (!property) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[51][0]++;
    cov_13ysamrg2a().s[140]++;
    throw new appError_1.AppError('Property not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[51][1]++;
  }
  // Check ownership
  cov_13ysamrg2a().s[141]++;
  if (property.ownerId.toString() !== userId) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[52][0]++;
    cov_13ysamrg2a().s[142]++;
    throw new appError_1.AppError('You can only view analytics for your own properties', 403);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[52][1]++;
  }
  cov_13ysamrg2a().s[143]++;
  return apiResponse_1.ApiResponse.success(res, {
    analytics: property.analytics,
    summary: {
      totalViews: property.analytics.views,
      totalFavorites: property.analytics.favorites,
      totalInquiries: property.analytics.inquiries,
      totalApplications: property.analytics.applications,
      lastViewedAt: property.analytics.lastViewedAt,
      averageViewDuration: property.analytics.averageViewDuration
    }
  }, 'Property analytics retrieved successfully');
});
/**
 * Search properties with advanced filters
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[144]++;
exports.searchProperties = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[10]++;
  const {
    query,
    propertyType,
    listingType,
    minPrice,
    maxPrice,
    bedrooms,
    bathrooms,
    location,
    amenities,
    page =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[53][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[54][0]++, 20)
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[145]++, req.body);
  // Build aggregation pipeline
  const pipeline =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[146]++, [
  // Match basic filters
  {
    $match: {
      status: 'active',
      isAvailable: true
    }
  }]);
  // Text search
  /* istanbul ignore next */
  cov_13ysamrg2a().s[147]++;
  if (query) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[55][0]++;
    cov_13ysamrg2a().s[148]++;
    pipeline.unshift({
      $match: {
        $text: {
          $search: query
        }
      }
    });
    /* istanbul ignore next */
    cov_13ysamrg2a().s[149]++;
    pipeline.push({
      $addFields: {
        score: {
          $meta: 'textScore'
        }
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[55][1]++;
  }
  // Property type filter
  cov_13ysamrg2a().s[150]++;
  if (propertyType) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[56][0]++;
    cov_13ysamrg2a().s[151]++;
    pipeline.push({
      $match: {
        propertyType
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[56][1]++;
  }
  // Listing type filter
  cov_13ysamrg2a().s[152]++;
  if (listingType) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[57][0]++;
    cov_13ysamrg2a().s[153]++;
    pipeline.push({
      $match: {
        listingType
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[57][1]++;
  }
  // Price range filter
  cov_13ysamrg2a().s[154]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[59][0]++, minPrice) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[59][1]++, maxPrice)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[58][0]++;
    const priceMatch =
    /* istanbul ignore next */
    (cov_13ysamrg2a().s[155]++, {});
    /* istanbul ignore next */
    cov_13ysamrg2a().s[156]++;
    if (minPrice) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[60][0]++;
      cov_13ysamrg2a().s[157]++;
      priceMatch.$gte = Number(minPrice);
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[60][1]++;
    }
    cov_13ysamrg2a().s[158]++;
    if (maxPrice) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[61][0]++;
      cov_13ysamrg2a().s[159]++;
      priceMatch.$lte = Number(maxPrice);
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[61][1]++;
    }
    cov_13ysamrg2a().s[160]++;
    pipeline.push({
      $match: {
        'pricing.rentPerMonth': priceMatch
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[58][1]++;
  }
  // Bedroom/bathroom filters
  cov_13ysamrg2a().s[161]++;
  if (bedrooms) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[62][0]++;
    cov_13ysamrg2a().s[162]++;
    pipeline.push({
      $match: {
        bedrooms: Number(bedrooms)
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[62][1]++;
  }
  cov_13ysamrg2a().s[163]++;
  if (bathrooms) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[63][0]++;
    cov_13ysamrg2a().s[164]++;
    pipeline.push({
      $match: {
        bathrooms: Number(bathrooms)
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[63][1]++;
  }
  // Location filter
  cov_13ysamrg2a().s[165]++;
  if (location) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[64][0]++;
    const locationMatch =
    /* istanbul ignore next */
    (cov_13ysamrg2a().s[166]++, {});
    /* istanbul ignore next */
    cov_13ysamrg2a().s[167]++;
    if (location.city) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[65][0]++;
      cov_13ysamrg2a().s[168]++;
      locationMatch['location.city'] = new RegExp(location.city, 'i');
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[65][1]++;
    }
    cov_13ysamrg2a().s[169]++;
    if (location.state) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[66][0]++;
      cov_13ysamrg2a().s[170]++;
      locationMatch['location.state'] = new RegExp(location.state, 'i');
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[66][1]++;
    }
    cov_13ysamrg2a().s[171]++;
    if (location.area) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[67][0]++;
      cov_13ysamrg2a().s[172]++;
      locationMatch['location.area'] = new RegExp(location.area, 'i');
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[67][1]++;
    }
    cov_13ysamrg2a().s[173]++;
    pipeline.push({
      $match: locationMatch
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[64][1]++;
  }
  // Amenities filter
  cov_13ysamrg2a().s[174]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[69][0]++, amenities) &&
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[69][1]++, amenities.length > 0)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[68][0]++;
    const amenityMatch =
    /* istanbul ignore next */
    (cov_13ysamrg2a().s[175]++, {});
    /* istanbul ignore next */
    cov_13ysamrg2a().s[176]++;
    amenities.forEach(amenity => {
      /* istanbul ignore next */
      cov_13ysamrg2a().f[11]++;
      cov_13ysamrg2a().s[177]++;
      amenityMatch[`amenities.${amenity}`] = true;
    });
    /* istanbul ignore next */
    cov_13ysamrg2a().s[178]++;
    pipeline.push({
      $match: amenityMatch
    });
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[68][1]++;
  }
  // Add owner information
  cov_13ysamrg2a().s[179]++;
  pipeline.push({
    $lookup: {
      from: 'users',
      localField: 'ownerId',
      foreignField: '_id',
      as: 'owner',
      pipeline: [{
        $project: {
          firstName: 1,
          lastName: 1,
          email: 1,
          accountType: 1,
          isEmailVerified: 1
        }
      }]
    }
  });
  /* istanbul ignore next */
  cov_13ysamrg2a().s[180]++;
  pipeline.push({
    $unwind: '$owner'
  });
  // Sort by relevance (if text search) or date
  /* istanbul ignore next */
  cov_13ysamrg2a().s[181]++;
  if (query) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[70][0]++;
    cov_13ysamrg2a().s[182]++;
    pipeline.push({
      $sort: {
        score: {
          $meta: 'textScore'
        },
        createdAt: -1
      }
    });
  } else {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[70][1]++;
    cov_13ysamrg2a().s[183]++;
    pipeline.push({
      $sort: {
        createdAt: -1
      }
    });
  }
  // Pagination
  const skip =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[184]++, (Number(page) - 1) * Number(limit));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[185]++;
  pipeline.push({
    $skip: skip
  }, {
    $limit: Number(limit)
  });
  // Execute aggregation
  const [properties, totalCount] =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[186]++, await Promise.all([Property_1.Property.aggregate(pipeline), Property_1.Property.aggregate([...pipeline.slice(0, -2),
  // Remove skip and limit for count
  {
    $count: 'total'
  }])]));
  const total =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[187]++,
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[71][0]++, totalCount[0]?.total) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[71][1]++, 0));
  const totalPages =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[188]++, Math.ceil(total / Number(limit)));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[189]++;
  return apiResponse_1.ApiResponse.success(res, {
    properties,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: totalPages
    },
    searchInfo: {
      query,
      resultsFound: properties.length,
      totalMatches: total
    }
  }, 'Property search completed successfully');
});
/**
 * Get property suggestions based on user preferences
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[190]++;
exports.getPropertySuggestions = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[12]++;
  const userId =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[191]++, req.user?._id);
  const {
    limit =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[72][0]++, 10)
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[192]++, req.query);
  // Get user profile to understand preferences
  const user =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[193]++, await User_model_1.default.findById(userId).populate('profile'));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[194]++;
  if (!user) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[73][0]++;
    cov_13ysamrg2a().s[195]++;
    throw new appError_1.AppError('User not found', 404);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[73][1]++;
  }
  // Build suggestion criteria based on user profile
  const suggestionCriteria =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[196]++, {
    status: 'active',
    isAvailable: true
  });
  // If user has housing preferences, use them
  // Get user profile for housing preferences
  const Profile =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[197]++, require('../models/Profile.model').default);
  const userProfile =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[198]++, await Profile.findOne({
    userId
  }));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[199]++;
  if (userProfile?.housingPreferences) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[74][0]++;
    const prefs =
    /* istanbul ignore next */
    (cov_13ysamrg2a().s[200]++, userProfile.housingPreferences);
    // Property types
    /* istanbul ignore next */
    cov_13ysamrg2a().s[201]++;
    if (
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[76][0]++, prefs.propertyTypes) &&
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[76][1]++, prefs.propertyTypes.length > 0)) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[75][0]++;
      cov_13ysamrg2a().s[202]++;
      suggestionCriteria.propertyType = {
        $in: prefs.propertyTypes
      };
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[75][1]++;
    }
    // Budget range
    cov_13ysamrg2a().s[203]++;
    if (prefs.budgetRange) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[77][0]++;
      cov_13ysamrg2a().s[204]++;
      suggestionCriteria['pricing.rentPerMonth'] = {
        $gte: prefs.budgetRange.min,
        $lte: prefs.budgetRange.max
      };
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[77][1]++;
    }
    // Preferred areas
    cov_13ysamrg2a().s[205]++;
    if (
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[79][0]++, prefs.preferredAreas) &&
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[79][1]++, prefs.preferredAreas.length > 0)) {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[78][0]++;
      cov_13ysamrg2a().s[206]++;
      suggestionCriteria['location.area'] = {
        $in: prefs.preferredAreas
      };
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[78][1]++;
    }
    // Room type
    cov_13ysamrg2a().s[207]++;
    if (prefs.roomType === 'private-room') {
      /* istanbul ignore next */
      cov_13ysamrg2a().b[80][0]++;
      cov_13ysamrg2a().s[208]++;
      suggestionCriteria.listingType = 'roommate';
    } else
    /* istanbul ignore next */
    {
      cov_13ysamrg2a().b[80][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[74][1]++;
  }
  // Get suggested properties
  const suggestions =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[209]++, await Property_1.Property.find(suggestionCriteria).sort({
    'analytics.views': -1,
    createdAt: -1
  }).limit(Number(limit)).populate('ownerId', 'firstName lastName email accountType').lean());
  /* istanbul ignore next */
  cov_13ysamrg2a().s[210]++;
  return apiResponse_1.ApiResponse.success(res, {
    suggestions,
    criteria: suggestionCriteria,
    count: suggestions.length
  }, 'Property suggestions retrieved successfully');
});
/**
 * Get nearby properties
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[211]++;
exports.getNearbyProperties = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[13]++;
  const {
    latitude,
    longitude,
    radius =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[81][0]++, 5000),
    limit =
    /* istanbul ignore next */
    (cov_13ysamrg2a().b[82][0]++, 20)
  } =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[212]++, req.query);
  /* istanbul ignore next */
  cov_13ysamrg2a().s[213]++;
  if (
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[84][0]++, !latitude) ||
  /* istanbul ignore next */
  (cov_13ysamrg2a().b[84][1]++, !longitude)) {
    /* istanbul ignore next */
    cov_13ysamrg2a().b[83][0]++;
    cov_13ysamrg2a().s[214]++;
    throw new appError_1.AppError('Latitude and longitude are required', 400);
  } else
  /* istanbul ignore next */
  {
    cov_13ysamrg2a().b[83][1]++;
  }
  const nearbyProperties =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[215]++, await Property_1.Property.findNearby(Number(longitude), Number(latitude), Number(radius)).limit(Number(limit)).populate('ownerId', 'firstName lastName email accountType').lean());
  /* istanbul ignore next */
  cov_13ysamrg2a().s[216]++;
  return apiResponse_1.ApiResponse.success(res, {
    properties: nearbyProperties,
    searchCenter: {
      latitude: Number(latitude),
      longitude: Number(longitude)
    },
    radius: Number(radius),
    count: nearbyProperties.length
  }, 'Nearby properties retrieved successfully');
});
/**
 * Health check for property routes
 */
/* istanbul ignore next */
cov_13ysamrg2a().s[217]++;
exports.healthCheck = (0, catchAsync_1.catchAsync)(async (_req, res) => {
  /* istanbul ignore next */
  cov_13ysamrg2a().f[14]++;
  const propertyCount =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[218]++, await Property_1.Property.countDocuments());
  const activeProperties =
  /* istanbul ignore next */
  (cov_13ysamrg2a().s[219]++, await Property_1.Property.countDocuments({
    status: 'active'
  }));
  /* istanbul ignore next */
  cov_13ysamrg2a().s[220]++;
  return apiResponse_1.ApiResponse.success(res, {
    message: 'Property routes working',
    timestamp: new Date().toISOString(),
    statistics: {
      totalProperties: propertyCount,
      activeProperties,
      availableProperties: await Property_1.Property.countDocuments({
        status: 'active',
        isAvailable: true
      })
    },
    endpoints: {
      createProperty: 'POST /',
      getProperties: 'GET /',
      getProperty: 'GET /:id',
      updateProperty: 'PATCH /:id',
      deleteProperty: 'DELETE /:id',
      getOwnerProperties: 'GET /owner',
      publishProperty: 'PATCH /:id/publish',
      getPropertyAnalytics: 'GET /:id/analytics',
      searchProperties: 'POST /search',
      getPropertySuggestions: 'GET /suggestions',
      getNearbyProperties: 'GET /nearby'
    }
  }, 'Property service is healthy');
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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