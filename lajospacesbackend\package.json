{"name": "lajospaces-backend", "version": "1.0.0", "description": "Backend API for LajoSpaces - Roommate matching platform", "main": "dist/server.js", "scripts": {"dev": "ts-node --transpile-only src/server.ts", "build": "tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:ci": "jest --coverage --watchAll=false --passWithNoTests", "test:performance": "node tests/performance/benchmark.js", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "prepare": "husky install", "commit": "cz", "quality:check": "npm run lint && npm run format:check && npm run build && npm run test:coverage", "quality:fix": "npm run lint:fix && npm run format", "coverage:check": "node scripts/coverage-check.js", "type-check": "tsc --noEmit", "seed": "ts-node --transpile-only src/scripts/seedDatabase.ts", "validate": "ts-node --transpile-only src/scripts/validateDatabase.ts"}, "keywords": ["roommate", "matching", "housing", "nodejs", "express", "mongodb", "typescript"], "author": "LajoSpaces Team", "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "handlebars": "^4.7.8", "helmet": "^7.1.0", "isomorphic-dompurify": "^2.6.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mjml": "^4.15.3", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^6.9.8", "rate-limit-redis": "^4.2.0", "redis": "^4.6.12", "sharp": "^0.34.2", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0", "uuid": "^11.1.0", "validator": "^13.11.0", "winston": "^3.11.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/socket.io": "^3.0.2", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/validator": "^13.11.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "axios": "^1.6.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "mongodb-memory-server": "^9.1.3", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}