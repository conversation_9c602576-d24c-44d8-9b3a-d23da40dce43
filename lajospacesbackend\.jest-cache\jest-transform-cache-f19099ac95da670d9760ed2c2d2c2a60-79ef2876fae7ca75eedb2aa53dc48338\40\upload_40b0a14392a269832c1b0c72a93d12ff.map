{"version": 3, "names": ["cov_s5eb49wyj", "actualCoverage", "multer_1", "s", "__importDefault", "require", "appError_1", "logger_1", "fileSecurityService_1", "allowedImageTypes", "allowedDocumentTypes", "allowedVideoTypes", "FILE_SIZE_LIMITS", "image", "document", "video", "avatar", "property", "storage", "default", "memoryStorage", "createFileFilter", "allowedTypes", "maxSize", "f", "req", "file", "cb", "includes", "mimetype", "b", "error", "Error", "join", "createUploadMiddleware", "fieldName", "maxCount", "limits", "fileSize", "files", "fileFilter", "exports", "uploadSingleImage", "single", "uploadMultipleImages", "array", "uploadAvatar", "uploadPropertyPhotos", "uploadDocument", "uploadVideo", "uploadMessageFile", "allAllowedTypes", "sizeLimit", "handleUploadError", "res", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "statusCode", "code", "logger", "field", "status", "json", "success", "validateUploadedFile", "type", "AppError", "size", "Math", "round", "extractFileMetadata", "originalName", "originalname", "mimeType", "encoding", "fieldname", "validateUploadedFiles", "length", "maxFiles", "for<PERSON>ach", "index", "performSecurityCheck", "securityResult", "performSecurityValidation", "isSecure", "warn", "filename", "issues", "warnings"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\upload.ts"], "sourcesContent": ["import multer from 'multer';\r\nimport { Request, Response, NextFunction } from 'express';\r\nimport { AppError } from '../utils/appError';\r\nimport { logger } from '../utils/logger';\r\nimport { performSecurityValidation } from '../services/fileSecurityService';\r\n\r\n// File type validation\r\nconst allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];\r\nconst allowedDocumentTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\r\nconst allowedVideoTypes = ['video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm'];\r\n\r\n// File size limits (in bytes)\r\nconst FILE_SIZE_LIMITS = {\r\n  image: 10 * 1024 * 1024, // 10MB\r\n  document: 5 * 1024 * 1024, // 5MB\r\n  video: 50 * 1024 * 1024, // 50MB\r\n  avatar: 2 * 1024 * 1024, // 2MB for profile pictures\r\n  property: 15 * 1024 * 1024 // 15MB for property photos\r\n};\r\n\r\n// Multer configuration for memory storage\r\nconst storage = multer.memoryStorage();\r\n\r\n// File filter function\r\nconst createFileFilter = (allowedTypes: string[], maxSize: number) => {\r\n  return (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {\r\n    // Check file type\r\n    if (!allowedTypes.includes(file.mimetype)) {\r\n      const error = new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);\r\n      return cb(error as any, false);\r\n    }\r\n\r\n    // File size will be checked by multer limits\r\n    cb(null, true);\r\n  };\r\n};\r\n\r\n// Generic upload middleware factory\r\nconst createUploadMiddleware = (\r\n  fieldName: string,\r\n  allowedTypes: string[],\r\n  maxSize: number,\r\n  maxCount: number = 1\r\n) => {\r\n  return multer({\r\n    storage,\r\n    limits: {\r\n      fileSize: maxSize,\r\n      files: maxCount\r\n    },\r\n    fileFilter: createFileFilter(allowedTypes, maxSize)\r\n  });\r\n};\r\n\r\n// Specific upload middlewares\r\nexport const uploadSingleImage = createUploadMiddleware(\r\n  'image',\r\n  allowedImageTypes,\r\n  FILE_SIZE_LIMITS.image,\r\n  1\r\n).single('image');\r\n\r\nexport const uploadMultipleImages = createUploadMiddleware(\r\n  'images',\r\n  allowedImageTypes,\r\n  FILE_SIZE_LIMITS.image,\r\n  10\r\n).array('images', 10);\r\n\r\nexport const uploadAvatar = createUploadMiddleware(\r\n  'avatar',\r\n  allowedImageTypes,\r\n  FILE_SIZE_LIMITS.avatar,\r\n  1\r\n).single('avatar');\r\n\r\nexport const uploadPropertyPhotos = createUploadMiddleware(\r\n  'photos',\r\n  allowedImageTypes,\r\n  FILE_SIZE_LIMITS.property,\r\n  20\r\n).array('photos', 20);\r\n\r\nexport const uploadDocument = createUploadMiddleware(\r\n  'document',\r\n  allowedDocumentTypes,\r\n  FILE_SIZE_LIMITS.document,\r\n  1\r\n).single('document');\r\n\r\nexport const uploadVideo = createUploadMiddleware(\r\n  'video',\r\n  allowedVideoTypes,\r\n  FILE_SIZE_LIMITS.video,\r\n  1\r\n).single('video');\r\n\r\n// Mixed upload for messages (image, document, or video)\r\nexport const uploadMessageFile = multer({\r\n  storage,\r\n  limits: {\r\n    fileSize: FILE_SIZE_LIMITS.image, // Use image limit as default\r\n    files: 1\r\n  },\r\n  fileFilter: (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {\r\n    const allAllowedTypes = [...allowedImageTypes, ...allowedDocumentTypes, ...allowedVideoTypes];\r\n    \r\n    if (!allAllowedTypes.includes(file.mimetype)) {\r\n      const error = new Error(`Invalid file type. Allowed types: ${allAllowedTypes.join(', ')}`);\r\n      return cb(error as any, false);\r\n    }\r\n\r\n    // Adjust size limit based on file type\r\n    let sizeLimit = FILE_SIZE_LIMITS.image;\r\n    if (allowedDocumentTypes.includes(file.mimetype)) {\r\n      sizeLimit = FILE_SIZE_LIMITS.document;\r\n    } else if (allowedVideoTypes.includes(file.mimetype)) {\r\n      sizeLimit = FILE_SIZE_LIMITS.video;\r\n    }\r\n\r\n    // Note: We can't dynamically change multer limits here, so we'll check in the route handler\r\n    cb(null, true);\r\n  }\r\n}).single('file');\r\n\r\n// Error handling middleware for multer errors\r\nexport const handleUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {\r\n  if (error instanceof multer.MulterError) {\r\n    let message = 'File upload error';\r\n    let statusCode = 400;\r\n\r\n    switch (error.code) {\r\n      case 'LIMIT_FILE_SIZE':\r\n        message = 'File size too large';\r\n        break;\r\n      case 'LIMIT_FILE_COUNT':\r\n        message = 'Too many files uploaded';\r\n        break;\r\n      case 'LIMIT_UNEXPECTED_FILE':\r\n        message = 'Unexpected file field';\r\n        break;\r\n      case 'LIMIT_PART_COUNT':\r\n        message = 'Too many parts in multipart form';\r\n        break;\r\n      case 'LIMIT_FIELD_KEY':\r\n        message = 'Field name too long';\r\n        break;\r\n      case 'LIMIT_FIELD_VALUE':\r\n        message = 'Field value too long';\r\n        break;\r\n      case 'LIMIT_FIELD_COUNT':\r\n        message = 'Too many fields';\r\n        break;\r\n      default:\r\n        message = error.message || 'File upload error';\r\n    }\r\n\r\n    logger.error('Multer upload error:', {\r\n      code: error.code,\r\n      message: error.message,\r\n      field: error.field\r\n    });\r\n\r\n    return res.status(statusCode).json({\r\n      success: false,\r\n      message,\r\n      error: {\r\n        code: error.code,\r\n        field: error.field\r\n      }\r\n    });\r\n  }\r\n\r\n  // Handle other upload-related errors\r\n  if (error.message && error.message.includes('Invalid file type')) {\r\n    logger.error('File type validation error:', error.message);\r\n    return res.status(400).json({\r\n      success: false,\r\n      message: error.message\r\n    });\r\n  }\r\n\r\n  // Pass other errors to the global error handler\r\n  next(error);\r\n};\r\n\r\n// File validation helper\r\nexport const validateUploadedFile = (file: Express.Multer.File, type: 'image' | 'document' | 'video' | 'avatar' | 'property') => {\r\n  if (!file) {\r\n    throw new AppError('No file uploaded', 400);\r\n  }\r\n\r\n  // Check file size based on type\r\n  const sizeLimit = FILE_SIZE_LIMITS[type];\r\n  if (file.size > sizeLimit) {\r\n    throw new AppError(`File size exceeds limit of ${Math.round(sizeLimit / (1024 * 1024))}MB`, 400);\r\n  }\r\n\r\n  // Check file type based on type\r\n  let allowedTypes: string[];\r\n  switch (type) {\r\n    case 'image':\r\n    case 'avatar':\r\n    case 'property':\r\n      allowedTypes = allowedImageTypes;\r\n      break;\r\n    case 'document':\r\n      allowedTypes = allowedDocumentTypes;\r\n      break;\r\n    case 'video':\r\n      allowedTypes = allowedVideoTypes;\r\n      break;\r\n    default:\r\n      allowedTypes = allowedImageTypes;\r\n  }\r\n\r\n  if (!allowedTypes.includes(file.mimetype)) {\r\n    throw new AppError(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`, 400);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n// File metadata extractor\r\nexport const extractFileMetadata = (file: Express.Multer.File) => {\r\n  return {\r\n    originalName: file.originalname,\r\n    mimeType: file.mimetype,\r\n    size: file.size,\r\n    encoding: file.encoding,\r\n    fieldName: file.fieldname\r\n  };\r\n};\r\n\r\n// Bulk file validation\r\nexport const validateUploadedFiles = (files: Express.Multer.File[], type: 'image' | 'document' | 'video' | 'property') => {\r\n  if (!files || files.length === 0) {\r\n    throw new AppError('No files uploaded', 400);\r\n  }\r\n\r\n  const maxFiles = type === 'property' ? 20 : 10;\r\n  if (files.length > maxFiles) {\r\n    throw new AppError(`Too many files. Maximum ${maxFiles} files allowed`, 400);\r\n  }\r\n\r\n  files.forEach((file, index) => {\r\n    try {\r\n      validateUploadedFile(file, type);\r\n    } catch (error) {\r\n      throw new AppError(`File ${index + 1}: ${(error as AppError).message}`, 400);\r\n    }\r\n  });\r\n\r\n  return true;\r\n};\r\n\r\n// Comprehensive security check using the security service\r\nexport const performSecurityCheck = async (file: Express.Multer.File): Promise<boolean> => {\r\n  try {\r\n    const securityResult = await performSecurityValidation(file);\r\n\r\n    if (!securityResult.isSecure) {\r\n      logger.warn('File failed comprehensive security validation:', {\r\n        filename: file.originalname,\r\n        mimetype: file.mimetype,\r\n        size: file.size,\r\n        issues: securityResult.issues,\r\n        warnings: securityResult.warnings\r\n      });\r\n      return false;\r\n    }\r\n\r\n    // Log warnings but allow the file\r\n    if (securityResult.warnings.length > 0) {\r\n      logger.warn('File security warnings:', {\r\n        filename: file.originalname,\r\n        warnings: securityResult.warnings\r\n      });\r\n    }\r\n\r\n    return true;\r\n  } catch (error) {\r\n    logger.error('Security check error:', error);\r\n    return false;\r\n  }\r\n};\r\n\r\nexport default {\r\n  uploadSingleImage,\r\n  uploadMultipleImages,\r\n  uploadAvatar,\r\n  uploadPropertyPhotos,\r\n  uploadDocument,\r\n  uploadVideo,\r\n  uploadMessageFile,\r\n  handleUploadError,\r\n  validateUploadedFile,\r\n  validateUploadedFiles,\r\n  extractFileMetadata,\r\n  performSecurityCheck\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZN,MAAAE,QAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AAEA,MAAAC,UAAA;AAAA;AAAA,CAAAN,aAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,MAAAG,qBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAG,CAAA,OAAAE,OAAA;AAEA;AACA,MAAMI,iBAAiB;AAAA;AAAA,CAAAT,aAAA,GAAAG,CAAA,OAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AAC7F,MAAMO,oBAAoB;AAAA;AAAA,CAAAV,aAAA,GAAAG,CAAA,OAAG,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,yEAAyE,CAAC;AACjJ,MAAMQ,iBAAiB;AAAA;AAAA,CAAAX,aAAA,GAAAG,CAAA,QAAG,CAAC,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,CAAC;AAEtF;AACA,MAAMS,gBAAgB;AAAA;AAAA,CAAAZ,aAAA,GAAAG,CAAA,QAAG;EACvBU,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EACzBC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EAC3BC,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EACzBC,MAAM,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;EAAE;EACzBC,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;CAC5B;AAED;AACA,MAAMC,OAAO;AAAA;AAAA,CAAAlB,aAAA,GAAAG,CAAA,QAAGD,QAAA,CAAAiB,OAAM,CAACC,aAAa,EAAE;AAEtC;AAAA;AAAApB,aAAA,GAAAG,CAAA;AACA,MAAMkB,gBAAgB,GAAGA,CAACC,YAAsB,EAAEC,OAAe,KAAI;EAAA;EAAAvB,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EACnE,OAAO,CAACsB,GAAY,EAAEC,IAAyB,EAAEC,EAA6B,KAAI;IAAA;IAAA3B,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAG,CAAA;IAChF;IACA,IAAI,CAACmB,YAAY,CAACM,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;MAAA;MAAA7B,aAAA,GAAA8B,CAAA;MACzC,MAAMC,KAAK;MAAA;MAAA,CAAA/B,aAAA,GAAAG,CAAA,QAAG,IAAI6B,KAAK,CAAC,qCAAqCV,YAAY,CAACW,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAAC;MAAAjC,aAAA,GAAAG,CAAA;MACxF,OAAOwB,EAAE,CAACI,KAAY,EAAE,KAAK,CAAC;IAChC,CAAC;IAAA;IAAA;MAAA/B,aAAA,GAAA8B,CAAA;IAAA;IAED;IAAA9B,aAAA,GAAAG,CAAA;IACAwB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAChB,CAAC;AACH,CAAC;AAED;AAAA;AAAA3B,aAAA,GAAAG,CAAA;AACA,MAAM+B,sBAAsB,GAAGA,CAC7BC,SAAiB,EACjBb,YAAsB,EACtBC,OAAe,EACfa,QAAA;AAAA;AAAA,CAAApC,aAAA,GAAA8B,CAAA,UAAmB,CAAC,MAClB;EAAA;EAAA9B,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EACF,OAAO,IAAAD,QAAA,CAAAiB,OAAM,EAAC;IACZD,OAAO;IACPmB,MAAM,EAAE;MACNC,QAAQ,EAAEf,OAAO;MACjBgB,KAAK,EAAEH;KACR;IACDI,UAAU,EAAEnB,gBAAgB,CAACC,YAAY,EAAEC,OAAO;GACnD,CAAC;AACJ,CAAC;AAED;AAAA;AAAAvB,aAAA,GAAAG,CAAA;AACasC,OAAA,CAAAC,iBAAiB,GAAGR,sBAAsB,CACrD,OAAO,EACPzB,iBAAiB,EACjBG,gBAAgB,CAACC,KAAK,EACtB,CAAC,CACF,CAAC8B,MAAM,CAAC,OAAO,CAAC;AAAC;AAAA3C,aAAA,GAAAG,CAAA;AAELsC,OAAA,CAAAG,oBAAoB,GAAGV,sBAAsB,CACxD,QAAQ,EACRzB,iBAAiB,EACjBG,gBAAgB,CAACC,KAAK,EACtB,EAAE,CACH,CAACgC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;AAAC;AAAA7C,aAAA,GAAAG,CAAA;AAETsC,OAAA,CAAAK,YAAY,GAAGZ,sBAAsB,CAChD,QAAQ,EACRzB,iBAAiB,EACjBG,gBAAgB,CAACI,MAAM,EACvB,CAAC,CACF,CAAC2B,MAAM,CAAC,QAAQ,CAAC;AAAC;AAAA3C,aAAA,GAAAG,CAAA;AAENsC,OAAA,CAAAM,oBAAoB,GAAGb,sBAAsB,CACxD,QAAQ,EACRzB,iBAAiB,EACjBG,gBAAgB,CAACK,QAAQ,EACzB,EAAE,CACH,CAAC4B,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;AAAC;AAAA7C,aAAA,GAAAG,CAAA;AAETsC,OAAA,CAAAO,cAAc,GAAGd,sBAAsB,CAClD,UAAU,EACVxB,oBAAoB,EACpBE,gBAAgB,CAACE,QAAQ,EACzB,CAAC,CACF,CAAC6B,MAAM,CAAC,UAAU,CAAC;AAAC;AAAA3C,aAAA,GAAAG,CAAA;AAERsC,OAAA,CAAAQ,WAAW,GAAGf,sBAAsB,CAC/C,OAAO,EACPvB,iBAAiB,EACjBC,gBAAgB,CAACG,KAAK,EACtB,CAAC,CACF,CAAC4B,MAAM,CAAC,OAAO,CAAC;AAEjB;AAAA;AAAA3C,aAAA,GAAAG,CAAA;AACasC,OAAA,CAAAS,iBAAiB,GAAG,IAAAhD,QAAA,CAAAiB,OAAM,EAAC;EACtCD,OAAO;EACPmB,MAAM,EAAE;IACNC,QAAQ,EAAE1B,gBAAgB,CAACC,KAAK;IAAE;IAClC0B,KAAK,EAAE;GACR;EACDC,UAAU,EAAEA,CAACf,GAAY,EAAEC,IAAyB,EAAEC,EAA6B,KAAI;IAAA;IAAA3B,aAAA,GAAAwB,CAAA;IACrF,MAAM2B,eAAe;IAAA;IAAA,CAAAnD,aAAA,GAAAG,CAAA,QAAG,CAAC,GAAGM,iBAAiB,EAAE,GAAGC,oBAAoB,EAAE,GAAGC,iBAAiB,CAAC;IAAC;IAAAX,aAAA,GAAAG,CAAA;IAE9F,IAAI,CAACgD,eAAe,CAACvB,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;MAAA;MAAA7B,aAAA,GAAA8B,CAAA;MAC5C,MAAMC,KAAK;MAAA;MAAA,CAAA/B,aAAA,GAAAG,CAAA,QAAG,IAAI6B,KAAK,CAAC,qCAAqCmB,eAAe,CAAClB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAAC;MAAAjC,aAAA,GAAAG,CAAA;MAC3F,OAAOwB,EAAE,CAACI,KAAY,EAAE,KAAK,CAAC;IAChC,CAAC;IAAA;IAAA;MAAA/B,aAAA,GAAA8B,CAAA;IAAA;IAED;IACA,IAAIsB,SAAS;IAAA;IAAA,CAAApD,aAAA,GAAAG,CAAA,QAAGS,gBAAgB,CAACC,KAAK;IAAC;IAAAb,aAAA,GAAAG,CAAA;IACvC,IAAIO,oBAAoB,CAACkB,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;MAAA;MAAA7B,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MAChDiD,SAAS,GAAGxC,gBAAgB,CAACE,QAAQ;IACvC,CAAC,MAAM;MAAA;MAAAd,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MAAA,IAAIQ,iBAAiB,CAACiB,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;QAAA;QAAA7B,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACpDiD,SAAS,GAAGxC,gBAAgB,CAACG,KAAK;MACpC,CAAC;MAAA;MAAA;QAAAf,aAAA,GAAA8B,CAAA;MAAA;IAAD;IAEA;IAAA;IAAA9B,aAAA,GAAAG,CAAA;IACAwB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAChB;CACD,CAAC,CAACgB,MAAM,CAAC,MAAM,CAAC;AAEjB;AAAA;AAAA3C,aAAA,GAAAG,CAAA;AACO,MAAMkD,iBAAiB,GAAGA,CAACtB,KAAU,EAAEN,GAAY,EAAE6B,GAAa,EAAEC,IAAkB,KAAI;EAAA;EAAAvD,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EAC/F,IAAI4B,KAAK,YAAY7B,QAAA,CAAAiB,OAAM,CAACqC,WAAW,EAAE;IAAA;IAAAxD,aAAA,GAAA8B,CAAA;IACvC,IAAI2B,OAAO;IAAA;IAAA,CAAAzD,aAAA,GAAAG,CAAA,QAAG,mBAAmB;IACjC,IAAIuD,UAAU;IAAA;IAAA,CAAA1D,aAAA,GAAAG,CAAA,QAAG,GAAG;IAAC;IAAAH,aAAA,GAAAG,CAAA;IAErB,QAAQ4B,KAAK,CAAC4B,IAAI;MAChB,KAAK,iBAAiB;QAAA;QAAA3D,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACpBsD,OAAO,GAAG,qBAAqB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QAChC;MACF,KAAK,kBAAkB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACrBsD,OAAO,GAAG,yBAAyB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QACpC;MACF,KAAK,uBAAuB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QAC1BsD,OAAO,GAAG,uBAAuB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QAClC;MACF,KAAK,kBAAkB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACrBsD,OAAO,GAAG,kCAAkC;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QAC7C;MACF,KAAK,iBAAiB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACpBsD,OAAO,GAAG,qBAAqB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QAChC;MACF,KAAK,mBAAmB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACtBsD,OAAO,GAAG,sBAAsB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QACjC;MACF,KAAK,mBAAmB;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACtBsD,OAAO,GAAG,iBAAiB;QAAC;QAAAzD,aAAA,GAAAG,CAAA;QAC5B;MACF;QAAA;QAAAH,aAAA,GAAA8B,CAAA;QAAA9B,aAAA,GAAAG,CAAA;QACEsD,OAAO;QAAG;QAAA,CAAAzD,aAAA,GAAA8B,CAAA,WAAAC,KAAK,CAAC0B,OAAO;QAAA;QAAA,CAAAzD,aAAA,GAAA8B,CAAA,WAAI,mBAAmB;IAClD;IAAC;IAAA9B,aAAA,GAAAG,CAAA;IAEDI,QAAA,CAAAqD,MAAM,CAAC7B,KAAK,CAAC,sBAAsB,EAAE;MACnC4B,IAAI,EAAE5B,KAAK,CAAC4B,IAAI;MAChBF,OAAO,EAAE1B,KAAK,CAAC0B,OAAO;MACtBI,KAAK,EAAE9B,KAAK,CAAC8B;KACd,CAAC;IAAC;IAAA7D,aAAA,GAAAG,CAAA;IAEH,OAAOmD,GAAG,CAACQ,MAAM,CAACJ,UAAU,CAAC,CAACK,IAAI,CAAC;MACjCC,OAAO,EAAE,KAAK;MACdP,OAAO;MACP1B,KAAK,EAAE;QACL4B,IAAI,EAAE5B,KAAK,CAAC4B,IAAI;QAChBE,KAAK,EAAE9B,KAAK,CAAC8B;;KAEhB,CAAC;EACJ,CAAC;EAAA;EAAA;IAAA7D,aAAA,GAAA8B,CAAA;EAAA;EAED;EAAA9B,aAAA,GAAAG,CAAA;EACA;EAAI;EAAA,CAAAH,aAAA,GAAA8B,CAAA,WAAAC,KAAK,CAAC0B,OAAO;EAAA;EAAA,CAAAzD,aAAA,GAAA8B,CAAA,WAAIC,KAAK,CAAC0B,OAAO,CAAC7B,QAAQ,CAAC,mBAAmB,CAAC,GAAE;IAAA;IAAA5B,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IAChEI,QAAA,CAAAqD,MAAM,CAAC7B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC0B,OAAO,CAAC;IAAC;IAAAzD,aAAA,GAAAG,CAAA;IAC3D,OAAOmD,GAAG,CAACQ,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BC,OAAO,EAAE,KAAK;MACdP,OAAO,EAAE1B,KAAK,CAAC0B;KAChB,CAAC;EACJ,CAAC;EAAA;EAAA;IAAAzD,aAAA,GAAA8B,CAAA;EAAA;EAED;EAAA9B,aAAA,GAAAG,CAAA;EACAoD,IAAI,CAACxB,KAAK,CAAC;AACb,CAAC;AAAC;AAAA/B,aAAA,GAAAG,CAAA;AA1DWsC,OAAA,CAAAY,iBAAiB,GAAAA,iBAAA;AA4D9B;AAAA;AAAArD,aAAA,GAAAG,CAAA;AACO,MAAM8D,oBAAoB,GAAGA,CAACvC,IAAyB,EAAEwC,IAA4D,KAAI;EAAA;EAAAlE,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EAC9H,IAAI,CAACuB,IAAI,EAAE;IAAA;IAAA1B,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IACT,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC;EAC7C,CAAC;EAAA;EAAA;IAAAnE,aAAA,GAAA8B,CAAA;EAAA;EAED;EACA,MAAMsB,SAAS;EAAA;EAAA,CAAApD,aAAA,GAAAG,CAAA,QAAGS,gBAAgB,CAACsD,IAAI,CAAC;EAAC;EAAAlE,aAAA,GAAAG,CAAA;EACzC,IAAIuB,IAAI,CAAC0C,IAAI,GAAGhB,SAAS,EAAE;IAAA;IAAApD,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IACzB,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,8BAA8BE,IAAI,CAACC,KAAK,CAAClB,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC;EAClG,CAAC;EAAA;EAAA;IAAApD,aAAA,GAAA8B,CAAA;EAAA;EAED;EACA,IAAIR,YAAsB;EAAC;EAAAtB,aAAA,GAAAG,CAAA;EAC3B,QAAQ+D,IAAI;IACV,KAAK,OAAO;MAAA;MAAAlE,aAAA,GAAA8B,CAAA;IACZ,KAAK,QAAQ;MAAA;MAAA9B,aAAA,GAAA8B,CAAA;IACb,KAAK,UAAU;MAAA;MAAA9B,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MACbmB,YAAY,GAAGb,iBAAiB;MAAC;MAAAT,aAAA,GAAAG,CAAA;MACjC;IACF,KAAK,UAAU;MAAA;MAAAH,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MACbmB,YAAY,GAAGZ,oBAAoB;MAAC;MAAAV,aAAA,GAAAG,CAAA;MACpC;IACF,KAAK,OAAO;MAAA;MAAAH,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MACVmB,YAAY,GAAGX,iBAAiB;MAAC;MAAAX,aAAA,GAAAG,CAAA;MACjC;IACF;MAAA;MAAAH,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MACEmB,YAAY,GAAGb,iBAAiB;EACpC;EAAC;EAAAT,aAAA,GAAAG,CAAA;EAED,IAAI,CAACmB,YAAY,CAACM,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;IAAA;IAAA7B,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IACzC,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,qCAAqC7C,YAAY,CAACW,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC;EACzF,CAAC;EAAA;EAAA;IAAAjC,aAAA,GAAA8B,CAAA;EAAA;EAAA9B,aAAA,GAAAG,CAAA;EAED,OAAO,IAAI;AACb,CAAC;AAAC;AAAAH,aAAA,GAAAG,CAAA;AAlCWsC,OAAA,CAAAwB,oBAAoB,GAAAA,oBAAA;AAoCjC;AAAA;AAAAjE,aAAA,GAAAG,CAAA;AACO,MAAMoE,mBAAmB,GAAI7C,IAAyB,IAAI;EAAA;EAAA1B,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EAC/D,OAAO;IACLqE,YAAY,EAAE9C,IAAI,CAAC+C,YAAY;IAC/BC,QAAQ,EAAEhD,IAAI,CAACG,QAAQ;IACvBuC,IAAI,EAAE1C,IAAI,CAAC0C,IAAI;IACfO,QAAQ,EAAEjD,IAAI,CAACiD,QAAQ;IACvBxC,SAAS,EAAET,IAAI,CAACkD;GACjB;AACH,CAAC;AAAC;AAAA5E,aAAA,GAAAG,CAAA;AARWsC,OAAA,CAAA8B,mBAAmB,GAAAA,mBAAA;AAUhC;AAAA;AAAAvE,aAAA,GAAAG,CAAA;AACO,MAAM0E,qBAAqB,GAAGA,CAACtC,KAA4B,EAAE2B,IAAiD,KAAI;EAAA;EAAAlE,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EACvH;EAAI;EAAA,CAAAH,aAAA,GAAA8B,CAAA,YAACS,KAAK;EAAA;EAAA,CAAAvC,aAAA,GAAA8B,CAAA,WAAIS,KAAK,CAACuC,MAAM,KAAK,CAAC,GAAE;IAAA;IAAA9E,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IAChC,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC;EAC9C,CAAC;EAAA;EAAA;IAAAnE,aAAA,GAAA8B,CAAA;EAAA;EAED,MAAMiD,QAAQ;EAAA;EAAA,CAAA/E,aAAA,GAAAG,CAAA,QAAG+D,IAAI,KAAK,UAAU;EAAA;EAAA,CAAAlE,aAAA,GAAA8B,CAAA,WAAG,EAAE;EAAA;EAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAG,EAAE;EAAC;EAAA9B,aAAA,GAAAG,CAAA;EAC/C,IAAIoC,KAAK,CAACuC,MAAM,GAAGC,QAAQ,EAAE;IAAA;IAAA/E,aAAA,GAAA8B,CAAA;IAAA9B,aAAA,GAAAG,CAAA;IAC3B,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,2BAA2BY,QAAQ,gBAAgB,EAAE,GAAG,CAAC;EAC9E,CAAC;EAAA;EAAA;IAAA/E,aAAA,GAAA8B,CAAA;EAAA;EAAA9B,aAAA,GAAAG,CAAA;EAEDoC,KAAK,CAACyC,OAAO,CAAC,CAACtD,IAAI,EAAEuD,KAAK,KAAI;IAAA;IAAAjF,aAAA,GAAAwB,CAAA;IAAAxB,aAAA,GAAAG,CAAA;IAC5B,IAAI;MAAA;MAAAH,aAAA,GAAAG,CAAA;MACF,IAAAsC,OAAA,CAAAwB,oBAAoB,EAACvC,IAAI,EAAEwC,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOnC,KAAK,EAAE;MAAA;MAAA/B,aAAA,GAAAG,CAAA;MACd,MAAM,IAAIG,UAAA,CAAA6D,QAAQ,CAAC,QAAQc,KAAK,GAAG,CAAC,KAAMlD,KAAkB,CAAC0B,OAAO,EAAE,EAAE,GAAG,CAAC;IAC9E;EACF,CAAC,CAAC;EAAC;EAAAzD,aAAA,GAAAG,CAAA;EAEH,OAAO,IAAI;AACb,CAAC;AAAC;AAAAH,aAAA,GAAAG,CAAA;AAnBWsC,OAAA,CAAAoC,qBAAqB,GAAAA,qBAAA;AAqBlC;AAAA;AAAA7E,aAAA,GAAAG,CAAA;AACO,MAAM+E,oBAAoB,GAAG,MAAOxD,IAAyB,IAAsB;EAAA;EAAA1B,aAAA,GAAAwB,CAAA;EAAAxB,aAAA,GAAAG,CAAA;EACxF,IAAI;IACF,MAAMgF,cAAc;IAAA;IAAA,CAAAnF,aAAA,GAAAG,CAAA,SAAG,MAAM,IAAAK,qBAAA,CAAA4E,yBAAyB,EAAC1D,IAAI,CAAC;IAAC;IAAA1B,aAAA,GAAAG,CAAA;IAE7D,IAAI,CAACgF,cAAc,CAACE,QAAQ,EAAE;MAAA;MAAArF,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MAC5BI,QAAA,CAAAqD,MAAM,CAAC0B,IAAI,CAAC,gDAAgD,EAAE;QAC5DC,QAAQ,EAAE7D,IAAI,CAAC+C,YAAY;QAC3B5C,QAAQ,EAAEH,IAAI,CAACG,QAAQ;QACvBuC,IAAI,EAAE1C,IAAI,CAAC0C,IAAI;QACfoB,MAAM,EAAEL,cAAc,CAACK,MAAM;QAC7BC,QAAQ,EAAEN,cAAc,CAACM;OAC1B,CAAC;MAAC;MAAAzF,aAAA,GAAAG,CAAA;MACH,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAH,aAAA,GAAA8B,CAAA;IAAA;IAED;IAAA9B,aAAA,GAAAG,CAAA;IACA,IAAIgF,cAAc,CAACM,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA9E,aAAA,GAAA8B,CAAA;MAAA9B,aAAA,GAAAG,CAAA;MACtCI,QAAA,CAAAqD,MAAM,CAAC0B,IAAI,CAAC,yBAAyB,EAAE;QACrCC,QAAQ,EAAE7D,IAAI,CAAC+C,YAAY;QAC3BgB,QAAQ,EAAEN,cAAc,CAACM;OAC1B,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAzF,aAAA,GAAA8B,CAAA;IAAA;IAAA9B,aAAA,GAAAG,CAAA;IAED,OAAO,IAAI;EACb,CAAC,CAAC,OAAO4B,KAAK,EAAE;IAAA;IAAA/B,aAAA,GAAAG,CAAA;IACdI,QAAA,CAAAqD,MAAM,CAAC7B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAAC;IAAA/B,aAAA,GAAAG,CAAA;IAC7C,OAAO,KAAK;EACd;AACF,CAAC;AAAC;AAAAH,aAAA,GAAAG,CAAA;AA5BWsC,OAAA,CAAAyC,oBAAoB,GAAAA,oBAAA;AA4B/B;AAAAlF,aAAA,GAAAG,CAAA;AAEFsC,OAAA,CAAAtB,OAAA,GAAe;EACbuB,iBAAiB,EAAjBD,OAAA,CAAAC,iBAAiB;EACjBE,oBAAoB,EAApBH,OAAA,CAAAG,oBAAoB;EACpBE,YAAY,EAAZL,OAAA,CAAAK,YAAY;EACZC,oBAAoB,EAApBN,OAAA,CAAAM,oBAAoB;EACpBC,cAAc,EAAdP,OAAA,CAAAO,cAAc;EACdC,WAAW,EAAXR,OAAA,CAAAQ,WAAW;EACXC,iBAAiB,EAAjBT,OAAA,CAAAS,iBAAiB;EACjBG,iBAAiB,EAAjBZ,OAAA,CAAAY,iBAAiB;EACjBY,oBAAoB,EAApBxB,OAAA,CAAAwB,oBAAoB;EACpBY,qBAAqB,EAArBpC,OAAA,CAAAoC,qBAAqB;EACrBN,mBAAmB,EAAnB9B,OAAA,CAAA8B,mBAAmB;EACnBW,oBAAoB,EAApBzC,OAAA,CAAAyC;CACD", "ignoreList": []}