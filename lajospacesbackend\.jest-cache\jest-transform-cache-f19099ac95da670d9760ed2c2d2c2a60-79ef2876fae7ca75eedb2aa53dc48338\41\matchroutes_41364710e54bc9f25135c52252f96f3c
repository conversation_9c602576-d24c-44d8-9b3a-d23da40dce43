57af2bbc346bfa69f404c058dffd260a
"use strict";

/* istanbul ignore next */
function cov_1zz9lj91db() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\match.routes.ts";
  var hash = "c9395f3c06d9801de962f56cdf282bbb9b50bea6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\match.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 27
        },
        end: {
          line: 4,
          column: 69
        }
      },
      "3": {
        start: {
          line: 5,
          column: 38
        },
        end: {
          line: 5,
          column: 91
        }
      },
      "4": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 6,
          column: 44
        }
      },
      "5": {
        start: {
          line: 7,
          column: 21
        },
        end: {
          line: 7,
          column: 56
        }
      },
      "6": {
        start: {
          line: 8,
          column: 27
        },
        end: {
          line: 8,
          column: 68
        }
      },
      "7": {
        start: {
          line: 9,
          column: 15
        },
        end: {
          line: 9,
          column: 38
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 13,
          column: 3
        }
      },
      "9": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 87
        }
      },
      "10": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 32
        }
      },
      "11": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 21,
          column: 47
        }
      },
      "12": {
        start: {
          line: 27,
          column: 0
        },
        end: {
          line: 27,
          column: 133
        }
      },
      "13": {
        start: {
          line: 33,
          column: 0
        },
        end: {
          line: 33,
          column: 59
        }
      },
      "14": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 78
        }
      },
      "15": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 168
        }
      },
      "16": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 176
        }
      },
      "17": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 91
        }
      },
      "18": {
        start: {
          line: 63,
          column: 0
        },
        end: {
          line: 63,
          column: 88
        }
      },
      "19": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 69,
          column: 169
        }
      },
      "20": {
        start: {
          line: 75,
          column: 0
        },
        end: {
          line: 75,
          column: 174
        }
      },
      "21": {
        start: {
          line: 81,
          column: 0
        },
        end: {
          line: 81,
          column: 94
        }
      },
      "22": {
        start: {
          line: 82,
          column: 0
        },
        end: {
          line: 82,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 22
          },
          end: {
            line: 11,
            column: 23
          }
        },
        loc: {
          start: {
            line: 11,
            column: 37
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 11
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\match.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,sEAKyC;AACzC,4FAQoD;AACpD,6CAAkD;AAClD,yDAA6E;AAC7E,qEAKwC;AAExC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,wBAAwB;AACxB,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAClC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC;AAEH,gDAAgD;AAChD,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,GAAG,EACH,6BAAU,CACX,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,QAAQ,EACR,IAAA,4BAAe,EAAC,mCAAgB,EAAE,MAAM,CAAC,EACzC,6BAAU,CACX,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,UAAU,EACV,kCAAe,CAChB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,cAAc,EACd,iDAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,cAAc,EACd,IAAA,4BAAe,EAAC,0CAAuB,EAAE,MAAM,CAAC,EAChD,oDAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,qBAAqB,EACrB,IAAA,4BAAe,EAAC,0CAAuB,EAAE,MAAM,CAAC,EAChD,oDAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,uBAAuB,EACvB,qDAAuB,CACxB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,sBAAsB,EACtB,mDAAqB,CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,4BAA4B,EAC5B,IAAA,4BAAe,EAAC,oCAAiB,EAAE,MAAM,CAAC,EAC1C,4CAAc,CACf,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,4BAA4B,EAC5B,IAAA,4BAAe,EAAC,oCAAiB,EAAE,MAAM,CAAC,EAC1C,+CAAiB,CAClB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,MAAM,EACN,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,+BAAY,CACb,CAAC;AAEF,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\match.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  getMatches,\r\n  swipeMatch,\r\n  getMatchHistory,\r\n  getMatchById\r\n} from '../controllers/match.controller';\r\nimport {\r\n  getMatchPreferences,\r\n  updateMatchPreferences,\r\n  toggleMatchPreferences,\r\n  updatePreferenceSection,\r\n  addDealBreaker,\r\n  removeDealBreaker,\r\n  getPreferencesSummary\r\n} from '../controllers/matchPreferences.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  swipeMatchSchema,\r\n  updatePreferencesSchema,\r\n  togglePreferencesSchema,\r\n  dealBreakerSchema\r\n} from '../validators/match.validators';\r\n\r\nconst router = Router();\r\n\r\n// Health check (public)\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ message: 'Match routes working', timestamp: new Date().toISOString() });\r\n});\r\n\r\n// All other match routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   GET /api/matches\r\n * @desc    Get potential matches for the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  getMatches\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/swipe\r\n * @desc    Swipe on a match (like, pass, super like)\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/swipe',\r\n  validateRequest(swipeMatchSchema, 'body'),\r\n  swipeMatch\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/history\r\n * @desc    Get user's match history\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/history',\r\n  getMatchHistory\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/preferences\r\n * @desc    Get user's match preferences\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/preferences',\r\n  getMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/matches/preferences\r\n * @desc    Update user's match preferences\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/preferences',\r\n  validateRequest(updatePreferencesSchema, 'body'),\r\n  updateMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/preferences/toggle\r\n * @desc    Toggle match preferences active status\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preferences/toggle',\r\n  validateRequest(togglePreferencesSchema, 'body'),\r\n  toggleMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/matches/preferences/:section\r\n * @desc    Update specific preference section\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/preferences/:section',\r\n  updatePreferenceSection\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/preferences/summary\r\n * @desc    Get match preferences summary/stats\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/preferences/summary',\r\n  getPreferencesSummary\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/preferences/deal-breakers\r\n * @desc    Add a deal breaker\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preferences/deal-breakers',\r\n  validateRequest(dealBreakerSchema, 'body'),\r\n  addDealBreaker\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/matches/preferences/deal-breakers\r\n * @desc    Remove a deal breaker\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/preferences/deal-breakers',\r\n  validateRequest(dealBreakerSchema, 'body'),\r\n  removeDealBreaker\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/:id\r\n * @desc    Get a specific match by ID\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getMatchById\r\n);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c9395f3c06d9801de962f56cdf282bbb9b50bea6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zz9lj91db = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zz9lj91db();
cov_1zz9lj91db().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[1]++, require("express"));
const match_controller_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[2]++, require("../controllers/match.controller"));
const matchPreferences_controller_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[3]++, require("../controllers/matchPreferences.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[4]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[5]++, require("../middleware/validation"));
const match_validators_1 =
/* istanbul ignore next */
(cov_1zz9lj91db().s[6]++, require("../validators/match.validators"));
const router =
/* istanbul ignore next */
(cov_1zz9lj91db().s[7]++, (0, express_1.Router)());
// Health check (public)
/* istanbul ignore next */
cov_1zz9lj91db().s[8]++;
router.get('/health', (_req, res) => {
  /* istanbul ignore next */
  cov_1zz9lj91db().f[0]++;
  cov_1zz9lj91db().s[9]++;
  res.json({
    message: 'Match routes working',
    timestamp: new Date().toISOString()
  });
});
// All other match routes require authentication
/* istanbul ignore next */
cov_1zz9lj91db().s[10]++;
router.use(auth_1.authenticate);
/**
 * @route   GET /api/matches
 * @desc    Get potential matches for the authenticated user
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[11]++;
router.get('/', match_controller_1.getMatches);
/**
 * @route   POST /api/matches/swipe
 * @desc    Swipe on a match (like, pass, super like)
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[12]++;
router.post('/swipe', (0, validation_1.validateRequest)(match_validators_1.swipeMatchSchema, 'body'), match_controller_1.swipeMatch);
/**
 * @route   GET /api/matches/history
 * @desc    Get user's match history
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[13]++;
router.get('/history', match_controller_1.getMatchHistory);
/**
 * @route   GET /api/matches/preferences
 * @desc    Get user's match preferences
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[14]++;
router.get('/preferences', matchPreferences_controller_1.getMatchPreferences);
/**
 * @route   PUT /api/matches/preferences
 * @desc    Update user's match preferences
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[15]++;
router.put('/preferences', (0, validation_1.validateRequest)(match_validators_1.updatePreferencesSchema, 'body'), matchPreferences_controller_1.updateMatchPreferences);
/**
 * @route   POST /api/matches/preferences/toggle
 * @desc    Toggle match preferences active status
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[16]++;
router.post('/preferences/toggle', (0, validation_1.validateRequest)(match_validators_1.togglePreferencesSchema, 'body'), matchPreferences_controller_1.toggleMatchPreferences);
/**
 * @route   PUT /api/matches/preferences/:section
 * @desc    Update specific preference section
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[17]++;
router.put('/preferences/:section', matchPreferences_controller_1.updatePreferenceSection);
/**
 * @route   GET /api/matches/preferences/summary
 * @desc    Get match preferences summary/stats
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[18]++;
router.get('/preferences/summary', matchPreferences_controller_1.getPreferencesSummary);
/**
 * @route   POST /api/matches/preferences/deal-breakers
 * @desc    Add a deal breaker
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[19]++;
router.post('/preferences/deal-breakers', (0, validation_1.validateRequest)(match_validators_1.dealBreakerSchema, 'body'), matchPreferences_controller_1.addDealBreaker);
/**
 * @route   DELETE /api/matches/preferences/deal-breakers
 * @desc    Remove a deal breaker
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[20]++;
router.delete('/preferences/deal-breakers', (0, validation_1.validateRequest)(match_validators_1.dealBreakerSchema, 'body'), matchPreferences_controller_1.removeDealBreaker);
/**
 * @route   GET /api/matches/:id
 * @desc    Get a specific match by ID
 * @access  Private
 */
/* istanbul ignore next */
cov_1zz9lj91db().s[21]++;
router.get('/:id', (0, validation_1.validateObjectId)('id'), match_controller_1.getMatchById);
/* istanbul ignore next */
cov_1zz9lj91db().s[22]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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