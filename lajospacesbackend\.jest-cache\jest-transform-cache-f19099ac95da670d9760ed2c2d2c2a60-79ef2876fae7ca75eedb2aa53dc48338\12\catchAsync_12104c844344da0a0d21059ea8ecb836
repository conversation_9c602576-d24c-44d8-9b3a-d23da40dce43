9d430ac8edb7d947f045b0adb6c0a3a1
"use strict";

/* istanbul ignore next */
function cov_2y8j6ybmi() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\catchAsync.ts";
  var hash = "b11d9c909801de6b44614e37b6d4bb00723be56f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\catchAsync.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 28
        }
      },
      "2": {
        start: {
          line: 7,
          column: 19
        },
        end: {
          line: 11,
          column: 1
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "4": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 56
        }
      },
      "5": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 32
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 19
          },
          end: {
            line: 7,
            column: 20
          }
        },
        loc: {
          start: {
            line: 7,
            column: 27
          },
          end: {
            line: 11,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 8,
            column: 11
          },
          end: {
            line: 8,
            column: 12
          }
        },
        loc: {
          start: {
            line: 8,
            column: 31
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 8
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\catchAsync.ts",
      mappings: ";;;AAEA;;GAEG;AACI,MAAM,UAAU,GAAG,CAAC,EAAY,EAAE,EAAE;IACzC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,UAAU,cAIrB",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\catchAsync.ts"],
      sourcesContent: ["import { Request, Response, NextFunction } from 'express';\r\n\r\n/**\r\n * Wrapper function to catch async errors and pass them to error handling middleware\r\n */\r\nexport const catchAsync = (fn: Function) => {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    Promise.resolve(fn(req, res, next)).catch(next);\r\n  };\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b11d9c909801de6b44614e37b6d4bb00723be56f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2y8j6ybmi = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2y8j6ybmi();
cov_2y8j6ybmi().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2y8j6ybmi().s[1]++;
exports.catchAsync = void 0;
/**
 * Wrapper function to catch async errors and pass them to error handling middleware
 */
/* istanbul ignore next */
cov_2y8j6ybmi().s[2]++;
const catchAsync = fn => {
  /* istanbul ignore next */
  cov_2y8j6ybmi().f[0]++;
  cov_2y8j6ybmi().s[3]++;
  return (req, res, next) => {
    /* istanbul ignore next */
    cov_2y8j6ybmi().f[1]++;
    cov_2y8j6ybmi().s[4]++;
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
/* istanbul ignore next */
cov_2y8j6ybmi().s[5]++;
exports.catchAsync = catchAsync;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMnk4ajZ5Ym1pIiwicyIsImNhdGNoQXN5bmMiLCJmbiIsImYiLCJyZXEiLCJyZXMiLCJuZXh0IiwiUHJvbWlzZSIsInJlc29sdmUiLCJjYXRjaCIsImV4cG9ydHMiXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXE1ZIFBDXFxEZXNrdG9wXFxsYWpvc3BhY2VzXFxsYWpvc3BhY2VzYmFja2VuZFxcc3JjXFx1dGlsc1xcY2F0Y2hBc3luYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZXF1ZXN0LCBSZXNwb25zZSwgTmV4dEZ1bmN0aW9uIH0gZnJvbSAnZXhwcmVzcyc7XHJcblxyXG4vKipcclxuICogV3JhcHBlciBmdW5jdGlvbiB0byBjYXRjaCBhc3luYyBlcnJvcnMgYW5kIHBhc3MgdGhlbSB0byBlcnJvciBoYW5kbGluZyBtaWRkbGV3YXJlXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgY2F0Y2hBc3luYyA9IChmbjogRnVuY3Rpb24pID0+IHtcclxuICByZXR1cm4gKHJlcTogUmVxdWVzdCwgcmVzOiBSZXNwb25zZSwgbmV4dDogTmV4dEZ1bmN0aW9uKSA9PiB7XHJcbiAgICBQcm9taXNlLnJlc29sdmUoZm4ocmVxLCByZXMsIG5leHQpKS5jYXRjaChuZXh0KTtcclxuICB9O1xyXG59O1xyXG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVBOzs7QUFBQTtBQUFBQSxhQUFBLEdBQUFDLENBQUE7QUFHTyxNQUFNQyxVQUFVLEdBQUlDLEVBQVksSUFBSTtFQUFBO0VBQUFILGFBQUEsR0FBQUksQ0FBQTtFQUFBSixhQUFBLEdBQUFDLENBQUE7RUFDekMsT0FBTyxDQUFDSSxHQUFZLEVBQUVDLEdBQWEsRUFBRUMsSUFBa0IsS0FBSTtJQUFBO0lBQUFQLGFBQUEsR0FBQUksQ0FBQTtJQUFBSixhQUFBLEdBQUFDLENBQUE7SUFDekRPLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDTixFQUFFLENBQUNFLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxJQUFJLENBQUMsQ0FBQyxDQUFDRyxLQUFLLENBQUNILElBQUksQ0FBQztFQUNqRCxDQUFDO0FBQ0gsQ0FBQztBQUFDO0FBQUFQLGFBQUEsR0FBQUMsQ0FBQTtBQUpXVSxPQUFBLENBQUFULFVBQVUsR0FBQUEsVUFBQSIsImlnbm9yZUxpc3QiOltdfQ==