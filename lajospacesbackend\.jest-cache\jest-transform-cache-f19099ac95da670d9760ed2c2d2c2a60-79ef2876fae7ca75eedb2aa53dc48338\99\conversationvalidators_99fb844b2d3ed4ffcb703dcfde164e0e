eaa97309e1633730005735ce063947e2
"use strict";

/* istanbul ignore next */
function cov_1w0todfos3() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\conversation.validators.ts";
  var hash = "f23395eaede383f5a7a82a4be9211e904a59098b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\conversation.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 440
        }
      },
      "4": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 45
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "6": {
        start: {
          line: 65,
          column: 0
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "7": {
        start: {
          line: 101,
          column: 0
        },
        end: {
          line: 140,
          column: 3
        }
      },
      "8": {
        start: {
          line: 144,
          column: 0
        },
        end: {
          line: 155,
          column: 3
        }
      },
      "9": {
        start: {
          line: 159,
          column: 0
        },
        end: {
          line: 163,
          column: 3
        }
      },
      "10": {
        start: {
          line: 167,
          column: 0
        },
        end: {
          line: 175,
          column: 3
        }
      },
      "11": {
        start: {
          line: 179,
          column: 0
        },
        end: {
          line: 186,
          column: 3
        }
      },
      "12": {
        start: {
          line: 190,
          column: 0
        },
        end: {
          line: 210,
          column: 3
        }
      },
      "13": {
        start: {
          line: 214,
          column: 0
        },
        end: {
          line: 242,
          column: 3
        }
      },
      "14": {
        start: {
          line: 246,
          column: 0
        },
        end: {
          line: 283,
          column: 3
        }
      },
      "15": {
        start: {
          line: 287,
          column: 0
        },
        end: {
          line: 302,
          column: 3
        }
      },
      "16": {
        start: {
          line: 306,
          column: 0
        },
        end: {
          line: 317,
          column: 3
        }
      },
      "17": {
        start: {
          line: 321,
          column: 0
        },
        end: {
          line: 329,
          column: 3
        }
      },
      "18": {
        start: {
          line: 333,
          column: 0
        },
        end: {
          line: 348,
          column: 3
        }
      },
      "19": {
        start: {
          line: 349,
          column: 0
        },
        end: {
          line: 364,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\conversation.validators.ts",
      mappings: ";;;;;;AAAA,8CAAsB;AAEtB;;GAEG;AACU,QAAA,wBAAwB,GAAG,aAAG,CAAC,MAAM,CAAC;IACjD,cAAc,EAAE,aAAG,CAAC,KAAK,EAAE;SACxB,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SAChD,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,sCAAsC;QACnD,WAAW,EAAE,iCAAiC;QAC9C,qBAAqB,EAAE,+BAA+B;KACvD,CAAC;IAEJ,gBAAgB,EAAE,aAAG,CAAC,MAAM,EAAE;SAC3B,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;SACnC,OAAO,CAAC,QAAQ,CAAC;SACjB,QAAQ,CAAC;QACR,UAAU,EAAE,qDAAqD;KAClE,CAAC;IAEJ,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,IAAI,CAAC,kBAAkB,EAAE;QACxB,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,aAAG,CAAC,QAAQ,EAAE;QACpB,SAAS,EAAE,aAAG,CAAC,QAAQ,EAAE;KAC1B,CAAC;SACD,QAAQ,CAAC;QACR,YAAY,EAAE,oCAAoC;QAClD,YAAY,EAAE,oCAAoC;QAClD,cAAc,EAAE,2CAA2C;KAC5D,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;KACzD,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,yBAAyB;KACjD,CAAC;IAEJ,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE;SACrB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,4BAA4B;KACpD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,wBAAwB,GAAG,aAAG,CAAC,MAAM,CAAC;IACjD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,oCAAoC;QAClD,YAAY,EAAE,oCAAoC;KACnD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,0CAA0C;KACzD,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,GAAG,EAAE;SACL,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4BAA4B;KAC3C,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,gBAAgB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC1C,oBAAoB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC9C,oBAAoB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC9C,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QACvD,kBAAkB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC5C,mBAAmB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC5D,4BAA4B,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;KACvD,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC1C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,6BAA6B;QAC7C,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,uCAAuC;KACtD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,QAAQ,CAAC;SACtE,OAAO,CAAC,MAAM,CAAC;SACf,QAAQ,CAAC;QACR,UAAU,EAAE,sBAAsB;KACnC,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QACjD,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,YAAY;QAC7E,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QACjD,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACvC,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QAC3C,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;YACnB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;YACrD,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;SACjD,CAAC,CAAC,QAAQ,EAAE;QACb,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAAQ,EAAE;QAChE,iBAAiB,EAAE,aAAG,CAAC,MAAM,EAAE;aAC5B,KAAK,CAAC,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,CAAC;aACnE,QAAQ,EAAE;KACd,CAAC,CAAC,QAAQ,EAAE;IAEb,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,oCAAoC;KAC5D,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC1C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,6BAA6B;QAC7C,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,uCAAuC;KACtD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,iBAAiB,EAAE,aAAG,CAAC,OAAO,EAAE;SAC7B,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;SACrD,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;QACnC,cAAc,EAAE,sBAAsB;KACvC,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,aAAG,CAAC,MAAM,CAAC;IACzC,UAAU,EAAE,aAAG,CAAC,KAAK,EAAE;SACpB,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SAChD,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,2BAA2B;KACnD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1D,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC;SACxD,OAAO,CAAC,QAAQ,CAAC;SACjB,QAAQ,EAAE;IACb,gBAAgB,EAAE,aAAG,CAAC,MAAM,EAAE;SAC3B,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;SAC1C,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IACb,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4CAA4C;QAC1D,YAAY,EAAE,2CAA2C;KAC1D,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC3C,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC1D,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,gDAAgD;KACxE,CAAC;IACJ,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,+CAA+C;KACvE,CAAC;IACJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,CAAC;SAC7E,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IACb,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4CAA4C;QAC1D,YAAY,EAAE,2CAA2C;KAC1D,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,0BAA0B;QAC1C,YAAY,EAAE,4CAA4C;QAC1D,YAAY,EAAE,2CAA2C;KAC1D,CAAC;IAEJ,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE;SACzB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,gCAAgC;KACxD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,CAAC;SAC7E,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IAEb,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC/C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAEzD,QAAQ,EAAE,aAAG,CAAC,IAAI,EAAE;SACjB,GAAG,EAAE;SACL,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,aAAa,EAAE,iCAAiC;KACjD,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,IAAI,EAAE;SACf,GAAG,EAAE;SACL,GAAG,CAAC,aAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACxB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,aAAa,EAAE,+BAA+B;QAC9C,UAAU,EAAE,iCAAiC;KAC9C,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE;SACnB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,yBAAyB;QACzC,cAAc,EAAE,+BAA+B;KAChD,CAAC;IAEJ,UAAU,EAAE,aAAG,CAAC,IAAI,EAAE;SACnB,GAAG,EAAE;SACL,GAAG,CAAC,KAAK,CAAC;SACV,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,aAAa,EAAE,mCAAmC;QAClD,UAAU,EAAE,mCAAmC;KAChD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,cAAc,EAAE,aAAG,CAAC,KAAK,EAAE;SACxB,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SAChD,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,EAAE,CAAC;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,sCAAsC;QACnD,WAAW,EAAE,8CAA8C;QAC3D,qBAAqB,EAAE,+BAA+B;KACvD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,aAAa,EAAE,aAAG,CAAC,MAAM,EAAE;SACxB,OAAO,CAAC,mBAAmB,CAAC;SAC5B,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,qBAAqB,EAAE,+BAA+B;QACtD,cAAc,EAAE,4BAA4B;KAC7C,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,aAAG,CAAC,MAAM,CAAC;IACzC,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;SACtB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,oCAAoC;QAChD,cAAc,EAAE,0BAA0B;KAC3C,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,sCAAsC;KACrD,CAAC;CACL,CAAC,CAAC;AAEH,kBAAe;IACb,wBAAwB,EAAxB,gCAAwB;IACxB,wBAAwB,EAAxB,gCAAwB;IACxB,iBAAiB,EAAjB,yBAAiB;IACjB,iBAAiB,EAAjB,yBAAiB;IACjB,mBAAmB,EAAnB,2BAAmB;IACnB,oBAAoB,EAApB,4BAAoB;IACpB,gBAAgB,EAAhB,wBAAgB;IAChB,uBAAuB,EAAvB,+BAAuB;IACvB,kBAAkB,EAAlB,0BAAkB;IAClB,oBAAoB,EAApB,4BAAoB;IACpB,sBAAsB,EAAtB,8BAAsB;IACtB,oBAAoB,EAApB,4BAAoB;IACpB,uBAAuB,EAAvB,+BAAuB;IACvB,gBAAgB,EAAhB,wBAAgB;CACjB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\conversation.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n/**\r\n * Create conversation validation schema\r\n */\r\nexport const createConversationSchema = Joi.object({\r\n  participantIds: Joi.array()\r\n    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))\r\n    .min(1)\r\n    .max(49)\r\n    .required()\r\n    .messages({\r\n      'array.min': 'At least one participant is required',\r\n      'array.max': 'Maximum 49 participants allowed',\r\n      'string.pattern.base': 'Invalid participant ID format'\r\n    }),\r\n  \r\n  conversationType: Joi.string()\r\n    .valid('direct', 'group', 'support')\r\n    .default('direct')\r\n    .messages({\r\n      'any.only': 'Conversation type must be direct, group, or support'\r\n    }),\r\n  \r\n  title: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .when('conversationType', {\r\n      is: 'group',\r\n      then: Joi.required(),\r\n      otherwise: Joi.optional()\r\n    })\r\n    .messages({\r\n      'string.min': 'Title must be at least 1 character',\r\n      'string.max': 'Title cannot exceed 100 characters',\r\n      'any.required': 'Title is required for group conversations'\r\n    }),\r\n  \r\n  description: Joi.string()\r\n    .trim()\r\n    .max(500)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Description cannot exceed 500 characters'\r\n    }),\r\n  \r\n  matchId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid match ID format'\r\n    }),\r\n  \r\n  propertyId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid property ID format'\r\n    })\r\n});\r\n\r\n/**\r\n * Update conversation validation schema\r\n */\r\nexport const updateConversationSchema = Joi.object({\r\n  title: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Title must be at least 1 character',\r\n      'string.max': 'Title cannot exceed 100 characters'\r\n    }),\r\n  \r\n  description: Joi.string()\r\n    .trim()\r\n    .max(500)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Description cannot exceed 500 characters'\r\n    }),\r\n  \r\n  avatar: Joi.string()\r\n    .uri()\r\n    .optional()\r\n    .messages({\r\n      'string.uri': 'Avatar must be a valid URL'\r\n    }),\r\n  \r\n  settings: Joi.object({\r\n    allowFileSharing: Joi.boolean().optional(),\r\n    allowLocationSharing: Joi.boolean().optional(),\r\n    allowPropertySharing: Joi.boolean().optional(),\r\n    maxParticipants: Joi.number().min(2).max(50).optional(),\r\n    autoDeleteMessages: Joi.boolean().optional(),\r\n    autoDeleteAfterDays: Joi.number().min(1).max(365).optional(),\r\n    requireApprovalForNewMembers: Joi.boolean().optional()\r\n  }).optional()\r\n});\r\n\r\n/**\r\n * Send message validation schema\r\n */\r\nexport const sendMessageSchema = Joi.object({\r\n  content: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(5000)\r\n    .required()\r\n    .messages({\r\n      'string.empty': 'Message content is required',\r\n      'string.min': 'Message must be at least 1 character',\r\n      'string.max': 'Message cannot exceed 5000 characters'\r\n    }),\r\n  \r\n  messageType: Joi.string()\r\n    .valid('text', 'image', 'file', 'location', 'property_share', 'system')\r\n    .default('text')\r\n    .messages({\r\n      'any.only': 'Invalid message type'\r\n    }),\r\n  \r\n  metadata: Joi.object({\r\n    fileName: Joi.string().trim().max(255).optional(),\r\n    fileSize: Joi.number().min(0).max(100 * 1024 * 1024).optional(), // 100MB max\r\n    fileType: Joi.string().trim().max(100).optional(),\r\n    imageUrl: Joi.string().uri().optional(),\r\n    thumbnailUrl: Joi.string().uri().optional(),\r\n    location: Joi.object({\r\n      latitude: Joi.number().min(-90).max(90).required(),\r\n      longitude: Joi.number().min(-180).max(180).required(),\r\n      address: Joi.string().trim().max(500).optional()\r\n    }).optional(),\r\n    propertyId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),\r\n    systemMessageType: Joi.string()\r\n      .valid('match_created', 'match_expired', 'user_joined', 'user_left')\r\n      .optional()\r\n  }).optional(),\r\n  \r\n  replyTo: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid reply-to message ID format'\r\n    })\r\n});\r\n\r\n/**\r\n * Edit message validation schema\r\n */\r\nexport const editMessageSchema = Joi.object({\r\n  content: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(5000)\r\n    .required()\r\n    .messages({\r\n      'string.empty': 'Message content is required',\r\n      'string.min': 'Message must be at least 1 character',\r\n      'string.max': 'Message cannot exceed 5000 characters'\r\n    })\r\n});\r\n\r\n/**\r\n * Delete message validation schema\r\n */\r\nexport const deleteMessageSchema = Joi.object({\r\n  deleteForEveryone: Joi.boolean()\r\n    .default(false)\r\n    .optional()\r\n});\r\n\r\n/**\r\n * React to message validation schema\r\n */\r\nexport const reactToMessageSchema = Joi.object({\r\n  reaction: Joi.string()\r\n    .valid('like', 'love', 'laugh', 'wow', 'sad', 'angry')\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Invalid reaction type',\r\n      'any.required': 'Reaction is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Mark messages as read validation schema\r\n */\r\nexport const markAsReadSchema = Joi.object({\r\n  messageIds: Joi.array()\r\n    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid message ID format'\r\n    })\r\n});\r\n\r\n/**\r\n * Conversation query validation schema\r\n */\r\nexport const conversationQuerySchema = Joi.object({\r\n  page: Joi.number().min(1).default(1).optional(),\r\n  limit: Joi.number().min(1).max(100).default(20).optional(),\r\n  status: Joi.string()\r\n    .valid('active', 'archived', 'blocked', 'deleted', 'all')\r\n    .default('active')\r\n    .optional(),\r\n  conversationType: Joi.string()\r\n    .valid('direct', 'group', 'support', 'all')\r\n    .default('all')\r\n    .optional(),\r\n  search: Joi.string()\r\n    .trim()\r\n    .min(2)\r\n    .max(100)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Search query must be at least 2 characters',\r\n      'string.max': 'Search query cannot exceed 100 characters'\r\n    })\r\n});\r\n\r\n/**\r\n * Message query validation schema\r\n */\r\nexport const messageQuerySchema = Joi.object({\r\n  page: Joi.number().min(1).default(1).optional(),\r\n  limit: Joi.number().min(1).max(100).default(50).optional(),\r\n  before: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid message ID format for before parameter'\r\n    }),\r\n  after: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid message ID format for after parameter'\r\n    }),\r\n  messageType: Joi.string()\r\n    .valid('text', 'image', 'file', 'location', 'property_share', 'system', 'all')\r\n    .default('all')\r\n    .optional(),\r\n  search: Joi.string()\r\n    .trim()\r\n    .min(2)\r\n    .max(100)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Search query must be at least 2 characters',\r\n      'string.max': 'Search query cannot exceed 100 characters'\r\n    })\r\n});\r\n\r\n/**\r\n * Search messages validation schema\r\n */\r\nexport const searchMessagesSchema = Joi.object({\r\n  query: Joi.string()\r\n    .trim()\r\n    .min(2)\r\n    .max(100)\r\n    .required()\r\n    .messages({\r\n      'string.empty': 'Search query is required',\r\n      'string.min': 'Search query must be at least 2 characters',\r\n      'string.max': 'Search query cannot exceed 100 characters'\r\n    }),\r\n  \r\n  conversationId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .optional()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid conversation ID format'\r\n    }),\r\n  \r\n  messageType: Joi.string()\r\n    .valid('text', 'image', 'file', 'location', 'property_share', 'system', 'all')\r\n    .default('all')\r\n    .optional(),\r\n  \r\n  page: Joi.number().min(1).default(1).optional(),\r\n  limit: Joi.number().min(1).max(50).default(20).optional(),\r\n  \r\n  dateFrom: Joi.date()\r\n    .iso()\r\n    .optional()\r\n    .messages({\r\n      'date.format': 'Date from must be in ISO format'\r\n    }),\r\n  \r\n  dateTo: Joi.date()\r\n    .iso()\r\n    .min(Joi.ref('dateFrom'))\r\n    .optional()\r\n    .messages({\r\n      'date.format': 'Date to must be in ISO format',\r\n      'date.min': 'Date to must be after date from'\r\n    })\r\n});\r\n\r\n/**\r\n * Mute conversation validation schema\r\n */\r\nexport const muteConversationSchema = Joi.object({\r\n  isMuted: Joi.boolean()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Mute status is required',\r\n      'boolean.base': 'Mute status must be a boolean'\r\n    }),\r\n  \r\n  mutedUntil: Joi.date()\r\n    .iso()\r\n    .min('now')\r\n    .optional()\r\n    .messages({\r\n      'date.format': 'Muted until must be in ISO format',\r\n      'date.min': 'Muted until must be in the future'\r\n    })\r\n});\r\n\r\n/**\r\n * Add participant validation schema\r\n */\r\nexport const addParticipantSchema = Joi.object({\r\n  participantIds: Joi.array()\r\n    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))\r\n    .min(1)\r\n    .max(10)\r\n    .required()\r\n    .messages({\r\n      'array.min': 'At least one participant is required',\r\n      'array.max': 'Maximum 10 participants can be added at once',\r\n      'string.pattern.base': 'Invalid participant ID format'\r\n    })\r\n});\r\n\r\n/**\r\n * Remove participant validation schema\r\n */\r\nexport const removeParticipantSchema = Joi.object({\r\n  participantId: Joi.string()\r\n    .pattern(/^[0-9a-fA-F]{24}$/)\r\n    .required()\r\n    .messages({\r\n      'string.pattern.base': 'Invalid participant ID format',\r\n      'any.required': 'Participant ID is required'\r\n    })\r\n});\r\n\r\n/**\r\n * File upload validation schema\r\n */\r\nexport const fileUploadSchema = Joi.object({\r\n  messageType: Joi.string()\r\n    .valid('image', 'file')\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Message type must be image or file',\r\n      'any.required': 'Message type is required'\r\n    }),\r\n  \r\n  caption: Joi.string()\r\n    .trim()\r\n    .max(500)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Caption cannot exceed 500 characters'\r\n    })\r\n});\r\n\r\nexport default {\r\n  createConversationSchema,\r\n  updateConversationSchema,\r\n  sendMessageSchema,\r\n  editMessageSchema,\r\n  deleteMessageSchema,\r\n  reactToMessageSchema,\r\n  markAsReadSchema,\r\n  conversationQuerySchema,\r\n  messageQuerySchema,\r\n  searchMessagesSchema,\r\n  muteConversationSchema,\r\n  addParticipantSchema,\r\n  removeParticipantSchema,\r\n  fileUploadSchema\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f23395eaede383f5a7a82a4be9211e904a59098b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1w0todfos3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1w0todfos3();
var __importDefault =
/* istanbul ignore next */
(cov_1w0todfos3().s[0]++,
/* istanbul ignore next */
(cov_1w0todfos3().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1w0todfos3().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1w0todfos3().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1w0todfos3().f[0]++;
  cov_1w0todfos3().s[1]++;
  return /* istanbul ignore next */(cov_1w0todfos3().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1w0todfos3().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1w0todfos3().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1w0todfos3().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1w0todfos3().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1w0todfos3().s[3]++;
exports.fileUploadSchema = exports.removeParticipantSchema = exports.addParticipantSchema = exports.muteConversationSchema = exports.searchMessagesSchema = exports.messageQuerySchema = exports.conversationQuerySchema = exports.markAsReadSchema = exports.reactToMessageSchema = exports.deleteMessageSchema = exports.editMessageSchema = exports.sendMessageSchema = exports.updateConversationSchema = exports.createConversationSchema = void 0;
const joi_1 =
/* istanbul ignore next */
(cov_1w0todfos3().s[4]++, __importDefault(require("joi")));
/**
 * Create conversation validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[5]++;
exports.createConversationSchema = joi_1.default.object({
  participantIds: joi_1.default.array().items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/)).min(1).max(49).required().messages({
    'array.min': 'At least one participant is required',
    'array.max': 'Maximum 49 participants allowed',
    'string.pattern.base': 'Invalid participant ID format'
  }),
  conversationType: joi_1.default.string().valid('direct', 'group', 'support').default('direct').messages({
    'any.only': 'Conversation type must be direct, group, or support'
  }),
  title: joi_1.default.string().trim().min(1).max(100).when('conversationType', {
    is: 'group',
    then: joi_1.default.required(),
    otherwise: joi_1.default.optional()
  }).messages({
    'string.min': 'Title must be at least 1 character',
    'string.max': 'Title cannot exceed 100 characters',
    'any.required': 'Title is required for group conversations'
  }),
  description: joi_1.default.string().trim().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters'
  }),
  matchId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid match ID format'
  }),
  propertyId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid property ID format'
  })
});
/**
 * Update conversation validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[6]++;
exports.updateConversationSchema = joi_1.default.object({
  title: joi_1.default.string().trim().min(1).max(100).optional().messages({
    'string.min': 'Title must be at least 1 character',
    'string.max': 'Title cannot exceed 100 characters'
  }),
  description: joi_1.default.string().trim().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters'
  }),
  avatar: joi_1.default.string().uri().optional().messages({
    'string.uri': 'Avatar must be a valid URL'
  }),
  settings: joi_1.default.object({
    allowFileSharing: joi_1.default.boolean().optional(),
    allowLocationSharing: joi_1.default.boolean().optional(),
    allowPropertySharing: joi_1.default.boolean().optional(),
    maxParticipants: joi_1.default.number().min(2).max(50).optional(),
    autoDeleteMessages: joi_1.default.boolean().optional(),
    autoDeleteAfterDays: joi_1.default.number().min(1).max(365).optional(),
    requireApprovalForNewMembers: joi_1.default.boolean().optional()
  }).optional()
});
/**
 * Send message validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[7]++;
exports.sendMessageSchema = joi_1.default.object({
  content: joi_1.default.string().trim().min(1).max(5000).required().messages({
    'string.empty': 'Message content is required',
    'string.min': 'Message must be at least 1 character',
    'string.max': 'Message cannot exceed 5000 characters'
  }),
  messageType: joi_1.default.string().valid('text', 'image', 'file', 'location', 'property_share', 'system').default('text').messages({
    'any.only': 'Invalid message type'
  }),
  metadata: joi_1.default.object({
    fileName: joi_1.default.string().trim().max(255).optional(),
    fileSize: joi_1.default.number().min(0).max(100 * 1024 * 1024).optional(),
    // 100MB max
    fileType: joi_1.default.string().trim().max(100).optional(),
    imageUrl: joi_1.default.string().uri().optional(),
    thumbnailUrl: joi_1.default.string().uri().optional(),
    location: joi_1.default.object({
      latitude: joi_1.default.number().min(-90).max(90).required(),
      longitude: joi_1.default.number().min(-180).max(180).required(),
      address: joi_1.default.string().trim().max(500).optional()
    }).optional(),
    propertyId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
    systemMessageType: joi_1.default.string().valid('match_created', 'match_expired', 'user_joined', 'user_left').optional()
  }).optional(),
  replyTo: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid reply-to message ID format'
  })
});
/**
 * Edit message validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[8]++;
exports.editMessageSchema = joi_1.default.object({
  content: joi_1.default.string().trim().min(1).max(5000).required().messages({
    'string.empty': 'Message content is required',
    'string.min': 'Message must be at least 1 character',
    'string.max': 'Message cannot exceed 5000 characters'
  })
});
/**
 * Delete message validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[9]++;
exports.deleteMessageSchema = joi_1.default.object({
  deleteForEveryone: joi_1.default.boolean().default(false).optional()
});
/**
 * React to message validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[10]++;
exports.reactToMessageSchema = joi_1.default.object({
  reaction: joi_1.default.string().valid('like', 'love', 'laugh', 'wow', 'sad', 'angry').required().messages({
    'any.only': 'Invalid reaction type',
    'any.required': 'Reaction is required'
  })
});
/**
 * Mark messages as read validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[11]++;
exports.markAsReadSchema = joi_1.default.object({
  messageIds: joi_1.default.array().items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/)).optional().messages({
    'string.pattern.base': 'Invalid message ID format'
  })
});
/**
 * Conversation query validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[12]++;
exports.conversationQuerySchema = joi_1.default.object({
  page: joi_1.default.number().min(1).default(1).optional(),
  limit: joi_1.default.number().min(1).max(100).default(20).optional(),
  status: joi_1.default.string().valid('active', 'archived', 'blocked', 'deleted', 'all').default('active').optional(),
  conversationType: joi_1.default.string().valid('direct', 'group', 'support', 'all').default('all').optional(),
  search: joi_1.default.string().trim().min(2).max(100).optional().messages({
    'string.min': 'Search query must be at least 2 characters',
    'string.max': 'Search query cannot exceed 100 characters'
  })
});
/**
 * Message query validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[13]++;
exports.messageQuerySchema = joi_1.default.object({
  page: joi_1.default.number().min(1).default(1).optional(),
  limit: joi_1.default.number().min(1).max(100).default(50).optional(),
  before: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid message ID format for before parameter'
  }),
  after: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid message ID format for after parameter'
  }),
  messageType: joi_1.default.string().valid('text', 'image', 'file', 'location', 'property_share', 'system', 'all').default('all').optional(),
  search: joi_1.default.string().trim().min(2).max(100).optional().messages({
    'string.min': 'Search query must be at least 2 characters',
    'string.max': 'Search query cannot exceed 100 characters'
  })
});
/**
 * Search messages validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[14]++;
exports.searchMessagesSchema = joi_1.default.object({
  query: joi_1.default.string().trim().min(2).max(100).required().messages({
    'string.empty': 'Search query is required',
    'string.min': 'Search query must be at least 2 characters',
    'string.max': 'Search query cannot exceed 100 characters'
  }),
  conversationId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': 'Invalid conversation ID format'
  }),
  messageType: joi_1.default.string().valid('text', 'image', 'file', 'location', 'property_share', 'system', 'all').default('all').optional(),
  page: joi_1.default.number().min(1).default(1).optional(),
  limit: joi_1.default.number().min(1).max(50).default(20).optional(),
  dateFrom: joi_1.default.date().iso().optional().messages({
    'date.format': 'Date from must be in ISO format'
  }),
  dateTo: joi_1.default.date().iso().min(joi_1.default.ref('dateFrom')).optional().messages({
    'date.format': 'Date to must be in ISO format',
    'date.min': 'Date to must be after date from'
  })
});
/**
 * Mute conversation validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[15]++;
exports.muteConversationSchema = joi_1.default.object({
  isMuted: joi_1.default.boolean().required().messages({
    'any.required': 'Mute status is required',
    'boolean.base': 'Mute status must be a boolean'
  }),
  mutedUntil: joi_1.default.date().iso().min('now').optional().messages({
    'date.format': 'Muted until must be in ISO format',
    'date.min': 'Muted until must be in the future'
  })
});
/**
 * Add participant validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[16]++;
exports.addParticipantSchema = joi_1.default.object({
  participantIds: joi_1.default.array().items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/)).min(1).max(10).required().messages({
    'array.min': 'At least one participant is required',
    'array.max': 'Maximum 10 participants can be added at once',
    'string.pattern.base': 'Invalid participant ID format'
  })
});
/**
 * Remove participant validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[17]++;
exports.removeParticipantSchema = joi_1.default.object({
  participantId: joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': 'Invalid participant ID format',
    'any.required': 'Participant ID is required'
  })
});
/**
 * File upload validation schema
 */
/* istanbul ignore next */
cov_1w0todfos3().s[18]++;
exports.fileUploadSchema = joi_1.default.object({
  messageType: joi_1.default.string().valid('image', 'file').required().messages({
    'any.only': 'Message type must be image or file',
    'any.required': 'Message type is required'
  }),
  caption: joi_1.default.string().trim().max(500).optional().messages({
    'string.max': 'Caption cannot exceed 500 characters'
  })
});
/* istanbul ignore next */
cov_1w0todfos3().s[19]++;
exports.default = {
  createConversationSchema: exports.createConversationSchema,
  updateConversationSchema: exports.updateConversationSchema,
  sendMessageSchema: exports.sendMessageSchema,
  editMessageSchema: exports.editMessageSchema,
  deleteMessageSchema: exports.deleteMessageSchema,
  reactToMessageSchema: exports.reactToMessageSchema,
  markAsReadSchema: exports.markAsReadSchema,
  conversationQuerySchema: exports.conversationQuerySchema,
  messageQuerySchema: exports.messageQuerySchema,
  searchMessagesSchema: exports.searchMessagesSchema,
  muteConversationSchema: exports.muteConversationSchema,
  addParticipantSchema: exports.addParticipantSchema,
  removeParticipantSchema: exports.removeParticipantSchema,
  fileUploadSchema: exports.fileUploadSchema
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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