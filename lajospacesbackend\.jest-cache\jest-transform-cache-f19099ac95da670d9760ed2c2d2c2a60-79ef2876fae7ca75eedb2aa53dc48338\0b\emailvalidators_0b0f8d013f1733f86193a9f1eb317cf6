4c5480529d3d1d97713914c353a3ae94
"use strict";

/* istanbul ignore next */
function cov_25heujrf5c() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\email.validators.ts";
  var hash = "3e8f00359a27bfaff399f80caeddd2e7642e6a28";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\email.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 279
        }
      },
      "4": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 45
        }
      },
      "5": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 18,
          column: 3
        }
      },
      "6": {
        start: {
          line: 22,
          column: 0
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "7": {
        start: {
          line: 34,
          column: 0
        },
        end: {
          line: 77,
          column: 3
        }
      },
      "8": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "9": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 54
        }
      },
      "10": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "11": {
        start: {
          line: 81,
          column: 0
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "12": {
        start: {
          line: 103,
          column: 0
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "13": {
        start: {
          line: 160,
          column: 0
        },
        end: {
          line: 208,
          column: 3
        }
      },
      "14": {
        start: {
          line: 212,
          column: 0
        },
        end: {
          line: 246,
          column: 3
        }
      },
      "15": {
        start: {
          line: 230,
          column: 23
        },
        end: {
          line: 230,
          column: 33
        }
      },
      "16": {
        start: {
          line: 250,
          column: 0
        },
        end: {
          line: 284,
          column: 3
        }
      },
      "17": {
        start: {
          line: 285,
          column: 0
        },
        end: {
          line: 294,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 69,
            column: 10
          },
          end: {
            line: 69,
            column: 11
          }
        },
        loc: {
          start: {
            line: 69,
            column: 30
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 69
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 230,
            column: 17
          },
          end: {
            line: 230,
            column: 18
          }
        },
        loc: {
          start: {
            line: 230,
            column: 23
          },
          end: {
            line: 230,
            column: 33
          }
        },
        line: 230
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 27
          }
        }, {
          start: {
            line: 71,
            column: 31
          },
          end: {
            line: 71,
            column: 51
          }
        }],
        line: 71
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\email.validators.ts",
      mappings: ";;;;;;AAAA,8CAAsB;AAEtB;;GAEG;AACU,QAAA,2BAA2B,GAAG,aAAG,CAAC,MAAM,CAAC;IACpD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,sCAAsC;KACvD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,4BAA4B,GAAG,aAAG,CAAC,MAAM,CAAC;IACrD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE;SAChB,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,sCAAsC;QACtD,cAAc,EAAE,mBAAmB;KACpC,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,qBAAqB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC9C,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE;SACzB,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,gDAAgD;QAChE,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IAEJ,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,KAAK,CACJ,SAAS,EACT,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,YAAY,EACZ,gBAAgB,CACjB;SACA,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;KACpC,CAAC;IAEJ,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,QAAQ,EAAE;SACV,OAAO,CAAC,EAAE,CAAC;IAEd,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,sCAAsC;KACrD,CAAC;IAEJ,aAAa,EAAE,aAAG,CAAC,MAAM,EAAE;SACxB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,KAAK,CAAC;SACV,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,6CAA6C;QAC3D,YAAY,EAAE,gDAAgD;KAC/D,CAAC;CACL,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IAC3B,wDAAwD;IACxD,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAChD,OAAO,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,CAAC,QAAQ,CAAC;IACV,uBAAuB,EAAE,uDAAuD;CACjF,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,0BAA0B,GAAG,aAAG,CAAC,MAAM,CAAC;IACnD,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,KAAK,CACJ,SAAS,EACT,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,YAAY,EACZ,gBAAgB,CACjB;SACA,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;QACnC,cAAc,EAAE,2BAA2B;KAC5C,CAAC;IAEJ,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,QAAQ,EAAE;SACV,OAAO,CAAC,EAAE,CAAC;IAEd,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;SACrB,OAAO,CAAC,MAAM,CAAC;SACf,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,oCAAoC;KACjD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,iBAAiB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC1C,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,QAAQ,EAAE;SACV,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,iBAAiB,EAAE,kCAAkC;QACrD,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEJ,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,OAAO,EAAE;SACT,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,KAAK,CAAC;SACV,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,gBAAgB,EAAE,yBAAyB;QAC3C,YAAY,EAAE,yBAAyB;QACvC,YAAY,EAAE,0BAA0B;QACxC,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,OAAO,EAAE;SAClB,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;IAEb,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE;SACf,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,oDAAoD;QACpE,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,GAAG,CAAC,CAAC,CAAC;SACN,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,+BAA+B;QAC7C,cAAc,EAAE,2BAA2B;KAC5C,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,OAAO,CAAC,YAAY,CAAC;SACrB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,wCAAwC;QACtD,YAAY,EAAE,wCAAwC;KACvD,CAAC;IAEJ,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE;SACtB,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,2CAA2C;QAC3D,cAAc,EAAE,gCAAgC;KACjD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,eAAe,GAAG,aAAG,CAAC,MAAM,CAAC;IACxC,UAAU,EAAE,aAAG,CAAC,KAAK,EAAE;SACpB,KAAK,CACJ,aAAG,CAAC,MAAM,CAAC;QACT,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;QACtC,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QACpD,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KACtC,CAAC,CACH;SACA,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,oCAAoC;QACjD,WAAW,EAAE,iCAAiC;QAC9C,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IAEJ,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,KAAK,CACJ,SAAS,EACT,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,YAAY,EACZ,gBAAgB,CACjB;SACA,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;QACnC,cAAc,EAAE,2BAA2B;KAC5C,CAAC;IAEJ,kBAAkB,EAAE,aAAG,CAAC,MAAM,EAAE;SAC7B,QAAQ,EAAE;SACV,OAAO,CAAC,EAAE,CAAC;IAEd,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,GAAG,CAAC,GAAG,CAAC;SACR,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,sCAAsC;KACrD,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,IAAI,EAAE;SACf,GAAG,EAAE;SACL,GAAG,CAAC,KAAK,CAAC;SACV,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,iCAAiC;KAC9C,CAAC;IAEJ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE;SACnB,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;SAC9B,OAAO,CAAC,QAAQ,CAAC;SACjB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uCAAuC;KACpD,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,yBAAyB,GAAG,aAAG,CAAC,MAAM,CAAC;IAClD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE;SACpB,IAAI,EAAE;SACN,GAAG,CAAC,CAAC,CAAC;SACN,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,4BAA4B;QAC1C,cAAc,EAAE,wBAAwB;KACzC,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC;SAC/D,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,yBAAyB;QACrC,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IAEJ,SAAS,EAAE,aAAG,CAAC,IAAI,EAAE;SAClB,GAAG,EAAE;SACL,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;SACzB,QAAQ,EAAE;IAEb,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,IAAI,EAAE;SACN,GAAG,CAAC,IAAI,CAAC;SACT,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,YAAY,EAAE,6CAA6C;KAC5D,CAAC;IAEJ,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE;SACzB,KAAK,EAAE;SACP,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,cAAc,EAAE,wCAAwC;QACxD,cAAc,EAAE,6BAA6B;KAC9C,CAAC;CACL,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,yBAAyB,GAAG,aAAG,CAAC,MAAM,CAAC;IAClD,SAAS,EAAE,aAAG,CAAC,IAAI,EAAE;SAClB,GAAG,EAAE;SACL,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,IAAI,EAAE;SAChB,GAAG,EAAE;SACL,GAAG,CAAC,aAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;SACzB,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,WAAW,EAAE,+BAA+B;QAC5C,UAAU,EAAE,mCAAmC;KAChD,CAAC;IAEJ,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE;SACvB,KAAK,CACJ,SAAS,EACT,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,qBAAqB,EACrB,YAAY,EACZ,gBAAgB,CACjB;SACA,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;KACpC,CAAC;IAEJ,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE;SACjB,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC;SAC/D,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,uBAAuB;KACpC,CAAC;IAEJ,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE;SAClB,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;SACnD,OAAO,CAAC,KAAK,CAAC;SACd,QAAQ,EAAE;SACV,QAAQ,CAAC;QACR,UAAU,EAAE,wDAAwD;KACrE,CAAC;CACL,CAAC,CAAC;AAEH,kBAAe;IACb,2BAA2B,EAA3B,mCAA2B;IAC3B,4BAA4B,EAA5B,oCAA4B;IAC5B,qBAAqB,EAArB,6BAAqB;IACrB,0BAA0B,EAA1B,kCAA0B;IAC1B,iBAAiB,EAAjB,yBAAiB;IACjB,eAAe,EAAf,uBAAe;IACf,yBAAyB,EAAzB,iCAAyB;IACzB,yBAAyB,EAAzB,iCAAyB;CAC1B,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\email.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n/**\r\n * Send verification email validation schema\r\n */\r\nexport const sendVerificationEmailSchema = Joi.object({\r\n  email: Joi.string()\r\n    .email()\r\n    .optional()\r\n    .messages({\r\n      'string.email': 'Please provide a valid email address'\r\n    })\r\n});\r\n\r\n/**\r\n * Send password reset email validation schema\r\n */\r\nexport const sendPasswordResetEmailSchema = Joi.object({\r\n  email: Joi.string()\r\n    .email()\r\n    .required()\r\n    .messages({\r\n      'string.email': 'Please provide a valid email address',\r\n      'any.required': 'Email is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Send custom email validation schema\r\n */\r\nexport const sendCustomEmailSchema = Joi.object({\r\n  recipientEmail: Joi.string()\r\n    .email()\r\n    .required()\r\n    .messages({\r\n      'string.email': 'Please provide a valid recipient email address',\r\n      'any.required': 'Recipient email is required'\r\n    }),\r\n  \r\n  templateType: Joi.string()\r\n    .valid(\r\n      'welcome',\r\n      'email_verification',\r\n      'password_reset',\r\n      'password_changed',\r\n      'new_message',\r\n      'new_match',\r\n      'property_posted',\r\n      'property_approved',\r\n      'system_notification',\r\n      'newsletter',\r\n      'security_alert'\r\n    )\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid template type'\r\n    }),\r\n  \r\n  templateData: Joi.object()\r\n    .optional()\r\n    .default({}),\r\n  \r\n  subject: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(200)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Subject must be at least 1 character',\r\n      'string.max': 'Subject cannot exceed 200 characters'\r\n    }),\r\n  \r\n  customContent: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(50000)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Custom content must be at least 1 character',\r\n      'string.max': 'Custom content cannot exceed 50,000 characters'\r\n    })\r\n}).custom((value, helpers) => {\r\n  // Either templateType or customContent must be provided\r\n  if (!value.templateType && !value.customContent) {\r\n    return helpers.error('custom.missingContent');\r\n  }\r\n  return value;\r\n}).messages({\r\n  'custom.missingContent': 'Either templateType or customContent must be provided'\r\n});\r\n\r\n/**\r\n * Preview email template validation schema\r\n */\r\nexport const previewEmailTemplateSchema = Joi.object({\r\n  templateType: Joi.string()\r\n    .valid(\r\n      'welcome',\r\n      'email_verification',\r\n      'password_reset',\r\n      'password_changed',\r\n      'new_message',\r\n      'new_match',\r\n      'property_posted',\r\n      'property_approved',\r\n      'system_notification',\r\n      'newsletter',\r\n      'security_alert'\r\n    )\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Invalid template type',\r\n      'any.required': 'Template type is required'\r\n    }),\r\n  \r\n  templateData: Joi.object()\r\n    .optional()\r\n    .default({}),\r\n  \r\n  format: Joi.string()\r\n    .valid('html', 'text')\r\n    .default('html')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Format must be either html or text'\r\n    })\r\n});\r\n\r\n/**\r\n * Email configuration validation schema\r\n */\r\nexport const emailConfigSchema = Joi.object({\r\n  host: Joi.string()\r\n    .hostname()\r\n    .required()\r\n    .messages({\r\n      'string.hostname': 'Please provide a valid SMTP host',\r\n      'any.required': 'SMTP host is required'\r\n    }),\r\n  \r\n  port: Joi.number()\r\n    .integer()\r\n    .min(1)\r\n    .max(65535)\r\n    .required()\r\n    .messages({\r\n      'number.integer': 'Port must be an integer',\r\n      'number.min': 'Port must be at least 1',\r\n      'number.max': 'Port cannot exceed 65535',\r\n      'any.required': 'SMTP port is required'\r\n    }),\r\n  \r\n  secure: Joi.boolean()\r\n    .default(false)\r\n    .optional(),\r\n  \r\n  user: Joi.string()\r\n    .email()\r\n    .required()\r\n    .messages({\r\n      'string.email': 'Please provide a valid email address for SMTP user',\r\n      'any.required': 'SMTP user is required'\r\n    }),\r\n  \r\n  password: Joi.string()\r\n    .min(1)\r\n    .required()\r\n    .messages({\r\n      'string.min': 'SMTP password cannot be empty',\r\n      'any.required': 'SMTP password is required'\r\n    }),\r\n  \r\n  fromName: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(100)\r\n    .default('LajoSpaces')\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'From name must be at least 1 character',\r\n      'string.max': 'From name cannot exceed 100 characters'\r\n    }),\r\n  \r\n  fromAddress: Joi.string()\r\n    .email()\r\n    .required()\r\n    .messages({\r\n      'string.email': 'Please provide a valid from email address',\r\n      'any.required': 'From email address is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Bulk email validation schema\r\n */\r\nexport const bulkEmailSchema = Joi.object({\r\n  recipients: Joi.array()\r\n    .items(\r\n      Joi.object({\r\n        email: Joi.string().email().required(),\r\n        name: Joi.string().trim().min(1).max(100).optional(),\r\n        templateData: Joi.object().optional()\r\n      })\r\n    )\r\n    .min(1)\r\n    .max(1000)\r\n    .required()\r\n    .messages({\r\n      'array.min': 'At least one recipient is required',\r\n      'array.max': 'Maximum 1000 recipients allowed',\r\n      'any.required': 'Recipients list is required'\r\n    }),\r\n  \r\n  templateType: Joi.string()\r\n    .valid(\r\n      'welcome',\r\n      'email_verification',\r\n      'password_reset',\r\n      'password_changed',\r\n      'new_message',\r\n      'new_match',\r\n      'property_posted',\r\n      'property_approved',\r\n      'system_notification',\r\n      'newsletter',\r\n      'security_alert'\r\n    )\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Invalid template type',\r\n      'any.required': 'Template type is required'\r\n    }),\r\n  \r\n  globalTemplateData: Joi.object()\r\n    .optional()\r\n    .default({}),\r\n  \r\n  subject: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .max(200)\r\n    .optional()\r\n    .messages({\r\n      'string.min': 'Subject must be at least 1 character',\r\n      'string.max': 'Subject cannot exceed 200 characters'\r\n    }),\r\n  \r\n  sendAt: Joi.date()\r\n    .iso()\r\n    .min('now')\r\n    .optional()\r\n    .messages({\r\n      'date.min': 'Send date cannot be in the past'\r\n    }),\r\n  \r\n  priority: Joi.string()\r\n    .valid('high', 'normal', 'low')\r\n    .default('normal')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Priority must be high, normal, or low'\r\n    })\r\n});\r\n\r\n/**\r\n * Email delivery status validation schema\r\n */\r\nexport const emailDeliveryStatusSchema = Joi.object({\r\n  messageId: Joi.string()\r\n    .trim()\r\n    .min(1)\r\n    .required()\r\n    .messages({\r\n      'string.min': 'Message ID cannot be empty',\r\n      'any.required': 'Message ID is required'\r\n    }),\r\n  \r\n  status: Joi.string()\r\n    .valid('sent', 'delivered', 'bounced', 'complained', 'rejected')\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Invalid delivery status',\r\n      'any.required': 'Delivery status is required'\r\n    }),\r\n  \r\n  timestamp: Joi.date()\r\n    .iso()\r\n    .default(() => new Date())\r\n    .optional(),\r\n  \r\n  errorMessage: Joi.string()\r\n    .trim()\r\n    .max(1000)\r\n    .optional()\r\n    .messages({\r\n      'string.max': 'Error message cannot exceed 1000 characters'\r\n    }),\r\n  \r\n  recipientEmail: Joi.string()\r\n    .email()\r\n    .required()\r\n    .messages({\r\n      'string.email': 'Please provide a valid recipient email',\r\n      'any.required': 'Recipient email is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Email analytics query validation schema\r\n */\r\nexport const emailAnalyticsQuerySchema = Joi.object({\r\n  startDate: Joi.date()\r\n    .iso()\r\n    .optional()\r\n    .messages({\r\n      'date.base': 'Start date must be a valid date'\r\n    }),\r\n  \r\n  endDate: Joi.date()\r\n    .iso()\r\n    .min(Joi.ref('startDate'))\r\n    .optional()\r\n    .messages({\r\n      'date.base': 'End date must be a valid date',\r\n      'date.min': 'End date must be after start date'\r\n    }),\r\n  \r\n  templateType: Joi.string()\r\n    .valid(\r\n      'welcome',\r\n      'email_verification',\r\n      'password_reset',\r\n      'password_changed',\r\n      'new_message',\r\n      'new_match',\r\n      'property_posted',\r\n      'property_approved',\r\n      'system_notification',\r\n      'newsletter',\r\n      'security_alert'\r\n    )\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid template type'\r\n    }),\r\n  \r\n  status: Joi.string()\r\n    .valid('sent', 'delivered', 'bounced', 'complained', 'rejected')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Invalid status filter'\r\n    }),\r\n  \r\n  groupBy: Joi.string()\r\n    .valid('day', 'week', 'month', 'template', 'status')\r\n    .default('day')\r\n    .optional()\r\n    .messages({\r\n      'any.only': 'Group by must be day, week, month, template, or status'\r\n    })\r\n});\r\n\r\nexport default {\r\n  sendVerificationEmailSchema,\r\n  sendPasswordResetEmailSchema,\r\n  sendCustomEmailSchema,\r\n  previewEmailTemplateSchema,\r\n  emailConfigSchema,\r\n  bulkEmailSchema,\r\n  emailDeliveryStatusSchema,\r\n  emailAnalyticsQuerySchema\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3e8f00359a27bfaff399f80caeddd2e7642e6a28"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25heujrf5c = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25heujrf5c();
var __importDefault =
/* istanbul ignore next */
(cov_25heujrf5c().s[0]++,
/* istanbul ignore next */
(cov_25heujrf5c().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_25heujrf5c().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_25heujrf5c().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_25heujrf5c().f[0]++;
  cov_25heujrf5c().s[1]++;
  return /* istanbul ignore next */(cov_25heujrf5c().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_25heujrf5c().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_25heujrf5c().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_25heujrf5c().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_25heujrf5c().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_25heujrf5c().s[3]++;
exports.emailAnalyticsQuerySchema = exports.emailDeliveryStatusSchema = exports.bulkEmailSchema = exports.emailConfigSchema = exports.previewEmailTemplateSchema = exports.sendCustomEmailSchema = exports.sendPasswordResetEmailSchema = exports.sendVerificationEmailSchema = void 0;
const joi_1 =
/* istanbul ignore next */
(cov_25heujrf5c().s[4]++, __importDefault(require("joi")));
/**
 * Send verification email validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[5]++;
exports.sendVerificationEmailSchema = joi_1.default.object({
  email: joi_1.default.string().email().optional().messages({
    'string.email': 'Please provide a valid email address'
  })
});
/**
 * Send password reset email validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[6]++;
exports.sendPasswordResetEmailSchema = joi_1.default.object({
  email: joi_1.default.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  })
});
/**
 * Send custom email validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[7]++;
exports.sendCustomEmailSchema = joi_1.default.object({
  recipientEmail: joi_1.default.string().email().required().messages({
    'string.email': 'Please provide a valid recipient email address',
    'any.required': 'Recipient email is required'
  }),
  templateType: joi_1.default.string().valid('welcome', 'email_verification', 'password_reset', 'password_changed', 'new_message', 'new_match', 'property_posted', 'property_approved', 'system_notification', 'newsletter', 'security_alert').optional().messages({
    'any.only': 'Invalid template type'
  }),
  templateData: joi_1.default.object().optional().default({}),
  subject: joi_1.default.string().trim().min(1).max(200).optional().messages({
    'string.min': 'Subject must be at least 1 character',
    'string.max': 'Subject cannot exceed 200 characters'
  }),
  customContent: joi_1.default.string().trim().min(1).max(50000).optional().messages({
    'string.min': 'Custom content must be at least 1 character',
    'string.max': 'Custom content cannot exceed 50,000 characters'
  })
}).custom((value, helpers) => {
  /* istanbul ignore next */
  cov_25heujrf5c().f[1]++;
  cov_25heujrf5c().s[8]++;
  // Either templateType or customContent must be provided
  if (
  /* istanbul ignore next */
  (cov_25heujrf5c().b[4][0]++, !value.templateType) &&
  /* istanbul ignore next */
  (cov_25heujrf5c().b[4][1]++, !value.customContent)) {
    /* istanbul ignore next */
    cov_25heujrf5c().b[3][0]++;
    cov_25heujrf5c().s[9]++;
    return helpers.error('custom.missingContent');
  } else
  /* istanbul ignore next */
  {
    cov_25heujrf5c().b[3][1]++;
  }
  cov_25heujrf5c().s[10]++;
  return value;
}).messages({
  'custom.missingContent': 'Either templateType or customContent must be provided'
});
/**
 * Preview email template validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[11]++;
exports.previewEmailTemplateSchema = joi_1.default.object({
  templateType: joi_1.default.string().valid('welcome', 'email_verification', 'password_reset', 'password_changed', 'new_message', 'new_match', 'property_posted', 'property_approved', 'system_notification', 'newsletter', 'security_alert').required().messages({
    'any.only': 'Invalid template type',
    'any.required': 'Template type is required'
  }),
  templateData: joi_1.default.object().optional().default({}),
  format: joi_1.default.string().valid('html', 'text').default('html').optional().messages({
    'any.only': 'Format must be either html or text'
  })
});
/**
 * Email configuration validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[12]++;
exports.emailConfigSchema = joi_1.default.object({
  host: joi_1.default.string().hostname().required().messages({
    'string.hostname': 'Please provide a valid SMTP host',
    'any.required': 'SMTP host is required'
  }),
  port: joi_1.default.number().integer().min(1).max(65535).required().messages({
    'number.integer': 'Port must be an integer',
    'number.min': 'Port must be at least 1',
    'number.max': 'Port cannot exceed 65535',
    'any.required': 'SMTP port is required'
  }),
  secure: joi_1.default.boolean().default(false).optional(),
  user: joi_1.default.string().email().required().messages({
    'string.email': 'Please provide a valid email address for SMTP user',
    'any.required': 'SMTP user is required'
  }),
  password: joi_1.default.string().min(1).required().messages({
    'string.min': 'SMTP password cannot be empty',
    'any.required': 'SMTP password is required'
  }),
  fromName: joi_1.default.string().trim().min(1).max(100).default('LajoSpaces').optional().messages({
    'string.min': 'From name must be at least 1 character',
    'string.max': 'From name cannot exceed 100 characters'
  }),
  fromAddress: joi_1.default.string().email().required().messages({
    'string.email': 'Please provide a valid from email address',
    'any.required': 'From email address is required'
  })
});
/**
 * Bulk email validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[13]++;
exports.bulkEmailSchema = joi_1.default.object({
  recipients: joi_1.default.array().items(joi_1.default.object({
    email: joi_1.default.string().email().required(),
    name: joi_1.default.string().trim().min(1).max(100).optional(),
    templateData: joi_1.default.object().optional()
  })).min(1).max(1000).required().messages({
    'array.min': 'At least one recipient is required',
    'array.max': 'Maximum 1000 recipients allowed',
    'any.required': 'Recipients list is required'
  }),
  templateType: joi_1.default.string().valid('welcome', 'email_verification', 'password_reset', 'password_changed', 'new_message', 'new_match', 'property_posted', 'property_approved', 'system_notification', 'newsletter', 'security_alert').required().messages({
    'any.only': 'Invalid template type',
    'any.required': 'Template type is required'
  }),
  globalTemplateData: joi_1.default.object().optional().default({}),
  subject: joi_1.default.string().trim().min(1).max(200).optional().messages({
    'string.min': 'Subject must be at least 1 character',
    'string.max': 'Subject cannot exceed 200 characters'
  }),
  sendAt: joi_1.default.date().iso().min('now').optional().messages({
    'date.min': 'Send date cannot be in the past'
  }),
  priority: joi_1.default.string().valid('high', 'normal', 'low').default('normal').optional().messages({
    'any.only': 'Priority must be high, normal, or low'
  })
});
/**
 * Email delivery status validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[14]++;
exports.emailDeliveryStatusSchema = joi_1.default.object({
  messageId: joi_1.default.string().trim().min(1).required().messages({
    'string.min': 'Message ID cannot be empty',
    'any.required': 'Message ID is required'
  }),
  status: joi_1.default.string().valid('sent', 'delivered', 'bounced', 'complained', 'rejected').required().messages({
    'any.only': 'Invalid delivery status',
    'any.required': 'Delivery status is required'
  }),
  timestamp: joi_1.default.date().iso().default(() => {
    /* istanbul ignore next */
    cov_25heujrf5c().f[2]++;
    cov_25heujrf5c().s[15]++;
    return new Date();
  }).optional(),
  errorMessage: joi_1.default.string().trim().max(1000).optional().messages({
    'string.max': 'Error message cannot exceed 1000 characters'
  }),
  recipientEmail: joi_1.default.string().email().required().messages({
    'string.email': 'Please provide a valid recipient email',
    'any.required': 'Recipient email is required'
  })
});
/**
 * Email analytics query validation schema
 */
/* istanbul ignore next */
cov_25heujrf5c().s[16]++;
exports.emailAnalyticsQuerySchema = joi_1.default.object({
  startDate: joi_1.default.date().iso().optional().messages({
    'date.base': 'Start date must be a valid date'
  }),
  endDate: joi_1.default.date().iso().min(joi_1.default.ref('startDate')).optional().messages({
    'date.base': 'End date must be a valid date',
    'date.min': 'End date must be after start date'
  }),
  templateType: joi_1.default.string().valid('welcome', 'email_verification', 'password_reset', 'password_changed', 'new_message', 'new_match', 'property_posted', 'property_approved', 'system_notification', 'newsletter', 'security_alert').optional().messages({
    'any.only': 'Invalid template type'
  }),
  status: joi_1.default.string().valid('sent', 'delivered', 'bounced', 'complained', 'rejected').optional().messages({
    'any.only': 'Invalid status filter'
  }),
  groupBy: joi_1.default.string().valid('day', 'week', 'month', 'template', 'status').default('day').optional().messages({
    'any.only': 'Group by must be day, week, month, template, or status'
  })
});
/* istanbul ignore next */
cov_25heujrf5c().s[17]++;
exports.default = {
  sendVerificationEmailSchema: exports.sendVerificationEmailSchema,
  sendPasswordResetEmailSchema: exports.sendPasswordResetEmailSchema,
  sendCustomEmailSchema: exports.sendCustomEmailSchema,
  previewEmailTemplateSchema: exports.previewEmailTemplateSchema,
  emailConfigSchema: exports.emailConfigSchema,
  bulkEmailSchema: exports.bulkEmailSchema,
  emailDeliveryStatusSchema: exports.emailDeliveryStatusSchema,
  emailAnalyticsQuerySchema: exports.emailAnalyticsQuerySchema
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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