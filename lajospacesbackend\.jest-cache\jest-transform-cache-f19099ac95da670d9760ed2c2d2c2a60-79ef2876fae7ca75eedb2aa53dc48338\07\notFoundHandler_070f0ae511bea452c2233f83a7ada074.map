{"version": 3, "names": ["exports", "notFoundHandler", "errorHandler_1", "cov_ygxuajumr", "s", "require", "req", "_res", "next", "f", "message", "originalUrl", "error", "AppError", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { AppError } from './errorHandler';\r\n\r\n/**\r\n * Handle 404 Not Found errors\r\n */\r\nexport function notFoundHandler(req: Request, _res: Response, next: NextFunction): void {\r\n  const message = `Route ${req.originalUrl} not found on this server`;\r\n  const error = new AppError(message, 404, true, 'ROUTE_NOT_FOUND');\r\n  next(error);\r\n}\r\n\r\nexport default notFoundHandler;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMAA,OAAA,CAAAC,eAAA,GAAAA,eAAA;AALA,MAAAC,cAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA;;;AAGA,SAAgBJ,eAAeA,CAACK,GAAY,EAAEC,IAAc,EAAEC,IAAkB;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAC9E,MAAMC,OAAO;EAAA;EAAA,CAAAP,aAAA,GAAAC,CAAA,OAAG,SAASE,GAAG,CAACK,WAAW,2BAA2B;EACnE,MAAMC,KAAK;EAAA;EAAA,CAAAT,aAAA,GAAAC,CAAA,OAAG,IAAIF,cAAA,CAAAW,QAAQ,CAACH,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC;EAAC;EAAAP,aAAA,GAAAC,CAAA;EAClEI,IAAI,CAACI,KAAK,CAAC;AACb;AAAC;AAAAT,aAAA,GAAAC,CAAA;AAEDJ,OAAA,CAAAc,OAAA,GAAeb,eAAe", "ignoreList": []}