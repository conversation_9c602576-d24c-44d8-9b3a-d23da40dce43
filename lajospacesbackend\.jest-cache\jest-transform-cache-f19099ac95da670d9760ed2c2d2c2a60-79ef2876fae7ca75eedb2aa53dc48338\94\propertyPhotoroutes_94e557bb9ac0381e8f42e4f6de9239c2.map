{"version": 3, "names": ["cov_q3ecutjxq", "actualCoverage", "express_1", "s", "require", "multer_1", "__importDefault", "propertyPhoto_controller_1", "auth_1", "validation_1", "property_validators_1", "router", "Router", "upload", "default", "storage", "memoryStorage", "limits", "fileSize", "files", "fileFilter", "_req", "file", "cb", "f", "allowedTypes", "includes", "mimetype", "b", "Error", "get", "getPhotoGuidelines", "use", "authenticate", "validateObjectId", "getPropertyPhotos", "post", "array", "validateFileUpload", "maxSize", "required", "maxFiles", "uploadPropertyPhotos", "delete", "deletePropertyPhoto", "patch", "setPrimaryPhoto", "validateRequest", "propertyPhotoSchema", "updatePhotoDetails", "reorderPhotosSchema", "reorderPropertyPhotos", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyPhoto.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport multer from 'multer';\r\nimport {\r\n  uploadPropertyPhotos,\r\n  getPropertyPhotos,\r\n  deletePropertyPhoto,\r\n  setPrimaryPhoto,\r\n  reorderPropertyPhotos,\r\n  updatePhotoDetails,\r\n  getPhotoGuidelines\r\n} from '../controllers/propertyPhoto.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateFileUpload, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  propertyPhotoSchema,\r\n  reorderPhotosSchema\r\n} from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n// Configure multer for memory storage\r\nconst upload = multer({\r\n  storage: multer.memoryStorage(),\r\n  limits: {\r\n    fileSize: 10 * 1024 * 1024, // 10MB\r\n    files: 10 // Max 10 files per upload\r\n  },\r\n  fileFilter: (_req, file, cb) => {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n    if (allowedTypes.includes(file.mimetype)) {\r\n      cb(null, true);\r\n    } else {\r\n      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));\r\n    }\r\n  }\r\n});\r\n\r\n// Public routes\r\nrouter.get('/guidelines', getPhotoGuidelines);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Property photo routes\r\nrouter.get('/:id/photos', validateObjectId('id'), getPropertyPhotos);\r\n\r\nrouter.post(\r\n  '/:id/photos',\r\n  validateObjectId('id'),\r\n  upload.array('photos', 10),\r\n  validateFileUpload({\r\n    maxSize: 10 * 1024 * 1024, // 10MB\r\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],\r\n    required: true,\r\n    maxFiles: 10\r\n  }),\r\n  uploadPropertyPhotos\r\n);\r\n\r\nrouter.delete(\r\n  '/:id/photos/:photoId',\r\n  validateObjectId('id'),\r\n  deletePropertyPhoto\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/:photoId/primary',\r\n  validateObjectId('id'),\r\n  setPrimaryPhoto\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/:photoId',\r\n  validateObjectId('id'),\r\n  validateRequest(propertyPhotoSchema),\r\n  updatePhotoDetails\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/reorder',\r\n  validateObjectId('id'),\r\n  validateRequest(reorderPhotosSchema),\r\n  reorderPropertyPhotos\r\n);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuBU;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBV,MAAAE,SAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,MAAAG,0BAAA;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA,OAAAC,OAAA;AASA,MAAAI,MAAA;AAAA;AAAA,CAAAR,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAK,YAAA;AAAA;AAAA,CAAAT,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAM,qBAAA;AAAA;AAAA,CAAAV,aAAA,GAAAG,CAAA,OAAAC,OAAA;AAKA,MAAMO,MAAM;AAAA;AAAA,CAAAX,aAAA,GAAAG,CAAA,OAAG,IAAAD,SAAA,CAAAU,MAAM,GAAE;AAEvB;AACA,MAAMC,MAAM;AAAA;AAAA,CAAAb,aAAA,GAAAG,CAAA,QAAG,IAAAE,QAAA,CAAAS,OAAM,EAAC;EACpBC,OAAO,EAAEV,QAAA,CAAAS,OAAM,CAACE,aAAa,EAAE;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;IAAE;IAC5BC,KAAK,EAAE,EAAE,CAAC;GACX;EACDC,UAAU,EAAEA,CAACC,IAAI,EAAEC,IAAI,EAAEC,EAAE,KAAI;IAAA;IAAAvB,aAAA,GAAAwB,CAAA;IAC7B;IACA,MAAMC,YAAY;IAAA;IAAA,CAAAzB,aAAA,GAAAG,CAAA,QAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;IAAC;IAAAH,aAAA,GAAAG,CAAA;IAC/D,IAAIsB,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAAC,EAAE;MAAA;MAAA3B,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAG,CAAA;MACxCoB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IAChB,CAAC,MAAM;MAAA;MAAAvB,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAG,CAAA;MACLoB,EAAE,CAAC,IAAIM,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAC3E;EACF;CACD,CAAC;AAEF;AAAA;AAAA7B,aAAA,GAAAG,CAAA;AACAQ,MAAM,CAACmB,GAAG,CAAC,aAAa,EAAEvB,0BAAA,CAAAwB,kBAAkB,CAAC;AAE7C;AAAA;AAAA/B,aAAA,GAAAG,CAAA;AACAQ,MAAM,CAACqB,GAAG,CAACxB,MAAA,CAAAyB,YAAY,CAAC;AAExB;AAAA;AAAAjC,aAAA,GAAAG,CAAA;AACAQ,MAAM,CAACmB,GAAG,CAAC,aAAa,EAAE,IAAArB,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EAAE3B,0BAAA,CAAA4B,iBAAiB,CAAC;AAAC;AAAAnC,aAAA,GAAAG,CAAA;AAErEQ,MAAM,CAACyB,IAAI,CACT,aAAa,EACb,IAAA3B,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EACtBrB,MAAM,CAACwB,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,EAC1B,IAAA5B,YAAA,CAAA6B,kBAAkB,EAAC;EACjBC,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EAC3Bd,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;EACvDe,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE;CACX,CAAC,EACFlC,0BAAA,CAAAmC,oBAAoB,CACrB;AAAC;AAAA1C,aAAA,GAAAG,CAAA;AAEFQ,MAAM,CAACgC,MAAM,CACX,sBAAsB,EACtB,IAAAlC,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EACtB3B,0BAAA,CAAAqC,mBAAmB,CACpB;AAAC;AAAA5C,aAAA,GAAAG,CAAA;AAEFQ,MAAM,CAACkC,KAAK,CACV,8BAA8B,EAC9B,IAAApC,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EACtB3B,0BAAA,CAAAuC,eAAe,CAChB;AAAC;AAAA9C,aAAA,GAAAG,CAAA;AAEFQ,MAAM,CAACkC,KAAK,CACV,sBAAsB,EACtB,IAAApC,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EACtB,IAAAzB,YAAA,CAAAsC,eAAe,EAACrC,qBAAA,CAAAsC,mBAAmB,CAAC,EACpCzC,0BAAA,CAAA0C,kBAAkB,CACnB;AAAC;AAAAjD,aAAA,GAAAG,CAAA;AAEFQ,MAAM,CAACkC,KAAK,CACV,qBAAqB,EACrB,IAAApC,YAAA,CAAAyB,gBAAgB,EAAC,IAAI,CAAC,EACtB,IAAAzB,YAAA,CAAAsC,eAAe,EAACrC,qBAAA,CAAAwC,mBAAmB,CAAC,EACpC3C,0BAAA,CAAA4C,qBAAqB,CACtB;AAAC;AAAAnD,aAAA,GAAAG,CAAA;AAEFiD,OAAA,CAAAtC,OAAA,GAAeH,MAAM", "ignoreList": []}