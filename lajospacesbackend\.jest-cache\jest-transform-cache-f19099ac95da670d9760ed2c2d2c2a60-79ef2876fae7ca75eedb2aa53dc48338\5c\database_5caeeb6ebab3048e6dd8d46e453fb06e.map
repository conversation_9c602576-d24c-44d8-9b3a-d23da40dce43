{"version": 3, "names": ["cov_1jyp3dm6tm", "actualCoverage", "exports", "connectDatabase", "s", "disconnectDatabase", "getDatabaseStatus", "clearDatabase", "mongoose_1", "__importDefault", "require", "environment_1", "logger_1", "mongoOptions", "maxPoolSize", "serverSelectionTimeoutMS", "socketTimeoutMS", "isConnected", "f", "b", "logger", "info", "mongo<PERSON>ri", "config", "NODE_ENV", "MONGODB_TEST_URI", "MONGODB_URI", "default", "connect", "dbN<PERSON>", "connection", "db", "databaseName", "error", "disconnect", "readyState", "host", "name", "Error", "collections", "key", "collection", "deleteMany", "on", "process", "exit", "getStatus", "clear"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\nimport { config } from './environment';\r\nimport { logger } from '../utils/logger';\r\n\r\n// MongoDB connection options\r\nconst mongoOptions: mongoose.ConnectOptions = {\r\n  maxPoolSize: 10, // Maintain up to 10 socket connections\r\n  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds\r\n  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity\r\n};\r\n\r\n// Database connection state\r\nlet isConnected = false;\r\n\r\n/**\r\n * Connect to MongoDB database\r\n */\r\nexport async function connectDatabase(): Promise<void> {\r\n  try {\r\n    if (isConnected) {\r\n      logger.info('📊 Database already connected');\r\n      return;\r\n    }\r\n\r\n    const mongoUri = config.NODE_ENV === 'test' ? config.MONGODB_TEST_URI : config.MONGODB_URI;\r\n    \r\n    logger.info('📊 Connecting to MongoDB...');\r\n    \r\n    await mongoose.connect(mongoUri, mongoOptions);\r\n    \r\n    isConnected = true;\r\n    logger.info('✅ MongoDB connected successfully');\r\n    \r\n    // Log database name\r\n    const dbName = mongoose.connection.db?.databaseName;\r\n    logger.info(`📊 Connected to database: ${dbName}`);\r\n    \r\n  } catch (error) {\r\n    logger.error('❌ MongoDB connection failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Disconnect from MongoDB database\r\n */\r\nexport async function disconnectDatabase(): Promise<void> {\r\n  try {\r\n    if (!isConnected) {\r\n      logger.info('📊 Database already disconnected');\r\n      return;\r\n    }\r\n\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    logger.info('📊 MongoDB disconnected successfully');\r\n  } catch (error) {\r\n    logger.error('❌ MongoDB disconnection failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get database connection status\r\n */\r\nexport function getDatabaseStatus(): {\r\n  isConnected: boolean;\r\n  readyState: number;\r\n  host?: string;\r\n  name?: string;\r\n} {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    name: mongoose.connection.name\r\n  };\r\n}\r\n\r\n/**\r\n * Clear database (for testing purposes only)\r\n */\r\nexport async function clearDatabase(): Promise<void> {\r\n  if (config.NODE_ENV !== 'test') {\r\n    throw new Error('clearDatabase can only be used in test environment');\r\n  }\r\n\r\n  const collections = mongoose.connection.collections;\r\n  \r\n  for (const key in collections) {\r\n    const collection = collections[key];\r\n    await collection.deleteMany({});\r\n  }\r\n  \r\n  logger.info('🧹 Test database cleared');\r\n}\r\n\r\n// Connection event handlers\r\nmongoose.connection.on('connected', () => {\r\n  logger.info('📊 Mongoose connected to MongoDB');\r\n});\r\n\r\nmongoose.connection.on('error', (error) => {\r\n  logger.error('❌ Mongoose connection error:', error);\r\n  isConnected = false;\r\n});\r\n\r\nmongoose.connection.on('disconnected', () => {\r\n  logger.info('📊 Mongoose disconnected from MongoDB');\r\n  isConnected = false;\r\n});\r\n\r\n// Handle application termination\r\nprocess.on('SIGINT', async () => {\r\n  try {\r\n    await disconnectDatabase();\r\n    logger.info('📊 MongoDB connection closed through app termination');\r\n    process.exit(0);\r\n  } catch (error) {\r\n    logger.error('❌ Error during MongoDB disconnection:', error);\r\n    process.exit(1);\r\n  }\r\n});\r\n\r\nexport default {\r\n  connect: connectDatabase,\r\n  disconnect: disconnectDatabase,\r\n  getStatus: getDatabaseStatus,\r\n  clear: clearDatabase\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUFE,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAwBC;AAAAH,cAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAcC;AAAAL,cAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AAYC;AAAAN,cAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAlFA,MAAAC,UAAA;AAAA;AAAA,CAAAR,cAAA,GAAAI,CAAA,OAAAK,eAAA,CAAAC,OAAA;AACA,MAAAC,aAAA;AAAA;AAAA,CAAAX,cAAA,GAAAI,CAAA,OAAAM,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAZ,cAAA,GAAAI,CAAA,OAAAM,OAAA;AAEA;AACA,MAAMG,YAAY;AAAA;AAAA,CAAAb,cAAA,GAAAI,CAAA,QAA4B;EAC5CU,WAAW,EAAE,EAAE;EAAE;EACjBC,wBAAwB,EAAE,IAAI;EAAE;EAChCC,eAAe,EAAE,KAAK,CAAE;CACzB;AAED;AACA,IAAIC,WAAW;AAAA;AAAA,CAAAjB,cAAA,GAAAI,CAAA,QAAG,KAAK;AAEvB;;;AAGO,eAAeD,eAAeA,CAAA;EAAA;EAAAH,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EACnC,IAAI;IAAA;IAAAJ,cAAA,GAAAI,CAAA;IACF,IAAIa,WAAW,EAAE;MAAA;MAAAjB,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAI,CAAA;MACfQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,+BAA+B,CAAC;MAAC;MAAArB,cAAA,GAAAI,CAAA;MAC7C;IACF,CAAC;IAAA;IAAA;MAAAJ,cAAA,GAAAmB,CAAA;IAAA;IAED,MAAMG,QAAQ;IAAA;IAAA,CAAAtB,cAAA,GAAAI,CAAA,QAAGO,aAAA,CAAAY,MAAM,CAACC,QAAQ,KAAK,MAAM;IAAA;IAAA,CAAAxB,cAAA,GAAAmB,CAAA,UAAGR,aAAA,CAAAY,MAAM,CAACE,gBAAgB;IAAA;IAAA,CAAAzB,cAAA,GAAAmB,CAAA,UAAGR,aAAA,CAAAY,MAAM,CAACG,WAAW;IAAC;IAAA1B,cAAA,GAAAI,CAAA;IAE3FQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,6BAA6B,CAAC;IAAC;IAAArB,cAAA,GAAAI,CAAA;IAE3C,MAAMI,UAAA,CAAAmB,OAAQ,CAACC,OAAO,CAACN,QAAQ,EAAET,YAAY,CAAC;IAAC;IAAAb,cAAA,GAAAI,CAAA;IAE/Ca,WAAW,GAAG,IAAI;IAAC;IAAAjB,cAAA,GAAAI,CAAA;IACnBQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC;IAE/C;IACA,MAAMQ,MAAM;IAAA;IAAA,CAAA7B,cAAA,GAAAI,CAAA,QAAGI,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACC,EAAE,EAAEC,YAAY;IAAC;IAAAhC,cAAA,GAAAI,CAAA;IACpDQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,6BAA6BQ,MAAM,EAAE,CAAC;EAEpD,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAAjC,cAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAQ,MAAM,CAACa,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IAAC;IAAAjC,cAAA,GAAAI,CAAA;IACpD,MAAM6B,KAAK;EACb;AACF;AAEA;;;AAGO,eAAe5B,kBAAkBA,CAAA;EAAA;EAAAL,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EACtC,IAAI;IAAA;IAAAJ,cAAA,GAAAI,CAAA;IACF,IAAI,CAACa,WAAW,EAAE;MAAA;MAAAjB,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAI,CAAA;MAChBQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC;MAAC;MAAArB,cAAA,GAAAI,CAAA;MAChD;IACF,CAAC;IAAA;IAAA;MAAAJ,cAAA,GAAAmB,CAAA;IAAA;IAAAnB,cAAA,GAAAI,CAAA;IAED,MAAMI,UAAA,CAAAmB,OAAQ,CAACO,UAAU,EAAE;IAAC;IAAAlC,cAAA,GAAAI,CAAA;IAC5Ba,WAAW,GAAG,KAAK;IAAC;IAAAjB,cAAA,GAAAI,CAAA;IACpBQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,sCAAsC,CAAC;EACrD,CAAC,CAAC,OAAOY,KAAK,EAAE;IAAA;IAAAjC,cAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAQ,MAAM,CAACa,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAAC;IAAAjC,cAAA,GAAAI,CAAA;IACvD,MAAM6B,KAAK;EACb;AACF;AAEA;;;AAGA,SAAgB3B,iBAAiBA,CAAA;EAAA;EAAAN,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EAM/B,OAAO;IACLa,WAAW;IACXkB,UAAU,EAAE3B,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACK,UAAU;IAC1CC,IAAI,EAAE5B,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACM,IAAI;IAC9BC,IAAI,EAAE7B,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACO;GAC3B;AACH;AAEA;;;AAGO,eAAe9B,aAAaA,CAAA;EAAA;EAAAP,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EACjC,IAAIO,aAAA,CAAAY,MAAM,CAACC,QAAQ,KAAK,MAAM,EAAE;IAAA;IAAAxB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAI,CAAA;IAC9B,MAAM,IAAIkC,KAAK,CAAC,oDAAoD,CAAC;EACvE,CAAC;EAAA;EAAA;IAAAtC,cAAA,GAAAmB,CAAA;EAAA;EAED,MAAMoB,WAAW;EAAA;EAAA,CAAAvC,cAAA,GAAAI,CAAA,QAAGI,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACS,WAAW;EAAC;EAAAvC,cAAA,GAAAI,CAAA;EAEpD,KAAK,MAAMoC,GAAG,IAAID,WAAW,EAAE;IAC7B,MAAME,UAAU;IAAA;IAAA,CAAAzC,cAAA,GAAAI,CAAA,QAAGmC,WAAW,CAACC,GAAG,CAAC;IAAC;IAAAxC,cAAA,GAAAI,CAAA;IACpC,MAAMqC,UAAU,CAACC,UAAU,CAAC,EAAE,CAAC;EACjC;EAAC;EAAA1C,cAAA,GAAAI,CAAA;EAEDQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,0BAA0B,CAAC;AACzC;AAEA;AAAA;AAAArB,cAAA,GAAAI,CAAA;AACAI,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACa,EAAE,CAAC,WAAW,EAAE,MAAK;EAAA;EAAA3C,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EACvCQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC;AACjD,CAAC,CAAC;AAAC;AAAArB,cAAA,GAAAI,CAAA;AAEHI,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACa,EAAE,CAAC,OAAO,EAAGV,KAAK,IAAI;EAAA;EAAAjC,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EACxCQ,QAAA,CAAAQ,MAAM,CAACa,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;EAAC;EAAAjC,cAAA,GAAAI,CAAA;EACpDa,WAAW,GAAG,KAAK;AACrB,CAAC,CAAC;AAAC;AAAAjB,cAAA,GAAAI,CAAA;AAEHI,UAAA,CAAAmB,OAAQ,CAACG,UAAU,CAACa,EAAE,CAAC,cAAc,EAAE,MAAK;EAAA;EAAA3C,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EAC1CQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,uCAAuC,CAAC;EAAC;EAAArB,cAAA,GAAAI,CAAA;EACrDa,WAAW,GAAG,KAAK;AACrB,CAAC,CAAC;AAEF;AAAA;AAAAjB,cAAA,GAAAI,CAAA;AACAwC,OAAO,CAACD,EAAE,CAAC,QAAQ,EAAE,YAAW;EAAA;EAAA3C,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAI,CAAA;EAC9B,IAAI;IAAA;IAAAJ,cAAA,GAAAI,CAAA;IACF,MAAMC,kBAAkB,EAAE;IAAC;IAAAL,cAAA,GAAAI,CAAA;IAC3BQ,QAAA,CAAAQ,MAAM,CAACC,IAAI,CAAC,sDAAsD,CAAC;IAAC;IAAArB,cAAA,GAAAI,CAAA;IACpEwC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,OAAOZ,KAAK,EAAE;IAAA;IAAAjC,cAAA,GAAAI,CAAA;IACdQ,QAAA,CAAAQ,MAAM,CAACa,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAAC;IAAAjC,cAAA,GAAAI,CAAA;IAC7DwC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;EACjB;AACF,CAAC,CAAC;AAAC;AAAA7C,cAAA,GAAAI,CAAA;AAEHF,OAAA,CAAAyB,OAAA,GAAe;EACbC,OAAO,EAAEzB,eAAe;EACxB+B,UAAU,EAAE7B,kBAAkB;EAC9ByC,SAAS,EAAExC,iBAAiB;EAC5ByC,KAAK,EAAExC;CACR", "ignoreList": []}