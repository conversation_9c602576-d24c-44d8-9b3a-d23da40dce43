{"version": 3, "names": ["express_1", "cov_o4uw83gxa", "s", "require", "email_controller_1", "auth_1", "validation_1", "email_validators_1", "router", "Router", "post", "authenticate", "validateRequest", "sendVerificationEmailSchema", "sendVerificationEmail", "sendPasswordResetEmailSchema", "sendPasswordResetEmail", "requireRole", "sendCustomEmailSchema", "sendCustomEmail", "testEmailService", "get", "getEmailServiceStatus", "getEmailTemplates", "previewEmailTemplateSchema", "previewEmailTemplate", "req", "res", "f", "json", "success", "message", "timestamp", "Date", "toISOString", "service", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\email.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  sendVerificationEmail,\r\n  sendPasswordResetEmail,\r\n  sendCustomEmail,\r\n  testEmailService,\r\n  getEmailServiceStatus,\r\n  getEmailTemplates,\r\n  previewEmailTemplate\r\n} from '../controllers/email.controller';\r\nimport { authenticate, requireRole } from '../middleware/auth';\r\nimport { validateRequest } from '../middleware/validation';\r\nimport {\r\n  sendVerificationEmailSchema,\r\n  sendPasswordResetEmailSchema,\r\n  sendCustomEmailSchema,\r\n  previewEmailTemplateSchema\r\n} from '../validators/email.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   POST /api/emails/send-verification\r\n * @desc    Send email verification\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/send-verification',\r\n  authenticate,\r\n  validateRequest(sendVerificationEmailSchema, 'body'),\r\n  sendVerificationEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/send-password-reset\r\n * @desc    Send password reset email\r\n * @access  Public\r\n */\r\nrouter.post(\r\n  '/send-password-reset',\r\n  validateRequest(sendPasswordResetEmailSchema, 'body'),\r\n  sendPasswordResetEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/send-custom\r\n * @desc    Send custom email (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.post(\r\n  '/send-custom',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  validateRequest(sendCustomEmailSchema, 'body'),\r\n  sendCustomEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/test\r\n * @desc    Test email service (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.post(\r\n  '/test',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  testEmailService\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/status\r\n * @desc    Get email service status (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.get(\r\n  '/status',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  getEmailServiceStatus\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/templates\r\n * @desc    Get available email templates\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/templates',\r\n  authenticate,\r\n  getEmailTemplates\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/preview-template\r\n * @desc    Preview email template\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preview-template',\r\n  authenticate,\r\n  validateRequest(previewEmailTemplateSchema, 'body'),\r\n  previewEmailTemplate\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/health\r\n * @desc    Health check for email service\r\n * @access  Public\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Email service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    service: 'Zoho SMTP'\r\n  });\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,kBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,OAAAC,OAAA;AASA,MAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAI,kBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAOA,MAAMK,MAAM;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAS,MAAM,GAAE;AAEvB;;;;;AAAA;AAAAR,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,oBAAoB,EACpBL,MAAA,CAAAM,YAAY,EACZ,IAAAL,YAAA,CAAAM,eAAe,EAACL,kBAAA,CAAAM,2BAA2B,EAAE,MAAM,CAAC,EACpDT,kBAAA,CAAAU,qBAAqB,CACtB;AAED;;;;;AAAA;AAAAb,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,sBAAsB,EACtB,IAAAJ,YAAA,CAAAM,eAAe,EAACL,kBAAA,CAAAQ,4BAA4B,EAAE,MAAM,CAAC,EACrDX,kBAAA,CAAAY,sBAAsB,CACvB;AAED;;;;;AAAA;AAAAf,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,cAAc,EACdL,MAAA,CAAAM,YAAY,EACZ,IAAAN,MAAA,CAAAY,WAAW,EAAC,OAAO,CAAC,EACpB,IAAAX,YAAA,CAAAM,eAAe,EAACL,kBAAA,CAAAW,qBAAqB,EAAE,MAAM,CAAC,EAC9Cd,kBAAA,CAAAe,eAAe,CAChB;AAED;;;;;AAAA;AAAAlB,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,OAAO,EACPL,MAAA,CAAAM,YAAY,EACZ,IAAAN,MAAA,CAAAY,WAAW,EAAC,OAAO,CAAC,EACpBb,kBAAA,CAAAgB,gBAAgB,CACjB;AAED;;;;;AAAA;AAAAnB,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACa,GAAG,CACR,SAAS,EACThB,MAAA,CAAAM,YAAY,EACZ,IAAAN,MAAA,CAAAY,WAAW,EAAC,OAAO,CAAC,EACpBb,kBAAA,CAAAkB,qBAAqB,CACtB;AAED;;;;;AAAA;AAAArB,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACa,GAAG,CACR,YAAY,EACZhB,MAAA,CAAAM,YAAY,EACZP,kBAAA,CAAAmB,iBAAiB,CAClB;AAED;;;;;AAAA;AAAAtB,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,mBAAmB,EACnBL,MAAA,CAAAM,YAAY,EACZ,IAAAL,YAAA,CAAAM,eAAe,EAACL,kBAAA,CAAAiB,0BAA0B,EAAE,MAAM,CAAC,EACnDpB,kBAAA,CAAAqB,oBAAoB,CACrB;AAED;;;;;AAAA;AAAAxB,aAAA,GAAAC,CAAA;AAKAM,MAAM,CAACa,GAAG,CAAC,SAAS,EAAE,CAACK,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAA1B,aAAA,GAAA2B,CAAA;EAAA3B,aAAA,GAAAC,CAAA;EACjCyB,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC;AAAC;AAAAlC,aAAA,GAAAC,CAAA;AAEHkC,OAAA,CAAAC,OAAA,GAAe7B,MAAM", "ignoreList": []}