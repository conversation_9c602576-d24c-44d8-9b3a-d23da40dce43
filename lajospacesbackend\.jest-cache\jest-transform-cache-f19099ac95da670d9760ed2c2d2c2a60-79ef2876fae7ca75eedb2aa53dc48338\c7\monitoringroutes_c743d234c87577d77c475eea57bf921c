93337f83cd8b253d6c0d45c4e03e910b
"use strict";

/* istanbul ignore next */
function cov_fgezjy1xw() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\monitoring.routes.ts";
  var hash = "1e954a7f2551e1bdea6f305560491dc0b43f695a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\monitoring.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 54
        }
      },
      "4": {
        start: {
          line: 6,
          column: 31
        },
        end: {
          line: 6,
          column: 74
        }
      },
      "5": {
        start: {
          line: 7,
          column: 39
        },
        end: {
          line: 7,
          column: 90
        }
      },
      "6": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 60
        }
      },
      "7": {
        start: {
          line: 9,
          column: 17
        },
        end: {
          line: 9,
          column: 43
        }
      },
      "8": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 10,
          column: 38
        }
      },
      "9": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 42,
          column: 3
        }
      },
      "10": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 41,
          column: 5
        }
      },
      "11": {
        start: {
          line: 18,
          column: 35
        },
        end: {
          line: 18,
          column: 107
        }
      },
      "12": {
        start: {
          line: 19,
          column: 29
        },
        end: {
          line: 19,
          column: 85
        }
      },
      "13": {
        start: {
          line: 20,
          column: 34
        },
        end: {
          line: 20,
          column: 111
        }
      },
      "14": {
        start: {
          line: 21,
          column: 28
        },
        end: {
          line: 21,
          column: 90
        }
      },
      "15": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 33,
          column: 11
        }
      },
      "16": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 92
        }
      },
      "17": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 40,
          column: 11
        }
      },
      "18": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "19": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "20": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 99
        }
      },
      "21": {
        start: {
          line: 51,
          column: 30
        },
        end: {
          line: 51,
          column: 110
        }
      },
      "22": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 59,
          column: 11
        }
      },
      "23": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 92
        }
      },
      "24": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 66,
          column: 11
        }
      },
      "25": {
        start: {
          line: 74,
          column: 0
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "26": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "27": {
        start: {
          line: 76,
          column: 22
        },
        end: {
          line: 76,
          column: 53
        }
      },
      "28": {
        start: {
          line: 77,
          column: 25
        },
        end: {
          line: 77,
          column: 43
        }
      },
      "29": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 80
        }
      },
      "30": {
        start: {
          line: 79,
          column: 29
        },
        end: {
          line: 81,
          column: 80
        }
      },
      "31": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 96,
          column: 11
        }
      },
      "32": {
        start: {
          line: 86,
          column: 57
        },
        end: {
          line: 93,
          column: 17
        }
      },
      "33": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 93
        }
      },
      "34": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 103,
          column: 11
        }
      },
      "35": {
        start: {
          line: 111,
          column: 0
        },
        end: {
          line: 152,
          column: 3
        }
      },
      "36": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "37": {
        start: {
          line: 113,
          column: 28
        },
        end: {
          line: 113,
          column: 49
        }
      },
      "38": {
        start: {
          line: 114,
          column: 25
        },
        end: {
          line: 114,
          column: 43
        }
      },
      "39": {
        start: {
          line: 115,
          column: 23
        },
        end: {
          line: 115,
          column: 39
        }
      },
      "40": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 143,
          column: 11
        }
      },
      "41": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 92
        }
      },
      "42": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 150,
          column: 11
        }
      },
      "43": {
        start: {
          line: 158,
          column: 0
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "44": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "45": {
        start: {
          line: 160,
          column: 29
        },
        end: {
          line: 160,
          column: 76
        }
      },
      "46": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 167,
          column: 11
        }
      },
      "47": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 170,
          column: 87
        }
      },
      "48": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 174,
          column: 11
        }
      },
      "49": {
        start: {
          line: 182,
          column: 0
        },
        end: {
          line: 215,
          column: 3
        }
      },
      "50": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 214,
          column: 5
        }
      },
      "51": {
        start: {
          line: 184,
          column: 25
        },
        end: {
          line: 184,
          column: 33
        }
      },
      "52": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 201,
          column: 9
        }
      },
      "53": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 83
        }
      },
      "54": {
        start: {
          line: 189,
          column: 16
        },
        end: {
          line: 189,
          column: 22
        }
      },
      "55": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 81
        }
      },
      "56": {
        start: {
          line: 192,
          column: 16
        },
        end: {
          line: 192,
          column: 22
        }
      },
      "57": {
        start: {
          line: 194,
          column: 16
        },
        end: {
          line: 194,
          column: 80
        }
      },
      "58": {
        start: {
          line: 195,
          column: 16
        },
        end: {
          line: 195,
          column: 22
        }
      },
      "59": {
        start: {
          line: 197,
          column: 16
        },
        end: {
          line: 200,
          column: 19
        }
      },
      "60": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 206,
          column: 11
        }
      },
      "61": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 104
        }
      },
      "62": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 213,
          column: 11
        }
      },
      "63": {
        start: {
          line: 221,
          column: 0
        },
        end: {
          line: 236,
          column: 3
        }
      },
      "64": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 235,
          column: 5
        }
      },
      "65": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 64
        }
      },
      "66": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 227,
          column: 11
        }
      },
      "67": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 85
        }
      },
      "68": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 234,
          column: 11
        }
      },
      "69": {
        start: {
          line: 242,
          column: 0
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "70": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 268,
          column: 5
        }
      },
      "71": {
        start: {
          line: 244,
          column: 22
        },
        end: {
          line: 244,
          column: 47
        }
      },
      "72": {
        start: {
          line: 245,
          column: 22
        },
        end: {
          line: 245,
          column: 54
        }
      },
      "73": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 260,
          column: 11
        }
      },
      "74": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 78
        }
      },
      "75": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 267,
          column: 11
        }
      },
      "76": {
        start: {
          line: 275,
          column: 0
        },
        end: {
          line: 323,
          column: 3
        }
      },
      "77": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "78": {
        start: {
          line: 277,
          column: 35
        },
        end: {
          line: 277,
          column: 107
        }
      },
      "79": {
        start: {
          line: 278,
          column: 29
        },
        end: {
          line: 278,
          column: 85
        }
      },
      "80": {
        start: {
          line: 279,
          column: 34
        },
        end: {
          line: 279,
          column: 111
        }
      },
      "81": {
        start: {
          line: 280,
          column: 28
        },
        end: {
          line: 280,
          column: 90
        }
      },
      "82": {
        start: {
          line: 281,
          column: 29
        },
        end: {
          line: 281,
          column: 76
        }
      },
      "83": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 282,
          column: 49
        }
      },
      "84": {
        start: {
          line: 284,
          column: 23
        },
        end: {
          line: 284,
          column: 39
        }
      },
      "85": {
        start: {
          line: 285,
          column: 35
        },
        end: {
          line: 285,
          column: 87
        }
      },
      "86": {
        start: {
          line: 286,
          column: 26
        },
        end: {
          line: 288,
          column: 15
        }
      },
      "87": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 314,
          column: 11
        }
      },
      "88": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 317,
          column: 88
        }
      },
      "89": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 321,
          column: 11
        }
      },
      "90": {
        start: {
          line: 324,
          column: 0
        },
        end: {
          line: 324,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 16,
            column: 59
          },
          end: {
            line: 16,
            column: 60
          }
        },
        loc: {
          start: {
            line: 16,
            column: 79
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 16
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 48,
            column: 63
          },
          end: {
            line: 48,
            column: 64
          }
        },
        loc: {
          start: {
            line: 48,
            column: 83
          },
          end: {
            line: 68,
            column: 1
          }
        },
        line: 48
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 74,
            column: 58
          },
          end: {
            line: 74,
            column: 59
          }
        },
        loc: {
          start: {
            line: 74,
            column: 78
          },
          end: {
            line: 105,
            column: 1
          }
        },
        line: 74
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 86,
            column: 47
          },
          end: {
            line: 86,
            column: 48
          }
        },
        loc: {
          start: {
            line: 86,
            column: 57
          },
          end: {
            line: 93,
            column: 17
          }
        },
        line: 86
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 111,
            column: 58
          },
          end: {
            line: 111,
            column: 59
          }
        },
        loc: {
          start: {
            line: 111,
            column: 78
          },
          end: {
            line: 152,
            column: 1
          }
        },
        line: 111
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 158,
            column: 59
          },
          end: {
            line: 158,
            column: 60
          }
        },
        loc: {
          start: {
            line: 158,
            column: 79
          },
          end: {
            line: 176,
            column: 1
          }
        },
        line: 158
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 182,
            column: 67
          },
          end: {
            line: 182,
            column: 68
          }
        },
        loc: {
          start: {
            line: 182,
            column: 87
          },
          end: {
            line: 215,
            column: 1
          }
        },
        line: 182
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 221,
            column: 68
          },
          end: {
            line: 221,
            column: 69
          }
        },
        loc: {
          start: {
            line: 221,
            column: 88
          },
          end: {
            line: 236,
            column: 1
          }
        },
        line: 221
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 242,
            column: 56
          },
          end: {
            line: 242,
            column: 57
          }
        },
        loc: {
          start: {
            line: 242,
            column: 76
          },
          end: {
            line: 269,
            column: 1
          }
        },
        line: 242
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 275,
            column: 61
          },
          end: {
            line: 275,
            column: 62
          }
        },
        loc: {
          start: {
            line: 275,
            column: 81
          },
          end: {
            line: 323,
            column: 1
          }
        },
        line: 275
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 76,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 76,
            column: 47
          }
        }, {
          start: {
            line: 76,
            column: 51
          },
          end: {
            line: 76,
            column: 53
          }
        }],
        line: 76
      },
      "1": {
        loc: {
          start: {
            line: 79,
            column: 29
          },
          end: {
            line: 81,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 14
          },
          end: {
            line: 80,
            column: 94
          }
        }, {
          start: {
            line: 81,
            column: 14
          },
          end: {
            line: 81,
            column: 80
          }
        }],
        line: 79
      },
      "2": {
        loc: {
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 201,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 187,
            column: 12
          },
          end: {
            line: 189,
            column: 22
          }
        }, {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 22
          }
        }, {
          start: {
            line: 193,
            column: 12
          },
          end: {
            line: 195,
            column: 22
          }
        }, {
          start: {
            line: 196,
            column: 12
          },
          end: {
            line: 200,
            column: 19
          }
        }],
        line: 186
      },
      "3": {
        loc: {
          start: {
            line: 244,
            column: 22
          },
          end: {
            line: 244,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 22
          },
          end: {
            line: 244,
            column: 37
          }
        }, {
          start: {
            line: 244,
            column: 41
          },
          end: {
            line: 244,
            column: 47
          }
        }],
        line: 244
      },
      "4": {
        loc: {
          start: {
            line: 245,
            column: 22
          },
          end: {
            line: 245,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 22
          },
          end: {
            line: 245,
            column: 47
          }
        }, {
          start: {
            line: 245,
            column: 51
          },
          end: {
            line: 245,
            column: 54
          }
        }],
        line: 245
      },
      "5": {
        loc: {
          start: {
            line: 286,
            column: 26
          },
          end: {
            line: 288,
            column: 15
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 287,
            column: 14
          },
          end: {
            line: 287,
            column: 92
          }
        }, {
          start: {
            line: 288,
            column: 14
          },
          end: {
            line: 288,
            column: 15
          }
        }],
        line: 286
      },
      "6": {
        loc: {
          start: {
            line: 293,
            column: 28
          },
          end: {
            line: 293,
            column: 126
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 293,
            column: 105
          },
          end: {
            line: 293,
            column: 114
          }
        }, {
          start: {
            line: 293,
            column: 117
          },
          end: {
            line: 293,
            column: 126
          }
        }],
        line: 293
      },
      "7": {
        loc: {
          start: {
            line: 293,
            column: 28
          },
          end: {
            line: 293,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 28
          },
          end: {
            line: 293,
            column: 66
          }
        }, {
          start: {
            line: 293,
            column: 70
          },
          end: {
            line: 293,
            column: 102
          }
        }],
        line: 293
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\monitoring.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,6CAA0C;AAC1C,uDAAoD;AACpD,2EAAwE;AACxE,2FAAwF;AACxF,6DAA0D;AAC1D,4CAAyC;AAEzC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACzD,IAAI,CAAC;QACH,MAAM,kBAAkB,GAAG,2DAA4B,CAAC,UAAU,EAAE,CAAC;QACrE,MAAM,YAAY,GAAG,2CAAoB,CAAC,UAAU,EAAE,CAAC;QACvD,MAAM,iBAAiB,GAAG,2DAA4B,CAAC,eAAe,EAAE,CAAC;QACzE,MAAM,WAAW,GAAG,2CAAoB,CAAC,gBAAgB,EAAE,CAAC;QAE5D,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,WAAW,EAAE,kBAAkB;gBAC/B,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE;oBACN,WAAW,EAAE,iBAAiB;oBAC9B,MAAM,EAAE,WAAW;iBACpB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uCAAuC;SAC/C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC7D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,2DAA4B,CAAC,cAAc,EAAE,CAAC;QAC7D,MAAM,aAAa,GAAG,2DAA4B,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAExE,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uCAAuC;SAC/C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,EAAE,CAAC;QACxD,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,QAAkB,CAAC;QAE9C,MAAM,OAAO,GAAG,2CAAoB,CAAC,UAAU,EAAE,CAAC;QAClD,MAAM,YAAY,GAAG,QAAQ;YAC3B,CAAC,CAAC,2CAAoB,CAAC,mBAAmB,CAAC,QAAe,EAAE,KAAK,CAAC;YAClE,CAAC,CAAC,2CAAoB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEhD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,OAAO;gBACP,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACvC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;oBACtD,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO;oBAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB,CAAC,CAAC;gBACH,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,wCAAwC;SAChD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxD,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAEhC,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,QAAQ,EAAE,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACjE,SAAS,EAAE,GAAG,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACnE,GAAG,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACvD,QAAQ,EAAE,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;oBACjE,eAAe,EAAE,CAAC,CAAC,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;iBACnF;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;iBACxB;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG;oBAC5E,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,WAAW,EAAE,OAAO,CAAC,OAAO;oBAC5B,GAAG,EAAE,OAAO,CAAC,GAAG;iBACjB;gBACD,WAAW,EAAE;oBACX,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;oBAC7B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI;oBACtB,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ;iBAC3D;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uCAAuC;SAC/C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACzD,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,6BAAa,CAAC,eAAe,EAAE,CAAC;QAErD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,YAAY;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kCAAkC;SAC1C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACjE,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE1B,IAAI,MAAM,CAAC;QACX,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,SAAS;gBACZ,MAAM,GAAG,MAAM,6BAAa,CAAC,mBAAmB,EAAE,CAAC;gBACnD,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,GAAG,MAAM,6BAAa,CAAC,iBAAiB,EAAE,CAAC;gBACjD,MAAM;YACR,KAAK,MAAM;gBACT,MAAM,GAAG,MAAM,6BAAa,CAAC,gBAAgB,EAAE,CAAC;gBAChD,MAAM;YACR;gBACE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mDAAmD;iBAC3D,CAAC,CAAC;QACP,CAAC;QAED,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,GAAG,IAAI,8BAA8B;SAC/C,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClE,IAAI,CAAC;QACH,MAAM,6BAAa,CAAC,iBAAiB,EAAE,CAAC;QAExC,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,uCAAuC;SACjD,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,2BAA2B;SACnC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAe,IAAI,MAAM,CAAC;QAClD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,GAAG,CAAC;QAEzD,sCAAsC;QACtC,iEAAiE;QACjE,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,OAAO,EAAE,+CAA+C;gBACxD,UAAU,EAAE,6DAA6D;gBACzE,QAAQ,EAAE;oBACR,eAAe;oBACf,iBAAiB;oBACjB,gBAAgB;oBAChB,sBAAsB;iBACvB;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,WAAI,EAAE,qBAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3D,IAAI,CAAC;QACH,MAAM,kBAAkB,GAAG,2DAA4B,CAAC,UAAU,EAAE,CAAC;QACrE,MAAM,YAAY,GAAG,2CAAoB,CAAC,UAAU,EAAE,CAAC;QACvD,MAAM,iBAAiB,GAAG,2DAA4B,CAAC,eAAe,EAAE,CAAC;QACzE,MAAM,WAAW,GAAG,2CAAoB,CAAC,gBAAgB,EAAE,CAAC;QAC5D,MAAM,YAAY,GAAG,6BAAa,CAAC,eAAe,EAAE,CAAC;QACrD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE1C,wBAAwB;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAChC,MAAM,kBAAkB,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAChF,MAAM,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC;YACrD,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG;YAChF,CAAC,CAAC,CAAC,CAAC;QAEN,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,QAAQ,EAAE;oBACR,MAAM,EAAE,iBAAiB,CAAC,MAAM,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;oBAC1G,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG;oBAC5E,WAAW,EAAE,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;oBAChD,SAAS,EAAE,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;oBACrC,WAAW,EAAE,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,MAAM;oBACtD,mBAAmB,EAAE,GAAG,kBAAkB,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;iBAC/E;gBACD,WAAW,EAAE;oBACX,MAAM,EAAE,iBAAiB;oBACzB,OAAO,EAAE,kBAAkB;iBAC5B;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,YAAY;iBACtB;gBACD,OAAO,EAAE;oBACP,OAAO,EAAE,YAAY,CAAC,OAAO;oBAC7B,UAAU,EAAE,YAAY,CAAC,OAAO,CAAC,UAAU;iBAC5C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,mCAAmC;SAC3C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\monitoring.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport { auth } from '../middleware/auth';\r\nimport { adminAuth } from '../middleware/adminAuth';\r\nimport { errorTrackingService } from '../services/errorTrackingService';\r\nimport { performanceMonitoringService } from '../services/performanceMonitoringService';\r\nimport { backupService } from '../services/backupService';\r\nimport { logger } from '../utils/logger';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route GET /api/monitoring/metrics\r\n * @desc Get performance metrics (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/metrics', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const performanceMetrics = performanceMonitoringService.getMetrics();\r\n    const errorMetrics = errorTrackingService.getMetrics();\r\n    const performanceHealth = performanceMonitoringService.getHealthStatus();\r\n    const errorHealth = errorTrackingService.getHealthSummary();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        performance: performanceMetrics,\r\n        errors: errorMetrics,\r\n        health: {\r\n          performance: performanceHealth,\r\n          errors: errorHealth\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get monitoring metrics', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve monitoring metrics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/performance\r\n * @desc Get detailed performance report (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/performance', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const report = performanceMonitoringService.generateReport();\r\n    const slowEndpoints = performanceMonitoringService.getSlowEndpoints(10);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        report,\r\n        slowEndpoints,\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get performance report', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve performance report'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/errors\r\n * @desc Get error tracking information (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/errors', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const limit = parseInt(req.query.limit as string) || 50;\r\n    const category = req.query.category as string;\r\n\r\n    const metrics = errorTrackingService.getMetrics();\r\n    const recentErrors = category \r\n      ? errorTrackingService.getErrorsByCategory(category as any, limit)\r\n      : errorTrackingService.getRecentErrors(limit);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        metrics,\r\n        recentErrors: recentErrors.map(error => ({\r\n          id: error.error.name + '_' + error.timestamp.getTime(),\r\n          message: error.error.message,\r\n          severity: error.severity,\r\n          category: error.category,\r\n          timestamp: error.timestamp,\r\n          context: error.context\r\n        })),\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get error tracking data', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve error tracking data'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/system\r\n * @desc Get system information (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/system', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const memoryUsage = process.memoryUsage();\r\n    const cpuUsage = process.cpuUsage();\r\n    const uptime = process.uptime();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        memory: {\r\n          heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n          heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,\r\n          rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,\r\n          external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,\r\n          heapUsedPercent: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(2)\r\n        },\r\n        cpu: {\r\n          user: cpuUsage.user,\r\n          system: cpuUsage.system\r\n        },\r\n        system: {\r\n          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m`,\r\n          platform: process.platform,\r\n          nodeVersion: process.version,\r\n          pid: process.pid\r\n        },\r\n        environment: {\r\n          nodeEnv: process.env.NODE_ENV,\r\n          port: process.env.PORT,\r\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get system information', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve system information'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/backups\r\n * @desc Get backup status and history (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/backups', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const backupStatus = backupService.getBackupStatus();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...backupStatus,\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get backup status', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve backup status'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route POST /api/monitoring/backups/create\r\n * @desc Create a new backup (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.post('/backups/create', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const { type } = req.body;\r\n\r\n    let result;\r\n    switch (type) {\r\n      case 'mongodb':\r\n        result = await backupService.createMongoDBBackup();\r\n        break;\r\n      case 'files':\r\n        result = await backupService.createFilesBackup();\r\n        break;\r\n      case 'full':\r\n        result = await backupService.createFullBackup();\r\n        break;\r\n      default:\r\n        return res.status(400).json({\r\n          success: false,\r\n          error: 'Invalid backup type. Use: mongodb, files, or full'\r\n        });\r\n    }\r\n\r\n    res.json({\r\n      success: true,\r\n      data: result,\r\n      message: `${type} backup created successfully`\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to create backup', { error: error.message, type: req.body.type });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to create backup'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route POST /api/monitoring/backups/cleanup\r\n * @desc Clean up old backups (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.post('/backups/cleanup', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    await backupService.cleanupOldBackups();\r\n\r\n    res.json({\r\n      success: true,\r\n      message: 'Backup cleanup completed successfully'\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to cleanup backups', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to cleanup backups'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/logs\r\n * @desc Get recent log entries (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/logs', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const level = req.query.level as string || 'info';\r\n    const limit = parseInt(req.query.limit as string) || 100;\r\n\r\n    // This is a simplified implementation\r\n    // In production, you might want to use a log aggregation service\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        message: 'Log retrieval not implemented in this version',\r\n        suggestion: 'Use log files directly or implement log aggregation service',\r\n        logFiles: [\r\n          '/logs/app.log',\r\n          '/logs/error.log',\r\n          '/logs/http.log',\r\n          '/logs/exceptions.log'\r\n        ]\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get logs', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve logs'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/dashboard\r\n * @desc Get monitoring dashboard data (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/dashboard', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const performanceMetrics = performanceMonitoringService.getMetrics();\r\n    const errorMetrics = errorTrackingService.getMetrics();\r\n    const performanceHealth = performanceMonitoringService.getHealthStatus();\r\n    const errorHealth = errorTrackingService.getHealthSummary();\r\n    const backupStatus = backupService.getBackupStatus();\r\n    const memoryUsage = process.memoryUsage();\r\n\r\n    // Calculate key metrics\r\n    const uptime = process.uptime();\r\n    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;\r\n    const errorRate = performanceMetrics.requests.total > 0 \r\n      ? (performanceMetrics.requests.failed / performanceMetrics.requests.total) * 100 \r\n      : 0;\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        overview: {\r\n          status: performanceHealth.status === 'healthy' && errorHealth.status === 'healthy' ? 'healthy' : 'warning',\r\n          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m`,\r\n          memoryUsage: `${memoryUsagePercent.toFixed(1)}%`,\r\n          errorRate: `${errorRate.toFixed(2)}%`,\r\n          requestRate: `${performanceMetrics.requests.rate}/min`,\r\n          averageResponseTime: `${performanceMetrics.responseTime.average.toFixed(0)}ms`\r\n        },\r\n        performance: {\r\n          health: performanceHealth,\r\n          metrics: performanceMetrics\r\n        },\r\n        errors: {\r\n          health: errorHealth,\r\n          metrics: errorMetrics\r\n        },\r\n        backups: {\r\n          summary: backupStatus.summary,\r\n          lastBackup: backupStatus.summary.lastBackup\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get dashboard data', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve dashboard data'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1e954a7f2551e1bdea6f305560491dc0b43f695a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_fgezjy1xw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_fgezjy1xw();
cov_fgezjy1xw().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[1]++, require("express"));
const auth_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[2]++, require("../middleware/auth"));
const adminAuth_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[3]++, require("../middleware/adminAuth"));
const errorTrackingService_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[4]++, require("../services/errorTrackingService"));
const performanceMonitoringService_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[5]++, require("../services/performanceMonitoringService"));
const backupService_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[6]++, require("../services/backupService"));
const logger_1 =
/* istanbul ignore next */
(cov_fgezjy1xw().s[7]++, require("../utils/logger"));
const router =
/* istanbul ignore next */
(cov_fgezjy1xw().s[8]++, (0, express_1.Router)());
/**
 * @route GET /api/monitoring/metrics
 * @desc Get performance metrics (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[9]++;
router.get('/metrics', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[0]++;
  cov_fgezjy1xw().s[10]++;
  try {
    const performanceMetrics =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[11]++, performanceMonitoringService_1.performanceMonitoringService.getMetrics());
    const errorMetrics =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[12]++, errorTrackingService_1.errorTrackingService.getMetrics());
    const performanceHealth =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[13]++, performanceMonitoringService_1.performanceMonitoringService.getHealthStatus());
    const errorHealth =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[14]++, errorTrackingService_1.errorTrackingService.getHealthSummary());
    /* istanbul ignore next */
    cov_fgezjy1xw().s[15]++;
    res.json({
      success: true,
      data: {
        performance: performanceMetrics,
        errors: errorMetrics,
        health: {
          performance: performanceHealth,
          errors: errorHealth
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[16]++;
    logger_1.logger.error('Failed to get monitoring metrics', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[17]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve monitoring metrics'
    });
  }
});
/**
 * @route GET /api/monitoring/performance
 * @desc Get detailed performance report (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[18]++;
router.get('/performance', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[1]++;
  cov_fgezjy1xw().s[19]++;
  try {
    const report =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[20]++, performanceMonitoringService_1.performanceMonitoringService.generateReport());
    const slowEndpoints =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[21]++, performanceMonitoringService_1.performanceMonitoringService.getSlowEndpoints(10));
    /* istanbul ignore next */
    cov_fgezjy1xw().s[22]++;
    res.json({
      success: true,
      data: {
        report,
        slowEndpoints,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[23]++;
    logger_1.logger.error('Failed to get performance report', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[24]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance report'
    });
  }
});
/**
 * @route GET /api/monitoring/errors
 * @desc Get error tracking information (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[25]++;
router.get('/errors', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[2]++;
  cov_fgezjy1xw().s[26]++;
  try {
    const limit =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[27]++,
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[0][0]++, parseInt(req.query.limit)) ||
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[0][1]++, 50));
    const category =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[28]++, req.query.category);
    const metrics =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[29]++, errorTrackingService_1.errorTrackingService.getMetrics());
    const recentErrors =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[30]++, category ?
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[1][0]++, errorTrackingService_1.errorTrackingService.getErrorsByCategory(category, limit)) :
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[1][1]++, errorTrackingService_1.errorTrackingService.getRecentErrors(limit)));
    /* istanbul ignore next */
    cov_fgezjy1xw().s[31]++;
    res.json({
      success: true,
      data: {
        metrics,
        recentErrors: recentErrors.map(error => {
          /* istanbul ignore next */
          cov_fgezjy1xw().f[3]++;
          cov_fgezjy1xw().s[32]++;
          return {
            id: error.error.name + '_' + error.timestamp.getTime(),
            message: error.error.message,
            severity: error.severity,
            category: error.category,
            timestamp: error.timestamp,
            context: error.context
          };
        }),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[33]++;
    logger_1.logger.error('Failed to get error tracking data', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[34]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve error tracking data'
    });
  }
});
/**
 * @route GET /api/monitoring/system
 * @desc Get system information (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[35]++;
router.get('/system', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[4]++;
  cov_fgezjy1xw().s[36]++;
  try {
    const memoryUsage =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[37]++, process.memoryUsage());
    const cpuUsage =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[38]++, process.cpuUsage());
    const uptime =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[39]++, process.uptime());
    /* istanbul ignore next */
    cov_fgezjy1xw().s[40]++;
    res.json({
      success: true,
      data: {
        memory: {
          heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
          heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
          external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
          heapUsedPercent: (memoryUsage.heapUsed / memoryUsage.heapTotal * 100).toFixed(2)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        system: {
          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor(uptime % 3600 / 60)}m`,
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        },
        environment: {
          nodeEnv: process.env.NODE_ENV,
          port: process.env.PORT,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[41]++;
    logger_1.logger.error('Failed to get system information', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[42]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system information'
    });
  }
});
/**
 * @route GET /api/monitoring/backups
 * @desc Get backup status and history (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[43]++;
router.get('/backups', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[5]++;
  cov_fgezjy1xw().s[44]++;
  try {
    const backupStatus =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[45]++, backupService_1.backupService.getBackupStatus());
    /* istanbul ignore next */
    cov_fgezjy1xw().s[46]++;
    res.json({
      success: true,
      data: {
        ...backupStatus,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[47]++;
    logger_1.logger.error('Failed to get backup status', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[48]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve backup status'
    });
  }
});
/**
 * @route POST /api/monitoring/backups/create
 * @desc Create a new backup (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[49]++;
router.post('/backups/create', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[6]++;
  cov_fgezjy1xw().s[50]++;
  try {
    const {
      type
    } =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[51]++, req.body);
    let result;
    /* istanbul ignore next */
    cov_fgezjy1xw().s[52]++;
    switch (type) {
      case 'mongodb':
        /* istanbul ignore next */
        cov_fgezjy1xw().b[2][0]++;
        cov_fgezjy1xw().s[53]++;
        result = await backupService_1.backupService.createMongoDBBackup();
        /* istanbul ignore next */
        cov_fgezjy1xw().s[54]++;
        break;
      case 'files':
        /* istanbul ignore next */
        cov_fgezjy1xw().b[2][1]++;
        cov_fgezjy1xw().s[55]++;
        result = await backupService_1.backupService.createFilesBackup();
        /* istanbul ignore next */
        cov_fgezjy1xw().s[56]++;
        break;
      case 'full':
        /* istanbul ignore next */
        cov_fgezjy1xw().b[2][2]++;
        cov_fgezjy1xw().s[57]++;
        result = await backupService_1.backupService.createFullBackup();
        /* istanbul ignore next */
        cov_fgezjy1xw().s[58]++;
        break;
      default:
        /* istanbul ignore next */
        cov_fgezjy1xw().b[2][3]++;
        cov_fgezjy1xw().s[59]++;
        return res.status(400).json({
          success: false,
          error: 'Invalid backup type. Use: mongodb, files, or full'
        });
    }
    /* istanbul ignore next */
    cov_fgezjy1xw().s[60]++;
    res.json({
      success: true,
      data: result,
      message: `${type} backup created successfully`
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[61]++;
    logger_1.logger.error('Failed to create backup', {
      error: error.message,
      type: req.body.type
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[62]++;
    res.status(500).json({
      success: false,
      error: 'Failed to create backup'
    });
  }
});
/**
 * @route POST /api/monitoring/backups/cleanup
 * @desc Clean up old backups (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[63]++;
router.post('/backups/cleanup', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[7]++;
  cov_fgezjy1xw().s[64]++;
  try {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[65]++;
    await backupService_1.backupService.cleanupOldBackups();
    /* istanbul ignore next */
    cov_fgezjy1xw().s[66]++;
    res.json({
      success: true,
      message: 'Backup cleanup completed successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[67]++;
    logger_1.logger.error('Failed to cleanup backups', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[68]++;
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup backups'
    });
  }
});
/**
 * @route GET /api/monitoring/logs
 * @desc Get recent log entries (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[69]++;
router.get('/logs', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[8]++;
  cov_fgezjy1xw().s[70]++;
  try {
    const level =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[71]++,
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[3][0]++, req.query.level) ||
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[3][1]++, 'info'));
    const limit =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[72]++,
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[4][0]++, parseInt(req.query.limit)) ||
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[4][1]++, 100));
    // This is a simplified implementation
    // In production, you might want to use a log aggregation service
    /* istanbul ignore next */
    cov_fgezjy1xw().s[73]++;
    res.json({
      success: true,
      data: {
        message: 'Log retrieval not implemented in this version',
        suggestion: 'Use log files directly or implement log aggregation service',
        logFiles: ['/logs/app.log', '/logs/error.log', '/logs/http.log', '/logs/exceptions.log']
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[74]++;
    logger_1.logger.error('Failed to get logs', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[75]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve logs'
    });
  }
});
/**
 * @route GET /api/monitoring/dashboard
 * @desc Get monitoring dashboard data (admin only)
 * @access Private/Admin
 */
/* istanbul ignore next */
cov_fgezjy1xw().s[76]++;
router.get('/dashboard', auth_1.auth, adminAuth_1.adminAuth, async (req, res) => {
  /* istanbul ignore next */
  cov_fgezjy1xw().f[9]++;
  cov_fgezjy1xw().s[77]++;
  try {
    const performanceMetrics =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[78]++, performanceMonitoringService_1.performanceMonitoringService.getMetrics());
    const errorMetrics =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[79]++, errorTrackingService_1.errorTrackingService.getMetrics());
    const performanceHealth =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[80]++, performanceMonitoringService_1.performanceMonitoringService.getHealthStatus());
    const errorHealth =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[81]++, errorTrackingService_1.errorTrackingService.getHealthSummary());
    const backupStatus =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[82]++, backupService_1.backupService.getBackupStatus());
    const memoryUsage =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[83]++, process.memoryUsage());
    // Calculate key metrics
    const uptime =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[84]++, process.uptime());
    const memoryUsagePercent =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[85]++, memoryUsage.heapUsed / memoryUsage.heapTotal * 100);
    const errorRate =
    /* istanbul ignore next */
    (cov_fgezjy1xw().s[86]++, performanceMetrics.requests.total > 0 ?
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[5][0]++, performanceMetrics.requests.failed / performanceMetrics.requests.total * 100) :
    /* istanbul ignore next */
    (cov_fgezjy1xw().b[5][1]++, 0));
    /* istanbul ignore next */
    cov_fgezjy1xw().s[87]++;
    res.json({
      success: true,
      data: {
        overview: {
          status:
          /* istanbul ignore next */
          (cov_fgezjy1xw().b[7][0]++, performanceHealth.status === 'healthy') &&
          /* istanbul ignore next */
          (cov_fgezjy1xw().b[7][1]++, errorHealth.status === 'healthy') ?
          /* istanbul ignore next */
          (cov_fgezjy1xw().b[6][0]++, 'healthy') :
          /* istanbul ignore next */
          (cov_fgezjy1xw().b[6][1]++, 'warning'),
          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor(uptime % 3600 / 60)}m`,
          memoryUsage: `${memoryUsagePercent.toFixed(1)}%`,
          errorRate: `${errorRate.toFixed(2)}%`,
          requestRate: `${performanceMetrics.requests.rate}/min`,
          averageResponseTime: `${performanceMetrics.responseTime.average.toFixed(0)}ms`
        },
        performance: {
          health: performanceHealth,
          metrics: performanceMetrics
        },
        errors: {
          health: errorHealth,
          metrics: errorMetrics
        },
        backups: {
          summary: backupStatus.summary,
          lastBackup: backupStatus.summary.lastBackup
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_fgezjy1xw().s[88]++;
    logger_1.logger.error('Failed to get dashboard data', {
      error: error.message
    });
    /* istanbul ignore next */
    cov_fgezjy1xw().s[89]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve dashboard data'
    });
  }
});
/* istanbul ignore next */
cov_fgezjy1xw().s[90]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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