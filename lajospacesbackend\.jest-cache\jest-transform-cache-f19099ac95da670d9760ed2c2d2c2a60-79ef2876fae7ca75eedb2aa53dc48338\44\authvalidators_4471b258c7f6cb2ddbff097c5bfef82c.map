{"version": 3, "names": ["cov_6lpkz6xfi", "actualCoverage", "exports", "validateRequest", "joi_1", "s", "__importDefault", "require", "passwordSchema", "default", "string", "min", "max", "pattern", "required", "messages", "emailSchema", "email", "tlds", "allow", "lowercase", "phoneSchema", "nameSchema", "trim", "dateOfBirthSchema", "date", "Date", "getFullYear", "custom", "value", "helpers", "f", "age", "monthDiff", "getMonth", "dayDiff", "getDate", "actualAge", "b", "error", "registerSchema", "object", "password", "firstName", "lastName", "dateOfBirth", "gender", "valid", "phoneNumber", "optional", "accountType", "location", "city", "state", "country", "agreeToTerms", "boolean", "loginSchema", "rememberMe", "forgotPasswordSchema", "resetPasswordSchema", "token", "confirmPassword", "ref", "changePasswordSchema", "currentPassword", "newPassword", "verifyEmailSchema", "resendVerificationSchema", "refreshTokenSchema", "refreshToken", "updateProfileSchema", "preferences", "emailNotifications", "pushNotifications", "smsNotifications", "marketingEmails", "schema", "req", "res", "next", "validate", "body", "abort<PERSON><PERSON><PERSON>", "stripUnknown", "convert", "errorMessages", "details", "map", "detail", "field", "path", "join", "message", "status", "json", "success", "code", "statusCode", "timestamp", "toISOString", "method"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\auth.validators.ts"], "sourcesContent": ["import Jo<PERSON> from 'joi';\r\n\r\n// Password validation schema\r\nconst passwordSchema = Joi.string()\r\n  .min(8)\r\n  .max(128)\r\n  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)\r\n  .required()\r\n  .messages({\r\n    'string.min': 'Password must be at least 8 characters long',\r\n    'string.max': 'Password cannot exceed 128 characters',\r\n    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',\r\n    'any.required': 'Password is required'\r\n  });\r\n\r\n// Email validation schema\r\nconst emailSchema = Joi.string()\r\n  .email({ tlds: { allow: false } })\r\n  .lowercase()\r\n  .required()\r\n  .messages({\r\n    'string.email': 'Please provide a valid email address',\r\n    'any.required': 'Email is required'\r\n  });\r\n\r\n// Nigerian phone number validation\r\nconst phoneSchema = Joi.string()\r\n  .pattern(/^(\\+234|234|0)?[789][01]\\d{8}$/)\r\n  .messages({\r\n    'string.pattern.base': 'Please provide a valid Nigerian phone number (e.g., +2348012345678, 08012345678)'\r\n  });\r\n\r\n// Name validation schema\r\nconst nameSchema = Joi.string()\r\n  .min(2)\r\n  .max(50)\r\n  .pattern(/^[a-zA-Z\\s'-]+$/)\r\n  .trim()\r\n  .required()\r\n  .messages({\r\n    'string.min': 'Name must be at least 2 characters long',\r\n    'string.max': 'Name cannot exceed 50 characters',\r\n    'string.pattern.base': 'Name can only contain letters, spaces, hyphens, and apostrophes',\r\n    'any.required': 'Name is required'\r\n  });\r\n\r\n// Date of birth validation (18-100 years old)\r\nconst dateOfBirthSchema = Joi.date()\r\n  .max('now')\r\n  .min(new Date(new Date().getFullYear() - 100, 0, 1))\r\n  .custom((value, helpers) => {\r\n    const age = new Date().getFullYear() - value.getFullYear();\r\n    const monthDiff = new Date().getMonth() - value.getMonth();\r\n    const dayDiff = new Date().getDate() - value.getDate();\r\n    \r\n    let actualAge = age;\r\n    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {\r\n      actualAge--;\r\n    }\r\n    \r\n    if (actualAge < 18) {\r\n      return helpers.error('date.min');\r\n    }\r\n    \r\n    return value;\r\n  })\r\n  .required()\r\n  .messages({\r\n    'date.max': 'Date of birth cannot be in the future',\r\n    'date.min': 'You must be at least 18 years old',\r\n    'any.required': 'Date of birth is required'\r\n  });\r\n\r\n/**\r\n * User registration validation schema\r\n */\r\nexport const registerSchema = Joi.object({\r\n  email: emailSchema,\r\n  password: passwordSchema,\r\n  firstName: nameSchema,\r\n  lastName: nameSchema,\r\n  dateOfBirth: dateOfBirthSchema,\r\n  gender: Joi.string()\r\n    .valid('male', 'female', 'non-binary', 'prefer-not-to-say')\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Gender must be one of: male, female, non-binary, prefer-not-to-say',\r\n      'any.required': 'Gender is required'\r\n    }),\r\n  phoneNumber: phoneSchema.optional(),\r\n  accountType: Joi.string()\r\n    .valid('seeker', 'owner', 'both')\r\n    .default('seeker')\r\n    .messages({\r\n      'any.only': 'Account type must be one of: seeker, owner, both'\r\n    }),\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().trim().max(100).optional(),\r\n    country: Joi.string().trim().max(100).default('Nigeria')\r\n  }).optional(),\r\n  agreeToTerms: Joi.boolean()\r\n    .valid(true)\r\n    .required()\r\n    .messages({\r\n      'any.only': 'You must agree to the terms and conditions',\r\n      'any.required': 'Agreement to terms is required'\r\n    })\r\n});\r\n\r\n/**\r\n * User login validation schema\r\n */\r\nexport const loginSchema = Joi.object({\r\n  email: emailSchema,\r\n  password: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Password is required'\r\n    }),\r\n  rememberMe: Joi.boolean().default(false)\r\n});\r\n\r\n/**\r\n * Forgot password validation schema\r\n */\r\nexport const forgotPasswordSchema = Joi.object({\r\n  email: emailSchema\r\n});\r\n\r\n/**\r\n * Reset password validation schema\r\n */\r\nexport const resetPasswordSchema = Joi.object({\r\n  token: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Reset token is required'\r\n    }),\r\n  password: passwordSchema,\r\n  confirmPassword: Joi.string()\r\n    .valid(Joi.ref('password'))\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Passwords do not match',\r\n      'any.required': 'Password confirmation is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Change password validation schema\r\n */\r\nexport const changePasswordSchema = Joi.object({\r\n  currentPassword: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Current password is required'\r\n    }),\r\n  newPassword: passwordSchema,\r\n  confirmPassword: Joi.string()\r\n    .valid(Joi.ref('newPassword'))\r\n    .required()\r\n    .messages({\r\n      'any.only': 'Passwords do not match',\r\n      'any.required': 'Password confirmation is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Email verification validation schema\r\n */\r\nexport const verifyEmailSchema = Joi.object({\r\n  token: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Verification token is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Resend verification email schema\r\n */\r\nexport const resendVerificationSchema = Joi.object({\r\n  email: emailSchema\r\n});\r\n\r\n/**\r\n * Refresh token validation schema\r\n */\r\nexport const refreshTokenSchema = Joi.object({\r\n  refreshToken: Joi.string()\r\n    .required()\r\n    .messages({\r\n      'any.required': 'Refresh token is required'\r\n    })\r\n});\r\n\r\n/**\r\n * Update profile validation schema\r\n */\r\nexport const updateProfileSchema = Joi.object({\r\n  firstName: nameSchema.optional(),\r\n  lastName: nameSchema.optional(),\r\n  phoneNumber: phoneSchema.optional().allow(''),\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().trim().max(100).optional(),\r\n    country: Joi.string().trim().max(100).optional()\r\n  }).optional(),\r\n  preferences: Joi.object({\r\n    emailNotifications: Joi.boolean().optional(),\r\n    pushNotifications: Joi.boolean().optional(),\r\n    smsNotifications: Joi.boolean().optional(),\r\n    marketingEmails: Joi.boolean().optional()\r\n  }).optional()\r\n});\r\n\r\n/**\r\n * Validation middleware factory\r\n */\r\nexport function validateRequest(schema: Joi.ObjectSchema) {\r\n  return (req: any, res: any, next: any) => {\r\n    const { error, value } = schema.validate(req.body, {\r\n      abortEarly: false,\r\n      stripUnknown: true,\r\n      convert: true\r\n    });\r\n\r\n    if (error) {\r\n      const errorMessages = error.details.map(detail => ({\r\n        field: detail.path.join('.'),\r\n        message: detail.message\r\n      }));\r\n\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: {\r\n          message: 'Validation failed',\r\n          code: 'VALIDATION_ERROR',\r\n          statusCode: 400,\r\n          timestamp: new Date().toISOString(),\r\n          path: req.path,\r\n          method: req.method,\r\n          details: errorMessages\r\n        }\r\n      });\r\n    }\r\n\r\n    // Replace req.body with validated and sanitized data\r\n    req.body = value;\r\n    next();\r\n  };\r\n}\r\n\r\nexport default {\r\n  registerSchema,\r\n  loginSchema,\r\n  forgotPasswordSchema,\r\n  resetPasswordSchema,\r\n  changePasswordSchema,\r\n  verifyEmailSchema,\r\n  resendVerificationSchema,\r\n  refreshTokenSchema,\r\n  updateProfileSchema,\r\n  validateRequest\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASI;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmNJE,OAAA,CAAAC,eAAA,GAAAA,eAAA;AA5NA,MAAAC,KAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AAEA;AACA,MAAMC,cAAc;AAAA;AAAA,CAAAR,aAAA,GAAAK,CAAA,OAAGD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAChCC,GAAG,CAAC,CAAC,CAAC,CACNC,GAAG,CAAC,GAAG,CAAC,CACRC,OAAO,CAAC,iEAAiE,CAAC,CAC1EC,QAAQ,EAAE,CACVC,QAAQ,CAAC;EACR,YAAY,EAAE,6CAA6C;EAC3D,YAAY,EAAE,uCAAuC;EACrD,qBAAqB,EAAE,kHAAkH;EACzI,cAAc,EAAE;CACjB,CAAC;AAEJ;AACA,MAAMC,WAAW;AAAA;AAAA,CAAAhB,aAAA,GAAAK,CAAA,OAAGD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC7BO,KAAK,CAAC;EAAEC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AAAE,CAAE,CAAC,CACjCC,SAAS,EAAE,CACXN,QAAQ,EAAE,CACVC,QAAQ,CAAC;EACR,cAAc,EAAE,sCAAsC;EACtD,cAAc,EAAE;CACjB,CAAC;AAEJ;AACA,MAAMM,WAAW;AAAA;AAAA,CAAArB,aAAA,GAAAK,CAAA,OAAGD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC7BG,OAAO,CAAC,gCAAgC,CAAC,CACzCE,QAAQ,CAAC;EACR,qBAAqB,EAAE;CACxB,CAAC;AAEJ;AACA,MAAMO,UAAU;AAAA;AAAA,CAAAtB,aAAA,GAAAK,CAAA,OAAGD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC5BC,GAAG,CAAC,CAAC,CAAC,CACNC,GAAG,CAAC,EAAE,CAAC,CACPC,OAAO,CAAC,iBAAiB,CAAC,CAC1BU,IAAI,EAAE,CACNT,QAAQ,EAAE,CACVC,QAAQ,CAAC;EACR,YAAY,EAAE,yCAAyC;EACvD,YAAY,EAAE,kCAAkC;EAChD,qBAAqB,EAAE,iEAAiE;EACxF,cAAc,EAAE;CACjB,CAAC;AAEJ;AACA,MAAMS,iBAAiB;AAAA;AAAA,CAAAxB,aAAA,GAAAK,CAAA,QAAGD,KAAA,CAAAK,OAAG,CAACgB,IAAI,EAAE,CACjCb,GAAG,CAAC,KAAK,CAAC,CACVD,GAAG,CAAC,IAAIe,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACC,WAAW,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CACnDC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;EAAA;EAAA9B,aAAA,GAAA+B,CAAA;EACzB,MAAMC,GAAG;EAAA;EAAA,CAAAhC,aAAA,GAAAK,CAAA,QAAG,IAAIqB,IAAI,EAAE,CAACC,WAAW,EAAE,GAAGE,KAAK,CAACF,WAAW,EAAE;EAC1D,MAAMM,SAAS;EAAA;EAAA,CAAAjC,aAAA,GAAAK,CAAA,QAAG,IAAIqB,IAAI,EAAE,CAACQ,QAAQ,EAAE,GAAGL,KAAK,CAACK,QAAQ,EAAE;EAC1D,MAAMC,OAAO;EAAA;EAAA,CAAAnC,aAAA,GAAAK,CAAA,QAAG,IAAIqB,IAAI,EAAE,CAACU,OAAO,EAAE,GAAGP,KAAK,CAACO,OAAO,EAAE;EAEtD,IAAIC,SAAS;EAAA;EAAA,CAAArC,aAAA,GAAAK,CAAA,QAAG2B,GAAG;EAAC;EAAAhC,aAAA,GAAAK,CAAA;EACpB;EAAI;EAAA,CAAAL,aAAA,GAAAsC,CAAA,UAAAL,SAAS,GAAG,CAAC;EAAK;EAAA,CAAAjC,aAAA,GAAAsC,CAAA,UAAAL,SAAS,KAAK,CAAC;EAAA;EAAA,CAAAjC,aAAA,GAAAsC,CAAA,UAAIH,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA;IAAAnC,aAAA,GAAAsC,CAAA;IAAAtC,aAAA,GAAAK,CAAA;IACrDgC,SAAS,EAAE;EACb,CAAC;EAAA;EAAA;IAAArC,aAAA,GAAAsC,CAAA;EAAA;EAAAtC,aAAA,GAAAK,CAAA;EAED,IAAIgC,SAAS,GAAG,EAAE,EAAE;IAAA;IAAArC,aAAA,GAAAsC,CAAA;IAAAtC,aAAA,GAAAK,CAAA;IAClB,OAAOyB,OAAO,CAACS,KAAK,CAAC,UAAU,CAAC;EAClC,CAAC;EAAA;EAAA;IAAAvC,aAAA,GAAAsC,CAAA;EAAA;EAAAtC,aAAA,GAAAK,CAAA;EAED,OAAOwB,KAAK;AACd,CAAC,CAAC,CACDf,QAAQ,EAAE,CACVC,QAAQ,CAAC;EACR,UAAU,EAAE,uCAAuC;EACnD,UAAU,EAAE,mCAAmC;EAC/C,cAAc,EAAE;CACjB,CAAC;AAEJ;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAsC,cAAc,GAAGpC,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EACvCxB,KAAK,EAAED,WAAW;EAClB0B,QAAQ,EAAElC,cAAc;EACxBmC,SAAS,EAAErB,UAAU;EACrBsB,QAAQ,EAAEtB,UAAU;EACpBuB,WAAW,EAAErB,iBAAiB;EAC9BsB,MAAM,EAAE1C,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CACjBqC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,mBAAmB,CAAC,CAC1DjC,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,UAAU,EAAE,oEAAoE;IAChF,cAAc,EAAE;GACjB,CAAC;EACJiC,WAAW,EAAE3B,WAAW,CAAC4B,QAAQ,EAAE;EACnCC,WAAW,EAAE9C,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CACtBqC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAChCtC,OAAO,CAAC,QAAQ,CAAC,CACjBM,QAAQ,CAAC;IACR,UAAU,EAAE;GACb,CAAC;EACJoC,QAAQ,EAAE/C,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;IACnBW,IAAI,EAAEhD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACqC,QAAQ,EAAE;IAC7CI,KAAK,EAAEjD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACqC,QAAQ,EAAE;IAC9CK,OAAO,EAAElD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACH,OAAO,CAAC,SAAS;GACxD,CAAC,CAACwC,QAAQ,EAAE;EACbM,YAAY,EAAEnD,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CACxBT,KAAK,CAAC,IAAI,CAAC,CACXjC,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,UAAU,EAAE,4CAA4C;IACxD,cAAc,EAAE;GACjB;CACJ,CAAC;AAEF;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAuD,WAAW,GAAGrD,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EACpCxB,KAAK,EAAED,WAAW;EAClB0B,QAAQ,EAAEtC,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CACnBI,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,cAAc,EAAE;GACjB,CAAC;EACJ2C,UAAU,EAAEtD,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CAAC/C,OAAO,CAAC,KAAK;CACxC,CAAC;AAEF;;;AAAA;AAAAT,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAyD,oBAAoB,GAAGvD,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC7CxB,KAAK,EAAED;CACR,CAAC;AAEF;;;AAAA;AAAAhB,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAA0D,mBAAmB,GAAGxD,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC5CoB,KAAK,EAAEzD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAChBI,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,cAAc,EAAE;GACjB,CAAC;EACJ2B,QAAQ,EAAElC,cAAc;EACxBsD,eAAe,EAAE1D,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC1BqC,KAAK,CAAC3C,KAAA,CAAAK,OAAG,CAACsD,GAAG,CAAC,UAAU,CAAC,CAAC,CAC1BjD,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,UAAU,EAAE,wBAAwB;IACpC,cAAc,EAAE;GACjB;CACJ,CAAC;AAEF;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAA8D,oBAAoB,GAAG5D,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC7CwB,eAAe,EAAE7D,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC1BI,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,cAAc,EAAE;GACjB,CAAC;EACJmD,WAAW,EAAE1D,cAAc;EAC3BsD,eAAe,EAAE1D,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAC1BqC,KAAK,CAAC3C,KAAA,CAAAK,OAAG,CAACsD,GAAG,CAAC,aAAa,CAAC,CAAC,CAC7BjD,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,UAAU,EAAE,wBAAwB;IACpC,cAAc,EAAE;GACjB;CACJ,CAAC;AAEF;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAiE,iBAAiB,GAAG/D,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC1CoB,KAAK,EAAEzD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAChBI,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,cAAc,EAAE;GACjB;CACJ,CAAC;AAEF;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAkE,wBAAwB,GAAGhE,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EACjDxB,KAAK,EAAED;CACR,CAAC;AAEF;;;AAAA;AAAAhB,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAmE,kBAAkB,GAAGjE,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC3C6B,YAAY,EAAElE,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CACvBI,QAAQ,EAAE,CACVC,QAAQ,CAAC;IACR,cAAc,EAAE;GACjB;CACJ,CAAC;AAEF;;;AAAA;AAAAf,aAAA,GAAAK,CAAA;AAGaH,OAAA,CAAAqE,mBAAmB,GAAGnE,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;EAC5CE,SAAS,EAAErB,UAAU,CAAC2B,QAAQ,EAAE;EAChCL,QAAQ,EAAEtB,UAAU,CAAC2B,QAAQ,EAAE;EAC/BD,WAAW,EAAE3B,WAAW,CAAC4B,QAAQ,EAAE,CAAC9B,KAAK,CAAC,EAAE,CAAC;EAC7CgC,QAAQ,EAAE/C,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;IACnBW,IAAI,EAAEhD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACqC,QAAQ,EAAE;IAC7CI,KAAK,EAAEjD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACqC,QAAQ,EAAE;IAC9CK,OAAO,EAAElD,KAAA,CAAAK,OAAG,CAACC,MAAM,EAAE,CAACa,IAAI,EAAE,CAACX,GAAG,CAAC,GAAG,CAAC,CAACqC,QAAQ;GAC/C,CAAC,CAACA,QAAQ,EAAE;EACbuB,WAAW,EAAEpE,KAAA,CAAAK,OAAG,CAACgC,MAAM,CAAC;IACtBgC,kBAAkB,EAAErE,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CAACP,QAAQ,EAAE;IAC5CyB,iBAAiB,EAAEtE,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CAACP,QAAQ,EAAE;IAC3C0B,gBAAgB,EAAEvE,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CAACP,QAAQ,EAAE;IAC1C2B,eAAe,EAAExE,KAAA,CAAAK,OAAG,CAAC+C,OAAO,EAAE,CAACP,QAAQ;GACxC,CAAC,CAACA,QAAQ;CACZ,CAAC;AAEF;;;AAGA,SAAgB9C,eAAeA,CAAC0E,MAAwB;EAAA;EAAA7E,aAAA,GAAA+B,CAAA;EAAA/B,aAAA,GAAAK,CAAA;EACtD,OAAO,CAACyE,GAAQ,EAAEC,GAAQ,EAAEC,IAAS,KAAI;IAAA;IAAAhF,aAAA,GAAA+B,CAAA;IACvC,MAAM;MAAEQ,KAAK;MAAEV;IAAK,CAAE;IAAA;IAAA,CAAA7B,aAAA,GAAAK,CAAA,QAAGwE,MAAM,CAACI,QAAQ,CAACH,GAAG,CAACI,IAAI,EAAE;MACjDC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE;KACV,CAAC;IAAC;IAAArF,aAAA,GAAAK,CAAA;IAEH,IAAIkC,KAAK,EAAE;MAAA;MAAAvC,aAAA,GAAAsC,CAAA;MACT,MAAMgD,aAAa;MAAA;MAAA,CAAAtF,aAAA,GAAAK,CAAA,QAAGkC,KAAK,CAACgD,OAAO,CAACC,GAAG,CAACC,MAAM,IAAK;QAAA;QAAAzF,aAAA,GAAA+B,CAAA;QAAA/B,aAAA,GAAAK,CAAA;QAAA;UACjDqF,KAAK,EAAED,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;UAC5BC,OAAO,EAAEJ,MAAM,CAACI;SACjB;OAAC,CAAC;MAAC;MAAA7F,aAAA,GAAAK,CAAA;MAEJ,OAAO0E,GAAG,CAACe,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdzD,KAAK,EAAE;UACLsD,OAAO,EAAE,mBAAmB;UAC5BI,IAAI,EAAE,kBAAkB;UACxBC,UAAU,EAAE,GAAG;UACfC,SAAS,EAAE,IAAIzE,IAAI,EAAE,CAAC0E,WAAW,EAAE;UACnCT,IAAI,EAAEb,GAAG,CAACa,IAAI;UACdU,MAAM,EAAEvB,GAAG,CAACuB,MAAM;UAClBd,OAAO,EAAED;;OAEZ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAtF,aAAA,GAAAsC,CAAA;IAAA;IAED;IAAAtC,aAAA,GAAAK,CAAA;IACAyE,GAAG,CAACI,IAAI,GAAGrD,KAAK;IAAC;IAAA7B,aAAA,GAAAK,CAAA;IACjB2E,IAAI,EAAE;EACR,CAAC;AACH;AAAC;AAAAhF,aAAA,GAAAK,CAAA;AAEDH,OAAA,CAAAO,OAAA,GAAe;EACb+B,cAAc,EAAdtC,OAAA,CAAAsC,cAAc;EACdiB,WAAW,EAAXvD,OAAA,CAAAuD,WAAW;EACXE,oBAAoB,EAApBzD,OAAA,CAAAyD,oBAAoB;EACpBC,mBAAmB,EAAnB1D,OAAA,CAAA0D,mBAAmB;EACnBI,oBAAoB,EAApB9D,OAAA,CAAA8D,oBAAoB;EACpBG,iBAAiB,EAAjBjE,OAAA,CAAAiE,iBAAiB;EACjBC,wBAAwB,EAAxBlE,OAAA,CAAAkE,wBAAwB;EACxBC,kBAAkB,EAAlBnE,OAAA,CAAAmE,kBAAkB;EAClBE,mBAAmB,EAAnBrE,OAAA,CAAAqE,mBAAmB;EACnBpE;CACD", "ignoreList": []}