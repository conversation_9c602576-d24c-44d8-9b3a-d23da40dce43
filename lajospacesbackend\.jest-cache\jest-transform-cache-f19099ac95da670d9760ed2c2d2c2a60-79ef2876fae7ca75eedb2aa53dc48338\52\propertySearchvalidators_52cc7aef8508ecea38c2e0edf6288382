e525b88555363b7e061ad09e4a7d457d
"use strict";

/* istanbul ignore next */
function cov_16u98ixvck() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\propertySearch.validators.ts";
  var hash = "41addbab6db260eadb212027bf6f41db7cf58a1d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\propertySearch.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 134
        }
      },
      "4": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 45
        }
      },
      "5": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 15,
          column: 1
        }
      },
      "6": {
        start: {
          line: 17,
          column: 23
        },
        end: {
          line: 17,
          column: 97
        }
      },
      "7": {
        start: {
          line: 18,
          column: 22
        },
        end: {
          line: 18,
          column: 52
        }
      },
      "8": {
        start: {
          line: 19,
          column: 21
        },
        end: {
          line: 19,
          column: 124
        }
      },
      "9": {
        start: {
          line: 20,
          column: 20
        },
        end: {
          line: 20,
          column: 35
        }
      },
      "10": {
        start: {
          line: 24,
          column: 0
        },
        end: {
          line: 151,
          column: 3
        }
      },
      "11": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 120,
          column: 5
        }
      },
      "12": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 57
        }
      },
      "13": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "14": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "15": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 124,
          column: 63
        }
      },
      "16": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "17": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 131,
          column: 9
        }
      },
      "18": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 64
        }
      },
      "19": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 136,
          column: 5
        }
      },
      "20": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 135,
          column: 64
        }
      },
      "21": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 139,
          column: 5
        }
      },
      "22": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 138,
          column: 63
        }
      },
      "23": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "24": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 63
        }
      },
      "25": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 17
        }
      },
      "26": {
        start: {
          line: 155,
          column: 0
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "27": {
        start: {
          line: 180,
          column: 0
        },
        end: {
          line: 192,
          column: 3
        }
      },
      "28": {
        start: {
          line: 196,
          column: 0
        },
        end: {
          line: 205,
          column: 3
        }
      },
      "29": {
        start: {
          line: 206,
          column: 0
        },
        end: {
          line: 211,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 116,
            column: 10
          },
          end: {
            line: 116,
            column: 11
          }
        },
        loc: {
          start: {
            line: 116,
            column: 30
          },
          end: {
            line: 144,
            column: 1
          }
        },
        line: 116
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "4": {
        loc: {
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 118,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 118,
            column: 22
          }
        }, {
          start: {
            line: 118,
            column: 26
          },
          end: {
            line: 118,
            column: 40
          }
        }, {
          start: {
            line: 118,
            column: 44
          },
          end: {
            line: 118,
            column: 75
          }
        }],
        line: 118
      },
      "5": {
        loc: {
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "6": {
        loc: {
          start: {
            line: 122,
            column: 8
          },
          end: {
            line: 122,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 8
          },
          end: {
            line: 122,
            column: 22
          }
        }, {
          start: {
            line: 122,
            column: 26
          },
          end: {
            line: 122,
            column: 60
          }
        }, {
          start: {
            line: 122,
            column: 64
          },
          end: {
            line: 122,
            column: 82
          }
        }, {
          start: {
            line: 122,
            column: 86
          },
          end: {
            line: 122,
            column: 104
          }
        }],
        line: 122
      },
      "7": {
        loc: {
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "8": {
        loc: {
          start: {
            line: 128,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "9": {
        loc: {
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 128,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 8
          },
          end: {
            line: 128,
            column: 23
          }
        }, {
          start: {
            line: 128,
            column: 27
          },
          end: {
            line: 128,
            column: 62
          }
        }, {
          start: {
            line: 128,
            column: 66
          },
          end: {
            line: 128,
            column: 85
          }
        }, {
          start: {
            line: 128,
            column: 89
          },
          end: {
            line: 128,
            column: 108
          }
        }],
        line: 128
      },
      "10": {
        loc: {
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "11": {
        loc: {
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 136,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 4
          },
          end: {
            line: 136,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "12": {
        loc: {
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 134,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 134,
            column: 27
          }
        }, {
          start: {
            line: 134,
            column: 31
          },
          end: {
            line: 134,
            column: 48
          }
        }, {
          start: {
            line: 134,
            column: 52
          },
          end: {
            line: 134,
            column: 91
          }
        }],
        line: 134
      },
      "13": {
        loc: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 139,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 139,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "14": {
        loc: {
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 137,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 137,
            column: 26
          }
        }, {
          start: {
            line: 137,
            column: 30
          },
          end: {
            line: 137,
            column: 49
          }
        }, {
          start: {
            line: 137,
            column: 53
          },
          end: {
            line: 137,
            column: 93
          }
        }],
        line: 137
      },
      "15": {
        loc: {
          start: {
            line: 140,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 4
          },
          end: {
            line: 142,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "16": {
        loc: {
          start: {
            line: 140,
            column: 8
          },
          end: {
            line: 140,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 8
          },
          end: {
            line: 140,
            column: 26
          }
        }, {
          start: {
            line: 140,
            column: 30
          },
          end: {
            line: 140,
            column: 49
          }
        }, {
          start: {
            line: 140,
            column: 53
          },
          end: {
            line: 140,
            column: 93
          }
        }],
        line: 140
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0],
      "6": [0, 0, 0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0, 0],
      "15": [0, 0],
      "16": [0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\propertySearch.validators.ts",
      mappings: ";;;;;;AAAA,8CAAsB;AAEtB,iCAAiC;AACjC,MAAM,eAAe,GAAG;IACtB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IAChF,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;IACzE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;IACtE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS;IACtE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;CAChD,CAAC;AAEF,2BAA2B;AAC3B,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAClG,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AACrD,MAAM,YAAY,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AAC7H,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAEpC;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,cAAc;IACd,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACtD,YAAY,EAAE,2CAA2C;KAC1D,CAAC;IAEF,mBAAmB;IACnB,YAAY,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CAClC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,EACrC,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAChE,CAAC,QAAQ,EAAE;IAEZ,WAAW,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CACjC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,EACpC,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAC/D,CAAC,QAAQ,EAAE;IAEZ,gBAAgB;IAChB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC9D,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,yCAAyC;KACxD,CAAC;IAEF,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC9D,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,yCAAyC;KACxD,CAAC;IAEF,eAAe;IACf,QAAQ,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CAC9B,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAC3B,aAAG,CAAC,MAAM,CAAC;QACT,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC3C,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;KAC5C,CAAC,CACH,CAAC,QAAQ,EAAE;IAEZ,SAAS,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CAC/B,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAC3B,aAAG,CAAC,MAAM,CAAC;QACT,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC3C,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;KAC5C,CAAC,CACH,CAAC,QAAQ,EAAE;IAEZ,mBAAmB;IACnB,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,CAAC,QAAQ,EAAE;QACxD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,WAAW,EAAE,aAAG,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;YACrD,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY;SACpE,CAAC,CAAC,QAAQ,EAAE;KACd,CAAC,CAAC,QAAQ,EAAE;IAEb,oBAAoB;IACpB,SAAS,EAAE,aAAG,CAAC,MAAM,CAAC;QACpB,IAAI,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC9B,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACjC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAClC,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACnC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAClC,eAAe,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACzC,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACjC,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACtC,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACnC,EAAE,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC5B,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACxC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAClC,GAAG,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC7B,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACtC,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACpC,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACtC,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACjC,eAAe,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;KAC1C,CAAC,CAAC,QAAQ,EAAE;IAEb,gBAAgB;IAChB,KAAK,EAAE,aAAG,CAAC,MAAM,CAAC;QAChB,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACxC,WAAW,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACrC,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACxC,aAAa,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;KACxC,CAAC,CAAC,QAAQ,EAAE;IAEb,uBAAuB;IACvB,aAAa,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACvD,UAAU,EAAE,2CAA2C;KACxD,CAAC;IAEF,WAAW,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAElC,+CAA+C;IAC/C,mBAAmB,EAAE,aAAG,CAAC,MAAM,CAAC;QAC9B,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE;QAC9D,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;YACnB,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;YAC7C,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;SAC9C,CAAC,CAAC,QAAQ,EAAE;KACd,CAAC,CAAC,QAAQ,EAAE;IAEb,yBAAyB;IACzB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpC,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;IAChE,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAE7D,mBAAmB;IACnB,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACpC,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACnC,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE;IAE1E,eAAe;IACf,YAAY,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACnC,aAAa,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACpC,YAAY,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACnC,aAAa,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAErC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IAC3B,uBAAuB;IACvB,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QACxE,OAAO,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACnD,CAAC;IAED,yBAAyB;IACzB,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QACrG,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YAC5C,OAAO,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,IAAI,KAAK,CAAC,SAAS,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACzG,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YAC9C,OAAO,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxF,OAAO,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QAC1F,OAAO,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QAC1F,OAAO,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,CAAC,QAAQ,CAAC;IACV,0BAA0B,EAAE,oDAAoD;IAChF,4BAA4B,EAAE,0DAA0D;IACxF,6BAA6B,EAAE,4DAA4D;IAC3F,iCAAiC,EAAE,uDAAuD;IAC1F,gCAAgC,EAAE,wDAAwD;IAC1F,gCAAgC,EAAE,wDAAwD;CAC3F,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC1D,YAAY,EAAE,qCAAqC;QACnD,YAAY,EAAE,qCAAqC;QACnD,cAAc,EAAE,sBAAsB;KACvC,CAAC;IAEF,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC7D,YAAY,EAAE,wCAAwC;QACtD,YAAY,EAAE,wCAAwC;QACtD,cAAc,EAAE,uBAAuB;KACxC,CAAC;IAEF,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;QAC9D,YAAY,EAAE,oCAAoC;QAClD,YAAY,EAAE,oCAAoC;KACnD,CAAC;IAEF,YAAY,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CAClC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,EACrC,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,CACzD,CAAC,QAAQ,EAAE;IAEZ,WAAW,EAAE,aAAG,CAAC,YAAY,EAAE,CAAC,GAAG,CACjC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,EACpC,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CACxD,CAAC,QAAQ,EAAE;IAEZ,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACxC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAExC,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;CAC1F,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,gBAAgB,GAAG,aAAG,CAAC,MAAM,CAAC;IACzC,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC5D,cAAc,EAAE,yBAAyB;QACzC,YAAY,EAAE,2CAA2C;QACzD,YAAY,EAAE,0CAA0C;QACxD,cAAc,EAAE,yBAAyB;KAC1C,CAAC;IAEF,cAAc,EAAE,8BAAsB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACzD,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IAEF,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAE5F,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CACtC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,uBAAuB,GAAG,aAAG,CAAC,MAAM,CAAC;IAChD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC7D,cAAc,EAAE,0BAA0B;QAC1C,YAAY,EAAE,4CAA4C;QAC1D,YAAY,EAAE,2CAA2C;QACzD,cAAc,EAAE,0BAA0B;KAC3C,CAAC;IAEF,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAEtF,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CAC/C,CAAC,CAAC;AAEH,kBAAe;IACb,sBAAsB,EAAtB,8BAAsB;IACtB,sBAAsB,EAAtB,8BAAsB;IACtB,gBAAgB,EAAhB,wBAAgB;IAChB,uBAAuB,EAAvB,+BAAuB;CACxB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\propertySearch.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n// Nigerian states for validation\r\nconst NIGERIAN_STATES = [\r\n  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',\r\n  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe',\r\n  'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara',\r\n  'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau',\r\n  'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'\r\n];\r\n\r\n// Property types and enums\r\nconst PROPERTY_TYPES = ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion'];\r\nconst LISTING_TYPES = ['rent', 'roommate', 'sublet'];\r\nconst SORT_OPTIONS = ['createdAt', 'updatedAt', 'pricing.rentPerMonth', 'analytics.views', 'title', 'bedrooms', 'bathrooms'];\r\nconst SORT_ORDERS = ['asc', 'desc'];\r\n\r\n/**\r\n * Advanced property search validation schema\r\n */\r\nexport const searchPropertiesSchema = Joi.object({\r\n  // Text search\r\n  query: Joi.string().trim().max(200).optional().messages({\r\n    'string.max': 'Search query cannot exceed 200 characters'\r\n  }),\r\n  \r\n  // Property filters\r\n  propertyType: Joi.alternatives().try(\r\n    Joi.string().valid(...PROPERTY_TYPES),\r\n    Joi.array().items(Joi.string().valid(...PROPERTY_TYPES)).max(5)\r\n  ).optional(),\r\n  \r\n  listingType: Joi.alternatives().try(\r\n    Joi.string().valid(...LISTING_TYPES),\r\n    Joi.array().items(Joi.string().valid(...LISTING_TYPES)).max(3)\r\n  ).optional(),\r\n  \r\n  // Price filters\r\n  minPrice: Joi.number().min(0).max(10000000).optional().messages({\r\n    'number.min': 'Minimum price cannot be negative',\r\n    'number.max': 'Minimum price cannot exceed \u20A610,000,000'\r\n  }),\r\n  \r\n  maxPrice: Joi.number().min(0).max(10000000).optional().messages({\r\n    'number.min': 'Maximum price cannot be negative',\r\n    'number.max': 'Maximum price cannot exceed \u20A610,000,000'\r\n  }),\r\n  \r\n  // Room filters\r\n  bedrooms: Joi.alternatives().try(\r\n    Joi.number().min(0).max(20),\r\n    Joi.object({\r\n      min: Joi.number().min(0).max(20).optional(),\r\n      max: Joi.number().min(0).max(20).optional()\r\n    })\r\n  ).optional(),\r\n  \r\n  bathrooms: Joi.alternatives().try(\r\n    Joi.number().min(1).max(20),\r\n    Joi.object({\r\n      min: Joi.number().min(1).max(20).optional(),\r\n      max: Joi.number().min(1).max(20).optional()\r\n    })\r\n  ).optional(),\r\n  \r\n  // Location filters\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().valid(...NIGERIAN_STATES).optional(),\r\n    area: Joi.string().trim().max(100).optional(),\r\n    coordinates: Joi.object({\r\n      latitude: Joi.number().min(-90).max(90).required(),\r\n      longitude: Joi.number().min(-180).max(180).required(),\r\n      radius: Joi.number().min(100).max(50000).default(5000) // in meters\r\n    }).optional()\r\n  }).optional(),\r\n  \r\n  // Amenities filters\r\n  amenities: Joi.object({\r\n    wifi: Joi.boolean().optional(),\r\n    parking: Joi.boolean().optional(),\r\n    security: Joi.boolean().optional(),\r\n    generator: Joi.boolean().optional(),\r\n    borehole: Joi.boolean().optional(),\r\n    airConditioning: Joi.boolean().optional(),\r\n    kitchen: Joi.boolean().optional(),\r\n    refrigerator: Joi.boolean().optional(),\r\n    furnished: Joi.boolean().optional(),\r\n    tv: Joi.boolean().optional(),\r\n    washingMachine: Joi.boolean().optional(),\r\n    elevator: Joi.boolean().optional(),\r\n    gym: Joi.boolean().optional(),\r\n    swimmingPool: Joi.boolean().optional(),\r\n    playground: Joi.boolean().optional(),\r\n    prepaidMeter: Joi.boolean().optional(),\r\n    cableTV: Joi.boolean().optional(),\r\n    cleaningService: Joi.boolean().optional()\r\n  }).optional(),\r\n  \r\n  // Rules filters\r\n  rules: Joi.object({\r\n    smokingAllowed: Joi.boolean().optional(),\r\n    petsAllowed: Joi.boolean().optional(),\r\n    partiesAllowed: Joi.boolean().optional(),\r\n    guestsAllowed: Joi.boolean().optional()\r\n  }).optional(),\r\n  \r\n  // Availability filters\r\n  availableFrom: Joi.date().min('now').optional().messages({\r\n    'date.min': 'Available from date cannot be in the past'\r\n  }),\r\n  \r\n  availableTo: Joi.date().optional(),\r\n  \r\n  // Roommate preferences (for roommate listings)\r\n  roommatePreferences: Joi.object({\r\n    gender: Joi.string().valid('male', 'female', 'any').optional(),\r\n    ageRange: Joi.object({\r\n      min: Joi.number().min(18).max(100).optional(),\r\n      max: Joi.number().min(18).max(100).optional()\r\n    }).optional()\r\n  }).optional(),\r\n  \r\n  // Pagination and sorting\r\n  page: Joi.number().min(1).default(1),\r\n  limit: Joi.number().min(1).max(100).default(20),\r\n  sortBy: Joi.string().valid(...SORT_OPTIONS).default('createdAt'),\r\n  sortOrder: Joi.string().valid(...SORT_ORDERS).default('desc'),\r\n  \r\n  // Advanced filters\r\n  isVerified: Joi.boolean().optional(),\r\n  hasPhotos: Joi.boolean().optional(),\r\n  ownerType: Joi.string().valid('individual', 'agent', 'company').optional(),\r\n  \r\n  // Date filters\r\n  createdAfter: Joi.date().optional(),\r\n  createdBefore: Joi.date().optional(),\r\n  updatedAfter: Joi.date().optional(),\r\n  updatedBefore: Joi.date().optional()\r\n  \r\n}).custom((value, helpers) => {\r\n  // Validate price range\r\n  if (value.minPrice && value.maxPrice && value.minPrice > value.maxPrice) {\r\n    return helpers.error('custom.invalidPriceRange');\r\n  }\r\n  \r\n  // Validate bedroom range\r\n  if (value.bedrooms && typeof value.bedrooms === 'object' && value.bedrooms.min && value.bedrooms.max) {\r\n    if (value.bedrooms.min > value.bedrooms.max) {\r\n      return helpers.error('custom.invalidBedroomRange');\r\n    }\r\n  }\r\n  \r\n  // Validate bathroom range\r\n  if (value.bathrooms && typeof value.bathrooms === 'object' && value.bathrooms.min && value.bathrooms.max) {\r\n    if (value.bathrooms.min > value.bathrooms.max) {\r\n      return helpers.error('custom.invalidBathroomRange');\r\n    }\r\n  }\r\n  \r\n  // Validate date ranges\r\n  if (value.availableFrom && value.availableTo && value.availableFrom > value.availableTo) {\r\n    return helpers.error('custom.invalidAvailabilityRange');\r\n  }\r\n  \r\n  if (value.createdAfter && value.createdBefore && value.createdAfter > value.createdBefore) {\r\n    return helpers.error('custom.invalidCreatedDateRange');\r\n  }\r\n  \r\n  if (value.updatedAfter && value.updatedBefore && value.updatedAfter > value.updatedBefore) {\r\n    return helpers.error('custom.invalidUpdatedDateRange');\r\n  }\r\n  \r\n  return value;\r\n}).messages({\r\n  'custom.invalidPriceRange': 'Minimum price cannot be greater than maximum price',\r\n  'custom.invalidBedroomRange': 'Minimum bedrooms cannot be greater than maximum bedrooms',\r\n  'custom.invalidBathroomRange': 'Minimum bathrooms cannot be greater than maximum bathrooms',\r\n  'custom.invalidAvailabilityRange': 'Available from date cannot be after available to date',\r\n  'custom.invalidCreatedDateRange': 'Created after date cannot be after created before date',\r\n  'custom.invalidUpdatedDateRange': 'Updated after date cannot be after updated before date'\r\n});\r\n\r\n/**\r\n * Nearby properties validation schema\r\n */\r\nexport const nearbyPropertiesSchema = Joi.object({\r\n  latitude: Joi.number().min(-90).max(90).required().messages({\r\n    'number.min': 'Latitude must be between -90 and 90',\r\n    'number.max': 'Latitude must be between -90 and 90',\r\n    'any.required': 'Latitude is required'\r\n  }),\r\n  \r\n  longitude: Joi.number().min(-180).max(180).required().messages({\r\n    'number.min': 'Longitude must be between -180 and 180',\r\n    'number.max': 'Longitude must be between -180 and 180',\r\n    'any.required': 'Longitude is required'\r\n  }),\r\n  \r\n  radius: Joi.number().min(100).max(50000).default(5000).messages({\r\n    'number.min': 'Radius must be at least 100 meters',\r\n    'number.max': 'Radius cannot exceed 50 kilometers'\r\n  }),\r\n  \r\n  propertyType: Joi.alternatives().try(\r\n    Joi.string().valid(...PROPERTY_TYPES),\r\n    Joi.array().items(Joi.string().valid(...PROPERTY_TYPES))\r\n  ).optional(),\r\n  \r\n  listingType: Joi.alternatives().try(\r\n    Joi.string().valid(...LISTING_TYPES),\r\n    Joi.array().items(Joi.string().valid(...LISTING_TYPES))\r\n  ).optional(),\r\n  \r\n  minPrice: Joi.number().min(0).optional(),\r\n  maxPrice: Joi.number().min(0).optional(),\r\n  \r\n  limit: Joi.number().min(1).max(100).default(20),\r\n  sortBy: Joi.string().valid('distance', 'price', 'createdAt', 'views').default('distance')\r\n});\r\n\r\n/**\r\n * Save search validation schema\r\n */\r\nexport const saveSearchSchema = Joi.object({\r\n  name: Joi.string().trim().min(3).max(100).required().messages({\r\n    'string.empty': 'Search name is required',\r\n    'string.min': 'Search name must be at least 3 characters',\r\n    'string.max': 'Search name cannot exceed 100 characters',\r\n    'any.required': 'Search name is required'\r\n  }),\r\n  \r\n  searchCriteria: searchPropertiesSchema.required().messages({\r\n    'any.required': 'Search criteria is required'\r\n  }),\r\n  \r\n  alertFrequency: Joi.string().valid('immediate', 'daily', 'weekly', 'never').default('never'),\r\n  \r\n  isActive: Joi.boolean().default(true)\r\n});\r\n\r\n/**\r\n * Search suggestions validation schema\r\n */\r\nexport const searchSuggestionsSchema = Joi.object({\r\n  query: Joi.string().trim().min(2).max(100).required().messages({\r\n    'string.empty': 'Search query is required',\r\n    'string.min': 'Search query must be at least 2 characters',\r\n    'string.max': 'Search query cannot exceed 100 characters',\r\n    'any.required': 'Search query is required'\r\n  }),\r\n  \r\n  type: Joi.string().valid('all', 'locations', 'properties', 'amenities').default('all'),\r\n  \r\n  limit: Joi.number().min(1).max(20).default(10)\r\n});\r\n\r\nexport default {\r\n  searchPropertiesSchema,\r\n  nearbyPropertiesSchema,\r\n  saveSearchSchema,\r\n  searchSuggestionsSchema\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "41addbab6db260eadb212027bf6f41db7cf58a1d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_16u98ixvck = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_16u98ixvck();
var __importDefault =
/* istanbul ignore next */
(cov_16u98ixvck().s[0]++,
/* istanbul ignore next */
(cov_16u98ixvck().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_16u98ixvck().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_16u98ixvck().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_16u98ixvck().f[0]++;
  cov_16u98ixvck().s[1]++;
  return /* istanbul ignore next */(cov_16u98ixvck().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_16u98ixvck().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_16u98ixvck().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_16u98ixvck().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_16u98ixvck().s[3]++;
exports.searchSuggestionsSchema = exports.saveSearchSchema = exports.nearbyPropertiesSchema = exports.searchPropertiesSchema = void 0;
const joi_1 =
/* istanbul ignore next */
(cov_16u98ixvck().s[4]++, __importDefault(require("joi")));
// Nigerian states for validation
const NIGERIAN_STATES =
/* istanbul ignore next */
(cov_16u98ixvck().s[5]++, ['Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara']);
// Property types and enums
const PROPERTY_TYPES =
/* istanbul ignore next */
(cov_16u98ixvck().s[6]++, ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion']);
const LISTING_TYPES =
/* istanbul ignore next */
(cov_16u98ixvck().s[7]++, ['rent', 'roommate', 'sublet']);
const SORT_OPTIONS =
/* istanbul ignore next */
(cov_16u98ixvck().s[8]++, ['createdAt', 'updatedAt', 'pricing.rentPerMonth', 'analytics.views', 'title', 'bedrooms', 'bathrooms']);
const SORT_ORDERS =
/* istanbul ignore next */
(cov_16u98ixvck().s[9]++, ['asc', 'desc']);
/**
 * Advanced property search validation schema
 */
/* istanbul ignore next */
cov_16u98ixvck().s[10]++;
exports.searchPropertiesSchema = joi_1.default.object({
  // Text search
  query: joi_1.default.string().trim().max(200).optional().messages({
    'string.max': 'Search query cannot exceed 200 characters'
  }),
  // Property filters
  propertyType: joi_1.default.alternatives().try(joi_1.default.string().valid(...PROPERTY_TYPES), joi_1.default.array().items(joi_1.default.string().valid(...PROPERTY_TYPES)).max(5)).optional(),
  listingType: joi_1.default.alternatives().try(joi_1.default.string().valid(...LISTING_TYPES), joi_1.default.array().items(joi_1.default.string().valid(...LISTING_TYPES)).max(3)).optional(),
  // Price filters
  minPrice: joi_1.default.number().min(0).max(10000000).optional().messages({
    'number.min': 'Minimum price cannot be negative',
    'number.max': 'Minimum price cannot exceed ₦10,000,000'
  }),
  maxPrice: joi_1.default.number().min(0).max(10000000).optional().messages({
    'number.min': 'Maximum price cannot be negative',
    'number.max': 'Maximum price cannot exceed ₦10,000,000'
  }),
  // Room filters
  bedrooms: joi_1.default.alternatives().try(joi_1.default.number().min(0).max(20), joi_1.default.object({
    min: joi_1.default.number().min(0).max(20).optional(),
    max: joi_1.default.number().min(0).max(20).optional()
  })).optional(),
  bathrooms: joi_1.default.alternatives().try(joi_1.default.number().min(1).max(20), joi_1.default.object({
    min: joi_1.default.number().min(1).max(20).optional(),
    max: joi_1.default.number().min(1).max(20).optional()
  })).optional(),
  // Location filters
  location: joi_1.default.object({
    city: joi_1.default.string().trim().max(100).optional(),
    state: joi_1.default.string().valid(...NIGERIAN_STATES).optional(),
    area: joi_1.default.string().trim().max(100).optional(),
    coordinates: joi_1.default.object({
      latitude: joi_1.default.number().min(-90).max(90).required(),
      longitude: joi_1.default.number().min(-180).max(180).required(),
      radius: joi_1.default.number().min(100).max(50000).default(5000) // in meters
    }).optional()
  }).optional(),
  // Amenities filters
  amenities: joi_1.default.object({
    wifi: joi_1.default.boolean().optional(),
    parking: joi_1.default.boolean().optional(),
    security: joi_1.default.boolean().optional(),
    generator: joi_1.default.boolean().optional(),
    borehole: joi_1.default.boolean().optional(),
    airConditioning: joi_1.default.boolean().optional(),
    kitchen: joi_1.default.boolean().optional(),
    refrigerator: joi_1.default.boolean().optional(),
    furnished: joi_1.default.boolean().optional(),
    tv: joi_1.default.boolean().optional(),
    washingMachine: joi_1.default.boolean().optional(),
    elevator: joi_1.default.boolean().optional(),
    gym: joi_1.default.boolean().optional(),
    swimmingPool: joi_1.default.boolean().optional(),
    playground: joi_1.default.boolean().optional(),
    prepaidMeter: joi_1.default.boolean().optional(),
    cableTV: joi_1.default.boolean().optional(),
    cleaningService: joi_1.default.boolean().optional()
  }).optional(),
  // Rules filters
  rules: joi_1.default.object({
    smokingAllowed: joi_1.default.boolean().optional(),
    petsAllowed: joi_1.default.boolean().optional(),
    partiesAllowed: joi_1.default.boolean().optional(),
    guestsAllowed: joi_1.default.boolean().optional()
  }).optional(),
  // Availability filters
  availableFrom: joi_1.default.date().min('now').optional().messages({
    'date.min': 'Available from date cannot be in the past'
  }),
  availableTo: joi_1.default.date().optional(),
  // Roommate preferences (for roommate listings)
  roommatePreferences: joi_1.default.object({
    gender: joi_1.default.string().valid('male', 'female', 'any').optional(),
    ageRange: joi_1.default.object({
      min: joi_1.default.number().min(18).max(100).optional(),
      max: joi_1.default.number().min(18).max(100).optional()
    }).optional()
  }).optional(),
  // Pagination and sorting
  page: joi_1.default.number().min(1).default(1),
  limit: joi_1.default.number().min(1).max(100).default(20),
  sortBy: joi_1.default.string().valid(...SORT_OPTIONS).default('createdAt'),
  sortOrder: joi_1.default.string().valid(...SORT_ORDERS).default('desc'),
  // Advanced filters
  isVerified: joi_1.default.boolean().optional(),
  hasPhotos: joi_1.default.boolean().optional(),
  ownerType: joi_1.default.string().valid('individual', 'agent', 'company').optional(),
  // Date filters
  createdAfter: joi_1.default.date().optional(),
  createdBefore: joi_1.default.date().optional(),
  updatedAfter: joi_1.default.date().optional(),
  updatedBefore: joi_1.default.date().optional()
}).custom((value, helpers) => {
  /* istanbul ignore next */
  cov_16u98ixvck().f[1]++;
  cov_16u98ixvck().s[11]++;
  // Validate price range
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[4][0]++, value.minPrice) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[4][1]++, value.maxPrice) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[4][2]++, value.minPrice > value.maxPrice)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[3][0]++;
    cov_16u98ixvck().s[12]++;
    return helpers.error('custom.invalidPriceRange');
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[3][1]++;
  }
  // Validate bedroom range
  cov_16u98ixvck().s[13]++;
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[6][0]++, value.bedrooms) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[6][1]++, typeof value.bedrooms === 'object') &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[6][2]++, value.bedrooms.min) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[6][3]++, value.bedrooms.max)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[5][0]++;
    cov_16u98ixvck().s[14]++;
    if (value.bedrooms.min > value.bedrooms.max) {
      /* istanbul ignore next */
      cov_16u98ixvck().b[7][0]++;
      cov_16u98ixvck().s[15]++;
      return helpers.error('custom.invalidBedroomRange');
    } else
    /* istanbul ignore next */
    {
      cov_16u98ixvck().b[7][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[5][1]++;
  }
  // Validate bathroom range
  cov_16u98ixvck().s[16]++;
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[9][0]++, value.bathrooms) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[9][1]++, typeof value.bathrooms === 'object') &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[9][2]++, value.bathrooms.min) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[9][3]++, value.bathrooms.max)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[8][0]++;
    cov_16u98ixvck().s[17]++;
    if (value.bathrooms.min > value.bathrooms.max) {
      /* istanbul ignore next */
      cov_16u98ixvck().b[10][0]++;
      cov_16u98ixvck().s[18]++;
      return helpers.error('custom.invalidBathroomRange');
    } else
    /* istanbul ignore next */
    {
      cov_16u98ixvck().b[10][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[8][1]++;
  }
  // Validate date ranges
  cov_16u98ixvck().s[19]++;
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[12][0]++, value.availableFrom) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[12][1]++, value.availableTo) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[12][2]++, value.availableFrom > value.availableTo)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[11][0]++;
    cov_16u98ixvck().s[20]++;
    return helpers.error('custom.invalidAvailabilityRange');
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[11][1]++;
  }
  cov_16u98ixvck().s[21]++;
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[14][0]++, value.createdAfter) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[14][1]++, value.createdBefore) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[14][2]++, value.createdAfter > value.createdBefore)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[13][0]++;
    cov_16u98ixvck().s[22]++;
    return helpers.error('custom.invalidCreatedDateRange');
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[13][1]++;
  }
  cov_16u98ixvck().s[23]++;
  if (
  /* istanbul ignore next */
  (cov_16u98ixvck().b[16][0]++, value.updatedAfter) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[16][1]++, value.updatedBefore) &&
  /* istanbul ignore next */
  (cov_16u98ixvck().b[16][2]++, value.updatedAfter > value.updatedBefore)) {
    /* istanbul ignore next */
    cov_16u98ixvck().b[15][0]++;
    cov_16u98ixvck().s[24]++;
    return helpers.error('custom.invalidUpdatedDateRange');
  } else
  /* istanbul ignore next */
  {
    cov_16u98ixvck().b[15][1]++;
  }
  cov_16u98ixvck().s[25]++;
  return value;
}).messages({
  'custom.invalidPriceRange': 'Minimum price cannot be greater than maximum price',
  'custom.invalidBedroomRange': 'Minimum bedrooms cannot be greater than maximum bedrooms',
  'custom.invalidBathroomRange': 'Minimum bathrooms cannot be greater than maximum bathrooms',
  'custom.invalidAvailabilityRange': 'Available from date cannot be after available to date',
  'custom.invalidCreatedDateRange': 'Created after date cannot be after created before date',
  'custom.invalidUpdatedDateRange': 'Updated after date cannot be after updated before date'
});
/**
 * Nearby properties validation schema
 */
/* istanbul ignore next */
cov_16u98ixvck().s[26]++;
exports.nearbyPropertiesSchema = joi_1.default.object({
  latitude: joi_1.default.number().min(-90).max(90).required().messages({
    'number.min': 'Latitude must be between -90 and 90',
    'number.max': 'Latitude must be between -90 and 90',
    'any.required': 'Latitude is required'
  }),
  longitude: joi_1.default.number().min(-180).max(180).required().messages({
    'number.min': 'Longitude must be between -180 and 180',
    'number.max': 'Longitude must be between -180 and 180',
    'any.required': 'Longitude is required'
  }),
  radius: joi_1.default.number().min(100).max(50000).default(5000).messages({
    'number.min': 'Radius must be at least 100 meters',
    'number.max': 'Radius cannot exceed 50 kilometers'
  }),
  propertyType: joi_1.default.alternatives().try(joi_1.default.string().valid(...PROPERTY_TYPES), joi_1.default.array().items(joi_1.default.string().valid(...PROPERTY_TYPES))).optional(),
  listingType: joi_1.default.alternatives().try(joi_1.default.string().valid(...LISTING_TYPES), joi_1.default.array().items(joi_1.default.string().valid(...LISTING_TYPES))).optional(),
  minPrice: joi_1.default.number().min(0).optional(),
  maxPrice: joi_1.default.number().min(0).optional(),
  limit: joi_1.default.number().min(1).max(100).default(20),
  sortBy: joi_1.default.string().valid('distance', 'price', 'createdAt', 'views').default('distance')
});
/**
 * Save search validation schema
 */
/* istanbul ignore next */
cov_16u98ixvck().s[27]++;
exports.saveSearchSchema = joi_1.default.object({
  name: joi_1.default.string().trim().min(3).max(100).required().messages({
    'string.empty': 'Search name is required',
    'string.min': 'Search name must be at least 3 characters',
    'string.max': 'Search name cannot exceed 100 characters',
    'any.required': 'Search name is required'
  }),
  searchCriteria: exports.searchPropertiesSchema.required().messages({
    'any.required': 'Search criteria is required'
  }),
  alertFrequency: joi_1.default.string().valid('immediate', 'daily', 'weekly', 'never').default('never'),
  isActive: joi_1.default.boolean().default(true)
});
/**
 * Search suggestions validation schema
 */
/* istanbul ignore next */
cov_16u98ixvck().s[28]++;
exports.searchSuggestionsSchema = joi_1.default.object({
  query: joi_1.default.string().trim().min(2).max(100).required().messages({
    'string.empty': 'Search query is required',
    'string.min': 'Search query must be at least 2 characters',
    'string.max': 'Search query cannot exceed 100 characters',
    'any.required': 'Search query is required'
  }),
  type: joi_1.default.string().valid('all', 'locations', 'properties', 'amenities').default('all'),
  limit: joi_1.default.number().min(1).max(20).default(10)
});
/* istanbul ignore next */
cov_16u98ixvck().s[29]++;
exports.default = {
  searchPropertiesSchema: exports.searchPropertiesSchema,
  nearbyPropertiesSchema: exports.nearbyPropertiesSchema,
  saveSearchSchema: exports.saveSearchSchema,
  searchSuggestionsSchema: exports.searchSuggestionsSchema
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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