482eb10e25951bef674590c583c0fb63
"use strict";

/* istanbul ignore next */
function cov_2avwqwwcso() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchingService.ts";
  var hash = "cadc9878a2309fbc677fc47452a17a91902e3196";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 16
        },
        end: {
          line: 4,
          column: 42
        }
      },
      "3": {
        start: {
          line: 5,
          column: 19
        },
        end: {
          line: 5,
          column: 48
        }
      },
      "4": {
        start: {
          line: 6,
          column: 24
        },
        end: {
          line: 6,
          column: 58
        }
      },
      "5": {
        start: {
          line: 7,
          column: 17
        },
        end: {
          line: 7,
          column: 43
        }
      },
      "6": {
        start: {
          line: 8,
          column: 19
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "7": {
        start: {
          line: 9,
          column: 26
        },
        end: {
          line: 9,
          column: 54
        }
      },
      "8": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 58,
          column: 9
        }
      },
      "9": {
        start: {
          line: 17,
          column: 77
        },
        end: {
          line: 22,
          column: 14
        }
      },
      "10": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 25,
          column: 13
        }
      },
      "11": {
        start: {
          line: 24,
          column: 16
        },
        end: {
          line: 24,
          column: 93
        }
      },
      "12": {
        start: {
          line: 26,
          column: 28
        },
        end: {
          line: 35,
          column: 13
        }
      },
      "13": {
        start: {
          line: 37,
          column: 28
        },
        end: {
          line: 45,
          column: 13
        }
      },
      "14": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 52,
          column: 61
        }
      },
      "15": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 27
        }
      },
      "16": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 82
        }
      },
      "17": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 84
        }
      },
      "18": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 105,
          column: 9
        }
      },
      "19": {
        start: {
          line: 65,
          column: 55
        },
        end: {
          line: 69,
          column: 14
        }
      },
      "20": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 72,
          column: 13
        }
      },
      "21": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 71,
          column: 103
        }
      },
      "22": {
        start: {
          line: 73,
          column: 28
        },
        end: {
          line: 82,
          column: 13
        }
      },
      "23": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 92,
          column: 13
        }
      },
      "24": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 99,
          column: 61
        }
      },
      "25": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 27
        }
      },
      "26": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 86
        }
      },
      "27": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 104,
          column: 93
        }
      },
      "28": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 150,
          column: 9
        }
      },
      "29": {
        start: {
          line: 112,
          column: 30
        },
        end: {
          line: 112,
          column: 80
        }
      },
      "30": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 115,
          column: 13
        }
      },
      "31": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 114,
          column: 26
        }
      },
      "32": {
        start: {
          line: 117,
          column: 31
        },
        end: {
          line: 117,
          column: 111
        }
      },
      "33": {
        start: {
          line: 118,
          column: 28
        },
        end: {
          line: 118,
          column: 30
        }
      },
      "34": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 141,
          column: 13
        }
      },
      "35": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 140,
          column: 17
        }
      },
      "36": {
        start: {
          line: 121,
          column: 42
        },
        end: {
          line: 121,
          column: 102
        }
      },
      "37": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 135,
          column: 21
        }
      },
      "38": {
        start: {
          line: 123,
          column: 41
        },
        end: {
          line: 123,
          column: 121
        }
      },
      "39": {
        start: {
          line: 124,
          column: 45
        },
        end: {
          line: 124,
          column: 114
        }
      },
      "40": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 134,
          column: 27
        }
      },
      "41": {
        start: {
          line: 138,
          column: 20
        },
        end: {
          line: 138,
          column: 117
        }
      },
      "42": {
        start: {
          line: 139,
          column: 20
        },
        end: {
          line: 139,
          column: 29
        }
      },
      "43": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 145,
          column: 33
        }
      },
      "44": {
        start: {
          line: 144,
          column: 32
        },
        end: {
          line: 144,
          column: 75
        }
      },
      "45": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 148,
          column: 76
        }
      },
      "46": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 82
        }
      },
      "47": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "48": {
        start: {
          line: 157,
          column: 30
        },
        end: {
          line: 157,
          column: 80
        }
      },
      "49": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 160,
          column: 13
        }
      },
      "50": {
        start: {
          line: 159,
          column: 16
        },
        end: {
          line: 159,
          column: 26
        }
      },
      "51": {
        start: {
          line: 162,
          column: 31
        },
        end: {
          line: 162,
          column: 111
        }
      },
      "52": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 30
        }
      },
      "53": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 186,
          column: 13
        }
      },
      "54": {
        start: {
          line: 165,
          column: 16
        },
        end: {
          line: 185,
          column: 17
        }
      },
      "55": {
        start: {
          line: 166,
          column: 42
        },
        end: {
          line: 166,
          column: 105
        }
      },
      "56": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 180,
          column: 21
        }
      },
      "57": {
        start: {
          line: 168,
          column: 41
        },
        end: {
          line: 168,
          column: 128
        }
      },
      "58": {
        start: {
          line: 169,
          column: 45
        },
        end: {
          line: 169,
          column: 132
        }
      },
      "59": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 179,
          column: 27
        }
      },
      "60": {
        start: {
          line: 183,
          column: 20
        },
        end: {
          line: 183,
          column: 115
        }
      },
      "61": {
        start: {
          line: 184,
          column: 20
        },
        end: {
          line: 184,
          column: 29
        }
      },
      "62": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 190,
          column: 33
        }
      },
      "63": {
        start: {
          line: 189,
          column: 32
        },
        end: {
          line: 189,
          column: 75
        }
      },
      "64": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 75
        }
      },
      "65": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 81
        }
      },
      "66": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 240,
          column: 9
        }
      },
      "67": {
        start: {
          line: 203,
          column: 34
        },
        end: {
          line: 203,
          column: 95
        }
      },
      "68": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "69": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 37
        }
      },
      "70": {
        start: {
          line: 208,
          column: 30
        },
        end: {
          line: 208,
          column: 76
        }
      },
      "71": {
        start: {
          line: 210,
          column: 35
        },
        end: {
          line: 210,
          column: 65
        }
      },
      "72": {
        start: {
          line: 211,
          column: 30
        },
        end: {
          line: 211,
          column: 40
        }
      },
      "73": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 68
        }
      },
      "74": {
        start: {
          line: 214,
          column: 38
        },
        end: {
          line: 216,
          column: 101
        }
      },
      "75": {
        start: {
          line: 217,
          column: 40
        },
        end: {
          line: 217,
          column: 67
        }
      },
      "76": {
        start: {
          line: 218,
          column: 31
        },
        end: {
          line: 218,
          column: 116
        }
      },
      "77": {
        start: {
          line: 219,
          column: 26
        },
        end: {
          line: 232,
          column: 14
        }
      },
      "78": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 233,
          column: 31
        }
      },
      "79": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 168
        }
      },
      "80": {
        start: {
          line: 235,
          column: 12
        },
        end: {
          line: 235,
          column: 25
        }
      },
      "81": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 66
        }
      },
      "82": {
        start: {
          line: 239,
          column: 12
        },
        end: {
          line: 239,
          column: 73
        }
      },
      "83": {
        start: {
          line: 244,
          column: 20
        },
        end: {
          line: 244,
          column: 21
        }
      },
      "84": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "85": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 247,
          column: 24
        }
      },
      "86": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 252,
          column: 9
        }
      },
      "87": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 251,
          column: 24
        }
      },
      "88": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "89": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 24
        }
      },
      "90": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "91": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 24
        }
      },
      "92": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 263,
          column: 9
        }
      },
      "93": {
        start: {
          line: 262,
          column: 12
        },
        end: {
          line: 262,
          column: 24
        }
      },
      "94": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 36
        }
      },
      "95": {
        start: {
          line: 267,
          column: 24
        },
        end: {
          line: 267,
          column: 49
        }
      },
      "96": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 268,
          column: 49
        }
      },
      "97": {
        start: {
          line: 269,
          column: 26
        },
        end: {
          line: 269,
          column: 53
        }
      },
      "98": {
        start: {
          line: 270,
          column: 26
        },
        end: {
          line: 270,
          column: 53
        }
      },
      "99": {
        start: {
          line: 272,
          column: 27
        },
        end: {
          line: 272,
          column: 55
        }
      },
      "100": {
        start: {
          line: 273,
          column: 27
        },
        end: {
          line: 273,
          column: 55
        }
      },
      "101": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 276,
          column: 9
        }
      },
      "102": {
        start: {
          line: 275,
          column: 12
        },
        end: {
          line: 275,
          column: 21
        }
      },
      "103": {
        start: {
          line: 277,
          column: 29
        },
        end: {
          line: 277,
          column: 52
        }
      },
      "104": {
        start: {
          line: 278,
          column: 26
        },
        end: {
          line: 278,
          column: 43
        }
      },
      "105": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 279,
          column: 49
        }
      },
      "106": {
        start: {
          line: 280,
          column: 25
        },
        end: {
          line: 280,
          column: 54
        }
      },
      "107": {
        start: {
          line: 281,
          column: 34
        },
        end: {
          line: 281,
          column: 65
        }
      },
      "108": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 282,
          column: 48
        }
      },
      "109": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 91
        }
      },
      "110": {
        start: {
          line: 286,
          column: 25
        },
        end: {
          line: 286,
          column: 26
        }
      },
      "111": {
        start: {
          line: 287,
          column: 27
        },
        end: {
          line: 287,
          column: 28
        }
      },
      "112": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 303,
          column: 11
        }
      },
      "113": {
        start: {
          line: 289,
          column: 29
        },
        end: {
          line: 289,
          column: 56
        }
      },
      "114": {
        start: {
          line: 290,
          column: 31
        },
        end: {
          line: 290,
          column: 60
        }
      },
      "115": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 302,
          column: 13
        }
      },
      "116": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 292,
          column: 31
        }
      },
      "117": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 301,
          column: 17
        }
      },
      "118": {
        start: {
          line: 294,
          column: 20
        },
        end: {
          line: 294,
          column: 38
        }
      },
      "119": {
        start: {
          line: 296,
          column: 21
        },
        end: {
          line: 301,
          column: 17
        }
      },
      "120": {
        start: {
          line: 297,
          column: 20
        },
        end: {
          line: 297,
          column: 37
        }
      },
      "121": {
        start: {
          line: 300,
          column: 20
        },
        end: {
          line: 300,
          column: 37
        }
      },
      "122": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 77
        }
      },
      "123": {
        start: {
          line: 307,
          column: 32
        },
        end: {
          line: 307,
          column: 83
        }
      },
      "124": {
        start: {
          line: 308,
          column: 25
        },
        end: {
          line: 308,
          column: 26
        }
      },
      "125": {
        start: {
          line: 309,
          column: 27
        },
        end: {
          line: 309,
          column: 28
        }
      },
      "126": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 325,
          column: 11
        }
      },
      "127": {
        start: {
          line: 311,
          column: 29
        },
        end: {
          line: 311,
          column: 55
        }
      },
      "128": {
        start: {
          line: 312,
          column: 31
        },
        end: {
          line: 312,
          column: 59
        }
      },
      "129": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 324,
          column: 13
        }
      },
      "130": {
        start: {
          line: 314,
          column: 16
        },
        end: {
          line: 314,
          column: 31
        }
      },
      "131": {
        start: {
          line: 315,
          column: 16
        },
        end: {
          line: 323,
          column: 17
        }
      },
      "132": {
        start: {
          line: 316,
          column: 20
        },
        end: {
          line: 316,
          column: 38
        }
      },
      "133": {
        start: {
          line: 318,
          column: 21
        },
        end: {
          line: 323,
          column: 17
        }
      },
      "134": {
        start: {
          line: 319,
          column: 20
        },
        end: {
          line: 319,
          column: 37
        }
      },
      "135": {
        start: {
          line: 322,
          column: 20
        },
        end: {
          line: 322,
          column: 37
        }
      },
      "136": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 326,
          column: 77
        }
      },
      "137": {
        start: {
          line: 329,
          column: 32
        },
        end: {
          line: 329,
          column: 63
        }
      },
      "138": {
        start: {
          line: 330,
          column: 34
        },
        end: {
          line: 330,
          column: 67
        }
      },
      "139": {
        start: {
          line: 331,
          column: 8
        },
        end: {
          line: 333,
          column: 9
        }
      },
      "140": {
        start: {
          line: 332,
          column: 12
        },
        end: {
          line: 332,
          column: 22
        }
      },
      "141": {
        start: {
          line: 334,
          column: 34
        },
        end: {
          line: 339,
          column: 9
        }
      },
      "142": {
        start: {
          line: 340,
          column: 26
        },
        end: {
          line: 340,
          column: 65
        }
      },
      "143": {
        start: {
          line: 341,
          column: 28
        },
        end: {
          line: 341,
          column: 69
        }
      },
      "144": {
        start: {
          line: 342,
          column: 27
        },
        end: {
          line: 342,
          column: 60
        }
      },
      "145": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 344,
          column: 53
        }
      },
      "146": {
        start: {
          line: 347,
          column: 27
        },
        end: {
          line: 347,
          column: 58
        }
      },
      "147": {
        start: {
          line: 348,
          column: 29
        },
        end: {
          line: 348,
          column: 62
        }
      },
      "148": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 351,
          column: 9
        }
      },
      "149": {
        start: {
          line: 350,
          column: 12
        },
        end: {
          line: 350,
          column: 22
        }
      },
      "150": {
        start: {
          line: 352,
          column: 29
        },
        end: {
          line: 357,
          column: 9
        }
      },
      "151": {
        start: {
          line: 358,
          column: 26
        },
        end: {
          line: 358,
          column: 55
        }
      },
      "152": {
        start: {
          line: 359,
          column: 28
        },
        end: {
          line: 359,
          column: 59
        }
      },
      "153": {
        start: {
          line: 360,
          column: 27
        },
        end: {
          line: 360,
          column: 60
        }
      },
      "154": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 361,
          column: 53
        }
      },
      "155": {
        start: {
          line: 364,
          column: 20
        },
        end: {
          line: 364,
          column: 21
        }
      },
      "156": {
        start: {
          line: 365,
          column: 22
        },
        end: {
          line: 365,
          column: 23
        }
      },
      "157": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 371,
          column: 9
        }
      },
      "158": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 368,
          column: 22
        }
      },
      "159": {
        start: {
          line: 370,
          column: 12
        },
        end: {
          line: 370,
          column: 24
        }
      },
      "160": {
        start: {
          line: 373,
          column: 29
        },
        end: {
          line: 373,
          column: 47
        }
      },
      "161": {
        start: {
          line: 374,
          column: 31
        },
        end: {
          line: 374,
          column: 51
        }
      },
      "162": {
        start: {
          line: 375,
          column: 30
        },
        end: {
          line: 375,
          column: 76
        }
      },
      "163": {
        start: {
          line: 376,
          column: 30
        },
        end: {
          line: 376,
          column: 76
        }
      },
      "164": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 382,
          column: 9
        }
      },
      "165": {
        start: {
          line: 378,
          column: 12
        },
        end: {
          line: 378,
          column: 22
        }
      },
      "166": {
        start: {
          line: 379,
          column: 32
        },
        end: {
          line: 379,
          column: 61
        }
      },
      "167": {
        start: {
          line: 380,
          column: 33
        },
        end: {
          line: 380,
          column: 120
        }
      },
      "168": {
        start: {
          line: 381,
          column: 12
        },
        end: {
          line: 381,
          column: 71
        }
      },
      "169": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 383,
          column: 62
        }
      },
      "170": {
        start: {
          line: 387,
          column: 20
        },
        end: {
          line: 387,
          column: 21
        }
      },
      "171": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 391,
          column: 9
        }
      },
      "172": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 24
        }
      },
      "173": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 395,
          column: 9
        }
      },
      "174": {
        start: {
          line: 394,
          column: 12
        },
        end: {
          line: 394,
          column: 24
        }
      },
      "175": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 399,
          column: 9
        }
      },
      "176": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 24
        }
      },
      "177": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 403,
          column: 9
        }
      },
      "178": {
        start: {
          line: 402,
          column: 12
        },
        end: {
          line: 402,
          column: 24
        }
      },
      "179": {
        start: {
          line: 404,
          column: 8
        },
        end: {
          line: 406,
          column: 9
        }
      },
      "180": {
        start: {
          line: 405,
          column: 12
        },
        end: {
          line: 405,
          column: 24
        }
      },
      "181": {
        start: {
          line: 407,
          column: 8
        },
        end: {
          line: 407,
          column: 36
        }
      },
      "182": {
        start: {
          line: 410,
          column: 24
        },
        end: {
          line: 410,
          column: 49
        }
      },
      "183": {
        start: {
          line: 411,
          column: 24
        },
        end: {
          line: 411,
          column: 49
        }
      },
      "184": {
        start: {
          line: 412,
          column: 30
        },
        end: {
          line: 412,
          column: 59
        }
      },
      "185": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 420,
          column: 9
        }
      },
      "186": {
        start: {
          line: 415,
          column: 30
        },
        end: {
          line: 415,
          column: 47
        }
      },
      "187": {
        start: {
          line: 417,
          column: 35
        },
        end: {
          line: 417,
          column: 84
        }
      },
      "188": {
        start: {
          line: 418,
          column: 38
        },
        end: {
          line: 418,
          column: 51
        }
      },
      "189": {
        start: {
          line: 419,
          column: 12
        },
        end: {
          line: 419,
          column: 81
        }
      },
      "190": {
        start: {
          line: 422,
          column: 28
        },
        end: {
          line: 422,
          column: 61
        }
      },
      "191": {
        start: {
          line: 423,
          column: 28
        },
        end: {
          line: 423,
          column: 55
        }
      },
      "192": {
        start: {
          line: 424,
          column: 28
        },
        end: {
          line: 424,
          column: 55
        }
      },
      "193": {
        start: {
          line: 425,
          column: 8
        },
        end: {
          line: 431,
          column: 9
        }
      },
      "194": {
        start: {
          line: 427,
          column: 38
        },
        end: {
          line: 429,
          column: 53
        }
      },
      "195": {
        start: {
          line: 430,
          column: 12
        },
        end: {
          line: 430,
          column: 64
        }
      },
      "196": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 17
        }
      },
      "197": {
        start: {
          line: 435,
          column: 20
        },
        end: {
          line: 435,
          column: 22
        }
      },
      "198": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 439,
          column: 9
        }
      },
      "199": {
        start: {
          line: 438,
          column: 12
        },
        end: {
          line: 438,
          column: 24
        }
      },
      "200": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 442,
          column: 9
        }
      },
      "201": {
        start: {
          line: 441,
          column: 12
        },
        end: {
          line: 441,
          column: 24
        }
      },
      "202": {
        start: {
          line: 443,
          column: 8
        },
        end: {
          line: 445,
          column: 9
        }
      },
      "203": {
        start: {
          line: 444,
          column: 12
        },
        end: {
          line: 444,
          column: 24
        }
      },
      "204": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 448,
          column: 9
        }
      },
      "205": {
        start: {
          line: 447,
          column: 12
        },
        end: {
          line: 447,
          column: 24
        }
      },
      "206": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 449,
          column: 34
        }
      },
      "207": {
        start: {
          line: 452,
          column: 20
        },
        end: {
          line: 452,
          column: 21
        }
      },
      "208": {
        start: {
          line: 453,
          column: 26
        },
        end: {
          line: 453,
          column: 27
        }
      },
      "209": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 460,
          column: 9
        }
      },
      "210": {
        start: {
          line: 456,
          column: 12
        },
        end: {
          line: 456,
          column: 26
        }
      },
      "211": {
        start: {
          line: 457,
          column: 12
        },
        end: {
          line: 459,
          column: 13
        }
      },
      "212": {
        start: {
          line: 458,
          column: 16
        },
        end: {
          line: 458,
          column: 29
        }
      },
      "213": {
        start: {
          line: 462,
          column: 8
        },
        end: {
          line: 462,
          column: 22
        }
      },
      "214": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "215": {
        start: {
          line: 464,
          column: 12
        },
        end: {
          line: 464,
          column: 25
        }
      },
      "216": {
        start: {
          line: 467,
          column: 12
        },
        end: {
          line: 467,
          column: 24
        }
      },
      "217": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 469,
          column: 22
        }
      },
      "218": {
        start: {
          line: 470,
          column: 8
        },
        end: {
          line: 475,
          column: 9
        }
      },
      "219": {
        start: {
          line: 471,
          column: 12
        },
        end: {
          line: 471,
          column: 25
        }
      },
      "220": {
        start: {
          line: 474,
          column: 12
        },
        end: {
          line: 474,
          column: 24
        }
      },
      "221": {
        start: {
          line: 477,
          column: 34
        },
        end: {
          line: 477,
          column: 73
        }
      },
      "222": {
        start: {
          line: 478,
          column: 8
        },
        end: {
          line: 487,
          column: 9
        }
      },
      "223": {
        start: {
          line: 479,
          column: 12
        },
        end: {
          line: 479,
          column: 26
        }
      },
      "224": {
        start: {
          line: 480,
          column: 31
        },
        end: {
          line: 480,
          column: 32
        }
      },
      "225": {
        start: {
          line: 481,
          column: 12
        },
        end: {
          line: 485,
          column: 15
        }
      },
      "226": {
        start: {
          line: 482,
          column: 16
        },
        end: {
          line: 484,
          column: 17
        }
      },
      "227": {
        start: {
          line: 483,
          column: 20
        },
        end: {
          line: 483,
          column: 67
        }
      },
      "228": {
        start: {
          line: 486,
          column: 12
        },
        end: {
          line: 486,
          column: 34
        }
      },
      "229": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 499,
          column: 9
        }
      },
      "230": {
        start: {
          line: 490,
          column: 12
        },
        end: {
          line: 490,
          column: 26
        }
      },
      "231": {
        start: {
          line: 491,
          column: 32
        },
        end: {
          line: 491,
          column: 60
        }
      },
      "232": {
        start: {
          line: 492,
          column: 12
        },
        end: {
          line: 498,
          column: 13
        }
      },
      "233": {
        start: {
          line: 494,
          column: 16
        },
        end: {
          line: 494,
          column: 29
        }
      },
      "234": {
        start: {
          line: 496,
          column: 17
        },
        end: {
          line: 498,
          column: 13
        }
      },
      "235": {
        start: {
          line: 497,
          column: 16
        },
        end: {
          line: 497,
          column: 28
        }
      },
      "236": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 70
        }
      },
      "237": {
        start: {
          line: 505,
          column: 20
        },
        end: {
          line: 505,
          column: 22
        }
      },
      "238": {
        start: {
          line: 506,
          column: 8
        },
        end: {
          line: 508,
          column: 9
        }
      },
      "239": {
        start: {
          line: 507,
          column: 12
        },
        end: {
          line: 507,
          column: 24
        }
      },
      "240": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 511,
          column: 9
        }
      },
      "241": {
        start: {
          line: 510,
          column: 12
        },
        end: {
          line: 510,
          column: 24
        }
      },
      "242": {
        start: {
          line: 512,
          column: 8
        },
        end: {
          line: 514,
          column: 9
        }
      },
      "243": {
        start: {
          line: 513,
          column: 12
        },
        end: {
          line: 513,
          column: 23
        }
      },
      "244": {
        start: {
          line: 515,
          column: 8
        },
        end: {
          line: 515,
          column: 36
        }
      },
      "245": {
        start: {
          line: 518,
          column: 0
        },
        end: {
          line: 518,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 14,
            column: 4
          },
          end: {
            line: 14,
            column: 5
          }
        },
        loc: {
          start: {
            line: 14,
            column: 66
          },
          end: {
            line: 59,
            column: 5
          }
        },
        line: 14
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 63,
            column: 4
          },
          end: {
            line: 63,
            column: 5
          }
        },
        loc: {
          start: {
            line: 63,
            column: 68
          },
          end: {
            line: 106,
            column: 5
          }
        },
        line: 63
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 110,
            column: 5
          }
        },
        loc: {
          start: {
            line: 110,
            column: 57
          },
          end: {
            line: 151,
            column: 5
          }
        },
        line: 110
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 144,
            column: 22
          },
          end: {
            line: 144,
            column: 23
          }
        },
        loc: {
          start: {
            line: 144,
            column: 32
          },
          end: {
            line: 144,
            column: 75
          }
        },
        line: 144
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 155,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        },
        loc: {
          start: {
            line: 155,
            column: 56
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 155
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 189,
            column: 22
          },
          end: {
            line: 189,
            column: 23
          }
        },
        loc: {
          start: {
            line: 189,
            column: 32
          },
          end: {
            line: 189,
            column: 75
          }
        },
        line: 189
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 81
          },
          end: {
            line: 241,
            column: 5
          }
        },
        line: 200
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        loc: {
          start: {
            line: 243,
            column: 85
          },
          end: {
            line: 265,
            column: 5
          }
        },
        line: 243
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 266,
            column: 4
          },
          end: {
            line: 266,
            column: 5
          }
        },
        loc: {
          start: {
            line: 266,
            column: 68
          },
          end: {
            line: 283,
            column: 5
          }
        },
        line: 266
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 284,
            column: 5
          }
        },
        loc: {
          start: {
            line: 284,
            column: 71
          },
          end: {
            line: 305,
            column: 5
          }
        },
        line: 284
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 288,
            column: 24
          },
          end: {
            line: 288,
            column: 25
          }
        },
        loc: {
          start: {
            line: 288,
            column: 34
          },
          end: {
            line: 303,
            column: 9
          }
        },
        line: 288
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 306,
            column: 5
          }
        },
        loc: {
          start: {
            line: 306,
            column: 70
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 306
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 310,
            column: 32
          },
          end: {
            line: 310,
            column: 33
          }
        },
        loc: {
          start: {
            line: 310,
            column: 42
          },
          end: {
            line: 325,
            column: 9
          }
        },
        line: 310
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 328,
            column: 4
          },
          end: {
            line: 328,
            column: 5
          }
        },
        loc: {
          start: {
            line: 328,
            column: 73
          },
          end: {
            line: 345,
            column: 5
          }
        },
        line: 328
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 346,
            column: 4
          },
          end: {
            line: 346,
            column: 5
          }
        },
        loc: {
          start: {
            line: 346,
            column: 68
          },
          end: {
            line: 362,
            column: 5
          }
        },
        line: 346
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 363,
            column: 4
          },
          end: {
            line: 363,
            column: 5
          }
        },
        loc: {
          start: {
            line: 363,
            column: 73
          },
          end: {
            line: 384,
            column: 5
          }
        },
        line: 363
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 386,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        },
        loc: {
          start: {
            line: 386,
            column: 84
          },
          end: {
            line: 408,
            column: 5
          }
        },
        line: 386
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 409,
            column: 4
          },
          end: {
            line: 409,
            column: 5
          }
        },
        loc: {
          start: {
            line: 409,
            column: 69
          },
          end: {
            line: 433,
            column: 5
          }
        },
        line: 409
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 434,
            column: 4
          },
          end: {
            line: 434,
            column: 5
          }
        },
        loc: {
          start: {
            line: 434,
            column: 72
          },
          end: {
            line: 450,
            column: 5
          }
        },
        line: 434
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 451,
            column: 4
          },
          end: {
            line: 451,
            column: 5
          }
        },
        loc: {
          start: {
            line: 451,
            column: 74
          },
          end: {
            line: 501,
            column: 5
          }
        },
        line: 451
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 481,
            column: 38
          },
          end: {
            line: 481,
            column: 39
          }
        },
        loc: {
          start: {
            line: 481,
            column: 49
          },
          end: {
            line: 485,
            column: 13
          }
        },
        line: 481
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 502,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        },
        loc: {
          start: {
            line: 502,
            column: 75
          },
          end: {
            line: 516,
            column: 5
          }
        },
        line: 502
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 25,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 25,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 26
          }
        }, {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 46
          }
        }, {
          start: {
            line: 23,
            column: 50
          },
          end: {
            line: 23,
            column: 62
          }
        }, {
          start: {
            line: 23,
            column: 66
          },
          end: {
            line: 23,
            column: 80
          }
        }],
        line: 23
      },
      "2": {
        loc: {
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 72,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 72,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "3": {
        loc: {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 70,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 70,
            column: 26
          }
        }, {
          start: {
            line: 70,
            column: 30
          },
          end: {
            line: 70,
            column: 42
          }
        }, {
          start: {
            line: 70,
            column: 46
          },
          end: {
            line: 70,
            column: 55
          }
        }],
        line: 70
      },
      "4": {
        loc: {
          start: {
            line: 110,
            column: 45
          },
          end: {
            line: 110,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 110,
            column: 53
          },
          end: {
            line: 110,
            column: 55
          }
        }],
        line: 110
      },
      "5": {
        loc: {
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 115,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 12
          },
          end: {
            line: 115,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "6": {
        loc: {
          start: {
            line: 113,
            column: 16
          },
          end: {
            line: 113,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 16
          },
          end: {
            line: 113,
            column: 26
          }
        }, {
          start: {
            line: 113,
            column: 30
          },
          end: {
            line: 113,
            column: 49
          }
        }],
        line: 113
      },
      "7": {
        loc: {
          start: {
            line: 122,
            column: 20
          },
          end: {
            line: 135,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 20
          },
          end: {
            line: 135,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "8": {
        loc: {
          start: {
            line: 155,
            column: 44
          },
          end: {
            line: 155,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 155,
            column: 52
          },
          end: {
            line: 155,
            column: 54
          }
        }],
        line: 155
      },
      "9": {
        loc: {
          start: {
            line: 158,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "10": {
        loc: {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 158,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 158,
            column: 26
          }
        }, {
          start: {
            line: 158,
            column: 30
          },
          end: {
            line: 158,
            column: 49
          }
        }],
        line: 158
      },
      "11": {
        loc: {
          start: {
            line: 167,
            column: 20
          },
          end: {
            line: 180,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 20
          },
          end: {
            line: 180,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "12": {
        loc: {
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "13": {
        loc: {
          start: {
            line: 208,
            column: 30
          },
          end: {
            line: 208,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 54
          },
          end: {
            line: 208,
            column: 64
          }
        }, {
          start: {
            line: 208,
            column: 67
          },
          end: {
            line: 208,
            column: 76
          }
        }],
        line: 208
      },
      "14": {
        loc: {
          start: {
            line: 210,
            column: 35
          },
          end: {
            line: 210,
            column: 65
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 210,
            column: 59
          },
          end: {
            line: 210,
            column: 60
          }
        }, {
          start: {
            line: 210,
            column: 63
          },
          end: {
            line: 210,
            column: 65
          }
        }],
        line: 210
      },
      "15": {
        loc: {
          start: {
            line: 214,
            column: 38
          },
          end: {
            line: 216,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 215,
            column: 18
          },
          end: {
            line: 215,
            column: 93
          }
        }, {
          start: {
            line: 216,
            column: 18
          },
          end: {
            line: 216,
            column: 101
          }
        }],
        line: 214
      },
      "16": {
        loc: {
          start: {
            line: 246,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "17": {
        loc: {
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 252,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 252,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "18": {
        loc: {
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "19": {
        loc: {
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "20": {
        loc: {
          start: {
            line: 261,
            column: 8
          },
          end: {
            line: 263,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 8
          },
          end: {
            line: 263,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "21": {
        loc: {
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 276,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 276,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "22": {
        loc: {
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 302,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 302,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "23": {
        loc: {
          start: {
            line: 291,
            column: 16
          },
          end: {
            line: 291,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 16
          },
          end: {
            line: 291,
            column: 44
          }
        }, {
          start: {
            line: 291,
            column: 48
          },
          end: {
            line: 291,
            column: 78
          }
        }],
        line: 291
      },
      "24": {
        loc: {
          start: {
            line: 293,
            column: 16
          },
          end: {
            line: 301,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 16
          },
          end: {
            line: 301,
            column: 17
          }
        }, {
          start: {
            line: 296,
            column: 21
          },
          end: {
            line: 301,
            column: 17
          }
        }],
        line: 293
      },
      "25": {
        loc: {
          start: {
            line: 296,
            column: 21
          },
          end: {
            line: 301,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 21
          },
          end: {
            line: 301,
            column: 17
          }
        }, {
          start: {
            line: 299,
            column: 21
          },
          end: {
            line: 301,
            column: 17
          }
        }],
        line: 296
      },
      "26": {
        loc: {
          start: {
            line: 304,
            column: 15
          },
          end: {
            line: 304,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 304,
            column: 34
          },
          end: {
            line: 304,
            column: 71
          }
        }, {
          start: {
            line: 304,
            column: 74
          },
          end: {
            line: 304,
            column: 76
          }
        }],
        line: 304
      },
      "27": {
        loc: {
          start: {
            line: 313,
            column: 12
          },
          end: {
            line: 324,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 12
          },
          end: {
            line: 324,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 313
      },
      "28": {
        loc: {
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 44
          }
        }, {
          start: {
            line: 313,
            column: 48
          },
          end: {
            line: 313,
            column: 78
          }
        }],
        line: 313
      },
      "29": {
        loc: {
          start: {
            line: 315,
            column: 16
          },
          end: {
            line: 323,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 16
          },
          end: {
            line: 323,
            column: 17
          }
        }, {
          start: {
            line: 318,
            column: 21
          },
          end: {
            line: 323,
            column: 17
          }
        }],
        line: 315
      },
      "30": {
        loc: {
          start: {
            line: 318,
            column: 21
          },
          end: {
            line: 323,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 21
          },
          end: {
            line: 323,
            column: 17
          }
        }, {
          start: {
            line: 321,
            column: 21
          },
          end: {
            line: 323,
            column: 17
          }
        }],
        line: 318
      },
      "31": {
        loc: {
          start: {
            line: 326,
            column: 15
          },
          end: {
            line: 326,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 326,
            column: 34
          },
          end: {
            line: 326,
            column: 71
          }
        }, {
          start: {
            line: 326,
            column: 74
          },
          end: {
            line: 326,
            column: 76
          }
        }],
        line: 326
      },
      "32": {
        loc: {
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 333,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 333,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      },
      "33": {
        loc: {
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 331,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 331,
            column: 12
          },
          end: {
            line: 331,
            column: 47
          }
        }, {
          start: {
            line: 331,
            column: 51
          },
          end: {
            line: 331,
            column: 88
          }
        }],
        line: 331
      },
      "34": {
        loc: {
          start: {
            line: 340,
            column: 26
          },
          end: {
            line: 340,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 26
          },
          end: {
            line: 340,
            column: 60
          }
        }, {
          start: {
            line: 340,
            column: 64
          },
          end: {
            line: 340,
            column: 65
          }
        }],
        line: 340
      },
      "35": {
        loc: {
          start: {
            line: 341,
            column: 28
          },
          end: {
            line: 341,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 341,
            column: 28
          },
          end: {
            line: 341,
            column: 64
          }
        }, {
          start: {
            line: 341,
            column: 68
          },
          end: {
            line: 341,
            column: 69
          }
        }],
        line: 341
      },
      "36": {
        loc: {
          start: {
            line: 349,
            column: 8
          },
          end: {
            line: 351,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 8
          },
          end: {
            line: 351,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "37": {
        loc: {
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 349,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 349,
            column: 42
          }
        }, {
          start: {
            line: 349,
            column: 46
          },
          end: {
            line: 349,
            column: 78
          }
        }],
        line: 349
      },
      "38": {
        loc: {
          start: {
            line: 358,
            column: 26
          },
          end: {
            line: 358,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 358,
            column: 26
          },
          end: {
            line: 358,
            column: 50
          }
        }, {
          start: {
            line: 358,
            column: 54
          },
          end: {
            line: 358,
            column: 55
          }
        }],
        line: 358
      },
      "39": {
        loc: {
          start: {
            line: 359,
            column: 28
          },
          end: {
            line: 359,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 359,
            column: 28
          },
          end: {
            line: 359,
            column: 54
          }
        }, {
          start: {
            line: 359,
            column: 58
          },
          end: {
            line: 359,
            column: 59
          }
        }],
        line: 359
      },
      "40": {
        loc: {
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 371,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 371,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 367
      },
      "41": {
        loc: {
          start: {
            line: 367,
            column: 12
          },
          end: {
            line: 367,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 367,
            column: 12
          },
          end: {
            line: 367,
            column: 48
          }
        }, {
          start: {
            line: 367,
            column: 52
          },
          end: {
            line: 367,
            column: 90
          }
        }],
        line: 367
      },
      "42": {
        loc: {
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 382,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 382,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 377
      },
      "43": {
        loc: {
          start: {
            line: 383,
            column: 15
          },
          end: {
            line: 383,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 383,
            column: 29
          },
          end: {
            line: 383,
            column: 56
          }
        }, {
          start: {
            line: 383,
            column: 59
          },
          end: {
            line: 383,
            column: 61
          }
        }],
        line: 383
      },
      "44": {
        loc: {
          start: {
            line: 389,
            column: 8
          },
          end: {
            line: 391,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 8
          },
          end: {
            line: 391,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "45": {
        loc: {
          start: {
            line: 393,
            column: 8
          },
          end: {
            line: 395,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 8
          },
          end: {
            line: 395,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      },
      "46": {
        loc: {
          start: {
            line: 397,
            column: 8
          },
          end: {
            line: 399,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 8
          },
          end: {
            line: 399,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "47": {
        loc: {
          start: {
            line: 401,
            column: 8
          },
          end: {
            line: 403,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 401,
            column: 8
          },
          end: {
            line: 403,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 401
      },
      "48": {
        loc: {
          start: {
            line: 404,
            column: 8
          },
          end: {
            line: 406,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 8
          },
          end: {
            line: 406,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "49": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 420,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 420,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "50": {
        loc: {
          start: {
            line: 413,
            column: 12
          },
          end: {
            line: 413,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 413,
            column: 12
          },
          end: {
            line: 413,
            column: 36
          }
        }, {
          start: {
            line: 413,
            column: 40
          },
          end: {
            line: 413,
            column: 64
          }
        }],
        line: 413
      },
      "51": {
        loc: {
          start: {
            line: 425,
            column: 8
          },
          end: {
            line: 431,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 425,
            column: 8
          },
          end: {
            line: 431,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 425
      },
      "52": {
        loc: {
          start: {
            line: 425,
            column: 12
          },
          end: {
            line: 425,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 425,
            column: 12
          },
          end: {
            line: 425,
            column: 40
          }
        }, {
          start: {
            line: 425,
            column: 44
          },
          end: {
            line: 425,
            column: 72
          }
        }],
        line: 425
      },
      "53": {
        loc: {
          start: {
            line: 427,
            column: 38
          },
          end: {
            line: 429,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 428,
            column: 18
          },
          end: {
            line: 428,
            column: 53
          }
        }, {
          start: {
            line: 429,
            column: 18
          },
          end: {
            line: 429,
            column: 53
          }
        }],
        line: 427
      },
      "54": {
        loc: {
          start: {
            line: 437,
            column: 8
          },
          end: {
            line: 439,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 437,
            column: 8
          },
          end: {
            line: 439,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 437
      },
      "55": {
        loc: {
          start: {
            line: 437,
            column: 12
          },
          end: {
            line: 437,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 437,
            column: 12
          },
          end: {
            line: 437,
            column: 49
          }
        }, {
          start: {
            line: 437,
            column: 53
          },
          end: {
            line: 437,
            column: 83
          }
        }],
        line: 437
      },
      "56": {
        loc: {
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 442,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 8
          },
          end: {
            line: 442,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "57": {
        loc: {
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 440,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 440,
            column: 12
          },
          end: {
            line: 440,
            column: 47
          }
        }, {
          start: {
            line: 440,
            column: 51
          },
          end: {
            line: 440,
            column: 78
          }
        }],
        line: 440
      },
      "58": {
        loc: {
          start: {
            line: 443,
            column: 8
          },
          end: {
            line: 445,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 443,
            column: 8
          },
          end: {
            line: 445,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 443
      },
      "59": {
        loc: {
          start: {
            line: 443,
            column: 12
          },
          end: {
            line: 443,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 443,
            column: 12
          },
          end: {
            line: 443,
            column: 50
          }
        }, {
          start: {
            line: 443,
            column: 54
          },
          end: {
            line: 443,
            column: 84
          }
        }],
        line: 443
      },
      "60": {
        loc: {
          start: {
            line: 446,
            column: 8
          },
          end: {
            line: 448,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 446,
            column: 8
          },
          end: {
            line: 448,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 446
      },
      "61": {
        loc: {
          start: {
            line: 446,
            column: 12
          },
          end: {
            line: 446,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 446,
            column: 12
          },
          end: {
            line: 446,
            column: 53
          }
        }, {
          start: {
            line: 446,
            column: 57
          },
          end: {
            line: 446,
            column: 86
          }
        }],
        line: 446
      },
      "62": {
        loc: {
          start: {
            line: 455,
            column: 8
          },
          end: {
            line: 460,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 455,
            column: 8
          },
          end: {
            line: 460,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 455
      },
      "63": {
        loc: {
          start: {
            line: 457,
            column: 12
          },
          end: {
            line: 459,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 457,
            column: 12
          },
          end: {
            line: 459,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 457
      },
      "64": {
        loc: {
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 468,
            column: 9
          }
        }, {
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 468,
            column: 9
          }
        }],
        line: 463
      },
      "65": {
        loc: {
          start: {
            line: 470,
            column: 8
          },
          end: {
            line: 475,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 470,
            column: 8
          },
          end: {
            line: 475,
            column: 9
          }
        }, {
          start: {
            line: 473,
            column: 13
          },
          end: {
            line: 475,
            column: 9
          }
        }],
        line: 470
      },
      "66": {
        loc: {
          start: {
            line: 478,
            column: 8
          },
          end: {
            line: 487,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 478,
            column: 8
          },
          end: {
            line: 487,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 478
      },
      "67": {
        loc: {
          start: {
            line: 482,
            column: 16
          },
          end: {
            line: 484,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 16
          },
          end: {
            line: 484,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 482
      },
      "68": {
        loc: {
          start: {
            line: 489,
            column: 8
          },
          end: {
            line: 499,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 489,
            column: 8
          },
          end: {
            line: 499,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 489
      },
      "69": {
        loc: {
          start: {
            line: 492,
            column: 12
          },
          end: {
            line: 498,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 492,
            column: 12
          },
          end: {
            line: 498,
            column: 13
          }
        }, {
          start: {
            line: 496,
            column: 17
          },
          end: {
            line: 498,
            column: 13
          }
        }],
        line: 492
      },
      "70": {
        loc: {
          start: {
            line: 492,
            column: 16
          },
          end: {
            line: 493,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 492,
            column: 17
          },
          end: {
            line: 492,
            column: 66
          }
        }, {
          start: {
            line: 492,
            column: 70
          },
          end: {
            line: 492,
            column: 81
          }
        }, {
          start: {
            line: 493,
            column: 17
          },
          end: {
            line: 493,
            column: 65
          }
        }, {
          start: {
            line: 493,
            column: 69
          },
          end: {
            line: 493,
            column: 81
          }
        }],
        line: 492
      },
      "71": {
        loc: {
          start: {
            line: 496,
            column: 17
          },
          end: {
            line: 498,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 17
          },
          end: {
            line: 498,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "72": {
        loc: {
          start: {
            line: 500,
            column: 15
          },
          end: {
            line: 500,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 500,
            column: 33
          },
          end: {
            line: 500,
            column: 64
          }
        }, {
          start: {
            line: 500,
            column: 67
          },
          end: {
            line: 500,
            column: 69
          }
        }],
        line: 500
      },
      "73": {
        loc: {
          start: {
            line: 506,
            column: 8
          },
          end: {
            line: 508,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 506,
            column: 8
          },
          end: {
            line: 508,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 506
      },
      "74": {
        loc: {
          start: {
            line: 509,
            column: 8
          },
          end: {
            line: 511,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 509,
            column: 8
          },
          end: {
            line: 511,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 509
      },
      "75": {
        loc: {
          start: {
            line: 512,
            column: 8
          },
          end: {
            line: 514,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 8
          },
          end: {
            line: 514,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 512
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0, 0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchingService.ts",
      mappings: ";;;AACA,2CAAqF;AACrF,iDAA8C;AAC9C,2DAAkD;AAClD,4CAAyC;AACzC,gDAA6C;AAC7C,uDAAoD;AA2BpD,MAAa,eAAe;IAE1B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,0BAA0B,CACrC,MAAsB,EACtB,YAA4B;QAE5B,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,CAAC,SAAS,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACjF,wBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;gBACpC,wBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;gBAClD,uBAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;gBAC3B,uBAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;aAC1C,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrE,MAAM,IAAI,mBAAQ,CAAC,wCAAwC,EAAE,GAAG,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,OAAO,GAAyB;gBACpC,QAAQ,EAAE,IAAI,CAAC,kCAAkC,CAAC,WAAW,EAAE,aAAa,EAAE,SAAS,CAAC;gBACxF,MAAM,EAAE,IAAI,CAAC,gCAAgC,CAAC,SAAS,EAAE,eAAe,CAAC;gBACzE,SAAS,EAAE,IAAI,CAAC,mCAAmC,CAAC,SAAS,EAAE,eAAe,CAAC;gBAC/E,WAAW,EAAE,IAAI,CAAC,qCAAqC,CAAC,SAAS,EAAE,eAAe,CAAC;gBACnF,QAAQ,EAAE,IAAI,CAAC,kCAAkC,CAAC,SAAS,EAAE,eAAe,CAAC;gBAC7E,WAAW,EAAE,IAAI,CAAC,qCAAqC,CAAC,SAAS,EAAE,eAAe,CAAC;gBACnF,WAAW,EAAE,IAAI,CAAC,gCAAgC,CAAC,SAAS,EAAE,eAAe,CAAC;gBAC9E,OAAO,EAAE,CAAC;aACX,CAAC;YAEF,mCAAmC;YACnC,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAC1B,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACjC,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;gBACvC,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBAC3C,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBAC3C,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAC5C,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,mBAAQ,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,8BAA8B,CACzC,MAAsB,EACtB,UAA0B;QAE1B,IAAI,CAAC;YACH,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC3D,wBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;gBACpC,uBAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;gBAC3B,mBAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5C,MAAM,IAAI,mBAAQ,CAAC,kDAAkD,EAAE,GAAG,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,OAAO,GAAyB;gBACpC,QAAQ,EAAE,IAAI,CAAC,sCAAsC,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;gBACvF,MAAM,EAAE,IAAI,CAAC,oCAAoC,CAAC,SAAS,EAAE,QAAQ,CAAC;gBACtE,SAAS,EAAE,IAAI,CAAC,uCAAuC,CAAC,SAAS,EAAE,QAAQ,CAAC;gBAC5E,WAAW,EAAE,IAAI,CAAC,yCAAyC,CAAC,SAAS,EAAE,QAAQ,CAAC;gBAChF,QAAQ,EAAE,EAAE,EAAE,+BAA+B;gBAC7C,WAAW,EAAE,IAAI,CAAC,yCAAyC,CAAC,SAAS,EAAE,QAAQ,CAAC;gBAChF,WAAW,EAAE,EAAE,EAAE,+BAA+B;gBAChD,OAAO,EAAE,CAAC;aACX,CAAC;YAEF,yDAAyD;YACzD,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAC1B,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACjC,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;gBACvC,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBAC3C,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBAC3C,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAC5C,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,mBAAQ,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,MAAsB,EACtB,QAAgB,EAAE;QAElB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,wBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACtC,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,mDAAmD;YACnD,MAAM,UAAU,GAAG,MAAM,iCAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAClF,MAAM,OAAO,GAAqB,EAAE,CAAC;YAErC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;oBAEnF,IAAI,aAAa,CAAC,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;wBAChF,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;wBAChF,MAAM,YAAY,GAAG,iCAAe,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;wBAEzE,OAAO,CAAC,IAAI,CAAC;4BACX,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAC5B,IAAI,EAAE,MAAM;4BACZ,kBAAkB,EAAE,aAAa,CAAC,OAAO;4BACzC,oBAAoB,EAAE,aAAa;4BACnC,QAAQ;4BACR,YAAY;4BACZ,eAAe,EAAE,EAAE,EAAE,0CAA0C;4BAC/D,iBAAiB,EAAE,EAAE,CAAC,8CAA8C;yBACrE,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,mDAAmD,SAAS,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;oBACxF,SAAS;gBACX,CAAC;YACH,CAAC;YAED,qDAAqD;YACrD,OAAO,OAAO;iBACX,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,kBAAkB,CAAC;iBAC3D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,mBAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,MAAsB,EACtB,QAAgB,EAAE;QAElB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,wBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACtC,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,oCAAoC;YACpC,MAAM,UAAU,GAAG,MAAM,iCAAe,CAAC,qBAAqB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAClF,MAAM,OAAO,GAAqB,EAAE,CAAC;YAErC,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAEtF,IAAI,aAAa,CAAC,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;wBAChF,MAAM,QAAQ,GAAG,MAAM,iCAAe,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;wBACvF,MAAM,YAAY,GAAG,iCAAe,CAAC,4BAA4B,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;wBAE3F,OAAO,CAAC,IAAI,CAAC;4BACX,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAC3B,IAAI,EAAE,UAAU;4BAChB,kBAAkB,EAAE,aAAa,CAAC,OAAO;4BACzC,oBAAoB,EAAE,aAAa;4BACnC,QAAQ;4BACR,YAAY;4BACZ,eAAe,EAAE,EAAE;4BACnB,iBAAiB,EAAE,EAAE;yBACtB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,kDAAkD,QAAQ,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;oBACtF,SAAS;gBACX,CAAC;YACH,CAAC;YAED,qDAAqD;YACrD,OAAO,OAAO;iBACX,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,kBAAkB,CAAC;iBAC3D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,mBAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,MAAsB,EACtB,QAAwB,EACxB,UAA+B,EAC/B,oBAA0C;QAE1C,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,aAAa,GAAG,MAAM,aAAK,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;YAC5E,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,aAAa,CAAC;YACvB,CAAC;YAED,uBAAuB;YACvB,MAAM,SAAS,GAAG,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;YAEjE,kEAAkE;YAClE,MAAM,cAAc,GAAG,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC;YAExD,+BAA+B;YAC/B,MAAM,iBAAiB,GAAG,UAAU,KAAK,MAAM;gBAC7C,CAAC,CAAC,MAAM,iCAAe,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC;gBAC3D,CAAC,CAAC,MAAM,iCAAe,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEtE,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,MAAM,CAAC;YACxD,MAAM,UAAU,GAAG,MAAM,iCAAe,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEvF,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC;gBACtB,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,SAAS;gBACT,kBAAkB,EAAE,oBAAoB,CAAC,OAAO;gBAChD,oBAAoB;gBACpB,SAAS;gBACT,iBAAiB;gBACjB,mBAAmB;gBACnB,UAAU;gBACV,WAAW,EAAE,iCAAe,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;gBACvE,iBAAiB,EAAE,IAAI,IAAI,EAAE;aAC9B,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;YAEnB,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,uBAAuB,MAAM,QAAQ,UAAU,IAAI,QAAQ,SAAS,oBAAoB,CAAC,OAAO,iBAAiB,CAAC,CAAC;YAEnJ,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,6DAA6D;IAErD,MAAM,CAAC,kCAAkC,CAC/C,WAAgB,EAChB,aAAkB,EAClB,SAA4B;QAE5B,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,mBAAmB;QACnB,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;YAClE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;YAChE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;YAChE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,IAAI,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;YACtE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;YACrE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,SAA4B,EAC5B,WAA8B;QAE9B,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC;QAC9C,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC;QAE9C,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAEhD,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;YAC5B,OAAO,CAAC,CAAC,CAAC,aAAa;QACzB,CAAC;QAED,MAAM,YAAY,GAAG,UAAU,GAAG,UAAU,CAAC;QAC7C,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;QACpC,MAAM,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;QAC1C,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAE/C,MAAM,iBAAiB,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC;QAC1D,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEO,MAAM,CAAC,mCAAmC,CAChD,SAA4B,EAC5B,WAA8B;QAE9B,MAAM,OAAO,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACpF,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,MAA0C,CAAC,CAAC;YACjF,MAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,MAA4C,CAAC,CAAC;YAEvF,IAAI,QAAQ,KAAK,eAAe,IAAI,UAAU,KAAK,eAAe,EAAE,CAAC;gBACnE,YAAY,EAAE,CAAC;gBACf,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;oBAC5B,UAAU,IAAI,GAAG,CAAC;gBACpB,CAAC;qBAAM,IAAI,iCAAe,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;oBACvE,UAAU,IAAI,EAAE,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,UAAU,IAAI,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;IAEO,MAAM,CAAC,kCAAkC,CAC/C,SAA4B,EAC5B,WAA8B;QAE9B,MAAM,eAAe,GAAG,CAAC,eAAe,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC5E,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAyC,CAAC,CAAC;YAC/E,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,MAA2C,CAAC,CAAC;YAErF,IAAI,QAAQ,KAAK,eAAe,IAAI,UAAU,KAAK,eAAe,EAAE,CAAC;gBACnE,YAAY,EAAE,CAAC;gBACf,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;oBAC5B,UAAU,IAAI,GAAG,CAAC;gBACpB,CAAC;qBAAM,IAAI,iCAAe,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;oBACtE,UAAU,IAAI,EAAE,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,UAAU,IAAI,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;IAEO,MAAM,CAAC,qCAAqC,CAClD,SAA4B,EAC5B,WAA8B;QAE9B,MAAM,eAAe,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC;QACxD,MAAM,iBAAiB,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC;QAE5D,IAAI,eAAe,KAAK,eAAe,IAAI,iBAAiB,KAAK,eAAe,EAAE,CAAC;YACjF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,iBAAiB,GAAG;YACxB,YAAY,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,SAAS,GAAG,iBAAiB,CAAC,eAAiD,CAAC,IAAI,CAAC,CAAC;QAC5F,MAAM,WAAW,GAAG,iBAAiB,CAAC,iBAAmD,CAAC,IAAI,CAAC,CAAC;QAChG,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QAErD,uEAAuE;QACvE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEO,MAAM,CAAC,gCAAgC,CAC7C,SAA4B,EAC5B,WAA8B;QAE9B,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;QACnD,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;QAEvD,IAAI,UAAU,KAAK,eAAe,IAAI,YAAY,KAAK,eAAe,EAAE,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,YAAY,GAAG;YACnB,aAAa,EAAE,CAAC;YAChB,QAAQ,EAAE,CAAC;YACX,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;SACb,CAAC;QAEF,MAAM,SAAS,GAAG,YAAY,CAAC,UAAuC,CAAC,IAAI,CAAC,CAAC;QAC7E,MAAM,WAAW,GAAG,YAAY,CAAC,YAAyC,CAAC,IAAI,CAAC,CAAC;QACjF,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/C,CAAC;IAEO,MAAM,CAAC,qCAAqC,CAClD,SAA4B,EAC5B,WAA8B;QAE9B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,kCAAkC;QAClC,IAAI,SAAS,CAAC,gBAAgB,KAAK,KAAK,IAAI,WAAW,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YACnF,OAAO,EAAE,CAAC;YACV,uDAAuD;YACvD,KAAK,IAAI,EAAE,CAAC,CAAC,wBAAwB;QACvC,CAAC;QAED,0BAA0B;QAC1B,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC;QACxC,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE5C,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QACrE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAErE,IAAI,aAAa,IAAI,aAAa,EAAE,CAAC;YACnC,OAAO,EAAE,CAAC;YACV,MAAM,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;YAClD,MAAM,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7G,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxD,CAAC;IAED,6CAA6C;IAErC,MAAM,CAAC,sCAAsC,CACnD,WAAgB,EAChB,QAAa,EACb,SAA4B;QAE5B,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,mBAAmB;QACnB,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC7D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;YAC3D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,IAAI,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;YACjE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;YAChE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,oCAAoC,CACjD,SAA4B,EAC5B,QAAa;QAEb,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;QAC1C,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC;QAEpD,IAAI,aAAa,IAAI,OAAO,IAAI,aAAa,IAAI,OAAO,EAAE,CAAC;YACzD,gCAAgC;YAChC,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;YAEpC,4CAA4C;YAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;YACzE,MAAM,iBAAiB,GAAG,SAAS,GAAG,CAAC,CAAC;YAExC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,cAAc,GAAG,iBAAiB,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,sCAAsC;QACtC,MAAM,WAAW,GAAG,SAAS,CAAC,iBAAiB,GAAG,GAAG,CAAC;QACtD,MAAM,WAAW,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;QAChD,MAAM,WAAW,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC;QAEhD,IAAI,aAAa,IAAI,WAAW,IAAI,aAAa,IAAI,WAAW,EAAE,CAAC;YACjE,wBAAwB;YACxB,MAAM,iBAAiB,GAAG,aAAa,GAAG,OAAO;gBAC/C,CAAC,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,OAAO;gBACrC,CAAC,CAAC,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC;YAExC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,CAAC,CAAC,CAAC,yBAAyB;IACrC,CAAC;IAEO,MAAM,CAAC,uCAAuC,CACpD,SAA4B,EAC5B,QAAa;QAEb,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,4BAA4B;QAC5B,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC5E,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC7E,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC/E,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,MAAM,CAAC,yCAAyC,CACtD,SAA4B,EAC5B,QAAa;QAEb,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,2BAA2B;QAC3B,IAAI,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,WAAW,EAAE,CAAC;YACd,IAAI,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChF,KAAK,IAAI,GAAG,CAAC;YACf,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,WAAW,EAAE,CAAC;QACd,IAAI,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,mBAAmB,CAAC,eAAe,EAAE,CAAC;YACvE,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,EAAE,CAAC,CAAC,iBAAiB;QAChC,CAAC;QAED,WAAW,EAAE,CAAC;QACd,IAAI,QAAQ,CAAC,SAAS,IAAI,SAAS,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;YACzE,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,MAAM,iBAAiB,GAAG,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC;QAClE,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,WAAW,EAAE,CAAC;YACd,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAClC,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;oBAChC,YAAY,IAAI,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC;gBACjD,CAAC;YACH,CAAC,CAAC,CAAC;YACH,KAAK,IAAI,YAAY,CAAC;QACxB,CAAC;QAED,uBAAuB;QACvB,IAAI,SAAS,CAAC,mBAAmB,CAAC,SAAS,KAAK,eAAe,EAAE,CAAC;YAChE,WAAW,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC;YACjD,IACE,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,KAAK,KAAK,IAAI,WAAW,CAAC;gBAClE,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,EAClE,CAAC;gBACD,KAAK,IAAI,GAAG,CAAC;YACf,CAAC;iBAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjE,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChE,CAAC;IAEO,MAAM,CAAC,yCAAyC,CACtD,UAA6B,EAC7B,QAAa;QAEb,uDAAuD;QACvD,uDAAuD;QACvD,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACvC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,CAAC,CAAC,iDAAiD;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;CAEF;AAhpBD,0CAgpBC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchingService.ts"],
      sourcesContent: ["import { Types } from 'mongoose';\r\nimport { Match, MatchPreferences, IMatch, IMatchPreferences } from '../models/Match';\r\nimport { Property } from '../models/Property';\r\nimport { Profile } from '../models/Profile.model';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\nimport { MatchingHelpers } from './matchingHelpers';\r\n\r\nexport interface CompatibilityFactors {\r\n  location: number;\r\n  budget: number;\r\n  lifestyle: number;\r\n  preferences: number;\r\n  schedule: number;\r\n  cleanliness: number;\r\n  socialLevel: number;\r\n  overall: number;\r\n}\r\n\r\n// Re-export IMatchPreferences for use in other files\r\nexport type { IMatchPreferences } from '../models/Match';\r\n\r\nexport interface MatchCandidate {\r\n  id: string;\r\n  type: 'user' | 'property';\r\n  compatibilityScore: number;\r\n  compatibilityFactors: CompatibilityFactors;\r\n  distance: number;\r\n  matchReasons: string[];\r\n  commonInterests: string[];\r\n  sharedPreferences: string[];\r\n}\r\n\r\nexport class MatchingService {\r\n  \r\n  /**\r\n   * Calculate compatibility between two users for roommate matching\r\n   */\r\n  static async calculateUserCompatibility(\r\n    userId: Types.ObjectId,\r\n    targetUserId: Types.ObjectId\r\n  ): Promise<CompatibilityFactors> {\r\n    try {\r\n      // Get user preferences and profiles\r\n      const [userPrefs, targetUserPrefs, userProfile, targetProfile] = await Promise.all([\r\n        MatchPreferences.findOne({ userId }),\r\n        MatchPreferences.findOne({ userId: targetUserId }),\r\n        Profile.findOne({ userId }),\r\n        Profile.findOne({ userId: targetUserId })\r\n      ]);\r\n\r\n      if (!userPrefs || !targetUserPrefs || !userProfile || !targetProfile) {\r\n        throw new AppError('User preferences or profiles not found', 404);\r\n      }\r\n\r\n      const factors: CompatibilityFactors = {\r\n        location: this.calculateUserLocationCompatibility(userProfile, targetProfile, userPrefs),\r\n        budget: this.calculateUserBudgetCompatibility(userPrefs, targetUserPrefs),\r\n        lifestyle: this.calculateUserLifestyleCompatibility(userPrefs, targetUserPrefs),\r\n        preferences: this.calculateUserPreferencesCompatibility(userPrefs, targetUserPrefs),\r\n        schedule: this.calculateUserScheduleCompatibility(userPrefs, targetUserPrefs),\r\n        cleanliness: this.calculateUserCleanlinessCompatibility(userPrefs, targetUserPrefs),\r\n        socialLevel: this.calculateUserSocialCompatibility(userPrefs, targetUserPrefs),\r\n        overall: 0\r\n      };\r\n\r\n      // Calculate weighted overall score\r\n      const weights = {\r\n        location: 0.20,\r\n        budget: 0.20,\r\n        lifestyle: 0.15,\r\n        preferences: 0.15,\r\n        schedule: 0.10,\r\n        cleanliness: 0.10,\r\n        socialLevel: 0.10\r\n      };\r\n\r\n      factors.overall = Math.round(\r\n        (factors.location * weights.location) +\r\n        (factors.budget * weights.budget) +\r\n        (factors.lifestyle * weights.lifestyle) +\r\n        (factors.preferences * weights.preferences) +\r\n        (factors.schedule * weights.schedule) +\r\n        (factors.cleanliness * weights.cleanliness) +\r\n        (factors.socialLevel * weights.socialLevel)\r\n      );\r\n\r\n      return factors;\r\n    } catch (error) {\r\n      logger.error('Error calculating user compatibility:', error);\r\n      throw new AppError('Failed to calculate compatibility', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate compatibility between user and property for housing matching\r\n   */\r\n  static async calculatePropertyCompatibility(\r\n    userId: Types.ObjectId,\r\n    propertyId: Types.ObjectId\r\n  ): Promise<CompatibilityFactors> {\r\n    try {\r\n      const [userPrefs, userProfile, property] = await Promise.all([\r\n        MatchPreferences.findOne({ userId }),\r\n        Profile.findOne({ userId }),\r\n        Property.findById(propertyId)\r\n      ]);\r\n\r\n      if (!userPrefs || !userProfile || !property) {\r\n        throw new AppError('User preferences, profile, or property not found', 404);\r\n      }\r\n\r\n      const factors: CompatibilityFactors = {\r\n        location: this.calculatePropertyLocationCompatibility(userProfile, property, userPrefs),\r\n        budget: this.calculatePropertyBudgetCompatibility(userPrefs, property),\r\n        lifestyle: this.calculatePropertyLifestyleCompatibility(userPrefs, property),\r\n        preferences: this.calculatePropertyPreferencesCompatibility(userPrefs, property),\r\n        schedule: 70, // Default for property matches\r\n        cleanliness: this.calculatePropertyCleanlinessCompatibility(userPrefs, property),\r\n        socialLevel: 70, // Default for property matches\r\n        overall: 0\r\n      };\r\n\r\n      // Calculate weighted overall score for property matching\r\n      const weights = {\r\n        location: 0.25,\r\n        budget: 0.30,\r\n        lifestyle: 0.15,\r\n        preferences: 0.20,\r\n        schedule: 0.05,\r\n        cleanliness: 0.05,\r\n        socialLevel: 0.00\r\n      };\r\n\r\n      factors.overall = Math.round(\r\n        (factors.location * weights.location) +\r\n        (factors.budget * weights.budget) +\r\n        (factors.lifestyle * weights.lifestyle) +\r\n        (factors.preferences * weights.preferences) +\r\n        (factors.schedule * weights.schedule) +\r\n        (factors.cleanliness * weights.cleanliness) +\r\n        (factors.socialLevel * weights.socialLevel)\r\n      );\r\n\r\n      return factors;\r\n    } catch (error) {\r\n      logger.error('Error calculating property compatibility:', error);\r\n      throw new AppError('Failed to calculate property compatibility', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find potential roommate matches for a user\r\n   */\r\n  static async findRoommateMatches(\r\n    userId: Types.ObjectId,\r\n    limit: number = 20\r\n  ): Promise<MatchCandidate[]> {\r\n    try {\r\n      const userPrefs = await MatchPreferences.findOne({ userId });\r\n      if (!userPrefs || !userPrefs.isActive) {\r\n        return [];\r\n      }\r\n\r\n      // Get potential candidates based on basic criteria\r\n      const candidates = await MatchingHelpers.getRoommateCandidates(userId, userPrefs);\r\n      const matches: MatchCandidate[] = [];\r\n\r\n      for (const candidate of candidates) {\r\n        try {\r\n          const compatibility = await this.calculateUserCompatibility(userId, candidate._id);\r\n          \r\n          if (compatibility.overall >= userPrefs.matchingSettings.compatibility_threshold) {\r\n            const distance = await MatchingHelpers.calculateDistance(userId, candidate._id);\r\n            const matchReasons = MatchingHelpers.generateMatchReasons(compatibility);\r\n            \r\n            matches.push({\r\n              id: candidate._id.toString(),\r\n              type: 'user',\r\n              compatibilityScore: compatibility.overall,\r\n              compatibilityFactors: compatibility,\r\n              distance,\r\n              matchReasons,\r\n              commonInterests: [], // TODO: Implement based on user interests\r\n              sharedPreferences: [] // TODO: Implement based on shared preferences\r\n            });\r\n          }\r\n        } catch (error) {\r\n          logger.warn(`Failed to calculate compatibility for candidate ${candidate._id}:`, error);\r\n          continue;\r\n        }\r\n      }\r\n\r\n      // Sort by compatibility score and return top matches\r\n      return matches\r\n        .sort((a, b) => b.compatibilityScore - a.compatibilityScore)\r\n        .slice(0, limit);\r\n\r\n    } catch (error) {\r\n      logger.error('Error finding roommate matches:', error);\r\n      throw new AppError('Failed to find roommate matches', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Find potential housing matches for a user\r\n   */\r\n  static async findHousingMatches(\r\n    userId: Types.ObjectId,\r\n    limit: number = 20\r\n  ): Promise<MatchCandidate[]> {\r\n    try {\r\n      const userPrefs = await MatchPreferences.findOne({ userId });\r\n      if (!userPrefs || !userPrefs.isActive) {\r\n        return [];\r\n      }\r\n\r\n      // Get potential property candidates\r\n      const properties = await MatchingHelpers.getPropertyCandidates(userId, userPrefs);\r\n      const matches: MatchCandidate[] = [];\r\n\r\n      for (const property of properties) {\r\n        try {\r\n          const compatibility = await this.calculatePropertyCompatibility(userId, property._id);\r\n          \r\n          if (compatibility.overall >= userPrefs.matchingSettings.compatibility_threshold) {\r\n            const distance = await MatchingHelpers.calculatePropertyDistance(userId, property._id);\r\n            const matchReasons = MatchingHelpers.generatePropertyMatchReasons(compatibility, property);\r\n            \r\n            matches.push({\r\n              id: property._id.toString(),\r\n              type: 'property',\r\n              compatibilityScore: compatibility.overall,\r\n              compatibilityFactors: compatibility,\r\n              distance,\r\n              matchReasons,\r\n              commonInterests: [],\r\n              sharedPreferences: []\r\n            });\r\n          }\r\n        } catch (error) {\r\n          logger.warn(`Failed to calculate compatibility for property ${property._id}:`, error);\r\n          continue;\r\n        }\r\n      }\r\n\r\n      // Sort by compatibility score and return top matches\r\n      return matches\r\n        .sort((a, b) => b.compatibilityScore - a.compatibilityScore)\r\n        .slice(0, limit);\r\n\r\n    } catch (error) {\r\n      logger.error('Error finding housing matches:', error);\r\n      throw new AppError('Failed to find housing matches', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a match record between user and target\r\n   */\r\n  static async createMatch(\r\n    userId: Types.ObjectId,\r\n    targetId: Types.ObjectId,\r\n    targetType: 'user' | 'property',\r\n    compatibilityFactors: CompatibilityFactors\r\n  ): Promise<IMatch> {\r\n    try {\r\n      // Check if match already exists\r\n      const existingMatch = await Match.findOne({ userId, targetId, targetType });\r\n      if (existingMatch) {\r\n        return existingMatch;\r\n      }\r\n\r\n      // Determine match type\r\n      const matchType = targetType === 'user' ? 'roommate' : 'housing';\r\n      \r\n      // Calculate expiration (7 days for roommate, 30 days for housing)\r\n      const expirationDays = targetType === 'user' ? 7 : 30;\r\n      const expiresAt = new Date();\r\n      expiresAt.setDate(expiresAt.getDate() + expirationDays);\r\n\r\n      // Calculate additional metrics\r\n      const locationProximity = targetType === 'user'\r\n        ? await MatchingHelpers.calculateDistance(userId, targetId)\r\n        : await MatchingHelpers.calculatePropertyDistance(userId, targetId);\r\n\r\n      const budgetCompatibility = compatibilityFactors.budget;\r\n      const stateMatch = await MatchingHelpers.checkStateMatch(userId, targetId, targetType);\r\n\r\n      const match = new Match({\r\n        userId,\r\n        targetId,\r\n        targetType,\r\n        matchType,\r\n        compatibilityScore: compatibilityFactors.overall,\r\n        compatibilityFactors,\r\n        expiresAt,\r\n        locationProximity,\r\n        budgetCompatibility,\r\n        stateMatch,\r\n        matchReason: MatchingHelpers.generateMatchReasons(compatibilityFactors),\r\n        lastInteractionAt: new Date()\r\n      });\r\n\r\n      await match.save();\r\n      \r\n      logger.info(`Created ${matchType} match between user ${userId} and ${targetType} ${targetId} with ${compatibilityFactors.overall}% compatibility`);\r\n      \r\n      return match;\r\n    } catch (error) {\r\n      logger.error('Error creating match:', error);\r\n      throw new AppError('Failed to create match', 500);\r\n    }\r\n  }\r\n\r\n  // Private helper methods for user compatibility calculations\r\n\r\n  private static calculateUserLocationCompatibility(\r\n    userProfile: any,\r\n    targetProfile: any,\r\n    userPrefs: IMatchPreferences\r\n  ): number {\r\n    let score = 0;\r\n\r\n    // Same state bonus\r\n    if (userProfile.location?.state === targetProfile.location?.state) {\r\n      score += 40;\r\n    }\r\n\r\n    // Same city bonus\r\n    if (userProfile.location?.city === targetProfile.location?.city) {\r\n      score += 30;\r\n    }\r\n\r\n    // Same area bonus\r\n    if (userProfile.location?.area === targetProfile.location?.area) {\r\n      score += 20;\r\n    }\r\n\r\n    // Preferred states/cities\r\n    if (userPrefs.preferredStates.includes(targetProfile.location?.state)) {\r\n      score += 10;\r\n    }\r\n\r\n    if (userPrefs.preferredCities.includes(targetProfile.location?.city)) {\r\n      score += 10;\r\n    }\r\n\r\n    return Math.min(score, 100);\r\n  }\r\n\r\n  private static calculateUserBudgetCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    const userMin = userPrefs.budgetRange.min;\r\n    const userMax = userPrefs.budgetRange.max;\r\n    const targetMin = targetPrefs.budgetRange.min;\r\n    const targetMax = targetPrefs.budgetRange.max;\r\n\r\n    // Calculate overlap\r\n    const overlapMin = Math.max(userMin, targetMin);\r\n    const overlapMax = Math.min(userMax, targetMax);\r\n\r\n    if (overlapMin > overlapMax) {\r\n      return 0; // No overlap\r\n    }\r\n\r\n    const overlapRange = overlapMax - overlapMin;\r\n    const userRange = userMax - userMin;\r\n    const targetRange = targetMax - targetMin;\r\n    const avgRange = (userRange + targetRange) / 2;\r\n\r\n    const overlapPercentage = (overlapRange / avgRange) * 100;\r\n    return Math.min(overlapPercentage, 100);\r\n  }\r\n\r\n  private static calculateUserLifestyleCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    const factors = ['smoking', 'drinking', 'pets', 'parties', 'guests', 'noise_level'];\r\n    let totalScore = 0;\r\n    let validFactors = 0;\r\n\r\n    factors.forEach(factor => {\r\n      const userPref = userPrefs.lifestyle[factor as keyof typeof userPrefs.lifestyle];\r\n      const targetPref = targetPrefs.lifestyle[factor as keyof typeof targetPrefs.lifestyle];\r\n\r\n      if (userPref !== 'no_preference' && targetPref !== 'no_preference') {\r\n        validFactors++;\r\n        if (userPref === targetPref) {\r\n          totalScore += 100;\r\n        } else if (MatchingHelpers.isCompatibleLifestyle(userPref, targetPref)) {\r\n          totalScore += 70;\r\n        } else {\r\n          totalScore += 30;\r\n        }\r\n      }\r\n    });\r\n\r\n    return validFactors > 0 ? Math.round(totalScore / validFactors) : 70;\r\n  }\r\n\r\n  private static calculateUserScheduleCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    const scheduleFactors = ['work_schedule', 'sleep_schedule', 'social_level'];\r\n    let totalScore = 0;\r\n    let validFactors = 0;\r\n\r\n    scheduleFactors.forEach(factor => {\r\n      const userPref = userPrefs.schedule[factor as keyof typeof userPrefs.schedule];\r\n      const targetPref = targetPrefs.schedule[factor as keyof typeof targetPrefs.schedule];\r\n\r\n      if (userPref !== 'no_preference' && targetPref !== 'no_preference') {\r\n        validFactors++;\r\n        if (userPref === targetPref) {\r\n          totalScore += 100;\r\n        } else if (MatchingHelpers.isCompatibleSchedule(userPref, targetPref)) {\r\n          totalScore += 70;\r\n        } else {\r\n          totalScore += 40;\r\n        }\r\n      }\r\n    });\r\n\r\n    return validFactors > 0 ? Math.round(totalScore / validFactors) : 70;\r\n  }\r\n\r\n  private static calculateUserCleanlinessCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    const userCleanliness = userPrefs.lifestyle.cleanliness;\r\n    const targetCleanliness = targetPrefs.lifestyle.cleanliness;\r\n\r\n    if (userCleanliness === 'no_preference' || targetCleanliness === 'no_preference') {\r\n      return 70;\r\n    }\r\n\r\n    const cleanlinessLevels = {\r\n      'very_clean': 4,\r\n      'clean': 3,\r\n      'average': 2,\r\n      'relaxed': 1\r\n    };\r\n\r\n    const userLevel = cleanlinessLevels[userCleanliness as keyof typeof cleanlinessLevels] || 2;\r\n    const targetLevel = cleanlinessLevels[targetCleanliness as keyof typeof cleanlinessLevels] || 2;\r\n    const difference = Math.abs(userLevel - targetLevel);\r\n\r\n    // Perfect match: 100%, 1 level diff: 80%, 2 levels: 60%, 3 levels: 40%\r\n    return Math.max(100 - (difference * 20), 40);\r\n  }\r\n\r\n  private static calculateUserSocialCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    const userSocial = userPrefs.schedule.social_level;\r\n    const targetSocial = targetPrefs.schedule.social_level;\r\n\r\n    if (userSocial === 'no_preference' || targetSocial === 'no_preference') {\r\n      return 70;\r\n    }\r\n\r\n    const socialLevels = {\r\n      'very_social': 4,\r\n      'social': 3,\r\n      'moderate': 2,\r\n      'private': 1\r\n    };\r\n\r\n    const userLevel = socialLevels[userSocial as keyof typeof socialLevels] || 2;\r\n    const targetLevel = socialLevels[targetSocial as keyof typeof socialLevels] || 2;\r\n    const difference = Math.abs(userLevel - targetLevel);\r\n\r\n    return Math.max(100 - (difference * 15), 50);\r\n  }\r\n\r\n  private static calculateUserPreferencesCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    targetPrefs: IMatchPreferences\r\n  ): number {\r\n    let score = 0;\r\n    let factors = 0;\r\n\r\n    // Gender preference compatibility\r\n    if (userPrefs.genderPreference !== 'any' || targetPrefs.genderPreference !== 'any') {\r\n      factors++;\r\n      // This would need user gender information from profile\r\n      score += 70; // Default score for now\r\n    }\r\n\r\n    // Age range compatibility\r\n    const userAgeRange = userPrefs.ageRange;\r\n    const targetAgeRange = targetPrefs.ageRange;\r\n\r\n    const ageOverlapMin = Math.max(userAgeRange.min, targetAgeRange.min);\r\n    const ageOverlapMax = Math.min(userAgeRange.max, targetAgeRange.max);\r\n\r\n    if (ageOverlapMin <= ageOverlapMax) {\r\n      factors++;\r\n      const overlapSize = ageOverlapMax - ageOverlapMin;\r\n      const avgRangeSize = ((userAgeRange.max - userAgeRange.min) + (targetAgeRange.max - targetAgeRange.min)) / 2;\r\n      score += Math.min((overlapSize / avgRangeSize) * 100, 100);\r\n    }\r\n\r\n    return factors > 0 ? Math.round(score / factors) : 70;\r\n  }\r\n\r\n  // Property compatibility calculation methods\r\n\r\n  private static calculatePropertyLocationCompatibility(\r\n    userProfile: any,\r\n    property: any,\r\n    userPrefs: IMatchPreferences\r\n  ): number {\r\n    let score = 0;\r\n\r\n    // Same state bonus\r\n    if (userProfile.location?.state === property.location?.state) {\r\n      score += 40;\r\n    }\r\n\r\n    // Same city bonus\r\n    if (userProfile.location?.city === property.location?.city) {\r\n      score += 30;\r\n    }\r\n\r\n    // Same area bonus\r\n    if (userProfile.location?.area === property.location?.area) {\r\n      score += 20;\r\n    }\r\n\r\n    // Preferred states/cities\r\n    if (userPrefs.preferredStates.includes(property.location?.state)) {\r\n      score += 10;\r\n    }\r\n\r\n    if (userPrefs.preferredCities.includes(property.location?.city)) {\r\n      score += 10;\r\n    }\r\n\r\n    return Math.min(score, 100);\r\n  }\r\n\r\n  private static calculatePropertyBudgetCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    property: any\r\n  ): number {\r\n    const userMin = userPrefs.budgetRange.min;\r\n    const userMax = userPrefs.budgetRange.max;\r\n    const propertyPrice = property.pricing.rentPerMonth;\r\n\r\n    if (propertyPrice >= userMin && propertyPrice <= userMax) {\r\n      // Perfect match if within range\r\n      const rangeSize = userMax - userMin;\r\n\r\n      // Score higher if closer to middle of range\r\n      const middleDistance = Math.abs(propertyPrice - (userMin + userMax) / 2);\r\n      const maxMiddleDistance = rangeSize / 2;\r\n\r\n      return Math.max(100 - (middleDistance / maxMiddleDistance) * 20, 80);\r\n    }\r\n\r\n    // Calculate how far outside the range\r\n    const flexibility = userPrefs.budgetFlexibility / 100;\r\n    const flexibleMin = userMin * (1 - flexibility);\r\n    const flexibleMax = userMax * (1 + flexibility);\r\n\r\n    if (propertyPrice >= flexibleMin && propertyPrice <= flexibleMax) {\r\n      // Within flexible range\r\n      const overagePercentage = propertyPrice > userMax\r\n        ? (propertyPrice - userMax) / userMax\r\n        : (userMin - propertyPrice) / userMin;\r\n\r\n      return Math.max(80 - (overagePercentage * 100), 40);\r\n    }\r\n\r\n    return 0; // Outside flexible range\r\n  }\r\n\r\n  private static calculatePropertyLifestyleCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    property: any\r\n  ): number {\r\n    let score = 70; // Base score\r\n\r\n    // Check rules compatibility\r\n    if (userPrefs.lifestyle.smoking === 'yes' && !property.rules.smokingAllowed) {\r\n      score -= 30;\r\n    }\r\n    if (userPrefs.lifestyle.pets === 'love' && !property.rules.petsAllowed) {\r\n      score -= 25;\r\n    }\r\n    if (userPrefs.lifestyle.parties === 'love' && !property.rules.partiesAllowed) {\r\n      score -= 20;\r\n    }\r\n    if (userPrefs.lifestyle.guests === 'frequent' && !property.rules.guestsAllowed) {\r\n      score -= 20;\r\n    }\r\n\r\n    return Math.max(score, 0);\r\n  }\r\n\r\n  private static calculatePropertyPreferencesCompatibility(\r\n    userPrefs: IMatchPreferences,\r\n    property: any\r\n  ): number {\r\n    let score = 0;\r\n    let totalChecks = 0;\r\n\r\n    // Property type preference\r\n    if (userPrefs.propertyPreferences.propertyTypes.length > 0) {\r\n      totalChecks++;\r\n      if (userPrefs.propertyPreferences.propertyTypes.includes(property.propertyType)) {\r\n        score += 100;\r\n      }\r\n    }\r\n\r\n    // Bedroom/bathroom requirements\r\n    totalChecks++;\r\n    if (property.bedrooms >= userPrefs.propertyPreferences.minimumBedrooms) {\r\n      score += 100;\r\n    } else {\r\n      score += 50; // Partial credit\r\n    }\r\n\r\n    totalChecks++;\r\n    if (property.bathrooms >= userPrefs.propertyPreferences.minimumBathrooms) {\r\n      score += 100;\r\n    } else {\r\n      score += 50;\r\n    }\r\n\r\n    // Amenities matching\r\n    const requiredAmenities = userPrefs.propertyPreferences.amenities;\r\n    if (requiredAmenities.length > 0) {\r\n      totalChecks++;\r\n      let amenityScore = 0;\r\n      requiredAmenities.forEach(amenity => {\r\n        if (property.amenities[amenity]) {\r\n          amenityScore += 100 / requiredAmenities.length;\r\n        }\r\n      });\r\n      score += amenityScore;\r\n    }\r\n\r\n    // Furnished preference\r\n    if (userPrefs.propertyPreferences.furnished !== 'no_preference') {\r\n      totalChecks++;\r\n      const isFurnished = property.amenities.furnished;\r\n      if (\r\n        (userPrefs.propertyPreferences.furnished === 'yes' && isFurnished) ||\r\n        (userPrefs.propertyPreferences.furnished === 'no' && !isFurnished)\r\n      ) {\r\n        score += 100;\r\n      } else if (userPrefs.propertyPreferences.furnished === 'partial') {\r\n        score += 70;\r\n      }\r\n    }\r\n\r\n    return totalChecks > 0 ? Math.round(score / totalChecks) : 70;\r\n  }\r\n\r\n  private static calculatePropertyCleanlinessCompatibility(\r\n    _userPrefs: IMatchPreferences,\r\n    property: any\r\n  ): number {\r\n    // For properties, we can't directly assess cleanliness\r\n    // But we can infer from amenities and property quality\r\n    let score = 70; // Base score\r\n\r\n    if (property.amenities.cleaningService) {\r\n      score += 20;\r\n    }\r\n    if (property.isVerified) {\r\n      score += 10;\r\n    }\r\n    if (property.amenities.furnished) {\r\n      score += 5; // Well-maintained properties are often furnished\r\n    }\r\n\r\n    return Math.min(score, 100);\r\n  }\r\n\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cadc9878a2309fbc677fc47452a17a91902e3196"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2avwqwwcso = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2avwqwwcso();
cov_2avwqwwcso().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2avwqwwcso().s[1]++;
exports.MatchingService = void 0;
const Match_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[2]++, require("../models/Match"));
const Property_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[3]++, require("../models/Property"));
const Profile_model_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[4]++, require("../models/Profile.model"));
const logger_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[5]++, require("../utils/logger"));
const appError_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[6]++, require("../utils/appError"));
const matchingHelpers_1 =
/* istanbul ignore next */
(cov_2avwqwwcso().s[7]++, require("./matchingHelpers"));
class MatchingService {
  /**
   * Calculate compatibility between two users for roommate matching
   */
  static async calculateUserCompatibility(userId, targetUserId) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[0]++;
    cov_2avwqwwcso().s[8]++;
    try {
      // Get user preferences and profiles
      const [userPrefs, targetUserPrefs, userProfile, targetProfile] =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[9]++, await Promise.all([Match_1.MatchPreferences.findOne({
        userId
      }), Match_1.MatchPreferences.findOne({
        userId: targetUserId
      }), Profile_model_1.Profile.findOne({
        userId
      }), Profile_model_1.Profile.findOne({
        userId: targetUserId
      })]));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[10]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[1][0]++, !userPrefs) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[1][1]++, !targetUserPrefs) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[1][2]++, !userProfile) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[1][3]++, !targetProfile)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[0][0]++;
        cov_2avwqwwcso().s[11]++;
        throw new appError_1.AppError('User preferences or profiles not found', 404);
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[0][1]++;
      }
      const factors =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[12]++, {
        location: this.calculateUserLocationCompatibility(userProfile, targetProfile, userPrefs),
        budget: this.calculateUserBudgetCompatibility(userPrefs, targetUserPrefs),
        lifestyle: this.calculateUserLifestyleCompatibility(userPrefs, targetUserPrefs),
        preferences: this.calculateUserPreferencesCompatibility(userPrefs, targetUserPrefs),
        schedule: this.calculateUserScheduleCompatibility(userPrefs, targetUserPrefs),
        cleanliness: this.calculateUserCleanlinessCompatibility(userPrefs, targetUserPrefs),
        socialLevel: this.calculateUserSocialCompatibility(userPrefs, targetUserPrefs),
        overall: 0
      });
      // Calculate weighted overall score
      const weights =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[13]++, {
        location: 0.20,
        budget: 0.20,
        lifestyle: 0.15,
        preferences: 0.15,
        schedule: 0.10,
        cleanliness: 0.10,
        socialLevel: 0.10
      });
      /* istanbul ignore next */
      cov_2avwqwwcso().s[14]++;
      factors.overall = Math.round(factors.location * weights.location + factors.budget * weights.budget + factors.lifestyle * weights.lifestyle + factors.preferences * weights.preferences + factors.schedule * weights.schedule + factors.cleanliness * weights.cleanliness + factors.socialLevel * weights.socialLevel);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[15]++;
      return factors;
    } catch (error) {
      /* istanbul ignore next */
      cov_2avwqwwcso().s[16]++;
      logger_1.logger.error('Error calculating user compatibility:', error);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[17]++;
      throw new appError_1.AppError('Failed to calculate compatibility', 500);
    }
  }
  /**
   * Calculate compatibility between user and property for housing matching
   */
  static async calculatePropertyCompatibility(userId, propertyId) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[1]++;
    cov_2avwqwwcso().s[18]++;
    try {
      const [userPrefs, userProfile, property] =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[19]++, await Promise.all([Match_1.MatchPreferences.findOne({
        userId
      }), Profile_model_1.Profile.findOne({
        userId
      }), Property_1.Property.findById(propertyId)]));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[20]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[3][0]++, !userPrefs) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[3][1]++, !userProfile) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[3][2]++, !property)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[2][0]++;
        cov_2avwqwwcso().s[21]++;
        throw new appError_1.AppError('User preferences, profile, or property not found', 404);
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[2][1]++;
      }
      const factors =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[22]++, {
        location: this.calculatePropertyLocationCompatibility(userProfile, property, userPrefs),
        budget: this.calculatePropertyBudgetCompatibility(userPrefs, property),
        lifestyle: this.calculatePropertyLifestyleCompatibility(userPrefs, property),
        preferences: this.calculatePropertyPreferencesCompatibility(userPrefs, property),
        schedule: 70,
        // Default for property matches
        cleanliness: this.calculatePropertyCleanlinessCompatibility(userPrefs, property),
        socialLevel: 70,
        // Default for property matches
        overall: 0
      });
      // Calculate weighted overall score for property matching
      const weights =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[23]++, {
        location: 0.25,
        budget: 0.30,
        lifestyle: 0.15,
        preferences: 0.20,
        schedule: 0.05,
        cleanliness: 0.05,
        socialLevel: 0.00
      });
      /* istanbul ignore next */
      cov_2avwqwwcso().s[24]++;
      factors.overall = Math.round(factors.location * weights.location + factors.budget * weights.budget + factors.lifestyle * weights.lifestyle + factors.preferences * weights.preferences + factors.schedule * weights.schedule + factors.cleanliness * weights.cleanliness + factors.socialLevel * weights.socialLevel);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[25]++;
      return factors;
    } catch (error) {
      /* istanbul ignore next */
      cov_2avwqwwcso().s[26]++;
      logger_1.logger.error('Error calculating property compatibility:', error);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[27]++;
      throw new appError_1.AppError('Failed to calculate property compatibility', 500);
    }
  }
  /**
   * Find potential roommate matches for a user
   */
  static async findRoommateMatches(userId, limit =
  /* istanbul ignore next */
  (cov_2avwqwwcso().b[4][0]++, 20)) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[2]++;
    cov_2avwqwwcso().s[28]++;
    try {
      const userPrefs =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[29]++, await Match_1.MatchPreferences.findOne({
        userId
      }));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[30]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[6][0]++, !userPrefs) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[6][1]++, !userPrefs.isActive)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[5][0]++;
        cov_2avwqwwcso().s[31]++;
        return [];
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[5][1]++;
      }
      // Get potential candidates based on basic criteria
      const candidates =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[32]++, await matchingHelpers_1.MatchingHelpers.getRoommateCandidates(userId, userPrefs));
      const matches =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[33]++, []);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[34]++;
      for (const candidate of candidates) {
        /* istanbul ignore next */
        cov_2avwqwwcso().s[35]++;
        try {
          const compatibility =
          /* istanbul ignore next */
          (cov_2avwqwwcso().s[36]++, await this.calculateUserCompatibility(userId, candidate._id));
          /* istanbul ignore next */
          cov_2avwqwwcso().s[37]++;
          if (compatibility.overall >= userPrefs.matchingSettings.compatibility_threshold) {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[7][0]++;
            const distance =
            /* istanbul ignore next */
            (cov_2avwqwwcso().s[38]++, await matchingHelpers_1.MatchingHelpers.calculateDistance(userId, candidate._id));
            const matchReasons =
            /* istanbul ignore next */
            (cov_2avwqwwcso().s[39]++, matchingHelpers_1.MatchingHelpers.generateMatchReasons(compatibility));
            /* istanbul ignore next */
            cov_2avwqwwcso().s[40]++;
            matches.push({
              id: candidate._id.toString(),
              type: 'user',
              compatibilityScore: compatibility.overall,
              compatibilityFactors: compatibility,
              distance,
              matchReasons,
              commonInterests: [],
              // TODO: Implement based on user interests
              sharedPreferences: [] // TODO: Implement based on shared preferences
            });
          } else
          /* istanbul ignore next */
          {
            cov_2avwqwwcso().b[7][1]++;
          }
        } catch (error) {
          /* istanbul ignore next */
          cov_2avwqwwcso().s[41]++;
          logger_1.logger.warn(`Failed to calculate compatibility for candidate ${candidate._id}:`, error);
          /* istanbul ignore next */
          cov_2avwqwwcso().s[42]++;
          continue;
        }
      }
      // Sort by compatibility score and return top matches
      /* istanbul ignore next */
      cov_2avwqwwcso().s[43]++;
      return matches.sort((a, b) => {
        /* istanbul ignore next */
        cov_2avwqwwcso().f[3]++;
        cov_2avwqwwcso().s[44]++;
        return b.compatibilityScore - a.compatibilityScore;
      }).slice(0, limit);
    } catch (error) {
      /* istanbul ignore next */
      cov_2avwqwwcso().s[45]++;
      logger_1.logger.error('Error finding roommate matches:', error);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[46]++;
      throw new appError_1.AppError('Failed to find roommate matches', 500);
    }
  }
  /**
   * Find potential housing matches for a user
   */
  static async findHousingMatches(userId, limit =
  /* istanbul ignore next */
  (cov_2avwqwwcso().b[8][0]++, 20)) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[4]++;
    cov_2avwqwwcso().s[47]++;
    try {
      const userPrefs =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[48]++, await Match_1.MatchPreferences.findOne({
        userId
      }));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[49]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[10][0]++, !userPrefs) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[10][1]++, !userPrefs.isActive)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[9][0]++;
        cov_2avwqwwcso().s[50]++;
        return [];
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[9][1]++;
      }
      // Get potential property candidates
      const properties =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[51]++, await matchingHelpers_1.MatchingHelpers.getPropertyCandidates(userId, userPrefs));
      const matches =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[52]++, []);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[53]++;
      for (const property of properties) {
        /* istanbul ignore next */
        cov_2avwqwwcso().s[54]++;
        try {
          const compatibility =
          /* istanbul ignore next */
          (cov_2avwqwwcso().s[55]++, await this.calculatePropertyCompatibility(userId, property._id));
          /* istanbul ignore next */
          cov_2avwqwwcso().s[56]++;
          if (compatibility.overall >= userPrefs.matchingSettings.compatibility_threshold) {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[11][0]++;
            const distance =
            /* istanbul ignore next */
            (cov_2avwqwwcso().s[57]++, await matchingHelpers_1.MatchingHelpers.calculatePropertyDistance(userId, property._id));
            const matchReasons =
            /* istanbul ignore next */
            (cov_2avwqwwcso().s[58]++, matchingHelpers_1.MatchingHelpers.generatePropertyMatchReasons(compatibility, property));
            /* istanbul ignore next */
            cov_2avwqwwcso().s[59]++;
            matches.push({
              id: property._id.toString(),
              type: 'property',
              compatibilityScore: compatibility.overall,
              compatibilityFactors: compatibility,
              distance,
              matchReasons,
              commonInterests: [],
              sharedPreferences: []
            });
          } else
          /* istanbul ignore next */
          {
            cov_2avwqwwcso().b[11][1]++;
          }
        } catch (error) {
          /* istanbul ignore next */
          cov_2avwqwwcso().s[60]++;
          logger_1.logger.warn(`Failed to calculate compatibility for property ${property._id}:`, error);
          /* istanbul ignore next */
          cov_2avwqwwcso().s[61]++;
          continue;
        }
      }
      // Sort by compatibility score and return top matches
      /* istanbul ignore next */
      cov_2avwqwwcso().s[62]++;
      return matches.sort((a, b) => {
        /* istanbul ignore next */
        cov_2avwqwwcso().f[5]++;
        cov_2avwqwwcso().s[63]++;
        return b.compatibilityScore - a.compatibilityScore;
      }).slice(0, limit);
    } catch (error) {
      /* istanbul ignore next */
      cov_2avwqwwcso().s[64]++;
      logger_1.logger.error('Error finding housing matches:', error);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[65]++;
      throw new appError_1.AppError('Failed to find housing matches', 500);
    }
  }
  /**
   * Create a match record between user and target
   */
  static async createMatch(userId, targetId, targetType, compatibilityFactors) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[6]++;
    cov_2avwqwwcso().s[66]++;
    try {
      // Check if match already exists
      const existingMatch =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[67]++, await Match_1.Match.findOne({
        userId,
        targetId,
        targetType
      }));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[68]++;
      if (existingMatch) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[12][0]++;
        cov_2avwqwwcso().s[69]++;
        return existingMatch;
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[12][1]++;
      }
      // Determine match type
      const matchType =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[70]++, targetType === 'user' ?
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[13][0]++, 'roommate') :
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[13][1]++, 'housing'));
      // Calculate expiration (7 days for roommate, 30 days for housing)
      const expirationDays =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[71]++, targetType === 'user' ?
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[14][0]++, 7) :
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[14][1]++, 30));
      const expiresAt =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[72]++, new Date());
      /* istanbul ignore next */
      cov_2avwqwwcso().s[73]++;
      expiresAt.setDate(expiresAt.getDate() + expirationDays);
      // Calculate additional metrics
      const locationProximity =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[74]++, targetType === 'user' ?
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[15][0]++, await matchingHelpers_1.MatchingHelpers.calculateDistance(userId, targetId)) :
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[15][1]++, await matchingHelpers_1.MatchingHelpers.calculatePropertyDistance(userId, targetId)));
      const budgetCompatibility =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[75]++, compatibilityFactors.budget);
      const stateMatch =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[76]++, await matchingHelpers_1.MatchingHelpers.checkStateMatch(userId, targetId, targetType));
      const match =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[77]++, new Match_1.Match({
        userId,
        targetId,
        targetType,
        matchType,
        compatibilityScore: compatibilityFactors.overall,
        compatibilityFactors,
        expiresAt,
        locationProximity,
        budgetCompatibility,
        stateMatch,
        matchReason: matchingHelpers_1.MatchingHelpers.generateMatchReasons(compatibilityFactors),
        lastInteractionAt: new Date()
      }));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[78]++;
      await match.save();
      /* istanbul ignore next */
      cov_2avwqwwcso().s[79]++;
      logger_1.logger.info(`Created ${matchType} match between user ${userId} and ${targetType} ${targetId} with ${compatibilityFactors.overall}% compatibility`);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[80]++;
      return match;
    } catch (error) {
      /* istanbul ignore next */
      cov_2avwqwwcso().s[81]++;
      logger_1.logger.error('Error creating match:', error);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[82]++;
      throw new appError_1.AppError('Failed to create match', 500);
    }
  }
  // Private helper methods for user compatibility calculations
  static calculateUserLocationCompatibility(userProfile, targetProfile, userPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[7]++;
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[83]++, 0);
    // Same state bonus
    /* istanbul ignore next */
    cov_2avwqwwcso().s[84]++;
    if (userProfile.location?.state === targetProfile.location?.state) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[16][0]++;
      cov_2avwqwwcso().s[85]++;
      score += 40;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[16][1]++;
    }
    // Same city bonus
    cov_2avwqwwcso().s[86]++;
    if (userProfile.location?.city === targetProfile.location?.city) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[17][0]++;
      cov_2avwqwwcso().s[87]++;
      score += 30;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[17][1]++;
    }
    // Same area bonus
    cov_2avwqwwcso().s[88]++;
    if (userProfile.location?.area === targetProfile.location?.area) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[18][0]++;
      cov_2avwqwwcso().s[89]++;
      score += 20;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[18][1]++;
    }
    // Preferred states/cities
    cov_2avwqwwcso().s[90]++;
    if (userPrefs.preferredStates.includes(targetProfile.location?.state)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[19][0]++;
      cov_2avwqwwcso().s[91]++;
      score += 10;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[19][1]++;
    }
    cov_2avwqwwcso().s[92]++;
    if (userPrefs.preferredCities.includes(targetProfile.location?.city)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[20][0]++;
      cov_2avwqwwcso().s[93]++;
      score += 10;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[20][1]++;
    }
    cov_2avwqwwcso().s[94]++;
    return Math.min(score, 100);
  }
  static calculateUserBudgetCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[8]++;
    const userMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[95]++, userPrefs.budgetRange.min);
    const userMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[96]++, userPrefs.budgetRange.max);
    const targetMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[97]++, targetPrefs.budgetRange.min);
    const targetMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[98]++, targetPrefs.budgetRange.max);
    // Calculate overlap
    const overlapMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[99]++, Math.max(userMin, targetMin));
    const overlapMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[100]++, Math.min(userMax, targetMax));
    /* istanbul ignore next */
    cov_2avwqwwcso().s[101]++;
    if (overlapMin > overlapMax) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[21][0]++;
      cov_2avwqwwcso().s[102]++;
      return 0; // No overlap
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[21][1]++;
    }
    const overlapRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[103]++, overlapMax - overlapMin);
    const userRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[104]++, userMax - userMin);
    const targetRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[105]++, targetMax - targetMin);
    const avgRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[106]++, (userRange + targetRange) / 2);
    const overlapPercentage =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[107]++, overlapRange / avgRange * 100);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[108]++;
    return Math.min(overlapPercentage, 100);
  }
  static calculateUserLifestyleCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[9]++;
    const factors =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[109]++, ['smoking', 'drinking', 'pets', 'parties', 'guests', 'noise_level']);
    let totalScore =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[110]++, 0);
    let validFactors =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[111]++, 0);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[112]++;
    factors.forEach(factor => {
      /* istanbul ignore next */
      cov_2avwqwwcso().f[10]++;
      const userPref =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[113]++, userPrefs.lifestyle[factor]);
      const targetPref =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[114]++, targetPrefs.lifestyle[factor]);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[115]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[23][0]++, userPref !== 'no_preference') &&
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[23][1]++, targetPref !== 'no_preference')) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[22][0]++;
        cov_2avwqwwcso().s[116]++;
        validFactors++;
        /* istanbul ignore next */
        cov_2avwqwwcso().s[117]++;
        if (userPref === targetPref) {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[24][0]++;
          cov_2avwqwwcso().s[118]++;
          totalScore += 100;
        } else {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[24][1]++;
          cov_2avwqwwcso().s[119]++;
          if (matchingHelpers_1.MatchingHelpers.isCompatibleLifestyle(userPref, targetPref)) {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[25][0]++;
            cov_2avwqwwcso().s[120]++;
            totalScore += 70;
          } else {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[25][1]++;
            cov_2avwqwwcso().s[121]++;
            totalScore += 30;
          }
        }
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[22][1]++;
      }
    });
    /* istanbul ignore next */
    cov_2avwqwwcso().s[122]++;
    return validFactors > 0 ?
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[26][0]++, Math.round(totalScore / validFactors)) :
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[26][1]++, 70);
  }
  static calculateUserScheduleCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[11]++;
    const scheduleFactors =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[123]++, ['work_schedule', 'sleep_schedule', 'social_level']);
    let totalScore =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[124]++, 0);
    let validFactors =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[125]++, 0);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[126]++;
    scheduleFactors.forEach(factor => {
      /* istanbul ignore next */
      cov_2avwqwwcso().f[12]++;
      const userPref =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[127]++, userPrefs.schedule[factor]);
      const targetPref =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[128]++, targetPrefs.schedule[factor]);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[129]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[28][0]++, userPref !== 'no_preference') &&
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[28][1]++, targetPref !== 'no_preference')) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[27][0]++;
        cov_2avwqwwcso().s[130]++;
        validFactors++;
        /* istanbul ignore next */
        cov_2avwqwwcso().s[131]++;
        if (userPref === targetPref) {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[29][0]++;
          cov_2avwqwwcso().s[132]++;
          totalScore += 100;
        } else {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[29][1]++;
          cov_2avwqwwcso().s[133]++;
          if (matchingHelpers_1.MatchingHelpers.isCompatibleSchedule(userPref, targetPref)) {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[30][0]++;
            cov_2avwqwwcso().s[134]++;
            totalScore += 70;
          } else {
            /* istanbul ignore next */
            cov_2avwqwwcso().b[30][1]++;
            cov_2avwqwwcso().s[135]++;
            totalScore += 40;
          }
        }
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[27][1]++;
      }
    });
    /* istanbul ignore next */
    cov_2avwqwwcso().s[136]++;
    return validFactors > 0 ?
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[31][0]++, Math.round(totalScore / validFactors)) :
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[31][1]++, 70);
  }
  static calculateUserCleanlinessCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[13]++;
    const userCleanliness =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[137]++, userPrefs.lifestyle.cleanliness);
    const targetCleanliness =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[138]++, targetPrefs.lifestyle.cleanliness);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[139]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[33][0]++, userCleanliness === 'no_preference') ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[33][1]++, targetCleanliness === 'no_preference')) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[32][0]++;
      cov_2avwqwwcso().s[140]++;
      return 70;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[32][1]++;
    }
    const cleanlinessLevels =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[141]++, {
      'very_clean': 4,
      'clean': 3,
      'average': 2,
      'relaxed': 1
    });
    const userLevel =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[142]++,
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[34][0]++, cleanlinessLevels[userCleanliness]) ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[34][1]++, 2));
    const targetLevel =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[143]++,
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[35][0]++, cleanlinessLevels[targetCleanliness]) ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[35][1]++, 2));
    const difference =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[144]++, Math.abs(userLevel - targetLevel));
    // Perfect match: 100%, 1 level diff: 80%, 2 levels: 60%, 3 levels: 40%
    /* istanbul ignore next */
    cov_2avwqwwcso().s[145]++;
    return Math.max(100 - difference * 20, 40);
  }
  static calculateUserSocialCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[14]++;
    const userSocial =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[146]++, userPrefs.schedule.social_level);
    const targetSocial =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[147]++, targetPrefs.schedule.social_level);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[148]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[37][0]++, userSocial === 'no_preference') ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[37][1]++, targetSocial === 'no_preference')) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[36][0]++;
      cov_2avwqwwcso().s[149]++;
      return 70;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[36][1]++;
    }
    const socialLevels =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[150]++, {
      'very_social': 4,
      'social': 3,
      'moderate': 2,
      'private': 1
    });
    const userLevel =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[151]++,
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[38][0]++, socialLevels[userSocial]) ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[38][1]++, 2));
    const targetLevel =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[152]++,
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[39][0]++, socialLevels[targetSocial]) ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[39][1]++, 2));
    const difference =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[153]++, Math.abs(userLevel - targetLevel));
    /* istanbul ignore next */
    cov_2avwqwwcso().s[154]++;
    return Math.max(100 - difference * 15, 50);
  }
  static calculateUserPreferencesCompatibility(userPrefs, targetPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[15]++;
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[155]++, 0);
    let factors =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[156]++, 0);
    // Gender preference compatibility
    /* istanbul ignore next */
    cov_2avwqwwcso().s[157]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[41][0]++, userPrefs.genderPreference !== 'any') ||
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[41][1]++, targetPrefs.genderPreference !== 'any')) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[40][0]++;
      cov_2avwqwwcso().s[158]++;
      factors++;
      // This would need user gender information from profile
      /* istanbul ignore next */
      cov_2avwqwwcso().s[159]++;
      score += 70; // Default score for now
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[40][1]++;
    }
    // Age range compatibility
    const userAgeRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[160]++, userPrefs.ageRange);
    const targetAgeRange =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[161]++, targetPrefs.ageRange);
    const ageOverlapMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[162]++, Math.max(userAgeRange.min, targetAgeRange.min));
    const ageOverlapMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[163]++, Math.min(userAgeRange.max, targetAgeRange.max));
    /* istanbul ignore next */
    cov_2avwqwwcso().s[164]++;
    if (ageOverlapMin <= ageOverlapMax) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[42][0]++;
      cov_2avwqwwcso().s[165]++;
      factors++;
      const overlapSize =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[166]++, ageOverlapMax - ageOverlapMin);
      const avgRangeSize =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[167]++, (userAgeRange.max - userAgeRange.min + (targetAgeRange.max - targetAgeRange.min)) / 2);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[168]++;
      score += Math.min(overlapSize / avgRangeSize * 100, 100);
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[42][1]++;
    }
    cov_2avwqwwcso().s[169]++;
    return factors > 0 ?
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[43][0]++, Math.round(score / factors)) :
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[43][1]++, 70);
  }
  // Property compatibility calculation methods
  static calculatePropertyLocationCompatibility(userProfile, property, userPrefs) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[16]++;
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[170]++, 0);
    // Same state bonus
    /* istanbul ignore next */
    cov_2avwqwwcso().s[171]++;
    if (userProfile.location?.state === property.location?.state) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[44][0]++;
      cov_2avwqwwcso().s[172]++;
      score += 40;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[44][1]++;
    }
    // Same city bonus
    cov_2avwqwwcso().s[173]++;
    if (userProfile.location?.city === property.location?.city) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[45][0]++;
      cov_2avwqwwcso().s[174]++;
      score += 30;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[45][1]++;
    }
    // Same area bonus
    cov_2avwqwwcso().s[175]++;
    if (userProfile.location?.area === property.location?.area) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[46][0]++;
      cov_2avwqwwcso().s[176]++;
      score += 20;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[46][1]++;
    }
    // Preferred states/cities
    cov_2avwqwwcso().s[177]++;
    if (userPrefs.preferredStates.includes(property.location?.state)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[47][0]++;
      cov_2avwqwwcso().s[178]++;
      score += 10;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[47][1]++;
    }
    cov_2avwqwwcso().s[179]++;
    if (userPrefs.preferredCities.includes(property.location?.city)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[48][0]++;
      cov_2avwqwwcso().s[180]++;
      score += 10;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[48][1]++;
    }
    cov_2avwqwwcso().s[181]++;
    return Math.min(score, 100);
  }
  static calculatePropertyBudgetCompatibility(userPrefs, property) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[17]++;
    const userMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[182]++, userPrefs.budgetRange.min);
    const userMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[183]++, userPrefs.budgetRange.max);
    const propertyPrice =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[184]++, property.pricing.rentPerMonth);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[185]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[50][0]++, propertyPrice >= userMin) &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[50][1]++, propertyPrice <= userMax)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[49][0]++;
      // Perfect match if within range
      const rangeSize =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[186]++, userMax - userMin);
      // Score higher if closer to middle of range
      const middleDistance =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[187]++, Math.abs(propertyPrice - (userMin + userMax) / 2));
      const maxMiddleDistance =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[188]++, rangeSize / 2);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[189]++;
      return Math.max(100 - middleDistance / maxMiddleDistance * 20, 80);
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[49][1]++;
    }
    // Calculate how far outside the range
    const flexibility =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[190]++, userPrefs.budgetFlexibility / 100);
    const flexibleMin =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[191]++, userMin * (1 - flexibility));
    const flexibleMax =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[192]++, userMax * (1 + flexibility));
    /* istanbul ignore next */
    cov_2avwqwwcso().s[193]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[52][0]++, propertyPrice >= flexibleMin) &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[52][1]++, propertyPrice <= flexibleMax)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[51][0]++;
      // Within flexible range
      const overagePercentage =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[194]++, propertyPrice > userMax ?
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[53][0]++, (propertyPrice - userMax) / userMax) :
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[53][1]++, (userMin - propertyPrice) / userMin));
      /* istanbul ignore next */
      cov_2avwqwwcso().s[195]++;
      return Math.max(80 - overagePercentage * 100, 40);
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[51][1]++;
    }
    cov_2avwqwwcso().s[196]++;
    return 0; // Outside flexible range
  }
  static calculatePropertyLifestyleCompatibility(userPrefs, property) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[18]++;
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[197]++, 70); // Base score
    // Check rules compatibility
    /* istanbul ignore next */
    cov_2avwqwwcso().s[198]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[55][0]++, userPrefs.lifestyle.smoking === 'yes') &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[55][1]++, !property.rules.smokingAllowed)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[54][0]++;
      cov_2avwqwwcso().s[199]++;
      score -= 30;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[54][1]++;
    }
    cov_2avwqwwcso().s[200]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[57][0]++, userPrefs.lifestyle.pets === 'love') &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[57][1]++, !property.rules.petsAllowed)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[56][0]++;
      cov_2avwqwwcso().s[201]++;
      score -= 25;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[56][1]++;
    }
    cov_2avwqwwcso().s[202]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[59][0]++, userPrefs.lifestyle.parties === 'love') &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[59][1]++, !property.rules.partiesAllowed)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[58][0]++;
      cov_2avwqwwcso().s[203]++;
      score -= 20;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[58][1]++;
    }
    cov_2avwqwwcso().s[204]++;
    if (
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[61][0]++, userPrefs.lifestyle.guests === 'frequent') &&
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[61][1]++, !property.rules.guestsAllowed)) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[60][0]++;
      cov_2avwqwwcso().s[205]++;
      score -= 20;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[60][1]++;
    }
    cov_2avwqwwcso().s[206]++;
    return Math.max(score, 0);
  }
  static calculatePropertyPreferencesCompatibility(userPrefs, property) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[19]++;
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[207]++, 0);
    let totalChecks =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[208]++, 0);
    // Property type preference
    /* istanbul ignore next */
    cov_2avwqwwcso().s[209]++;
    if (userPrefs.propertyPreferences.propertyTypes.length > 0) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[62][0]++;
      cov_2avwqwwcso().s[210]++;
      totalChecks++;
      /* istanbul ignore next */
      cov_2avwqwwcso().s[211]++;
      if (userPrefs.propertyPreferences.propertyTypes.includes(property.propertyType)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[63][0]++;
        cov_2avwqwwcso().s[212]++;
        score += 100;
      } else
      /* istanbul ignore next */
      {
        cov_2avwqwwcso().b[63][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[62][1]++;
    }
    // Bedroom/bathroom requirements
    cov_2avwqwwcso().s[213]++;
    totalChecks++;
    /* istanbul ignore next */
    cov_2avwqwwcso().s[214]++;
    if (property.bedrooms >= userPrefs.propertyPreferences.minimumBedrooms) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[64][0]++;
      cov_2avwqwwcso().s[215]++;
      score += 100;
    } else {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[64][1]++;
      cov_2avwqwwcso().s[216]++;
      score += 50; // Partial credit
    }
    /* istanbul ignore next */
    cov_2avwqwwcso().s[217]++;
    totalChecks++;
    /* istanbul ignore next */
    cov_2avwqwwcso().s[218]++;
    if (property.bathrooms >= userPrefs.propertyPreferences.minimumBathrooms) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[65][0]++;
      cov_2avwqwwcso().s[219]++;
      score += 100;
    } else {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[65][1]++;
      cov_2avwqwwcso().s[220]++;
      score += 50;
    }
    // Amenities matching
    const requiredAmenities =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[221]++, userPrefs.propertyPreferences.amenities);
    /* istanbul ignore next */
    cov_2avwqwwcso().s[222]++;
    if (requiredAmenities.length > 0) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[66][0]++;
      cov_2avwqwwcso().s[223]++;
      totalChecks++;
      let amenityScore =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[224]++, 0);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[225]++;
      requiredAmenities.forEach(amenity => {
        /* istanbul ignore next */
        cov_2avwqwwcso().f[20]++;
        cov_2avwqwwcso().s[226]++;
        if (property.amenities[amenity]) {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[67][0]++;
          cov_2avwqwwcso().s[227]++;
          amenityScore += 100 / requiredAmenities.length;
        } else
        /* istanbul ignore next */
        {
          cov_2avwqwwcso().b[67][1]++;
        }
      });
      /* istanbul ignore next */
      cov_2avwqwwcso().s[228]++;
      score += amenityScore;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[66][1]++;
    }
    // Furnished preference
    cov_2avwqwwcso().s[229]++;
    if (userPrefs.propertyPreferences.furnished !== 'no_preference') {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[68][0]++;
      cov_2avwqwwcso().s[230]++;
      totalChecks++;
      const isFurnished =
      /* istanbul ignore next */
      (cov_2avwqwwcso().s[231]++, property.amenities.furnished);
      /* istanbul ignore next */
      cov_2avwqwwcso().s[232]++;
      if (
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[70][0]++, userPrefs.propertyPreferences.furnished === 'yes') &&
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[70][1]++, isFurnished) ||
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[70][2]++, userPrefs.propertyPreferences.furnished === 'no') &&
      /* istanbul ignore next */
      (cov_2avwqwwcso().b[70][3]++, !isFurnished)) {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[69][0]++;
        cov_2avwqwwcso().s[233]++;
        score += 100;
      } else {
        /* istanbul ignore next */
        cov_2avwqwwcso().b[69][1]++;
        cov_2avwqwwcso().s[234]++;
        if (userPrefs.propertyPreferences.furnished === 'partial') {
          /* istanbul ignore next */
          cov_2avwqwwcso().b[71][0]++;
          cov_2avwqwwcso().s[235]++;
          score += 70;
        } else
        /* istanbul ignore next */
        {
          cov_2avwqwwcso().b[71][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[68][1]++;
    }
    cov_2avwqwwcso().s[236]++;
    return totalChecks > 0 ?
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[72][0]++, Math.round(score / totalChecks)) :
    /* istanbul ignore next */
    (cov_2avwqwwcso().b[72][1]++, 70);
  }
  static calculatePropertyCleanlinessCompatibility(_userPrefs, property) {
    /* istanbul ignore next */
    cov_2avwqwwcso().f[21]++;
    // For properties, we can't directly assess cleanliness
    // But we can infer from amenities and property quality
    let score =
    /* istanbul ignore next */
    (cov_2avwqwwcso().s[237]++, 70); // Base score
    /* istanbul ignore next */
    cov_2avwqwwcso().s[238]++;
    if (property.amenities.cleaningService) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[73][0]++;
      cov_2avwqwwcso().s[239]++;
      score += 20;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[73][1]++;
    }
    cov_2avwqwwcso().s[240]++;
    if (property.isVerified) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[74][0]++;
      cov_2avwqwwcso().s[241]++;
      score += 10;
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[74][1]++;
    }
    cov_2avwqwwcso().s[242]++;
    if (property.amenities.furnished) {
      /* istanbul ignore next */
      cov_2avwqwwcso().b[75][0]++;
      cov_2avwqwwcso().s[243]++;
      score += 5; // Well-maintained properties are often furnished
    } else
    /* istanbul ignore next */
    {
      cov_2avwqwwcso().b[75][1]++;
    }
    cov_2avwqwwcso().s[244]++;
    return Math.min(score, 100);
  }
}
/* istanbul ignore next */
cov_2avwqwwcso().s[245]++;
exports.MatchingService = MatchingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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