{"version": 3, "names": ["cov_2y8j6ybmi", "s", "catchAsync", "fn", "f", "req", "res", "next", "Promise", "resolve", "catch", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\catchAsync.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\n\r\n/**\r\n * Wrapper function to catch async errors and pass them to error handling middleware\r\n */\r\nexport const catchAsync = (fn: Function) => {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    Promise.resolve(fn(req, res, next)).catch(next);\r\n  };\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;AAAA;AAAAA,aAAA,GAAAC,CAAA;AAGO,MAAMC,UAAU,GAAIC,EAAY,IAAI;EAAA;EAAAH,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAC,CAAA;EACzC,OAAO,CAACI,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAP,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACzDO,OAAO,CAACC,OAAO,CAACN,EAAE,CAACE,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,CAACG,KAAK,CAACH,IAAI,CAAC;EACjD,CAAC;AACH,CAAC;AAAC;AAAAP,aAAA,GAAAC,CAAA;AAJWU,OAAA,CAAAT,UAAU,GAAAA,UAAA", "ignoreList": []}