# LajoSpaces Backend Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:8080

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
MONGODB_TEST_URI=mongodb+srv://username:<EMAIL>/database_test?retryWrites=true&w=majority

# Redis Configuration
REDIS_URL=redis://username:password@host:port

# JWT Configuration (Generate secure random strings)
JWT_SECRET=your-super-secret-jwt-key-64-characters-long
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-refresh-secret-key-64-characters-long
JWT_REFRESH_EXPIRES_IN=7d
PASSWORD_RESET_SECRET=your-password-reset-secret-64-characters-long
PASSWORD_RESET_EXPIRES_IN=1h

# Email Configuration (Zoho Mail)
SMTP_HOST=smtp.zoho.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-zoho-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=LajoSpaces

# File Upload Configuration (Cloudinary)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-key
COOKIE_MAX_AGE=86400000

# API Configuration
API_VERSION=v1
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 🔒 SECURITY NOTES:
# 1. Never commit the actual .env file to Git
# 2. Generate strong, unique secrets for JWT tokens
# 3. Use app-specific passwords for email services
# 4. Keep database credentials secure
# 5. Use different secrets for development and production
