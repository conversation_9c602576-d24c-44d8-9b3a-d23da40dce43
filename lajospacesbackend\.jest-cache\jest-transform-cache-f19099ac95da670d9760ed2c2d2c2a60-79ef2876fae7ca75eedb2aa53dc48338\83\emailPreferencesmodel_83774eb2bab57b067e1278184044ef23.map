{"version": 3, "names": ["mongoose_1", "cov_1qjrwc7gqr", "s", "__importStar", "require", "EmailFrequency", "f", "b", "exports", "defaultPreferences", "accountSecurity", "loginAlerts", "passwordChanges", "emailChanges", "securityAlerts", "propertyUpdates", "newListings", "priceChanges", "statusUpdates", "favoriteUpdates", "nearbyProperties", "roommateMatching", "newMatches", "matchRequests", "matchAcceptance", "profileViews", "compatibilityUpdates", "messaging", "newMessages", "messageRequests", "conversationUpdates", "offlineMessages", "marketing", "newsletters", "promotions", "tips", "surveys", "productUpdates", "system", "maintenanceAlerts", "systemUpdates", "policyChanges", "featureAnnouncements", "emailPreferencesSchema", "<PERSON><PERSON><PERSON>", "userId", "type", "Types", "ObjectId", "ref", "required", "unique", "index", "preferences", "Boolean", "default", "globalSettings", "emailEnabled", "frequency", "String", "enum", "Object", "values", "IMMEDIATE", "quietHours", "enabled", "startTime", "endTime", "timezone", "unsubscribeAll", "deliverySettings", "format", "language", "lastUpdated", "Date", "now", "updatedBy", "timestamps", "toJSON", "virtuals", "toObject", "updatedAt", "virtual", "get", "methods", "updatePreference", "category", "setting", "value", "hasOwnProperty", "save", "Error", "updateGlobalSetting", "resubscribe", "shouldSendEmail", "emailsDisabled", "isInQuietHours", "userTime", "Intl", "DateTimeFormat", "timeZone", "hour12", "hour", "minute", "currentTime", "statics", "createDefault", "create", "getByUserId", "findOne", "pre", "next", "EmailPreferences", "model"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\emailPreferences.model.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\r\n\r\n// Email preference categories\r\nexport interface EmailPreferences {\r\n  // Account & Security\r\n  accountSecurity: {\r\n    loginAlerts: boolean;\r\n    passwordChanges: boolean;\r\n    emailChanges: boolean;\r\n    securityAlerts: boolean;\r\n  };\r\n  \r\n  // Property & Housing\r\n  propertyUpdates: {\r\n    newListings: boolean;\r\n    priceChanges: boolean;\r\n    statusUpdates: boolean;\r\n    favoriteUpdates: boolean;\r\n    nearbyProperties: boolean;\r\n  };\r\n  \r\n  // Roommate Matching\r\n  roommateMatching: {\r\n    newMatches: boolean;\r\n    matchRequests: boolean;\r\n    matchAcceptance: boolean;\r\n    profileViews: boolean;\r\n    compatibilityUpdates: boolean;\r\n  };\r\n  \r\n  // Messages & Communication\r\n  messaging: {\r\n    newMessages: boolean;\r\n    messageRequests: boolean;\r\n    conversationUpdates: boolean;\r\n    offlineMessages: boolean;\r\n  };\r\n  \r\n  // Marketing & Promotions\r\n  marketing: {\r\n    newsletters: boolean;\r\n    promotions: boolean;\r\n    tips: boolean;\r\n    surveys: boolean;\r\n    productUpdates: boolean;\r\n  };\r\n  \r\n  // System & Platform\r\n  system: {\r\n    maintenanceAlerts: boolean;\r\n    systemUpdates: boolean;\r\n    policyChanges: boolean;\r\n    featureAnnouncements: boolean;\r\n  };\r\n}\r\n\r\n// Email frequency options\r\nexport enum EmailFrequency {\r\n  IMMEDIATE = 'immediate',\r\n  DAILY = 'daily',\r\n  WEEKLY = 'weekly',\r\n  MONTHLY = 'monthly',\r\n  NEVER = 'never'\r\n}\r\n\r\n// Email preferences document interface\r\nexport interface IEmailPreferences extends Document {\r\n  _id: mongoose.Types.ObjectId;\r\n  userId: mongoose.Types.ObjectId;\r\n  \r\n  // Email preferences by category\r\n  preferences: EmailPreferences;\r\n  \r\n  // Global settings\r\n  globalSettings: {\r\n    emailEnabled: boolean;\r\n    frequency: EmailFrequency;\r\n    quietHours: {\r\n      enabled: boolean;\r\n      startTime: string; // HH:MM format\r\n      endTime: string;   // HH:MM format\r\n      timezone: string;\r\n    };\r\n    unsubscribeAll: boolean;\r\n  };\r\n  \r\n  // Delivery preferences\r\n  deliverySettings: {\r\n    format: 'html' | 'text' | 'both';\r\n    language: string;\r\n    timezone: string;\r\n  };\r\n  \r\n  // Tracking\r\n  lastUpdated: Date;\r\n  updatedBy: 'user' | 'system' | 'admin';\r\n  \r\n  // Timestamps\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\n// Default email preferences\r\nconst defaultPreferences: EmailPreferences = {\r\n  accountSecurity: {\r\n    loginAlerts: true,\r\n    passwordChanges: true,\r\n    emailChanges: true,\r\n    securityAlerts: true\r\n  },\r\n  propertyUpdates: {\r\n    newListings: true,\r\n    priceChanges: true,\r\n    statusUpdates: true,\r\n    favoriteUpdates: true,\r\n    nearbyProperties: false\r\n  },\r\n  roommateMatching: {\r\n    newMatches: true,\r\n    matchRequests: true,\r\n    matchAcceptance: true,\r\n    profileViews: false,\r\n    compatibilityUpdates: true\r\n  },\r\n  messaging: {\r\n    newMessages: true,\r\n    messageRequests: true,\r\n    conversationUpdates: false,\r\n    offlineMessages: true\r\n  },\r\n  marketing: {\r\n    newsletters: true,\r\n    promotions: false,\r\n    tips: true,\r\n    surveys: false,\r\n    productUpdates: true\r\n  },\r\n  system: {\r\n    maintenanceAlerts: true,\r\n    systemUpdates: true,\r\n    policyChanges: true,\r\n    featureAnnouncements: true\r\n  }\r\n};\r\n\r\n// Email preferences schema\r\nconst emailPreferencesSchema = new Schema<IEmailPreferences>({\r\n  userId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'User',\r\n    required: true,\r\n    unique: true,\r\n    index: true\r\n  },\r\n  \r\n  preferences: {\r\n    accountSecurity: {\r\n      loginAlerts: { type: Boolean, default: true },\r\n      passwordChanges: { type: Boolean, default: true },\r\n      emailChanges: { type: Boolean, default: true },\r\n      securityAlerts: { type: Boolean, default: true }\r\n    },\r\n    \r\n    propertyUpdates: {\r\n      newListings: { type: Boolean, default: true },\r\n      priceChanges: { type: Boolean, default: true },\r\n      statusUpdates: { type: Boolean, default: true },\r\n      favoriteUpdates: { type: Boolean, default: true },\r\n      nearbyProperties: { type: Boolean, default: false }\r\n    },\r\n    \r\n    roommateMatching: {\r\n      newMatches: { type: Boolean, default: true },\r\n      matchRequests: { type: Boolean, default: true },\r\n      matchAcceptance: { type: Boolean, default: true },\r\n      profileViews: { type: Boolean, default: false },\r\n      compatibilityUpdates: { type: Boolean, default: true }\r\n    },\r\n    \r\n    messaging: {\r\n      newMessages: { type: Boolean, default: true },\r\n      messageRequests: { type: Boolean, default: true },\r\n      conversationUpdates: { type: Boolean, default: false },\r\n      offlineMessages: { type: Boolean, default: true }\r\n    },\r\n    \r\n    marketing: {\r\n      newsletters: { type: Boolean, default: true },\r\n      promotions: { type: Boolean, default: false },\r\n      tips: { type: Boolean, default: true },\r\n      surveys: { type: Boolean, default: false },\r\n      productUpdates: { type: Boolean, default: true }\r\n    },\r\n    \r\n    system: {\r\n      maintenanceAlerts: { type: Boolean, default: true },\r\n      systemUpdates: { type: Boolean, default: true },\r\n      policyChanges: { type: Boolean, default: true },\r\n      featureAnnouncements: { type: Boolean, default: true }\r\n    }\r\n  },\r\n  \r\n  globalSettings: {\r\n    emailEnabled: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    frequency: {\r\n      type: String,\r\n      enum: Object.values(EmailFrequency),\r\n      default: EmailFrequency.IMMEDIATE\r\n    },\r\n    quietHours: {\r\n      enabled: { type: Boolean, default: false },\r\n      startTime: { type: String, default: '22:00' },\r\n      endTime: { type: String, default: '08:00' },\r\n      timezone: { type: String, default: 'Africa/Lagos' }\r\n    },\r\n    unsubscribeAll: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  deliverySettings: {\r\n    format: {\r\n      type: String,\r\n      enum: ['html', 'text', 'both'],\r\n      default: 'html'\r\n    },\r\n    language: {\r\n      type: String,\r\n      default: 'en'\r\n    },\r\n    timezone: {\r\n      type: String,\r\n      default: 'Africa/Lagos'\r\n    }\r\n  },\r\n  \r\n  lastUpdated: {\r\n    type: Date,\r\n    default: Date.now\r\n  },\r\n  \r\n  updatedBy: {\r\n    type: String,\r\n    enum: ['user', 'system', 'admin'],\r\n    default: 'user'\r\n  }\r\n}, {\r\n  timestamps: true,\r\n  toJSON: { virtuals: true },\r\n  toObject: { virtuals: true }\r\n});\r\n\r\n// Indexes\r\nemailPreferencesSchema.index({ userId: 1 });\r\nemailPreferencesSchema.index({ 'globalSettings.emailEnabled': 1 });\r\nemailPreferencesSchema.index({ updatedAt: -1 });\r\n\r\n// Virtual for checking if emails are globally disabled\r\nemailPreferencesSchema.virtual('emailsDisabled').get(function() {\r\n  return !this.globalSettings.emailEnabled || this.globalSettings.unsubscribeAll;\r\n});\r\n\r\n// Instance methods\r\nemailPreferencesSchema.methods.updatePreference = function(\r\n  category: keyof EmailPreferences,\r\n  setting: string,\r\n  value: boolean\r\n) {\r\n  if (this.preferences[category] && this.preferences[category].hasOwnProperty(setting)) {\r\n    this.preferences[category][setting] = value;\r\n    this.lastUpdated = new Date();\r\n    this.updatedBy = 'user';\r\n    return this.save();\r\n  }\r\n  throw new Error(`Invalid preference: ${category}.${setting}`);\r\n};\r\n\r\nemailPreferencesSchema.methods.updateGlobalSetting = function(\r\n  setting: string,\r\n  value: any\r\n) {\r\n  if (this.globalSettings.hasOwnProperty(setting)) {\r\n    this.globalSettings[setting] = value;\r\n    this.lastUpdated = new Date();\r\n    this.updatedBy = 'user';\r\n    return this.save();\r\n  }\r\n  throw new Error(`Invalid global setting: ${setting}`);\r\n};\r\n\r\nemailPreferencesSchema.methods.unsubscribeAll = function() {\r\n  this.globalSettings.unsubscribeAll = true;\r\n  this.globalSettings.emailEnabled = false;\r\n  this.lastUpdated = new Date();\r\n  this.updatedBy = 'user';\r\n  return this.save();\r\n};\r\n\r\nemailPreferencesSchema.methods.resubscribe = function() {\r\n  this.globalSettings.unsubscribeAll = false;\r\n  this.globalSettings.emailEnabled = true;\r\n  this.lastUpdated = new Date();\r\n  this.updatedBy = 'user';\r\n  return this.save();\r\n};\r\n\r\nemailPreferencesSchema.methods.shouldSendEmail = function(\r\n  category: keyof EmailPreferences,\r\n  setting: string\r\n): boolean {\r\n  // Check if emails are globally disabled\r\n  if (this.emailsDisabled) {\r\n    return false;\r\n  }\r\n  \r\n  // Check specific preference\r\n  if (this.preferences[category] && this.preferences[category].hasOwnProperty(setting)) {\r\n    return this.preferences[category][setting];\r\n  }\r\n  \r\n  return false;\r\n};\r\n\r\nemailPreferencesSchema.methods.isInQuietHours = function(): boolean {\r\n  if (!this.globalSettings.quietHours.enabled) {\r\n    return false;\r\n  }\r\n  \r\n  const now = new Date();\r\n  const timezone = this.deliverySettings.timezone;\r\n  \r\n  // Convert current time to user's timezone\r\n  const userTime = new Intl.DateTimeFormat('en-US', {\r\n    timeZone: timezone,\r\n    hour12: false,\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  }).format(now);\r\n  \r\n  const currentTime = userTime;\r\n  const startTime = this.globalSettings.quietHours.startTime;\r\n  const endTime = this.globalSettings.quietHours.endTime;\r\n  \r\n  // Handle overnight quiet hours (e.g., 22:00 to 08:00)\r\n  if (startTime > endTime) {\r\n    return currentTime >= startTime || currentTime <= endTime;\r\n  } else {\r\n    return currentTime >= startTime && currentTime <= endTime;\r\n  }\r\n};\r\n\r\n// Static methods\r\nemailPreferencesSchema.statics.createDefault = function(userId: mongoose.Types.ObjectId) {\r\n  return this.create({\r\n    userId,\r\n    preferences: defaultPreferences,\r\n    globalSettings: {\r\n      emailEnabled: true,\r\n      frequency: EmailFrequency.IMMEDIATE,\r\n      quietHours: {\r\n        enabled: false,\r\n        startTime: '22:00',\r\n        endTime: '08:00',\r\n        timezone: 'Africa/Lagos'\r\n      },\r\n      unsubscribeAll: false\r\n    },\r\n    deliverySettings: {\r\n      format: 'html',\r\n      language: 'en',\r\n      timezone: 'Africa/Lagos'\r\n    }\r\n  });\r\n};\r\n\r\nemailPreferencesSchema.statics.getByUserId = function(userId: mongoose.Types.ObjectId) {\r\n  return this.findOne({ userId });\r\n};\r\n\r\n// Pre-save middleware\r\nemailPreferencesSchema.pre('save', function(next) {\r\n  this.lastUpdated = new Date();\r\n  next();\r\n});\r\n\r\n// Create and export the model\r\nexport const EmailPreferences = mongoose.model<IEmailPreferences>('EmailPreferences', emailPreferencesSchema);\r\n\r\nexport default EmailPreferences;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,UAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AAwDA;AACA,IAAYC,cAMX;AAAA;AAAAJ,cAAA,GAAAC,CAAA;AAND,WAAYG,cAAc;EAAA;EAAAJ,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EACxBG,cAAA,2BAAuB;EAAA;EAAAJ,cAAA,GAAAC,CAAA;EACvBG,cAAA,mBAAe;EAAA;EAAAJ,cAAA,GAAAC,CAAA;EACfG,cAAA,qBAAiB;EAAA;EAAAJ,cAAA,GAAAC,CAAA;EACjBG,cAAA,uBAAmB;EAAA;EAAAJ,cAAA,GAAAC,CAAA;EACnBG,cAAA,mBAAe;AACjB,CAAC;AANW;AAAA,CAAAJ,cAAA,GAAAM,CAAA,WAAAF,cAAc;AAAA;AAAA,CAAAJ,cAAA,GAAAM,CAAA,WAAAC,OAAA,CAAAH,cAAA,GAAdA,cAAc;AA6C1B;AACA,MAAMI,kBAAkB;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAqB;EAC3CQ,eAAe,EAAE;IACfC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE;GACjB;EACDC,eAAe,EAAE;IACfC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE;GACnB;EACDC,gBAAgB,EAAE;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE,KAAK;IACnBC,oBAAoB,EAAE;GACvB;EACDC,SAAS,EAAE;IACTC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE,IAAI;IACrBC,mBAAmB,EAAE,KAAK;IAC1BC,eAAe,EAAE;GAClB;EACDC,SAAS,EAAE;IACTC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE;GACjB;EACDC,MAAM,EAAE;IACNC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE;;CAEzB;AAED;AACA,MAAMC,sBAAsB;AAAA;AAAA,CAAA1C,cAAA,GAAAC,CAAA,QAAG,IAAIF,UAAA,CAAA4C,MAAM,CAAoB;EAC3DC,MAAM,EAAE;IACNC,IAAI,EAAE9C,UAAA,CAAA4C,MAAM,CAACG,KAAK,CAACC,QAAQ;IAC3BC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;GACR;EAEDC,WAAW,EAAE;IACX3C,eAAe,EAAE;MACfC,WAAW,EAAE;QAAEmC,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC7C3C,eAAe,EAAE;QAAEkC,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACjD1C,YAAY,EAAE;QAAEiC,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC9CzC,cAAc,EAAE;QAAEgC,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI;KAC/C;IAEDxC,eAAe,EAAE;MACfC,WAAW,EAAE;QAAE8B,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC7CtC,YAAY,EAAE;QAAE6B,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC9CrC,aAAa,EAAE;QAAE4B,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/CpC,eAAe,EAAE;QAAE2B,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACjDnC,gBAAgB,EAAE;QAAE0B,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK;KAClD;IAEDlC,gBAAgB,EAAE;MAChBC,UAAU,EAAE;QAAEwB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC5ChC,aAAa,EAAE;QAAEuB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/C/B,eAAe,EAAE;QAAEsB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACjD9B,YAAY,EAAE;QAAEqB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAE;MAC/C7B,oBAAoB,EAAE;QAAEoB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI;KACrD;IAED5B,SAAS,EAAE;MACTC,WAAW,EAAE;QAAEkB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC7C1B,eAAe,EAAE;QAAEiB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACjDzB,mBAAmB,EAAE;QAAEgB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAE;MACtDxB,eAAe,EAAE;QAAEe,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI;KAChD;IAEDvB,SAAS,EAAE;MACTC,WAAW,EAAE;QAAEa,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC7CrB,UAAU,EAAE;QAAEY,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAE;MAC7CpB,IAAI,EAAE;QAAEW,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACtCnB,OAAO,EAAE;QAAEU,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAE;MAC1ClB,cAAc,EAAE;QAAES,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI;KAC/C;IAEDjB,MAAM,EAAE;MACNC,iBAAiB,EAAE;QAAEO,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MACnDf,aAAa,EAAE;QAAEM,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/Cd,aAAa,EAAE;QAAEK,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC/Cb,oBAAoB,EAAE;QAAEI,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAI;;GAEvD;EAEDC,cAAc,EAAE;IACdC,YAAY,EAAE;MACZX,IAAI,EAAEQ,OAAO;MACbC,OAAO,EAAE;KACV;IACDG,SAAS,EAAE;MACTZ,IAAI,EAAEa,MAAM;MACZC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAACzD,cAAc,CAAC;MACnCkD,OAAO,EAAElD,cAAc,CAAC0D;KACzB;IACDC,UAAU,EAAE;MACVC,OAAO,EAAE;QAAEnB,IAAI,EAAEQ,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAE;MAC1CW,SAAS,EAAE;QAAEpB,IAAI,EAAEa,MAAM;QAAEJ,OAAO,EAAE;MAAO,CAAE;MAC7CY,OAAO,EAAE;QAAErB,IAAI,EAAEa,MAAM;QAAEJ,OAAO,EAAE;MAAO,CAAE;MAC3Ca,QAAQ,EAAE;QAAEtB,IAAI,EAAEa,MAAM;QAAEJ,OAAO,EAAE;MAAc;KAClD;IACDc,cAAc,EAAE;MACdvB,IAAI,EAAEQ,OAAO;MACbC,OAAO,EAAE;;GAEZ;EAEDe,gBAAgB,EAAE;IAChBC,MAAM,EAAE;MACNzB,IAAI,EAAEa,MAAM;MACZC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MAC9BL,OAAO,EAAE;KACV;IACDiB,QAAQ,EAAE;MACR1B,IAAI,EAAEa,MAAM;MACZJ,OAAO,EAAE;KACV;IACDa,QAAQ,EAAE;MACRtB,IAAI,EAAEa,MAAM;MACZJ,OAAO,EAAE;;GAEZ;EAEDkB,WAAW,EAAE;IACX3B,IAAI,EAAE4B,IAAI;IACVnB,OAAO,EAAEmB,IAAI,CAACC;GACf;EAEDC,SAAS,EAAE;IACT9B,IAAI,EAAEa,MAAM;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IACjCL,OAAO,EAAE;;CAEZ,EAAE;EACDsB,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE;IAAEC,QAAQ,EAAE;EAAI,CAAE;EAC1BC,QAAQ,EAAE;IAAED,QAAQ,EAAE;EAAI;CAC3B,CAAC;AAEF;AAAA;AAAA9E,cAAA,GAAAC,CAAA;AACAyC,sBAAsB,CAACS,KAAK,CAAC;EAAEP,MAAM,EAAE;AAAC,CAAE,CAAC;AAAC;AAAA5C,cAAA,GAAAC,CAAA;AAC5CyC,sBAAsB,CAACS,KAAK,CAAC;EAAE,6BAA6B,EAAE;AAAC,CAAE,CAAC;AAAC;AAAAnD,cAAA,GAAAC,CAAA;AACnEyC,sBAAsB,CAACS,KAAK,CAAC;EAAE6B,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAE/C;AAAA;AAAAhF,cAAA,GAAAC,CAAA;AACAyC,sBAAsB,CAACuC,OAAO,CAAC,gBAAgB,CAAC,CAACC,GAAG,CAAC;EAAA;EAAAlF,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EACnD,OAAO,2BAAAD,cAAA,GAAAM,CAAA,YAAC,IAAI,CAACiD,cAAc,CAACC,YAAY;EAAA;EAAA,CAAAxD,cAAA,GAAAM,CAAA,WAAI,IAAI,CAACiD,cAAc,CAACa,cAAc;AAChF,CAAC,CAAC;AAEF;AAAA;AAAApE,cAAA,GAAAC,CAAA;AACAyC,sBAAsB,CAACyC,OAAO,CAACC,gBAAgB,GAAG,UAChDC,QAAgC,EAChCC,OAAe,EACfC,KAAc;EAAA;EAAAvF,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAEd;EAAI;EAAA,CAAAD,cAAA,GAAAM,CAAA,eAAI,CAAC8C,WAAW,CAACiC,QAAQ,CAAC;EAAA;EAAA,CAAArF,cAAA,GAAAM,CAAA,WAAI,IAAI,CAAC8C,WAAW,CAACiC,QAAQ,CAAC,CAACG,cAAc,CAACF,OAAO,CAAC,GAAE;IAAA;IAAAtF,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IACpF,IAAI,CAACmD,WAAW,CAACiC,QAAQ,CAAC,CAACC,OAAO,CAAC,GAAGC,KAAK;IAAC;IAAAvF,cAAA,GAAAC,CAAA;IAC5C,IAAI,CAACuE,WAAW,GAAG,IAAIC,IAAI,EAAE;IAAC;IAAAzE,cAAA,GAAAC,CAAA;IAC9B,IAAI,CAAC0E,SAAS,GAAG,MAAM;IAAC;IAAA3E,cAAA,GAAAC,CAAA;IACxB,OAAO,IAAI,CAACwF,IAAI,EAAE;EACpB,CAAC;EAAA;EAAA;IAAAzF,cAAA,GAAAM,CAAA;EAAA;EAAAN,cAAA,GAAAC,CAAA;EACD,MAAM,IAAIyF,KAAK,CAAC,uBAAuBL,QAAQ,IAAIC,OAAO,EAAE,CAAC;AAC/D,CAAC;AAAC;AAAAtF,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAACyC,OAAO,CAACQ,mBAAmB,GAAG,UACnDL,OAAe,EACfC,KAAU;EAAA;EAAAvF,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAEV,IAAI,IAAI,CAACsD,cAAc,CAACiC,cAAc,CAACF,OAAO,CAAC,EAAE;IAAA;IAAAtF,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IAC/C,IAAI,CAACsD,cAAc,CAAC+B,OAAO,CAAC,GAAGC,KAAK;IAAC;IAAAvF,cAAA,GAAAC,CAAA;IACrC,IAAI,CAACuE,WAAW,GAAG,IAAIC,IAAI,EAAE;IAAC;IAAAzE,cAAA,GAAAC,CAAA;IAC9B,IAAI,CAAC0E,SAAS,GAAG,MAAM;IAAC;IAAA3E,cAAA,GAAAC,CAAA;IACxB,OAAO,IAAI,CAACwF,IAAI,EAAE;EACpB,CAAC;EAAA;EAAA;IAAAzF,cAAA,GAAAM,CAAA;EAAA;EAAAN,cAAA,GAAAC,CAAA;EACD,MAAM,IAAIyF,KAAK,CAAC,2BAA2BJ,OAAO,EAAE,CAAC;AACvD,CAAC;AAAC;AAAAtF,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAACyC,OAAO,CAACf,cAAc,GAAG;EAAA;EAAApE,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAC9C,IAAI,CAACsD,cAAc,CAACa,cAAc,GAAG,IAAI;EAAC;EAAApE,cAAA,GAAAC,CAAA;EAC1C,IAAI,CAACsD,cAAc,CAACC,YAAY,GAAG,KAAK;EAAC;EAAAxD,cAAA,GAAAC,CAAA;EACzC,IAAI,CAACuE,WAAW,GAAG,IAAIC,IAAI,EAAE;EAAC;EAAAzE,cAAA,GAAAC,CAAA;EAC9B,IAAI,CAAC0E,SAAS,GAAG,MAAM;EAAC;EAAA3E,cAAA,GAAAC,CAAA;EACxB,OAAO,IAAI,CAACwF,IAAI,EAAE;AACpB,CAAC;AAAC;AAAAzF,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAACyC,OAAO,CAACS,WAAW,GAAG;EAAA;EAAA5F,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAC3C,IAAI,CAACsD,cAAc,CAACa,cAAc,GAAG,KAAK;EAAC;EAAApE,cAAA,GAAAC,CAAA;EAC3C,IAAI,CAACsD,cAAc,CAACC,YAAY,GAAG,IAAI;EAAC;EAAAxD,cAAA,GAAAC,CAAA;EACxC,IAAI,CAACuE,WAAW,GAAG,IAAIC,IAAI,EAAE;EAAC;EAAAzE,cAAA,GAAAC,CAAA;EAC9B,IAAI,CAAC0E,SAAS,GAAG,MAAM;EAAC;EAAA3E,cAAA,GAAAC,CAAA;EACxB,OAAO,IAAI,CAACwF,IAAI,EAAE;AACpB,CAAC;AAAC;AAAAzF,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAACyC,OAAO,CAACU,eAAe,GAAG,UAC/CR,QAAgC,EAChCC,OAAe;EAAA;EAAAtF,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAEf;EACA,IAAI,IAAI,CAAC6F,cAAc,EAAE;IAAA;IAAA9F,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IACvB,OAAO,KAAK;EACd,CAAC;EAAA;EAAA;IAAAD,cAAA,GAAAM,CAAA;EAAA;EAED;EAAAN,cAAA,GAAAC,CAAA;EACA;EAAI;EAAA,CAAAD,cAAA,GAAAM,CAAA,eAAI,CAAC8C,WAAW,CAACiC,QAAQ,CAAC;EAAA;EAAA,CAAArF,cAAA,GAAAM,CAAA,WAAI,IAAI,CAAC8C,WAAW,CAACiC,QAAQ,CAAC,CAACG,cAAc,CAACF,OAAO,CAAC,GAAE;IAAA;IAAAtF,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IACpF,OAAO,IAAI,CAACmD,WAAW,CAACiC,QAAQ,CAAC,CAACC,OAAO,CAAC;EAC5C,CAAC;EAAA;EAAA;IAAAtF,cAAA,GAAAM,CAAA;EAAA;EAAAN,cAAA,GAAAC,CAAA;EAED,OAAO,KAAK;AACd,CAAC;AAAC;AAAAD,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAACyC,OAAO,CAACY,cAAc,GAAG;EAAA;EAAA/F,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAC9C,IAAI,CAAC,IAAI,CAACsD,cAAc,CAACQ,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAhE,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IAC3C,OAAO,KAAK;EACd,CAAC;EAAA;EAAA;IAAAD,cAAA,GAAAM,CAAA;EAAA;EAED,MAAMoE,GAAG;EAAA;EAAA,CAAA1E,cAAA,GAAAC,CAAA,QAAG,IAAIwE,IAAI,EAAE;EACtB,MAAMN,QAAQ;EAAA;EAAA,CAAAnE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACoE,gBAAgB,CAACF,QAAQ;EAE/C;EACA,MAAM6B,QAAQ;EAAA;EAAA,CAAAhG,cAAA,GAAAC,CAAA,QAAG,IAAIgG,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;IAChDC,QAAQ,EAAEhC,QAAQ;IAClBiC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;GACT,CAAC,CAAChC,MAAM,CAACI,GAAG,CAAC;EAEd,MAAM6B,WAAW;EAAA;EAAA,CAAAvG,cAAA,GAAAC,CAAA,QAAG+F,QAAQ;EAC5B,MAAM/B,SAAS;EAAA;EAAA,CAAAjE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACsD,cAAc,CAACQ,UAAU,CAACE,SAAS;EAC1D,MAAMC,OAAO;EAAA;EAAA,CAAAlE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACsD,cAAc,CAACQ,UAAU,CAACG,OAAO;EAEtD;EAAA;EAAAlE,cAAA,GAAAC,CAAA;EACA,IAAIgE,SAAS,GAAGC,OAAO,EAAE;IAAA;IAAAlE,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IACvB,OAAO,2BAAAD,cAAA,GAAAM,CAAA,WAAAiG,WAAW,IAAItC,SAAS;IAAA;IAAA,CAAAjE,cAAA,GAAAM,CAAA,WAAIiG,WAAW,IAAIrC,OAAO;EAC3D,CAAC,MAAM;IAAA;IAAAlE,cAAA,GAAAM,CAAA;IAAAN,cAAA,GAAAC,CAAA;IACL,OAAO,2BAAAD,cAAA,GAAAM,CAAA,WAAAiG,WAAW,IAAItC,SAAS;IAAA;IAAA,CAAAjE,cAAA,GAAAM,CAAA,WAAIiG,WAAW,IAAIrC,OAAO;EAC3D;AACF,CAAC;AAED;AAAA;AAAAlE,cAAA,GAAAC,CAAA;AACAyC,sBAAsB,CAAC8D,OAAO,CAACC,aAAa,GAAG,UAAS7D,MAA+B;EAAA;EAAA5C,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EACrF,OAAO,IAAI,CAACyG,MAAM,CAAC;IACjB9D,MAAM;IACNQ,WAAW,EAAE5C,kBAAkB;IAC/B+C,cAAc,EAAE;MACdC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAErD,cAAc,CAAC0D,SAAS;MACnCC,UAAU,EAAE;QACVC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE;OACX;MACDC,cAAc,EAAE;KACjB;IACDC,gBAAgB,EAAE;MAChBC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,IAAI;MACdJ,QAAQ,EAAE;;GAEb,CAAC;AACJ,CAAC;AAAC;AAAAnE,cAAA,GAAAC,CAAA;AAEFyC,sBAAsB,CAAC8D,OAAO,CAACG,WAAW,GAAG,UAAS/D,MAA+B;EAAA;EAAA5C,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EACnF,OAAO,IAAI,CAAC2G,OAAO,CAAC;IAAEhE;EAAM,CAAE,CAAC;AACjC,CAAC;AAED;AAAA;AAAA5C,cAAA,GAAAC,CAAA;AACAyC,sBAAsB,CAACmE,GAAG,CAAC,MAAM,EAAE,UAASC,IAAI;EAAA;EAAA9G,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAC,CAAA;EAC9C,IAAI,CAACuE,WAAW,GAAG,IAAIC,IAAI,EAAE;EAAC;EAAAzE,cAAA,GAAAC,CAAA;EAC9B6G,IAAI,EAAE;AACR,CAAC,CAAC;AAEF;AAAA;AAAA9G,cAAA,GAAAC,CAAA;AACaM,OAAA,CAAAwG,gBAAgB,GAAGhH,UAAA,CAAAuD,OAAQ,CAAC0D,KAAK,CAAoB,kBAAkB,EAAEtE,sBAAsB,CAAC;AAAC;AAAA1C,cAAA,GAAAC,CAAA;AAE9GM,OAAA,CAAA+C,OAAA,GAAe/C,OAAA,CAAAwG,gBAAgB", "ignoreList": []}