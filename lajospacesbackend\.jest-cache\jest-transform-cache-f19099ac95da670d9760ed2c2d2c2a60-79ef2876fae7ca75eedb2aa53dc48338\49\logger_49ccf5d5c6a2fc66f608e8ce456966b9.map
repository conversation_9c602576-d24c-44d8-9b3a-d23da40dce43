{"version": 3, "names": ["cov_2owy25toec", "actualCoverage", "winston_1", "s", "__importDefault", "require", "path_1", "environment_1", "logLevels", "error", "warn", "info", "http", "debug", "logColors", "default", "addColors", "logFormat", "format", "combine", "timestamp", "colorize", "all", "printf", "f", "level", "message", "fileFormat", "errors", "stack", "json", "transports", "config", "NODE_ENV", "b", "push", "<PERSON><PERSON><PERSON>", "LOG_LEVEL", "logsDir", "dirname", "LOG_FILE", "File", "filename", "maxsize", "maxFiles", "join", "exports", "logger", "createLogger", "levels", "exceptionHandlers", "rejectionHandlers", "exitOnError", "morganStream", "write", "trim", "logHelpers", "userAction", "userId", "action", "details", "Date", "toISOString", "apiRequest", "method", "url", "ip", "dbOperation", "operation", "collection", "authEvent", "event", "securityEvent", "severity", "logLevel", "performance", "metric", "value", "unit", "errorWithContext", "context", "name"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\logger.ts"], "sourcesContent": ["import winston from 'winston';\r\nimport path from 'path';\r\nimport { config } from '../config/environment';\r\n\r\n// Define log levels\r\nconst logLevels = {\r\n  error: 0,\r\n  warn: 1,\r\n  info: 2,\r\n  http: 3,\r\n  debug: 4,\r\n};\r\n\r\n// Define log colors\r\nconst logColors = {\r\n  error: 'red',\r\n  warn: 'yellow',\r\n  info: 'green',\r\n  http: 'magenta',\r\n  debug: 'white',\r\n};\r\n\r\n// Add colors to winston\r\nwinston.addColors(logColors);\r\n\r\n// Create log format\r\nconst logFormat = winston.format.combine(\r\n  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),\r\n  winston.format.colorize({ all: true }),\r\n  winston.format.printf(\r\n    (info) => `${info.timestamp} ${info.level}: ${info.message}`\r\n  )\r\n);\r\n\r\n// Create file format (without colors)\r\nconst fileFormat = winston.format.combine(\r\n  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),\r\n  winston.format.errors({ stack: true }),\r\n  winston.format.json()\r\n);\r\n\r\n// Define transports\r\nconst transports: winston.transport[] = [];\r\n\r\n// Console transport (always enabled in development)\r\nif (config.NODE_ENV === 'development') {\r\n  transports.push(\r\n    new winston.transports.Console({\r\n      format: logFormat,\r\n      level: 'debug'\r\n    })\r\n  );\r\n} else {\r\n  transports.push(\r\n    new winston.transports.Console({\r\n      format: logFormat,\r\n      level: config.LOG_LEVEL\r\n    })\r\n  );\r\n}\r\n\r\n// File transports (for production and development)\r\nif (config.NODE_ENV !== 'test') {\r\n  // Ensure logs directory exists\r\n  const logsDir = path.dirname(config.LOG_FILE);\r\n  \r\n  // Combined logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: config.LOG_FILE,\r\n      format: fileFormat,\r\n      level: config.LOG_LEVEL,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 5,\r\n    })\r\n  );\r\n\r\n  // Error logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: path.join(logsDir, 'error.log'),\r\n      format: fileFormat,\r\n      level: 'error',\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 5,\r\n    })\r\n  );\r\n\r\n  // HTTP logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: path.join(logsDir, 'http.log'),\r\n      format: fileFormat,\r\n      level: 'http',\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 3,\r\n    })\r\n  );\r\n}\r\n\r\n// Create logger instance\r\nexport const logger = winston.createLogger({\r\n  level: config.LOG_LEVEL,\r\n  levels: logLevels,\r\n  format: fileFormat,\r\n  transports,\r\n  exceptionHandlers: [\r\n    new winston.transports.File({\r\n      filename: path.join(path.dirname(config.LOG_FILE), 'exceptions.log'),\r\n      format: fileFormat,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 2,\r\n    })\r\n  ],\r\n  rejectionHandlers: [\r\n    new winston.transports.File({\r\n      filename: path.join(path.dirname(config.LOG_FILE), 'rejections.log'),\r\n      format: fileFormat,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 2,\r\n    })\r\n  ],\r\n  exitOnError: false,\r\n});\r\n\r\n// Create a stream object for Morgan HTTP logging\r\nexport const morganStream = {\r\n  write: (message: string) => {\r\n    logger.http(message.trim());\r\n  },\r\n};\r\n\r\n// Helper functions for structured logging\r\nexport const logHelpers = {\r\n  /**\r\n   * Log user action\r\n   */\r\n  userAction: (userId: string, action: string, details?: any) => {\r\n    logger.info(`User Action: ${action}`, {\r\n      userId,\r\n      action,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log API request\r\n   */\r\n  apiRequest: (method: string, url: string, userId?: string, ip?: string) => {\r\n    logger.http(`API Request: ${method} ${url}`, {\r\n      method,\r\n      url,\r\n      userId,\r\n      ip,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log database operation\r\n   */\r\n  dbOperation: (operation: string, collection: string, details?: any) => {\r\n    logger.debug(`DB Operation: ${operation} on ${collection}`, {\r\n      operation,\r\n      collection,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log authentication event\r\n   */\r\n  authEvent: (event: string, userId?: string, ip?: string, details?: any) => {\r\n    logger.info(`Auth Event: ${event}`, {\r\n      event,\r\n      userId,\r\n      ip,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log security event\r\n   */\r\n  securityEvent: (event: string, severity: 'low' | 'medium' | 'high', details?: any) => {\r\n    const logLevel = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'info';\r\n    logger[logLevel](`Security Event: ${event}`, {\r\n      event,\r\n      severity,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log performance metric\r\n   */\r\n  performance: (metric: string, value: number, unit: string, details?: any) => {\r\n    logger.info(`Performance: ${metric} = ${value}${unit}`, {\r\n      metric,\r\n      value,\r\n      unit,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log error with context\r\n   */\r\n  errorWithContext: (error: Error, context: string, details?: any) => {\r\n    logger.error(`Error in ${context}: ${error.message}`, {\r\n      error: {\r\n        name: error.name,\r\n        message: error.message,\r\n        stack: error.stack\r\n      },\r\n      context,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n};\r\n\r\nexport default logger;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUU;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVV,MAAAE,SAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAE,aAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAE,OAAA;AAEA;AACA,MAAMG,SAAS;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAG;EAChBM,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;CACR;AAED;AACA,MAAMC,SAAS;AAAA;AAAA,CAAAd,cAAA,GAAAG,CAAA,OAAG;EAChBM,KAAK,EAAE,KAAK;EACZC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;CACR;AAED;AAAA;AAAAb,cAAA,GAAAG,CAAA;AACAD,SAAA,CAAAa,OAAO,CAACC,SAAS,CAACF,SAAS,CAAC;AAE5B;AACA,MAAMG,SAAS;AAAA;AAAA,CAAAjB,cAAA,GAAAG,CAAA,QAAGD,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACC,OAAO,CACtCjB,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACE,SAAS,CAAC;EAAEF,MAAM,EAAE;AAAwB,CAAE,CAAC,EAC9DhB,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACG,QAAQ,CAAC;EAAEC,GAAG,EAAE;AAAI,CAAE,CAAC,EACtCpB,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACK,MAAM,CAClBZ,IAAI,IAAK;EAAA;EAAAX,cAAA,GAAAwB,CAAA;EAAAxB,cAAA,GAAAG,CAAA;EAAA,UAAGQ,IAAI,CAACS,SAAS,IAAIT,IAAI,CAACc,KAAK,KAAKd,IAAI,CAACe,OAAO,EAAE;AAAF,CAAE,CAC7D,CACF;AAED;AACA,MAAMC,UAAU;AAAA;AAAA,CAAA3B,cAAA,GAAAG,CAAA,QAAGD,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACC,OAAO,CACvCjB,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACE,SAAS,CAAC;EAAEF,MAAM,EAAE;AAAwB,CAAE,CAAC,EAC9DhB,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACU,MAAM,CAAC;EAAEC,KAAK,EAAE;AAAI,CAAE,CAAC,EACtC3B,SAAA,CAAAa,OAAO,CAACG,MAAM,CAACY,IAAI,EAAE,CACtB;AAED;AACA,MAAMC,UAAU;AAAA;AAAA,CAAA/B,cAAA,GAAAG,CAAA,QAAwB,EAAE;AAE1C;AAAA;AAAAH,cAAA,GAAAG,CAAA;AACA,IAAII,aAAA,CAAAyB,MAAM,CAACC,QAAQ,KAAK,aAAa,EAAE;EAAA;EAAAjC,cAAA,GAAAkC,CAAA;EAAAlC,cAAA,GAAAG,CAAA;EACrC4B,UAAU,CAACI,IAAI,CACb,IAAIjC,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACK,OAAO,CAAC;IAC7BlB,MAAM,EAAED,SAAS;IACjBQ,KAAK,EAAE;GACR,CAAC,CACH;AACH,CAAC,MAAM;EAAA;EAAAzB,cAAA,GAAAkC,CAAA;EAAAlC,cAAA,GAAAG,CAAA;EACL4B,UAAU,CAACI,IAAI,CACb,IAAIjC,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACK,OAAO,CAAC;IAC7BlB,MAAM,EAAED,SAAS;IACjBQ,KAAK,EAAElB,aAAA,CAAAyB,MAAM,CAACK;GACf,CAAC,CACH;AACH;AAEA;AAAA;AAAArC,cAAA,GAAAG,CAAA;AACA,IAAII,aAAA,CAAAyB,MAAM,CAACC,QAAQ,KAAK,MAAM,EAAE;EAAA;EAAAjC,cAAA,GAAAkC,CAAA;EAC9B;EACA,MAAMI,OAAO;EAAA;EAAA,CAAAtC,cAAA,GAAAG,CAAA,QAAGG,MAAA,CAAAS,OAAI,CAACwB,OAAO,CAAChC,aAAA,CAAAyB,MAAM,CAACQ,QAAQ,CAAC;EAE7C;EAAA;EAAAxC,cAAA,GAAAG,CAAA;EACA4B,UAAU,CAACI,IAAI,CACb,IAAIjC,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACU,IAAI,CAAC;IAC1BC,QAAQ,EAAEnC,aAAA,CAAAyB,MAAM,CAACQ,QAAQ;IACzBtB,MAAM,EAAES,UAAU;IAClBF,KAAK,EAAElB,aAAA,CAAAyB,MAAM,CAACK,SAAS;IACvBM,OAAO,EAAE,OAAO;IAAE;IAClBC,QAAQ,EAAE;GACX,CAAC,CACH;EAED;EAAA;EAAA5C,cAAA,GAAAG,CAAA;EACA4B,UAAU,CAACI,IAAI,CACb,IAAIjC,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACU,IAAI,CAAC;IAC1BC,QAAQ,EAAEpC,MAAA,CAAAS,OAAI,CAAC8B,IAAI,CAACP,OAAO,EAAE,WAAW,CAAC;IACzCpB,MAAM,EAAES,UAAU;IAClBF,KAAK,EAAE,OAAO;IACdkB,OAAO,EAAE,OAAO;IAAE;IAClBC,QAAQ,EAAE;GACX,CAAC,CACH;EAED;EAAA;EAAA5C,cAAA,GAAAG,CAAA;EACA4B,UAAU,CAACI,IAAI,CACb,IAAIjC,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACU,IAAI,CAAC;IAC1BC,QAAQ,EAAEpC,MAAA,CAAAS,OAAI,CAAC8B,IAAI,CAACP,OAAO,EAAE,UAAU,CAAC;IACxCpB,MAAM,EAAES,UAAU;IAClBF,KAAK,EAAE,MAAM;IACbkB,OAAO,EAAE,OAAO;IAAE;IAClBC,QAAQ,EAAE;GACX,CAAC,CACH;AACH,CAAC;AAAA;AAAA;EAAA5C,cAAA,GAAAkC,CAAA;AAAA;AAED;AAAAlC,cAAA,GAAAG,CAAA;AACa2C,OAAA,CAAAC,MAAM,GAAG7C,SAAA,CAAAa,OAAO,CAACiC,YAAY,CAAC;EACzCvB,KAAK,EAAElB,aAAA,CAAAyB,MAAM,CAACK,SAAS;EACvBY,MAAM,EAAEzC,SAAS;EACjBU,MAAM,EAAES,UAAU;EAClBI,UAAU;EACVmB,iBAAiB,EAAE,CACjB,IAAIhD,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACU,IAAI,CAAC;IAC1BC,QAAQ,EAAEpC,MAAA,CAAAS,OAAI,CAAC8B,IAAI,CAACvC,MAAA,CAAAS,OAAI,CAACwB,OAAO,CAAChC,aAAA,CAAAyB,MAAM,CAACQ,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IACpEtB,MAAM,EAAES,UAAU;IAClBgB,OAAO,EAAE,OAAO;IAAE;IAClBC,QAAQ,EAAE;GACX,CAAC,CACH;EACDO,iBAAiB,EAAE,CACjB,IAAIjD,SAAA,CAAAa,OAAO,CAACgB,UAAU,CAACU,IAAI,CAAC;IAC1BC,QAAQ,EAAEpC,MAAA,CAAAS,OAAI,CAAC8B,IAAI,CAACvC,MAAA,CAAAS,OAAI,CAACwB,OAAO,CAAChC,aAAA,CAAAyB,MAAM,CAACQ,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IACpEtB,MAAM,EAAES,UAAU;IAClBgB,OAAO,EAAE,OAAO;IAAE;IAClBC,QAAQ,EAAE;GACX,CAAC,CACH;EACDQ,WAAW,EAAE;CACd,CAAC;AAEF;AAAA;AAAApD,cAAA,GAAAG,CAAA;AACa2C,OAAA,CAAAO,YAAY,GAAG;EAC1BC,KAAK,EAAG5B,OAAe,IAAI;IAAA;IAAA1B,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACzB2C,OAAA,CAAAC,MAAM,CAACnC,IAAI,CAACc,OAAO,CAAC6B,IAAI,EAAE,CAAC;EAC7B;CACD;AAED;AAAA;AAAAvD,cAAA,GAAAG,CAAA;AACa2C,OAAA,CAAAU,UAAU,GAAG;EACxB;;;EAGAC,UAAU,EAAEA,CAACC,MAAc,EAAEC,MAAc,EAAEC,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IAC5D2C,OAAA,CAAAC,MAAM,CAACpC,IAAI,CAAC,gBAAgBgD,MAAM,EAAE,EAAE;MACpCD,MAAM;MACNC,MAAM;MACNC,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAC,UAAU,EAAEA,CAACC,MAAc,EAAEC,GAAW,EAAEP,MAAe,EAAEQ,EAAW,KAAI;IAAA;IAAAlE,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACxE2C,OAAA,CAAAC,MAAM,CAACnC,IAAI,CAAC,gBAAgBoD,MAAM,IAAIC,GAAG,EAAE,EAAE;MAC3CD,MAAM;MACNC,GAAG;MACHP,MAAM;MACNQ,EAAE;MACF9C,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAK,WAAW,EAAEA,CAACC,SAAiB,EAAEC,UAAkB,EAAET,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACpE2C,OAAA,CAAAC,MAAM,CAAClC,KAAK,CAAC,iBAAiBuD,SAAS,OAAOC,UAAU,EAAE,EAAE;MAC1DD,SAAS;MACTC,UAAU;MACVT,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAQ,SAAS,EAAEA,CAACC,KAAa,EAAEb,MAAe,EAAEQ,EAAW,EAAEN,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACxE2C,OAAA,CAAAC,MAAM,CAACpC,IAAI,CAAC,eAAe4D,KAAK,EAAE,EAAE;MAClCA,KAAK;MACLb,MAAM;MACNQ,EAAE;MACFN,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAU,aAAa,EAAEA,CAACD,KAAa,EAAEE,QAAmC,EAAEb,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IACnF,MAAMkD,QAAQ;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,QAAGsE,QAAQ,KAAK,MAAM;IAAA;IAAA,CAAAzE,cAAA,GAAAkC,CAAA,UAAG,OAAO;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAGuC,QAAQ,KAAK,QAAQ;IAAA;IAAA,CAAAzE,cAAA,GAAAkC,CAAA,UAAG,MAAM;IAAA;IAAA,CAAAlC,cAAA,GAAAkC,CAAA,UAAG,MAAM;IAAC;IAAAlC,cAAA,GAAAG,CAAA;IACzF2C,OAAA,CAAAC,MAAM,CAAC2B,QAAQ,CAAC,CAAC,mBAAmBH,KAAK,EAAE,EAAE;MAC3CA,KAAK;MACLE,QAAQ;MACRb,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAa,WAAW,EAAEA,CAACC,MAAc,EAAEC,KAAa,EAAEC,IAAY,EAAElB,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IAC1E2C,OAAA,CAAAC,MAAM,CAACpC,IAAI,CAAC,gBAAgBiE,MAAM,MAAMC,KAAK,GAAGC,IAAI,EAAE,EAAE;MACtDF,MAAM;MACNC,KAAK;MACLC,IAAI;MACJlB,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ,CAAC;EAED;;;EAGAiB,gBAAgB,EAAEA,CAACtE,KAAY,EAAEuE,OAAe,EAAEpB,OAAa,KAAI;IAAA;IAAA5D,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAG,CAAA;IACjE2C,OAAA,CAAAC,MAAM,CAACtC,KAAK,CAAC,YAAYuE,OAAO,KAAKvE,KAAK,CAACiB,OAAO,EAAE,EAAE;MACpDjB,KAAK,EAAE;QACLwE,IAAI,EAAExE,KAAK,CAACwE,IAAI;QAChBvD,OAAO,EAAEjB,KAAK,CAACiB,OAAO;QACtBG,KAAK,EAAEpB,KAAK,CAACoB;OACd;MACDmD,OAAO;MACPpB,OAAO;MACPxC,SAAS,EAAE,IAAIyC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;CACD;AAAC;AAAA9D,cAAA,GAAAG,CAAA;AAEF2C,OAAA,CAAA/B,OAAA,GAAe+B,OAAA,CAAAC,MAAM", "ignoreList": []}