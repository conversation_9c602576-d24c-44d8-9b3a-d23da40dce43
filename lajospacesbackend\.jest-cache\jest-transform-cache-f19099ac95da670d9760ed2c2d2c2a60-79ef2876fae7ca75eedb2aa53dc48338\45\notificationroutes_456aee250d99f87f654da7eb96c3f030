a0785578b24b0cfbc9b2ba5dcf6679f4
"use strict";

/* istanbul ignore next */
function cov_25l1wm8dhf() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\notification.routes.ts";
  var hash = "4398d253d16908842c306a784dbf5f5a400184f3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\notification.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 34
        },
        end: {
          line: 4,
          column: 83
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 34
        },
        end: {
          line: 7,
          column: 82
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 32
        }
      },
      "8": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 162
        }
      },
      "9": {
        start: {
          line: 22,
          column: 0
        },
        end: {
          line: 22,
          column: 140
        }
      },
      "10": {
        start: {
          line: 28,
          column: 0
        },
        end: {
          line: 28,
          column: 78
        }
      },
      "11": {
        start: {
          line: 34,
          column: 0
        },
        end: {
          line: 34,
          column: 135
        }
      },
      "12": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 69
        }
      },
      "13": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 80
        }
      },
      "14": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 182
        }
      },
      "15": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 84
        }
      },
      "16": {
        start: {
          line: 64,
          column: 0
        },
        end: {
          line: 64,
          column: 75
        }
      },
      "17": {
        start: {
          line: 70,
          column: 0
        },
        end: {
          line: 82,
          column: 3
        }
      },
      "18": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 81,
          column: 7
        }
      },
      "19": {
        start: {
          line: 83,
          column: 0
        },
        end: {
          line: 83,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 70,
            column: 22
          },
          end: {
            line: 70,
            column: 23
          }
        },
        loc: {
          start: {
            line: 70,
            column: 36
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 70
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\notification.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,oFAUgD;AAChD,6CAAkD;AAClD,yDAA6E;AAC7E,mFAG+C;AAE/C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,iDAAiD;AACjD,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,GAAG,EACH,IAAA,4BAAe,EAAC,oDAA0B,EAAE,OAAO,CAAC,EACpD,8CAAoB,CACrB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,uBAAuB,EACvB,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,gDAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,WAAW,EACX,oDAA0B,CAC3B,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,CACX,kBAAkB,EAClB,IAAA,6BAAgB,EAAC,gBAAgB,CAAC,EAClC,6CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,QAAQ,EACR,8CAAoB,CACrB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,oBAAoB,EACpB,6CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,oBAAoB,EACpB,IAAA,4BAAe,EAAC,sDAA4B,EAAE,MAAM,CAAC,EACrD,gDAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,kBAAkB,EAClB,kDAAwB,CACzB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,cAAc,EACd,6CAAmB,CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,iCAAiC;QAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE;YACR,kBAAkB,EAAE,QAAQ;YAC5B,kBAAkB,EAAE,QAAQ;YAC5B,iBAAiB,EAAE,SAAS;YAC5B,gBAAgB,EAAE,SAAS;SAC5B;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\notification.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  getUserNotifications,\r\n  markNotificationAsRead,\r\n  markAllNotificationsAsRead,\r\n  dismissNotification,\r\n  getNotificationStats,\r\n  getEmailPreferences,\r\n  updateEmailPreferences,\r\n  unsubscribeFromAllEmails,\r\n  resubscribeToEmails\r\n} from '../controllers/notification.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  getUserNotificationsSchema,\r\n  updateEmailPreferencesSchema\r\n} from '../validators/notification.validators';\r\n\r\nconst router = Router();\r\n\r\n// All notification routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   GET /api/notifications\r\n * @desc    Get user notifications with pagination\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(getUserNotificationsSchema, 'query'),\r\n  getUserNotifications\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/:notificationId/read\r\n * @desc    Mark notification as read\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:notificationId/read',\r\n  validateObjectId('notificationId'),\r\n  markNotificationAsRead\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/read-all\r\n * @desc    Mark all notifications as read\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/read-all',\r\n  markAllNotificationsAsRead\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/notifications/:notificationId\r\n * @desc    Dismiss notification\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:notificationId',\r\n  validateObjectId('notificationId'),\r\n  dismissNotification\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/stats\r\n * @desc    Get notification statistics\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/stats',\r\n  getNotificationStats\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/email-preferences\r\n * @desc    Get user email preferences\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/email-preferences',\r\n  getEmailPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/email-preferences\r\n * @desc    Update user email preferences\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/email-preferences',\r\n  validateRequest(updateEmailPreferencesSchema, 'body'),\r\n  updateEmailPreferences\r\n);\r\n\r\n/**\r\n * @route   POST /api/notifications/unsubscribe-all\r\n * @desc    Unsubscribe from all emails\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/unsubscribe-all',\r\n  unsubscribeFromAllEmails\r\n);\r\n\r\n/**\r\n * @route   POST /api/notifications/resubscribe\r\n * @desc    Resubscribe to emails\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/resubscribe',\r\n  resubscribeToEmails\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/health\r\n * @desc    Health check for notification service\r\n * @access  Private\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Notification service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    features: {\r\n      inAppNotifications: 'active',\r\n      emailNotifications: 'active',\r\n      pushNotifications: 'planned',\r\n      smsNotifications: 'planned'\r\n    }\r\n  });\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4398d253d16908842c306a784dbf5f5a400184f3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25l1wm8dhf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25l1wm8dhf();
cov_25l1wm8dhf().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[1]++, require("express"));
const notification_controller_1 =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[2]++, require("../controllers/notification.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[4]++, require("../middleware/validation"));
const notification_validators_1 =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[5]++, require("../validators/notification.validators"));
const router =
/* istanbul ignore next */
(cov_25l1wm8dhf().s[6]++, (0, express_1.Router)());
// All notification routes require authentication
/* istanbul ignore next */
cov_25l1wm8dhf().s[7]++;
router.use(auth_1.authenticate);
/**
 * @route   GET /api/notifications
 * @desc    Get user notifications with pagination
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[8]++;
router.get('/', (0, validation_1.validateRequest)(notification_validators_1.getUserNotificationsSchema, 'query'), notification_controller_1.getUserNotifications);
/**
 * @route   PUT /api/notifications/:notificationId/read
 * @desc    Mark notification as read
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[9]++;
router.put('/:notificationId/read', (0, validation_1.validateObjectId)('notificationId'), notification_controller_1.markNotificationAsRead);
/**
 * @route   PUT /api/notifications/read-all
 * @desc    Mark all notifications as read
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[10]++;
router.put('/read-all', notification_controller_1.markAllNotificationsAsRead);
/**
 * @route   DELETE /api/notifications/:notificationId
 * @desc    Dismiss notification
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[11]++;
router.delete('/:notificationId', (0, validation_1.validateObjectId)('notificationId'), notification_controller_1.dismissNotification);
/**
 * @route   GET /api/notifications/stats
 * @desc    Get notification statistics
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[12]++;
router.get('/stats', notification_controller_1.getNotificationStats);
/**
 * @route   GET /api/notifications/email-preferences
 * @desc    Get user email preferences
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[13]++;
router.get('/email-preferences', notification_controller_1.getEmailPreferences);
/**
 * @route   PUT /api/notifications/email-preferences
 * @desc    Update user email preferences
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[14]++;
router.put('/email-preferences', (0, validation_1.validateRequest)(notification_validators_1.updateEmailPreferencesSchema, 'body'), notification_controller_1.updateEmailPreferences);
/**
 * @route   POST /api/notifications/unsubscribe-all
 * @desc    Unsubscribe from all emails
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[15]++;
router.post('/unsubscribe-all', notification_controller_1.unsubscribeFromAllEmails);
/**
 * @route   POST /api/notifications/resubscribe
 * @desc    Resubscribe to emails
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[16]++;
router.post('/resubscribe', notification_controller_1.resubscribeToEmails);
/**
 * @route   GET /api/notifications/health
 * @desc    Health check for notification service
 * @access  Private
 */
/* istanbul ignore next */
cov_25l1wm8dhf().s[17]++;
router.get('/health', (req, res) => {
  /* istanbul ignore next */
  cov_25l1wm8dhf().f[0]++;
  cov_25l1wm8dhf().s[18]++;
  res.json({
    success: true,
    message: 'Notification service is healthy',
    timestamp: new Date().toISOString(),
    features: {
      inAppNotifications: 'active',
      emailNotifications: 'active',
      pushNotifications: 'planned',
      smsNotifications: 'planned'
    }
  });
});
/* istanbul ignore next */
cov_25l1wm8dhf().s[19]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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