{"version": 3, "names": ["Match_1", "cov_2a8za2xt6w", "s", "require", "logger_1", "appError_1", "MatchExpirationService", "processExpiredMatches", "f", "now", "Date", "expiredMatches", "Match", "find", "status", "expiresAt", "$lt", "expired", "extended", "errors", "match", "shouldExtend", "shouldExtendMatch", "b", "extendExpiration", "save", "logger", "info", "_id", "userId", "error", "result", "processed", "length", "AppError", "compatibilityScore", "viewedAt", "daysS<PERSON><PERSON><PERSON><PERSON>wed", "getTime", "viewCount", "cleanupOldMatches", "thirtyDaysAgo", "setDate", "getDate", "deleteMany", "updatedAt", "deletedCount", "deleted", "getExpirationStats", "tomorrow", "totalMatches", "pendingMatches", "expiring<PERSON><PERSON><PERSON>", "expiringSoon", "matchDurations", "Promise", "all", "countDocuments", "$gte", "getFullYear", "getMonth", "aggregate", "$match", "$in", "createdAt", "$exists", "lastInteractionAt", "$project", "duration", "$divide", "$subtract", "$group", "averageDuration", "$avg", "averageMatchDuration", "Math", "round", "extendMatch", "matchId", "days", "findOne", "getExpiringSoonForUser", "hoursAhead", "futureTime", "populate", "sort", "lean", "map", "id", "targetId", "targetType", "matchType", "hoursUntilExpiry", "scheduleExpirationProcessing", "expirationResult", "getHours", "cleanupResult", "sendExpirationNotifications", "sixHoursFromNow", "expiring<PERSON><PERSON><PERSON>", "$or", "userNotifications", "Map", "for<PERSON>ach", "toString", "has", "set", "user", "matches", "get", "push", "notification", "size", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\matchExpirationService.ts"], "sourcesContent": ["import { Match } from '../models/Match';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\nexport class MatchExpirationService {\r\n  \r\n  /**\r\n   * Process expired matches and update their status\r\n   */\r\n  static async processExpiredMatches(): Promise<{\r\n    processed: number;\r\n    expired: number;\r\n    extended: number;\r\n    errors: number;\r\n  }> {\r\n    try {\r\n      const now = new Date();\r\n      \r\n      // Find all matches that have expired but are still pending\r\n      const expiredMatches = await Match.find({\r\n        status: 'pending',\r\n        expiresAt: { $lt: now }\r\n      });\r\n\r\n      let expired = 0;\r\n      let extended = 0;\r\n      let errors = 0;\r\n\r\n      for (const match of expiredMatches) {\r\n        try {\r\n          // Check if match should be extended based on activity\r\n          const shouldExtend = await this.shouldExtendMatch(match);\r\n          \r\n          if (shouldExtend) {\r\n            // Extend match by 3 days\r\n            match.extendExpiration(3);\r\n            await match.save();\r\n            extended++;\r\n            \r\n            logger.info(`Extended match ${match._id} for user ${match.userId}`);\r\n          } else {\r\n            // Mark as expired\r\n            match.status = 'expired';\r\n            await match.save();\r\n            expired++;\r\n            \r\n            logger.info(`Expired match ${match._id} for user ${match.userId}`);\r\n          }\r\n        } catch (error) {\r\n          logger.error(`Error processing expired match ${match._id}:`, error);\r\n          errors++;\r\n        }\r\n      }\r\n\r\n      const result = {\r\n        processed: expiredMatches.length,\r\n        expired,\r\n        extended,\r\n        errors\r\n      };\r\n\r\n      logger.info('Match expiration processing completed', result);\r\n      \r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Error processing expired matches:', error);\r\n      throw new AppError('Failed to process expired matches', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if a match should be extended based on user activity\r\n   */\r\n  private static async shouldExtendMatch(match: any): Promise<boolean> {\r\n    // Extend if match has high compatibility (>85%) and was viewed recently\r\n    if (match.compatibilityScore >= 85 && match.viewedAt) {\r\n      const daysSinceViewed = (new Date().getTime() - match.viewedAt.getTime()) / (1000 * 60 * 60 * 24);\r\n      if (daysSinceViewed <= 2) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    // Extend if it's a super high compatibility match (>90%)\r\n    if (match.compatibilityScore >= 90) {\r\n      return true;\r\n    }\r\n\r\n    // Extend if user has been active recently and this is a good match\r\n    if (match.compatibilityScore >= 80 && match.viewCount > 0) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Clean up old expired matches (older than 30 days)\r\n   */\r\n  static async cleanupOldMatches(): Promise<{\r\n    deleted: number;\r\n    errors: number;\r\n  }> {\r\n    try {\r\n      const thirtyDaysAgo = new Date();\r\n      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n      const result = await Match.deleteMany({\r\n        status: 'expired',\r\n        updatedAt: { $lt: thirtyDaysAgo }\r\n      });\r\n\r\n      logger.info(`Cleaned up ${result.deletedCount} old expired matches`);\r\n\r\n      return {\r\n        deleted: result.deletedCount || 0,\r\n        errors: 0\r\n      };\r\n    } catch (error) {\r\n      logger.error('Error cleaning up old matches:', error);\r\n      return {\r\n        deleted: 0,\r\n        errors: 1\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get match expiration statistics\r\n   */\r\n  static async getExpirationStats(): Promise<{\r\n    totalMatches: number;\r\n    pendingMatches: number;\r\n    expiredMatches: number;\r\n    expiringToday: number;\r\n    expiringSoon: number; // within 24 hours\r\n    averageMatchDuration: number; // in hours\r\n  }> {\r\n    try {\r\n      const now = new Date();\r\n      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);\r\n\r\n      const [\r\n        totalMatches,\r\n        pendingMatches,\r\n        expiredMatches,\r\n        expiringToday,\r\n        expiringSoon,\r\n        matchDurations\r\n      ] = await Promise.all([\r\n        Match.countDocuments({}),\r\n        Match.countDocuments({ status: 'pending' }),\r\n        Match.countDocuments({ status: 'expired' }),\r\n        Match.countDocuments({\r\n          status: 'pending',\r\n          expiresAt: {\r\n            $gte: now,\r\n            $lt: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)\r\n          }\r\n        }),\r\n        Match.countDocuments({\r\n          status: 'pending',\r\n          expiresAt: {\r\n            $gte: now,\r\n            $lt: tomorrow\r\n          }\r\n        }),\r\n        Match.aggregate([\r\n          {\r\n            $match: {\r\n              status: { $in: ['matched', 'expired', 'rejected'] },\r\n              createdAt: { $exists: true },\r\n              lastInteractionAt: { $exists: true }\r\n            }\r\n          },\r\n          {\r\n            $project: {\r\n              duration: {\r\n                $divide: [\r\n                  { $subtract: ['$lastInteractionAt', '$createdAt'] },\r\n                  1000 * 60 * 60 // Convert to hours\r\n                ]\r\n              }\r\n            }\r\n          },\r\n          {\r\n            $group: {\r\n              _id: null,\r\n              averageDuration: { $avg: '$duration' }\r\n            }\r\n          }\r\n        ])\r\n      ]);\r\n\r\n      const averageMatchDuration = matchDurations.length > 0 \r\n        ? Math.round(matchDurations[0].averageDuration * 100) / 100 \r\n        : 0;\r\n\r\n      return {\r\n        totalMatches,\r\n        pendingMatches,\r\n        expiredMatches,\r\n        expiringToday,\r\n        expiringSoon,\r\n        averageMatchDuration\r\n      };\r\n    } catch (error) {\r\n      logger.error('Error getting expiration stats:', error);\r\n      throw new AppError('Failed to get expiration statistics', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extend a specific match\r\n   */\r\n  static async extendMatch(\r\n    matchId: string,\r\n    userId: string,\r\n    days: number = 7\r\n  ): Promise<any> {\r\n    try {\r\n      if (days < 1 || days > 30) {\r\n        throw new AppError('Extension days must be between 1 and 30', 400);\r\n      }\r\n\r\n      const match = await Match.findOne({\r\n        _id: matchId,\r\n        userId,\r\n        status: { $in: ['pending', 'expired'] }\r\n      });\r\n\r\n      if (!match) {\r\n        throw new AppError('Match not found or cannot be extended', 404);\r\n      }\r\n\r\n      // Extend the match\r\n      match.extendExpiration(days);\r\n      \r\n      // If match was expired, reactivate it\r\n      if (match.status === 'expired') {\r\n        match.status = 'pending';\r\n      }\r\n\r\n      await match.save();\r\n\r\n      logger.info(`Extended match ${matchId} by ${days} days for user ${userId}`);\r\n\r\n      return match;\r\n    } catch (error) {\r\n      logger.error('Error extending match:', error);\r\n      throw error instanceof AppError ? error : new AppError('Failed to extend match', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get matches expiring soon for a user\r\n   */\r\n  static async getExpiringSoonForUser(\r\n    userId: string,\r\n    hoursAhead: number = 24\r\n  ): Promise<any[]> {\r\n    try {\r\n      const now = new Date();\r\n      const futureTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);\r\n\r\n      const expiringSoon = await Match.find({\r\n        userId,\r\n        status: 'pending',\r\n        expiresAt: {\r\n          $gte: now,\r\n          $lt: futureTime\r\n        }\r\n      })\r\n      .populate('targetId', 'firstName lastName title propertyType location')\r\n      .sort({ expiresAt: 1 })\r\n      .lean();\r\n\r\n      return expiringSoon.map(match => ({\r\n        id: match._id,\r\n        targetId: match.targetId,\r\n        targetType: match.targetType,\r\n        matchType: match.matchType,\r\n        compatibilityScore: match.compatibilityScore,\r\n        expiresAt: match.expiresAt,\r\n        hoursUntilExpiry: Math.round((match.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60))\r\n      }));\r\n    } catch (error) {\r\n      logger.error('Error getting expiring matches for user:', error);\r\n      throw new AppError('Failed to get expiring matches', 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule automatic match expiration processing\r\n   * This would typically be called by a cron job or scheduler\r\n   */\r\n  static async scheduleExpirationProcessing(): Promise<void> {\r\n    try {\r\n      // Process expired matches\r\n      const expirationResult = await this.processExpiredMatches();\r\n      \r\n      // Clean up old matches (run less frequently)\r\n      const now = new Date();\r\n      if (now.getHours() === 2) { // Run cleanup at 2 AM\r\n        const cleanupResult = await this.cleanupOldMatches();\r\n        logger.info('Scheduled cleanup completed', cleanupResult);\r\n      }\r\n\r\n      logger.info('Scheduled expiration processing completed', expirationResult);\r\n    } catch (error) {\r\n      logger.error('Error in scheduled expiration processing:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send expiration notifications (placeholder for notification service)\r\n   */\r\n  static async sendExpirationNotifications(): Promise<void> {\r\n    try {\r\n      // Get all users with matches expiring in the next 6 hours\r\n      const sixHoursFromNow = new Date(Date.now() + 6 * 60 * 60 * 1000);\r\n      \r\n      const expiringMatches = await Match.find({\r\n        status: 'pending',\r\n        expiresAt: {\r\n          $gte: new Date(),\r\n          $lt: sixHoursFromNow\r\n        },\r\n        // Only send notification if match hasn't been viewed recently\r\n        $or: [\r\n          { viewedAt: { $exists: false } },\r\n          { viewedAt: { $lt: new Date(Date.now() - 2 * 60 * 60 * 1000) } } // Not viewed in last 2 hours\r\n        ]\r\n      })\r\n      .populate('userId', 'email firstName')\r\n      .populate('targetId', 'firstName title')\r\n      .lean();\r\n\r\n      // Group by user\r\n      const userNotifications = new Map();\r\n      \r\n      expiringMatches.forEach(match => {\r\n        const userId = match.userId._id.toString();\r\n        if (!userNotifications.has(userId)) {\r\n          userNotifications.set(userId, {\r\n            user: match.userId,\r\n            matches: []\r\n          });\r\n        }\r\n        userNotifications.get(userId).matches.push(match);\r\n      });\r\n\r\n      // Send notifications (this would integrate with your notification service)\r\n      for (const [userId, notification] of userNotifications) {\r\n        logger.info(`Would send expiration notification to user ${userId} for ${notification.matches.length} matches`);\r\n        // TODO: Integrate with notification service\r\n        // await NotificationService.sendMatchExpirationNotification(notification.user, notification.matches);\r\n      }\r\n\r\n      logger.info(`Processed expiration notifications for ${userNotifications.size} users`);\r\n    } catch (error) {\r\n      logger.error('Error sending expiration notifications:', error);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,OAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAE,UAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAaG,sBAAsB;EAEjC;;;EAGA,aAAaC,qBAAqBA,CAAA;IAAA;IAAAN,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAMhC,IAAI;MACF,MAAMO,GAAG;MAAA;MAAA,CAAAR,cAAA,GAAAC,CAAA,OAAG,IAAIQ,IAAI,EAAE;MAEtB;MACA,MAAMC,cAAc;MAAA;MAAA,CAAAV,cAAA,GAAAC,CAAA,OAAG,MAAMF,OAAA,CAAAY,KAAK,CAACC,IAAI,CAAC;QACtCC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;UAAEC,GAAG,EAAEP;QAAG;OACtB,CAAC;MAEF,IAAIQ,OAAO;MAAA;MAAA,CAAAhB,cAAA,GAAAC,CAAA,OAAG,CAAC;MACf,IAAIgB,QAAQ;MAAA;MAAA,CAAAjB,cAAA,GAAAC,CAAA,OAAG,CAAC;MAChB,IAAIiB,MAAM;MAAA;MAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAG,CAAC;MAAC;MAAAD,cAAA,GAAAC,CAAA;MAEf,KAAK,MAAMkB,KAAK,IAAIT,cAAc,EAAE;QAAA;QAAAV,cAAA,GAAAC,CAAA;QAClC,IAAI;UACF;UACA,MAAMmB,YAAY;UAAA;UAAA,CAAApB,cAAA,GAAAC,CAAA,QAAG,MAAM,IAAI,CAACoB,iBAAiB,CAACF,KAAK,CAAC;UAAC;UAAAnB,cAAA,GAAAC,CAAA;UAEzD,IAAImB,YAAY,EAAE;YAAA;YAAApB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAC,CAAA;YAChB;YACAkB,KAAK,CAACI,gBAAgB,CAAC,CAAC,CAAC;YAAC;YAAAvB,cAAA,GAAAC,CAAA;YAC1B,MAAMkB,KAAK,CAACK,IAAI,EAAE;YAAC;YAAAxB,cAAA,GAAAC,CAAA;YACnBgB,QAAQ,EAAE;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAEXE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,kBAAkBP,KAAK,CAACQ,GAAG,aAAaR,KAAK,CAACS,MAAM,EAAE,CAAC;UACrE,CAAC,MAAM;YAAA;YAAA5B,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAC,CAAA;YACL;YACAkB,KAAK,CAACN,MAAM,GAAG,SAAS;YAAC;YAAAb,cAAA,GAAAC,CAAA;YACzB,MAAMkB,KAAK,CAACK,IAAI,EAAE;YAAC;YAAAxB,cAAA,GAAAC,CAAA;YACnBe,OAAO,EAAE;YAAC;YAAAhB,cAAA,GAAAC,CAAA;YAEVE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,iBAAiBP,KAAK,CAACQ,GAAG,aAAaR,KAAK,CAACS,MAAM,EAAE,CAAC;UACpE;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA;UAAA7B,cAAA,GAAAC,CAAA;UACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,kCAAkCV,KAAK,CAACQ,GAAG,GAAG,EAAEE,KAAK,CAAC;UAAC;UAAA7B,cAAA,GAAAC,CAAA;UACpEiB,MAAM,EAAE;QACV;MACF;MAEA,MAAMY,MAAM;MAAA;MAAA,CAAA9B,cAAA,GAAAC,CAAA,QAAG;QACb8B,SAAS,EAAErB,cAAc,CAACsB,MAAM;QAChChB,OAAO;QACPC,QAAQ;QACRC;OACD;MAAC;MAAAlB,cAAA,GAAAC,CAAA;MAEFE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,uCAAuC,EAAEI,MAAM,CAAC;MAAC;MAAA9B,cAAA,GAAAC,CAAA;MAE7D,OAAO6B,MAAM;IACf,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAC,CAAA;MACzD,MAAM,IAAIG,UAAA,CAAA6B,QAAQ,CAAC,mCAAmC,EAAE,GAAG,CAAC;IAC9D;EACF;EAEA;;;EAGQ,aAAaZ,iBAAiBA,CAACF,KAAU;IAAA;IAAAnB,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAC/C;IACA;IAAI;IAAA,CAAAD,cAAA,GAAAsB,CAAA,UAAAH,KAAK,CAACe,kBAAkB,IAAI,EAAE;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAIH,KAAK,CAACgB,QAAQ,GAAE;MAAA;MAAAnC,cAAA,GAAAsB,CAAA;MACpD,MAAMc,eAAe;MAAA;MAAA,CAAApC,cAAA,GAAAC,CAAA,QAAG,CAAC,IAAIQ,IAAI,EAAE,CAAC4B,OAAO,EAAE,GAAGlB,KAAK,CAACgB,QAAQ,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MAAC;MAAArC,cAAA,GAAAC,CAAA;MAClG,IAAImC,eAAe,IAAI,CAAC,EAAE;QAAA;QAAApC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QACxB,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAD,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAC,CAAA;IACA,IAAIkB,KAAK,CAACe,kBAAkB,IAAI,EAAE,EAAE;MAAA;MAAAlC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MAClC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,cAAA,GAAAsB,CAAA;IAAA;IAED;IAAAtB,cAAA,GAAAC,CAAA;IACA;IAAI;IAAA,CAAAD,cAAA,GAAAsB,CAAA,UAAAH,KAAK,CAACe,kBAAkB,IAAI,EAAE;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAIH,KAAK,CAACmB,SAAS,GAAG,CAAC,GAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAC,CAAA;MACzD,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAC,CAAA;IAED,OAAO,KAAK;EACd;EAEA;;;EAGA,aAAasC,iBAAiBA,CAAA;IAAA;IAAAvC,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAI5B,IAAI;MACF,MAAMuC,aAAa;MAAA;MAAA,CAAAxC,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,EAAE;MAAC;MAAAT,cAAA,GAAAC,CAAA;MACjCuC,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,EAAE,GAAG,EAAE,CAAC;MAEnD,MAAMZ,MAAM;MAAA;MAAA,CAAA9B,cAAA,GAAAC,CAAA,QAAG,MAAMF,OAAA,CAAAY,KAAK,CAACgC,UAAU,CAAC;QACpC9B,MAAM,EAAE,SAAS;QACjB+B,SAAS,EAAE;UAAE7B,GAAG,EAAEyB;QAAa;OAChC,CAAC;MAAC;MAAAxC,cAAA,GAAAC,CAAA;MAEHE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,cAAcI,MAAM,CAACe,YAAY,sBAAsB,CAAC;MAAC;MAAA7C,cAAA,GAAAC,CAAA;MAErE,OAAO;QACL6C,OAAO;QAAE;QAAA,CAAA9C,cAAA,GAAAsB,CAAA,UAAAQ,MAAM,CAACe,YAAY;QAAA;QAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAI,CAAC;QACjCJ,MAAM,EAAE;OACT;IACH,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAC,CAAA;MACtD,OAAO;QACL6C,OAAO,EAAE,CAAC;QACV5B,MAAM,EAAE;OACT;IACH;EACF;EAEA;;;EAGA,aAAa6B,kBAAkBA,CAAA;IAAA;IAAA/C,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAQ7B,IAAI;MACF,MAAMO,GAAG;MAAA;MAAA,CAAAR,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,EAAE;MACtB,MAAMuC,QAAQ;MAAA;MAAA,CAAAhD,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,CAACD,GAAG,CAAC6B,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAE9D,MAAM,CACJY,YAAY,EACZC,cAAc,EACdxC,cAAc,EACdyC,aAAa,EACbC,YAAY,EACZC,cAAc,CACf;MAAA;MAAA,CAAArD,cAAA,GAAAC,CAAA,QAAG,MAAMqD,OAAO,CAACC,GAAG,CAAC,CACpBxD,OAAA,CAAAY,KAAK,CAAC6C,cAAc,CAAC,EAAE,CAAC,EACxBzD,OAAA,CAAAY,KAAK,CAAC6C,cAAc,CAAC;QAAE3C,MAAM,EAAE;MAAS,CAAE,CAAC,EAC3Cd,OAAA,CAAAY,KAAK,CAAC6C,cAAc,CAAC;QAAE3C,MAAM,EAAE;MAAS,CAAE,CAAC,EAC3Cd,OAAA,CAAAY,KAAK,CAAC6C,cAAc,CAAC;QACnB3C,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;UACT2C,IAAI,EAAEjD,GAAG;UACTO,GAAG,EAAE,IAAIN,IAAI,CAACD,GAAG,CAACkD,WAAW,EAAE,EAAElD,GAAG,CAACmD,QAAQ,EAAE,EAAEnD,GAAG,CAACkC,OAAO,EAAE,GAAG,CAAC;;OAErE,CAAC,EACF3C,OAAA,CAAAY,KAAK,CAAC6C,cAAc,CAAC;QACnB3C,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;UACT2C,IAAI,EAAEjD,GAAG;UACTO,GAAG,EAAEiC;;OAER,CAAC,EACFjD,OAAA,CAAAY,KAAK,CAACiD,SAAS,CAAC,CACd;QACEC,MAAM,EAAE;UACNhD,MAAM,EAAE;YAAEiD,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU;UAAC,CAAE;UACnDC,SAAS,EAAE;YAAEC,OAAO,EAAE;UAAI,CAAE;UAC5BC,iBAAiB,EAAE;YAAED,OAAO,EAAE;UAAI;;OAErC,EACD;QACEE,QAAQ,EAAE;UACRC,QAAQ,EAAE;YACRC,OAAO,EAAE,CACP;cAAEC,SAAS,EAAE,CAAC,oBAAoB,EAAE,YAAY;YAAC,CAAE,EACnD,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;YAAA;;;OAItB,EACD;QACEC,MAAM,EAAE;UACN3C,GAAG,EAAE,IAAI;UACT4C,eAAe,EAAE;YAAEC,IAAI,EAAE;UAAW;;OAEvC,CACF,CAAC,CACH,CAAC;MAEF,MAAMC,oBAAoB;MAAA;MAAA,CAAAzE,cAAA,GAAAC,CAAA,QAAGoD,cAAc,CAACrB,MAAM,GAAG,CAAC;MAAA;MAAA,CAAAhC,cAAA,GAAAsB,CAAA,UAClDoD,IAAI,CAACC,KAAK,CAACtB,cAAc,CAAC,CAAC,CAAC,CAACkB,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;MAAA;MAAA,CAAAvE,cAAA,GAAAsB,CAAA,UACzD,CAAC;MAAC;MAAAtB,cAAA,GAAAC,CAAA;MAEN,OAAO;QACLgD,YAAY;QACZC,cAAc;QACdxC,cAAc;QACdyC,aAAa;QACbC,YAAY;QACZqB;OACD;IACH,CAAC,CAAC,OAAO5C,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAC,CAAA;MACvD,MAAM,IAAIG,UAAA,CAAA6B,QAAQ,CAAC,qCAAqC,EAAE,GAAG,CAAC;IAChE;EACF;EAEA;;;EAGA,aAAa2C,WAAWA,CACtBC,OAAe,EACfjD,MAAc,EACdkD,IAAA;EAAA;EAAA,CAAA9E,cAAA,GAAAsB,CAAA,UAAe,CAAC;IAAA;IAAAtB,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAEhB,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF;MAAI;MAAA,CAAAD,cAAA,GAAAsB,CAAA,WAAAwD,IAAI,GAAG,CAAC;MAAA;MAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAIwD,IAAI,GAAG,EAAE,GAAE;QAAA;QAAA9E,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QACzB,MAAM,IAAIG,UAAA,CAAA6B,QAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC;MACpE,CAAC;MAAA;MAAA;QAAAjC,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMH,KAAK;MAAA;MAAA,CAAAnB,cAAA,GAAAC,CAAA,QAAG,MAAMF,OAAA,CAAAY,KAAK,CAACoE,OAAO,CAAC;QAChCpD,GAAG,EAAEkD,OAAO;QACZjD,MAAM;QACNf,MAAM,EAAE;UAAEiD,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS;QAAC;OACtC,CAAC;MAAC;MAAA9D,cAAA,GAAAC,CAAA;MAEH,IAAI,CAACkB,KAAK,EAAE;QAAA;QAAAnB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QACV,MAAM,IAAIG,UAAA,CAAA6B,QAAQ,CAAC,uCAAuC,EAAE,GAAG,CAAC;MAClE,CAAC;MAAA;MAAA;QAAAjC,cAAA,GAAAsB,CAAA;MAAA;MAED;MAAAtB,cAAA,GAAAC,CAAA;MACAkB,KAAK,CAACI,gBAAgB,CAACuD,IAAI,CAAC;MAE5B;MAAA;MAAA9E,cAAA,GAAAC,CAAA;MACA,IAAIkB,KAAK,CAACN,MAAM,KAAK,SAAS,EAAE;QAAA;QAAAb,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAC,CAAA;QAC9BkB,KAAK,CAACN,MAAM,GAAG,SAAS;MAC1B,CAAC;MAAA;MAAA;QAAAb,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAC,CAAA;MAED,MAAMkB,KAAK,CAACK,IAAI,EAAE;MAAC;MAAAxB,cAAA,GAAAC,CAAA;MAEnBE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,kBAAkBmD,OAAO,OAAOC,IAAI,kBAAkBlD,MAAM,EAAE,CAAC;MAAC;MAAA5B,cAAA,GAAAC,CAAA;MAE5E,OAAOkB,KAAK;IACd,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAC,CAAA;MAC9C,MAAM4B,KAAK,YAAYzB,UAAA,CAAA6B,QAAQ;MAAA;MAAA,CAAAjC,cAAA,GAAAsB,CAAA,WAAGO,KAAK;MAAA;MAAA,CAAA7B,cAAA,GAAAsB,CAAA,WAAG,IAAIlB,UAAA,CAAA6B,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;IACvF;EACF;EAEA;;;EAGA,aAAa+C,sBAAsBA,CACjCpD,MAAc,EACdqD,UAAA;EAAA;EAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAAqB,EAAE;IAAA;IAAAtB,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IAEvB,IAAI;MACF,MAAMO,GAAG;MAAA;MAAA,CAAAR,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,EAAE;MACtB,MAAMyE,UAAU;MAAA;MAAA,CAAAlF,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,CAACD,GAAG,CAAC6B,OAAO,EAAE,GAAG4C,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAExE,MAAM7B,YAAY;MAAA;MAAA,CAAApD,cAAA,GAAAC,CAAA,QAAG,MAAMF,OAAA,CAAAY,KAAK,CAACC,IAAI,CAAC;QACpCgB,MAAM;QACNf,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;UACT2C,IAAI,EAAEjD,GAAG;UACTO,GAAG,EAAEmE;;OAER,CAAC,CACDC,QAAQ,CAAC,UAAU,EAAE,gDAAgD,CAAC,CACtEC,IAAI,CAAC;QAAEtE,SAAS,EAAE;MAAC,CAAE,CAAC,CACtBuE,IAAI,EAAE;MAAC;MAAArF,cAAA,GAAAC,CAAA;MAER,OAAOmD,YAAY,CAACkC,GAAG,CAACnE,KAAK,IAAK;QAAA;QAAAnB,cAAA,GAAAO,CAAA;QAAAP,cAAA,GAAAC,CAAA;QAAA;UAChCsF,EAAE,EAAEpE,KAAK,CAACQ,GAAG;UACb6D,QAAQ,EAAErE,KAAK,CAACqE,QAAQ;UACxBC,UAAU,EAAEtE,KAAK,CAACsE,UAAU;UAC5BC,SAAS,EAAEvE,KAAK,CAACuE,SAAS;UAC1BxD,kBAAkB,EAAEf,KAAK,CAACe,kBAAkB;UAC5CpB,SAAS,EAAEK,KAAK,CAACL,SAAS;UAC1B6E,gBAAgB,EAAEjB,IAAI,CAACC,KAAK,CAAC,CAACxD,KAAK,CAACL,SAAS,CAACuB,OAAO,EAAE,GAAG7B,GAAG,CAAC6B,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;SAC5F;OAAC,CAAC;IACL,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAC,CAAA;MAChE,MAAM,IAAIG,UAAA,CAAA6B,QAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC;IAC3D;EACF;EAEA;;;;EAIA,aAAa2D,4BAA4BA,CAAA;IAAA;IAAA5F,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IACvC,IAAI;MACF;MACA,MAAM4F,gBAAgB;MAAA;MAAA,CAAA7F,cAAA,GAAAC,CAAA,QAAG,MAAM,IAAI,CAACK,qBAAqB,EAAE;MAE3D;MACA,MAAME,GAAG;MAAA;MAAA,CAAAR,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,EAAE;MAAC;MAAAT,cAAA,GAAAC,CAAA;MACvB,IAAIO,GAAG,CAACsF,QAAQ,EAAE,KAAK,CAAC,EAAE;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAE;QAC1B,MAAMyE,aAAa;QAAA;QAAA,CAAA/F,cAAA,GAAAC,CAAA,QAAG,MAAM,IAAI,CAACsC,iBAAiB,EAAE;QAAC;QAAAvC,cAAA,GAAAC,CAAA;QACrDE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,6BAA6B,EAAEqE,aAAa,CAAC;MAC3D,CAAC;MAAA;MAAA;QAAA/F,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAC,CAAA;MAEDE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,2CAA2C,EAAEmE,gBAAgB,CAAC;IAC5E,CAAC,CAAC,OAAOhE,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IAClE;EACF;EAEA;;;EAGA,aAAamE,2BAA2BA,CAAA;IAAA;IAAAhG,cAAA,GAAAO,CAAA;IAAAP,cAAA,GAAAC,CAAA;IACtC,IAAI;MACF;MACA,MAAMgG,eAAe;MAAA;MAAA,CAAAjG,cAAA,GAAAC,CAAA,QAAG,IAAIQ,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAEjE,MAAM0F,eAAe;MAAA;MAAA,CAAAlG,cAAA,GAAAC,CAAA,QAAG,MAAMF,OAAA,CAAAY,KAAK,CAACC,IAAI,CAAC;QACvCC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;UACT2C,IAAI,EAAE,IAAIhD,IAAI,EAAE;UAChBM,GAAG,EAAEkF;SACN;QACD;QACAE,GAAG,EAAE,CACH;UAAEhE,QAAQ,EAAE;YAAE6B,OAAO,EAAE;UAAK;QAAE,CAAE,EAChC;UAAE7B,QAAQ,EAAE;YAAEpB,GAAG,EAAE,IAAIN,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;UAAC;QAAE,CAAE,CAAC;QAAA;OAEpE,CAAC,CACD2E,QAAQ,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CACrCA,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC,CACvCE,IAAI,EAAE;MAEP;MACA,MAAMe,iBAAiB;MAAA;MAAA,CAAApG,cAAA,GAAAC,CAAA,QAAG,IAAIoG,GAAG,EAAE;MAAC;MAAArG,cAAA,GAAAC,CAAA;MAEpCiG,eAAe,CAACI,OAAO,CAACnF,KAAK,IAAG;QAAA;QAAAnB,cAAA,GAAAO,CAAA;QAC9B,MAAMqB,MAAM;QAAA;QAAA,CAAA5B,cAAA,GAAAC,CAAA,QAAGkB,KAAK,CAACS,MAAM,CAACD,GAAG,CAAC4E,QAAQ,EAAE;QAAC;QAAAvG,cAAA,GAAAC,CAAA;QAC3C,IAAI,CAACmG,iBAAiB,CAACI,GAAG,CAAC5E,MAAM,CAAC,EAAE;UAAA;UAAA5B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAC,CAAA;UAClCmG,iBAAiB,CAACK,GAAG,CAAC7E,MAAM,EAAE;YAC5B8E,IAAI,EAAEvF,KAAK,CAACS,MAAM;YAClB+E,OAAO,EAAE;WACV,CAAC;QACJ,CAAC;QAAA;QAAA;UAAA3G,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAC,CAAA;QACDmG,iBAAiB,CAACQ,GAAG,CAAChF,MAAM,CAAC,CAAC+E,OAAO,CAACE,IAAI,CAAC1F,KAAK,CAAC;MACnD,CAAC,CAAC;MAEF;MAAA;MAAAnB,cAAA,GAAAC,CAAA;MACA,KAAK,MAAM,CAAC2B,MAAM,EAAEkF,YAAY,CAAC,IAAIV,iBAAiB,EAAE;QAAA;QAAApG,cAAA,GAAAC,CAAA;QACtDE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,8CAA8CE,MAAM,QAAQkF,YAAY,CAACH,OAAO,CAAC3E,MAAM,UAAU,CAAC;QAC9G;QACA;MACF;MAAC;MAAAhC,cAAA,GAAAC,CAAA;MAEDE,QAAA,CAAAsB,MAAM,CAACC,IAAI,CAAC,0CAA0C0E,iBAAiB,CAACW,IAAI,QAAQ,CAAC;IACvF,CAAC,CAAC,OAAOlF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAC,CAAA;MACdE,QAAA,CAAAsB,MAAM,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAChE;EACF;;AACD;AAAA7B,cAAA,GAAAC,CAAA;AAvWD+G,OAAA,CAAA3G,sBAAA,GAAAA,sBAAA", "ignoreList": []}