{"version": 3, "names": ["cov_q7nj6mqkg", "actualCoverage", "s", "express_1", "require", "auth_1", "auth_validators_1", "auth_validators_2", "auth_controller_1", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "endpoints", "register", "login", "refresh", "logout", "profile", "post", "validateRequest", "registerSchema", "loginSchema", "refreshTokenSchema", "refreshToken", "forgotPasswordSchema", "forgotPassword", "resetPasswordSchema", "resetPassword", "verifyEmailSchema", "verifyEmail", "resendVerificationSchema", "sendEmailVerification", "use", "authenticate", "logoutAll", "changePasswordSchema", "changePassword", "getProfile", "patch", "updateProfileSchema", "updateProfile", "delete", "deactivateAccount", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\auth.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest } from '../validators/auth.validators';\r\nimport {\r\n  registerSchema,\r\n  loginSchema,\r\n  forgotPasswordSchema,\r\n  resetPasswordSchema,\r\n  changePasswordSchema,\r\n  verifyEmailSchema,\r\n  resendVerificationSchema,\r\n  refreshTokenSchema,\r\n  updateProfileSchema\r\n} from '../validators/auth.validators';\r\nimport {\r\n  register,\r\n  login,\r\n  refreshToken,\r\n  logout,\r\n  logoutAll,\r\n  sendEmailVerification,\r\n  verifyEmail,\r\n  forgotPassword,\r\n  resetPassword,\r\n  changePassword,\r\n  getProfile,\r\n  updateProfile,\r\n  deactivateAccount\r\n} from '../controllers/auth.controller';\r\n\r\nconst router = Router();\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({\r\n    message: 'Auth routes working',\r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      register: 'POST /register',\r\n      login: 'POST /login',\r\n      refresh: 'POST /refresh',\r\n      logout: 'POST /logout',\r\n      profile: 'GET /profile'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes (no authentication required) - temporarily without rate limiting\r\nrouter.post('/register',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(registerSchema),\r\n  register\r\n);\r\n\r\nrouter.post('/login',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(loginSchema),\r\n  login\r\n);\r\n\r\nrouter.post('/refresh',\r\n  // authRateLimit(10, 15 * 60 * 1000), // 10 attempts per 15 minutes\r\n  validateRequest(refreshTokenSchema),\r\n  refreshToken\r\n);\r\n\r\nrouter.post('/forgot-password',\r\n  // authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes\r\n  validateRequest(forgotPasswordSchema),\r\n  forgotPassword\r\n);\r\n\r\nrouter.post('/reset-password',\r\n  // authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes\r\n  validateRequest(resetPasswordSchema),\r\n  resetPassword\r\n);\r\n\r\nrouter.post('/verify-email',\r\n  validateRequest(verifyEmailSchema),\r\n  verifyEmail\r\n);\r\n\r\nrouter.post('/resend-verification',\r\n  // authRateLimit(3, 15 * 60 * 1000), // 3 attempts per 15 minutes\r\n  validateRequest(resendVerificationSchema),\r\n  sendEmailVerification\r\n);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate); // All routes below require authentication\r\n\r\nrouter.post('/logout', logout);\r\n\r\nrouter.post('/logout-all', logoutAll);\r\n\r\nrouter.post('/change-password',\r\n  validateRequest(changePasswordSchema),\r\n  changePassword\r\n);\r\n\r\nrouter.get('/profile', getProfile);\r\n\r\nrouter.patch('/profile',\r\n  validateRequest(updateProfileSchema),\r\n  updateProfile\r\n);\r\n\r\nrouter.delete('/deactivate', deactivateAccount);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AAvCN,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,iBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,iBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAWA,MAAAI,iBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAgBA,MAAMK,MAAM;AAAA;AAAA,CAAAT,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAO,MAAM,GAAE;AAEvB;AAAA;AAAAV,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAb,aAAA,GAAAc,CAAA;EAAAd,aAAA,GAAAE,CAAA;EAClCW,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,SAAS,EAAE;MACTC,QAAQ,EAAE,gBAAgB;MAC1BC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAE,cAAc;MACtBC,OAAO,EAAE;;GAEZ,CAAC;AACJ,CAAC,CAAC;AAEF;AAAA;AAAAzB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACiB,IAAI,CAAC,WAAW;AACrB;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAqB,cAAc,CAAC,EAC/BpB,iBAAA,CAAAa,QAAQ,CACT;AAAC;AAAArB,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,QAAQ;AAClB;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAsB,WAAW,CAAC,EAC5BrB,iBAAA,CAAAc,KAAK,CACN;AAAC;AAAAtB,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,UAAU;AACpB;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAuB,kBAAkB,CAAC,EACnCtB,iBAAA,CAAAuB,YAAY,CACb;AAAC;AAAA/B,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,kBAAkB;AAC5B;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAyB,oBAAoB,CAAC,EACrCxB,iBAAA,CAAAyB,cAAc,CACf;AAAC;AAAAjC,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,iBAAiB;AAC3B;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAA2B,mBAAmB,CAAC,EACpC1B,iBAAA,CAAA2B,aAAa,CACd;AAAC;AAAAnC,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,eAAe,EACzB,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAA6B,iBAAiB,CAAC,EAClC5B,iBAAA,CAAA6B,WAAW,CACZ;AAAC;AAAArC,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACiB,IAAI,CAAC,sBAAsB;AAChC;AACA,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAA+B,wBAAwB,CAAC,EACzC9B,iBAAA,CAAA+B,qBAAqB,CACtB;AAED;AAAA;AAAAvC,aAAA,GAAAE,CAAA;AACAO,MAAM,CAAC+B,GAAG,CAACnC,MAAA,CAAAoC,YAAY,CAAC,CAAC,CAAC;AAAA;AAAAzC,aAAA,GAAAE,CAAA;AAE1BO,MAAM,CAACiB,IAAI,CAAC,SAAS,EAAElB,iBAAA,CAAAgB,MAAM,CAAC;AAAC;AAAAxB,aAAA,GAAAE,CAAA;AAE/BO,MAAM,CAACiB,IAAI,CAAC,aAAa,EAAElB,iBAAA,CAAAkC,SAAS,CAAC;AAAC;AAAA1C,aAAA,GAAAE,CAAA;AAEtCO,MAAM,CAACiB,IAAI,CAAC,kBAAkB,EAC5B,IAAApB,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAoC,oBAAoB,CAAC,EACrCnC,iBAAA,CAAAoC,cAAc,CACf;AAAC;AAAA5C,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACE,GAAG,CAAC,UAAU,EAAEH,iBAAA,CAAAqC,UAAU,CAAC;AAAC;AAAA7C,aAAA,GAAAE,CAAA;AAEnCO,MAAM,CAACqC,KAAK,CAAC,UAAU,EACrB,IAAAxC,iBAAA,CAAAqB,eAAe,EAACpB,iBAAA,CAAAwC,mBAAmB,CAAC,EACpCvC,iBAAA,CAAAwC,aAAa,CACd;AAAC;AAAAhD,aAAA,GAAAE,CAAA;AAEFO,MAAM,CAACwC,MAAM,CAAC,aAAa,EAAEzC,iBAAA,CAAA0C,iBAAiB,CAAC;AAAC;AAAAlD,aAAA,GAAAE,CAAA;AAEhDiD,OAAA,CAAAC,OAAA,GAAe3C,MAAM", "ignoreList": []}