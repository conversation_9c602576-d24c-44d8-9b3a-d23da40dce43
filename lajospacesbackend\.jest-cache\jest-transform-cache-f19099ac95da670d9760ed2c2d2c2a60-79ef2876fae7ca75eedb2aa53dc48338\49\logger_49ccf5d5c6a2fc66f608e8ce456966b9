57d933b713048eef09b56713e95d192d
"use strict";

/* istanbul ignore next */
function cov_2owy25toec() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\logger.ts";
  var hash = "296d6d04e848655a186a3147755dcb2c94ca9f16";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\logger.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "4": {
        start: {
          line: 7,
          column: 18
        },
        end: {
          line: 7,
          column: 53
        }
      },
      "5": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "6": {
        start: {
          line: 9,
          column: 22
        },
        end: {
          line: 9,
          column: 54
        }
      },
      "7": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "8": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "9": {
        start: {
          line: 27,
          column: 0
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "10": {
        start: {
          line: 29,
          column: 18
        },
        end: {
          line: 29,
          column: 269
        }
      },
      "11": {
        start: {
          line: 29,
          column: 217
        },
        end: {
          line: 29,
          column: 267
        }
      },
      "12": {
        start: {
          line: 31,
          column: 19
        },
        end: {
          line: 31,
          column: 208
        }
      },
      "13": {
        start: {
          line: 33,
          column: 19
        },
        end: {
          line: 33,
          column: 21
        }
      },
      "14": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 46,
          column: 1
        }
      },
      "15": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 39,
          column: 8
        }
      },
      "16": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 45,
          column: 8
        }
      },
      "17": {
        start: {
          line: 48,
          column: 0
        },
        end: {
          line: 75,
          column: 1
        }
      },
      "18": {
        start: {
          line: 50,
          column: 20
        },
        end: {
          line: 50,
          column: 73
        }
      },
      "19": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 58,
          column: 8
        }
      },
      "20": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 66,
          column: 8
        }
      },
      "21": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 74,
          column: 8
        }
      },
      "22": {
        start: {
          line: 77,
          column: 0
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "23": {
        start: {
          line: 101,
          column: 0
        },
        end: {
          line: 105,
          column: 2
        }
      },
      "24": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 44
        }
      },
      "25": {
        start: {
          line: 107,
          column: 0
        },
        end: {
          line: 193,
          column: 2
        }
      },
      "26": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 117,
          column: 11
        }
      },
      "27": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 129,
          column: 11
        }
      },
      "28": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 140,
          column: 11
        }
      },
      "29": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 152,
          column: 11
        }
      },
      "30": {
        start: {
          line: 158,
          column: 25
        },
        end: {
          line: 158,
          column: 96
        }
      },
      "31": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 164,
          column: 11
        }
      },
      "32": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 176,
          column: 11
        }
      },
      "33": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 191,
          column: 11
        }
      },
      "34": {
        start: {
          line: 194,
          column: 0
        },
        end: {
          line: 194,
          column: 33
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 29,
            column: 207
          },
          end: {
            line: 29,
            column: 208
          }
        },
        loc: {
          start: {
            line: 29,
            column: 217
          },
          end: {
            line: 29,
            column: 267
          }
        },
        line: 29
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 102,
            column: 11
          },
          end: {
            line: 102,
            column: 12
          }
        },
        loc: {
          start: {
            line: 102,
            column: 24
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 102
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 111,
            column: 17
          }
        },
        loc: {
          start: {
            line: 111,
            column: 45
          },
          end: {
            line: 118,
            column: 5
          }
        },
        line: 111
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        },
        loc: {
          start: {
            line: 122,
            column: 45
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 122
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 134,
            column: 17
          },
          end: {
            line: 134,
            column: 18
          }
        },
        loc: {
          start: {
            line: 134,
            column: 53
          },
          end: {
            line: 141,
            column: 5
          }
        },
        line: 134
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 145,
            column: 15
          },
          end: {
            line: 145,
            column: 16
          }
        },
        loc: {
          start: {
            line: 145,
            column: 47
          },
          end: {
            line: 153,
            column: 5
          }
        },
        line: 145
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 157,
            column: 19
          },
          end: {
            line: 157,
            column: 20
          }
        },
        loc: {
          start: {
            line: 157,
            column: 49
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 157
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 169,
            column: 17
          },
          end: {
            line: 169,
            column: 18
          }
        },
        loc: {
          start: {
            line: 169,
            column: 51
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 169
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 181,
            column: 22
          },
          end: {
            line: 181,
            column: 23
          }
        },
        loc: {
          start: {
            line: 181,
            column: 51
          },
          end: {
            line: 192,
            column: 5
          }
        },
        line: 181
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 35,
            column: 0
          },
          end: {
            line: 46,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 0
          },
          end: {
            line: 46,
            column: 1
          }
        }, {
          start: {
            line: 41,
            column: 5
          },
          end: {
            line: 46,
            column: 1
          }
        }],
        line: 35
      },
      "4": {
        loc: {
          start: {
            line: 48,
            column: 0
          },
          end: {
            line: 75,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 0
          },
          end: {
            line: 75,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "5": {
        loc: {
          start: {
            line: 158,
            column: 25
          },
          end: {
            line: 158,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 158,
            column: 47
          },
          end: {
            line: 158,
            column: 54
          }
        }, {
          start: {
            line: 158,
            column: 57
          },
          end: {
            line: 158,
            column: 96
          }
        }],
        line: 158
      },
      "6": {
        loc: {
          start: {
            line: 158,
            column: 57
          },
          end: {
            line: 158,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 158,
            column: 81
          },
          end: {
            line: 158,
            column: 87
          }
        }, {
          start: {
            line: 158,
            column: 90
          },
          end: {
            line: 158,
            column: 96
          }
        }],
        line: 158
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\logger.ts",
      mappings: ";;;;;;AAAA,sDAA8B;AAC9B,gDAAwB;AACxB,uDAA+C;AAE/C,oBAAoB;AACpB,MAAM,SAAS,GAAG;IAChB,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACT,CAAC;AAEF,oBAAoB;AACpB,MAAM,SAAS,GAAG;IAChB,KAAK,EAAE,KAAK;IACZ,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,OAAO;CACf,CAAC;AAEF,wBAAwB;AACxB,iBAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAE7B,oBAAoB;AACpB,MAAM,SAAS,GAAG,iBAAO,CAAC,MAAM,CAAC,OAAO,CACtC,iBAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAC,EAC9D,iBAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EACtC,iBAAO,CAAC,MAAM,CAAC,MAAM,CACnB,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE,CAC7D,CACF,CAAC;AAEF,sCAAsC;AACtC,MAAM,UAAU,GAAG,iBAAO,CAAC,MAAM,CAAC,OAAO,CACvC,iBAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAC,EAC9D,iBAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB,CAAC;AAEF,oBAAoB;AACpB,MAAM,UAAU,GAAwB,EAAE,CAAC;AAE3C,oDAAoD;AACpD,IAAI,oBAAM,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IACtC,UAAU,CAAC,IAAI,CACb,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC7B,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;AACJ,CAAC;KAAM,CAAC;IACN,UAAU,CAAC,IAAI,CACb,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC7B,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,oBAAM,CAAC,SAAS;KACxB,CAAC,CACH,CAAC;AACJ,CAAC;AAED,mDAAmD;AACnD,IAAI,oBAAM,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;IAC/B,+BAA+B;IAC/B,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,oBAAM,CAAC,QAAQ,CAAC,CAAC;IAE9C,gBAAgB;IAChB,UAAU,CAAC,IAAI,CACb,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QAC1B,QAAQ,EAAE,oBAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,UAAU;QAClB,KAAK,EAAE,oBAAM,CAAC,SAAS;QACvB,OAAO,EAAE,OAAO,EAAE,MAAM;QACxB,QAAQ,EAAE,CAAC;KACZ,CAAC,CACH,CAAC;IAEF,aAAa;IACb,UAAU,CAAC,IAAI,CACb,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QAC1B,QAAQ,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC;QACzC,MAAM,EAAE,UAAU;QAClB,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,OAAO,EAAE,MAAM;QACxB,QAAQ,EAAE,CAAC;KACZ,CAAC,CACH,CAAC;IAEF,YAAY;IACZ,UAAU,CAAC,IAAI,CACb,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QAC1B,QAAQ,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC;QACxC,MAAM,EAAE,UAAU;QAClB,KAAK,EAAE,MAAM;QACb,OAAO,EAAE,OAAO,EAAE,MAAM;QACxB,QAAQ,EAAE,CAAC;KACZ,CAAC,CACH,CAAC;AACJ,CAAC;AAED,yBAAyB;AACZ,QAAA,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;IACzC,KAAK,EAAE,oBAAM,CAAC,SAAS;IACvB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,UAAU;IAClB,UAAU;IACV,iBAAiB,EAAE;QACjB,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,oBAAM,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;YACpE,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;SACZ,CAAC;KACH;IACD,iBAAiB,EAAE;QACjB,IAAI,iBAAO,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,oBAAM,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;YACpE,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,OAAO,EAAE,MAAM;YACxB,QAAQ,EAAE,CAAC;SACZ,CAAC;KACH;IACD,WAAW,EAAE,KAAK;CACnB,CAAC,CAAC;AAEH,iDAAiD;AACpC,QAAA,YAAY,GAAG;IAC1B,KAAK,EAAE,CAAC,OAAe,EAAE,EAAE;QACzB,cAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF,CAAC;AAEF,0CAA0C;AAC7B,QAAA,UAAU,GAAG;IACxB;;OAEG;IACH,UAAU,EAAE,CAAC,MAAc,EAAE,MAAc,EAAE,OAAa,EAAE,EAAE;QAC5D,cAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,EAAE,EAAE;YACpC,MAAM;YACN,MAAM;YACN,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,EAAE,CAAC,MAAc,EAAE,GAAW,EAAE,MAAe,EAAE,EAAW,EAAE,EAAE;QACxE,cAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,IAAI,GAAG,EAAE,EAAE;YAC3C,MAAM;YACN,GAAG;YACH,MAAM;YACN,EAAE;YACF,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,EAAE,CAAC,SAAiB,EAAE,UAAkB,EAAE,OAAa,EAAE,EAAE;QACpE,cAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,OAAO,UAAU,EAAE,EAAE;YAC1D,SAAS;YACT,UAAU;YACV,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,EAAE,CAAC,KAAa,EAAE,MAAe,EAAE,EAAW,EAAE,OAAa,EAAE,EAAE;QACxE,cAAM,CAAC,IAAI,CAAC,eAAe,KAAK,EAAE,EAAE;YAClC,KAAK;YACL,MAAM;YACN,EAAE;YACF,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,EAAE,CAAC,KAAa,EAAE,QAAmC,EAAE,OAAa,EAAE,EAAE;QACnF,MAAM,QAAQ,GAAG,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACzF,cAAM,CAAC,QAAQ,CAAC,CAAC,mBAAmB,KAAK,EAAE,EAAE;YAC3C,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,EAAE,CAAC,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,OAAa,EAAE,EAAE;QAC1E,cAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,MAAM,KAAK,GAAG,IAAI,EAAE,EAAE;YACtD,MAAM;YACN,KAAK;YACL,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,EAAE,CAAC,KAAY,EAAE,OAAe,EAAE,OAAa,EAAE,EAAE;QACjE,cAAM,CAAC,KAAK,CAAC,YAAY,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;YACpD,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;YACD,OAAO;YACP,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,kBAAe,cAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\logger.ts"],
      sourcesContent: ["import winston from 'winston';\r\nimport path from 'path';\r\nimport { config } from '../config/environment';\r\n\r\n// Define log levels\r\nconst logLevels = {\r\n  error: 0,\r\n  warn: 1,\r\n  info: 2,\r\n  http: 3,\r\n  debug: 4,\r\n};\r\n\r\n// Define log colors\r\nconst logColors = {\r\n  error: 'red',\r\n  warn: 'yellow',\r\n  info: 'green',\r\n  http: 'magenta',\r\n  debug: 'white',\r\n};\r\n\r\n// Add colors to winston\r\nwinston.addColors(logColors);\r\n\r\n// Create log format\r\nconst logFormat = winston.format.combine(\r\n  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),\r\n  winston.format.colorize({ all: true }),\r\n  winston.format.printf(\r\n    (info) => `${info.timestamp} ${info.level}: ${info.message}`\r\n  )\r\n);\r\n\r\n// Create file format (without colors)\r\nconst fileFormat = winston.format.combine(\r\n  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),\r\n  winston.format.errors({ stack: true }),\r\n  winston.format.json()\r\n);\r\n\r\n// Define transports\r\nconst transports: winston.transport[] = [];\r\n\r\n// Console transport (always enabled in development)\r\nif (config.NODE_ENV === 'development') {\r\n  transports.push(\r\n    new winston.transports.Console({\r\n      format: logFormat,\r\n      level: 'debug'\r\n    })\r\n  );\r\n} else {\r\n  transports.push(\r\n    new winston.transports.Console({\r\n      format: logFormat,\r\n      level: config.LOG_LEVEL\r\n    })\r\n  );\r\n}\r\n\r\n// File transports (for production and development)\r\nif (config.NODE_ENV !== 'test') {\r\n  // Ensure logs directory exists\r\n  const logsDir = path.dirname(config.LOG_FILE);\r\n  \r\n  // Combined logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: config.LOG_FILE,\r\n      format: fileFormat,\r\n      level: config.LOG_LEVEL,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 5,\r\n    })\r\n  );\r\n\r\n  // Error logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: path.join(logsDir, 'error.log'),\r\n      format: fileFormat,\r\n      level: 'error',\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 5,\r\n    })\r\n  );\r\n\r\n  // HTTP logs\r\n  transports.push(\r\n    new winston.transports.File({\r\n      filename: path.join(logsDir, 'http.log'),\r\n      format: fileFormat,\r\n      level: 'http',\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 3,\r\n    })\r\n  );\r\n}\r\n\r\n// Create logger instance\r\nexport const logger = winston.createLogger({\r\n  level: config.LOG_LEVEL,\r\n  levels: logLevels,\r\n  format: fileFormat,\r\n  transports,\r\n  exceptionHandlers: [\r\n    new winston.transports.File({\r\n      filename: path.join(path.dirname(config.LOG_FILE), 'exceptions.log'),\r\n      format: fileFormat,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 2,\r\n    })\r\n  ],\r\n  rejectionHandlers: [\r\n    new winston.transports.File({\r\n      filename: path.join(path.dirname(config.LOG_FILE), 'rejections.log'),\r\n      format: fileFormat,\r\n      maxsize: 5242880, // 5MB\r\n      maxFiles: 2,\r\n    })\r\n  ],\r\n  exitOnError: false,\r\n});\r\n\r\n// Create a stream object for Morgan HTTP logging\r\nexport const morganStream = {\r\n  write: (message: string) => {\r\n    logger.http(message.trim());\r\n  },\r\n};\r\n\r\n// Helper functions for structured logging\r\nexport const logHelpers = {\r\n  /**\r\n   * Log user action\r\n   */\r\n  userAction: (userId: string, action: string, details?: any) => {\r\n    logger.info(`User Action: ${action}`, {\r\n      userId,\r\n      action,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log API request\r\n   */\r\n  apiRequest: (method: string, url: string, userId?: string, ip?: string) => {\r\n    logger.http(`API Request: ${method} ${url}`, {\r\n      method,\r\n      url,\r\n      userId,\r\n      ip,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log database operation\r\n   */\r\n  dbOperation: (operation: string, collection: string, details?: any) => {\r\n    logger.debug(`DB Operation: ${operation} on ${collection}`, {\r\n      operation,\r\n      collection,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log authentication event\r\n   */\r\n  authEvent: (event: string, userId?: string, ip?: string, details?: any) => {\r\n    logger.info(`Auth Event: ${event}`, {\r\n      event,\r\n      userId,\r\n      ip,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log security event\r\n   */\r\n  securityEvent: (event: string, severity: 'low' | 'medium' | 'high', details?: any) => {\r\n    const logLevel = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'info';\r\n    logger[logLevel](`Security Event: ${event}`, {\r\n      event,\r\n      severity,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log performance metric\r\n   */\r\n  performance: (metric: string, value: number, unit: string, details?: any) => {\r\n    logger.info(`Performance: ${metric} = ${value}${unit}`, {\r\n      metric,\r\n      value,\r\n      unit,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Log error with context\r\n   */\r\n  errorWithContext: (error: Error, context: string, details?: any) => {\r\n    logger.error(`Error in ${context}: ${error.message}`, {\r\n      error: {\r\n        name: error.name,\r\n        message: error.message,\r\n        stack: error.stack\r\n      },\r\n      context,\r\n      details,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n};\r\n\r\nexport default logger;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "296d6d04e848655a186a3147755dcb2c94ca9f16"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2owy25toec = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2owy25toec();
var __importDefault =
/* istanbul ignore next */
(cov_2owy25toec().s[0]++,
/* istanbul ignore next */
(cov_2owy25toec().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2owy25toec().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2owy25toec().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2owy25toec().f[0]++;
  cov_2owy25toec().s[1]++;
  return /* istanbul ignore next */(cov_2owy25toec().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2owy25toec().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2owy25toec().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_2owy25toec().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2owy25toec().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2owy25toec().s[3]++;
exports.logHelpers = exports.morganStream = exports.logger = void 0;
const winston_1 =
/* istanbul ignore next */
(cov_2owy25toec().s[4]++, __importDefault(require("winston")));
const path_1 =
/* istanbul ignore next */
(cov_2owy25toec().s[5]++, __importDefault(require("path")));
const environment_1 =
/* istanbul ignore next */
(cov_2owy25toec().s[6]++, require("../config/environment"));
// Define log levels
const logLevels =
/* istanbul ignore next */
(cov_2owy25toec().s[7]++, {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
});
// Define log colors
const logColors =
/* istanbul ignore next */
(cov_2owy25toec().s[8]++, {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
});
// Add colors to winston
/* istanbul ignore next */
cov_2owy25toec().s[9]++;
winston_1.default.addColors(logColors);
// Create log format
const logFormat =
/* istanbul ignore next */
(cov_2owy25toec().s[10]++, winston_1.default.format.combine(winston_1.default.format.timestamp({
  format: 'YYYY-MM-DD HH:mm:ss:ms'
}), winston_1.default.format.colorize({
  all: true
}), winston_1.default.format.printf(info => {
  /* istanbul ignore next */
  cov_2owy25toec().f[1]++;
  cov_2owy25toec().s[11]++;
  return `${info.timestamp} ${info.level}: ${info.message}`;
})));
// Create file format (without colors)
const fileFormat =
/* istanbul ignore next */
(cov_2owy25toec().s[12]++, winston_1.default.format.combine(winston_1.default.format.timestamp({
  format: 'YYYY-MM-DD HH:mm:ss:ms'
}), winston_1.default.format.errors({
  stack: true
}), winston_1.default.format.json()));
// Define transports
const transports =
/* istanbul ignore next */
(cov_2owy25toec().s[13]++, []);
// Console transport (always enabled in development)
/* istanbul ignore next */
cov_2owy25toec().s[14]++;
if (environment_1.config.NODE_ENV === 'development') {
  /* istanbul ignore next */
  cov_2owy25toec().b[3][0]++;
  cov_2owy25toec().s[15]++;
  transports.push(new winston_1.default.transports.Console({
    format: logFormat,
    level: 'debug'
  }));
} else {
  /* istanbul ignore next */
  cov_2owy25toec().b[3][1]++;
  cov_2owy25toec().s[16]++;
  transports.push(new winston_1.default.transports.Console({
    format: logFormat,
    level: environment_1.config.LOG_LEVEL
  }));
}
// File transports (for production and development)
/* istanbul ignore next */
cov_2owy25toec().s[17]++;
if (environment_1.config.NODE_ENV !== 'test') {
  /* istanbul ignore next */
  cov_2owy25toec().b[4][0]++;
  // Ensure logs directory exists
  const logsDir =
  /* istanbul ignore next */
  (cov_2owy25toec().s[18]++, path_1.default.dirname(environment_1.config.LOG_FILE));
  // Combined logs
  /* istanbul ignore next */
  cov_2owy25toec().s[19]++;
  transports.push(new winston_1.default.transports.File({
    filename: environment_1.config.LOG_FILE,
    format: fileFormat,
    level: environment_1.config.LOG_LEVEL,
    maxsize: 5242880,
    // 5MB
    maxFiles: 5
  }));
  // Error logs
  /* istanbul ignore next */
  cov_2owy25toec().s[20]++;
  transports.push(new winston_1.default.transports.File({
    filename: path_1.default.join(logsDir, 'error.log'),
    format: fileFormat,
    level: 'error',
    maxsize: 5242880,
    // 5MB
    maxFiles: 5
  }));
  // HTTP logs
  /* istanbul ignore next */
  cov_2owy25toec().s[21]++;
  transports.push(new winston_1.default.transports.File({
    filename: path_1.default.join(logsDir, 'http.log'),
    format: fileFormat,
    level: 'http',
    maxsize: 5242880,
    // 5MB
    maxFiles: 3
  }));
} else
/* istanbul ignore next */
{
  cov_2owy25toec().b[4][1]++;
}
// Create logger instance
cov_2owy25toec().s[22]++;
exports.logger = winston_1.default.createLogger({
  level: environment_1.config.LOG_LEVEL,
  levels: logLevels,
  format: fileFormat,
  transports,
  exceptionHandlers: [new winston_1.default.transports.File({
    filename: path_1.default.join(path_1.default.dirname(environment_1.config.LOG_FILE), 'exceptions.log'),
    format: fileFormat,
    maxsize: 5242880,
    // 5MB
    maxFiles: 2
  })],
  rejectionHandlers: [new winston_1.default.transports.File({
    filename: path_1.default.join(path_1.default.dirname(environment_1.config.LOG_FILE), 'rejections.log'),
    format: fileFormat,
    maxsize: 5242880,
    // 5MB
    maxFiles: 2
  })],
  exitOnError: false
});
// Create a stream object for Morgan HTTP logging
/* istanbul ignore next */
cov_2owy25toec().s[23]++;
exports.morganStream = {
  write: message => {
    /* istanbul ignore next */
    cov_2owy25toec().f[2]++;
    cov_2owy25toec().s[24]++;
    exports.logger.http(message.trim());
  }
};
// Helper functions for structured logging
/* istanbul ignore next */
cov_2owy25toec().s[25]++;
exports.logHelpers = {
  /**
   * Log user action
   */
  userAction: (userId, action, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[3]++;
    cov_2owy25toec().s[26]++;
    exports.logger.info(`User Action: ${action}`, {
      userId,
      action,
      details,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log API request
   */
  apiRequest: (method, url, userId, ip) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[4]++;
    cov_2owy25toec().s[27]++;
    exports.logger.http(`API Request: ${method} ${url}`, {
      method,
      url,
      userId,
      ip,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log database operation
   */
  dbOperation: (operation, collection, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[5]++;
    cov_2owy25toec().s[28]++;
    exports.logger.debug(`DB Operation: ${operation} on ${collection}`, {
      operation,
      collection,
      details,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log authentication event
   */
  authEvent: (event, userId, ip, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[6]++;
    cov_2owy25toec().s[29]++;
    exports.logger.info(`Auth Event: ${event}`, {
      event,
      userId,
      ip,
      details,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log security event
   */
  securityEvent: (event, severity, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[7]++;
    const logLevel =
    /* istanbul ignore next */
    (cov_2owy25toec().s[30]++, severity === 'high' ?
    /* istanbul ignore next */
    (cov_2owy25toec().b[5][0]++, 'error') :
    /* istanbul ignore next */
    (cov_2owy25toec().b[5][1]++, severity === 'medium' ?
    /* istanbul ignore next */
    (cov_2owy25toec().b[6][0]++, 'warn') :
    /* istanbul ignore next */
    (cov_2owy25toec().b[6][1]++, 'info')));
    /* istanbul ignore next */
    cov_2owy25toec().s[31]++;
    exports.logger[logLevel](`Security Event: ${event}`, {
      event,
      severity,
      details,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log performance metric
   */
  performance: (metric, value, unit, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[8]++;
    cov_2owy25toec().s[32]++;
    exports.logger.info(`Performance: ${metric} = ${value}${unit}`, {
      metric,
      value,
      unit,
      details,
      timestamp: new Date().toISOString()
    });
  },
  /**
   * Log error with context
   */
  errorWithContext: (error, context, details) => {
    /* istanbul ignore next */
    cov_2owy25toec().f[9]++;
    cov_2owy25toec().s[33]++;
    exports.logger.error(`Error in ${context}: ${error.message}`, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      details,
      timestamp: new Date().toISOString()
    });
  }
};
/* istanbul ignore next */
cov_2owy25toec().s[34]++;
exports.default = exports.logger;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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