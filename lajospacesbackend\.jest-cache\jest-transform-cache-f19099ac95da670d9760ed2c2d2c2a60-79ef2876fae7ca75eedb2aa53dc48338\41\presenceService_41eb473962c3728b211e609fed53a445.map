{"version": 3, "names": ["cov_3ygeh07az", "actualCoverage", "s", "User_model_1", "require", "logger_1", "PresenceService", "constructor", "f", "onlineUsers", "Map", "typingStatuses", "cleanupInterval", "setInterval", "cleanupStaleData", "getInstance", "instance", "b", "setUserOnline", "userId", "socketId", "status", "existingPresence", "get", "socketIds", "includes", "push", "lastSeen", "Date", "set", "updateUserLastActive", "logger", "debug", "setUserOffline", "presence", "filter", "id", "length", "clearUserTypingStatus", "updateUserStatus", "setUserActivity", "activityType", "details", "activity", "type", "startedAt", "currentActivity", "clearUserActivity", "getUserPresence", "getUserStatus", "isUserOnline", "getOnlineUsers", "Array", "from", "values", "getOnlineUsersCount", "getUserSocketIds", "setTypingStatus", "conversationId", "isTyping", "key", "delete", "getTypingUsersInConversation", "keysToDelete", "entries", "for<PERSON>ach", "getRecentlyOnlineUsers", "minutesAgo", "cutoffTime", "now", "getPresenceStats", "presences", "stats", "totalOnline", "p", "totalAway", "totalBusy", "totalOffline", "totalTyping", "size", "averageSessionDuration", "activeSessions", "totalDuration", "reduce", "sum", "sessionDuration", "getTime", "Math", "round", "staleThreshold", "typingThreshold", "timeSinceLastSeen", "typingStatus", "timeSinceStarted", "User", "findByIdAndUpdate", "lastActiveAt", "error", "syncWithDatabase", "recentlyActiveUsers", "find", "$gte", "select", "lean", "user", "_id", "toString", "cleanup", "clearInterval", "clear", "info", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\presenceService.ts"], "sourcesContent": ["\r\nimport { User } from '../models/User.model';\r\nimport { logger } from '../utils/logger';\r\n\r\nexport interface UserPresence {\r\n  userId: string;\r\n  status: 'online' | 'away' | 'busy' | 'offline';\r\n  lastSeen: Date;\r\n  socketIds: string[];\r\n  currentActivity?: {\r\n    type: 'browsing' | 'messaging' | 'viewing_property' | 'matching';\r\n    details?: string;\r\n    startedAt: Date;\r\n  };\r\n}\r\n\r\nexport interface TypingStatus {\r\n  userId: string;\r\n  conversationId: string;\r\n  isTyping: boolean;\r\n  startedAt: Date;\r\n}\r\n\r\nexport class PresenceService {\r\n  private static instance: PresenceService;\r\n  private onlineUsers: Map<string, UserPresence> = new Map();\r\n  private typingStatuses: Map<string, TypingStatus> = new Map(); // key: userId_conversationId\r\n  private cleanupInterval: NodeJS.Timeout;\r\n\r\n  constructor() {\r\n    // Start cleanup interval for stale data\r\n    this.cleanupInterval = setInterval(() => {\r\n      this.cleanupStaleData();\r\n    }, 30000); // Every 30 seconds\r\n  }\r\n\r\n  public static getInstance(): PresenceService {\r\n    if (!PresenceService.instance) {\r\n      PresenceService.instance = new PresenceService();\r\n    }\r\n    return PresenceService.instance;\r\n  }\r\n\r\n  /**\r\n   * Set user online status\r\n   */\r\n  public setUserOnline(userId: string, socketId: string, status: 'online' | 'away' | 'busy' = 'online'): void {\r\n    const existingPresence = this.onlineUsers.get(userId);\r\n    \r\n    if (existingPresence) {\r\n      // Add socket ID if not already present\r\n      if (!existingPresence.socketIds.includes(socketId)) {\r\n        existingPresence.socketIds.push(socketId);\r\n      }\r\n      existingPresence.status = status;\r\n      existingPresence.lastSeen = new Date();\r\n    } else {\r\n      // Create new presence record\r\n      this.onlineUsers.set(userId, {\r\n        userId,\r\n        status,\r\n        lastSeen: new Date(),\r\n        socketIds: [socketId]\r\n      });\r\n    }\r\n\r\n    // Update user's last active time in database\r\n    this.updateUserLastActive(userId);\r\n\r\n    logger.debug(`User ${userId} set to ${status} with socket ${socketId}`);\r\n  }\r\n\r\n  /**\r\n   * Set user offline\r\n   */\r\n  public setUserOffline(userId: string, socketId?: string): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      if (socketId) {\r\n        // Remove specific socket\r\n        presence.socketIds = presence.socketIds.filter(id => id !== socketId);\r\n        \r\n        // If no more sockets, set offline\r\n        if (presence.socketIds.length === 0) {\r\n          presence.status = 'offline';\r\n          presence.lastSeen = new Date();\r\n          this.updateUserLastActive(userId);\r\n        }\r\n      } else {\r\n        // Remove all sockets and set offline\r\n        presence.status = 'offline';\r\n        presence.lastSeen = new Date();\r\n        presence.socketIds = [];\r\n        this.updateUserLastActive(userId);\r\n      }\r\n    }\r\n\r\n    // Clear typing status for this user\r\n    this.clearUserTypingStatus(userId);\r\n\r\n    logger.debug(`User ${userId} set offline${socketId ? ` (socket ${socketId})` : ''}`);\r\n  }\r\n\r\n  /**\r\n   * Update user status\r\n   */\r\n  public updateUserStatus(userId: string, status: 'online' | 'away' | 'busy'): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      presence.status = status;\r\n      presence.lastSeen = new Date();\r\n      this.updateUserLastActive(userId);\r\n    }\r\n\r\n    logger.debug(`User ${userId} status updated to ${status}`);\r\n  }\r\n\r\n  /**\r\n   * Set user activity\r\n   */\r\n  public setUserActivity(\r\n    userId: string, \r\n    activityType: 'browsing' | 'messaging' | 'viewing_property' | 'matching',\r\n    details?: string\r\n  ): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      const activity: any = {\r\n        type: activityType,\r\n        startedAt: new Date()\r\n      };\r\n      if (details) {\r\n        activity.details = details;\r\n      }\r\n      presence.currentActivity = activity;\r\n      presence.lastSeen = new Date();\r\n    }\r\n\r\n    logger.debug(`User ${userId} activity set to ${activityType}${details ? `: ${details}` : ''}`);\r\n  }\r\n\r\n  /**\r\n   * Clear user activity\r\n   */\r\n  public clearUserActivity(userId: string): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      delete presence.currentActivity;\r\n      presence.lastSeen = new Date();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user presence\r\n   */\r\n  public getUserPresence(userId: string): UserPresence | null {\r\n    return this.onlineUsers.get(userId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get user status\r\n   */\r\n  public getUserStatus(userId: string): 'online' | 'away' | 'busy' | 'offline' {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.status : 'offline';\r\n  }\r\n\r\n  /**\r\n   * Check if user is online\r\n   */\r\n  public isUserOnline(userId: string): boolean {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.status !== 'offline' && presence.socketIds.length > 0 : false;\r\n  }\r\n\r\n  /**\r\n   * Get all online users\r\n   */\r\n  public getOnlineUsers(): UserPresence[] {\r\n    return Array.from(this.onlineUsers.values()).filter(presence => \r\n      presence.status !== 'offline' && presence.socketIds.length > 0\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get online users count\r\n   */\r\n  public getOnlineUsersCount(): number {\r\n    return this.getOnlineUsers().length;\r\n  }\r\n\r\n  /**\r\n   * Get user's socket IDs\r\n   */\r\n  public getUserSocketIds(userId: string): string[] {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.socketIds : [];\r\n  }\r\n\r\n  /**\r\n   * Set typing status\r\n   */\r\n  public setTypingStatus(userId: string, conversationId: string, isTyping: boolean): void {\r\n    const key = `${userId}_${conversationId}`;\r\n    \r\n    if (isTyping) {\r\n      this.typingStatuses.set(key, {\r\n        userId,\r\n        conversationId,\r\n        isTyping: true,\r\n        startedAt: new Date()\r\n      });\r\n    } else {\r\n      this.typingStatuses.delete(key);\r\n    }\r\n\r\n    logger.debug(`User ${userId} ${isTyping ? 'started' : 'stopped'} typing in conversation ${conversationId}`);\r\n  }\r\n\r\n  /**\r\n   * Get typing status for conversation\r\n   */\r\n  public getTypingUsersInConversation(conversationId: string): TypingStatus[] {\r\n    return Array.from(this.typingStatuses.values()).filter(\r\n      status => status.conversationId === conversationId && status.isTyping\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear all typing status for a user\r\n   */\r\n  public clearUserTypingStatus(userId: string): void {\r\n    const keysToDelete: string[] = [];\r\n    \r\n    for (const [key, status] of this.typingStatuses.entries()) {\r\n      if (status.userId === userId) {\r\n        keysToDelete.push(key);\r\n      }\r\n    }\r\n    \r\n    keysToDelete.forEach(key => this.typingStatuses.delete(key));\r\n  }\r\n\r\n  /**\r\n   * Get users who were recently online\r\n   */\r\n  public getRecentlyOnlineUsers(minutesAgo: number = 15): UserPresence[] {\r\n    const cutoffTime = new Date(Date.now() - minutesAgo * 60 * 1000);\r\n    \r\n    return Array.from(this.onlineUsers.values()).filter(presence => \r\n      presence.lastSeen >= cutoffTime\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get presence statistics\r\n   */\r\n  public getPresenceStats(): {\r\n    totalOnline: number;\r\n    totalAway: number;\r\n    totalBusy: number;\r\n    totalOffline: number;\r\n    totalTyping: number;\r\n    averageSessionDuration: number;\r\n  } {\r\n    const presences = Array.from(this.onlineUsers.values());\r\n    \r\n    const stats = {\r\n      totalOnline: presences.filter(p => p.status === 'online' && p.socketIds.length > 0).length,\r\n      totalAway: presences.filter(p => p.status === 'away').length,\r\n      totalBusy: presences.filter(p => p.status === 'busy').length,\r\n      totalOffline: presences.filter(p => p.status === 'offline' || p.socketIds.length === 0).length,\r\n      totalTyping: this.typingStatuses.size,\r\n      averageSessionDuration: 0\r\n    };\r\n\r\n    // Calculate average session duration (simplified)\r\n    const activeSessions = presences.filter(p => p.socketIds.length > 0);\r\n    if (activeSessions.length > 0) {\r\n      const totalDuration = activeSessions.reduce((sum, presence) => {\r\n        const sessionDuration = Date.now() - presence.lastSeen.getTime();\r\n        return sum + sessionDuration;\r\n      }, 0);\r\n      stats.averageSessionDuration = Math.round(totalDuration / activeSessions.length / 1000 / 60); // in minutes\r\n    }\r\n\r\n    return stats;\r\n  }\r\n\r\n  /**\r\n   * Cleanup stale data\r\n   */\r\n  private cleanupStaleData(): void {\r\n    const now = new Date();\r\n    const staleThreshold = 5 * 60 * 1000; // 5 minutes\r\n    const typingThreshold = 30 * 1000; // 30 seconds\r\n\r\n    // Clean up stale presence data\r\n    for (const [userId, presence] of this.onlineUsers.entries()) {\r\n      const timeSinceLastSeen = now.getTime() - presence.lastSeen.getTime();\r\n      \r\n      if (timeSinceLastSeen > staleThreshold && presence.status !== 'offline') {\r\n        presence.status = 'offline';\r\n        presence.socketIds = [];\r\n        this.updateUserLastActive(userId);\r\n        logger.debug(`Marked user ${userId} as offline due to inactivity`);\r\n      }\r\n    }\r\n\r\n    // Clean up stale typing indicators\r\n    for (const [key, typingStatus] of this.typingStatuses.entries()) {\r\n      const timeSinceStarted = now.getTime() - typingStatus.startedAt.getTime();\r\n      \r\n      if (timeSinceStarted > typingThreshold) {\r\n        this.typingStatuses.delete(key);\r\n        logger.debug(`Cleared stale typing status for user ${typingStatus.userId} in conversation ${typingStatus.conversationId}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user's last active time in database\r\n   */\r\n  private async updateUserLastActive(userId: string): Promise<void> {\r\n    try {\r\n      await User.findByIdAndUpdate(userId, {\r\n        lastActiveAt: new Date()\r\n      });\r\n    } catch (error) {\r\n      logger.error(`Error updating last active time for user ${userId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Bulk update user statuses from database\r\n   */\r\n  public async syncWithDatabase(): Promise<void> {\r\n    try {\r\n      // Get users who were active in the last hour\r\n      const recentlyActiveUsers = await User.find({\r\n        lastActiveAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }\r\n      }).select('_id lastActiveAt').lean();\r\n\r\n      // Update presence data\r\n      for (const user of recentlyActiveUsers) {\r\n        const presence = this.onlineUsers.get(user._id.toString());\r\n        if (presence) {\r\n          // Update last seen from database if it's more recent\r\n          if (user.lastActiveAt > presence.lastSeen) {\r\n            presence.lastSeen = user.lastActiveAt;\r\n          }\r\n        }\r\n      }\r\n\r\n      logger.debug(`Synced presence data for ${recentlyActiveUsers.length} users`);\r\n    } catch (error) {\r\n      logger.error('Error syncing presence with database:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup on service shutdown\r\n   */\r\n  public cleanup(): void {\r\n    if (this.cleanupInterval) {\r\n      clearInterval(this.cleanupInterval);\r\n    }\r\n    \r\n    // Mark all users as offline\r\n    for (const [userId, presence] of this.onlineUsers.entries()) {\r\n      presence.status = 'offline';\r\n      presence.socketIds = [];\r\n      this.updateUserLastActive(userId);\r\n    }\r\n    \r\n    this.onlineUsers.clear();\r\n    this.typingStatuses.clear();\r\n    \r\n    logger.info('Presence service cleaned up');\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqCQ;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AApCR,MAAAC,YAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAqBA,MAAaE,eAAe;EAM1BC,YAAA;IAAA;IAAAP,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IAJQ,KAAAO,WAAW,GAA8B,IAAIC,GAAG,EAAE;IAAC;IAAAV,aAAA,GAAAE,CAAA;IACnD,KAAAS,cAAc,GAA8B,IAAID,GAAG,EAAE,CAAC,CAAC;IAI7D;IAAA;IAAAV,aAAA,GAAAE,CAAA;IACA,IAAI,CAACU,eAAe,GAAGC,WAAW,CAAC,MAAK;MAAA;MAAAb,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MACtC,IAAI,CAACY,gBAAgB,EAAE;IACzB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;EAEO,OAAOC,WAAWA,CAAA;IAAA;IAAAf,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACvB,IAAI,CAACI,eAAe,CAACU,QAAQ,EAAE;MAAA;MAAAhB,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MAC7BI,eAAe,CAACU,QAAQ,GAAG,IAAIV,eAAe,EAAE;IAClD,CAAC;IAAA;IAAA;MAAAN,aAAA,GAAAiB,CAAA;IAAA;IAAAjB,aAAA,GAAAE,CAAA;IACD,OAAOI,eAAe,CAACU,QAAQ;EACjC;EAEA;;;EAGOE,aAAaA,CAACC,MAAc,EAAEC,QAAgB,EAAEC,MAAA;EAAA;EAAA,CAAArB,aAAA,GAAAiB,CAAA,UAAqC,QAAQ;IAAA;IAAAjB,aAAA,GAAAQ,CAAA;IAClG,MAAMc,gBAAgB;IAAA;IAAA,CAAAtB,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAEtD,IAAIoB,gBAAgB,EAAE;MAAA;MAAAtB,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACpB;MACA,IAAI,CAACoB,gBAAgB,CAACE,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QAAA;QAAApB,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QAClDoB,gBAAgB,CAACE,SAAS,CAACE,IAAI,CAACN,QAAQ,CAAC;MAC3C,CAAC;MAAA;MAAA;QAAApB,aAAA,GAAAiB,CAAA;MAAA;MAAAjB,aAAA,GAAAE,CAAA;MACDoB,gBAAgB,CAACD,MAAM,GAAGA,MAAM;MAAC;MAAArB,aAAA,GAAAE,CAAA;MACjCoB,gBAAgB,CAACK,QAAQ,GAAG,IAAIC,IAAI,EAAE;IACxC,CAAC,MAAM;MAAA;MAAA5B,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACL;MACA,IAAI,CAACO,WAAW,CAACoB,GAAG,CAACV,MAAM,EAAE;QAC3BA,MAAM;QACNE,MAAM;QACNM,QAAQ,EAAE,IAAIC,IAAI,EAAE;QACpBJ,SAAS,EAAE,CAACJ,QAAQ;OACrB,CAAC;IACJ;IAEA;IAAA;IAAApB,aAAA,GAAAE,CAAA;IACA,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAElCG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,QAAQb,MAAM,WAAWE,MAAM,gBAAgBD,QAAQ,EAAE,CAAC;EACzE;EAEA;;;EAGOa,cAAcA,CAACd,MAAc,EAAEC,QAAiB;IAAA;IAAApB,aAAA,GAAAQ,CAAA;IACrD,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE9C,IAAIgC,QAAQ,EAAE;MAAA;MAAAlC,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACZ,IAAIkB,QAAQ,EAAE;QAAA;QAAApB,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QACZ;QACAgC,QAAQ,CAACV,SAAS,GAAGU,QAAQ,CAACV,SAAS,CAACW,MAAM,CAACC,EAAE,IAAI;UAAA;UAAApC,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAE,CAAA;UAAA,OAAAkC,EAAE,KAAKhB,QAAQ;QAAR,CAAQ,CAAC;QAErE;QAAA;QAAApB,aAAA,GAAAE,CAAA;QACA,IAAIgC,QAAQ,CAACV,SAAS,CAACa,MAAM,KAAK,CAAC,EAAE;UAAA;UAAArC,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAE,CAAA;UACnCgC,QAAQ,CAACb,MAAM,GAAG,SAAS;UAAC;UAAArB,aAAA,GAAAE,CAAA;UAC5BgC,QAAQ,CAACP,QAAQ,GAAG,IAAIC,IAAI,EAAE;UAAC;UAAA5B,aAAA,GAAAE,CAAA;UAC/B,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;QACnC,CAAC;QAAA;QAAA;UAAAnB,aAAA,GAAAiB,CAAA;QAAA;MACH,CAAC,MAAM;QAAA;QAAAjB,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QACL;QACAgC,QAAQ,CAACb,MAAM,GAAG,SAAS;QAAC;QAAArB,aAAA,GAAAE,CAAA;QAC5BgC,QAAQ,CAACP,QAAQ,GAAG,IAAIC,IAAI,EAAE;QAAC;QAAA5B,aAAA,GAAAE,CAAA;QAC/BgC,QAAQ,CAACV,SAAS,GAAG,EAAE;QAAC;QAAAxB,aAAA,GAAAE,CAAA;QACxB,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;MACnC;IACF,CAAC;IAAA;IAAA;MAAAnB,aAAA,GAAAiB,CAAA;IAAA;IAED;IAAAjB,aAAA,GAAAE,CAAA;IACA,IAAI,CAACoC,qBAAqB,CAACnB,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAEnCG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,QAAQb,MAAM,eAAeC,QAAQ;IAAA;IAAA,CAAApB,aAAA,GAAAiB,CAAA,UAAG,YAAYG,QAAQ,GAAG;IAAA;IAAA,CAAApB,aAAA,GAAAiB,CAAA,UAAG,EAAE,GAAE,CAAC;EACtF;EAEA;;;EAGOsB,gBAAgBA,CAACpB,MAAc,EAAEE,MAAkC;IAAA;IAAArB,aAAA,GAAAQ,CAAA;IACxE,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE9C,IAAIgC,QAAQ,EAAE;MAAA;MAAAlC,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACZgC,QAAQ,CAACb,MAAM,GAAGA,MAAM;MAAC;MAAArB,aAAA,GAAAE,CAAA;MACzBgC,QAAQ,CAACP,QAAQ,GAAG,IAAIC,IAAI,EAAE;MAAC;MAAA5B,aAAA,GAAAE,CAAA;MAC/B,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;IACnC,CAAC;IAAA;IAAA;MAAAnB,aAAA,GAAAiB,CAAA;IAAA;IAAAjB,aAAA,GAAAE,CAAA;IAEDG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,QAAQb,MAAM,sBAAsBE,MAAM,EAAE,CAAC;EAC5D;EAEA;;;EAGOmB,eAAeA,CACpBrB,MAAc,EACdsB,YAAwE,EACxEC,OAAgB;IAAA;IAAA1C,aAAA,GAAAQ,CAAA;IAEhB,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE9C,IAAIgC,QAAQ,EAAE;MAAA;MAAAlC,aAAA,GAAAiB,CAAA;MACZ,MAAM0B,QAAQ;MAAA;MAAA,CAAA3C,aAAA,GAAAE,CAAA,QAAQ;QACpB0C,IAAI,EAAEH,YAAY;QAClBI,SAAS,EAAE,IAAIjB,IAAI;OACpB;MAAC;MAAA5B,aAAA,GAAAE,CAAA;MACF,IAAIwC,OAAO,EAAE;QAAA;QAAA1C,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QACXyC,QAAQ,CAACD,OAAO,GAAGA,OAAO;MAC5B,CAAC;MAAA;MAAA;QAAA1C,aAAA,GAAAiB,CAAA;MAAA;MAAAjB,aAAA,GAAAE,CAAA;MACDgC,QAAQ,CAACY,eAAe,GAAGH,QAAQ;MAAC;MAAA3C,aAAA,GAAAE,CAAA;MACpCgC,QAAQ,CAACP,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAChC,CAAC;IAAA;IAAA;MAAA5B,aAAA,GAAAiB,CAAA;IAAA;IAAAjB,aAAA,GAAAE,CAAA;IAEDG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,QAAQb,MAAM,oBAAoBsB,YAAY,GAAGC,OAAO;IAAA;IAAA,CAAA1C,aAAA,GAAAiB,CAAA,WAAG,KAAKyB,OAAO,EAAE;IAAA;IAAA,CAAA1C,aAAA,GAAAiB,CAAA,WAAG,EAAE,GAAE,CAAC;EAChG;EAEA;;;EAGO8B,iBAAiBA,CAAC5B,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IACrC,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE9C,IAAIgC,QAAQ,EAAE;MAAA;MAAAlC,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACZ,OAAOgC,QAAQ,CAACY,eAAe;MAAC;MAAA9C,aAAA,GAAAE,CAAA;MAChCgC,QAAQ,CAACP,QAAQ,GAAG,IAAIC,IAAI,EAAE;IAChC,CAAC;IAAA;IAAA;MAAA5B,aAAA,GAAAiB,CAAA;IAAA;EACH;EAEA;;;EAGO+B,eAAeA,CAAC7B,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACnC,OAAO,2BAAAF,aAAA,GAAAiB,CAAA,eAAI,CAACR,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAA;IAAA,CAAAnB,aAAA,GAAAiB,CAAA,WAAI,IAAI;EAC7C;EAEA;;;EAGOgC,aAAaA,CAAC9B,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IACjC,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAC9C,OAAOgC,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAiB,CAAA,WAAGiB,QAAQ,CAACb,MAAM;IAAA;IAAA,CAAArB,aAAA,GAAAiB,CAAA,WAAG,SAAS;EAC/C;EAEA;;;EAGOiC,YAAYA,CAAC/B,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IAChC,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAC9C,OAAOgC,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAiB,CAAA;IAAG;IAAA,CAAAjB,aAAA,GAAAiB,CAAA,WAAAiB,QAAQ,CAACb,MAAM,KAAK,SAAS;IAAA;IAAA,CAAArB,aAAA,GAAAiB,CAAA,WAAIiB,QAAQ,CAACV,SAAS,CAACa,MAAM,GAAG,CAAC;IAAA;IAAA,CAAArC,aAAA,GAAAiB,CAAA,WAAG,KAAK;EAC1F;EAEA;;;EAGOkC,cAAcA,CAAA;IAAA;IAAAnD,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACnB,OAAOkD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,WAAW,CAAC6C,MAAM,EAAE,CAAC,CAACnB,MAAM,CAACD,QAAQ,IAC1D;MAAA;MAAAlC,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,kCAAAF,aAAA,GAAAiB,CAAA,WAAAiB,QAAQ,CAACb,MAAM,KAAK,SAAS;MAAA;MAAA,CAAArB,aAAA,GAAAiB,CAAA,WAAIiB,QAAQ,CAACV,SAAS,CAACa,MAAM,GAAG,CAAC;IAAD,CAAC,CAC/D;EACH;EAEA;;;EAGOkB,mBAAmBA,CAAA;IAAA;IAAAvD,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACxB,OAAO,IAAI,CAACiD,cAAc,EAAE,CAACd,MAAM;EACrC;EAEA;;;EAGOmB,gBAAgBA,CAACrC,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IACpC,MAAM0B,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACJ,MAAM,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAC9C,OAAOgC,QAAQ;IAAA;IAAA,CAAAlC,aAAA,GAAAiB,CAAA,WAAGiB,QAAQ,CAACV,SAAS;IAAA;IAAA,CAAAxB,aAAA,GAAAiB,CAAA,WAAG,EAAE;EAC3C;EAEA;;;EAGOwC,eAAeA,CAACtC,MAAc,EAAEuC,cAAsB,EAAEC,QAAiB;IAAA;IAAA3D,aAAA,GAAAQ,CAAA;IAC9E,MAAMoD,GAAG;IAAA;IAAA,CAAA5D,aAAA,GAAAE,CAAA,QAAG,GAAGiB,MAAM,IAAIuC,cAAc,EAAE;IAAC;IAAA1D,aAAA,GAAAE,CAAA;IAE1C,IAAIyD,QAAQ,EAAE;MAAA;MAAA3D,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACZ,IAAI,CAACS,cAAc,CAACkB,GAAG,CAAC+B,GAAG,EAAE;QAC3BzC,MAAM;QACNuC,cAAc;QACdC,QAAQ,EAAE,IAAI;QACdd,SAAS,EAAE,IAAIjB,IAAI;OACpB,CAAC;IACJ,CAAC,MAAM;MAAA;MAAA5B,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACL,IAAI,CAACS,cAAc,CAACkD,MAAM,CAACD,GAAG,CAAC;IACjC;IAAC;IAAA5D,aAAA,GAAAE,CAAA;IAEDG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,QAAQb,MAAM,IAAIwC,QAAQ;IAAA;IAAA,CAAA3D,aAAA,GAAAiB,CAAA,WAAG,SAAS;IAAA;IAAA,CAAAjB,aAAA,GAAAiB,CAAA,WAAG,SAAS,4BAA2ByC,cAAc,EAAE,CAAC;EAC7G;EAEA;;;EAGOI,4BAA4BA,CAACJ,cAAsB;IAAA;IAAA1D,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACxD,OAAOkD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1C,cAAc,CAAC2C,MAAM,EAAE,CAAC,CAACnB,MAAM,CACpDd,MAAM,IAAI;MAAA;MAAArB,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,kCAAAF,aAAA,GAAAiB,CAAA,WAAAI,MAAM,CAACqC,cAAc,KAAKA,cAAc;MAAA;MAAA,CAAA1D,aAAA,GAAAiB,CAAA,WAAII,MAAM,CAACsC,QAAQ;IAAR,CAAQ,CACtE;EACH;EAEA;;;EAGOrB,qBAAqBA,CAACnB,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IACzC,MAAMuD,YAAY;IAAA;IAAA,CAAA/D,aAAA,GAAAE,CAAA,QAAa,EAAE;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAElC,KAAK,MAAM,CAAC0D,GAAG,EAAEvC,MAAM,CAAC,IAAI,IAAI,CAACV,cAAc,CAACqD,OAAO,EAAE,EAAE;MAAA;MAAAhE,aAAA,GAAAE,CAAA;MACzD,IAAImB,MAAM,CAACF,MAAM,KAAKA,MAAM,EAAE;QAAA;QAAAnB,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QAC5B6D,YAAY,CAACrC,IAAI,CAACkC,GAAG,CAAC;MACxB,CAAC;MAAA;MAAA;QAAA5D,aAAA,GAAAiB,CAAA;MAAA;IACH;IAAC;IAAAjB,aAAA,GAAAE,CAAA;IAED6D,YAAY,CAACE,OAAO,CAACL,GAAG,IAAI;MAAA;MAAA5D,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,WAAI,CAACS,cAAc,CAACkD,MAAM,CAACD,GAAG,CAAC;IAAD,CAAC,CAAC;EAC9D;EAEA;;;EAGOM,sBAAsBA,CAACC,UAAA;EAAA;EAAA,CAAAnE,aAAA,GAAAiB,CAAA,WAAqB,EAAE;IAAA;IAAAjB,aAAA,GAAAQ,CAAA;IACnD,MAAM4D,UAAU;IAAA;IAAA,CAAApE,aAAA,GAAAE,CAAA,QAAG,IAAI0B,IAAI,CAACA,IAAI,CAACyC,GAAG,EAAE,GAAGF,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;IAAC;IAAAnE,aAAA,GAAAE,CAAA;IAEjE,OAAOkD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,WAAW,CAAC6C,MAAM,EAAE,CAAC,CAACnB,MAAM,CAACD,QAAQ,IAC1D;MAAA;MAAAlC,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,OAAAgC,QAAQ,CAACP,QAAQ,IAAIyC,UAAU;IAAV,CAAU,CAChC;EACH;EAEA;;;EAGOE,gBAAgBA,CAAA;IAAA;IAAAtE,aAAA,GAAAQ,CAAA;IAQrB,MAAM+D,SAAS;IAAA;IAAA,CAAAvE,aAAA,GAAAE,CAAA,QAAGkD,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,WAAW,CAAC6C,MAAM,EAAE,CAAC;IAEvD,MAAMkB,KAAK;IAAA;IAAA,CAAAxE,aAAA,GAAAE,CAAA,QAAG;MACZuE,WAAW,EAAEF,SAAS,CAACpC,MAAM,CAACuC,CAAC,IAAI;QAAA;QAAA1E,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QAAA,kCAAAF,aAAA,GAAAiB,CAAA,WAAAyD,CAAC,CAACrD,MAAM,KAAK,QAAQ;QAAA;QAAA,CAAArB,aAAA,GAAAiB,CAAA,WAAIyD,CAAC,CAAClD,SAAS,CAACa,MAAM,GAAG,CAAC;MAAD,CAAC,CAAC,CAACA,MAAM;MAC1FsC,SAAS,EAAEJ,SAAS,CAACpC,MAAM,CAACuC,CAAC,IAAI;QAAA;QAAA1E,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QAAA,OAAAwE,CAAC,CAACrD,MAAM,KAAK,MAAM;MAAN,CAAM,CAAC,CAACgB,MAAM;MAC5DuC,SAAS,EAAEL,SAAS,CAACpC,MAAM,CAACuC,CAAC,IAAI;QAAA;QAAA1E,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QAAA,OAAAwE,CAAC,CAACrD,MAAM,KAAK,MAAM;MAAN,CAAM,CAAC,CAACgB,MAAM;MAC5DwC,YAAY,EAAEN,SAAS,CAACpC,MAAM,CAACuC,CAAC,IAAI;QAAA;QAAA1E,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAE,CAAA;QAAA,kCAAAF,aAAA,GAAAiB,CAAA,WAAAyD,CAAC,CAACrD,MAAM,KAAK,SAAS;QAAA;QAAA,CAAArB,aAAA,GAAAiB,CAAA,WAAIyD,CAAC,CAAClD,SAAS,CAACa,MAAM,KAAK,CAAC;MAAD,CAAC,CAAC,CAACA,MAAM;MAC9FyC,WAAW,EAAE,IAAI,CAACnE,cAAc,CAACoE,IAAI;MACrCC,sBAAsB,EAAE;KACzB;IAED;IACA,MAAMC,cAAc;IAAA;IAAA,CAAAjF,aAAA,GAAAE,CAAA,QAAGqE,SAAS,CAACpC,MAAM,CAACuC,CAAC,IAAI;MAAA;MAAA1E,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAE,CAAA;MAAA,OAAAwE,CAAC,CAAClD,SAAS,CAACa,MAAM,GAAG,CAAC;IAAD,CAAC,CAAC;IAAC;IAAArC,aAAA,GAAAE,CAAA;IACrE,IAAI+E,cAAc,CAAC5C,MAAM,GAAG,CAAC,EAAE;MAAA;MAAArC,aAAA,GAAAiB,CAAA;MAC7B,MAAMiE,aAAa;MAAA;MAAA,CAAAlF,aAAA,GAAAE,CAAA,QAAG+E,cAAc,CAACE,MAAM,CAAC,CAACC,GAAG,EAAElD,QAAQ,KAAI;QAAA;QAAAlC,aAAA,GAAAQ,CAAA;QAC5D,MAAM6E,eAAe;QAAA;QAAA,CAAArF,aAAA,GAAAE,CAAA,QAAG0B,IAAI,CAACyC,GAAG,EAAE,GAAGnC,QAAQ,CAACP,QAAQ,CAAC2D,OAAO,EAAE;QAAC;QAAAtF,aAAA,GAAAE,CAAA;QACjE,OAAOkF,GAAG,GAAGC,eAAe;MAC9B,CAAC,EAAE,CAAC,CAAC;MAAC;MAAArF,aAAA,GAAAE,CAAA;MACNsE,KAAK,CAACQ,sBAAsB,GAAGO,IAAI,CAACC,KAAK,CAACN,aAAa,GAAGD,cAAc,CAAC5C,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAChG,CAAC;IAAA;IAAA;MAAArC,aAAA,GAAAiB,CAAA;IAAA;IAAAjB,aAAA,GAAAE,CAAA;IAED,OAAOsE,KAAK;EACd;EAEA;;;EAGQ1D,gBAAgBA,CAAA;IAAA;IAAAd,aAAA,GAAAQ,CAAA;IACtB,MAAM6D,GAAG;IAAA;IAAA,CAAArE,aAAA,GAAAE,CAAA,QAAG,IAAI0B,IAAI,EAAE;IACtB,MAAM6D,cAAc;IAAA;IAAA,CAAAzF,aAAA,GAAAE,CAAA,QAAG,CAAC,GAAG,EAAE,GAAG,IAAI,EAAC,CAAC;IACtC,MAAMwF,eAAe;IAAA;IAAA,CAAA1F,aAAA,GAAAE,CAAA,QAAG,EAAE,GAAG,IAAI,EAAC,CAAC;IAEnC;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACA,KAAK,MAAM,CAACiB,MAAM,EAAEe,QAAQ,CAAC,IAAI,IAAI,CAACzB,WAAW,CAACuD,OAAO,EAAE,EAAE;MAC3D,MAAM2B,iBAAiB;MAAA;MAAA,CAAA3F,aAAA,GAAAE,CAAA,QAAGmE,GAAG,CAACiB,OAAO,EAAE,GAAGpD,QAAQ,CAACP,QAAQ,CAAC2D,OAAO,EAAE;MAAC;MAAAtF,aAAA,GAAAE,CAAA;MAEtE;MAAI;MAAA,CAAAF,aAAA,GAAAiB,CAAA,WAAA0E,iBAAiB,GAAGF,cAAc;MAAA;MAAA,CAAAzF,aAAA,GAAAiB,CAAA,WAAIiB,QAAQ,CAACb,MAAM,KAAK,SAAS,GAAE;QAAA;QAAArB,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QACvEgC,QAAQ,CAACb,MAAM,GAAG,SAAS;QAAC;QAAArB,aAAA,GAAAE,CAAA;QAC5BgC,QAAQ,CAACV,SAAS,GAAG,EAAE;QAAC;QAAAxB,aAAA,GAAAE,CAAA;QACxB,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;QAAC;QAAAnB,aAAA,GAAAE,CAAA;QAClCG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,eAAeb,MAAM,+BAA+B,CAAC;MACpE,CAAC;MAAA;MAAA;QAAAnB,aAAA,GAAAiB,CAAA;MAAA;IACH;IAEA;IAAA;IAAAjB,aAAA,GAAAE,CAAA;IACA,KAAK,MAAM,CAAC0D,GAAG,EAAEgC,YAAY,CAAC,IAAI,IAAI,CAACjF,cAAc,CAACqD,OAAO,EAAE,EAAE;MAC/D,MAAM6B,gBAAgB;MAAA;MAAA,CAAA7F,aAAA,GAAAE,CAAA,SAAGmE,GAAG,CAACiB,OAAO,EAAE,GAAGM,YAAY,CAAC/C,SAAS,CAACyC,OAAO,EAAE;MAAC;MAAAtF,aAAA,GAAAE,CAAA;MAE1E,IAAI2F,gBAAgB,GAAGH,eAAe,EAAE;QAAA;QAAA1F,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAE,CAAA;QACtC,IAAI,CAACS,cAAc,CAACkD,MAAM,CAACD,GAAG,CAAC;QAAC;QAAA5D,aAAA,GAAAE,CAAA;QAChCG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,wCAAwC4D,YAAY,CAACzE,MAAM,oBAAoByE,YAAY,CAAClC,cAAc,EAAE,CAAC;MAC5H,CAAC;MAAA;MAAA;QAAA1D,aAAA,GAAAiB,CAAA;MAAA;IACH;EACF;EAEA;;;EAGQ,MAAMa,oBAAoBA,CAACX,MAAc;IAAA;IAAAnB,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IAC/C,IAAI;MAAA;MAAAF,aAAA,GAAAE,CAAA;MACF,MAAMC,YAAA,CAAA2F,IAAI,CAACC,iBAAiB,CAAC5E,MAAM,EAAE;QACnC6E,YAAY,EAAE,IAAIpE,IAAI;OACvB,CAAC;IACJ,CAAC,CAAC,OAAOqE,KAAK,EAAE;MAAA;MAAAjG,aAAA,GAAAE,CAAA;MACdG,QAAA,CAAA0B,MAAM,CAACkE,KAAK,CAAC,4CAA4C9E,MAAM,GAAG,EAAE8E,KAAK,CAAC;IAC5E;EACF;EAEA;;;EAGO,MAAMC,gBAAgBA,CAAA;IAAA;IAAAlG,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IAC3B,IAAI;MACF;MACA,MAAMiG,mBAAmB;MAAA;MAAA,CAAAnG,aAAA,GAAAE,CAAA,SAAG,MAAMC,YAAA,CAAA2F,IAAI,CAACM,IAAI,CAAC;QAC1CJ,YAAY,EAAE;UAAEK,IAAI,EAAE,IAAIzE,IAAI,CAACA,IAAI,CAACyC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAAC;OAC5D,CAAC,CAACiC,MAAM,CAAC,kBAAkB,CAAC,CAACC,IAAI,EAAE;MAEpC;MAAA;MAAAvG,aAAA,GAAAE,CAAA;MACA,KAAK,MAAMsG,IAAI,IAAIL,mBAAmB,EAAE;QACtC,MAAMjE,QAAQ;QAAA;QAAA,CAAAlC,aAAA,GAAAE,CAAA,SAAG,IAAI,CAACO,WAAW,CAACc,GAAG,CAACiF,IAAI,CAACC,GAAG,CAACC,QAAQ,EAAE,CAAC;QAAC;QAAA1G,aAAA,GAAAE,CAAA;QAC3D,IAAIgC,QAAQ,EAAE;UAAA;UAAAlC,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAE,CAAA;UACZ;UACA,IAAIsG,IAAI,CAACR,YAAY,GAAG9D,QAAQ,CAACP,QAAQ,EAAE;YAAA;YAAA3B,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAE,CAAA;YACzCgC,QAAQ,CAACP,QAAQ,GAAG6E,IAAI,CAACR,YAAY;UACvC,CAAC;UAAA;UAAA;YAAAhG,aAAA,GAAAiB,CAAA;UAAA;QACH,CAAC;QAAA;QAAA;UAAAjB,aAAA,GAAAiB,CAAA;QAAA;MACH;MAAC;MAAAjB,aAAA,GAAAE,CAAA;MAEDG,QAAA,CAAA0B,MAAM,CAACC,KAAK,CAAC,4BAA4BmE,mBAAmB,CAAC9D,MAAM,QAAQ,CAAC;IAC9E,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA;MAAAjG,aAAA,GAAAE,CAAA;MACdG,QAAA,CAAA0B,MAAM,CAACkE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC9D;EACF;EAEA;;;EAGOU,OAAOA,CAAA;IAAA;IAAA3G,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAE,CAAA;IACZ,IAAI,IAAI,CAACU,eAAe,EAAE;MAAA;MAAAZ,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAE,CAAA;MACxB0G,aAAa,CAAC,IAAI,CAAChG,eAAe,CAAC;IACrC,CAAC;IAAA;IAAA;MAAAZ,aAAA,GAAAiB,CAAA;IAAA;IAED;IAAAjB,aAAA,GAAAE,CAAA;IACA,KAAK,MAAM,CAACiB,MAAM,EAAEe,QAAQ,CAAC,IAAI,IAAI,CAACzB,WAAW,CAACuD,OAAO,EAAE,EAAE;MAAA;MAAAhE,aAAA,GAAAE,CAAA;MAC3DgC,QAAQ,CAACb,MAAM,GAAG,SAAS;MAAC;MAAArB,aAAA,GAAAE,CAAA;MAC5BgC,QAAQ,CAACV,SAAS,GAAG,EAAE;MAAC;MAAAxB,aAAA,GAAAE,CAAA;MACxB,IAAI,CAAC4B,oBAAoB,CAACX,MAAM,CAAC;IACnC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAED,IAAI,CAACO,WAAW,CAACoG,KAAK,EAAE;IAAC;IAAA7G,aAAA,GAAAE,CAAA;IACzB,IAAI,CAACS,cAAc,CAACkG,KAAK,EAAE;IAAC;IAAA7G,aAAA,GAAAE,CAAA;IAE5BG,QAAA,CAAA0B,MAAM,CAAC+E,IAAI,CAAC,6BAA6B,CAAC;EAC5C;;AACD;AAAA9G,aAAA,GAAAE,CAAA;AAzWD6G,OAAA,CAAAzG,eAAA,GAAAA,eAAA", "ignoreList": []}