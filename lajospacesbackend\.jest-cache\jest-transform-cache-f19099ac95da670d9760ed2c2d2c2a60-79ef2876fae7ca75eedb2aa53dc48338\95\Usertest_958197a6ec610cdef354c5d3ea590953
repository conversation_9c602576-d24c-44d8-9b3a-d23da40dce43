fc4fc41ddde6bf55a707891077ee096e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const User_1 = require("../../../src/models/User");
const testDatabase_1 = require("../../../src/config/testDatabase");
const jest_setup_1 = require("../../setup/jest.setup");
describe('User Model', () => {
    beforeAll(async () => {
        await (0, testDatabase_1.setupTestDatabase)();
    });
    afterAll(async () => {
        await (0, testDatabase_1.cleanupTestDatabase)();
    });
    beforeEach(async () => {
        await (0, testDatabase_1.clearTestData)();
    });
    describe('User Creation', () => {
        it('should create a valid user', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            const user = new User_1.User(userData);
            const savedUser = await user.save();
            expect(savedUser._id).toBeDefined();
            expect(savedUser.email).toBe(userData.email);
            expect(savedUser.firstName).toBe(userData.firstName);
            expect(savedUser.lastName).toBe(userData.lastName);
            expect(savedUser.phoneNumber).toBe(userData.phoneNumber);
            expect(savedUser.role).toBe('user');
            expect(savedUser.emailVerified).toBe(true);
            expect(savedUser.isActive).toBe(true);
            expect(savedUser).toHaveValidTimestamps();
        });
        it('should hash password before saving', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: 'plaintext123' });
            const user = new User_1.User(userData);
            const savedUser = await user.save();
            expect(savedUser.password).not.toBe('plaintext123');
            expect(savedUser.password).toMatch(/^\$2[aby]\$\d+\$/); // bcrypt hash pattern
        });
        it('should generate full name virtual', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({
                firstName: 'John',
                lastName: 'Doe'
            });
            const user = new User_1.User(userData);
            expect(user.fullName).toBe('John Doe');
        });
        it('should set default values correctly', async () => {
            const userData = {
                firstName: 'Test',
                lastName: 'User',
                email: jest_setup_1.testUtils.randomEmail(),
                password: 'password123',
                phoneNumber: jest_setup_1.testUtils.randomPhoneNumber()
            };
            const user = new User_1.User(userData);
            const savedUser = await user.save();
            expect(savedUser.role).toBe('user');
            expect(savedUser.emailVerified).toBe(false);
            expect(savedUser.isActive).toBe(true);
            expect(savedUser.preferences).toBeDefined();
            expect(savedUser.preferences.notifications).toBe(true);
            expect(savedUser.preferences.emailUpdates).toBe(true);
        });
    });
    describe('User Validation', () => {
        it('should require firstName', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ firstName: undefined });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/firstName.*required/);
        });
        it('should require lastName', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ lastName: undefined });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/lastName.*required/);
        });
        it('should require email', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ email: undefined });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/email.*required/);
        });
        it('should validate email format', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ email: 'invalid-email' });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/email.*valid/);
        });
        it('should require unique email', async () => {
            const email = jest_setup_1.testUtils.randomEmail();
            const userData1 = jest_setup_1.testUtils.generateTestUser({ email });
            const userData2 = jest_setup_1.testUtils.generateTestUser({ email });
            const user1 = new User_1.User(userData1);
            await user1.save();
            const user2 = new User_1.User(userData2);
            await expect(user2.save()).rejects.toThrow(/email.*unique/);
        });
        it('should validate phone number format', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ phoneNumber: 'invalid-phone' });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/phoneNumber.*valid/);
        });
        it('should accept valid Nigerian phone numbers', async () => {
            const validPhones = [
                '+2348031234567',
                '+2347031234567',
                '+2349031234567',
                '+2348061234567'
            ];
            for (const phone of validPhones) {
                const userData = jest_setup_1.testUtils.generateTestUser({
                    email: jest_setup_1.testUtils.randomEmail(),
                    phoneNumber: phone
                });
                const user = new User_1.User(userData);
                const savedUser = await user.save();
                expect(savedUser.phoneNumber).toBe(phone);
            }
        });
        it('should validate role enum', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ role: 'invalid-role' });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/role.*enum/);
        });
        it('should validate password length', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: '123' });
            const user = new User_1.User(userData);
            await expect(user.save()).rejects.toThrow(/password.*6/);
        });
    });
    describe('User Methods', () => {
        let user;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: 'password123' });
            user = new User_1.User(userData);
            await user.save();
        });
        it('should compare password correctly', async () => {
            const isMatch = await user.comparePassword('password123');
            expect(isMatch).toBe(true);
            const isNotMatch = await user.comparePassword('wrongpassword');
            expect(isNotMatch).toBe(false);
        });
        it('should generate auth token', () => {
            const token = user.generateAuthToken();
            expect(typeof token).toBe('string');
            expect(token.split('.')).toHaveLength(3); // JWT format
        });
        it('should convert to JSON without password', () => {
            const userJSON = user.toJSON();
            expect(userJSON.password).toBeUndefined();
            expect(userJSON.email).toBe(user.email);
            expect(userJSON.firstName).toBe(user.firstName);
        });
    });
    describe('User Indexes', () => {
        it('should have email index', async () => {
            const indexes = await User_1.User.collection.getIndexes();
            const emailIndex = Object.keys(indexes).find(key => key.includes('email'));
            expect(emailIndex).toBeDefined();
        });
        it('should have phoneNumber index', async () => {
            const indexes = await User_1.User.collection.getIndexes();
            const phoneIndex = Object.keys(indexes).find(key => key.includes('phoneNumber'));
            expect(phoneIndex).toBeDefined();
        });
    });
    describe('User Profile', () => {
        it('should save profile information', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({
                profile: {
                    bio: 'Test bio',
                    occupation: 'Software Developer',
                    interests: ['technology', 'music'],
                    socialMedia: {
                        instagram: '@testuser',
                        twitter: '@testuser'
                    }
                }
            });
            const user = new User_1.User(userData);
            const savedUser = await user.save();
            expect(savedUser.profile.bio).toBe('Test bio');
            expect(savedUser.profile.occupation).toBe('Software Developer');
            expect(savedUser.profile.interests).toContain('technology');
            expect(savedUser.profile.socialMedia.instagram).toBe('@testuser');
        });
        it('should save roommate preferences', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({
                roommatePreferences: {
                    ageRange: { min: 25, max: 35 },
                    gender: 'any',
                    occupation: 'professional',
                    lifestyle: 'quiet',
                    cleanliness: 'very_clean',
                    smokingPolicy: 'no_smoking',
                    petPolicy: 'no_pets',
                    guestPolicy: 'occasional'
                }
            });
            const user = new User_1.User(userData);
            const savedUser = await user.save();
            expect(savedUser.roommatePreferences.ageRange.min).toBe(25);
            expect(savedUser.roommatePreferences.gender).toBe('any');
            expect(savedUser.roommatePreferences.smokingPolicy).toBe('no_smoking');
        });
    });
    describe('User Statics', () => {
        it('should find user by email', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            const user = new User_1.User(userData);
            await user.save();
            const foundUser = await User_1.User.findByEmail(userData.email);
            expect(foundUser).toBeDefined();
            expect(foundUser.email).toBe(userData.email);
        });
        it('should find user by phone number', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            const user = new User_1.User(userData);
            await user.save();
            const foundUser = await User_1.User.findByPhoneNumber(userData.phoneNumber);
            expect(foundUser).toBeDefined();
            expect(foundUser.phoneNumber).toBe(userData.phoneNumber);
        });
        it('should find active users', async () => {
            const activeUser = jest_setup_1.testUtils.generateTestUser({
                email: jest_setup_1.testUtils.randomEmail(),
                isActive: true
            });
            const inactiveUser = jest_setup_1.testUtils.generateTestUser({
                email: jest_setup_1.testUtils.randomEmail(),
                isActive: false
            });
            await User_1.User.create([activeUser, inactiveUser]);
            const activeUsers = await User_1.User.findActiveUsers();
            expect(activeUsers).toHaveLength(1);
            expect(activeUsers[0].isActive).toBe(true);
        });
        it('should find verified users', async () => {
            const verifiedUser = jest_setup_1.testUtils.generateTestUser({
                email: jest_setup_1.testUtils.randomEmail(),
                emailVerified: true
            });
            const unverifiedUser = jest_setup_1.testUtils.generateTestUser({
                email: jest_setup_1.testUtils.randomEmail(),
                emailVerified: false
            });
            await User_1.User.create([verifiedUser, unverifiedUser]);
            const verifiedUsers = await User_1.User.findVerifiedUsers();
            expect(verifiedUsers).toHaveLength(1);
            expect(verifiedUsers[0].emailVerified).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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