{"version": 3, "names": ["cov_1z8bquhe7f", "actualCoverage", "handlebars_1", "s", "__importDefault", "require", "logger_1", "environment_1", "EmailTemplateType", "f", "b", "exports", "EmailTemplateService", "constructor", "templates", "Map", "initializeTemplates", "set", "WELCOME", "subject", "html", "getWelcomeHTMLTemplate", "text", "getWelcomeTextTemplate", "EMAIL_VERIFICATION", "getEmailVerificationHTMLTemplate", "getEmailVerificationTextTemplate", "PASSWORD_RESET", "getPasswordResetHTMLTemplate", "getPasswordResetTextTemplate", "NEW_MESSAGE", "getNewMessageHTMLTemplate", "getNewMessageTextTemplate", "NEW_MATCH", "getNewMatchHTMLTemplate", "getNewMatchTextTemplate", "PROPERTY_POSTED", "getPropertyPostedHTMLTemplate", "getPropertyPostedTextTemplate", "logger", "info", "renderTemplate", "templateType", "data", "format", "template", "get", "Error", "templateData", "siteName", "siteUrl", "config", "FRONTEND_URL", "supportEmail", "currentYear", "Date", "getFullYear", "compiledTemplate", "default", "compile", "compiledSubject", "content", "error", "emailTemplateService"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\emailTemplateService.ts"], "sourcesContent": ["import handlebars from 'handlebars';\r\nimport { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\n\r\n// Template data interface\r\nexport interface TemplateData {\r\n  [key: string]: any;\r\n  // Common fields\r\n  userName?: string;\r\n  userEmail?: string;\r\n  siteName?: string;\r\n  siteUrl?: string;\r\n  supportEmail?: string;\r\n  currentYear?: number;\r\n}\r\n\r\n// Email template types\r\nexport enum EmailTemplateType {\r\n  WELCOME = 'welcome',\r\n  EMAIL_VERIFICATION = 'email_verification',\r\n  PASSWORD_RESET = 'password_reset',\r\n  PASSWORD_CHANGED = 'password_changed',\r\n  NEW_MESSAGE = 'new_message',\r\n  NEW_MATCH = 'new_match',\r\n  PROPERTY_POSTED = 'property_posted',\r\n  PROPERTY_APPROVED = 'property_approved',\r\n  SYSTEM_NOTIFICATION = 'system_notification',\r\n  NEWSLETTER = 'newsletter',\r\n  SECURITY_ALERT = 'security_alert'\r\n}\r\n\r\nclass EmailTemplateService {\r\n  private templates: Map<EmailTemplateType, { html: string; text: string; subject: string }> = new Map();\r\n\r\n  constructor() {\r\n    this.initializeTemplates();\r\n  }\r\n\r\n  /**\r\n   * Initialize email templates\r\n   */\r\n  private initializeTemplates(): void {\r\n    // Welcome email template\r\n    this.templates.set(EmailTemplateType.WELCOME, {\r\n      subject: 'Welcome to LajoSpaces! 🏠',\r\n      html: this.getWelcomeHTMLTemplate(),\r\n      text: this.getWelcomeTextTemplate()\r\n    });\r\n\r\n    // Email verification template\r\n    this.templates.set(EmailTemplateType.EMAIL_VERIFICATION, {\r\n      subject: 'Verify Your LajoSpaces Account',\r\n      html: this.getEmailVerificationHTMLTemplate(),\r\n      text: this.getEmailVerificationTextTemplate()\r\n    });\r\n\r\n    // Password reset template\r\n    this.templates.set(EmailTemplateType.PASSWORD_RESET, {\r\n      subject: 'Reset Your LajoSpaces Password',\r\n      html: this.getPasswordResetHTMLTemplate(),\r\n      text: this.getPasswordResetTextTemplate()\r\n    });\r\n\r\n    // New message template\r\n    this.templates.set(EmailTemplateType.NEW_MESSAGE, {\r\n      subject: 'New Message on LajoSpaces',\r\n      html: this.getNewMessageHTMLTemplate(),\r\n      text: this.getNewMessageTextTemplate()\r\n    });\r\n\r\n    // New match template\r\n    this.templates.set(EmailTemplateType.NEW_MATCH, {\r\n      subject: '🎉 New Roommate Match Found!',\r\n      html: this.getNewMatchHTMLTemplate(),\r\n      text: this.getNewMatchTextTemplate()\r\n    });\r\n\r\n    // Property posted template\r\n    this.templates.set(EmailTemplateType.PROPERTY_POSTED, {\r\n      subject: 'Property Posted Successfully',\r\n      html: this.getPropertyPostedHTMLTemplate(),\r\n      text: this.getPropertyPostedTextTemplate()\r\n    });\r\n\r\n    logger.info('Email templates initialized successfully');\r\n  }\r\n\r\n  /**\r\n   * Render email template\r\n   */\r\n  public renderTemplate(\r\n    templateType: EmailTemplateType,\r\n    data: TemplateData,\r\n    format: 'html' | 'text' = 'html'\r\n  ): { subject: string; content: string } {\r\n    const template = this.templates.get(templateType);\r\n    \r\n    if (!template) {\r\n      throw new Error(`Template not found: ${templateType}`);\r\n    }\r\n\r\n    // Add common data\r\n    const templateData = {\r\n      ...data,\r\n      siteName: 'LajoSpaces',\r\n      siteUrl: config.FRONTEND_URL,\r\n      supportEmail: '<EMAIL>',\r\n      currentYear: new Date().getFullYear()\r\n    };\r\n\r\n    try {\r\n      const compiledTemplate = handlebars.compile(template[format]);\r\n      const compiledSubject = handlebars.compile(template.subject);\r\n\r\n      return {\r\n        subject: compiledSubject(templateData),\r\n        content: compiledTemplate(templateData)\r\n      };\r\n    } catch (error) {\r\n      logger.error(`Error rendering template ${templateType}:`, error);\r\n      throw new Error(`Failed to render template: ${templateType}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Welcome email HTML template\r\n   */\r\n  private getWelcomeHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Welcome to {{siteName}}!</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #2563eb; }\r\n          .feature-icon { font-size: 24px; margin-right: 10px; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>Welcome to Nigeria's Premier Housing Platform!</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>🎉 Welcome to {{siteName}}! We're thrilled to have you join our community of housing seekers and providers across Nigeria.</p>\r\n            \r\n            <h3>What you can do now:</h3>\r\n            \r\n            <div class=\"feature\">\r\n              <span class=\"feature-icon\">🔍</span>\r\n              <strong>Search Properties</strong><br>\r\n              Browse thousands of verified properties across Nigeria's major cities\r\n            </div>\r\n            \r\n            <div class=\"feature\">\r\n              <span class=\"feature-icon\">💬</span>\r\n              <strong>Find Roommates</strong><br>\r\n              Connect with compatible roommates using our smart matching system\r\n            </div>\r\n            \r\n            <div class=\"feature\">\r\n              <span class=\"feature-icon\">📝</span>\r\n              <strong>List Your Property</strong><br>\r\n              Post your available rooms and properties to reach thousands of potential tenants\r\n            </div>\r\n            \r\n            <div class=\"feature\">\r\n              <span class=\"feature-icon\">⭐</span>\r\n              <strong>Save Favorites</strong><br>\r\n              Bookmark properties you love and get notified of price changes\r\n            </div>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{siteUrl}}/dashboard\" class=\"button\">Start Exploring</a>\r\n            </div>\r\n            \r\n            <p>Need help getting started? Check out our <a href=\"{{siteUrl}}/help\">Help Center</a> or reply to this email.</p>\r\n            \r\n            <p>Happy house hunting!</p>\r\n            <p><strong>The {{siteName}} Team</strong></p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p>Nigeria's trusted housing platform</p>\r\n            <p><a href=\"{{siteUrl}}/unsubscribe?email={{userEmail}}\">Unsubscribe</a> | <a href=\"{{siteUrl}}/privacy\">Privacy Policy</a></p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Welcome email text template\r\n   */\r\n  private getWelcomeTextTemplate(): string {\r\n    return `\r\n      Welcome to {{siteName}}!\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      Welcome to {{siteName}}! We're thrilled to have you join our community of housing seekers and providers across Nigeria.\r\n      \r\n      What you can do now:\r\n      \r\n      🔍 Search Properties\r\n      Browse thousands of verified properties across Nigeria's major cities\r\n      \r\n      💬 Find Roommates\r\n      Connect with compatible roommates using our smart matching system\r\n      \r\n      📝 List Your Property\r\n      Post your available rooms and properties to reach thousands of potential tenants\r\n      \r\n      ⭐ Save Favorites\r\n      Bookmark properties you love and get notified of price changes\r\n      \r\n      Start exploring: {{siteUrl}}/dashboard\r\n      \r\n      Need help getting started? Check out our Help Center: {{siteUrl}}/help\r\n      \r\n      Happy house hunting!\r\n      The {{siteName}} Team\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Nigeria's trusted housing platform\r\n      \r\n      Unsubscribe: {{siteUrl}}/unsubscribe?email={{userEmail}}\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Email verification HTML template\r\n   */\r\n  private getEmailVerificationHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Verify Your {{siteName}} Account</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: #2563eb; color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>Verify Your Account</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>Thank you for joining {{siteName}}! To complete your registration and start finding your perfect home, please verify your email address.</p>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{verificationUrl}}\" class=\"button\">Verify My Account</a>\r\n            </div>\r\n            \r\n            <p>Or copy and paste this link into your browser:</p>\r\n            <p style=\"word-break: break-all; color: #2563eb; background: #e5f3ff; padding: 10px; border-radius: 5px;\">{{verificationUrl}}</p>\r\n            \r\n            <div class=\"warning\">\r\n              <strong>⚠️ Important:</strong>\r\n              <ul>\r\n                <li>This verification link will expire in 24 hours</li>\r\n                <li>If you didn't create an account with {{siteName}}, please ignore this email</li>\r\n              </ul>\r\n            </div>\r\n            \r\n            <p>Once verified, you'll have access to all {{siteName}} features including property search, roommate matching, and more!</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p>Nigeria's trusted housing platform</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Email verification text template\r\n   */\r\n  private getEmailVerificationTextTemplate(): string {\r\n    return `\r\n      Verify Your {{siteName}} Account\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      Thank you for joining {{siteName}}! To complete your registration and start finding your perfect home, please verify your email address.\r\n      \r\n      Click here to verify: {{verificationUrl}}\r\n      \r\n      IMPORTANT:\r\n      - This verification link will expire in 24 hours\r\n      - If you didn't create an account with {{siteName}}, please ignore this email\r\n      \r\n      Once verified, you'll have access to all {{siteName}} features including property search, roommate matching, and more!\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Nigeria's trusted housing platform\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Password reset HTML template\r\n   */\r\n  private getPasswordResetHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Reset Your {{siteName}} Password</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: #dc2626; color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>Password Reset Request</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>We received a request to reset your {{siteName}} account password. If you made this request, click the button below:</p>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{resetUrl}}\" class=\"button\">Reset My Password</a>\r\n            </div>\r\n            \r\n            <p>Or copy and paste this link into your browser:</p>\r\n            <p style=\"word-break: break-all; color: #dc2626; background: #fee2e2; padding: 10px; border-radius: 5px;\">{{resetUrl}}</p>\r\n            \r\n            <div class=\"warning\">\r\n              <strong>⚠️ Security Information:</strong>\r\n              <ul>\r\n                <li>This password reset link will expire in 1 hour</li>\r\n                <li>If you didn't request this reset, please ignore this email</li>\r\n                <li>Your password will remain unchanged until you create a new one</li>\r\n              </ul>\r\n            </div>\r\n            \r\n            <p>For your security, we recommend choosing a strong password with at least 8 characters, including uppercase and lowercase letters, numbers, and special characters.</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p>Nigeria's trusted housing platform</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Password reset text template\r\n   */\r\n  private getPasswordResetTextTemplate(): string {\r\n    return `\r\n      Password Reset Request - {{siteName}}\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      We received a request to reset your {{siteName}} account password. If you made this request, use the link below:\r\n      \r\n      {{resetUrl}}\r\n      \r\n      SECURITY INFORMATION:\r\n      - This password reset link will expire in 1 hour\r\n      - If you didn't request this reset, please ignore this email\r\n      - Your password will remain unchanged until you create a new one\r\n      \r\n      For your security, we recommend choosing a strong password with at least 8 characters, including uppercase and lowercase letters, numbers, and special characters.\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Nigeria's trusted housing platform\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * New message HTML template\r\n   */\r\n  private getNewMessageHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>New Message on {{siteName}}</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: #059669; color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .message-preview { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin: 20px 0; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>💬 New Message</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>You have a new message from <strong>{{senderName}}</strong>:</p>\r\n            \r\n            <div class=\"message-preview\">\r\n              <p><strong>{{messagePreview}}</strong></p>\r\n            </div>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{messageUrl}}\" class=\"button\">View Message</a>\r\n            </div>\r\n            \r\n            <p>Don't want to receive message notifications? <a href=\"{{siteUrl}}/settings/notifications\">Update your preferences</a>.</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p><a href=\"{{siteUrl}}/unsubscribe?email={{userEmail}}\">Unsubscribe</a></p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * New message text template\r\n   */\r\n  private getNewMessageTextTemplate(): string {\r\n    return `\r\n      New Message on {{siteName}}\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      You have a new message from {{senderName}}:\r\n      \r\n      \"{{messagePreview}}\"\r\n      \r\n      View message: {{messageUrl}}\r\n      \r\n      Don't want to receive message notifications? Update your preferences: {{siteUrl}}/settings/notifications\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Unsubscribe: {{siteUrl}}/unsubscribe?email={{userEmail}}\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * New match HTML template\r\n   */\r\n  private getNewMatchHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>New Roommate Match - {{siteName}}</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #f59e0b; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .match-info { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0; }\r\n          .compatibility { font-size: 24px; font-weight: bold; color: #f59e0b; text-align: center; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>🎉 New Roommate Match!</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>Great news! We found a potential roommate match for you with high compatibility.</p>\r\n            \r\n            <div class=\"match-info\">\r\n              <div class=\"compatibility\">{{compatibilityScore}}% Compatible</div>\r\n              <p><strong>Match Type:</strong> {{matchType}}</p>\r\n              <p><strong>Location:</strong> {{location}}</p>\r\n              <p><strong>Budget Range:</strong> {{budgetRange}}</p>\r\n            </div>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{matchUrl}}\" class=\"button\">View Match</a>\r\n            </div>\r\n            \r\n            <p>Don't miss out on this opportunity to find your perfect roommate!</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p><a href=\"{{siteUrl}}/unsubscribe?email={{userEmail}}\">Unsubscribe</a></p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * New match text template\r\n   */\r\n  private getNewMatchTextTemplate(): string {\r\n    return `\r\n      🎉 New Roommate Match! - {{siteName}}\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      Great news! We found a potential roommate match for you with {{compatibilityScore}}% compatibility.\r\n      \r\n      Match Details:\r\n      - Type: {{matchType}}\r\n      - Location: {{location}}\r\n      - Budget Range: {{budgetRange}}\r\n      \r\n      View match: {{matchUrl}}\r\n      \r\n      Don't miss out on this opportunity to find your perfect roommate!\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Unsubscribe: {{siteUrl}}/unsubscribe?email={{userEmail}}\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Property posted HTML template\r\n   */\r\n  private getPropertyPostedHTMLTemplate(): string {\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Property Posted Successfully - {{siteName}}</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: #059669; color: white; padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9fafb; padding: 40px 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; font-weight: bold; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\r\n          .property-info { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #059669; margin: 20px 0; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <div class=\"logo\">🏠 {{siteName}}</div>\r\n            <h1>✅ Property Posted Successfully!</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello {{userName}}!</h2>\r\n            <p>Great news! Your property listing has been successfully posted on {{siteName}} and is now visible to thousands of potential tenants.</p>\r\n            \r\n            <div class=\"property-info\">\r\n              <p><strong>Property:</strong> {{propertyTitle}}</p>\r\n              <p><strong>Location:</strong> {{propertyLocation}}</p>\r\n              <p><strong>Price:</strong> {{propertyPrice}}</p>\r\n              <p><strong>Status:</strong> Active</p>\r\n            </div>\r\n            \r\n            <div style=\"text-align: center; margin: 30px 0;\">\r\n              <a href=\"{{propertyUrl}}\" class=\"button\">View Listing</a>\r\n            </div>\r\n            \r\n            <p>Your listing is now being promoted to relevant users. You'll receive notifications when users show interest in your property.</p>\r\n            \r\n            <p><strong>Tips to get more inquiries:</strong></p>\r\n            <ul>\r\n              <li>Add high-quality photos</li>\r\n              <li>Write a detailed description</li>\r\n              <li>Respond quickly to inquiries</li>\r\n              <li>Keep your listing updated</li>\r\n            </ul>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>© {{currentYear}} {{siteName}}. All rights reserved.</p>\r\n            <p><a href=\"{{siteUrl}}/unsubscribe?email={{userEmail}}\">Unsubscribe</a></p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * Property posted text template\r\n   */\r\n  private getPropertyPostedTextTemplate(): string {\r\n    return `\r\n      Property Posted Successfully - {{siteName}}\r\n      \r\n      Hello {{userName}}!\r\n      \r\n      Great news! Your property listing has been successfully posted on {{siteName}} and is now visible to thousands of potential tenants.\r\n      \r\n      Property Details:\r\n      - Property: {{propertyTitle}}\r\n      - Location: {{propertyLocation}}\r\n      - Price: {{propertyPrice}}\r\n      - Status: Active\r\n      \r\n      View listing: {{propertyUrl}}\r\n      \r\n      Your listing is now being promoted to relevant users. You'll receive notifications when users show interest in your property.\r\n      \r\n      Tips to get more inquiries:\r\n      - Add high-quality photos\r\n      - Write a detailed description\r\n      - Respond quickly to inquiries\r\n      - Keep your listing updated\r\n      \r\n      © {{currentYear}} {{siteName}}. All rights reserved.\r\n      Unsubscribe: {{siteUrl}}/unsubscribe?email={{userEmail}}\r\n    `;\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const emailTemplateService = new EmailTemplateService();\r\n\r\nexport default emailTemplateService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArBF,MAAAE,YAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,MAAAE,aAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAE,OAAA;AAcA;AACA,IAAYG,iBAYX;AAAA;AAAAR,cAAA,GAAAG,CAAA;AAZD,WAAYK,iBAAiB;EAAA;EAAAR,cAAA,GAAAS,CAAA;EAAAT,cAAA,GAAAG,CAAA;EAC3BK,iBAAA,uBAAmB;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACnBK,iBAAA,6CAAyC;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACzCK,iBAAA,qCAAiC;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACjCK,iBAAA,yCAAqC;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACrCK,iBAAA,+BAA2B;EAAA;EAAAR,cAAA,GAAAG,CAAA;EAC3BK,iBAAA,2BAAuB;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACvBK,iBAAA,uCAAmC;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACnCK,iBAAA,2CAAuC;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACvCK,iBAAA,+CAA2C;EAAA;EAAAR,cAAA,GAAAG,CAAA;EAC3CK,iBAAA,6BAAyB;EAAA;EAAAR,cAAA,GAAAG,CAAA;EACzBK,iBAAA,qCAAiC;AACnC,CAAC;AAZW;AAAA,CAAAR,cAAA,GAAAU,CAAA,UAAAF,iBAAiB;AAAA;AAAA,CAAAR,cAAA,GAAAU,CAAA,UAAAC,OAAA,CAAAH,iBAAA,GAAjBA,iBAAiB;AAc7B,MAAMI,oBAAoB;EAGxBC,YAAA;IAAA;IAAAb,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAFQ,KAAAW,SAAS,GAA4E,IAAIC,GAAG,EAAE;IAAC;IAAAf,cAAA,GAAAG,CAAA;IAGrG,IAAI,CAACa,mBAAmB,EAAE;EAC5B;EAEA;;;EAGQA,mBAAmBA,CAAA;IAAA;IAAAhB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACzB;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAACU,OAAO,EAAE;MAC5CC,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,IAAI,CAACC,sBAAsB,EAAE;MACnCC,IAAI,EAAE,IAAI,CAACC,sBAAsB;KAClC,CAAC;IAEF;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAACgB,kBAAkB,EAAE;MACvDL,OAAO,EAAE,gCAAgC;MACzCC,IAAI,EAAE,IAAI,CAACK,gCAAgC,EAAE;MAC7CH,IAAI,EAAE,IAAI,CAACI,gCAAgC;KAC5C,CAAC;IAEF;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAACmB,cAAc,EAAE;MACnDR,OAAO,EAAE,gCAAgC;MACzCC,IAAI,EAAE,IAAI,CAACQ,4BAA4B,EAAE;MACzCN,IAAI,EAAE,IAAI,CAACO,4BAA4B;KACxC,CAAC;IAEF;IAAA;IAAA7B,cAAA,GAAAG,CAAA;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAACsB,WAAW,EAAE;MAChDX,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,IAAI,CAACW,yBAAyB,EAAE;MACtCT,IAAI,EAAE,IAAI,CAACU,yBAAyB;KACrC,CAAC;IAEF;IAAA;IAAAhC,cAAA,GAAAG,CAAA;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAACyB,SAAS,EAAE;MAC9Cd,OAAO,EAAE,8BAA8B;MACvCC,IAAI,EAAE,IAAI,CAACc,uBAAuB,EAAE;MACpCZ,IAAI,EAAE,IAAI,CAACa,uBAAuB;KACnC,CAAC;IAEF;IAAA;IAAAnC,cAAA,GAAAG,CAAA;IACA,IAAI,CAACW,SAAS,CAACG,GAAG,CAACT,iBAAiB,CAAC4B,eAAe,EAAE;MACpDjB,OAAO,EAAE,8BAA8B;MACvCC,IAAI,EAAE,IAAI,CAACiB,6BAA6B,EAAE;MAC1Cf,IAAI,EAAE,IAAI,CAACgB,6BAA6B;KACzC,CAAC;IAAC;IAAAtC,cAAA,GAAAG,CAAA;IAEHG,QAAA,CAAAiC,MAAM,CAACC,IAAI,CAAC,0CAA0C,CAAC;EACzD;EAEA;;;EAGOC,cAAcA,CACnBC,YAA+B,EAC/BC,IAAkB,EAClBC,MAAA;EAAA;EAAA,CAAA5C,cAAA,GAAAU,CAAA,UAA0B,MAAM;IAAA;IAAAV,cAAA,GAAAS,CAAA;IAEhC,MAAMoC,QAAQ;IAAA;IAAA,CAAA7C,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACW,SAAS,CAACgC,GAAG,CAACJ,YAAY,CAAC;IAAC;IAAA1C,cAAA,GAAAG,CAAA;IAElD,IAAI,CAAC0C,QAAQ,EAAE;MAAA;MAAA7C,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAG,CAAA;MACb,MAAM,IAAI4C,KAAK,CAAC,uBAAuBL,YAAY,EAAE,CAAC;IACxD,CAAC;IAAA;IAAA;MAAA1C,cAAA,GAAAU,CAAA;IAAA;IAED;IACA,MAAMsC,YAAY;IAAA;IAAA,CAAAhD,cAAA,GAAAG,CAAA,QAAG;MACnB,GAAGwC,IAAI;MACPM,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE3C,aAAA,CAAA4C,MAAM,CAACC,YAAY;MAC5BC,YAAY,EAAE,wBAAwB;MACtCC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KACpC;IAAC;IAAAxD,cAAA,GAAAG,CAAA;IAEF,IAAI;MACF,MAAMsD,gBAAgB;MAAA;MAAA,CAAAzD,cAAA,GAAAG,CAAA,QAAGD,YAAA,CAAAwD,OAAU,CAACC,OAAO,CAACd,QAAQ,CAACD,MAAM,CAAC,CAAC;MAC7D,MAAMgB,eAAe;MAAA;MAAA,CAAA5D,cAAA,GAAAG,CAAA,QAAGD,YAAA,CAAAwD,OAAU,CAACC,OAAO,CAACd,QAAQ,CAAC1B,OAAO,CAAC;MAAC;MAAAnB,cAAA,GAAAG,CAAA;MAE7D,OAAO;QACLgB,OAAO,EAAEyC,eAAe,CAACZ,YAAY,CAAC;QACtCa,OAAO,EAAEJ,gBAAgB,CAACT,YAAY;OACvC;IACH,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAG,CAAA;MACdG,QAAA,CAAAiC,MAAM,CAACuB,KAAK,CAAC,4BAA4BpB,YAAY,GAAG,EAAEoB,KAAK,CAAC;MAAC;MAAA9D,cAAA,GAAAG,CAAA;MACjE,MAAM,IAAI4C,KAAK,CAAC,8BAA8BL,YAAY,EAAE,CAAC;IAC/D;EACF;EAEA;;;EAGQrB,sBAAsBA,CAAA;IAAA;IAAArB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC5B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwEN;EACH;EAEA;;;EAGQoB,sBAAsBA,CAAA;IAAA;IAAAvB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC5B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgCN;EACH;EAEA;;;EAGQsB,gCAAgCA,CAAA;IAAA;IAAAzB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACtC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoDN;EACH;EAEA;;;EAGQuB,gCAAgCA,CAAA;IAAA;IAAA1B,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACtC,OAAO;;;;;;;;;;;;;;;;;KAiBN;EACH;EAEA;;;EAGQyB,4BAA4BA,CAAA;IAAA;IAAA5B,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAClC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqDN;EACH;EAEA;;;EAGQ0B,4BAA4BA,CAAA;IAAA;IAAA7B,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAClC,OAAO;;;;;;;;;;;;;;;;;;KAkBN;EACH;EAEA;;;EAGQ4B,yBAAyBA,CAAA;IAAA;IAAA/B,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC/B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6CN;EACH;EAEA;;;EAGQ6B,yBAAyBA,CAAA;IAAA;IAAAhC,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC/B,OAAO;;;;;;;;;;;;;;;KAeN;EACH;EAEA;;;EAGQ+B,uBAAuBA,CAAA;IAAA;IAAAlC,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC7B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiDN;EACH;EAEA;;;EAGQgC,uBAAuBA,CAAA;IAAA;IAAAnC,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IAC7B,OAAO;;;;;;;;;;;;;;;;;;KAkBN;EACH;EAEA;;;EAGQkC,6BAA6BA,CAAA;IAAA;IAAArC,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACnC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwDN;EACH;EAEA;;;EAGQmC,6BAA6BA,CAAA;IAAA;IAAAtC,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAG,CAAA;IACnC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;KAyBN;EACH;;AAGF;AAAA;AAAAH,cAAA,GAAAG,CAAA;AACaQ,OAAA,CAAAoD,oBAAoB,GAAG,IAAInD,oBAAoB,EAAE;AAAC;AAAAZ,cAAA,GAAAG,CAAA;AAE/DQ,OAAA,CAAA+C,OAAA,GAAe/C,OAAA,CAAAoD,oBAAoB", "ignoreList": []}