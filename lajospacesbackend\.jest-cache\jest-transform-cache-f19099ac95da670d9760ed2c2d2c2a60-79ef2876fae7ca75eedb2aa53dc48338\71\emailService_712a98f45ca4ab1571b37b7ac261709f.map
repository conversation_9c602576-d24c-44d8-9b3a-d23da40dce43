{"version": 3, "names": ["cov_gesy55vpo", "actualCoverage", "exports", "sendEmail", "s", "sendWelcomeEmail", "sendVerificationEmail", "sendPasswordResetEmail", "sendPasswordChangedEmail", "nodemailer_1", "__importDefault", "require", "environment_1", "logger_1", "transporter", "default", "createTransport", "host", "config", "SMTP_HOST", "port", "SMTP_PORT", "secure", "auth", "user", "SMTP_USER", "pass", "SMTP_PASS", "tls", "rejectUnauthorized", "requireTLS", "logger", "info", "emailTemplates", "welcome", "data", "f", "subject", "html", "firstName", "lastName", "FRONTEND_URL", "text", "verification", "verificationLink", "resetLink", "Date", "toLocaleString", "timeZone", "options", "emailContent", "b", "template", "templateFunction", "Error", "mailOptions", "from", "FROM_NAME", "FROM_EMAIL", "to", "result", "sendMail", "messageId", "error", "verificationToken", "resetToken"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\emailService.ts"], "sourcesContent": ["import nodemailer from 'nodemailer';\r\nimport { config } from '../config/environment';\r\nimport { logger } from '../utils/logger';\r\n\r\n// Email template interface\r\ninterface EmailTemplate {\r\n  subject: string;\r\n  html: string;\r\n  text: string;\r\n}\r\n\r\n// Email options interface\r\ninterface EmailOptions {\r\n  to: string;\r\n  subject: string;\r\n  html?: string;\r\n  text?: string;\r\n  template?: 'welcome' | 'verification' | 'password-reset' | 'password-changed';\r\n  data?: Record<string, any>;\r\n}\r\n\r\n// Create transporter\r\nconst transporter = nodemailer.createTransport({\r\n  host: config.SMTP_HOST,\r\n  port: config.SMTP_PORT,\r\n  secure: false, // false for 587 (STARTTLS), true for 465 (SSL)\r\n  auth: {\r\n    user: config.SMTP_USER,\r\n    pass: config.SMTP_PASS\r\n  },\r\n  tls: {\r\n    rejectUnauthorized: false\r\n  },\r\n  requireTLS: true\r\n});\r\n\r\n// Verify transporter configuration (temporarily disabled to isolate startup issues)\r\n// transporter.verify((error: any, _success: any) => {\r\n//   if (error) {\r\n//     logger.error('Email transporter configuration error:', error);\r\n//   } else {\r\n//     logger.info('✅ Email service ready');\r\n//   }\r\n// });\r\n\r\n// Log immediate readiness for startup\r\nlogger.info('📧 Email service initialized (verification disabled for testing)');\r\n\r\n/**\r\n * Email templates\r\n */\r\nconst emailTemplates = {\r\n  welcome: (data: { firstName: string; lastName: string }): EmailTemplate => ({\r\n    subject: 'Welcome to LajoSpaces! 🏠',\r\n    html: `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Welcome to LajoSpaces</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <h1>Welcome to LajoSpaces! 🏠</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello ${data.firstName} ${data.lastName}!</h2>\r\n            <p>Welcome to LajoSpaces, Nigeria's premier roommate matching platform! We're excited to help you find your perfect living situation.</p>\r\n            \r\n            <h3>What's Next?</h3>\r\n            <ul>\r\n              <li>✅ Complete your profile to get better matches</li>\r\n              <li>🔍 Browse available rooms and roommates</li>\r\n              <li>💬 Start connecting with potential roommates</li>\r\n              <li>🏠 Find your perfect home in Nigeria</li>\r\n            </ul>\r\n            \r\n            <p>Our platform is designed specifically for the Nigerian market, covering all major cities from Lagos to Abuja, Port Harcourt to Kano.</p>\r\n            \r\n            <div style=\"text-align: center;\">\r\n              <a href=\"${config.FRONTEND_URL}/dashboard\" class=\"button\">Complete Your Profile</a>\r\n            </div>\r\n            \r\n            <p>If you have any questions, our support team is here to help!</p>\r\n            \r\n            <p>Best regards,<br>The LajoSpaces Team</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>LajoSpaces - Connecting Roommates Across Nigeria</p>\r\n            <p>This email was sent to you because you created an account on LajoSpaces.</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `,\r\n    text: `\r\n      Welcome to LajoSpaces, ${data.firstName} ${data.lastName}!\r\n      \r\n      We're excited to help you find your perfect living situation in Nigeria.\r\n      \r\n      What's Next?\r\n      - Complete your profile to get better matches\r\n      - Browse available rooms and roommates\r\n      - Start connecting with potential roommates\r\n      - Find your perfect home in Nigeria\r\n      \r\n      Visit ${config.FRONTEND_URL}/dashboard to get started.\r\n      \r\n      Best regards,\r\n      The LajoSpaces Team\r\n    `\r\n  }),\r\n\r\n  verification: (data: { firstName: string; verificationLink: string }): EmailTemplate => ({\r\n    subject: 'Verify Your LajoSpaces Account 📧',\r\n    html: `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Verify Your Account</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }\r\n          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <h1>Verify Your Account 📧</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello ${data.firstName}!</h2>\r\n            <p>Thank you for joining LajoSpaces! To complete your registration and start finding roommates, please verify your email address.</p>\r\n            \r\n            <div style=\"text-align: center;\">\r\n              <a href=\"${data.verificationLink}\" class=\"button\">Verify Email Address</a>\r\n            </div>\r\n            \r\n            <div class=\"warning\">\r\n              <strong>⚠️ Important:</strong> This verification link will expire in 24 hours for security reasons.\r\n            </div>\r\n            \r\n            <p>If the button doesn't work, copy and paste this link into your browser:</p>\r\n            <p style=\"word-break: break-all; background: #f1f1f1; padding: 10px; border-radius: 5px;\">${data.verificationLink}</p>\r\n            \r\n            <p>If you didn't create an account with LajoSpaces, please ignore this email.</p>\r\n            \r\n            <p>Best regards,<br>The LajoSpaces Team</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>LajoSpaces - Connecting Roommates Across Nigeria</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `,\r\n    text: `\r\n      Hello ${data.firstName}!\r\n      \r\n      Thank you for joining LajoSpaces! To complete your registration, please verify your email address by clicking the link below:\r\n      \r\n      ${data.verificationLink}\r\n      \r\n      This link will expire in 24 hours for security reasons.\r\n      \r\n      If you didn't create an account with LajoSpaces, please ignore this email.\r\n      \r\n      Best regards,\r\n      The LajoSpaces Team\r\n    `\r\n  }),\r\n\r\n  'password-reset': (data: { firstName: string; resetLink: string }): EmailTemplate => ({\r\n    subject: 'Reset Your LajoSpaces Password 🔐',\r\n    html: `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Reset Your Password</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\r\n          .button { display: inline-block; background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }\r\n          .warning { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <h1>Reset Your Password 🔐</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello ${data.firstName}!</h2>\r\n            <p>We received a request to reset your LajoSpaces account password. Click the button below to create a new password:</p>\r\n            \r\n            <div style=\"text-align: center;\">\r\n              <a href=\"${data.resetLink}\" class=\"button\">Reset Password</a>\r\n            </div>\r\n            \r\n            <div class=\"warning\">\r\n              <strong>⚠️ Security Notice:</strong> This password reset link will expire in 1 hour for your security.\r\n            </div>\r\n            \r\n            <p>If the button doesn't work, copy and paste this link into your browser:</p>\r\n            <p style=\"word-break: break-all; background: #f1f1f1; padding: 10px; border-radius: 5px;\">${data.resetLink}</p>\r\n            \r\n            <p><strong>If you didn't request this password reset, please ignore this email.</strong> Your password will remain unchanged.</p>\r\n            \r\n            <p>For security reasons, we recommend:</p>\r\n            <ul>\r\n              <li>Using a strong, unique password</li>\r\n              <li>Not sharing your password with anyone</li>\r\n              <li>Logging out of shared devices</li>\r\n            </ul>\r\n            \r\n            <p>Best regards,<br>The LajoSpaces Team</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>LajoSpaces - Connecting Roommates Across Nigeria</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `,\r\n    text: `\r\n      Hello ${data.firstName}!\r\n      \r\n      We received a request to reset your LajoSpaces account password. Click the link below to create a new password:\r\n      \r\n      ${data.resetLink}\r\n      \r\n      This link will expire in 1 hour for your security.\r\n      \r\n      If you didn't request this password reset, please ignore this email.\r\n      \r\n      Best regards,\r\n      The LajoSpaces Team\r\n    `\r\n  }),\r\n\r\n  'password-changed': (data: { firstName: string }): EmailTemplate => ({\r\n    subject: 'Your LajoSpaces Password Has Been Changed ✅',\r\n    html: `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Password Changed</title>\r\n        <style>\r\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\r\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\r\n          .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }\r\n          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }\r\n          .alert { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }\r\n          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"container\">\r\n          <div class=\"header\">\r\n            <h1>Password Changed Successfully ✅</h1>\r\n          </div>\r\n          <div class=\"content\">\r\n            <h2>Hello ${data.firstName}!</h2>\r\n            <p>This email confirms that your LajoSpaces account password has been successfully changed.</p>\r\n            \r\n            <div class=\"alert\">\r\n              <strong>🔐 Security Confirmation:</strong> Your password was changed on ${new Date().toLocaleString('en-NG', { timeZone: 'Africa/Lagos' })} (WAT).\r\n            </div>\r\n            \r\n            <p><strong>If you made this change:</strong> No further action is required. Your account is secure.</p>\r\n            \r\n            <p><strong>If you didn't make this change:</strong> Please contact our support team <NAME_EMAIL> or log into your account to secure it.</p>\r\n            \r\n            <p>For your security, we recommend:</p>\r\n            <ul>\r\n              <li>Using a unique password for your LajoSpaces account</li>\r\n              <li>Enabling two-factor authentication when available</li>\r\n              <li>Regularly reviewing your account activity</li>\r\n            </ul>\r\n            \r\n            <p>Best regards,<br>The LajoSpaces Team</p>\r\n          </div>\r\n          <div class=\"footer\">\r\n            <p>LajoSpaces - Connecting Roommates Across Nigeria</p>\r\n          </div>\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `,\r\n    text: `\r\n      Hello ${data.firstName}!\r\n      \r\n      This email confirms that your LajoSpaces account password has been successfully changed on ${new Date().toLocaleString('en-NG', { timeZone: 'Africa/Lagos' })} (WAT).\r\n      \r\n      If you made this change: No further action is required.\r\n      \r\n      If you didn't make this change: Please contact our support team immediately.\r\n      \r\n      Best regards,\r\n      The LajoSpaces Team\r\n    `\r\n  })\r\n};\r\n\r\n/**\r\n * Send email\r\n */\r\nexport async function sendEmail(options: EmailOptions): Promise<void> {\r\n  try {\r\n    let emailContent: EmailTemplate;\r\n\r\n    if (options.template && options.data) {\r\n      // Use template\r\n      const templateFunction = emailTemplates[options.template];\r\n      if (!templateFunction) {\r\n        throw new Error(`Email template '${options.template}' not found`);\r\n      }\r\n      emailContent = templateFunction(options.data as any);\r\n    } else {\r\n      // Use custom content\r\n      emailContent = {\r\n        subject: options.subject,\r\n        html: options.html || '',\r\n        text: options.text || ''\r\n      };\r\n    }\r\n\r\n    const mailOptions = {\r\n      from: `${config.FROM_NAME} <${config.FROM_EMAIL}>`,\r\n      to: options.to,\r\n      subject: emailContent.subject,\r\n      html: emailContent.html,\r\n      text: emailContent.text\r\n    };\r\n\r\n    const result = await transporter.sendMail(mailOptions);\r\n    \r\n    logger.info(`Email sent successfully to ${options.to}`, {\r\n      messageId: result.messageId,\r\n      template: options.template || 'custom'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error(`Failed to send email to ${options.to}:`, error);\r\n    throw new Error('Failed to send email');\r\n  }\r\n}\r\n\r\n/**\r\n * Send welcome email\r\n */\r\nexport async function sendWelcomeEmail(to: string, firstName: string, lastName: string): Promise<void> {\r\n  await sendEmail({\r\n    to,\r\n    template: 'welcome',\r\n    subject: '', // Will be overridden by template\r\n    data: { firstName, lastName }\r\n  });\r\n}\r\n\r\n/**\r\n * Send email verification\r\n */\r\nexport async function sendVerificationEmail(to: string, firstName: string, verificationToken: string): Promise<void> {\r\n  const verificationLink = `${config.FRONTEND_URL}/verify-email?token=${verificationToken}`;\r\n  \r\n  await sendEmail({\r\n    to,\r\n    template: 'verification',\r\n    subject: '', // Will be overridden by template\r\n    data: { firstName, verificationLink }\r\n  });\r\n}\r\n\r\n/**\r\n * Send password reset email\r\n */\r\nexport async function sendPasswordResetEmail(to: string, firstName: string, resetToken: string): Promise<void> {\r\n  const resetLink = `${config.FRONTEND_URL}/reset-password?token=${resetToken}`;\r\n  \r\n  await sendEmail({\r\n    to,\r\n    template: 'password-reset',\r\n    subject: '', // Will be overridden by template\r\n    data: { firstName, resetLink }\r\n  });\r\n}\r\n\r\n/**\r\n * Send password changed confirmation\r\n */\r\nexport async function sendPasswordChangedEmail(to: string, firstName: string): Promise<void> {\r\n  await sendEmail({\r\n    to,\r\n    template: 'password-changed',\r\n    subject: '', // Will be overridden by template\r\n    data: { firstName }\r\n  });\r\n}\r\n\r\nexport default {\r\n  sendEmail,\r\n  sendWelcomeEmail,\r\n  sendVerificationEmail,\r\n  sendPasswordResetEmail,\r\n  sendPasswordChangedEmail\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuBQ;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoTRE,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAuCC;AAAAH,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAG,gBAAA,GAAAA,gBAAA;AAOC;AAAAL,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAI,qBAAA,GAAAA,qBAAA;AASC;AAAAN,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAK,sBAAA,GAAAA,sBAAA;AASC;AAAAP,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAM,wBAAA,GAAAA,wBAAA;AA/ZA,MAAAC,YAAA;AAAA;AAAA,CAAAT,aAAA,GAAAI,CAAA,OAAAM,eAAA,CAAAC,OAAA;AACA,MAAAC,aAAA;AAAA;AAAA,CAAAZ,aAAA,GAAAI,CAAA,OAAAO,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAb,aAAA,GAAAI,CAAA,QAAAO,OAAA;AAmBA;AACA,MAAMG,WAAW;AAAA;AAAA,CAAAd,aAAA,GAAAI,CAAA,QAAGK,YAAA,CAAAM,OAAU,CAACC,eAAe,CAAC;EAC7CC,IAAI,EAAEL,aAAA,CAAAM,MAAM,CAACC,SAAS;EACtBC,IAAI,EAAER,aAAA,CAAAM,MAAM,CAACG,SAAS;EACtBC,MAAM,EAAE,KAAK;EAAE;EACfC,IAAI,EAAE;IACJC,IAAI,EAAEZ,aAAA,CAAAM,MAAM,CAACO,SAAS;IACtBC,IAAI,EAAEd,aAAA,CAAAM,MAAM,CAACS;GACd;EACDC,GAAG,EAAE;IACHC,kBAAkB,EAAE;GACrB;EACDC,UAAU,EAAE;CACb,CAAC;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA9B,aAAA,GAAAI,CAAA;AACAS,QAAA,CAAAkB,MAAM,CAACC,IAAI,CAAC,kEAAkE,CAAC;AAE/E;;;AAGA,MAAMC,cAAc;AAAA;AAAA,CAAAjC,aAAA,GAAAI,CAAA,QAAG;EACrB8B,OAAO,EAAGC,IAA6C,IAAqB;IAAA;IAAAnC,aAAA,GAAAoC,CAAA;IAAApC,aAAA,GAAAI,CAAA;IAAA;MAC1EiC,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;wBAsBcH,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ;;;;;;;;;;;;;;yBAc9B5B,aAAA,CAAAM,MAAM,CAACuB,YAAY;;;;;;;;;;;;;;KAcvC;MACDC,IAAI,EAAE;+BACqBP,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ;;;;;;;;;;cAUhD5B,aAAA,CAAAM,MAAM,CAACuB,YAAY;;;;;KAK9B;GAAC;EAEFE,YAAY,EAAGR,IAAqD,IAAqB;IAAA;IAAAnC,aAAA,GAAAoC,CAAA;IAAApC,aAAA,GAAAI,CAAA;IAAA;MACvFiC,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;wBAuBcH,IAAI,CAACI,SAAS;;;;yBAIbJ,IAAI,CAACS,gBAAgB;;;;;;;;wGAQ0DT,IAAI,CAACS,gBAAgB;;;;;;;;;;;;KAYxH;MACDF,IAAI,EAAE;cACIP,IAAI,CAACI,SAAS;;;;QAIpBJ,IAAI,CAACS,gBAAgB;;;;;;;;;KAS1B;GAAC;EAEF,gBAAgB,EAAGT,IAA8C,IAAqB;IAAA;IAAAnC,aAAA,GAAAoC,CAAA;IAAApC,aAAA,GAAAI,CAAA;IAAA;MACpFiC,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;wBAuBcH,IAAI,CAACI,SAAS;;;;yBAIbJ,IAAI,CAACU,SAAS;;;;;;;;wGAQiEV,IAAI,CAACU,SAAS;;;;;;;;;;;;;;;;;;;KAmBjH;MACDH,IAAI,EAAE;cACIP,IAAI,CAACI,SAAS;;;;QAIpBJ,IAAI,CAACU,SAAS;;;;;;;;;KASnB;GAAC;EAEF,kBAAkB,EAAGV,IAA2B,IAAqB;IAAA;IAAAnC,aAAA,GAAAoC,CAAA;IAAApC,aAAA,GAAAI,CAAA;IAAA;MACnEiC,OAAO,EAAE,6CAA6C;MACtDC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;wBAsBcH,IAAI,CAACI,SAAS;;;;wFAIkD,IAAIO,IAAI,EAAE,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAc,CAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;KAsBnJ;MACDN,IAAI,EAAE;cACIP,IAAI,CAACI,SAAS;;mGAEuE,IAAIO,IAAI,EAAE,CAACC,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAc,CAAE,CAAC;;;;;;;;;KAShK;;CACF;AAED;;;AAGO,eAAe7C,SAASA,CAAC8C,OAAqB;EAAA;EAAAjD,aAAA,GAAAoC,CAAA;EAAApC,aAAA,GAAAI,CAAA;EACnD,IAAI;IACF,IAAI8C,YAA2B;IAAC;IAAAlD,aAAA,GAAAI,CAAA;IAEhC;IAAI;IAAA,CAAAJ,aAAA,GAAAmD,CAAA,UAAAF,OAAO,CAACG,QAAQ;IAAA;IAAA,CAAApD,aAAA,GAAAmD,CAAA,UAAIF,OAAO,CAACd,IAAI,GAAE;MAAA;MAAAnC,aAAA,GAAAmD,CAAA;MACpC;MACA,MAAME,gBAAgB;MAAA;MAAA,CAAArD,aAAA,GAAAI,CAAA,QAAG6B,cAAc,CAACgB,OAAO,CAACG,QAAQ,CAAC;MAAC;MAAApD,aAAA,GAAAI,CAAA;MAC1D,IAAI,CAACiD,gBAAgB,EAAE;QAAA;QAAArD,aAAA,GAAAmD,CAAA;QAAAnD,aAAA,GAAAI,CAAA;QACrB,MAAM,IAAIkD,KAAK,CAAC,mBAAmBL,OAAO,CAACG,QAAQ,aAAa,CAAC;MACnE,CAAC;MAAA;MAAA;QAAApD,aAAA,GAAAmD,CAAA;MAAA;MAAAnD,aAAA,GAAAI,CAAA;MACD8C,YAAY,GAAGG,gBAAgB,CAACJ,OAAO,CAACd,IAAW,CAAC;IACtD,CAAC,MAAM;MAAA;MAAAnC,aAAA,GAAAmD,CAAA;MAAAnD,aAAA,GAAAI,CAAA;MACL;MACA8C,YAAY,GAAG;QACbb,OAAO,EAAEY,OAAO,CAACZ,OAAO;QACxBC,IAAI;QAAE;QAAA,CAAAtC,aAAA,GAAAmD,CAAA,UAAAF,OAAO,CAACX,IAAI;QAAA;QAAA,CAAAtC,aAAA,GAAAmD,CAAA,UAAI,EAAE;QACxBT,IAAI;QAAE;QAAA,CAAA1C,aAAA,GAAAmD,CAAA,UAAAF,OAAO,CAACP,IAAI;QAAA;QAAA,CAAA1C,aAAA,GAAAmD,CAAA,UAAI,EAAE;OACzB;IACH;IAEA,MAAMI,WAAW;IAAA;IAAA,CAAAvD,aAAA,GAAAI,CAAA,QAAG;MAClBoD,IAAI,EAAE,GAAG5C,aAAA,CAAAM,MAAM,CAACuC,SAAS,KAAK7C,aAAA,CAAAM,MAAM,CAACwC,UAAU,GAAG;MAClDC,EAAE,EAAEV,OAAO,CAACU,EAAE;MACdtB,OAAO,EAAEa,YAAY,CAACb,OAAO;MAC7BC,IAAI,EAAEY,YAAY,CAACZ,IAAI;MACvBI,IAAI,EAAEQ,YAAY,CAACR;KACpB;IAED,MAAMkB,MAAM;IAAA;IAAA,CAAA5D,aAAA,GAAAI,CAAA,QAAG,MAAMU,WAAW,CAAC+C,QAAQ,CAACN,WAAW,CAAC;IAAC;IAAAvD,aAAA,GAAAI,CAAA;IAEvDS,QAAA,CAAAkB,MAAM,CAACC,IAAI,CAAC,8BAA8BiB,OAAO,CAACU,EAAE,EAAE,EAAE;MACtDG,SAAS,EAAEF,MAAM,CAACE,SAAS;MAC3BV,QAAQ;MAAE;MAAA,CAAApD,aAAA,GAAAmD,CAAA,UAAAF,OAAO,CAACG,QAAQ;MAAA;MAAA,CAAApD,aAAA,GAAAmD,CAAA,UAAI,QAAQ;KACvC,CAAC;EAEJ,CAAC,CAAC,OAAOY,KAAK,EAAE;IAAA;IAAA/D,aAAA,GAAAI,CAAA;IACdS,QAAA,CAAAkB,MAAM,CAACgC,KAAK,CAAC,2BAA2Bd,OAAO,CAACU,EAAE,GAAG,EAAEI,KAAK,CAAC;IAAC;IAAA/D,aAAA,GAAAI,CAAA;IAC9D,MAAM,IAAIkD,KAAK,CAAC,sBAAsB,CAAC;EACzC;AACF;AAEA;;;AAGO,eAAejD,gBAAgBA,CAACsD,EAAU,EAAEpB,SAAiB,EAAEC,QAAgB;EAAA;EAAAxC,aAAA,GAAAoC,CAAA;EAAApC,aAAA,GAAAI,CAAA;EACpF,MAAMD,SAAS,CAAC;IACdwD,EAAE;IACFP,QAAQ,EAAE,SAAS;IACnBf,OAAO,EAAE,EAAE;IAAE;IACbF,IAAI,EAAE;MAAEI,SAAS;MAAEC;IAAQ;GAC5B,CAAC;AACJ;AAEA;;;AAGO,eAAelC,qBAAqBA,CAACqD,EAAU,EAAEpB,SAAiB,EAAEyB,iBAAyB;EAAA;EAAAhE,aAAA,GAAAoC,CAAA;EAClG,MAAMQ,gBAAgB;EAAA;EAAA,CAAA5C,aAAA,GAAAI,CAAA,QAAG,GAAGQ,aAAA,CAAAM,MAAM,CAACuB,YAAY,uBAAuBuB,iBAAiB,EAAE;EAAC;EAAAhE,aAAA,GAAAI,CAAA;EAE1F,MAAMD,SAAS,CAAC;IACdwD,EAAE;IACFP,QAAQ,EAAE,cAAc;IACxBf,OAAO,EAAE,EAAE;IAAE;IACbF,IAAI,EAAE;MAAEI,SAAS;MAAEK;IAAgB;GACpC,CAAC;AACJ;AAEA;;;AAGO,eAAerC,sBAAsBA,CAACoD,EAAU,EAAEpB,SAAiB,EAAE0B,UAAkB;EAAA;EAAAjE,aAAA,GAAAoC,CAAA;EAC5F,MAAMS,SAAS;EAAA;EAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAG,GAAGQ,aAAA,CAAAM,MAAM,CAACuB,YAAY,yBAAyBwB,UAAU,EAAE;EAAC;EAAAjE,aAAA,GAAAI,CAAA;EAE9E,MAAMD,SAAS,CAAC;IACdwD,EAAE;IACFP,QAAQ,EAAE,gBAAgB;IAC1Bf,OAAO,EAAE,EAAE;IAAE;IACbF,IAAI,EAAE;MAAEI,SAAS;MAAEM;IAAS;GAC7B,CAAC;AACJ;AAEA;;;AAGO,eAAerC,wBAAwBA,CAACmD,EAAU,EAAEpB,SAAiB;EAAA;EAAAvC,aAAA,GAAAoC,CAAA;EAAApC,aAAA,GAAAI,CAAA;EAC1E,MAAMD,SAAS,CAAC;IACdwD,EAAE;IACFP,QAAQ,EAAE,kBAAkB;IAC5Bf,OAAO,EAAE,EAAE;IAAE;IACbF,IAAI,EAAE;MAAEI;IAAS;GAClB,CAAC;AACJ;AAAC;AAAAvC,aAAA,GAAAI,CAAA;AAEDF,OAAA,CAAAa,OAAA,GAAe;EACbZ,SAAS;EACTE,gBAAgB;EAChBC,qBAAqB;EACrBC,sBAAsB;EACtBC;CACD", "ignoreList": []}