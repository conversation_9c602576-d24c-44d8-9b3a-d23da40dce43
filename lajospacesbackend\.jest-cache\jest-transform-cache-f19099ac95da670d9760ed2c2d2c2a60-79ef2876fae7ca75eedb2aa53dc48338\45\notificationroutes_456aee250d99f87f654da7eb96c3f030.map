{"version": 3, "names": ["cov_25l1wm8dhf", "actualCoverage", "s", "express_1", "require", "notification_controller_1", "auth_1", "validation_1", "notification_validators_1", "router", "Router", "use", "authenticate", "get", "validateRequest", "getUserNotificationsSchema", "getUserNotifications", "put", "validateObjectId", "markNotificationAsRead", "markAllNotificationsAsRead", "delete", "dismissNotification", "getNotificationStats", "getEmailPreferences", "updateEmailPreferencesSchema", "updateEmailPreferences", "post", "unsubscribeFromAllEmails", "resubscribeToEmails", "req", "res", "f", "json", "success", "message", "timestamp", "Date", "toISOString", "features", "inAppNotifications", "emailNotifications", "pushNotifications", "smsNotifications", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\notification.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  getUserNotifications,\r\n  markNotificationAsRead,\r\n  markAllNotificationsAsRead,\r\n  dismissNotification,\r\n  getNotificationStats,\r\n  getEmailPreferences,\r\n  updateEmailPreferences,\r\n  unsubscribeFromAllEmails,\r\n  resubscribeToEmails\r\n} from '../controllers/notification.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  getUserNotificationsSchema,\r\n  updateEmailPreferencesSchema\r\n} from '../validators/notification.validators';\r\n\r\nconst router = Router();\r\n\r\n// All notification routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   GET /api/notifications\r\n * @desc    Get user notifications with pagination\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(getUserNotificationsSchema, 'query'),\r\n  getUserNotifications\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/:notificationId/read\r\n * @desc    Mark notification as read\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:notificationId/read',\r\n  validateObjectId('notificationId'),\r\n  markNotificationAsRead\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/read-all\r\n * @desc    Mark all notifications as read\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/read-all',\r\n  markAllNotificationsAsRead\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/notifications/:notificationId\r\n * @desc    Dismiss notification\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:notificationId',\r\n  validateObjectId('notificationId'),\r\n  dismissNotification\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/stats\r\n * @desc    Get notification statistics\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/stats',\r\n  getNotificationStats\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/email-preferences\r\n * @desc    Get user email preferences\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/email-preferences',\r\n  getEmailPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/notifications/email-preferences\r\n * @desc    Update user email preferences\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/email-preferences',\r\n  validateRequest(updateEmailPreferencesSchema, 'body'),\r\n  updateEmailPreferences\r\n);\r\n\r\n/**\r\n * @route   POST /api/notifications/unsubscribe-all\r\n * @desc    Unsubscribe from all emails\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/unsubscribe-all',\r\n  unsubscribeFromAllEmails\r\n);\r\n\r\n/**\r\n * @route   POST /api/notifications/resubscribe\r\n * @desc    Resubscribe to emails\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/resubscribe',\r\n  resubscribeToEmails\r\n);\r\n\r\n/**\r\n * @route   GET /api/notifications/health\r\n * @desc    Health check for notification service\r\n * @access  Private\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Notification service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    features: {\r\n      inAppNotifications: 'active',\r\n      emailNotifications: 'active',\r\n      pushNotifications: 'planned',\r\n      smsNotifications: 'planned'\r\n    }\r\n  });\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8BE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AA9BF,MAAAC,SAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,yBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAWA,MAAAE,MAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,yBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAKA,MAAMK,MAAM;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAO,MAAM,GAAE;AAEvB;AAAA;AAAAV,cAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAACL,MAAA,CAAAM,YAAY,CAAC;AAExB;;;;;AAAA;AAAAZ,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACI,GAAG,CACR,GAAG,EACH,IAAAN,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAO,0BAA0B,EAAE,OAAO,CAAC,EACpDV,yBAAA,CAAAW,oBAAoB,CACrB;AAED;;;;;AAAA;AAAAhB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACQ,GAAG,CACR,uBAAuB,EACvB,IAAAV,YAAA,CAAAW,gBAAgB,EAAC,gBAAgB,CAAC,EAClCb,yBAAA,CAAAc,sBAAsB,CACvB;AAED;;;;;AAAA;AAAAnB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACQ,GAAG,CACR,WAAW,EACXZ,yBAAA,CAAAe,0BAA0B,CAC3B;AAED;;;;;AAAA;AAAApB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACY,MAAM,CACX,kBAAkB,EAClB,IAAAd,YAAA,CAAAW,gBAAgB,EAAC,gBAAgB,CAAC,EAClCb,yBAAA,CAAAiB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAAtB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACI,GAAG,CACR,QAAQ,EACRR,yBAAA,CAAAkB,oBAAoB,CACrB;AAED;;;;;AAAA;AAAAvB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACI,GAAG,CACR,oBAAoB,EACpBR,yBAAA,CAAAmB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAAxB,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACQ,GAAG,CACR,oBAAoB,EACpB,IAAAV,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAiB,4BAA4B,EAAE,MAAM,CAAC,EACrDpB,yBAAA,CAAAqB,sBAAsB,CACvB;AAED;;;;;AAAA;AAAA1B,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACkB,IAAI,CACT,kBAAkB,EAClBtB,yBAAA,CAAAuB,wBAAwB,CACzB;AAED;;;;;AAAA;AAAA5B,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACkB,IAAI,CACT,cAAc,EACdtB,yBAAA,CAAAwB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAA7B,cAAA,GAAAE,CAAA;AAKAO,MAAM,CAACI,GAAG,CAAC,SAAS,EAAE,CAACiB,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAA/B,cAAA,GAAAgC,CAAA;EAAAhC,cAAA,GAAAE,CAAA;EACjC6B,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,iCAAiC;IAC1CC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,QAAQ,EAAE;MACRC,kBAAkB,EAAE,QAAQ;MAC5BC,kBAAkB,EAAE,QAAQ;MAC5BC,iBAAiB,EAAE,SAAS;MAC5BC,gBAAgB,EAAE;;GAErB,CAAC;AACJ,CAAC,CAAC;AAAC;AAAA3C,cAAA,GAAAE,CAAA;AAEH0C,OAAA,CAAAC,OAAA,GAAepC,MAAM", "ignoreList": []}