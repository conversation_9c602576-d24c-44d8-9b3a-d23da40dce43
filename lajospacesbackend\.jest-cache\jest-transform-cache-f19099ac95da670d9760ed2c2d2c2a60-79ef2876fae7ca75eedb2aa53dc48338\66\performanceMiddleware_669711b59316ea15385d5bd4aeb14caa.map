{"version": 3, "names": ["cov_2nbetw55qf", "actualCoverage", "s", "exports", "performanceTrackingMiddleware", "requestTimeoutMiddleware", "memoryMonitoringMiddleware", "rateLimitMonitoringMiddleware", "databaseMonitoringMiddleware", "monitoringMiddleware", "perf_hooks_1", "require", "performanceMonitoringService_1", "errorTrackingService_1", "logger_1", "generateRequestId", "f", "Date", "now", "Math", "random", "toString", "substr", "req", "res", "next", "b", "path", "startsWith", "startTime", "performance", "requestId", "<PERSON><PERSON><PERSON><PERSON>", "on", "endTime", "responseTime", "userId", "user", "id", "_id", "userAgent", "get", "ip", "connection", "remoteAddress", "metrics", "method", "endpoint", "route", "statusCode", "timestamp", "performanceMonitoringService", "trackRequest", "logLevel", "logger", "toFixed", "substring", "errorTrackingService", "trackError", "Error", "ErrorSeverity", "HIGH", "Error<PERSON>ate<PERSON><PERSON>", "SYSTEM", "LOW", "VALIDATION", "timeoutMs", "timeout", "setTimeout", "headersSent", "error", "PERFORMANCE", "status", "json", "success", "clearTimeout", "memoryBefore", "process", "memoryUsage", "memoryAfter", "<PERSON><PERSON><PERSON><PERSON>", "heapUsed", "heapTotal", "rss", "external", "warn", "MEDIUM", "SECURITY", "originalQuery", "query", "queryCount", "totalQueryTime", "trackQuery", "queryTime", "averageQueryTime", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\performanceMiddleware.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { performance } from 'perf_hooks';\r\nimport { performanceMonitoringService } from '../services/performanceMonitoringService';\r\nimport { errorTrackingService, ErrorSeverity, ErrorCategory } from '../services/errorTrackingService';\r\nimport { logger } from '../utils/logger';\r\n\r\n/**\r\n * Performance monitoring middleware\r\n * Tracks request performance and integrates with monitoring services\r\n */\r\n\r\ninterface ExtendedRequest extends Request {\r\n  startTime?: number;\r\n  requestId?: string;\r\n}\r\n\r\n/**\r\n * Generate unique request ID\r\n */\r\nfunction generateRequestId(): string {\r\n  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n}\r\n\r\n/**\r\n * Performance tracking middleware\r\n */\r\nexport function performanceTrackingMiddleware(\r\n  req: ExtendedRequest,\r\n  res: Response,\r\n  next: NextFunction\r\n): void {\r\n  // Skip health checks and static files\r\n  if (req.path === '/health' || req.path.startsWith('/static')) {\r\n    return next();\r\n  }\r\n\r\n  const startTime = performance.now();\r\n  const requestId = generateRequestId();\r\n  \r\n  req.startTime = startTime;\r\n  req.requestId = requestId;\r\n\r\n  // Add request ID to response headers\r\n  res.setHeader('X-Request-ID', requestId);\r\n\r\n  // Track when response finishes\r\n  res.on('finish', () => {\r\n    const endTime = performance.now();\r\n    const responseTime = endTime - startTime;\r\n\r\n    // Extract user info if available\r\n    const userId = (req as any).user?.id || (req as any).user?._id;\r\n    const userAgent = req.get('User-Agent') || 'unknown';\r\n    const ip = req.ip || req.connection.remoteAddress || 'unknown';\r\n\r\n    // Create performance metrics\r\n    const metrics = {\r\n      method: req.method,\r\n      endpoint: req.route?.path || req.path,\r\n      statusCode: res.statusCode,\r\n      responseTime,\r\n      timestamp: new Date(),\r\n      userId,\r\n      ip,\r\n      userAgent\r\n    };\r\n\r\n    // Track the request\r\n    performanceMonitoringService.trackRequest(metrics);\r\n\r\n    // Log performance data\r\n    const logLevel = responseTime > 2000 ? 'warn' : responseTime > 1000 ? 'info' : 'debug';\r\n    logger[logLevel]('Request completed', {\r\n      requestId,\r\n      method: req.method,\r\n      endpoint: metrics.endpoint,\r\n      statusCode: res.statusCode,\r\n      responseTime: `${responseTime.toFixed(2)}ms`,\r\n      userId,\r\n      ip,\r\n      userAgent: userAgent.substring(0, 100) // Truncate long user agents\r\n    });\r\n\r\n    // Track errors if status code indicates failure\r\n    if (res.statusCode >= 500) {\r\n      errorTrackingService.trackError(\r\n        new Error(`Server error: ${res.statusCode}`),\r\n        {\r\n          requestId,\r\n          endpoint: metrics.endpoint,\r\n          method: req.method,\r\n          ip,\r\n          userAgent,\r\n          userId\r\n        },\r\n        ErrorSeverity.HIGH,\r\n        ErrorCategory.SYSTEM\r\n      );\r\n    } else if (res.statusCode >= 400) {\r\n      errorTrackingService.trackError(\r\n        new Error(`Client error: ${res.statusCode}`),\r\n        {\r\n          requestId,\r\n          endpoint: metrics.endpoint,\r\n          method: req.method,\r\n          ip,\r\n          userAgent,\r\n          userId\r\n        },\r\n        ErrorSeverity.LOW,\r\n        ErrorCategory.VALIDATION\r\n      );\r\n    }\r\n  });\r\n\r\n  next();\r\n}\r\n\r\n/**\r\n * Request timeout middleware\r\n */\r\nexport function requestTimeoutMiddleware(timeoutMs: number = 30000) {\r\n  return (req: ExtendedRequest, res: Response, next: NextFunction): void => {\r\n    const timeout = setTimeout(() => {\r\n      if (!res.headersSent) {\r\n        const error = new Error('Request timeout');\r\n        \r\n        errorTrackingService.trackError(\r\n          error,\r\n          {\r\n            requestId: req.requestId,\r\n            endpoint: req.route?.path || req.path,\r\n            method: req.method,\r\n            ip: req.ip,\r\n            userAgent: req.get('User-Agent'),\r\n            timeout: timeoutMs\r\n          },\r\n          ErrorSeverity.HIGH,\r\n          ErrorCategory.PERFORMANCE\r\n        );\r\n\r\n        res.status(408).json({\r\n          success: false,\r\n          error: 'Request timeout',\r\n          requestId: req.requestId,\r\n          timeout: timeoutMs\r\n        });\r\n      }\r\n    }, timeoutMs);\r\n\r\n    res.on('finish', () => {\r\n      clearTimeout(timeout);\r\n    });\r\n\r\n    res.on('close', () => {\r\n      clearTimeout(timeout);\r\n    });\r\n\r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * Memory monitoring middleware\r\n */\r\nexport function memoryMonitoringMiddleware(\r\n  req: ExtendedRequest,\r\n  res: Response,\r\n  next: NextFunction\r\n): void {\r\n  const memoryBefore = process.memoryUsage();\r\n\r\n  res.on('finish', () => {\r\n    const memoryAfter = process.memoryUsage();\r\n    const memoryDelta = {\r\n      heapUsed: memoryAfter.heapUsed - memoryBefore.heapUsed,\r\n      heapTotal: memoryAfter.heapTotal - memoryBefore.heapTotal,\r\n      rss: memoryAfter.rss - memoryBefore.rss,\r\n      external: memoryAfter.external - memoryBefore.external\r\n    };\r\n\r\n    // Log significant memory increases\r\n    if (memoryDelta.heapUsed > 10 * 1024 * 1024) { // 10MB increase\r\n      logger.warn('Significant memory increase detected', {\r\n        requestId: req.requestId,\r\n        endpoint: req.route?.path || req.path,\r\n        method: req.method,\r\n        memoryDelta: {\r\n          heapUsed: `${(memoryDelta.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n          rss: `${(memoryDelta.rss / 1024 / 1024).toFixed(2)} MB`\r\n        },\r\n        memoryAfter: {\r\n          heapUsed: `${(memoryAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n          heapTotal: `${(memoryAfter.heapTotal / 1024 / 1024).toFixed(2)} MB`,\r\n          rss: `${(memoryAfter.rss / 1024 / 1024).toFixed(2)} MB`\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  next();\r\n}\r\n\r\n/**\r\n * Rate limiting monitoring middleware\r\n */\r\nexport function rateLimitMonitoringMiddleware(\r\n  req: ExtendedRequest,\r\n  res: Response,\r\n  next: NextFunction\r\n): void {\r\n  // Check if request was rate limited\r\n  res.on('finish', () => {\r\n    if (res.statusCode === 429) {\r\n      errorTrackingService.trackError(\r\n        new Error('Rate limit exceeded'),\r\n        {\r\n          requestId: req.requestId,\r\n          endpoint: req.route?.path || req.path,\r\n          method: req.method,\r\n          ip: req.ip,\r\n          userAgent: req.get('User-Agent')\r\n        },\r\n        ErrorSeverity.MEDIUM,\r\n        ErrorCategory.SECURITY\r\n      );\r\n\r\n      logger.warn('Rate limit exceeded', {\r\n        requestId: req.requestId,\r\n        endpoint: req.route?.path || req.path,\r\n        method: req.method,\r\n        ip: req.ip,\r\n        userAgent: req.get('User-Agent')\r\n      });\r\n    }\r\n  });\r\n\r\n  next();\r\n}\r\n\r\n/**\r\n * Database query monitoring middleware\r\n */\r\nexport function databaseMonitoringMiddleware(\r\n  req: ExtendedRequest,\r\n  res: Response,\r\n  next: NextFunction\r\n): void {\r\n  const originalQuery = req.query;\r\n  let queryCount = 0;\r\n  let totalQueryTime = 0;\r\n\r\n  // Mock query tracking (in real implementation, this would hook into Mongoose)\r\n  const trackQuery = (queryTime: number) => {\r\n    queryCount++;\r\n    totalQueryTime += queryTime;\r\n  };\r\n\r\n  // Add query tracker to request\r\n  (req as any).trackQuery = trackQuery;\r\n\r\n  res.on('finish', () => {\r\n    if (queryCount > 10) {\r\n      logger.warn('High database query count detected', {\r\n        requestId: req.requestId,\r\n        endpoint: req.route?.path || req.path,\r\n        method: req.method,\r\n        queryCount,\r\n        totalQueryTime: `${totalQueryTime.toFixed(2)}ms`,\r\n        averageQueryTime: `${(totalQueryTime / queryCount).toFixed(2)}ms`\r\n      });\r\n    }\r\n\r\n    if (totalQueryTime > 1000) {\r\n      logger.warn('Slow database queries detected', {\r\n        requestId: req.requestId,\r\n        endpoint: req.route?.path || req.path,\r\n        method: req.method,\r\n        queryCount,\r\n        totalQueryTime: `${totalQueryTime.toFixed(2)}ms`\r\n      });\r\n    }\r\n  });\r\n\r\n  next();\r\n}\r\n\r\n/**\r\n * Combined monitoring middleware\r\n */\r\nexport function monitoringMiddleware(\r\n  req: ExtendedRequest,\r\n  res: Response,\r\n  next: NextFunction\r\n): void {\r\n  // Apply all monitoring middlewares\r\n  performanceTrackingMiddleware(req, res, () => {\r\n    memoryMonitoringMiddleware(req, res, () => {\r\n      rateLimitMonitoringMiddleware(req, res, () => {\r\n        databaseMonitoringMiddleware(req, res, next);\r\n      });\r\n    });\r\n  });\r\n}\r\n\r\nexport default {\r\n  performanceTrackingMiddleware,\r\n  requestTimeoutMiddleware,\r\n  memoryMonitoringMiddleware,\r\n  rateLimitMonitoringMiddleware,\r\n  databaseMonitoringMiddleware,\r\n  monitoringMiddleware\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AAOTC,OAAA,CAAAC,6BAAA,GAAAA,6BAAA;AA0FC;AAAAJ,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAE,wBAAA,GAAAA,wBAAA;AAuCC;AAAAL,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAG,0BAAA,GAAAA,0BAAA;AAoCC;AAAAN,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAI,6BAAA,GAAAA,6BAAA;AAgCC;AAAAP,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAK,4BAAA,GAAAA,4BAAA;AA0CC;AAAAR,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAM,oBAAA,GAAAA,oBAAA;AAjSA,MAAAC,YAAA;AAAA;AAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAS,OAAA;AACA,MAAAC,8BAAA;AAAA;AAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAS,OAAA;AACA,MAAAE,sBAAA;AAAA;AAAA,CAAAb,cAAA,GAAAE,CAAA,OAAAS,OAAA;AACA,MAAAG,QAAA;AAAA;AAAA,CAAAd,cAAA,GAAAE,CAAA,QAAAS,OAAA;AAYA;;;AAGA,SAASI,iBAAiBA,CAAA;EAAA;EAAAf,cAAA,GAAAgB,CAAA;EAAAhB,cAAA,GAAAE,CAAA;EACxB,OAAO,OAAOe,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AACvE;AAEA;;;AAGA,SAAgBlB,6BAA6BA,CAC3CmB,GAAoB,EACpBC,GAAa,EACbC,IAAkB;EAAA;EAAAzB,cAAA,GAAAgB,CAAA;EAAAhB,cAAA,GAAAE,CAAA;EAElB;EACA;EAAI;EAAA,CAAAF,cAAA,GAAA0B,CAAA,UAAAH,GAAG,CAACI,IAAI,KAAK,SAAS;EAAA;EAAA,CAAA3B,cAAA,GAAA0B,CAAA,UAAIH,GAAG,CAACI,IAAI,CAACC,UAAU,CAAC,SAAS,CAAC,GAAE;IAAA;IAAA5B,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAE,CAAA;IAC5D,OAAOuB,IAAI,EAAE;EACf,CAAC;EAAA;EAAA;IAAAzB,cAAA,GAAA0B,CAAA;EAAA;EAED,MAAMG,SAAS;EAAA;EAAA,CAAA7B,cAAA,GAAAE,CAAA,QAAGQ,YAAA,CAAAoB,WAAW,CAACZ,GAAG,EAAE;EACnC,MAAMa,SAAS;EAAA;EAAA,CAAA/B,cAAA,GAAAE,CAAA,QAAGa,iBAAiB,EAAE;EAAC;EAAAf,cAAA,GAAAE,CAAA;EAEtCqB,GAAG,CAACM,SAAS,GAAGA,SAAS;EAAC;EAAA7B,cAAA,GAAAE,CAAA;EAC1BqB,GAAG,CAACQ,SAAS,GAAGA,SAAS;EAEzB;EAAA;EAAA/B,cAAA,GAAAE,CAAA;EACAsB,GAAG,CAACQ,SAAS,CAAC,cAAc,EAAED,SAAS,CAAC;EAExC;EAAA;EAAA/B,cAAA,GAAAE,CAAA;EACAsB,GAAG,CAACS,EAAE,CAAC,QAAQ,EAAE,MAAK;IAAA;IAAAjC,cAAA,GAAAgB,CAAA;IACpB,MAAMkB,OAAO;IAAA;IAAA,CAAAlC,cAAA,GAAAE,CAAA,QAAGQ,YAAA,CAAAoB,WAAW,CAACZ,GAAG,EAAE;IACjC,MAAMiB,YAAY;IAAA;IAAA,CAAAnC,cAAA,GAAAE,CAAA,QAAGgC,OAAO,GAAGL,SAAS;IAExC;IACA,MAAMO,MAAM;IAAA;IAAA,CAAApC,cAAA,GAAAE,CAAA;IAAI;IAAA,CAAAF,cAAA,GAAA0B,CAAA,UAAAH,GAAW,CAACc,IAAI,EAAEC,EAAE;IAAA;IAAA,CAAAtC,cAAA,GAAA0B,CAAA,UAAKH,GAAW,CAACc,IAAI,EAAEE,GAAG;IAC9D,MAAMC,SAAS;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,cAAA,GAAA0B,CAAA,UAAAH,GAAG,CAACkB,GAAG,CAAC,YAAY,CAAC;IAAA;IAAA,CAAAzC,cAAA,GAAA0B,CAAA,UAAI,SAAS;IACpD,MAAMgB,EAAE;IAAA;IAAA,CAAA1C,cAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,cAAA,GAAA0B,CAAA,UAAAH,GAAG,CAACmB,EAAE;IAAA;IAAA,CAAA1C,cAAA,GAAA0B,CAAA,UAAIH,GAAG,CAACoB,UAAU,CAACC,aAAa;IAAA;IAAA,CAAA5C,cAAA,GAAA0B,CAAA,UAAI,SAAS;IAE9D;IACA,MAAMmB,OAAO;IAAA;IAAA,CAAA7C,cAAA,GAAAE,CAAA,QAAG;MACd4C,MAAM,EAAEvB,GAAG,CAACuB,MAAM;MAClBC,QAAQ;MAAE;MAAA,CAAA/C,cAAA,GAAA0B,CAAA,UAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;MAAA;MAAA,CAAA3B,cAAA,GAAA0B,CAAA,UAAIH,GAAG,CAACI,IAAI;MACrCsB,UAAU,EAAEzB,GAAG,CAACyB,UAAU;MAC1Bd,YAAY;MACZe,SAAS,EAAE,IAAIjC,IAAI,EAAE;MACrBmB,MAAM;MACNM,EAAE;MACFF;KACD;IAED;IAAA;IAAAxC,cAAA,GAAAE,CAAA;IACAU,8BAAA,CAAAuC,4BAA4B,CAACC,YAAY,CAACP,OAAO,CAAC;IAElD;IACA,MAAMQ,QAAQ;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAGiC,YAAY,GAAG,IAAI;IAAA;IAAA,CAAAnC,cAAA,GAAA0B,CAAA,UAAG,MAAM;IAAA;IAAA,CAAA1B,cAAA,GAAA0B,CAAA,UAAGS,YAAY,GAAG,IAAI;IAAA;IAAA,CAAAnC,cAAA,GAAA0B,CAAA,UAAG,MAAM;IAAA;IAAA,CAAA1B,cAAA,GAAA0B,CAAA,UAAG,OAAO;IAAC;IAAA1B,cAAA,GAAAE,CAAA;IACvFY,QAAA,CAAAwC,MAAM,CAACD,QAAQ,CAAC,CAAC,mBAAmB,EAAE;MACpCtB,SAAS;MACTe,MAAM,EAAEvB,GAAG,CAACuB,MAAM;MAClBC,QAAQ,EAAEF,OAAO,CAACE,QAAQ;MAC1BE,UAAU,EAAEzB,GAAG,CAACyB,UAAU;MAC1Bd,YAAY,EAAE,GAAGA,YAAY,CAACoB,OAAO,CAAC,CAAC,CAAC,IAAI;MAC5CnB,MAAM;MACNM,EAAE;MACFF,SAAS,EAAEA,SAAS,CAACgB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;KACxC,CAAC;IAEF;IAAA;IAAAxD,cAAA,GAAAE,CAAA;IACA,IAAIsB,GAAG,CAACyB,UAAU,IAAI,GAAG,EAAE;MAAA;MAAAjD,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MACzBW,sBAAA,CAAA4C,oBAAoB,CAACC,UAAU,CAC7B,IAAIC,KAAK,CAAC,iBAAiBnC,GAAG,CAACyB,UAAU,EAAE,CAAC,EAC5C;QACElB,SAAS;QACTgB,QAAQ,EAAEF,OAAO,CAACE,QAAQ;QAC1BD,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClBJ,EAAE;QACFF,SAAS;QACTJ;OACD,EACDvB,sBAAA,CAAA+C,aAAa,CAACC,IAAI,EAClBhD,sBAAA,CAAAiD,aAAa,CAACC,MAAM,CACrB;IACH,CAAC,MAAM;MAAA;MAAA/D,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MAAA,IAAIsB,GAAG,CAACyB,UAAU,IAAI,GAAG,EAAE;QAAA;QAAAjD,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAE,CAAA;QAChCW,sBAAA,CAAA4C,oBAAoB,CAACC,UAAU,CAC7B,IAAIC,KAAK,CAAC,iBAAiBnC,GAAG,CAACyB,UAAU,EAAE,CAAC,EAC5C;UACElB,SAAS;UACTgB,QAAQ,EAAEF,OAAO,CAACE,QAAQ;UAC1BD,MAAM,EAAEvB,GAAG,CAACuB,MAAM;UAClBJ,EAAE;UACFF,SAAS;UACTJ;SACD,EACDvB,sBAAA,CAAA+C,aAAa,CAACI,GAAG,EACjBnD,sBAAA,CAAAiD,aAAa,CAACG,UAAU,CACzB;MACH,CAAC;MAAA;MAAA;QAAAjE,cAAA,GAAA0B,CAAA;MAAA;IAAD;EACF,CAAC,CAAC;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EAEHuB,IAAI,EAAE;AACR;AAEA;;;AAGA,SAAgBpB,wBAAwBA,CAAC6D,SAAA;AAAA;AAAA,CAAAlE,cAAA,GAAA0B,CAAA,WAAoB,KAAK;EAAA;EAAA1B,cAAA,GAAAgB,CAAA;EAAAhB,cAAA,GAAAE,CAAA;EAChE,OAAO,CAACqB,GAAoB,EAAEC,GAAa,EAAEC,IAAkB,KAAU;IAAA;IAAAzB,cAAA,GAAAgB,CAAA;IACvE,MAAMmD,OAAO;IAAA;IAAA,CAAAnE,cAAA,GAAAE,CAAA,QAAGkE,UAAU,CAAC,MAAK;MAAA;MAAApE,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAE,CAAA;MAC9B,IAAI,CAACsB,GAAG,CAAC6C,WAAW,EAAE;QAAA;QAAArE,cAAA,GAAA0B,CAAA;QACpB,MAAM4C,KAAK;QAAA;QAAA,CAAAtE,cAAA,GAAAE,CAAA,QAAG,IAAIyD,KAAK,CAAC,iBAAiB,CAAC;QAAC;QAAA3D,cAAA,GAAAE,CAAA;QAE3CW,sBAAA,CAAA4C,oBAAoB,CAACC,UAAU,CAC7BY,KAAK,EACL;UACEvC,SAAS,EAAER,GAAG,CAACQ,SAAS;UACxBgB,QAAQ;UAAE;UAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;UAAA;UAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;UACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;UAClBJ,EAAE,EAAEnB,GAAG,CAACmB,EAAE;UACVF,SAAS,EAAEjB,GAAG,CAACkB,GAAG,CAAC,YAAY,CAAC;UAChC0B,OAAO,EAAED;SACV,EACDrD,sBAAA,CAAA+C,aAAa,CAACC,IAAI,EAClBhD,sBAAA,CAAAiD,aAAa,CAACS,WAAW,CAC1B;QAAC;QAAAvE,cAAA,GAAAE,CAAA;QAEFsB,GAAG,CAACgD,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;UACnBC,OAAO,EAAE,KAAK;UACdJ,KAAK,EAAE,iBAAiB;UACxBvC,SAAS,EAAER,GAAG,CAACQ,SAAS;UACxBoC,OAAO,EAAED;SACV,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAlE,cAAA,GAAA0B,CAAA;MAAA;IACH,CAAC,EAAEwC,SAAS,CAAC;IAAC;IAAAlE,cAAA,GAAAE,CAAA;IAEdsB,GAAG,CAACS,EAAE,CAAC,QAAQ,EAAE,MAAK;MAAA;MAAAjC,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAE,CAAA;MACpByE,YAAY,CAACR,OAAO,CAAC;IACvB,CAAC,CAAC;IAAC;IAAAnE,cAAA,GAAAE,CAAA;IAEHsB,GAAG,CAACS,EAAE,CAAC,OAAO,EAAE,MAAK;MAAA;MAAAjC,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAE,CAAA;MACnByE,YAAY,CAACR,OAAO,CAAC;IACvB,CAAC,CAAC;IAAC;IAAAnE,cAAA,GAAAE,CAAA;IAEHuB,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBnB,0BAA0BA,CACxCiB,GAAoB,EACpBC,GAAa,EACbC,IAAkB;EAAA;EAAAzB,cAAA,GAAAgB,CAAA;EAElB,MAAM4D,YAAY;EAAA;EAAA,CAAA5E,cAAA,GAAAE,CAAA,QAAG2E,OAAO,CAACC,WAAW,EAAE;EAAC;EAAA9E,cAAA,GAAAE,CAAA;EAE3CsB,GAAG,CAACS,EAAE,CAAC,QAAQ,EAAE,MAAK;IAAA;IAAAjC,cAAA,GAAAgB,CAAA;IACpB,MAAM+D,WAAW;IAAA;IAAA,CAAA/E,cAAA,GAAAE,CAAA,QAAG2E,OAAO,CAACC,WAAW,EAAE;IACzC,MAAME,WAAW;IAAA;IAAA,CAAAhF,cAAA,GAAAE,CAAA,QAAG;MAClB+E,QAAQ,EAAEF,WAAW,CAACE,QAAQ,GAAGL,YAAY,CAACK,QAAQ;MACtDC,SAAS,EAAEH,WAAW,CAACG,SAAS,GAAGN,YAAY,CAACM,SAAS;MACzDC,GAAG,EAAEJ,WAAW,CAACI,GAAG,GAAGP,YAAY,CAACO,GAAG;MACvCC,QAAQ,EAAEL,WAAW,CAACK,QAAQ,GAAGR,YAAY,CAACQ;KAC/C;IAED;IAAA;IAAApF,cAAA,GAAAE,CAAA;IACA,IAAI8E,WAAW,CAACC,QAAQ,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MAAA;MAAAjF,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MAAE;MAC7CY,QAAA,CAAAwC,MAAM,CAAC+B,IAAI,CAAC,sCAAsC,EAAE;QAClDtD,SAAS,EAAER,GAAG,CAACQ,SAAS;QACxBgB,QAAQ;QAAE;QAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;QAAA;QAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;QACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClBkC,WAAW,EAAE;UACXC,QAAQ,EAAE,GAAG,CAACD,WAAW,CAACC,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE1B,OAAO,CAAC,CAAC,CAAC,KAAK;UACjE4B,GAAG,EAAE,GAAG,CAACH,WAAW,CAACG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE5B,OAAO,CAAC,CAAC,CAAC;SACnD;QACDwB,WAAW,EAAE;UACXE,QAAQ,EAAE,GAAG,CAACF,WAAW,CAACE,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE1B,OAAO,CAAC,CAAC,CAAC,KAAK;UACjE2B,SAAS,EAAE,GAAG,CAACH,WAAW,CAACG,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE3B,OAAO,CAAC,CAAC,CAAC,KAAK;UACnE4B,GAAG,EAAE,GAAG,CAACJ,WAAW,CAACI,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE5B,OAAO,CAAC,CAAC,CAAC;;OAErD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAA0B,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EAEHuB,IAAI,EAAE;AACR;AAEA;;;AAGA,SAAgBlB,6BAA6BA,CAC3CgB,GAAoB,EACpBC,GAAa,EACbC,IAAkB;EAAA;EAAAzB,cAAA,GAAAgB,CAAA;EAAAhB,cAAA,GAAAE,CAAA;EAElB;EACAsB,GAAG,CAACS,EAAE,CAAC,QAAQ,EAAE,MAAK;IAAA;IAAAjC,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACpB,IAAIsB,GAAG,CAACyB,UAAU,KAAK,GAAG,EAAE;MAAA;MAAAjD,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MAC1BW,sBAAA,CAAA4C,oBAAoB,CAACC,UAAU,CAC7B,IAAIC,KAAK,CAAC,qBAAqB,CAAC,EAChC;QACE5B,SAAS,EAAER,GAAG,CAACQ,SAAS;QACxBgB,QAAQ;QAAE;QAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;QAAA;QAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;QACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClBJ,EAAE,EAAEnB,GAAG,CAACmB,EAAE;QACVF,SAAS,EAAEjB,GAAG,CAACkB,GAAG,CAAC,YAAY;OAChC,EACD5B,sBAAA,CAAA+C,aAAa,CAAC0B,MAAM,EACpBzE,sBAAA,CAAAiD,aAAa,CAACyB,QAAQ,CACvB;MAAC;MAAAvF,cAAA,GAAAE,CAAA;MAEFY,QAAA,CAAAwC,MAAM,CAAC+B,IAAI,CAAC,qBAAqB,EAAE;QACjCtD,SAAS,EAAER,GAAG,CAACQ,SAAS;QACxBgB,QAAQ;QAAE;QAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;QAAA;QAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;QACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClBJ,EAAE,EAAEnB,GAAG,CAACmB,EAAE;QACVF,SAAS,EAAEjB,GAAG,CAACkB,GAAG,CAAC,YAAY;OAChC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAzC,cAAA,GAAA0B,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EAEHuB,IAAI,EAAE;AACR;AAEA;;;AAGA,SAAgBjB,4BAA4BA,CAC1Ce,GAAoB,EACpBC,GAAa,EACbC,IAAkB;EAAA;EAAAzB,cAAA,GAAAgB,CAAA;EAElB,MAAMwE,aAAa;EAAA;EAAA,CAAAxF,cAAA,GAAAE,CAAA,QAAGqB,GAAG,CAACkE,KAAK;EAC/B,IAAIC,UAAU;EAAA;EAAA,CAAA1F,cAAA,GAAAE,CAAA,QAAG,CAAC;EAClB,IAAIyF,cAAc;EAAA;EAAA,CAAA3F,cAAA,GAAAE,CAAA,QAAG,CAAC;EAEtB;EAAA;EAAAF,cAAA,GAAAE,CAAA;EACA,MAAM0F,UAAU,GAAIC,SAAiB,IAAI;IAAA;IAAA7F,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACvCwF,UAAU,EAAE;IAAC;IAAA1F,cAAA,GAAAE,CAAA;IACbyF,cAAc,IAAIE,SAAS;EAC7B,CAAC;EAED;EAAA;EAAA7F,cAAA,GAAAE,CAAA;EACCqB,GAAW,CAACqE,UAAU,GAAGA,UAAU;EAAC;EAAA5F,cAAA,GAAAE,CAAA;EAErCsB,GAAG,CAACS,EAAE,CAAC,QAAQ,EAAE,MAAK;IAAA;IAAAjC,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACpB,IAAIwF,UAAU,GAAG,EAAE,EAAE;MAAA;MAAA1F,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MACnBY,QAAA,CAAAwC,MAAM,CAAC+B,IAAI,CAAC,oCAAoC,EAAE;QAChDtD,SAAS,EAAER,GAAG,CAACQ,SAAS;QACxBgB,QAAQ;QAAE;QAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;QAAA;QAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;QACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClB4C,UAAU;QACVC,cAAc,EAAE,GAAGA,cAAc,CAACpC,OAAO,CAAC,CAAC,CAAC,IAAI;QAChDuC,gBAAgB,EAAE,GAAG,CAACH,cAAc,GAAGD,UAAU,EAAEnC,OAAO,CAAC,CAAC,CAAC;OAC9D,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAA0B,CAAA;IAAA;IAAA1B,cAAA,GAAAE,CAAA;IAED,IAAIyF,cAAc,GAAG,IAAI,EAAE;MAAA;MAAA3F,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAE,CAAA;MACzBY,QAAA,CAAAwC,MAAM,CAAC+B,IAAI,CAAC,gCAAgC,EAAE;QAC5CtD,SAAS,EAAER,GAAG,CAACQ,SAAS;QACxBgB,QAAQ;QAAE;QAAA,CAAA/C,cAAA,GAAA0B,CAAA,WAAAH,GAAG,CAACyB,KAAK,EAAErB,IAAI;QAAA;QAAA,CAAA3B,cAAA,GAAA0B,CAAA,WAAIH,GAAG,CAACI,IAAI;QACrCmB,MAAM,EAAEvB,GAAG,CAACuB,MAAM;QAClB4C,UAAU;QACVC,cAAc,EAAE,GAAGA,cAAc,CAACpC,OAAO,CAAC,CAAC,CAAC;OAC7C,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAvD,cAAA,GAAA0B,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EAEHuB,IAAI,EAAE;AACR;AAEA;;;AAGA,SAAgBhB,oBAAoBA,CAClCc,GAAoB,EACpBC,GAAa,EACbC,IAAkB;EAAA;EAAAzB,cAAA,GAAAgB,CAAA;EAAAhB,cAAA,GAAAE,CAAA;EAElB;EACAE,6BAA6B,CAACmB,GAAG,EAAEC,GAAG,EAAE,MAAK;IAAA;IAAAxB,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IAC3CI,0BAA0B,CAACiB,GAAG,EAAEC,GAAG,EAAE,MAAK;MAAA;MAAAxB,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAE,CAAA;MACxCK,6BAA6B,CAACgB,GAAG,EAAEC,GAAG,EAAE,MAAK;QAAA;QAAAxB,cAAA,GAAAgB,CAAA;QAAAhB,cAAA,GAAAE,CAAA;QAC3CM,4BAA4B,CAACe,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAAC;AAAAzB,cAAA,GAAAE,CAAA;AAEDC,OAAA,CAAA4F,OAAA,GAAe;EACb3F,6BAA6B;EAC7BC,wBAAwB;EACxBC,0BAA0B;EAC1BC,6BAA6B;EAC7BC,4BAA4B;EAC5BC;CACD", "ignoreList": []}