{"version": 3, "names": ["cov_4ciu2k62p", "actualCoverage", "s", "express_1", "require", "auth_1", "auth_validators_1", "profile_validators_1", "profile_controller_1", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "endpoints", "getProfile", "updateProfile", "getPublicProfile", "updatePrivacy", "getCompletion", "optionalAuth", "req", "next", "error", "userIdParamSchema", "validate", "params", "b", "status", "success", "code", "details", "map", "detail", "field", "path", "join", "use", "authenticate", "patch", "validateRequest", "updateProfileSchema", "privacySettingsSchema", "updatePrivacySettings", "getProfileCompletion", "housingPreferencesSchema", "roommatePreferencesSchema", "delete", "deleteProfile", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\profile.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { authenticate, optionalAuth } from '../middleware/auth';\r\nimport { validateRequest } from '../validators/auth.validators';\r\nimport {\r\n  updateProfileSchema,\r\n  privacySettingsSchema,\r\n  housingPreferencesSchema,\r\n  roommatePreferencesSchema,\r\n  userIdParamSchema\r\n} from '../validators/profile.validators';\r\nimport {\r\n  getProfile,\r\n  updateProfile,\r\n  getPublicProfile,\r\n  updatePrivacySettings,\r\n  getProfileCompletion,\r\n  deleteProfile\r\n} from '../controllers/profile.controller';\r\n\r\nconst router = Router();\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({\r\n    message: 'Profile routes working',\r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      getProfile: 'GET /',\r\n      updateProfile: 'PATCH /',\r\n      getPublicProfile: 'GET /:userId',\r\n      updatePrivacy: 'PATCH /privacy',\r\n      getCompletion: 'GET /completion'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes (optional authentication)\r\nrouter.get('/:userId',\r\n  optionalAuth,\r\n  (req, res, next) => {\r\n    const { error } = userIdParamSchema.validate(req.params);\r\n    if (error) {\r\n      res.status(400).json({\r\n        success: false,\r\n        error: {\r\n          message: 'Validation failed',\r\n          code: 'VALIDATION_ERROR',\r\n          details: error.details.map(detail => ({\r\n            field: detail.path.join('.'),\r\n            message: detail.message\r\n          }))\r\n        }\r\n      });\r\n      return;\r\n    }\r\n    next();\r\n  },\r\n  getPublicProfile\r\n);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Get current user's profile\r\nrouter.get('/', getProfile);\r\n\r\n// Update profile\r\nrouter.patch('/',\r\n  validateRequest(updateProfileSchema),\r\n  updateProfile\r\n);\r\n\r\n// Update privacy settings\r\nrouter.patch('/privacy',\r\n  validateRequest(privacySettingsSchema),\r\n  updatePrivacySettings\r\n);\r\n\r\n// Get profile completion status\r\nrouter.get('/completion', getProfileCompletion);\r\n\r\n// Update housing preferences\r\nrouter.patch('/housing-preferences',\r\n  validateRequest(housingPreferencesSchema),\r\n  updateProfile\r\n);\r\n\r\n// Update roommate preferences\r\nrouter.patch('/roommate-preferences',\r\n  validateRequest(roommatePreferencesSchema),\r\n  updateProfile\r\n);\r\n\r\n// Delete profile (soft delete)\r\nrouter.delete('/', deleteProfile);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AA5BN,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,iBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,oBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAOA,MAAAI,oBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAMK,MAAM;AAAA;AAAA,CAAAT,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAO,MAAM,GAAE;AAEvB;AAAA;AAAAV,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAb,aAAA,GAAAc,CAAA;EAAAd,aAAA,GAAAE,CAAA;EAClCW,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,wBAAwB;IACjCC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,SAAS,EAAE;MACTC,UAAU,EAAE,OAAO;MACnBC,aAAa,EAAE,SAAS;MACxBC,gBAAgB,EAAE,cAAc;MAChCC,aAAa,EAAE,gBAAgB;MAC/BC,aAAa,EAAE;;GAElB,CAAC;AACJ,CAAC,CAAC;AAEF;AAAA;AAAAzB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,UAAU,EACnBN,MAAA,CAAAqB,YAAY,EACZ,CAACC,GAAG,EAAEd,GAAG,EAAEe,IAAI,KAAI;EAAA;EAAA5B,aAAA,GAAAc,CAAA;EACjB,MAAM;IAAEe;EAAK,CAAE;EAAA;EAAA,CAAA7B,aAAA,GAAAE,CAAA,QAAGK,oBAAA,CAAAuB,iBAAiB,CAACC,QAAQ,CAACJ,GAAG,CAACK,MAAM,CAAC;EAAC;EAAAhC,aAAA,GAAAE,CAAA;EACzD,IAAI2B,KAAK,EAAE;IAAA;IAAA7B,aAAA,GAAAiC,CAAA;IAAAjC,aAAA,GAAAE,CAAA;IACTW,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;MACnBoB,OAAO,EAAE,KAAK;MACdN,KAAK,EAAE;QACLb,OAAO,EAAE,mBAAmB;QAC5BoB,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO,CAACC,GAAG,CAACC,MAAM,IAAK;UAAA;UAAAvC,aAAA,GAAAc,CAAA;UAAAd,aAAA,GAAAE,CAAA;UAAA;YACpCsC,KAAK,EAAED,MAAM,CAACE,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;YAC5B1B,OAAO,EAAEuB,MAAM,CAACvB;WACjB;SAAC;;KAEL,CAAC;IAAC;IAAAhB,aAAA,GAAAE,CAAA;IACH;EACF,CAAC;EAAA;EAAA;IAAAF,aAAA,GAAAiC,CAAA;EAAA;EAAAjC,aAAA,GAAAE,CAAA;EACD0B,IAAI,EAAE;AACR,CAAC,EACDpB,oBAAA,CAAAe,gBAAgB,CACjB;AAED;AAAA;AAAAvB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACkC,GAAG,CAACtC,MAAA,CAAAuC,YAAY,CAAC;AAExB;AAAA;AAAA5C,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,GAAG,EAAEH,oBAAA,CAAAa,UAAU,CAAC;AAE3B;AAAA;AAAArB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACoC,KAAK,CAAC,GAAG,EACd,IAAAvC,iBAAA,CAAAwC,eAAe,EAACvC,oBAAA,CAAAwC,mBAAmB,CAAC,EACpCvC,oBAAA,CAAAc,aAAa,CACd;AAED;AAAA;AAAAtB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACoC,KAAK,CAAC,UAAU,EACrB,IAAAvC,iBAAA,CAAAwC,eAAe,EAACvC,oBAAA,CAAAyC,qBAAqB,CAAC,EACtCxC,oBAAA,CAAAyC,qBAAqB,CACtB;AAED;AAAA;AAAAjD,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACE,GAAG,CAAC,aAAa,EAAEH,oBAAA,CAAA0C,oBAAoB,CAAC;AAE/C;AAAA;AAAAlD,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACoC,KAAK,CAAC,sBAAsB,EACjC,IAAAvC,iBAAA,CAAAwC,eAAe,EAACvC,oBAAA,CAAA4C,wBAAwB,CAAC,EACzC3C,oBAAA,CAAAc,aAAa,CACd;AAED;AAAA;AAAAtB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAACoC,KAAK,CAAC,uBAAuB,EAClC,IAAAvC,iBAAA,CAAAwC,eAAe,EAACvC,oBAAA,CAAA6C,yBAAyB,CAAC,EAC1C5C,oBAAA,CAAAc,aAAa,CACd;AAED;AAAA;AAAAtB,aAAA,GAAAE,CAAA;AACAO,MAAM,CAAC4C,MAAM,CAAC,GAAG,EAAE7C,oBAAA,CAAA8C,aAAa,CAAC;AAAC;AAAAtD,aAAA,GAAAE,CAAA;AAElCqD,OAAA,CAAAC,OAAA,GAAe/C,MAAM", "ignoreList": []}