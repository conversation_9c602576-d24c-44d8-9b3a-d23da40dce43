{"version": 3, "names": ["express_1", "cov_1il925iyfp", "s", "require", "property_controller_1", "auth_1", "validation_1", "property_validators_1", "router", "Router", "get", "healthCheck", "validateRequest", "propertyQuerySchema", "getProperties", "authenticate", "getOwnerProperties", "getPropertySuggestions", "post", "createPropertySchema", "createProperty", "validateObjectId", "getProperty", "put", "updatePropertySchema", "updateProperty", "delete", "deleteProperty", "publishProperty", "getPropertyAnalytics", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\property.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  createProperty,\r\n  getProperties,\r\n  getProperty,\r\n  updateProperty,\r\n  deleteProperty,\r\n  getOwnerProperties,\r\n  publishProperty,\r\n  getPropertyAnalytics,\r\n  getPropertySuggestions,\r\n  healthCheck\r\n} from '../controllers/property.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  createPropertySchema,\r\n  updatePropertySchema,\r\n  propertyQuerySchema\r\n} from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   GET /api/properties/health\r\n * @desc    Health check for property service\r\n * @access  Public\r\n */\r\nrouter.get('/health', healthCheck);\r\n\r\n/**\r\n * @route   GET /api/properties\r\n * @desc    Get all properties with filtering and pagination\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(propertyQuerySchema, 'query'),\r\n  getProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/owner\r\n * @desc    Get properties owned by the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/owner',\r\n  authenticate,\r\n  getOwnerProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/suggestions\r\n * @desc    Get property suggestions for the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/suggestions',\r\n  authenticate,\r\n  getPropertySuggestions\r\n);\r\n\r\n/**\r\n * @route   POST /api/properties\r\n * @desc    Create a new property listing\r\n * @access  Private (Property owners only)\r\n */\r\nrouter.post(\r\n  '/',\r\n  authenticate,\r\n  validateRequest(createPropertySchema, 'body'),\r\n  createProperty\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/:id\r\n * @desc    Get a specific property by ID\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getProperty\r\n);\r\n\r\n/**\r\n * @route   PUT /api/properties/:id\r\n * @desc    Update a property listing\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.put(\r\n  '/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  validateRequest(updatePropertySchema, 'body'),\r\n  updateProperty\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/properties/:id\r\n * @desc    Delete a property listing\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.delete(\r\n  '/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  deleteProperty\r\n);\r\n\r\n/**\r\n * @route   POST /api/properties/:id/publish\r\n * @desc    Publish a draft property\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.post(\r\n  '/:id/publish',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  publishProperty\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/:id/analytics\r\n * @desc    Get analytics for a specific property\r\n * @access  Private (Property owner only)\r\n */\r\nrouter.get(\r\n  '/:id/analytics',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  getPropertyAnalytics\r\n);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,qBAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAYA,MAAAE,MAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAI,qBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAMA,MAAMK,MAAM;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAS,MAAM,GAAE;AAEvB;;;;;AAAA;AAAAR,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CAAC,SAAS,EAAEN,qBAAA,CAAAO,WAAW,CAAC;AAElC;;;;;AAAA;AAAAV,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CACR,GAAG,EACH,IAAAJ,YAAA,CAAAM,eAAe,EAACL,qBAAA,CAAAM,mBAAmB,EAAE,OAAO,CAAC,EAC7CT,qBAAA,CAAAU,aAAa,CACd;AAED;;;;;AAAA;AAAAb,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CACR,QAAQ,EACRL,MAAA,CAAAU,YAAY,EACZX,qBAAA,CAAAY,kBAAkB,CACnB;AAED;;;;;AAAA;AAAAf,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CACR,cAAc,EACdL,MAAA,CAAAU,YAAY,EACZX,qBAAA,CAAAa,sBAAsB,CACvB;AAED;;;;;AAAA;AAAAhB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACU,IAAI,CACT,GAAG,EACHb,MAAA,CAAAU,YAAY,EACZ,IAAAT,YAAA,CAAAM,eAAe,EAACL,qBAAA,CAAAY,oBAAoB,EAAE,MAAM,CAAC,EAC7Cf,qBAAA,CAAAgB,cAAc,CACf;AAED;;;;;AAAA;AAAAnB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CACR,MAAM,EACN,IAAAJ,YAAA,CAAAe,gBAAgB,EAAC,IAAI,CAAC,EACtBjB,qBAAA,CAAAkB,WAAW,CACZ;AAED;;;;;AAAA;AAAArB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACe,GAAG,CACR,MAAM,EACNlB,MAAA,CAAAU,YAAY,EACZ,IAAAT,YAAA,CAAAe,gBAAgB,EAAC,IAAI,CAAC,EACtB,IAAAf,YAAA,CAAAM,eAAe,EAACL,qBAAA,CAAAiB,oBAAoB,EAAE,MAAM,CAAC,EAC7CpB,qBAAA,CAAAqB,cAAc,CACf;AAED;;;;;AAAA;AAAAxB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACkB,MAAM,CACX,MAAM,EACNrB,MAAA,CAAAU,YAAY,EACZ,IAAAT,YAAA,CAAAe,gBAAgB,EAAC,IAAI,CAAC,EACtBjB,qBAAA,CAAAuB,cAAc,CACf;AAED;;;;;AAAA;AAAA1B,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACU,IAAI,CACT,cAAc,EACdb,MAAA,CAAAU,YAAY,EACZ,IAAAT,YAAA,CAAAe,gBAAgB,EAAC,IAAI,CAAC,EACtBjB,qBAAA,CAAAwB,eAAe,CAChB;AAED;;;;;AAAA;AAAA3B,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,GAAG,CACR,gBAAgB,EAChBL,MAAA,CAAAU,YAAY,EACZ,IAAAT,YAAA,CAAAe,gBAAgB,EAAC,IAAI,CAAC,EACtBjB,qBAAA,CAAAyB,oBAAoB,CACrB;AAAC;AAAA5B,cAAA,GAAAC,CAAA;AAEF4B,OAAA,CAAAC,OAAA,GAAevB,MAAM", "ignoreList": []}