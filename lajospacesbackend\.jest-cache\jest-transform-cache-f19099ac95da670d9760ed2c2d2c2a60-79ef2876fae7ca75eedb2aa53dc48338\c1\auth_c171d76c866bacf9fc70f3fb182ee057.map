{"version": 3, "names": ["exports", "authenticate", "cov_2oygr7s5qw", "s", "optionalAuth", "authorize", "requireEmailVerification", "authRateLimit", "clearAuthRateLimit", "requireOwnership", "requireRole", "validate<PERSON><PERSON><PERSON><PERSON>", "jwt_1", "require", "errorHandler_1", "logger_1", "User_model_1", "__importDefault", "req", "_res", "next", "f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "authorization", "b", "startsWith", "AppError", "token", "substring", "payload", "verifyAccessToken", "user", "default", "findById", "userId", "select", "isActive", "lastActiveAt", "Date", "save", "_id", "toString", "logger", "info", "email", "error", "warn", "accountTypes", "userAccountType", "accountType", "includes", "isEmailVerified", "maxAttempts", "windowMs", "redisUtils", "redisKeys", "Promise", "resolve", "then", "__importStar", "ip", "connection", "remoteAddress", "key", "rateLimit", "current", "get", "attempts", "parseInt", "ttl", "Math", "ceil", "incr", "expire", "del", "resourceUserIdField", "resourceUserId", "params", "body", "query", "roles", "userRole", "role", "<PERSON><PERSON><PERSON><PERSON>", "validApi<PERSON>eys", "process", "env", "VALID_API_KEYS", "split"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\auth.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { verifyAccessToken, JWTPayload } from '../utils/jwt';\r\nimport { AppError } from './errorHandler';\r\nimport { logger } from '../utils/logger';\r\nimport User from '../models/User.model';\r\n\r\n// Extend Request interface to include user\r\ndeclare global {\r\n  namespace Express {\r\n    interface Request {\r\n      user?: JWTPayload & { _id: string };\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Authentication middleware - Verify JWT token\r\n */\r\nexport async function authenticate(req: Request, _res: Response, next: NextFunction): Promise<void> {\r\n  try {\r\n    // Get token from header\r\n    const authHeader = req.headers.authorization;\r\n    \r\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\r\n      throw new AppError('Access token required', 401, true, 'MISSING_TOKEN');\r\n    }\r\n\r\n    const token = authHeader.substring(7); // Remove 'Bearer ' prefix\r\n\r\n    // Verify token\r\n    const payload = verifyAccessToken(token);\r\n\r\n    // Check if user still exists and is active\r\n    const user = await User.findById(payload.userId).select('+isActive');\r\n    \r\n    if (!user) {\r\n      throw new AppError('User no longer exists', 401, true, 'USER_NOT_FOUND');\r\n    }\r\n\r\n    if (!user.isActive) {\r\n      throw new AppError('Account has been deactivated', 401, true, 'ACCOUNT_DEACTIVATED');\r\n    }\r\n\r\n    // Update last active time\r\n    user.lastActiveAt = new Date();\r\n    await user.save();\r\n\r\n    // Add user to request object\r\n    req.user = {\r\n      ...payload,\r\n      _id: (user._id as any).toString()\r\n    };\r\n\r\n    logger.info(`User authenticated: ${user.email}`);\r\n    next();\r\n\r\n  } catch (error) {\r\n    if (error instanceof AppError) {\r\n      next(error);\r\n    } else {\r\n      logger.error('Authentication error:', error);\r\n      next(new AppError('Authentication failed', 401, true, 'AUTH_FAILED'));\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Optional authentication middleware - Don't fail if no token\r\n */\r\nexport async function optionalAuth(req: Request, _res: Response, next: NextFunction): Promise<void> {\r\n  try {\r\n    const authHeader = req.headers.authorization;\r\n    \r\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\r\n      return next(); // Continue without authentication\r\n    }\r\n\r\n    const token = authHeader.substring(7);\r\n    const payload = verifyAccessToken(token);\r\n\r\n    const user = await User.findById(payload.userId).select('+isActive');\r\n    \r\n    if (user && user.isActive) {\r\n      user.lastActiveAt = new Date();\r\n      await user.save();\r\n\r\n      req.user = {\r\n        ...payload,\r\n        _id: (user._id as any).toString()\r\n      };\r\n    }\r\n\r\n    next();\r\n\r\n  } catch (error) {\r\n    // Log error but don't fail the request\r\n    logger.warn('Optional authentication failed:', error);\r\n    next();\r\n  }\r\n}\r\n\r\n/**\r\n * Authorization middleware - Check user roles/permissions\r\n */\r\nexport function authorize(...accountTypes: ('seeker' | 'owner' | 'both')[]): (req: Request, _res: Response, next: NextFunction) => void {\r\n  return (req: Request, _res: Response, next: NextFunction): void => {\r\n    try {\r\n      if (!req.user) {\r\n        throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n      }\r\n\r\n      const userAccountType = req.user.accountType;\r\n      \r\n      // 'both' account type has access to everything\r\n      if (userAccountType === 'both') {\r\n        return next();\r\n      }\r\n\r\n      // Check if user's account type is in allowed types\r\n      if (!accountTypes.includes(userAccountType)) {\r\n        throw new AppError('Insufficient permissions', 403, true, 'INSUFFICIENT_PERMISSIONS');\r\n      }\r\n\r\n      next();\r\n\r\n    } catch (error) {\r\n      next(error);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Email verification middleware - Check if email is verified\r\n */\r\nexport function requireEmailVerification(req: Request, _res: Response, next: NextFunction): void {\r\n  try {\r\n    if (!req.user) {\r\n      throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n    }\r\n\r\n    if (!req.user.isEmailVerified) {\r\n      throw new AppError('Email verification required', 403, true, 'EMAIL_NOT_VERIFIED');\r\n    }\r\n\r\n    next();\r\n\r\n  } catch (error) {\r\n    next(error);\r\n  }\r\n}\r\n\r\n/**\r\n * Rate limiting middleware for authentication endpoints\r\n */\r\nexport function authRateLimit(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): (req: Request, _res: Response, next: NextFunction) => Promise<void> {\r\n  return async (req: Request, _res: Response, next: NextFunction): Promise<void> => {\r\n    try {\r\n      const { redisUtils, redisKeys } = await import('../config/redis');\r\n      \r\n      const ip = req.ip || req.connection.remoteAddress || 'unknown';\r\n      const key = redisKeys.rateLimit(`auth:${ip}`);\r\n      \r\n      const current = await redisUtils.get(key);\r\n      const attempts = current ? parseInt(current) : 0;\r\n\r\n      if (attempts >= maxAttempts) {\r\n        const ttl = await redisUtils.ttl(key);\r\n        throw new AppError(\r\n          `Too many authentication attempts. Try again in ${Math.ceil(ttl / 60)} minutes.`,\r\n          429,\r\n          true,\r\n          'RATE_LIMIT_EXCEEDED'\r\n        );\r\n      }\r\n\r\n      // Increment attempts\r\n      await redisUtils.incr(key);\r\n      await redisUtils.expire(key, Math.ceil(windowMs / 1000));\r\n\r\n      next();\r\n\r\n    } catch (error) {\r\n      if (error instanceof AppError) {\r\n        next(error);\r\n      } else {\r\n        logger.error('Rate limiting error:', error);\r\n        next(); // Continue on Redis errors\r\n      }\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Clear rate limit on successful authentication\r\n */\r\nexport async function clearAuthRateLimit(req: Request): Promise<void> {\r\n  try {\r\n    const { redisUtils, redisKeys } = await import('../config/redis');\r\n    \r\n    const ip = req.ip || req.connection.remoteAddress || 'unknown';\r\n    const key = redisKeys.rateLimit(`auth:${ip}`);\r\n    \r\n    await redisUtils.del(key);\r\n    \r\n  } catch (error) {\r\n    logger.error('Error clearing auth rate limit:', error);\r\n    // Don't throw - this is not critical\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware to check if user owns the resource\r\n */\r\nexport function requireOwnership(resourceUserIdField: string = 'userId'): (req: Request, _res: Response, next: NextFunction) => void {\r\n  return (req: Request, _res: Response, next: NextFunction): void => {\r\n    try {\r\n      if (!req.user) {\r\n        throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n      }\r\n\r\n      // Get resource user ID from request params, body, or query\r\n      const resourceUserId = req.params[resourceUserIdField] || \r\n                           req.body[resourceUserIdField] || \r\n                           req.query[resourceUserIdField];\r\n\r\n      if (!resourceUserId) {\r\n        throw new AppError('Resource user ID not found', 400, true, 'RESOURCE_USER_ID_MISSING');\r\n      }\r\n\r\n      if (req.user.userId !== resourceUserId) {\r\n        throw new AppError('Access denied - not resource owner', 403, true, 'NOT_RESOURCE_OWNER');\r\n      }\r\n\r\n      next();\r\n\r\n    } catch (error) {\r\n      next(error);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Role-based authorization middleware\r\n */\r\nexport function requireRole(...roles: string[]): (req: Request, _res: Response, next: NextFunction) => void {\r\n  return (req: Request, _res: Response, next: NextFunction): void => {\r\n    try {\r\n      if (!req.user) {\r\n        throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n      }\r\n\r\n      // Check if user has required role\r\n      const userRole = req.user.role || 'user'; // Default to 'user' if no role specified\r\n\r\n      if (!roles.includes(userRole)) {\r\n        throw new AppError('Insufficient permissions', 403, true, 'INSUFFICIENT_PERMISSIONS');\r\n      }\r\n\r\n      next();\r\n\r\n    } catch (error) {\r\n      next(error);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Middleware to validate API key (for external integrations)\r\n */\r\nexport function validateApiKey(req: Request, _res: Response, next: NextFunction): void {\r\n  try {\r\n    const apiKey = req.headers['x-api-key'] as string;\r\n\r\n    if (!apiKey) {\r\n      throw new AppError('API key required', 401, true, 'API_KEY_REQUIRED');\r\n    }\r\n\r\n    // In a real application, you would validate against a database\r\n    // For now, we'll use a simple check\r\n    const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];\r\n\r\n    if (!validApiKeys.includes(apiKey)) {\r\n      throw new AppError('Invalid API key', 401, true, 'INVALID_API_KEY');\r\n    }\r\n\r\n    next();\r\n\r\n  } catch (error) {\r\n    next(error);\r\n  }\r\n}\r\n\r\nexport default {\r\n  authenticate,\r\n  optionalAuth,\r\n  authorize,\r\n  requireEmailVerification,\r\n  authRateLimit,\r\n  clearAuthRateLimit,\r\n  requireOwnership,\r\n  requireRole,\r\n  validateApiKey\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBAA,OAAA,CAAAC,YAAA,GAAAA,YAAA;AA8CC;AAAAC,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAI,YAAA,GAAAA,YAAA;AA8BC;AAAAF,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAK,SAAA,GAAAA,SAAA;AAyBC;AAAAH,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAM,wBAAA,GAAAA,wBAAA;AAeC;AAAAJ,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAO,aAAA,GAAAA,aAAA;AAoCC;AAAAL,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAQ,kBAAA,GAAAA,kBAAA;AAaC;AAAAN,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAS,gBAAA,GAAAA,gBAAA;AA0BC;AAAAP,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAU,WAAA,GAAAA,WAAA;AAoBC;AAAAR,cAAA,GAAAC,CAAA;AAKDH,OAAA,CAAAW,cAAA,GAAAA,cAAA;AA5QA,MAAAC,KAAA;AAAA;AAAA,CAAAV,cAAA,GAAAC,CAAA,QAAAU,OAAA;AACA,MAAAC,cAAA;AAAA;AAAA,CAAAZ,cAAA,GAAAC,CAAA,QAAAU,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAb,cAAA,GAAAC,CAAA,QAAAU,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAd,cAAA,GAAAC,CAAA,QAAAc,eAAA,CAAAJ,OAAA;AAWA;;;AAGO,eAAeZ,YAAYA,CAACiB,GAAY,EAAEC,IAAc,EAAEC,IAAkB;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACjF,IAAI;IACF;IACA,MAAMmB,UAAU;IAAA;IAAA,CAAApB,cAAA,GAAAC,CAAA,QAAGe,GAAG,CAACK,OAAO,CAACC,aAAa;IAAC;IAAAtB,cAAA,GAAAC,CAAA;IAE7C;IAAI;IAAA,CAAAD,cAAA,GAAAuB,CAAA,YAACH,UAAU;IAAA;IAAA,CAAApB,cAAA,GAAAuB,CAAA,WAAI,CAACH,UAAU,CAACI,UAAU,CAAC,SAAS,CAAC,GAAE;MAAA;MAAAxB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACpD,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;IACzE,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAMG,KAAK;IAAA;IAAA,CAAA1B,cAAA,GAAAC,CAAA,QAAGmB,UAAU,CAACO,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC;IAEvC;IACA,MAAMC,OAAO;IAAA;IAAA,CAAA5B,cAAA,GAAAC,CAAA,QAAG,IAAAS,KAAA,CAAAmB,iBAAiB,EAACH,KAAK,CAAC;IAExC;IACA,MAAMI,IAAI;IAAA;IAAA,CAAA9B,cAAA,GAAAC,CAAA,QAAG,MAAMa,YAAA,CAAAiB,OAAI,CAACC,QAAQ,CAACJ,OAAO,CAACK,MAAM,CAAC,CAACC,MAAM,CAAC,WAAW,CAAC;IAAC;IAAAlC,cAAA,GAAAC,CAAA;IAErE,IAAI,CAAC6B,IAAI,EAAE;MAAA;MAAA9B,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACT,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAC,CAAA;IAED,IAAI,CAAC6B,IAAI,CAACK,QAAQ,EAAE;MAAA;MAAAnC,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAClB,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC;IACtF,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAC,CAAA;IACA6B,IAAI,CAACM,YAAY,GAAG,IAAIC,IAAI,EAAE;IAAC;IAAArC,cAAA,GAAAC,CAAA;IAC/B,MAAM6B,IAAI,CAACQ,IAAI,EAAE;IAEjB;IAAA;IAAAtC,cAAA,GAAAC,CAAA;IACAe,GAAG,CAACc,IAAI,GAAG;MACT,GAAGF,OAAO;MACVW,GAAG,EAAGT,IAAI,CAACS,GAAW,CAACC,QAAQ;KAChC;IAAC;IAAAxC,cAAA,GAAAC,CAAA;IAEFY,QAAA,CAAA4B,MAAM,CAACC,IAAI,CAAC,uBAAuBZ,IAAI,CAACa,KAAK,EAAE,CAAC;IAAC;IAAA3C,cAAA,GAAAC,CAAA;IACjDiB,IAAI,EAAE;EAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAC,CAAA;IACd,IAAI2C,KAAK,YAAYhC,cAAA,CAAAa,QAAQ,EAAE;MAAA;MAAAzB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAC7BiB,IAAI,CAAC0B,KAAK,CAAC;IACb,CAAC,MAAM;MAAA;MAAA5C,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACLY,QAAA,CAAA4B,MAAM,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAAC;MAAA5C,cAAA,GAAAC,CAAA;MAC7CiB,IAAI,CAAC,IAAIN,cAAA,CAAAa,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;IACvE;EACF;AACF;AAEA;;;AAGO,eAAevB,YAAYA,CAACc,GAAY,EAAEC,IAAc,EAAEC,IAAkB;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACjF,IAAI;IACF,MAAMmB,UAAU;IAAA;IAAA,CAAApB,cAAA,GAAAC,CAAA,QAAGe,GAAG,CAACK,OAAO,CAACC,aAAa;IAAC;IAAAtB,cAAA,GAAAC,CAAA;IAE7C;IAAI;IAAA,CAAAD,cAAA,GAAAuB,CAAA,YAACH,UAAU;IAAA;IAAA,CAAApB,cAAA,GAAAuB,CAAA,WAAI,CAACH,UAAU,CAACI,UAAU,CAAC,SAAS,CAAC,GAAE;MAAA;MAAAxB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACpD,OAAOiB,IAAI,EAAE,CAAC,CAAC;IACjB,CAAC;IAAA;IAAA;MAAAlB,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAMG,KAAK;IAAA;IAAA,CAAA1B,cAAA,GAAAC,CAAA,QAAGmB,UAAU,CAACO,SAAS,CAAC,CAAC,CAAC;IACrC,MAAMC,OAAO;IAAA;IAAA,CAAA5B,cAAA,GAAAC,CAAA,QAAG,IAAAS,KAAA,CAAAmB,iBAAiB,EAACH,KAAK,CAAC;IAExC,MAAMI,IAAI;IAAA;IAAA,CAAA9B,cAAA,GAAAC,CAAA,QAAG,MAAMa,YAAA,CAAAiB,OAAI,CAACC,QAAQ,CAACJ,OAAO,CAACK,MAAM,CAAC,CAACC,MAAM,CAAC,WAAW,CAAC;IAAC;IAAAlC,cAAA,GAAAC,CAAA;IAErE;IAAI;IAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAAO,IAAI;IAAA;IAAA,CAAA9B,cAAA,GAAAuB,CAAA,WAAIO,IAAI,CAACK,QAAQ,GAAE;MAAA;MAAAnC,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACzB6B,IAAI,CAACM,YAAY,GAAG,IAAIC,IAAI,EAAE;MAAC;MAAArC,cAAA,GAAAC,CAAA;MAC/B,MAAM6B,IAAI,CAACQ,IAAI,EAAE;MAAC;MAAAtC,cAAA,GAAAC,CAAA;MAElBe,GAAG,CAACc,IAAI,GAAG;QACT,GAAGF,OAAO;QACVW,GAAG,EAAGT,IAAI,CAACS,GAAW,CAACC,QAAQ;OAChC;IACH,CAAC;IAAA;IAAA;MAAAxC,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAC,CAAA;IAEDiB,IAAI,EAAE;EAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAC,CAAA;IACd;IACAY,QAAA,CAAA4B,MAAM,CAACI,IAAI,CAAC,iCAAiC,EAAED,KAAK,CAAC;IAAC;IAAA5C,cAAA,GAAAC,CAAA;IACtDiB,IAAI,EAAE;EACR;AACF;AAEA;;;AAGA,SAAgBf,SAASA,CAAC,GAAG2C,YAA6C;EAAA;EAAA9C,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACxE,OAAO,CAACe,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAU;IAAA;IAAAlB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAChE,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF,IAAI,CAACe,GAAG,CAACc,IAAI,EAAE;QAAA;QAAA9B,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACb,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;MAC3E,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAED,MAAMwB,eAAe;MAAA;MAAA,CAAA/C,cAAA,GAAAC,CAAA,QAAGe,GAAG,CAACc,IAAI,CAACkB,WAAW;MAE5C;MAAA;MAAAhD,cAAA,GAAAC,CAAA;MACA,IAAI8C,eAAe,KAAK,MAAM,EAAE;QAAA;QAAA/C,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC9B,OAAOiB,IAAI,EAAE;MACf,CAAC;MAAA;MAAA;QAAAlB,cAAA,GAAAuB,CAAA;MAAA;MAED;MAAAvB,cAAA,GAAAC,CAAA;MACA,IAAI,CAAC6C,YAAY,CAACG,QAAQ,CAACF,eAAe,CAAC,EAAE;QAAA;QAAA/C,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC3C,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE,IAAI,EAAE,0BAA0B,CAAC;MACvF,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAC,CAAA;MAEDiB,IAAI,EAAE;IAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAC,CAAA;MACdiB,IAAI,CAAC0B,KAAK,CAAC;IACb;EACF,CAAC;AACH;AAEA;;;AAGA,SAAgBxC,wBAAwBA,CAACY,GAAY,EAAEC,IAAc,EAAEC,IAAkB;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACvF,IAAI;IAAA;IAAAD,cAAA,GAAAC,CAAA;IACF,IAAI,CAACe,GAAG,CAACc,IAAI,EAAE;MAAA;MAAA9B,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACb,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;IAC3E,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAC,CAAA;IAED,IAAI,CAACe,GAAG,CAACc,IAAI,CAACoB,eAAe,EAAE;MAAA;MAAAlD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAC7B,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE,IAAI,EAAE,oBAAoB,CAAC;IACpF,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAC,CAAA;IAEDiB,IAAI,EAAE;EAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAC,CAAA;IACdiB,IAAI,CAAC0B,KAAK,CAAC;EACb;AACF;AAEA;;;AAGA,SAAgBvC,aAAaA,CAAC8C,WAAA;AAAA;AAAA,CAAAnD,cAAA,GAAAuB,CAAA,WAAsB,CAAC,GAAE6B,QAAA;AAAA;AAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAmB,EAAE,GAAG,EAAE,GAAG,IAAI;EAAA;EAAAvB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACtF,OAAO,OAAOe,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAmB;IAAA;IAAAlB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAC/E,IAAI;MACF,MAAM;QAAEoD,UAAU;QAAEC;MAAS,CAAE;MAAA;MAAA,CAAAtD,cAAA,GAAAC,CAAA,SAAG,MAAAsD,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA;QAAAzD,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAC,CAAA;QAAA,OAAAyD,YAAA,CAAA/C,OAAA,CAAa,iBAAiB;MAAA,EAAC;MAEjE,MAAMgD,EAAE;MAAA;MAAA,CAAA3D,cAAA,GAAAC,CAAA;MAAG;MAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAAP,GAAG,CAAC2C,EAAE;MAAA;MAAA,CAAA3D,cAAA,GAAAuB,CAAA,WAAIP,GAAG,CAAC4C,UAAU,CAACC,aAAa;MAAA;MAAA,CAAA7D,cAAA,GAAAuB,CAAA,WAAI,SAAS;MAC9D,MAAMuC,GAAG;MAAA;MAAA,CAAA9D,cAAA,GAAAC,CAAA,SAAGqD,SAAS,CAACS,SAAS,CAAC,QAAQJ,EAAE,EAAE,CAAC;MAE7C,MAAMK,OAAO;MAAA;MAAA,CAAAhE,cAAA,GAAAC,CAAA,SAAG,MAAMoD,UAAU,CAACY,GAAG,CAACH,GAAG,CAAC;MACzC,MAAMI,QAAQ;MAAA;MAAA,CAAAlE,cAAA,GAAAC,CAAA,SAAG+D,OAAO;MAAA;MAAA,CAAAhE,cAAA,GAAAuB,CAAA,WAAG4C,QAAQ,CAACH,OAAO,CAAC;MAAA;MAAA,CAAAhE,cAAA,GAAAuB,CAAA,WAAG,CAAC;MAAC;MAAAvB,cAAA,GAAAC,CAAA;MAEjD,IAAIiE,QAAQ,IAAIf,WAAW,EAAE;QAAA;QAAAnD,cAAA,GAAAuB,CAAA;QAC3B,MAAM6C,GAAG;QAAA;QAAA,CAAApE,cAAA,GAAAC,CAAA,SAAG,MAAMoD,UAAU,CAACe,GAAG,CAACN,GAAG,CAAC;QAAC;QAAA9D,cAAA,GAAAC,CAAA;QACtC,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAChB,kDAAkD4C,IAAI,CAACC,IAAI,CAACF,GAAG,GAAG,EAAE,CAAC,WAAW,EAChF,GAAG,EACH,IAAI,EACJ,qBAAqB,CACtB;MACH,CAAC;MAAA;MAAA;QAAApE,cAAA,GAAAuB,CAAA;MAAA;MAED;MAAAvB,cAAA,GAAAC,CAAA;MACA,MAAMoD,UAAU,CAACkB,IAAI,CAACT,GAAG,CAAC;MAAC;MAAA9D,cAAA,GAAAC,CAAA;MAC3B,MAAMoD,UAAU,CAACmB,MAAM,CAACV,GAAG,EAAEO,IAAI,CAACC,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAAC,CAAC;MAAC;MAAApD,cAAA,GAAAC,CAAA;MAEzDiB,IAAI,EAAE;IAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAC,CAAA;MACd,IAAI2C,KAAK,YAAYhC,cAAA,CAAAa,QAAQ,EAAE;QAAA;QAAAzB,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC7BiB,IAAI,CAAC0B,KAAK,CAAC;MACb,CAAC,MAAM;QAAA;QAAA5C,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACLY,QAAA,CAAA4B,MAAM,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAAC;QAAA5C,cAAA,GAAAC,CAAA;QAC5CiB,IAAI,EAAE,CAAC,CAAC;MACV;IACF;EACF,CAAC;AACH;AAEA;;;AAGO,eAAeZ,kBAAkBA,CAACU,GAAY;EAAA;EAAAhB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACnD,IAAI;IACF,MAAM;MAAEoD,UAAU;MAAEC;IAAS,CAAE;IAAA;IAAA,CAAAtD,cAAA,GAAAC,CAAA,SAAG,MAAAsD,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA;MAAAzD,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAC,CAAA;MAAA,OAAAyD,YAAA,CAAA/C,OAAA,CAAa,iBAAiB;IAAA,EAAC;IAEjE,MAAMgD,EAAE;IAAA;IAAA,CAAA3D,cAAA,GAAAC,CAAA;IAAG;IAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAAP,GAAG,CAAC2C,EAAE;IAAA;IAAA,CAAA3D,cAAA,GAAAuB,CAAA,WAAIP,GAAG,CAAC4C,UAAU,CAACC,aAAa;IAAA;IAAA,CAAA7D,cAAA,GAAAuB,CAAA,WAAI,SAAS;IAC9D,MAAMuC,GAAG;IAAA;IAAA,CAAA9D,cAAA,GAAAC,CAAA,SAAGqD,SAAS,CAACS,SAAS,CAAC,QAAQJ,EAAE,EAAE,CAAC;IAAC;IAAA3D,cAAA,GAAAC,CAAA;IAE9C,MAAMoD,UAAU,CAACoB,GAAG,CAACX,GAAG,CAAC;EAE3B,CAAC,CAAC,OAAOlB,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAC,CAAA;IACdY,QAAA,CAAA4B,MAAM,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACtD;EACF;AACF;AAEA;;;AAGA,SAAgBrC,gBAAgBA,CAACmE,mBAAA;AAAA;AAAA,CAAA1E,cAAA,GAAAuB,CAAA,WAA8B,QAAQ;EAAA;EAAAvB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EACrE,OAAO,CAACe,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAU;IAAA;IAAAlB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAChE,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF,IAAI,CAACe,GAAG,CAACc,IAAI,EAAE;QAAA;QAAA9B,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACb,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;MAC3E,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAMoD,cAAc;MAAA;MAAA,CAAA3E,cAAA,GAAAC,CAAA;MAAG;MAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAAP,GAAG,CAAC4D,MAAM,CAACF,mBAAmB,CAAC;MAAA;MAAA,CAAA1E,cAAA,GAAAuB,CAAA,WACjCP,GAAG,CAAC6D,IAAI,CAACH,mBAAmB,CAAC;MAAA;MAAA,CAAA1E,cAAA,GAAAuB,CAAA,WAC7BP,GAAG,CAAC8D,KAAK,CAACJ,mBAAmB,CAAC;MAAC;MAAA1E,cAAA,GAAAC,CAAA;MAEpD,IAAI,CAAC0E,cAAc,EAAE;QAAA;QAAA3E,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACnB,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE,IAAI,EAAE,0BAA0B,CAAC;MACzF,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAC,CAAA;MAED,IAAIe,GAAG,CAACc,IAAI,CAACG,MAAM,KAAK0C,cAAc,EAAE;QAAA;QAAA3E,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACtC,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE,IAAI,EAAE,oBAAoB,CAAC;MAC3F,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAC,CAAA;MAEDiB,IAAI,EAAE;IAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAC,CAAA;MACdiB,IAAI,CAAC0B,KAAK,CAAC;IACb;EACF,CAAC;AACH;AAEA;;;AAGA,SAAgBpC,WAAWA,CAAC,GAAGuE,KAAe;EAAA;EAAA/E,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EAC5C,OAAO,CAACe,GAAY,EAAEC,IAAc,EAAEC,IAAkB,KAAU;IAAA;IAAAlB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAC,CAAA;IAChE,IAAI;MAAA;MAAAD,cAAA,GAAAC,CAAA;MACF,IAAI,CAACe,GAAG,CAACc,IAAI,EAAE;QAAA;QAAA9B,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACb,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;MAC3E,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAMyD,QAAQ;MAAA;MAAA,CAAAhF,cAAA,GAAAC,CAAA;MAAG;MAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAAP,GAAG,CAACc,IAAI,CAACmD,IAAI;MAAA;MAAA,CAAAjF,cAAA,GAAAuB,CAAA,WAAI,MAAM,GAAC,CAAC;MAAA;MAAAvB,cAAA,GAAAC,CAAA;MAE1C,IAAI,CAAC8E,KAAK,CAAC9B,QAAQ,CAAC+B,QAAQ,CAAC,EAAE;QAAA;QAAAhF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC7B,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE,IAAI,EAAE,0BAA0B,CAAC;MACvF,CAAC;MAAA;MAAA;QAAAzB,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAC,CAAA;MAEDiB,IAAI,EAAE;IAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAC,CAAA;MACdiB,IAAI,CAAC0B,KAAK,CAAC;IACb;EACF,CAAC;AACH;AAEA;;;AAGA,SAAgBnC,cAAcA,CAACO,GAAY,EAAEC,IAAc,EAAEC,IAAkB;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EAAAnB,cAAA,GAAAC,CAAA;EAC7E,IAAI;IACF,MAAMiF,MAAM;IAAA;IAAA,CAAAlF,cAAA,GAAAC,CAAA,SAAGe,GAAG,CAACK,OAAO,CAAC,WAAW,CAAW;IAAC;IAAArB,cAAA,GAAAC,CAAA;IAElD,IAAI,CAACiF,MAAM,EAAE;MAAA;MAAAlF,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACX,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE,IAAI,EAAE,kBAAkB,CAAC;IACvE,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAED;IACA;IACA,MAAM4D,YAAY;IAAA;IAAA,CAAAnF,cAAA,GAAAC,CAAA;IAAG;IAAA,CAAAD,cAAA,GAAAuB,CAAA,WAAA6D,OAAO,CAACC,GAAG,CAACC,cAAc,EAAEC,KAAK,CAAC,GAAG,CAAC;IAAA;IAAA,CAAAvF,cAAA,GAAAuB,CAAA,WAAI,EAAE;IAAC;IAAAvB,cAAA,GAAAC,CAAA;IAElE,IAAI,CAACkF,YAAY,CAAClC,QAAQ,CAACiC,MAAM,CAAC,EAAE;MAAA;MAAAlF,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAClC,MAAM,IAAIW,cAAA,CAAAa,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC;IACrE,CAAC;IAAA;IAAA;MAAAzB,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAC,CAAA;IAEDiB,IAAI,EAAE;EAER,CAAC,CAAC,OAAO0B,KAAK,EAAE;IAAA;IAAA5C,cAAA,GAAAC,CAAA;IACdiB,IAAI,CAAC0B,KAAK,CAAC;EACb;AACF;AAAC;AAAA5C,cAAA,GAAAC,CAAA;AAEDH,OAAA,CAAAiC,OAAA,GAAe;EACbhC,YAAY;EACZG,YAAY;EACZC,SAAS;EACTC,wBAAwB;EACxBC,aAAa;EACbC,kBAAkB;EAClBC,gBAAgB;EAChBC,WAAW;EACXC;CACD", "ignoreList": []}