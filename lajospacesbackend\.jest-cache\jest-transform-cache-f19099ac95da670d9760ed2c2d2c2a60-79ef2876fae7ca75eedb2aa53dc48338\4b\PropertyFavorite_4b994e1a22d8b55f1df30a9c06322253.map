{"version": 3, "names": ["mongoose_1", "cov_124l034041", "s", "__importStar", "require", "PropertyFavoriteSchema", "<PERSON><PERSON><PERSON>", "userId", "type", "Types", "ObjectId", "ref", "required", "index", "propertyId", "timestamps", "unique", "createdAt", "exports", "PropertyFavorite", "default", "model"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\PropertyFavorite.ts"], "sourcesContent": ["import mongoose, { Document, Schema, Types } from 'mongoose';\r\n\r\nexport interface IPropertyFavorite extends Document {\r\n  userId: Types.ObjectId;\r\n  propertyId: Types.ObjectId;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PropertyFavoriteSchema = new Schema<IPropertyFavorite>({\r\n  userId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'User',\r\n    required: true,\r\n    index: true\r\n  },\r\n  propertyId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'Property',\r\n    required: true,\r\n    index: true\r\n  }\r\n}, {\r\n  timestamps: true\r\n});\r\n\r\n// Compound index to ensure unique user-property combinations\r\nPropertyFavoriteSchema.index({ userId: 1, propertyId: 1 }, { unique: true });\r\n\r\n// Index for efficient queries\r\nPropertyFavoriteSchema.index({ userId: 1, createdAt: -1 });\r\nPropertyFavoriteSchema.index({ propertyId: 1, createdAt: -1 });\r\n\r\nexport const PropertyFavorite = mongoose.model<IPropertyFavorite>('PropertyFavorite', PropertyFavoriteSchema);\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,UAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AASA,MAAMC,sBAAsB;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAG,IAAIF,UAAA,CAAAM,MAAM,CAAoB;EAC3DC,MAAM,EAAE;IACNC,IAAI,EAAER,UAAA,CAAAM,MAAM,CAACG,KAAK,CAACC,QAAQ;IAC3BC,GAAG,EAAE,MAAM;IACXC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACDC,UAAU,EAAE;IACVN,IAAI,EAAER,UAAA,CAAAM,MAAM,CAACG,KAAK,CAACC,QAAQ;IAC3BC,GAAG,EAAE,UAAU;IACfC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;;CAEV,EAAE;EACDE,UAAU,EAAE;CACb,CAAC;AAEF;AAAA;AAAAd,cAAA,GAAAC,CAAA;AACAG,sBAAsB,CAACQ,KAAK,CAAC;EAAEN,MAAM,EAAE,CAAC;EAAEO,UAAU,EAAE;AAAC,CAAE,EAAE;EAAEE,MAAM,EAAE;AAAI,CAAE,CAAC;AAE5E;AAAA;AAAAf,cAAA,GAAAC,CAAA;AACAG,sBAAsB,CAACQ,KAAK,CAAC;EAAEN,MAAM,EAAE,CAAC;EAAEU,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAAhB,cAAA,GAAAC,CAAA;AAC3DG,sBAAsB,CAACQ,KAAK,CAAC;EAAEC,UAAU,EAAE,CAAC;EAAEG,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAAhB,cAAA,GAAAC,CAAA;AAElDgB,OAAA,CAAAC,gBAAgB,GAAGnB,UAAA,CAAAoB,OAAQ,CAACC,KAAK,CAAoB,kBAAkB,EAAEhB,sBAAsB,CAAC", "ignoreList": []}