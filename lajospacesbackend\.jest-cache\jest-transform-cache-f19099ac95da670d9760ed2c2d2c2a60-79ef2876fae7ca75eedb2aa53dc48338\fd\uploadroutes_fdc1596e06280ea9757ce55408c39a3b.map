{"version": 3, "names": ["cov_os2dsi2td", "actualCoverage", "s", "express_1", "require", "upload_controller_1", "auth_1", "validation_1", "upload_1", "upload_validators_1", "router", "Router", "use", "authenticate", "post", "uploadSingleImage", "handleUploadError", "validateRequest", "uploadImageSchema", "uploadAvatar", "uploadAvatarSchema", "uploadPropertyPhotos", "uploadPropertyPhotosSchema", "uploadMessageFile", "uploadMessageAttachmentSchema", "uploadMessageAttachment", "uploadMultipleImages", "bulkUploadSchema", "bulkUploadImages", "delete", "deleteUploadedImage", "generateUploadUrlSchema", "generateUploadUrl", "get", "req", "res", "f", "json", "success", "message", "timestamp", "Date", "toISOString", "services", "cloudinary", "imageOptimization", "fileValidation", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  uploadSingleImage,\r\n  uploadAvatar,\r\n  uploadPropertyPhotos,\r\n  uploadMessageAttachment,\r\n  bulkUploadImages,\r\n  deleteUploadedImage,\r\n  generateUploadUrl\r\n} from '../controllers/upload.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  uploadSingleImage as uploadSingleImageMiddleware,\r\n  uploadAvatar as uploadAvatarMiddleware,\r\n  uploadPropertyPhotos as uploadPropertyPhotosMiddleware,\r\n  uploadMessageFile,\r\n  uploadMultipleImages,\r\n  handleUploadError\r\n} from '../middleware/upload';\r\nimport {\r\n  uploadImageSchema,\r\n  uploadAvatarSchema,\r\n  uploadPropertyPhotosSchema,\r\n  uploadMessageAttachmentSchema,\r\n  bulkUploadSchema,\r\n  generateUploadUrlSchema\r\n} from '../validators/upload.validators';\r\n\r\nconst router = Router();\r\n\r\n// All upload routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   POST /api/uploads/image\r\n * @desc    Upload single image\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/image',\r\n  uploadSingleImageMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadImageSchema, 'body'),\r\n  uploadSingleImage\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/avatar\r\n * @desc    Upload user avatar\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/avatar',\r\n  uploadAvatarMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadAvatarSchema, 'body'),\r\n  uploadAvatar\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/property-photos\r\n * @desc    Upload property photos (bulk)\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/property-photos',\r\n  uploadPropertyPhotosMiddleware,\r\n  handleUploadError,\r\n  validateRequest(uploadPropertyPhotosSchema, 'body'),\r\n  uploadPropertyPhotos\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/message-attachment\r\n * @desc    Upload message attachment\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/message-attachment',\r\n  uploadMessageFile,\r\n  handleUploadError,\r\n  validateRequest(uploadMessageAttachmentSchema, 'body'),\r\n  uploadMessageAttachment\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/bulk\r\n * @desc    Bulk upload images\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/bulk',\r\n  uploadMultipleImages,\r\n  handleUploadError,\r\n  validateRequest(bulkUploadSchema, 'body'),\r\n  bulkUploadImages\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/uploads/:publicId\r\n * @desc    Delete uploaded image\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:publicId',\r\n  deleteUploadedImage\r\n);\r\n\r\n/**\r\n * @route   POST /api/uploads/signed-url\r\n * @desc    Generate signed upload URL for direct client uploads\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/signed-url',\r\n  validateRequest(generateUploadUrlSchema, 'body'),\r\n  generateUploadUrl\r\n);\r\n\r\n/**\r\n * @route   GET /api/uploads/health\r\n * @desc    Health check for upload service\r\n * @access  Private\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Upload service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    services: {\r\n      cloudinary: 'connected',\r\n      imageOptimization: 'available',\r\n      fileValidation: 'active'\r\n    }\r\n  });\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsCG;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AAtCH,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,mBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAAE,MAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,QAAA;AAAA;AAAA,CAAAR,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAQA,MAAAK,mBAAA;AAAA;AAAA,CAAAT,aAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAMM,MAAM;AAAA;AAAA,CAAAV,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAQ,MAAM,GAAE;AAEvB;AAAA;AAAAX,aAAA,GAAAE,CAAA;AACAQ,MAAM,CAACE,GAAG,CAACN,MAAA,CAAAO,YAAY,CAAC;AAExB;;;;;AAAA;AAAAb,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,QAAQ,EACRN,QAAA,CAAAO,iBAA2B,EAC3BP,QAAA,CAAAQ,iBAAiB,EACjB,IAAAT,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAS,iBAAiB,EAAE,MAAM,CAAC,EAC1Cb,mBAAA,CAAAU,iBAAiB,CAClB;AAED;;;;;AAAA;AAAAf,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,SAAS,EACTN,QAAA,CAAAW,YAAsB,EACtBX,QAAA,CAAAQ,iBAAiB,EACjB,IAAAT,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAW,kBAAkB,EAAE,MAAM,CAAC,EAC3Cf,mBAAA,CAAAc,YAAY,CACb;AAED;;;;;AAAA;AAAAnB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,kBAAkB,EAClBN,QAAA,CAAAa,oBAA8B,EAC9Bb,QAAA,CAAAQ,iBAAiB,EACjB,IAAAT,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAa,0BAA0B,EAAE,MAAM,CAAC,EACnDjB,mBAAA,CAAAgB,oBAAoB,CACrB;AAED;;;;;AAAA;AAAArB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,qBAAqB,EACrBN,QAAA,CAAAe,iBAAiB,EACjBf,QAAA,CAAAQ,iBAAiB,EACjB,IAAAT,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAe,6BAA6B,EAAE,MAAM,CAAC,EACtDnB,mBAAA,CAAAoB,uBAAuB,CACxB;AAED;;;;;AAAA;AAAAzB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,OAAO,EACPN,QAAA,CAAAkB,oBAAoB,EACpBlB,QAAA,CAAAQ,iBAAiB,EACjB,IAAAT,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAkB,gBAAgB,EAAE,MAAM,CAAC,EACzCtB,mBAAA,CAAAuB,gBAAgB,CACjB;AAED;;;;;AAAA;AAAA5B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACmB,MAAM,CACX,YAAY,EACZxB,mBAAA,CAAAyB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAA9B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,aAAa,EACb,IAAAP,YAAA,CAAAU,eAAe,EAACR,mBAAA,CAAAsB,uBAAuB,EAAE,MAAM,CAAC,EAChD1B,mBAAA,CAAA2B,iBAAiB,CAClB;AAED;;;;;AAAA;AAAAhC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACuB,GAAG,CAAC,SAAS,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAnC,aAAA,GAAAoC,CAAA;EAAApC,aAAA,GAAAE,CAAA;EACjCiC,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,2BAA2B;IACpCC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,QAAQ,EAAE;MACRC,UAAU,EAAE,WAAW;MACvBC,iBAAiB,EAAE,WAAW;MAC9BC,cAAc,EAAE;;GAEnB,CAAC;AACJ,CAAC,CAAC;AAAC;AAAA9C,aAAA,GAAAE,CAAA;AAEH6C,OAAA,CAAAC,OAAA,GAAetC,MAAM", "ignoreList": []}