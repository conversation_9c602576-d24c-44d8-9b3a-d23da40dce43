{"version": 3, "names": ["express_1", "cov_1twxbrdvh7", "s", "require", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\message.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\n\r\nconst router = Router();\r\n\r\n// Placeholder routes - will be implemented in Phase 2\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ message: 'Message routes working', timestamp: new Date().toISOString() });\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAMC,MAAM;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAK,MAAM,GAAE;AAEvB;AAAA;AAAAJ,cAAA,GAAAC,CAAA;AACAE,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAC,CAAA;EAClCM,GAAG,CAACE,IAAI,CAAC;IAAEC,OAAO,EAAE,wBAAwB;IAAEC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;EAAE,CAAE,CAAC;AACtF,CAAC,CAAC;AAAC;AAAAb,cAAA,GAAAC,CAAA;AAEHa,OAAA,CAAAC,OAAA,GAAeZ,MAAM", "ignoreList": []}