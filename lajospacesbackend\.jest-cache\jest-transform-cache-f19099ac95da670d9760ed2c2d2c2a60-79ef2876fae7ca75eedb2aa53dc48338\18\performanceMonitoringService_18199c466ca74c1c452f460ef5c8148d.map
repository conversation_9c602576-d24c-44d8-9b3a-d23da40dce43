{"version": 3, "names": ["cov_3v9r3edtw", "actualCoverage", "s", "logger_1", "require", "PerformanceMonitoringService", "constructor", "f", "requestHistory", "MAX_HISTORY_SIZE", "SLOW_REQUEST_THRESHOLD", "MEMORY_WARNING_THRESHOLD", "startTime", "Date", "now", "trackRequest", "metrics", "push", "length", "b", "shift", "responseTime", "logger", "warn", "method", "endpoint", "statusCode", "userId", "timestamp", "toISOString", "error", "ip", "getMetrics", "oneMinuteAgo", "recentRequests", "filter", "req", "getTime", "responseTimes", "map", "responseTimeMetrics", "calculateResponseTimeMetrics", "memoryUsage", "process", "uptime", "cpuUsage", "totalRequests", "successfulRequests", "failedRequests", "requestRate", "endpointMetrics", "calculateEndpointMetrics", "memory", "heapUsed", "heapTotal", "rss", "external", "system", "loadAverage", "platform", "loadavg", "requests", "total", "successful", "failed", "rate", "endpoints", "average", "min", "max", "p95", "p99", "sorted", "sort", "a", "sum", "reduce", "Math", "floor", "endpointStats", "for<PERSON>ach", "key", "times", "errors", "result", "Object", "entries", "stats", "averageTime", "count", "getHealthStatus", "issues", "status", "memoryUsagePercent", "errorRate", "averageResponseTime", "getSlowEndpoints", "limit", "slice", "monitorMemoryUsage", "toFixed", "generateReport", "healthStatus", "slowEndpoints", "recommendations", "summary", "toUpperCase", "cleanup", "oneDayAgo", "startMonitoring", "setInterval", "report", "info", "exports", "performanceMonitoringService", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\performanceMonitoringService.ts"], "sourcesContent": ["import { performance } from 'perf_hooks';\r\nimport { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\n\r\n/**\r\n * Performance Monitoring Service for LajoSpaces Backend\r\n * Tracks response times, memory usage, and system performance\r\n */\r\n\r\nexport interface PerformanceMetrics {\r\n  responseTime: {\r\n    average: number;\r\n    min: number;\r\n    max: number;\r\n    p95: number;\r\n    p99: number;\r\n  };\r\n  memory: {\r\n    heapUsed: number;\r\n    heapTotal: number;\r\n    rss: number;\r\n    external: number;\r\n  };\r\n  system: {\r\n    uptime: number;\r\n    loadAverage: number[];\r\n    cpuUsage: NodeJS.CpuUsage;\r\n  };\r\n  requests: {\r\n    total: number;\r\n    successful: number;\r\n    failed: number;\r\n    rate: number; // requests per minute\r\n  };\r\n  endpoints: Record<string, {\r\n    count: number;\r\n    averageTime: number;\r\n    errors: number;\r\n  }>;\r\n}\r\n\r\nexport interface RequestMetrics {\r\n  method: string;\r\n  endpoint: string;\r\n  statusCode: number;\r\n  responseTime: number;\r\n  timestamp: Date;\r\n  userId?: string;\r\n  ip?: string;\r\n  userAgent?: string;\r\n}\r\n\r\nclass PerformanceMonitoringService {\r\n  private requestHistory: RequestMetrics[] = [];\r\n  private readonly MAX_HISTORY_SIZE = 10000;\r\n  private readonly SLOW_REQUEST_THRESHOLD = 1000; // 1 second\r\n  private readonly MEMORY_WARNING_THRESHOLD = 512 * 1024 * 1024; // 512MB\r\n  private startTime = Date.now();\r\n\r\n  /**\r\n   * Track a request's performance\r\n   */\r\n  trackRequest(metrics: RequestMetrics): void {\r\n    // Add to history\r\n    this.requestHistory.push(metrics);\r\n\r\n    // Maintain history size\r\n    if (this.requestHistory.length > this.MAX_HISTORY_SIZE) {\r\n      this.requestHistory.shift();\r\n    }\r\n\r\n    // Log slow requests\r\n    if (metrics.responseTime > this.SLOW_REQUEST_THRESHOLD) {\r\n      logger.warn('Slow request detected', {\r\n        method: metrics.method,\r\n        endpoint: metrics.endpoint,\r\n        responseTime: metrics.responseTime,\r\n        statusCode: metrics.statusCode,\r\n        userId: metrics.userId,\r\n        timestamp: metrics.timestamp.toISOString()\r\n      });\r\n    }\r\n\r\n    // Log failed requests\r\n    if (metrics.statusCode >= 500) {\r\n      logger.error('Server error response', {\r\n        method: metrics.method,\r\n        endpoint: metrics.endpoint,\r\n        statusCode: metrics.statusCode,\r\n        responseTime: metrics.responseTime,\r\n        userId: metrics.userId,\r\n        ip: metrics.ip,\r\n        timestamp: metrics.timestamp.toISOString()\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get comprehensive performance metrics\r\n   */\r\n  getMetrics(): PerformanceMetrics {\r\n    const now = Date.now();\r\n    const oneMinuteAgo = now - 60 * 1000;\r\n    const recentRequests = this.requestHistory.filter(\r\n      req => req.timestamp.getTime() > oneMinuteAgo\r\n    );\r\n\r\n    // Calculate response time metrics\r\n    const responseTimes = this.requestHistory.map(req => req.responseTime);\r\n    const responseTimeMetrics = this.calculateResponseTimeMetrics(responseTimes);\r\n\r\n    // Calculate memory metrics\r\n    const memoryUsage = process.memoryUsage();\r\n\r\n    // Calculate system metrics\r\n    const uptime = (now - this.startTime) / 1000; // seconds\r\n    const cpuUsage = process.cpuUsage();\r\n\r\n    // Calculate request metrics\r\n    const totalRequests = this.requestHistory.length;\r\n    const successfulRequests = this.requestHistory.filter(req => req.statusCode < 400).length;\r\n    const failedRequests = totalRequests - successfulRequests;\r\n    const requestRate = recentRequests.length; // requests per minute\r\n\r\n    // Calculate endpoint metrics\r\n    const endpointMetrics = this.calculateEndpointMetrics();\r\n\r\n    return {\r\n      responseTime: responseTimeMetrics,\r\n      memory: {\r\n        heapUsed: memoryUsage.heapUsed,\r\n        heapTotal: memoryUsage.heapTotal,\r\n        rss: memoryUsage.rss,\r\n        external: memoryUsage.external\r\n      },\r\n      system: {\r\n        uptime,\r\n        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0],\r\n        cpuUsage\r\n      },\r\n      requests: {\r\n        total: totalRequests,\r\n        successful: successfulRequests,\r\n        failed: failedRequests,\r\n        rate: requestRate\r\n      },\r\n      endpoints: endpointMetrics\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate response time statistics\r\n   */\r\n  private calculateResponseTimeMetrics(responseTimes: number[]): PerformanceMetrics['responseTime'] {\r\n    if (responseTimes.length === 0) {\r\n      return { average: 0, min: 0, max: 0, p95: 0, p99: 0 };\r\n    }\r\n\r\n    const sorted = responseTimes.sort((a, b) => a - b);\r\n    const sum = sorted.reduce((a, b) => a + b, 0);\r\n\r\n    return {\r\n      average: sum / sorted.length,\r\n      min: sorted[0],\r\n      max: sorted[sorted.length - 1],\r\n      p95: sorted[Math.floor(sorted.length * 0.95)] || 0,\r\n      p99: sorted[Math.floor(sorted.length * 0.99)] || 0\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Calculate per-endpoint metrics\r\n   */\r\n  private calculateEndpointMetrics(): Record<string, { count: number; averageTime: number; errors: number }> {\r\n    const endpointStats: Record<string, { times: number[]; errors: number }> = {};\r\n\r\n    this.requestHistory.forEach(req => {\r\n      const key = `${req.method} ${req.endpoint}`;\r\n      if (!endpointStats[key]) {\r\n        endpointStats[key] = { times: [], errors: 0 };\r\n      }\r\n      endpointStats[key].times.push(req.responseTime);\r\n      if (req.statusCode >= 400) {\r\n        endpointStats[key].errors++;\r\n      }\r\n    });\r\n\r\n    const result: Record<string, { count: number; averageTime: number; errors: number }> = {};\r\n    Object.entries(endpointStats).forEach(([endpoint, stats]) => {\r\n      const averageTime = stats.times.reduce((a, b) => a + b, 0) / stats.times.length;\r\n      result[endpoint] = {\r\n        count: stats.times.length,\r\n        averageTime,\r\n        errors: stats.errors\r\n      };\r\n    });\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Get performance health status\r\n   */\r\n  getHealthStatus(): {\r\n    status: 'healthy' | 'warning' | 'critical';\r\n    issues: string[];\r\n    metrics: {\r\n      averageResponseTime: number;\r\n      memoryUsage: number;\r\n      errorRate: number;\r\n    };\r\n  } {\r\n    const metrics = this.getMetrics();\r\n    const issues: string[] = [];\r\n    let status: 'healthy' | 'warning' | 'critical' = 'healthy';\r\n\r\n    // Check response time\r\n    if (metrics.responseTime.average > 2000) {\r\n      issues.push('High average response time');\r\n      status = 'critical';\r\n    } else if (metrics.responseTime.average > 1000) {\r\n      issues.push('Elevated response time');\r\n      if (status === 'healthy') status = 'warning';\r\n    }\r\n\r\n    // Check memory usage\r\n    const memoryUsagePercent = (metrics.memory.heapUsed / metrics.memory.heapTotal) * 100;\r\n    if (memoryUsagePercent > 90) {\r\n      issues.push('Critical memory usage');\r\n      status = 'critical';\r\n    } else if (memoryUsagePercent > 75) {\r\n      issues.push('High memory usage');\r\n      if (status === 'healthy') status = 'warning';\r\n    }\r\n\r\n    // Check error rate\r\n    const errorRate = metrics.requests.total > 0 \r\n      ? (metrics.requests.failed / metrics.requests.total) * 100 \r\n      : 0;\r\n    if (errorRate > 10) {\r\n      issues.push('High error rate');\r\n      status = 'critical';\r\n    } else if (errorRate > 5) {\r\n      issues.push('Elevated error rate');\r\n      if (status === 'healthy') status = 'warning';\r\n    }\r\n\r\n    return {\r\n      status,\r\n      issues,\r\n      metrics: {\r\n        averageResponseTime: metrics.responseTime.average,\r\n        memoryUsage: memoryUsagePercent,\r\n        errorRate\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get slow endpoints report\r\n   */\r\n  getSlowEndpoints(limit: number = 10): Array<{\r\n    endpoint: string;\r\n    averageTime: number;\r\n    count: number;\r\n    errors: number;\r\n  }> {\r\n    const endpointMetrics = this.calculateEndpointMetrics();\r\n    \r\n    return Object.entries(endpointMetrics)\r\n      .map(([endpoint, metrics]) => ({\r\n        endpoint,\r\n        averageTime: metrics.averageTime,\r\n        count: metrics.count,\r\n        errors: metrics.errors\r\n      }))\r\n      .sort((a, b) => b.averageTime - a.averageTime)\r\n      .slice(0, limit);\r\n  }\r\n\r\n  /**\r\n   * Monitor memory usage and alert if necessary\r\n   */\r\n  monitorMemoryUsage(): void {\r\n    const memoryUsage = process.memoryUsage();\r\n    \r\n    if (memoryUsage.heapUsed > this.MEMORY_WARNING_THRESHOLD) {\r\n      logger.warn('High memory usage detected', {\r\n        heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n        heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,\r\n        rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,\r\n        external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate performance report\r\n   */\r\n  generateReport(): {\r\n    summary: string;\r\n    metrics: PerformanceMetrics;\r\n    healthStatus: ReturnType<typeof this.getHealthStatus>;\r\n    slowEndpoints: ReturnType<typeof this.getSlowEndpoints>;\r\n    recommendations: string[];\r\n  } {\r\n    const metrics = this.getMetrics();\r\n    const healthStatus = this.getHealthStatus();\r\n    const slowEndpoints = this.getSlowEndpoints(5);\r\n    const recommendations: string[] = [];\r\n\r\n    // Generate recommendations\r\n    if (metrics.responseTime.average > 1000) {\r\n      recommendations.push('Consider optimizing database queries and adding caching');\r\n    }\r\n    if (metrics.memory.heapUsed > this.MEMORY_WARNING_THRESHOLD) {\r\n      recommendations.push('Monitor memory leaks and optimize memory usage');\r\n    }\r\n    if (metrics.requests.failed > metrics.requests.successful * 0.05) {\r\n      recommendations.push('Investigate and fix high error rate');\r\n    }\r\n\r\n    const summary = `Performance Summary: ${healthStatus.status.toUpperCase()} - ` +\r\n      `Avg Response: ${metrics.responseTime.average.toFixed(0)}ms, ` +\r\n      `Memory: ${(metrics.memory.heapUsed / 1024 / 1024).toFixed(0)}MB, ` +\r\n      `Requests: ${metrics.requests.total} (${metrics.requests.rate}/min)`;\r\n\r\n    return {\r\n      summary,\r\n      metrics,\r\n      healthStatus,\r\n      slowEndpoints,\r\n      recommendations\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cleanup old performance data\r\n   */\r\n  cleanup(): void {\r\n    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\r\n    this.requestHistory = this.requestHistory.filter(\r\n      req => req.timestamp > oneDayAgo\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Start periodic monitoring\r\n   */\r\n  startMonitoring(): void {\r\n    // Monitor memory every 5 minutes\r\n    setInterval(() => {\r\n      this.monitorMemoryUsage();\r\n    }, 5 * 60 * 1000);\r\n\r\n    // Cleanup old data every hour\r\n    setInterval(() => {\r\n      this.cleanup();\r\n    }, 60 * 60 * 1000);\r\n\r\n    // Log performance summary every 15 minutes\r\n    setInterval(() => {\r\n      const report = this.generateReport();\r\n      logger.info('Performance Report', {\r\n        summary: report.summary,\r\n        healthStatus: report.healthStatus,\r\n        slowEndpoints: report.slowEndpoints.slice(0, 3),\r\n        timestamp: new Date().toISOString()\r\n      });\r\n    }, 15 * 60 * 1000);\r\n\r\n    logger.info('Performance monitoring started');\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const performanceMonitoringService = new PerformanceMonitoringService();\r\n\r\nexport default performanceMonitoringService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8DE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AA7DF,MAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAmDA,MAAMC,4BAA4B;EAAlCC,YAAA;IAAA;IAAAN,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACU,KAAAM,cAAc,GAAqB,EAAE;IAAC;IAAAR,aAAA,GAAAE,CAAA;IAC7B,KAAAO,gBAAgB,GAAG,KAAK;IAAC;IAAAT,aAAA,GAAAE,CAAA;IACzB,KAAAQ,sBAAsB,GAAG,IAAI,CAAC,CAAC;IAAA;IAAAV,aAAA,GAAAE,CAAA;IAC/B,KAAAS,wBAAwB,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAAA;IAAAX,aAAA,GAAAE,CAAA;IACvD,KAAAU,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;EA6ThC;EA3TE;;;EAGAC,YAAYA,CAACC,OAAuB;IAAA;IAAAhB,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAClC;IACA,IAAI,CAACM,cAAc,CAACS,IAAI,CAACD,OAAO,CAAC;IAEjC;IAAA;IAAAhB,aAAA,GAAAE,CAAA;IACA,IAAI,IAAI,CAACM,cAAc,CAACU,MAAM,GAAG,IAAI,CAACT,gBAAgB,EAAE;MAAA;MAAAT,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MACtD,IAAI,CAACM,cAAc,CAACY,KAAK,EAAE;IAC7B,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAE,CAAA;IACA,IAAIc,OAAO,CAACK,YAAY,GAAG,IAAI,CAACX,sBAAsB,EAAE;MAAA;MAAAV,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MACtDC,QAAA,CAAAmB,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAE;QACnCC,MAAM,EAAER,OAAO,CAACQ,MAAM;QACtBC,QAAQ,EAAET,OAAO,CAACS,QAAQ;QAC1BJ,YAAY,EAAEL,OAAO,CAACK,YAAY;QAClCK,UAAU,EAAEV,OAAO,CAACU,UAAU;QAC9BC,MAAM,EAAEX,OAAO,CAACW,MAAM;QACtBC,SAAS,EAAEZ,OAAO,CAACY,SAAS,CAACC,WAAW;OACzC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7B,aAAA,GAAAmB,CAAA;IAAA;IAED;IAAAnB,aAAA,GAAAE,CAAA;IACA,IAAIc,OAAO,CAACU,UAAU,IAAI,GAAG,EAAE;MAAA;MAAA1B,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAC7BC,QAAA,CAAAmB,MAAM,CAACQ,KAAK,CAAC,uBAAuB,EAAE;QACpCN,MAAM,EAAER,OAAO,CAACQ,MAAM;QACtBC,QAAQ,EAAET,OAAO,CAACS,QAAQ;QAC1BC,UAAU,EAAEV,OAAO,CAACU,UAAU;QAC9BL,YAAY,EAAEL,OAAO,CAACK,YAAY;QAClCM,MAAM,EAAEX,OAAO,CAACW,MAAM;QACtBI,EAAE,EAAEf,OAAO,CAACe,EAAE;QACdH,SAAS,EAAEZ,OAAO,CAACY,SAAS,CAACC,WAAW;OACzC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7B,aAAA,GAAAmB,CAAA;IAAA;EACH;EAEA;;;EAGAa,UAAUA,CAAA;IAAA;IAAAhC,aAAA,GAAAO,CAAA;IACR,MAAMO,GAAG;IAAA;IAAA,CAAAd,aAAA,GAAAE,CAAA,QAAGW,IAAI,CAACC,GAAG,EAAE;IACtB,MAAMmB,YAAY;IAAA;IAAA,CAAAjC,aAAA,GAAAE,CAAA,QAAGY,GAAG,GAAG,EAAE,GAAG,IAAI;IACpC,MAAMoB,cAAc;IAAA;IAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACM,cAAc,CAAC2B,MAAM,CAC/CC,GAAG,IAAI;MAAA;MAAApC,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAkC,GAAG,CAACR,SAAS,CAACS,OAAO,EAAE,GAAGJ,YAAY;IAAZ,CAAY,CAC9C;IAED;IACA,MAAMK,aAAa;IAAA;IAAA,CAAAtC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACM,cAAc,CAAC+B,GAAG,CAACH,GAAG,IAAI;MAAA;MAAApC,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAkC,GAAG,CAACf,YAAY;IAAZ,CAAY,CAAC;IACtE,MAAMmB,mBAAmB;IAAA;IAAA,CAAAxC,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACuC,4BAA4B,CAACH,aAAa,CAAC;IAE5E;IACA,MAAMI,WAAW;IAAA;IAAA,CAAA1C,aAAA,GAAAE,CAAA,QAAGyC,OAAO,CAACD,WAAW,EAAE;IAEzC;IACA,MAAME,MAAM;IAAA;IAAA,CAAA5C,aAAA,GAAAE,CAAA,QAAG,CAACY,GAAG,GAAG,IAAI,CAACF,SAAS,IAAI,IAAI,EAAC,CAAC;IAC9C,MAAMiC,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAE,CAAA,QAAGyC,OAAO,CAACE,QAAQ,EAAE;IAEnC;IACA,MAAMC,aAAa;IAAA;IAAA,CAAA9C,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACM,cAAc,CAACU,MAAM;IAChD,MAAM6B,kBAAkB;IAAA;IAAA,CAAA/C,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACM,cAAc,CAAC2B,MAAM,CAACC,GAAG,IAAI;MAAA;MAAApC,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAkC,GAAG,CAACV,UAAU,GAAG,GAAG;IAAH,CAAG,CAAC,CAACR,MAAM;IACzF,MAAM8B,cAAc;IAAA;IAAA,CAAAhD,aAAA,GAAAE,CAAA,QAAG4C,aAAa,GAAGC,kBAAkB;IACzD,MAAME,WAAW;IAAA;IAAA,CAAAjD,aAAA,GAAAE,CAAA,QAAGgC,cAAc,CAAChB,MAAM,EAAC,CAAC;IAE3C;IACA,MAAMgC,eAAe;IAAA;IAAA,CAAAlD,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACiD,wBAAwB,EAAE;IAAC;IAAAnD,aAAA,GAAAE,CAAA;IAExD,OAAO;MACLmB,YAAY,EAAEmB,mBAAmB;MACjCY,MAAM,EAAE;QACNC,QAAQ,EAAEX,WAAW,CAACW,QAAQ;QAC9BC,SAAS,EAAEZ,WAAW,CAACY,SAAS;QAChCC,GAAG,EAAEb,WAAW,CAACa,GAAG;QACpBC,QAAQ,EAAEd,WAAW,CAACc;OACvB;MACDC,MAAM,EAAE;QACNb,MAAM;QACNc,WAAW,EAAEf,OAAO,CAACgB,QAAQ,KAAK,OAAO;QAAA;QAAA,CAAA3D,aAAA,GAAAmB,CAAA,UAAGf,OAAO,CAAC,IAAI,CAAC,CAACwD,OAAO,EAAE;QAAA;QAAA,CAAA5D,aAAA,GAAAmB,CAAA,UAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC/E0B;OACD;MACDgB,QAAQ,EAAE;QACRC,KAAK,EAAEhB,aAAa;QACpBiB,UAAU,EAAEhB,kBAAkB;QAC9BiB,MAAM,EAAEhB,cAAc;QACtBiB,IAAI,EAAEhB;OACP;MACDiB,SAAS,EAAEhB;KACZ;EACH;EAEA;;;EAGQT,4BAA4BA,CAACH,aAAuB;IAAA;IAAAtC,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IAC1D,IAAIoC,aAAa,CAACpB,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAlB,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAC9B,OAAO;QAAEiE,OAAO,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAC,CAAE;IACvD,CAAC;IAAA;IAAA;MAAAvE,aAAA,GAAAmB,CAAA;IAAA;IAED,MAAMqD,MAAM;IAAA;IAAA,CAAAxE,aAAA,GAAAE,CAAA,QAAGoC,aAAa,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEvD,CAAC,KAAK;MAAA;MAAAnB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAwE,CAAC,GAAGvD,CAAC;IAAD,CAAC,CAAC;IAClD,MAAMwD,GAAG;IAAA;IAAA,CAAA3E,aAAA,GAAAE,CAAA,QAAGsE,MAAM,CAACI,MAAM,CAAC,CAACF,CAAC,EAAEvD,CAAC,KAAK;MAAA;MAAAnB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAwE,CAAC,GAAGvD,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE9C,OAAO;MACLiE,OAAO,EAAEQ,GAAG,GAAGH,MAAM,CAACtD,MAAM;MAC5BkD,GAAG,EAAEI,MAAM,CAAC,CAAC,CAAC;MACdH,GAAG,EAAEG,MAAM,CAACA,MAAM,CAACtD,MAAM,GAAG,CAAC,CAAC;MAC9BoD,GAAG;MAAE;MAAA,CAAAtE,aAAA,GAAAmB,CAAA,UAAAqD,MAAM,CAACK,IAAI,CAACC,KAAK,CAACN,MAAM,CAACtD,MAAM,GAAG,IAAI,CAAC,CAAC;MAAA;MAAA,CAAAlB,aAAA,GAAAmB,CAAA,UAAI,CAAC;MAClDoD,GAAG;MAAE;MAAA,CAAAvE,aAAA,GAAAmB,CAAA,UAAAqD,MAAM,CAACK,IAAI,CAACC,KAAK,CAACN,MAAM,CAACtD,MAAM,GAAG,IAAI,CAAC,CAAC;MAAA;MAAA,CAAAlB,aAAA,GAAAmB,CAAA,UAAI,CAAC;KACnD;EACH;EAEA;;;EAGQgC,wBAAwBA,CAAA;IAAA;IAAAnD,aAAA,GAAAO,CAAA;IAC9B,MAAMwE,aAAa;IAAA;IAAA,CAAA/E,aAAA,GAAAE,CAAA,QAAwD,EAAE;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAE9E,IAAI,CAACM,cAAc,CAACwE,OAAO,CAAC5C,GAAG,IAAG;MAAA;MAAApC,aAAA,GAAAO,CAAA;MAChC,MAAM0E,GAAG;MAAA;MAAA,CAAAjF,aAAA,GAAAE,CAAA,QAAG,GAAGkC,GAAG,CAACZ,MAAM,IAAIY,GAAG,CAACX,QAAQ,EAAE;MAAC;MAAAzB,aAAA,GAAAE,CAAA;MAC5C,IAAI,CAAC6E,aAAa,CAACE,GAAG,CAAC,EAAE;QAAA;QAAAjF,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAE,CAAA;QACvB6E,aAAa,CAACE,GAAG,CAAC,GAAG;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAC,CAAE;MAC/C,CAAC;MAAA;MAAA;QAAAnF,aAAA,GAAAmB,CAAA;MAAA;MAAAnB,aAAA,GAAAE,CAAA;MACD6E,aAAa,CAACE,GAAG,CAAC,CAACC,KAAK,CAACjE,IAAI,CAACmB,GAAG,CAACf,YAAY,CAAC;MAAC;MAAArB,aAAA,GAAAE,CAAA;MAChD,IAAIkC,GAAG,CAACV,UAAU,IAAI,GAAG,EAAE;QAAA;QAAA1B,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAE,CAAA;QACzB6E,aAAa,CAACE,GAAG,CAAC,CAACE,MAAM,EAAE;MAC7B,CAAC;MAAA;MAAA;QAAAnF,aAAA,GAAAmB,CAAA;MAAA;IACH,CAAC,CAAC;IAEF,MAAMiE,MAAM;IAAA;IAAA,CAAApF,aAAA,GAAAE,CAAA,QAA2E,EAAE;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAC1FmF,MAAM,CAACC,OAAO,CAACP,aAAa,CAAC,CAACC,OAAO,CAAC,CAAC,CAACvD,QAAQ,EAAE8D,KAAK,CAAC,KAAI;MAAA;MAAAvF,aAAA,GAAAO,CAAA;MAC1D,MAAMiF,WAAW;MAAA;MAAA,CAAAxF,aAAA,GAAAE,CAAA,QAAGqF,KAAK,CAACL,KAAK,CAACN,MAAM,CAAC,CAACF,CAAC,EAAEvD,CAAC,KAAK;QAAA;QAAAnB,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAE,CAAA;QAAA,OAAAwE,CAAC,GAAGvD,CAAC;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGoE,KAAK,CAACL,KAAK,CAAChE,MAAM;MAAC;MAAAlB,aAAA,GAAAE,CAAA;MAChFkF,MAAM,CAAC3D,QAAQ,CAAC,GAAG;QACjBgE,KAAK,EAAEF,KAAK,CAACL,KAAK,CAAChE,MAAM;QACzBsE,WAAW;QACXL,MAAM,EAAEI,KAAK,CAACJ;OACf;IACH,CAAC,CAAC;IAAC;IAAAnF,aAAA,GAAAE,CAAA;IAEH,OAAOkF,MAAM;EACf;EAEA;;;EAGAM,eAAeA,CAAA;IAAA;IAAA1F,aAAA,GAAAO,CAAA;IASb,MAAMS,OAAO;IAAA;IAAA,CAAAhB,aAAA,GAAAE,CAAA,QAAG,IAAI,CAAC8B,UAAU,EAAE;IACjC,MAAM2D,MAAM;IAAA;IAAA,CAAA3F,aAAA,GAAAE,CAAA,QAAa,EAAE;IAC3B,IAAI0F,MAAM;IAAA;IAAA,CAAA5F,aAAA,GAAAE,CAAA,QAAuC,SAAS;IAE1D;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACA,IAAIc,OAAO,CAACK,YAAY,CAAC8C,OAAO,GAAG,IAAI,EAAE;MAAA;MAAAnE,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MACvCyF,MAAM,CAAC1E,IAAI,CAAC,4BAA4B,CAAC;MAAC;MAAAjB,aAAA,GAAAE,CAAA;MAC1C0F,MAAM,GAAG,UAAU;IACrB,CAAC,MAAM;MAAA;MAAA5F,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAAA,IAAIc,OAAO,CAACK,YAAY,CAAC8C,OAAO,GAAG,IAAI,EAAE;QAAA;QAAAnE,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAE,CAAA;QAC9CyF,MAAM,CAAC1E,IAAI,CAAC,wBAAwB,CAAC;QAAC;QAAAjB,aAAA,GAAAE,CAAA;QACtC,IAAI0F,MAAM,KAAK,SAAS,EAAE;UAAA;UAAA5F,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAE,CAAA;UAAA0F,MAAM,GAAG,SAAS;QAAA,CAAC;QAAA;QAAA;UAAA5F,aAAA,GAAAmB,CAAA;QAAA;MAC/C,CAAC;MAAA;MAAA;QAAAnB,aAAA,GAAAmB,CAAA;MAAA;IAAD;IAEA;IACA,MAAM0E,kBAAkB;IAAA;IAAA,CAAA7F,aAAA,GAAAE,CAAA,QAAIc,OAAO,CAACoC,MAAM,CAACC,QAAQ,GAAGrC,OAAO,CAACoC,MAAM,CAACE,SAAS,GAAI,GAAG;IAAC;IAAAtD,aAAA,GAAAE,CAAA;IACtF,IAAI2F,kBAAkB,GAAG,EAAE,EAAE;MAAA;MAAA7F,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAC3ByF,MAAM,CAAC1E,IAAI,CAAC,uBAAuB,CAAC;MAAC;MAAAjB,aAAA,GAAAE,CAAA;MACrC0F,MAAM,GAAG,UAAU;IACrB,CAAC,MAAM;MAAA;MAAA5F,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAAA,IAAI2F,kBAAkB,GAAG,EAAE,EAAE;QAAA;QAAA7F,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAE,CAAA;QAClCyF,MAAM,CAAC1E,IAAI,CAAC,mBAAmB,CAAC;QAAC;QAAAjB,aAAA,GAAAE,CAAA;QACjC,IAAI0F,MAAM,KAAK,SAAS,EAAE;UAAA;UAAA5F,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAE,CAAA;UAAA0F,MAAM,GAAG,SAAS;QAAA,CAAC;QAAA;QAAA;UAAA5F,aAAA,GAAAmB,CAAA;QAAA;MAC/C,CAAC;MAAA;MAAA;QAAAnB,aAAA,GAAAmB,CAAA;MAAA;IAAD;IAEA;IACA,MAAM2E,SAAS;IAAA;IAAA,CAAA9F,aAAA,GAAAE,CAAA,QAAGc,OAAO,CAAC6C,QAAQ,CAACC,KAAK,GAAG,CAAC;IAAA;IAAA,CAAA9D,aAAA,GAAAmB,CAAA,WACvCH,OAAO,CAAC6C,QAAQ,CAACG,MAAM,GAAGhD,OAAO,CAAC6C,QAAQ,CAACC,KAAK,GAAI,GAAG;IAAA;IAAA,CAAA9D,aAAA,GAAAmB,CAAA,WACxD,CAAC;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IACN,IAAI4F,SAAS,GAAG,EAAE,EAAE;MAAA;MAAA9F,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAClByF,MAAM,CAAC1E,IAAI,CAAC,iBAAiB,CAAC;MAAC;MAAAjB,aAAA,GAAAE,CAAA;MAC/B0F,MAAM,GAAG,UAAU;IACrB,CAAC,MAAM;MAAA;MAAA5F,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAAA,IAAI4F,SAAS,GAAG,CAAC,EAAE;QAAA;QAAA9F,aAAA,GAAAmB,CAAA;QAAAnB,aAAA,GAAAE,CAAA;QACxByF,MAAM,CAAC1E,IAAI,CAAC,qBAAqB,CAAC;QAAC;QAAAjB,aAAA,GAAAE,CAAA;QACnC,IAAI0F,MAAM,KAAK,SAAS,EAAE;UAAA;UAAA5F,aAAA,GAAAmB,CAAA;UAAAnB,aAAA,GAAAE,CAAA;UAAA0F,MAAM,GAAG,SAAS;QAAA,CAAC;QAAA;QAAA;UAAA5F,aAAA,GAAAmB,CAAA;QAAA;MAC/C,CAAC;MAAA;MAAA;QAAAnB,aAAA,GAAAmB,CAAA;MAAA;IAAD;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAED,OAAO;MACL0F,MAAM;MACND,MAAM;MACN3E,OAAO,EAAE;QACP+E,mBAAmB,EAAE/E,OAAO,CAACK,YAAY,CAAC8C,OAAO;QACjDzB,WAAW,EAAEmD,kBAAkB;QAC/BC;;KAEH;EACH;EAEA;;;EAGAE,gBAAgBA,CAACC,KAAA;EAAA;EAAA,CAAAjG,aAAA,GAAAmB,CAAA,WAAgB,EAAE;IAAA;IAAAnB,aAAA,GAAAO,CAAA;IAMjC,MAAM2C,eAAe;IAAA;IAAA,CAAAlD,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACiD,wBAAwB,EAAE;IAAC;IAAAnD,aAAA,GAAAE,CAAA;IAExD,OAAOmF,MAAM,CAACC,OAAO,CAACpC,eAAe,CAAC,CACnCX,GAAG,CAAC,CAAC,CAACd,QAAQ,EAAET,OAAO,CAAC,KAAM;MAAA;MAAAhB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA;QAC7BuB,QAAQ;QACR+D,WAAW,EAAExE,OAAO,CAACwE,WAAW;QAChCC,KAAK,EAAEzE,OAAO,CAACyE,KAAK;QACpBN,MAAM,EAAEnE,OAAO,CAACmE;OACjB;KAAC,CAAC,CACFV,IAAI,CAAC,CAACC,CAAC,EAAEvD,CAAC,KAAK;MAAA;MAAAnB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAiB,CAAC,CAACqE,WAAW,GAAGd,CAAC,CAACc,WAAW;IAAX,CAAW,CAAC,CAC7CU,KAAK,CAAC,CAAC,EAAED,KAAK,CAAC;EACpB;EAEA;;;EAGAE,kBAAkBA,CAAA;IAAA;IAAAnG,aAAA,GAAAO,CAAA;IAChB,MAAMmC,WAAW;IAAA;IAAA,CAAA1C,aAAA,GAAAE,CAAA,QAAGyC,OAAO,CAACD,WAAW,EAAE;IAAC;IAAA1C,aAAA,GAAAE,CAAA;IAE1C,IAAIwC,WAAW,CAACW,QAAQ,GAAG,IAAI,CAAC1C,wBAAwB,EAAE;MAAA;MAAAX,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MACxDC,QAAA,CAAAmB,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAE;QACxC8B,QAAQ,EAAE,GAAG,CAACX,WAAW,CAACW,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE+C,OAAO,CAAC,CAAC,CAAC,KAAK;QACjE9C,SAAS,EAAE,GAAG,CAACZ,WAAW,CAACY,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE8C,OAAO,CAAC,CAAC,CAAC,KAAK;QACnE7C,GAAG,EAAE,GAAG,CAACb,WAAW,CAACa,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE6C,OAAO,CAAC,CAAC,CAAC,KAAK;QACvD5C,QAAQ,EAAE,GAAG,CAACd,WAAW,CAACc,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE4C,OAAO,CAAC,CAAC,CAAC,KAAK;QACjExE,SAAS,EAAE,IAAIf,IAAI,EAAE,CAACgB,WAAW;OAClC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAA7B,aAAA,GAAAmB,CAAA;IAAA;EACH;EAEA;;;EAGAkF,cAAcA,CAAA;IAAA;IAAArG,aAAA,GAAAO,CAAA;IAOZ,MAAMS,OAAO;IAAA;IAAA,CAAAhB,aAAA,GAAAE,CAAA,QAAG,IAAI,CAAC8B,UAAU,EAAE;IACjC,MAAMsE,YAAY;IAAA;IAAA,CAAAtG,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACwF,eAAe,EAAE;IAC3C,MAAMa,aAAa;IAAA;IAAA,CAAAvG,aAAA,GAAAE,CAAA,QAAG,IAAI,CAAC8F,gBAAgB,CAAC,CAAC,CAAC;IAC9C,MAAMQ,eAAe;IAAA;IAAA,CAAAxG,aAAA,GAAAE,CAAA,QAAa,EAAE;IAEpC;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACA,IAAIc,OAAO,CAACK,YAAY,CAAC8C,OAAO,GAAG,IAAI,EAAE;MAAA;MAAAnE,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MACvCsG,eAAe,CAACvF,IAAI,CAAC,yDAAyD,CAAC;IACjF,CAAC;IAAA;IAAA;MAAAjB,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAE,CAAA;IACD,IAAIc,OAAO,CAACoC,MAAM,CAACC,QAAQ,GAAG,IAAI,CAAC1C,wBAAwB,EAAE;MAAA;MAAAX,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAC3DsG,eAAe,CAACvF,IAAI,CAAC,gDAAgD,CAAC;IACxE,CAAC;IAAA;IAAA;MAAAjB,aAAA,GAAAmB,CAAA;IAAA;IAAAnB,aAAA,GAAAE,CAAA;IACD,IAAIc,OAAO,CAAC6C,QAAQ,CAACG,MAAM,GAAGhD,OAAO,CAAC6C,QAAQ,CAACE,UAAU,GAAG,IAAI,EAAE;MAAA;MAAA/D,aAAA,GAAAmB,CAAA;MAAAnB,aAAA,GAAAE,CAAA;MAChEsG,eAAe,CAACvF,IAAI,CAAC,qCAAqC,CAAC;IAC7D,CAAC;IAAA;IAAA;MAAAjB,aAAA,GAAAmB,CAAA;IAAA;IAED,MAAMsF,OAAO;IAAA;IAAA,CAAAzG,aAAA,GAAAE,CAAA,QAAG,wBAAwBoG,YAAY,CAACV,MAAM,CAACc,WAAW,EAAE,KAAK,GAC5E,iBAAiB1F,OAAO,CAACK,YAAY,CAAC8C,OAAO,CAACiC,OAAO,CAAC,CAAC,CAAC,MAAM,GAC9D,WAAW,CAACpF,OAAO,CAACoC,MAAM,CAACC,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAE+C,OAAO,CAAC,CAAC,CAAC,MAAM,GACnE,aAAapF,OAAO,CAAC6C,QAAQ,CAACC,KAAK,KAAK9C,OAAO,CAAC6C,QAAQ,CAACI,IAAI,OAAO;IAAC;IAAAjE,aAAA,GAAAE,CAAA;IAEvE,OAAO;MACLuG,OAAO;MACPzF,OAAO;MACPsF,YAAY;MACZC,aAAa;MACbC;KACD;EACH;EAEA;;;EAGAG,OAAOA,CAAA;IAAA;IAAA3G,aAAA,GAAAO,CAAA;IACL,MAAMqG,SAAS;IAAA;IAAA,CAAA5G,aAAA,GAAAE,CAAA,QAAG,IAAIW,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAC;IAAAd,aAAA,GAAAE,CAAA;IAC7D,IAAI,CAACM,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC2B,MAAM,CAC9CC,GAAG,IAAI;MAAA;MAAApC,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MAAA,OAAAkC,GAAG,CAACR,SAAS,GAAGgF,SAAS;IAAT,CAAS,CACjC;EACH;EAEA;;;EAGAC,eAAeA,CAAA;IAAA;IAAA7G,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAE,CAAA;IACb;IACA4G,WAAW,CAAC,MAAK;MAAA;MAAA9G,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MACf,IAAI,CAACiG,kBAAkB,EAAE;IAC3B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAEjB;IAAA;IAAAnG,aAAA,GAAAE,CAAA;IACA4G,WAAW,CAAC,MAAK;MAAA;MAAA9G,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAE,CAAA;MACf,IAAI,CAACyG,OAAO,EAAE;IAChB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAElB;IAAA;IAAA3G,aAAA,GAAAE,CAAA;IACA4G,WAAW,CAAC,MAAK;MAAA;MAAA9G,aAAA,GAAAO,CAAA;MACf,MAAMwG,MAAM;MAAA;MAAA,CAAA/G,aAAA,GAAAE,CAAA,SAAG,IAAI,CAACmG,cAAc,EAAE;MAAC;MAAArG,aAAA,GAAAE,CAAA;MACrCC,QAAA,CAAAmB,MAAM,CAAC0F,IAAI,CAAC,oBAAoB,EAAE;QAChCP,OAAO,EAAEM,MAAM,CAACN,OAAO;QACvBH,YAAY,EAAES,MAAM,CAACT,YAAY;QACjCC,aAAa,EAAEQ,MAAM,CAACR,aAAa,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/CtE,SAAS,EAAE,IAAIf,IAAI,EAAE,CAACgB,WAAW;OAClC,CAAC;IACJ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAC;IAAA7B,aAAA,GAAAE,CAAA;IAEnBC,QAAA,CAAAmB,MAAM,CAAC0F,IAAI,CAAC,gCAAgC,CAAC;EAC/C;;AAGF;AAAA;AAAAhH,aAAA,GAAAE,CAAA;AACa+G,OAAA,CAAAC,4BAA4B,GAAG,IAAI7G,4BAA4B,EAAE;AAAC;AAAAL,aAAA,GAAAE,CAAA;AAE/E+G,OAAA,CAAAE,OAAA,GAAeF,OAAA,CAAAC,4BAA4B", "ignoreList": []}