b8f1b6e10ab3631575cc6b0d2fdc8700
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const User_1 = require("../../src/models/User");
const testDatabase_1 = require("../../src/config/testDatabase");
const jest_setup_1 = require("../setup/jest.setup");
// Import app without starting server
let app;
describe('Authentication API Integration Tests', () => {
    beforeAll(async () => {
        await (0, testDatabase_1.setupTestDatabase)();
        // Dynamically import app to avoid server startup
        const { createApp } = await Promise.resolve().then(() => __importStar(require('../../src/app')));
        app = createApp();
    });
    afterAll(async () => {
        await (0, testDatabase_1.cleanupTestDatabase)();
    });
    beforeEach(async () => {
        await (0, testDatabase_1.clearTestData)();
    });
    describe('POST /api/auth/register', () => {
        it('should register a new user successfully', async () => {
            const userData = {
                firstName: 'John',
                lastName: 'Doe',
                email: jest_setup_1.testUtils.randomEmail(),
                password: 'password123',
                phoneNumber: jest_setup_1.testUtils.randomPhoneNumber()
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.user).toBeDefined();
            expect(response.body.data.user.email).toBe(userData.email);
            expect(response.body.data.user.password).toBeUndefined();
            expect(response.body.data.token).toBeDefined();
            // Verify user was created in database
            const user = await User_1.User.findOne({ email: userData.email });
            expect(user).toBeDefined();
            expect(user.firstName).toBe(userData.firstName);
        });
        it('should return validation error for invalid email', async () => {
            const userData = {
                firstName: 'John',
                lastName: 'Doe',
                email: 'invalid-email',
                password: 'password123',
                phoneNumber: jest_setup_1.testUtils.randomPhoneNumber()
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('email');
        });
        it('should return error for duplicate email', async () => {
            const email = jest_setup_1.testUtils.randomEmail();
            const userData = jest_setup_1.testUtils.generateTestUser({ email });
            // Create first user
            await User_1.User.create(userData);
            // Try to create second user with same email
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('email');
        });
        it('should return validation error for weak password', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: '123' });
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('password');
        });
        it('should return validation error for invalid phone number', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ phoneNumber: 'invalid-phone' });
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('phone');
        });
    });
    describe('POST /api/auth/login', () => {
        let testUser;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: 'password123' });
            testUser = await User_1.User.create(userData);
        });
        it('should login user with valid credentials', async () => {
            const loginData = {
                email: testUser.email,
                password: 'password123'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.user).toBeDefined();
            expect(response.body.data.user.email).toBe(testUser.email);
            expect(response.body.data.user.password).toBeUndefined();
            expect(response.body.data.token).toBeDefined();
        });
        it('should return error for invalid email', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'password123'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('Invalid');
        });
        it('should return error for invalid password', async () => {
            const loginData = {
                email: testUser.email,
                password: 'wrongpassword'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('Invalid');
        });
        it('should return error for inactive user', async () => {
            await User_1.User.findByIdAndUpdate(testUser._id, { isActive: false });
            const loginData = {
                email: testUser.email,
                password: 'password123'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('inactive');
        });
    });
    describe('POST /api/auth/logout', () => {
        let testUser;
        let authToken;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: 'password123' });
            testUser = await User_1.User.create(userData);
            authToken = testUser.generateAuthToken();
        });
        it('should logout user successfully', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/logout')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toContain('logout');
        });
        it('should return error without auth token', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/logout')
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('token');
        });
    });
    describe('POST /api/auth/forgot-password', () => {
        let testUser;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            testUser = await User_1.User.create(userData);
        });
        it('should send password reset email for valid email', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/forgot-password')
                .send({ email: testUser.email })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toContain('reset');
        });
        it('should return success even for non-existent email (security)', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/forgot-password')
                .send({ email: '<EMAIL>' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toContain('reset');
        });
        it('should return validation error for invalid email format', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/forgot-password')
                .send({ email: 'invalid-email' })
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('email');
        });
    });
    describe('GET /api/auth/me', () => {
        let testUser;
        let authToken;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            testUser = await User_1.User.create(userData);
            authToken = testUser.generateAuthToken();
        });
        it('should return current user profile', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/me')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.user).toBeDefined();
            expect(response.body.data.user.email).toBe(testUser.email);
            expect(response.body.data.user.password).toBeUndefined();
        });
        it('should return error without auth token', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/me')
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('token');
        });
        it('should return error with invalid token', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/me')
                .set('Authorization', 'Bearer invalid-token')
                .expect(401);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('token');
        });
    });
    describe('PUT /api/auth/change-password', () => {
        let testUser;
        let authToken;
        beforeEach(async () => {
            const userData = jest_setup_1.testUtils.generateTestUser({ password: 'oldpassword123' });
            testUser = await User_1.User.create(userData);
            authToken = testUser.generateAuthToken();
        });
        it('should change password successfully', async () => {
            const passwordData = {
                currentPassword: 'oldpassword123',
                newPassword: 'newpassword123'
            };
            const response = await (0, supertest_1.default)(app)
                .put('/api/auth/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordData)
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toContain('password');
            // Verify password was changed
            const updatedUser = await User_1.User.findById(testUser._id);
            const isNewPasswordValid = await updatedUser.comparePassword('newpassword123');
            expect(isNewPasswordValid).toBe(true);
        });
        it('should return error for incorrect current password', async () => {
            const passwordData = {
                currentPassword: 'wrongpassword',
                newPassword: 'newpassword123'
            };
            const response = await (0, supertest_1.default)(app)
                .put('/api/auth/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('current password');
        });
        it('should return validation error for weak new password', async () => {
            const passwordData = {
                currentPassword: 'oldpassword123',
                newPassword: '123'
            };
            const response = await (0, supertest_1.default)(app)
                .put('/api/auth/change-password')
                .set('Authorization', `Bearer ${authToken}`)
                .send(passwordData)
                .expect(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('password');
        });
    });
    describe('Rate Limiting', () => {
        it('should apply rate limiting to auth endpoints', async () => {
            const userData = jest_setup_1.testUtils.generateTestUser();
            // Make multiple requests quickly
            const requests = Array(25).fill(null).map(() => (0, supertest_1.default)(app)
                .post('/api/auth/register')
                .send(userData));
            const responses = await Promise.all(requests);
            // Some requests should be rate limited
            const rateLimitedResponses = responses.filter(res => res.status === 429);
            expect(rateLimitedResponses.length).toBeGreaterThan(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************