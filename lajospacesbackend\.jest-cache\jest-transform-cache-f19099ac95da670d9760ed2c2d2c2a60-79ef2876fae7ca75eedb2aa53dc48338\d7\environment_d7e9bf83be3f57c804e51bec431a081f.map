{"version": 3, "names": ["cov_24pd3ahc2t", "actualCoverage", "exports", "validateConfig", "dotenv_1", "s", "__importDefault", "require", "path_1", "default", "config", "path", "join", "__dirname", "requiredEnvVars", "process", "env", "NODE_ENV", "b", "envVar", "Error", "PORT", "parseInt", "FRONTEND_URL", "MONGODB_URI", "MONGODB_TEST_URI", "replace", "REDIS_URL", "JWT_SECRET", "JWT_EXPIRES_IN", "JWT_REFRESH_SECRET", "JWT_REFRESH_EXPIRES_IN", "PASSWORD_RESET_SECRET", "PASSWORD_RESET_EXPIRES_IN", "SMTP_HOST", "SMTP_PORT", "SMTP_SECURE", "SMTP_USER", "SMTP_PASS", "FROM_EMAIL", "FROM_NAME", "CLOUDINARY_CLOUD_NAME", "CLOUDINARY_API_KEY", "CLOUDINARY_API_SECRET", "MAX_FILE_SIZE", "ALLOWED_FILE_TYPES", "split", "BCRYPT_ROUNDS", "RATE_LIMIT_WINDOW_MS", "RATE_LIMIT_MAX_REQUESTS", "SESSION_SECRET", "COOKIE_MAX_AGE", "API_VERSION", "LOG_LEVEL", "LOG_FILE", "f", "errors", "push", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\environment.ts"], "sourcesContent": ["import dotenv from 'dotenv';\r\nimport path from 'path';\r\n\r\n// Load environment variables\r\ndotenv.config({ path: path.join(__dirname, '../../.env') });\r\n\r\ninterface Config {\r\n  // Server\r\n  NODE_ENV: string;\r\n  PORT: number;\r\n  FRONTEND_URL: string;\r\n\r\n  // Database\r\n  MONGODB_URI: string;\r\n  MONGODB_TEST_URI: string;\r\n  REDIS_URL: string;\r\n\r\n  // Authentication\r\n  JWT_SECRET: string;\r\n  JWT_EXPIRES_IN: string;\r\n  JWT_REFRESH_SECRET: string;\r\n  JWT_REFRESH_EXPIRES_IN: string;\r\n  PASSWORD_RESET_SECRET: string;\r\n  PASSWORD_RESET_EXPIRES_IN: string;\r\n\r\n  // Email\r\n  SMTP_HOST: string;\r\n  SMTP_PORT: number;\r\n  SMTP_SECURE: boolean;\r\n  SMTP_USER: string;\r\n  SMTP_PASS: string;\r\n  FROM_EMAIL: string;\r\n  FROM_NAME: string;\r\n\r\n  // File Upload\r\n  CLOUDINARY_CLOUD_NAME: string;\r\n  CLOUDINARY_API_KEY: string;\r\n  CLOUDINARY_API_SECRET: string;\r\n  MAX_FILE_SIZE: number;\r\n  ALLOWED_FILE_TYPES: string[];\r\n\r\n  // Security\r\n  BCRYPT_ROUNDS: number;\r\n  RATE_LIMIT_WINDOW_MS: number;\r\n  RATE_LIMIT_MAX_REQUESTS: number;\r\n  SESSION_SECRET: string;\r\n  COOKIE_MAX_AGE: number;\r\n\r\n  // API\r\n  API_VERSION: string;\r\n  LOG_LEVEL: string;\r\n  LOG_FILE: string;\r\n}\r\n\r\nconst requiredEnvVars = [\r\n  'MONGODB_URI',\r\n  'REDIS_URL',\r\n  'JWT_SECRET',\r\n  'JWT_REFRESH_SECRET',\r\n  'PASSWORD_RESET_SECRET',\r\n  'SMTP_USER',\r\n  'SMTP_PASS',\r\n  'CLOUDINARY_CLOUD_NAME',\r\n  'CLOUDINARY_API_KEY',\r\n  'CLOUDINARY_API_SECRET'\r\n];\r\n\r\n// Validate required environment variables (skip in development for now)\r\nif (process.env.NODE_ENV === 'production') {\r\n  for (const envVar of requiredEnvVars) {\r\n    if (!process.env[envVar]) {\r\n      throw new Error(`Missing required environment variable: ${envVar}`);\r\n    }\r\n  }\r\n}\r\n\r\nexport const config: Config = {\r\n  // Server\r\n  NODE_ENV: process.env.NODE_ENV || 'development',\r\n  PORT: parseInt(process.env.PORT || '3001', 10),\r\n  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:8080',\r\n\r\n  // Database\r\n  MONGODB_URI: process.env.MONGODB_URI!,\r\n  MONGODB_TEST_URI: process.env.MONGODB_TEST_URI || (process.env.MONGODB_URI ? process.env.MONGODB_URI.replace('/lajospaces', '/lajospaces_test') : ''),\r\n  REDIS_URL: process.env.REDIS_URL!,\r\n\r\n  // Authentication\r\n  JWT_SECRET: process.env.JWT_SECRET!,\r\n  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '15m',\r\n  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET!,\r\n  JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || '7d',\r\n  PASSWORD_RESET_SECRET: process.env.PASSWORD_RESET_SECRET!,\r\n  PASSWORD_RESET_EXPIRES_IN: process.env.PASSWORD_RESET_EXPIRES_IN || '1h',\r\n\r\n  // Email\r\n  SMTP_HOST: process.env.SMTP_HOST || 'smtp.zoho.com',\r\n  SMTP_PORT: parseInt(process.env.SMTP_PORT || '587', 10),\r\n  SMTP_SECURE: process.env.SMTP_SECURE === 'true' || false, // Use STARTTLS for port 587\r\n  SMTP_USER: process.env.SMTP_USER!,\r\n  SMTP_PASS: process.env.SMTP_PASS!,\r\n  FROM_EMAIL: process.env.FROM_EMAIL || process.env.SMTP_USER!,\r\n  FROM_NAME: process.env.FROM_NAME || 'LajoSpaces',\r\n\r\n  // File Upload\r\n  CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME!,\r\n  CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY!,\r\n  CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET!,\r\n  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB\r\n  ALLOWED_FILE_TYPES: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp,image/gif').split(','),\r\n\r\n  // Security\r\n  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),\r\n  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes\r\n  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),\r\n  SESSION_SECRET: process.env.SESSION_SECRET || 'lajospaces_session_secret',\r\n  COOKIE_MAX_AGE: parseInt(process.env.COOKIE_MAX_AGE || '86400000', 10), // 24 hours\r\n\r\n  // API\r\n  API_VERSION: process.env.API_VERSION || 'v1',\r\n  LOG_LEVEL: process.env.LOG_LEVEL || 'info',\r\n  LOG_FILE: process.env.LOG_FILE || 'logs/app.log'\r\n};\r\n\r\n// Validate configuration\r\nexport function validateConfig(): void {\r\n  const errors: string[] = [];\r\n\r\n  if (config.PORT < 1 || config.PORT > 65535) {\r\n    errors.push('PORT must be between 1 and 65535');\r\n  }\r\n\r\n  if (config.BCRYPT_ROUNDS < 10 || config.BCRYPT_ROUNDS > 15) {\r\n    errors.push('BCRYPT_ROUNDS should be between 10 and 15 for security and performance');\r\n  }\r\n\r\n  if (config.MAX_FILE_SIZE > 50 * 1024 * 1024) { // 50MB\r\n    errors.push('MAX_FILE_SIZE should not exceed 50MB');\r\n  }\r\n\r\n  if (errors.length > 0) {\r\n    throw new Error(`Configuration validation failed:\\n${errors.join('\\n')}`);\r\n  }\r\n}\r\n\r\n// Validate configuration on import\r\nvalidateConfig();\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0DE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEFE,OAAA,CAAAC,cAAA,GAAAA,cAAA;AA7HA,MAAAC,QAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAR,cAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AAEA;AAAA;AAAAP,cAAA,GAAAK,CAAA;AACAD,QAAA,CAAAK,OAAM,CAACC,MAAM,CAAC;EAAEC,IAAI,EAAEH,MAAA,CAAAC,OAAI,CAACG,IAAI,CAACC,SAAS,EAAE,YAAY;AAAC,CAAE,CAAC;AAkD3D,MAAMC,eAAe;AAAA;AAAA,CAAAd,cAAA,GAAAK,CAAA,OAAG,CACtB,aAAa,EACb,WAAW,EACX,YAAY,EACZ,oBAAoB,EACpB,uBAAuB,EACvB,WAAW,EACX,WAAW,EACX,uBAAuB,EACvB,oBAAoB,EACpB,uBAAuB,CACxB;AAED;AAAA;AAAAL,cAAA,GAAAK,CAAA;AACA,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EAAA;EAAAjB,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAK,CAAA;EACzC,KAAK,MAAMc,MAAM,IAAIL,eAAe,EAAE;IAAA;IAAAd,cAAA,GAAAK,CAAA;IACpC,IAAI,CAACU,OAAO,CAACC,GAAG,CAACG,MAAM,CAAC,EAAE;MAAA;MAAAnB,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAK,CAAA;MACxB,MAAM,IAAIe,KAAK,CAAC,0CAA0CD,MAAM,EAAE,CAAC;IACrE,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAkB,CAAA;IAAA;EACH;AACF,CAAC;AAAA;AAAA;EAAAlB,cAAA,GAAAkB,CAAA;AAAA;AAAAlB,cAAA,GAAAK,CAAA;AAEYH,OAAA,CAAAQ,MAAM,GAAW;EAC5B;EACAO,QAAQ;EAAE;EAAA,CAAAjB,cAAA,GAAAkB,CAAA,UAAAH,OAAO,CAACC,GAAG,CAACC,QAAQ;EAAA;EAAA,CAAAjB,cAAA,GAAAkB,CAAA,UAAI,aAAa;EAC/CG,IAAI,EAAEC,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,UAAAH,OAAO,CAACC,GAAG,CAACK,IAAI;EAAA;EAAA,CAAArB,cAAA,GAAAkB,CAAA,UAAI,MAAM,GAAE,EAAE,CAAC;EAC9CK,YAAY;EAAE;EAAA,CAAAvB,cAAA,GAAAkB,CAAA,UAAAH,OAAO,CAACC,GAAG,CAACO,YAAY;EAAA;EAAA,CAAAvB,cAAA,GAAAkB,CAAA,UAAI,uBAAuB;EAEjE;EACAM,WAAW,EAAET,OAAO,CAACC,GAAG,CAACQ,WAAY;EACrCC,gBAAgB;EAAE;EAAA,CAAAzB,cAAA,GAAAkB,CAAA,UAAAH,OAAO,CAACC,GAAG,CAACS,gBAAgB;EAAA;EAAA,CAAAzB,cAAA,GAAAkB,CAAA,UAAKH,OAAO,CAACC,GAAG,CAACQ,WAAW;EAAA;EAAA,CAAAxB,cAAA,GAAAkB,CAAA,UAAGH,OAAO,CAACC,GAAG,CAACQ,WAAW,CAACE,OAAO,CAAC,aAAa,EAAE,kBAAkB,CAAC;EAAA;EAAA,CAAA1B,cAAA,GAAAkB,CAAA,UAAG,EAAE,EAAC;EACrJS,SAAS,EAAEZ,OAAO,CAACC,GAAG,CAACW,SAAU;EAEjC;EACAC,UAAU,EAAEb,OAAO,CAACC,GAAG,CAACY,UAAW;EACnCC,cAAc;EAAE;EAAA,CAAA7B,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACa,cAAc;EAAA;EAAA,CAAA7B,cAAA,GAAAkB,CAAA,WAAI,KAAK;EACnDY,kBAAkB,EAAEf,OAAO,CAACC,GAAG,CAACc,kBAAmB;EACnDC,sBAAsB;EAAE;EAAA,CAAA/B,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACe,sBAAsB;EAAA;EAAA,CAAA/B,cAAA,GAAAkB,CAAA,WAAI,IAAI;EAClEc,qBAAqB,EAAEjB,OAAO,CAACC,GAAG,CAACgB,qBAAsB;EACzDC,yBAAyB;EAAE;EAAA,CAAAjC,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACiB,yBAAyB;EAAA;EAAA,CAAAjC,cAAA,GAAAkB,CAAA,WAAI,IAAI;EAExE;EACAgB,SAAS;EAAE;EAAA,CAAAlC,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACkB,SAAS;EAAA;EAAA,CAAAlC,cAAA,GAAAkB,CAAA,WAAI,eAAe;EACnDiB,SAAS,EAAEb,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACmB,SAAS;EAAA;EAAA,CAAAnC,cAAA,GAAAkB,CAAA,WAAI,KAAK,GAAE,EAAE,CAAC;EACvDkB,WAAW;EAAE;EAAA,CAAApC,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACoB,WAAW,KAAK,MAAM;EAAA;EAAA,CAAApC,cAAA,GAAAkB,CAAA,WAAI,KAAK;EAAE;EAC1DmB,SAAS,EAAEtB,OAAO,CAACC,GAAG,CAACqB,SAAU;EACjCC,SAAS,EAAEvB,OAAO,CAACC,GAAG,CAACsB,SAAU;EACjCC,UAAU;EAAE;EAAA,CAAAvC,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACuB,UAAU;EAAA;EAAA,CAAAvC,cAAA,GAAAkB,CAAA,WAAIH,OAAO,CAACC,GAAG,CAACqB,SAAU;EAC5DG,SAAS;EAAE;EAAA,CAAAxC,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACwB,SAAS;EAAA;EAAA,CAAAxC,cAAA,GAAAkB,CAAA,WAAI,YAAY;EAEhD;EACAuB,qBAAqB,EAAE1B,OAAO,CAACC,GAAG,CAACyB,qBAAsB;EACzDC,kBAAkB,EAAE3B,OAAO,CAACC,GAAG,CAAC0B,kBAAmB;EACnDC,qBAAqB,EAAE5B,OAAO,CAACC,GAAG,CAAC2B,qBAAsB;EACzDC,aAAa,EAAEtB,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAAC4B,aAAa;EAAA;EAAA,CAAA5C,cAAA,GAAAkB,CAAA,WAAI,UAAU,GAAE,EAAE,CAAC;EAAE;EACtE2B,kBAAkB,EAAE;EAAC;EAAA,CAAA7C,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAAC6B,kBAAkB;EAAA;EAAA,CAAA7C,cAAA,GAAAkB,CAAA,WAAI,2CAA2C,GAAE4B,KAAK,CAAC,GAAG,CAAC;EAE9G;EACAC,aAAa,EAAEzB,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAAC+B,aAAa;EAAA;EAAA,CAAA/C,cAAA,GAAAkB,CAAA,WAAI,IAAI,GAAE,EAAE,CAAC;EAC9D8B,oBAAoB,EAAE1B,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACgC,oBAAoB;EAAA;EAAA,CAAAhD,cAAA,GAAAkB,CAAA,WAAI,QAAQ,GAAE,EAAE,CAAC;EAAE;EAClF+B,uBAAuB,EAAE3B,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACiC,uBAAuB;EAAA;EAAA,CAAAjD,cAAA,GAAAkB,CAAA,WAAI,KAAK,GAAE,EAAE,CAAC;EACnFgC,cAAc;EAAE;EAAA,CAAAlD,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACkC,cAAc;EAAA;EAAA,CAAAlD,cAAA,GAAAkB,CAAA,WAAI,2BAA2B;EACzEiC,cAAc,EAAE7B,QAAQ;EAAC;EAAA,CAAAtB,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACmC,cAAc;EAAA;EAAA,CAAAnD,cAAA,GAAAkB,CAAA,WAAI,UAAU,GAAE,EAAE,CAAC;EAAE;EAExE;EACAkC,WAAW;EAAE;EAAA,CAAApD,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACoC,WAAW;EAAA;EAAA,CAAApD,cAAA,GAAAkB,CAAA,WAAI,IAAI;EAC5CmC,SAAS;EAAE;EAAA,CAAArD,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACqC,SAAS;EAAA;EAAA,CAAArD,cAAA,GAAAkB,CAAA,WAAI,MAAM;EAC1CoC,QAAQ;EAAE;EAAA,CAAAtD,cAAA,GAAAkB,CAAA,WAAAH,OAAO,CAACC,GAAG,CAACsC,QAAQ;EAAA;EAAA,CAAAtD,cAAA,GAAAkB,CAAA,WAAI,cAAc;CACjD;AAED;AACA,SAAgBf,cAAcA,CAAA;EAAA;EAAAH,cAAA,GAAAuD,CAAA;EAC5B,MAAMC,MAAM;EAAA;EAAA,CAAAxD,cAAA,GAAAK,CAAA,QAAa,EAAE;EAAC;EAAAL,cAAA,GAAAK,CAAA;EAE5B;EAAI;EAAA,CAAAL,cAAA,GAAAkB,CAAA,WAAAhB,OAAA,CAAAQ,MAAM,CAACW,IAAI,GAAG,CAAC;EAAA;EAAA,CAAArB,cAAA,GAAAkB,CAAA,WAAIhB,OAAA,CAAAQ,MAAM,CAACW,IAAI,GAAG,KAAK,GAAE;IAAA;IAAArB,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAK,CAAA;IAC1CmD,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAAA;EAAA;IAAAzD,cAAA,GAAAkB,CAAA;EAAA;EAAAlB,cAAA,GAAAK,CAAA;EAED;EAAI;EAAA,CAAAL,cAAA,GAAAkB,CAAA,WAAAhB,OAAA,CAAAQ,MAAM,CAACqC,aAAa,GAAG,EAAE;EAAA;EAAA,CAAA/C,cAAA,GAAAkB,CAAA,WAAIhB,OAAA,CAAAQ,MAAM,CAACqC,aAAa,GAAG,EAAE,GAAE;IAAA;IAAA/C,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAK,CAAA;IAC1DmD,MAAM,CAACC,IAAI,CAAC,wEAAwE,CAAC;EACvF,CAAC;EAAA;EAAA;IAAAzD,cAAA,GAAAkB,CAAA;EAAA;EAAAlB,cAAA,GAAAK,CAAA;EAED,IAAIH,OAAA,CAAAQ,MAAM,CAACkC,aAAa,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;IAAA;IAAA5C,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAK,CAAA;IAAE;IAC7CmD,MAAM,CAACC,IAAI,CAAC,sCAAsC,CAAC;EACrD,CAAC;EAAA;EAAA;IAAAzD,cAAA,GAAAkB,CAAA;EAAA;EAAAlB,cAAA,GAAAK,CAAA;EAED,IAAImD,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA1D,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAK,CAAA;IACrB,MAAM,IAAIe,KAAK,CAAC,qCAAqCoC,MAAM,CAAC5C,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAZ,cAAA,GAAAkB,CAAA;EAAA;AACH;AAEA;AAAA;AAAAlB,cAAA,GAAAK,CAAA;AACAF,cAAc,EAAE", "ignoreList": []}