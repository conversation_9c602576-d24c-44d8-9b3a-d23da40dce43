{"version": 3, "names": ["cov_62pd1v51f", "actualCoverage", "s", "appError_1", "require", "auditService_1", "logger_1", "adminMiddleware", "req", "res", "next", "f", "user", "b", "AppError", "role", "auditService", "logEvent", "AuditEventType", "ACCESS_DENIED", "riskLevel", "metadata", "attemptedResource", "userRole", "reason", "ACCESS_GRANTED", "resource", "error", "status", "statusCode", "json", "success", "message", "code", "logger", "exports", "superAdminMiddleware", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\adminMiddleware.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { AppError } from '../utils/appError';\r\nimport { auditService, AuditEventType } from '../services/auditService';\r\nimport { logger } from '../utils/logger';\r\n\r\n/**\r\n * Middleware to check if user has admin privileges\r\n */\r\nexport const adminMiddleware = async (req: Request, res: Response, next: NextFunction) => {\r\n  try {\r\n    // Check if user is authenticated\r\n    if (!req.user) {\r\n      throw new AppError('Authentication required', 401, true, 'UNAUTHORIZED');\r\n    }\r\n\r\n    // Check if user has admin role\r\n    if (req.user.role !== 'admin') {\r\n      // Log unauthorized admin access attempt\r\n      await auditService.logEvent(AuditEventType.ACCESS_DENIED, req, {\r\n        riskLevel: 'high',\r\n        metadata: {\r\n          attemptedResource: 'admin_panel',\r\n          userRole: req.user.role,\r\n          reason: 'insufficient_privileges'\r\n        }\r\n      });\r\n\r\n      throw new AppError('Admin privileges required', 403, true, 'FORBIDDEN');\r\n    }\r\n\r\n    // Log successful admin access\r\n    await auditService.logEvent(AuditEventType.ACCESS_GRANTED, req, {\r\n      metadata: {\r\n        resource: 'admin_panel',\r\n        userRole: req.user.role\r\n      }\r\n    });\r\n\r\n    next();\r\n  } catch (error) {\r\n    if (error instanceof AppError) {\r\n      return res.status(error.statusCode).json({\r\n        success: false,\r\n        error: error.message,\r\n        code: error.code\r\n      });\r\n    }\r\n\r\n    logger.error('Admin middleware error:', error);\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Internal server error'\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Middleware to check if user has super admin privileges\r\n */\r\nexport const superAdminMiddleware = async (req: Request, res: Response, next: NextFunction) => {\r\n  try {\r\n    // Check if user is authenticated\r\n    if (!req.user) {\r\n      throw new AppError('Authentication required', 401, true, 'UNAUTHORIZED');\r\n    }\r\n\r\n    // Check if user has super admin role\r\n    if (req.user.role !== 'super_admin') {\r\n      // Log unauthorized super admin access attempt\r\n      await auditService.logEvent(AuditEventType.ACCESS_DENIED, req, {\r\n        riskLevel: 'critical',\r\n        metadata: {\r\n          attemptedResource: 'super_admin_panel',\r\n          userRole: req.user.role,\r\n          reason: 'insufficient_privileges'\r\n        }\r\n      });\r\n\r\n      throw new AppError('Super admin privileges required', 403, true, 'FORBIDDEN');\r\n    }\r\n\r\n    // Log successful super admin access\r\n    await auditService.logEvent(AuditEventType.ACCESS_GRANTED, req, {\r\n      metadata: {\r\n        resource: 'super_admin_panel',\r\n        userRole: req.user.role\r\n      }\r\n    });\r\n\r\n    next();\r\n  } catch (error) {\r\n    if (error instanceof AppError) {\r\n      return res.status(error.statusCode).json({\r\n        success: false,\r\n        error: error.message,\r\n        code: error.code\r\n      });\r\n    }\r\n\r\n    logger.error('Super admin middleware error:', error);\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Internal server error'\r\n    });\r\n  }\r\n};\r\n\r\nexport default { adminMiddleware, superAdminMiddleware };\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeI;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAdJ,MAAAC,UAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,cAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA;;;AAAA;AAAAJ,aAAA,GAAAE,CAAA;AAGO,MAAMK,eAAe,GAAG,MAAAA,CAAOC,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;EAAA;EAAAV,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAE,CAAA;EACvF,IAAI;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACF;IACA,IAAI,CAACM,GAAG,CAACI,IAAI,EAAE;MAAA;MAAAZ,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MACb,MAAM,IAAIC,UAAA,CAAAW,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAd,aAAA,GAAAa,CAAA;IAAA;IAED;IAAAb,aAAA,GAAAE,CAAA;IACA,IAAIM,GAAG,CAACI,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MAAA;MAAAf,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MAC7B;MACA,MAAMG,cAAA,CAAAW,YAAY,CAACC,QAAQ,CAACZ,cAAA,CAAAa,cAAc,CAACC,aAAa,EAAEX,GAAG,EAAE;QAC7DY,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE;UACRC,iBAAiB,EAAE,aAAa;UAChCC,QAAQ,EAAEf,GAAG,CAACI,IAAI,CAACG,IAAI;UACvBS,MAAM,EAAE;;OAEX,CAAC;MAAC;MAAAxB,aAAA,GAAAE,CAAA;MAEH,MAAM,IAAIC,UAAA,CAAAW,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC;IACzE,CAAC;IAAA;IAAA;MAAAd,aAAA,GAAAa,CAAA;IAAA;IAED;IAAAb,aAAA,GAAAE,CAAA;IACA,MAAMG,cAAA,CAAAW,YAAY,CAACC,QAAQ,CAACZ,cAAA,CAAAa,cAAc,CAACO,cAAc,EAAEjB,GAAG,EAAE;MAC9Da,QAAQ,EAAE;QACRK,QAAQ,EAAE,aAAa;QACvBH,QAAQ,EAAEf,GAAG,CAACI,IAAI,CAACG;;KAEtB,CAAC;IAAC;IAAAf,aAAA,GAAAE,CAAA;IAEHQ,IAAI,EAAE;EACR,CAAC,CAAC,OAAOiB,KAAK,EAAE;IAAA;IAAA3B,aAAA,GAAAE,CAAA;IACd,IAAIyB,KAAK,YAAYxB,UAAA,CAAAW,QAAQ,EAAE;MAAA;MAAAd,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MAC7B,OAAOO,GAAG,CAACmB,MAAM,CAACD,KAAK,CAACE,UAAU,CAAC,CAACC,IAAI,CAAC;QACvCC,OAAO,EAAE,KAAK;QACdJ,KAAK,EAAEA,KAAK,CAACK,OAAO;QACpBC,IAAI,EAAEN,KAAK,CAACM;OACb,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAjC,aAAA,GAAAa,CAAA;IAAA;IAAAb,aAAA,GAAAE,CAAA;IAEDI,QAAA,CAAA4B,MAAM,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAAC;IAAA3B,aAAA,GAAAE,CAAA;IAC/CO,GAAG,CAACmB,MAAM,CAAC,GAAG,CAAC,CAACE,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdJ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC;AAAC;AAAA3B,aAAA,GAAAE,CAAA;AA9CWiC,OAAA,CAAA5B,eAAe,GAAAA,eAAA;AAgD5B;;;AAAA;AAAAP,aAAA,GAAAE,CAAA;AAGO,MAAMkC,oBAAoB,GAAG,MAAAA,CAAO5B,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;EAAA;EAAAV,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAE,CAAA;EAC5F,IAAI;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACF;IACA,IAAI,CAACM,GAAG,CAACI,IAAI,EAAE;MAAA;MAAAZ,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MACb,MAAM,IAAIC,UAAA,CAAAW,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAd,aAAA,GAAAa,CAAA;IAAA;IAED;IAAAb,aAAA,GAAAE,CAAA;IACA,IAAIM,GAAG,CAACI,IAAI,CAACG,IAAI,KAAK,aAAa,EAAE;MAAA;MAAAf,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MACnC;MACA,MAAMG,cAAA,CAAAW,YAAY,CAACC,QAAQ,CAACZ,cAAA,CAAAa,cAAc,CAACC,aAAa,EAAEX,GAAG,EAAE;QAC7DY,SAAS,EAAE,UAAU;QACrBC,QAAQ,EAAE;UACRC,iBAAiB,EAAE,mBAAmB;UACtCC,QAAQ,EAAEf,GAAG,CAACI,IAAI,CAACG,IAAI;UACvBS,MAAM,EAAE;;OAEX,CAAC;MAAC;MAAAxB,aAAA,GAAAE,CAAA;MAEH,MAAM,IAAIC,UAAA,CAAAW,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC;IAC/E,CAAC;IAAA;IAAA;MAAAd,aAAA,GAAAa,CAAA;IAAA;IAED;IAAAb,aAAA,GAAAE,CAAA;IACA,MAAMG,cAAA,CAAAW,YAAY,CAACC,QAAQ,CAACZ,cAAA,CAAAa,cAAc,CAACO,cAAc,EAAEjB,GAAG,EAAE;MAC9Da,QAAQ,EAAE;QACRK,QAAQ,EAAE,mBAAmB;QAC7BH,QAAQ,EAAEf,GAAG,CAACI,IAAI,CAACG;;KAEtB,CAAC;IAAC;IAAAf,aAAA,GAAAE,CAAA;IAEHQ,IAAI,EAAE;EACR,CAAC,CAAC,OAAOiB,KAAK,EAAE;IAAA;IAAA3B,aAAA,GAAAE,CAAA;IACd,IAAIyB,KAAK,YAAYxB,UAAA,CAAAW,QAAQ,EAAE;MAAA;MAAAd,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MAC7B,OAAOO,GAAG,CAACmB,MAAM,CAACD,KAAK,CAACE,UAAU,CAAC,CAACC,IAAI,CAAC;QACvCC,OAAO,EAAE,KAAK;QACdJ,KAAK,EAAEA,KAAK,CAACK,OAAO;QACpBC,IAAI,EAAEN,KAAK,CAACM;OACb,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAjC,aAAA,GAAAa,CAAA;IAAA;IAAAb,aAAA,GAAAE,CAAA;IAEDI,QAAA,CAAA4B,MAAM,CAACP,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IAAC;IAAA3B,aAAA,GAAAE,CAAA;IACrDO,GAAG,CAACmB,MAAM,CAAC,GAAG,CAAC,CAACE,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdJ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC;AAAC;AAAA3B,aAAA,GAAAE,CAAA;AA9CWiC,OAAA,CAAAC,oBAAoB,GAAAA,oBAAA;AA8C/B;AAAApC,aAAA,GAAAE,CAAA;AAEFiC,OAAA,CAAAE,OAAA,GAAe;EAAE9B,eAAe,EAAf4B,OAAA,CAAA5B,eAAe;EAAE6B,oBAAoB,EAApBD,OAAA,CAAAC;AAAoB,CAAE", "ignoreList": []}