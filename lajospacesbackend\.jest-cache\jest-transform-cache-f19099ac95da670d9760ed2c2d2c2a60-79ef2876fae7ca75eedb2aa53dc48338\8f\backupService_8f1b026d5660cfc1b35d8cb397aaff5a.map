{"version": 3, "names": ["cov_2gyqvax0op", "actualCoverage", "child_process_1", "s", "require", "util_1", "promises_1", "__importDefault", "path_1", "logger_1", "environment_1", "execAsync", "promisify", "exec", "BackupService", "constructor", "f", "backupHistory", "BACKUP_DIR", "default", "join", "process", "cwd", "backupConfig", "mongodb", "enabled", "config", "NODE_ENV", "schedule", "retention", "compression", "files", "directories", "storage", "local", "path", "cloud", "provider", "initializeBackupDirectory", "mkdir", "recursive", "logger", "info", "backupDir", "error", "message", "createMongoDBBackup", "backupId", "Date", "now", "timestamp", "filename", "<PERSON><PERSON><PERSON>", "metadata", "id", "type", "size", "status", "push", "mongo<PERSON>ri", "MONGODB_URI", "dbN<PERSON>", "extractDatabaseName", "dumpCommand", "b", "stdout", "stderr", "includes", "Error", "stats", "stat", "checksum", "generateChecksum", "toFixed", "unlink", "cleanupError", "warn", "createFilesBackup", "filter", "dir", "directoryExists", "tarCommand", "warnings", "createFullBackup", "mongoBackup", "filesBackup", "Promise", "all", "totalSize", "restoreMongoDBBackup", "backup", "find", "access", "restoreCommand", "cleanupOldBackups", "mongoRetentionDate", "getTime", "oldMongoBackups", "filesRetentionDate", "oldFileBackups", "oldBackups", "age", "Math", "floor", "cleanedUp", "length", "getBackupStatus", "successful", "failed", "reduce", "sum", "lastBackup", "undefined", "history", "slice", "summary", "total", "url", "URL", "pathname", "substring", "split", "isDirectory", "filePath", "exports", "backupService"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\backupService.ts"], "sourcesContent": ["import { exec } from 'child_process';\r\nimport { promisify } from 'util';\r\nimport fs from 'fs/promises';\r\nimport path from 'path';\r\nimport { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\n\r\n/**\r\n * Backup Service for LajoSpaces Backend\r\n * Handles database backups, file backups, and backup management\r\n */\r\n\r\nconst execAsync = promisify(exec);\r\n\r\nexport interface BackupConfig {\r\n  mongodb: {\r\n    enabled: boolean;\r\n    schedule: string; // cron format\r\n    retention: number; // days\r\n    compression: boolean;\r\n  };\r\n  files: {\r\n    enabled: boolean;\r\n    directories: string[];\r\n    schedule: string;\r\n    retention: number;\r\n  };\r\n  storage: {\r\n    local: {\r\n      enabled: boolean;\r\n      path: string;\r\n    };\r\n    cloud: {\r\n      enabled: boolean;\r\n      provider: 'aws' | 'gcp' | 'azure';\r\n      bucket?: string;\r\n    };\r\n  };\r\n}\r\n\r\nexport interface BackupMetadata {\r\n  id: string;\r\n  type: 'mongodb' | 'files' | 'full';\r\n  timestamp: Date;\r\n  size: number;\r\n  status: 'success' | 'failed' | 'in_progress';\r\n  path: string;\r\n  checksum?: string;\r\n  error?: string;\r\n}\r\n\r\nclass BackupService {\r\n  private backupConfig: BackupConfig;\r\n  private backupHistory: BackupMetadata[] = [];\r\n  private readonly BACKUP_DIR = path.join(process.cwd(), 'backups');\r\n\r\n  constructor() {\r\n    this.backupConfig = {\r\n      mongodb: {\r\n        enabled: config.NODE_ENV === 'production',\r\n        schedule: '0 2 * * *', // Daily at 2 AM\r\n        retention: 30, // 30 days\r\n        compression: true\r\n      },\r\n      files: {\r\n        enabled: config.NODE_ENV === 'production',\r\n        directories: ['uploads', 'logs', 'config'],\r\n        schedule: '0 3 * * *', // Daily at 3 AM\r\n        retention: 7 // 7 days\r\n      },\r\n      storage: {\r\n        local: {\r\n          enabled: true,\r\n          path: this.BACKUP_DIR\r\n        },\r\n        cloud: {\r\n          enabled: false,\r\n          provider: 'aws'\r\n        }\r\n      }\r\n    };\r\n\r\n    this.initializeBackupDirectory();\r\n  }\r\n\r\n  /**\r\n   * Initialize backup directory\r\n   */\r\n  private async initializeBackupDirectory(): Promise<void> {\r\n    try {\r\n      await fs.mkdir(this.BACKUP_DIR, { recursive: true });\r\n      await fs.mkdir(path.join(this.BACKUP_DIR, 'mongodb'), { recursive: true });\r\n      await fs.mkdir(path.join(this.BACKUP_DIR, 'files'), { recursive: true });\r\n      logger.info('Backup directories initialized', { backupDir: this.BACKUP_DIR });\r\n    } catch (error) {\r\n      logger.error('Failed to initialize backup directories', { error: error.message });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create MongoDB backup\r\n   */\r\n  async createMongoDBBackup(): Promise<BackupMetadata> {\r\n    const backupId = `mongodb_${Date.now()}`;\r\n    const timestamp = new Date();\r\n    const filename = `${backupId}.gz`;\r\n    const backupPath = path.join(this.BACKUP_DIR, 'mongodb', filename);\r\n\r\n    const metadata: BackupMetadata = {\r\n      id: backupId,\r\n      type: 'mongodb',\r\n      timestamp,\r\n      size: 0,\r\n      status: 'in_progress',\r\n      path: backupPath\r\n    };\r\n\r\n    this.backupHistory.push(metadata);\r\n\r\n    try {\r\n      logger.info('Starting MongoDB backup', { backupId, path: backupPath });\r\n\r\n      // Extract database name from MongoDB URI\r\n      const mongoUri = config.MONGODB_URI;\r\n      const dbName = this.extractDatabaseName(mongoUri);\r\n\r\n      // Create mongodump command\r\n      const dumpCommand = this.backupConfig.mongodb.compression\r\n        ? `mongodump --uri=\"${mongoUri}\" --db=\"${dbName}\" --archive=\"${backupPath}\" --gzip`\r\n        : `mongodump --uri=\"${mongoUri}\" --db=\"${dbName}\" --archive=\"${backupPath}\"`;\r\n\r\n      // Execute backup\r\n      const { stdout, stderr } = await execAsync(dumpCommand);\r\n\r\n      if (stderr && !stderr.includes('done dumping')) {\r\n        throw new Error(`MongoDB backup failed: ${stderr}`);\r\n      }\r\n\r\n      // Get file size\r\n      const stats = await fs.stat(backupPath);\r\n      metadata.size = stats.size;\r\n      metadata.status = 'success';\r\n\r\n      // Generate checksum\r\n      metadata.checksum = await this.generateChecksum(backupPath);\r\n\r\n      logger.info('MongoDB backup completed successfully', {\r\n        backupId,\r\n        size: `${(metadata.size / 1024 / 1024).toFixed(2)} MB`,\r\n        checksum: metadata.checksum\r\n      });\r\n\r\n      return metadata;\r\n\r\n    } catch (error) {\r\n      metadata.status = 'failed';\r\n      metadata.error = error.message;\r\n\r\n      logger.error('MongoDB backup failed', {\r\n        backupId,\r\n        error: error.message\r\n      });\r\n\r\n      // Clean up failed backup file\r\n      try {\r\n        await fs.unlink(backupPath);\r\n      } catch (cleanupError) {\r\n        logger.warn('Failed to clean up failed backup file', {\r\n          backupId,\r\n          path: backupPath,\r\n          error: cleanupError.message\r\n        });\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create files backup\r\n   */\r\n  async createFilesBackup(): Promise<BackupMetadata> {\r\n    const backupId = `files_${Date.now()}`;\r\n    const timestamp = new Date();\r\n    const filename = `${backupId}.tar.gz`;\r\n    const backupPath = path.join(this.BACKUP_DIR, 'files', filename);\r\n\r\n    const metadata: BackupMetadata = {\r\n      id: backupId,\r\n      type: 'files',\r\n      timestamp,\r\n      size: 0,\r\n      status: 'in_progress',\r\n      path: backupPath\r\n    };\r\n\r\n    this.backupHistory.push(metadata);\r\n\r\n    try {\r\n      logger.info('Starting files backup', { backupId, directories: this.backupConfig.files.directories });\r\n\r\n      // Create tar command for specified directories\r\n      const directories = this.backupConfig.files.directories\r\n        .filter(dir => this.directoryExists(dir))\r\n        .join(' ');\r\n\r\n      if (!directories) {\r\n        throw new Error('No valid directories found for backup');\r\n      }\r\n\r\n      const tarCommand = `tar -czf \"${backupPath}\" ${directories}`;\r\n\r\n      // Execute backup\r\n      const { stdout, stderr } = await execAsync(tarCommand);\r\n\r\n      if (stderr) {\r\n        logger.warn('Files backup completed with warnings', { backupId, warnings: stderr });\r\n      }\r\n\r\n      // Get file size\r\n      const stats = await fs.stat(backupPath);\r\n      metadata.size = stats.size;\r\n      metadata.status = 'success';\r\n\r\n      // Generate checksum\r\n      metadata.checksum = await this.generateChecksum(backupPath);\r\n\r\n      logger.info('Files backup completed successfully', {\r\n        backupId,\r\n        size: `${(metadata.size / 1024 / 1024).toFixed(2)} MB`,\r\n        checksum: metadata.checksum\r\n      });\r\n\r\n      return metadata;\r\n\r\n    } catch (error) {\r\n      metadata.status = 'failed';\r\n      metadata.error = error.message;\r\n\r\n      logger.error('Files backup failed', {\r\n        backupId,\r\n        error: error.message\r\n      });\r\n\r\n      // Clean up failed backup file\r\n      try {\r\n        await fs.unlink(backupPath);\r\n      } catch (cleanupError) {\r\n        logger.warn('Failed to clean up failed backup file', {\r\n          backupId,\r\n          path: backupPath,\r\n          error: cleanupError.message\r\n        });\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create full backup (MongoDB + Files)\r\n   */\r\n  async createFullBackup(): Promise<{ mongodb: BackupMetadata; files: BackupMetadata }> {\r\n    logger.info('Starting full backup');\r\n\r\n    try {\r\n      const [mongoBackup, filesBackup] = await Promise.all([\r\n        this.createMongoDBBackup(),\r\n        this.createFilesBackup()\r\n      ]);\r\n\r\n      logger.info('Full backup completed successfully', {\r\n        mongodb: mongoBackup.id,\r\n        files: filesBackup.id,\r\n        totalSize: `${((mongoBackup.size + filesBackup.size) / 1024 / 1024).toFixed(2)} MB`\r\n      });\r\n\r\n      return { mongodb: mongoBackup, files: filesBackup };\r\n\r\n    } catch (error) {\r\n      logger.error('Full backup failed', { error: error.message });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Restore MongoDB backup\r\n   */\r\n  async restoreMongoDBBackup(backupId: string): Promise<void> {\r\n    const backup = this.backupHistory.find(b => b.id === backupId && b.type === 'mongodb');\r\n    \r\n    if (!backup) {\r\n      throw new Error(`MongoDB backup not found: ${backupId}`);\r\n    }\r\n\r\n    if (backup.status !== 'success') {\r\n      throw new Error(`Cannot restore failed backup: ${backupId}`);\r\n    }\r\n\r\n    try {\r\n      logger.info('Starting MongoDB restore', { backupId, path: backup.path });\r\n\r\n      // Verify backup file exists\r\n      await fs.access(backup.path);\r\n\r\n      // Extract database name from MongoDB URI\r\n      const mongoUri = config.MONGODB_URI;\r\n      const dbName = this.extractDatabaseName(mongoUri);\r\n\r\n      // Create mongorestore command\r\n      const restoreCommand = `mongorestore --uri=\"${mongoUri}\" --db=\"${dbName}\" --archive=\"${backup.path}\" --gzip --drop`;\r\n\r\n      // Execute restore\r\n      const { stdout, stderr } = await execAsync(restoreCommand);\r\n\r\n      if (stderr && !stderr.includes('done')) {\r\n        throw new Error(`MongoDB restore failed: ${stderr}`);\r\n      }\r\n\r\n      logger.info('MongoDB restore completed successfully', { backupId });\r\n\r\n    } catch (error) {\r\n      logger.error('MongoDB restore failed', { backupId, error: error.message });\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean up old backups based on retention policy\r\n   */\r\n  async cleanupOldBackups(): Promise<void> {\r\n    try {\r\n      const now = new Date();\r\n      \r\n      // Clean up MongoDB backups\r\n      const mongoRetentionDate = new Date(now.getTime() - this.backupConfig.mongodb.retention * 24 * 60 * 60 * 1000);\r\n      const oldMongoBackups = this.backupHistory.filter(\r\n        b => b.type === 'mongodb' && b.timestamp < mongoRetentionDate\r\n      );\r\n\r\n      // Clean up file backups\r\n      const filesRetentionDate = new Date(now.getTime() - this.backupConfig.files.retention * 24 * 60 * 60 * 1000);\r\n      const oldFileBackups = this.backupHistory.filter(\r\n        b => b.type === 'files' && b.timestamp < filesRetentionDate\r\n      );\r\n\r\n      const oldBackups = [...oldMongoBackups, ...oldFileBackups];\r\n\r\n      for (const backup of oldBackups) {\r\n        try {\r\n          await fs.unlink(backup.path);\r\n          this.backupHistory = this.backupHistory.filter(b => b.id !== backup.id);\r\n          \r\n          logger.info('Old backup cleaned up', {\r\n            backupId: backup.id,\r\n            type: backup.type,\r\n            age: Math.floor((now.getTime() - backup.timestamp.getTime()) / (24 * 60 * 60 * 1000))\r\n          });\r\n        } catch (error) {\r\n          logger.warn('Failed to clean up old backup', {\r\n            backupId: backup.id,\r\n            path: backup.path,\r\n            error: error.message\r\n          });\r\n        }\r\n      }\r\n\r\n      logger.info('Backup cleanup completed', { cleanedUp: oldBackups.length });\r\n\r\n    } catch (error) {\r\n      logger.error('Backup cleanup failed', { error: error.message });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get backup status and history\r\n   */\r\n  getBackupStatus(): {\r\n    config: BackupConfig;\r\n    history: BackupMetadata[];\r\n    summary: {\r\n      total: number;\r\n      successful: number;\r\n      failed: number;\r\n      totalSize: number;\r\n      lastBackup?: Date;\r\n    };\r\n  } {\r\n    const successful = this.backupHistory.filter(b => b.status === 'success');\r\n    const failed = this.backupHistory.filter(b => b.status === 'failed');\r\n    const totalSize = successful.reduce((sum, b) => sum + b.size, 0);\r\n    const lastBackup = this.backupHistory.length > 0 \r\n      ? this.backupHistory[this.backupHistory.length - 1].timestamp \r\n      : undefined;\r\n\r\n    return {\r\n      config: this.backupConfig,\r\n      history: this.backupHistory.slice(-20), // Last 20 backups\r\n      summary: {\r\n        total: this.backupHistory.length,\r\n        successful: successful.length,\r\n        failed: failed.length,\r\n        totalSize,\r\n        lastBackup\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Helper methods\r\n   */\r\n  private extractDatabaseName(mongoUri: string): string {\r\n    try {\r\n      const url = new URL(mongoUri);\r\n      return url.pathname.substring(1).split('?')[0] || 'lajospaces';\r\n    } catch {\r\n      return 'lajospaces';\r\n    }\r\n  }\r\n\r\n  private async directoryExists(dir: string): Promise<boolean> {\r\n    try {\r\n      const stats = await fs.stat(dir);\r\n      return stats.isDirectory();\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private async generateChecksum(filePath: string): Promise<string> {\r\n    try {\r\n      const { stdout } = await execAsync(`sha256sum \"${filePath}\"`);\r\n      return stdout.split(' ')[0];\r\n    } catch {\r\n      return 'unknown';\r\n    }\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const backupService = new BackupService();\r\n\r\nexport default backupService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUG;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVH,MAAAE,eAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAE,UAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AACA,MAAAI,MAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AACA,MAAAK,QAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAM,aAAA;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA;;;;AAKA,MAAMO,SAAS;AAAA;AAAA,CAAAX,cAAA,GAAAG,CAAA,QAAG,IAAAE,MAAA,CAAAO,SAAS,EAACV,eAAA,CAAAW,IAAI,CAAC;AAuCjC,MAAMC,aAAa;EAKjBC,YAAA;IAAA;IAAAf,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IAHQ,KAAAc,aAAa,GAAqB,EAAE;IAAC;IAAAjB,cAAA,GAAAG,CAAA;IAC5B,KAAAe,UAAU,GAAGV,MAAA,CAAAW,OAAI,CAACC,IAAI,CAACC,OAAO,CAACC,GAAG,EAAE,EAAE,SAAS,CAAC;IAAC;IAAAtB,cAAA,GAAAG,CAAA;IAGhE,IAAI,CAACoB,YAAY,GAAG;MAClBC,OAAO,EAAE;QACPC,OAAO,EAAEf,aAAA,CAAAgB,MAAM,CAACC,QAAQ,KAAK,YAAY;QACzCC,QAAQ,EAAE,WAAW;QAAE;QACvBC,SAAS,EAAE,EAAE;QAAE;QACfC,WAAW,EAAE;OACd;MACDC,KAAK,EAAE;QACLN,OAAO,EAAEf,aAAA,CAAAgB,MAAM,CAACC,QAAQ,KAAK,YAAY;QACzCK,WAAW,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;QAC1CJ,QAAQ,EAAE,WAAW;QAAE;QACvBC,SAAS,EAAE,CAAC,CAAC;OACd;MACDI,OAAO,EAAE;QACPC,KAAK,EAAE;UACLT,OAAO,EAAE,IAAI;UACbU,IAAI,EAAE,IAAI,CAACjB;SACZ;QACDkB,KAAK,EAAE;UACLX,OAAO,EAAE,KAAK;UACdY,QAAQ,EAAE;;;KAGf;IAAC;IAAArC,cAAA,GAAAG,CAAA;IAEF,IAAI,CAACmC,yBAAyB,EAAE;EAClC;EAEA;;;EAGQ,MAAMA,yBAAyBA,CAAA;IAAA;IAAAtC,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IACrC,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACF,MAAMG,UAAA,CAAAa,OAAE,CAACoB,KAAK,CAAC,IAAI,CAACrB,UAAU,EAAE;QAAEsB,SAAS,EAAE;MAAI,CAAE,CAAC;MAAC;MAAAxC,cAAA,GAAAG,CAAA;MACrD,MAAMG,UAAA,CAAAa,OAAE,CAACoB,KAAK,CAAC/B,MAAA,CAAAW,OAAI,CAACC,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,SAAS,CAAC,EAAE;QAAEsB,SAAS,EAAE;MAAI,CAAE,CAAC;MAAC;MAAAxC,cAAA,GAAAG,CAAA;MAC3E,MAAMG,UAAA,CAAAa,OAAE,CAACoB,KAAK,CAAC/B,MAAA,CAAAW,OAAI,CAACC,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,OAAO,CAAC,EAAE;QAAEsB,SAAS,EAAE;MAAI,CAAE,CAAC;MAAC;MAAAxC,cAAA,GAAAG,CAAA;MACzEM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,gCAAgC,EAAE;QAAEC,SAAS,EAAE,IAAI,CAACzB;MAAU,CAAE,CAAC;IAC/E,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,yCAAyC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAO,CAAE,CAAC;IACnF;EACF;EAEA;;;EAGA,MAAMC,mBAAmBA,CAAA;IAAA;IAAA9C,cAAA,GAAAgB,CAAA;IACvB,MAAM+B,QAAQ;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,QAAG,WAAW6C,IAAI,CAACC,GAAG,EAAE,EAAE;IACxC,MAAMC,SAAS;IAAA;IAAA,CAAAlD,cAAA,GAAAG,CAAA,QAAG,IAAI6C,IAAI,EAAE;IAC5B,MAAMG,QAAQ;IAAA;IAAA,CAAAnD,cAAA,GAAAG,CAAA,QAAG,GAAG4C,QAAQ,KAAK;IACjC,MAAMK,UAAU;IAAA;IAAA,CAAApD,cAAA,GAAAG,CAAA,QAAGK,MAAA,CAAAW,OAAI,CAACC,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,SAAS,EAAEiC,QAAQ,CAAC;IAElE,MAAME,QAAQ;IAAA;IAAA,CAAArD,cAAA,GAAAG,CAAA,QAAmB;MAC/BmD,EAAE,EAAEP,QAAQ;MACZQ,IAAI,EAAE,SAAS;MACfL,SAAS;MACTM,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,aAAa;MACrBtB,IAAI,EAAEiB;KACP;IAAC;IAAApD,cAAA,GAAAG,CAAA;IAEF,IAAI,CAACc,aAAa,CAACyC,IAAI,CAACL,QAAQ,CAAC;IAAC;IAAArD,cAAA,GAAAG,CAAA;IAElC,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACFM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE;QAAEK,QAAQ;QAAEZ,IAAI,EAAEiB;MAAU,CAAE,CAAC;MAEtE;MACA,MAAMO,QAAQ;MAAA;MAAA,CAAA3D,cAAA,GAAAG,CAAA,QAAGO,aAAA,CAAAgB,MAAM,CAACkC,WAAW;MACnC,MAAMC,MAAM;MAAA;MAAA,CAAA7D,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC2D,mBAAmB,CAACH,QAAQ,CAAC;MAEjD;MACA,MAAMI,WAAW;MAAA;MAAA,CAAA/D,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACoB,YAAY,CAACC,OAAO,CAACM,WAAW;MAAA;MAAA,CAAA9B,cAAA,GAAAgE,CAAA,UACrD,oBAAoBL,QAAQ,WAAWE,MAAM,gBAAgBT,UAAU,UAAU;MAAA;MAAA,CAAApD,cAAA,GAAAgE,CAAA,UACjF,oBAAoBL,QAAQ,WAAWE,MAAM,gBAAgBT,UAAU,GAAG;MAE9E;MACA,MAAM;QAAEa,MAAM;QAAEC;MAAM,CAAE;MAAA;MAAA,CAAAlE,cAAA,GAAAG,CAAA,QAAG,MAAMQ,SAAS,CAACoD,WAAW,CAAC;MAAC;MAAA/D,cAAA,GAAAG,CAAA;MAExD;MAAI;MAAA,CAAAH,cAAA,GAAAgE,CAAA,UAAAE,MAAM;MAAA;MAAA,CAAAlE,cAAA,GAAAgE,CAAA,UAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,cAAc,CAAC,GAAE;QAAA;QAAAnE,cAAA,GAAAgE,CAAA;QAAAhE,cAAA,GAAAG,CAAA;QAC9C,MAAM,IAAIiE,KAAK,CAAC,0BAA0BF,MAAM,EAAE,CAAC;MACrD,CAAC;MAAA;MAAA;QAAAlE,cAAA,GAAAgE,CAAA;MAAA;MAED;MACA,MAAMK,KAAK;MAAA;MAAA,CAAArE,cAAA,GAAAG,CAAA,QAAG,MAAMG,UAAA,CAAAa,OAAE,CAACmD,IAAI,CAAClB,UAAU,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MACxCkD,QAAQ,CAACG,IAAI,GAAGa,KAAK,CAACb,IAAI;MAAC;MAAAxD,cAAA,GAAAG,CAAA;MAC3BkD,QAAQ,CAACI,MAAM,GAAG,SAAS;MAE3B;MAAA;MAAAzD,cAAA,GAAAG,CAAA;MACAkD,QAAQ,CAACkB,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACpB,UAAU,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAE5DM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,uCAAuC,EAAE;QACnDK,QAAQ;QACRS,IAAI,EAAE,GAAG,CAACH,QAAQ,CAACG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEiB,OAAO,CAAC,CAAC,CAAC,KAAK;QACtDF,QAAQ,EAAElB,QAAQ,CAACkB;OACpB,CAAC;MAAC;MAAAvE,cAAA,GAAAG,CAAA;MAEH,OAAOkD,QAAQ;IAEjB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdkD,QAAQ,CAACI,MAAM,GAAG,QAAQ;MAAC;MAAAzD,cAAA,GAAAG,CAAA;MAC3BkD,QAAQ,CAACT,KAAK,GAAGA,KAAK,CAACC,OAAO;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAE/BM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,uBAAuB,EAAE;QACpCG,QAAQ;QACRH,KAAK,EAAEA,KAAK,CAACC;OACd,CAAC;MAEF;MAAA;MAAA7C,cAAA,GAAAG,CAAA;MACA,IAAI;QAAA;QAAAH,cAAA,GAAAG,CAAA;QACF,MAAMG,UAAA,CAAAa,OAAE,CAACuD,MAAM,CAACtB,UAAU,CAAC;MAC7B,CAAC,CAAC,OAAOuB,YAAY,EAAE;QAAA;QAAA3E,cAAA,GAAAG,CAAA;QACrBM,QAAA,CAAAgC,MAAM,CAACmC,IAAI,CAAC,uCAAuC,EAAE;UACnD7B,QAAQ;UACRZ,IAAI,EAAEiB,UAAU;UAChBR,KAAK,EAAE+B,YAAY,CAAC9B;SACrB,CAAC;MACJ;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAED,MAAMyC,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMiC,iBAAiBA,CAAA;IAAA;IAAA7E,cAAA,GAAAgB,CAAA;IACrB,MAAM+B,QAAQ;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,QAAG,SAAS6C,IAAI,CAACC,GAAG,EAAE,EAAE;IACtC,MAAMC,SAAS;IAAA;IAAA,CAAAlD,cAAA,GAAAG,CAAA,QAAG,IAAI6C,IAAI,EAAE;IAC5B,MAAMG,QAAQ;IAAA;IAAA,CAAAnD,cAAA,GAAAG,CAAA,QAAG,GAAG4C,QAAQ,SAAS;IACrC,MAAMK,UAAU;IAAA;IAAA,CAAApD,cAAA,GAAAG,CAAA,QAAGK,MAAA,CAAAW,OAAI,CAACC,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,OAAO,EAAEiC,QAAQ,CAAC;IAEhE,MAAME,QAAQ;IAAA;IAAA,CAAArD,cAAA,GAAAG,CAAA,QAAmB;MAC/BmD,EAAE,EAAEP,QAAQ;MACZQ,IAAI,EAAE,OAAO;MACbL,SAAS;MACTM,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,aAAa;MACrBtB,IAAI,EAAEiB;KACP;IAAC;IAAApD,cAAA,GAAAG,CAAA;IAEF,IAAI,CAACc,aAAa,CAACyC,IAAI,CAACL,QAAQ,CAAC;IAAC;IAAArD,cAAA,GAAAG,CAAA;IAElC,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACFM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAE;QAAEK,QAAQ;QAAEf,WAAW,EAAE,IAAI,CAACT,YAAY,CAACQ,KAAK,CAACC;MAAW,CAAE,CAAC;MAEpG;MACA,MAAMA,WAAW;MAAA;MAAA,CAAAhC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACoB,YAAY,CAACQ,KAAK,CAACC,WAAW,CACpD8C,MAAM,CAACC,GAAG,IAAI;QAAA;QAAA/E,cAAA,GAAAgB,CAAA;QAAAhB,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC6E,eAAe,CAACD,GAAG,CAAC;MAAD,CAAC,CAAC,CACxC3D,IAAI,CAAC,GAAG,CAAC;MAAC;MAAApB,cAAA,GAAAG,CAAA;MAEb,IAAI,CAAC6B,WAAW,EAAE;QAAA;QAAAhC,cAAA,GAAAgE,CAAA;QAAAhE,cAAA,GAAAG,CAAA;QAChB,MAAM,IAAIiE,KAAK,CAAC,uCAAuC,CAAC;MAC1D,CAAC;MAAA;MAAA;QAAApE,cAAA,GAAAgE,CAAA;MAAA;MAED,MAAMiB,UAAU;MAAA;MAAA,CAAAjF,cAAA,GAAAG,CAAA,QAAG,aAAaiD,UAAU,KAAKpB,WAAW,EAAE;MAE5D;MACA,MAAM;QAAEiC,MAAM;QAAEC;MAAM,CAAE;MAAA;MAAA,CAAAlE,cAAA,GAAAG,CAAA,QAAG,MAAMQ,SAAS,CAACsE,UAAU,CAAC;MAAC;MAAAjF,cAAA,GAAAG,CAAA;MAEvD,IAAI+D,MAAM,EAAE;QAAA;QAAAlE,cAAA,GAAAgE,CAAA;QAAAhE,cAAA,GAAAG,CAAA;QACVM,QAAA,CAAAgC,MAAM,CAACmC,IAAI,CAAC,sCAAsC,EAAE;UAAE7B,QAAQ;UAAEmC,QAAQ,EAAEhB;QAAM,CAAE,CAAC;MACrF,CAAC;MAAA;MAAA;QAAAlE,cAAA,GAAAgE,CAAA;MAAA;MAED;MACA,MAAMK,KAAK;MAAA;MAAA,CAAArE,cAAA,GAAAG,CAAA,QAAG,MAAMG,UAAA,CAAAa,OAAE,CAACmD,IAAI,CAAClB,UAAU,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MACxCkD,QAAQ,CAACG,IAAI,GAAGa,KAAK,CAACb,IAAI;MAAC;MAAAxD,cAAA,GAAAG,CAAA;MAC3BkD,QAAQ,CAACI,MAAM,GAAG,SAAS;MAE3B;MAAA;MAAAzD,cAAA,GAAAG,CAAA;MACAkD,QAAQ,CAACkB,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACpB,UAAU,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAE5DM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,qCAAqC,EAAE;QACjDK,QAAQ;QACRS,IAAI,EAAE,GAAG,CAACH,QAAQ,CAACG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEiB,OAAO,CAAC,CAAC,CAAC,KAAK;QACtDF,QAAQ,EAAElB,QAAQ,CAACkB;OACpB,CAAC;MAAC;MAAAvE,cAAA,GAAAG,CAAA;MAEH,OAAOkD,QAAQ;IAEjB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdkD,QAAQ,CAACI,MAAM,GAAG,QAAQ;MAAC;MAAAzD,cAAA,GAAAG,CAAA;MAC3BkD,QAAQ,CAACT,KAAK,GAAGA,KAAK,CAACC,OAAO;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAE/BM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,qBAAqB,EAAE;QAClCG,QAAQ;QACRH,KAAK,EAAEA,KAAK,CAACC;OACd,CAAC;MAEF;MAAA;MAAA7C,cAAA,GAAAG,CAAA;MACA,IAAI;QAAA;QAAAH,cAAA,GAAAG,CAAA;QACF,MAAMG,UAAA,CAAAa,OAAE,CAACuD,MAAM,CAACtB,UAAU,CAAC;MAC7B,CAAC,CAAC,OAAOuB,YAAY,EAAE;QAAA;QAAA3E,cAAA,GAAAG,CAAA;QACrBM,QAAA,CAAAgC,MAAM,CAACmC,IAAI,CAAC,uCAAuC,EAAE;UACnD7B,QAAQ;UACRZ,IAAI,EAAEiB,UAAU;UAChBR,KAAK,EAAE+B,YAAY,CAAC9B;SACrB,CAAC;MACJ;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAED,MAAMyC,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMuC,gBAAgBA,CAAA;IAAA;IAAAnF,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IACpBM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,sBAAsB,CAAC;IAAC;IAAA1C,cAAA,GAAAG,CAAA;IAEpC,IAAI;MACF,MAAM,CAACiF,WAAW,EAAEC,WAAW,CAAC;MAAA;MAAA,CAAArF,cAAA,GAAAG,CAAA,QAAG,MAAMmF,OAAO,CAACC,GAAG,CAAC,CACnD,IAAI,CAACzC,mBAAmB,EAAE,EAC1B,IAAI,CAAC+B,iBAAiB,EAAE,CACzB,CAAC;MAAC;MAAA7E,cAAA,GAAAG,CAAA;MAEHM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,oCAAoC,EAAE;QAChDlB,OAAO,EAAE4D,WAAW,CAAC9B,EAAE;QACvBvB,KAAK,EAAEsD,WAAW,CAAC/B,EAAE;QACrBkC,SAAS,EAAE,GAAG,CAAC,CAACJ,WAAW,CAAC5B,IAAI,GAAG6B,WAAW,CAAC7B,IAAI,IAAI,IAAI,GAAG,IAAI,EAAEiB,OAAO,CAAC,CAAC,CAAC;OAC/E,CAAC;MAAC;MAAAzE,cAAA,GAAAG,CAAA;MAEH,OAAO;QAAEqB,OAAO,EAAE4D,WAAW;QAAErD,KAAK,EAAEsD;MAAW,CAAE;IAErD,CAAC,CAAC,OAAOzC,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,oBAAoB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAO,CAAE,CAAC;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAC7D,MAAMyC,KAAK;IACb;EACF;EAEA;;;EAGA,MAAM6C,oBAAoBA,CAAC1C,QAAgB;IAAA;IAAA/C,cAAA,GAAAgB,CAAA;IACzC,MAAM0E,MAAM;IAAA;IAAA,CAAA1F,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACc,aAAa,CAAC0E,IAAI,CAAC3B,CAAC,IAAI;MAAA;MAAAhE,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAG,CAAA;MAAA,kCAAAH,cAAA,GAAAgE,CAAA,UAAAA,CAAC,CAACV,EAAE,KAAKP,QAAQ;MAAA;MAAA,CAAA/C,cAAA,GAAAgE,CAAA,UAAIA,CAAC,CAACT,IAAI,KAAK,SAAS;IAAT,CAAS,CAAC;IAAC;IAAAvD,cAAA,GAAAG,CAAA;IAEvF,IAAI,CAACuF,MAAM,EAAE;MAAA;MAAA1F,cAAA,GAAAgE,CAAA;MAAAhE,cAAA,GAAAG,CAAA;MACX,MAAM,IAAIiE,KAAK,CAAC,6BAA6BrB,QAAQ,EAAE,CAAC;IAC1D,CAAC;IAAA;IAAA;MAAA/C,cAAA,GAAAgE,CAAA;IAAA;IAAAhE,cAAA,GAAAG,CAAA;IAED,IAAIuF,MAAM,CAACjC,MAAM,KAAK,SAAS,EAAE;MAAA;MAAAzD,cAAA,GAAAgE,CAAA;MAAAhE,cAAA,GAAAG,CAAA;MAC/B,MAAM,IAAIiE,KAAK,CAAC,iCAAiCrB,QAAQ,EAAE,CAAC;IAC9D,CAAC;IAAA;IAAA;MAAA/C,cAAA,GAAAgE,CAAA;IAAA;IAAAhE,cAAA,GAAAG,CAAA;IAED,IAAI;MAAA;MAAAH,cAAA,GAAAG,CAAA;MACFM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAE;QAAEK,QAAQ;QAAEZ,IAAI,EAAEuD,MAAM,CAACvD;MAAI,CAAE,CAAC;MAExE;MAAA;MAAAnC,cAAA,GAAAG,CAAA;MACA,MAAMG,UAAA,CAAAa,OAAE,CAACyE,MAAM,CAACF,MAAM,CAACvD,IAAI,CAAC;MAE5B;MACA,MAAMwB,QAAQ;MAAA;MAAA,CAAA3D,cAAA,GAAAG,CAAA,QAAGO,aAAA,CAAAgB,MAAM,CAACkC,WAAW;MACnC,MAAMC,MAAM;MAAA;MAAA,CAAA7D,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC2D,mBAAmB,CAACH,QAAQ,CAAC;MAEjD;MACA,MAAMkC,cAAc;MAAA;MAAA,CAAA7F,cAAA,GAAAG,CAAA,QAAG,uBAAuBwD,QAAQ,WAAWE,MAAM,gBAAgB6B,MAAM,CAACvD,IAAI,iBAAiB;MAEnH;MACA,MAAM;QAAE8B,MAAM;QAAEC;MAAM,CAAE;MAAA;MAAA,CAAAlE,cAAA,GAAAG,CAAA,QAAG,MAAMQ,SAAS,CAACkF,cAAc,CAAC;MAAC;MAAA7F,cAAA,GAAAG,CAAA;MAE3D;MAAI;MAAA,CAAAH,cAAA,GAAAgE,CAAA,WAAAE,MAAM;MAAA;MAAA,CAAAlE,cAAA,GAAAgE,CAAA,WAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,GAAE;QAAA;QAAAnE,cAAA,GAAAgE,CAAA;QAAAhE,cAAA,GAAAG,CAAA;QACtC,MAAM,IAAIiE,KAAK,CAAC,2BAA2BF,MAAM,EAAE,CAAC;MACtD,CAAC;MAAA;MAAA;QAAAlE,cAAA,GAAAgE,CAAA;MAAA;MAAAhE,cAAA,GAAAG,CAAA;MAEDM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,wCAAwC,EAAE;QAAEK;MAAQ,CAAE,CAAC;IAErE,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,wBAAwB,EAAE;QAAEG,QAAQ;QAAEH,KAAK,EAAEA,KAAK,CAACC;MAAO,CAAE,CAAC;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAC3E,MAAMyC,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMkD,iBAAiBA,CAAA;IAAA;IAAA9F,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IACrB,IAAI;MACF,MAAM8C,GAAG;MAAA;MAAA,CAAAjD,cAAA,GAAAG,CAAA,SAAG,IAAI6C,IAAI,EAAE;MAEtB;MACA,MAAM+C,kBAAkB;MAAA;MAAA,CAAA/F,cAAA,GAAAG,CAAA,SAAG,IAAI6C,IAAI,CAACC,GAAG,CAAC+C,OAAO,EAAE,GAAG,IAAI,CAACzE,YAAY,CAACC,OAAO,CAACK,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC9G,MAAMoE,eAAe;MAAA;MAAA,CAAAjG,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACc,aAAa,CAAC6D,MAAM,CAC/Cd,CAAC,IAAI;QAAA;QAAAhE,cAAA,GAAAgB,CAAA;QAAAhB,cAAA,GAAAG,CAAA;QAAA,kCAAAH,cAAA,GAAAgE,CAAA,WAAAA,CAAC,CAACT,IAAI,KAAK,SAAS;QAAA;QAAA,CAAAvD,cAAA,GAAAgE,CAAA,WAAIA,CAAC,CAACd,SAAS,GAAG6C,kBAAkB;MAAlB,CAAkB,CAC9D;MAED;MACA,MAAMG,kBAAkB;MAAA;MAAA,CAAAlG,cAAA,GAAAG,CAAA,SAAG,IAAI6C,IAAI,CAACC,GAAG,CAAC+C,OAAO,EAAE,GAAG,IAAI,CAACzE,YAAY,CAACQ,KAAK,CAACF,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC5G,MAAMsE,cAAc;MAAA;MAAA,CAAAnG,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACc,aAAa,CAAC6D,MAAM,CAC9Cd,CAAC,IAAI;QAAA;QAAAhE,cAAA,GAAAgB,CAAA;QAAAhB,cAAA,GAAAG,CAAA;QAAA,kCAAAH,cAAA,GAAAgE,CAAA,WAAAA,CAAC,CAACT,IAAI,KAAK,OAAO;QAAA;QAAA,CAAAvD,cAAA,GAAAgE,CAAA,WAAIA,CAAC,CAACd,SAAS,GAAGgD,kBAAkB;MAAlB,CAAkB,CAC5D;MAED,MAAME,UAAU;MAAA;MAAA,CAAApG,cAAA,GAAAG,CAAA,SAAG,CAAC,GAAG8F,eAAe,EAAE,GAAGE,cAAc,CAAC;MAAC;MAAAnG,cAAA,GAAAG,CAAA;MAE3D,KAAK,MAAMuF,MAAM,IAAIU,UAAU,EAAE;QAAA;QAAApG,cAAA,GAAAG,CAAA;QAC/B,IAAI;UAAA;UAAAH,cAAA,GAAAG,CAAA;UACF,MAAMG,UAAA,CAAAa,OAAE,CAACuD,MAAM,CAACgB,MAAM,CAACvD,IAAI,CAAC;UAAC;UAAAnC,cAAA,GAAAG,CAAA;UAC7B,IAAI,CAACc,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC6D,MAAM,CAACd,CAAC,IAAI;YAAA;YAAAhE,cAAA,GAAAgB,CAAA;YAAAhB,cAAA,GAAAG,CAAA;YAAA,OAAA6D,CAAC,CAACV,EAAE,KAAKoC,MAAM,CAACpC,EAAE;UAAF,CAAE,CAAC;UAAC;UAAAtD,cAAA,GAAAG,CAAA;UAExEM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAE;YACnCK,QAAQ,EAAE2C,MAAM,CAACpC,EAAE;YACnBC,IAAI,EAAEmC,MAAM,CAACnC,IAAI;YACjB8C,GAAG,EAAEC,IAAI,CAACC,KAAK,CAAC,CAACtD,GAAG,CAAC+C,OAAO,EAAE,GAAGN,MAAM,CAACxC,SAAS,CAAC8C,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;WACrF,CAAC;QACJ,CAAC,CAAC,OAAOpD,KAAK,EAAE;UAAA;UAAA5C,cAAA,GAAAG,CAAA;UACdM,QAAA,CAAAgC,MAAM,CAACmC,IAAI,CAAC,+BAA+B,EAAE;YAC3C7B,QAAQ,EAAE2C,MAAM,CAACpC,EAAE;YACnBnB,IAAI,EAAEuD,MAAM,CAACvD,IAAI;YACjBS,KAAK,EAAEA,KAAK,CAACC;WACd,CAAC;QACJ;MACF;MAAC;MAAA7C,cAAA,GAAAG,CAAA;MAEDM,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAE;QAAE8D,SAAS,EAAEJ,UAAU,CAACK;MAAM,CAAE,CAAC;IAE3E,CAAC,CAAC,OAAO7D,KAAK,EAAE;MAAA;MAAA5C,cAAA,GAAAG,CAAA;MACdM,QAAA,CAAAgC,MAAM,CAACG,KAAK,CAAC,uBAAuB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAO,CAAE,CAAC;IACjE;EACF;EAEA;;;EAGA6D,eAAeA,CAAA;IAAA;IAAA1G,cAAA,GAAAgB,CAAA;IAWb,MAAM2F,UAAU;IAAA;IAAA,CAAA3G,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACc,aAAa,CAAC6D,MAAM,CAACd,CAAC,IAAI;MAAA;MAAAhE,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAG,CAAA;MAAA,OAAA6D,CAAC,CAACP,MAAM,KAAK,SAAS;IAAT,CAAS,CAAC;IACzE,MAAMmD,MAAM;IAAA;IAAA,CAAA5G,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACc,aAAa,CAAC6D,MAAM,CAACd,CAAC,IAAI;MAAA;MAAAhE,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAG,CAAA;MAAA,OAAA6D,CAAC,CAACP,MAAM,KAAK,QAAQ;IAAR,CAAQ,CAAC;IACpE,MAAM+B,SAAS;IAAA;IAAA,CAAAxF,cAAA,GAAAG,CAAA,SAAGwG,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAE9C,CAAC,KAAK;MAAA;MAAAhE,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAG,CAAA;MAAA,OAAA2G,GAAG,GAAG9C,CAAC,CAACR,IAAI;IAAJ,CAAI,EAAE,CAAC,CAAC;IAChE,MAAMuD,UAAU;IAAA;IAAA,CAAA/G,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACc,aAAa,CAACwF,MAAM,GAAG,CAAC;IAAA;IAAA,CAAAzG,cAAA,GAAAgE,CAAA,WAC5C,IAAI,CAAC/C,aAAa,CAAC,IAAI,CAACA,aAAa,CAACwF,MAAM,GAAG,CAAC,CAAC,CAACvD,SAAS;IAAA;IAAA,CAAAlD,cAAA,GAAAgE,CAAA,WAC3DgD,SAAS;IAAC;IAAAhH,cAAA,GAAAG,CAAA;IAEd,OAAO;MACLuB,MAAM,EAAE,IAAI,CAACH,YAAY;MACzB0F,OAAO,EAAE,IAAI,CAAChG,aAAa,CAACiG,KAAK,CAAC,CAAC,EAAE,CAAC;MAAE;MACxCC,OAAO,EAAE;QACPC,KAAK,EAAE,IAAI,CAACnG,aAAa,CAACwF,MAAM;QAChCE,UAAU,EAAEA,UAAU,CAACF,MAAM;QAC7BG,MAAM,EAAEA,MAAM,CAACH,MAAM;QACrBjB,SAAS;QACTuB;;KAEH;EACH;EAEA;;;EAGQjD,mBAAmBA,CAACH,QAAgB;IAAA;IAAA3D,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IAC1C,IAAI;MACF,MAAMkH,GAAG;MAAA;MAAA,CAAArH,cAAA,GAAAG,CAAA,SAAG,IAAImH,GAAG,CAAC3D,QAAQ,CAAC;MAAC;MAAA3D,cAAA,GAAAG,CAAA;MAC9B,OAAO,2BAAAH,cAAA,GAAAgE,CAAA,WAAAqD,GAAG,CAACE,QAAQ,CAACC,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAA;MAAA,CAAAzH,cAAA,GAAAgE,CAAA,WAAI,YAAY;IAChE,CAAC,CAAC,MAAM;MAAA;MAAAhE,cAAA,GAAAG,CAAA;MACN,OAAO,YAAY;IACrB;EACF;EAEQ,MAAM6E,eAAeA,CAACD,GAAW;IAAA;IAAA/E,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IACvC,IAAI;MACF,MAAMkE,KAAK;MAAA;MAAA,CAAArE,cAAA,GAAAG,CAAA,SAAG,MAAMG,UAAA,CAAAa,OAAE,CAACmD,IAAI,CAACS,GAAG,CAAC;MAAC;MAAA/E,cAAA,GAAAG,CAAA;MACjC,OAAOkE,KAAK,CAACqD,WAAW,EAAE;IAC5B,CAAC,CAAC,MAAM;MAAA;MAAA1H,cAAA,GAAAG,CAAA;MACN,OAAO,KAAK;IACd;EACF;EAEQ,MAAMqE,gBAAgBA,CAACmD,QAAgB;IAAA;IAAA3H,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAG,CAAA;IAC7C,IAAI;MACF,MAAM;QAAE8D;MAAM,CAAE;MAAA;MAAA,CAAAjE,cAAA,GAAAG,CAAA,SAAG,MAAMQ,SAAS,CAAC,cAAcgH,QAAQ,GAAG,CAAC;MAAC;MAAA3H,cAAA,GAAAG,CAAA;MAC9D,OAAO8D,MAAM,CAACwD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,MAAM;MAAA;MAAAzH,cAAA,GAAAG,CAAA;MACN,OAAO,SAAS;IAClB;EACF;;AAGF;AAAA;AAAAH,cAAA,GAAAG,CAAA;AACayH,OAAA,CAAAC,aAAa,GAAG,IAAI/G,aAAa,EAAE;AAAC;AAAAd,cAAA,GAAAG,CAAA;AAEjDyH,OAAA,CAAAzG,OAAA,GAAeyG,OAAA,CAAAC,aAAa", "ignoreList": []}