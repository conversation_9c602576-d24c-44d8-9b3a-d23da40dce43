{"version": 3, "names": ["cov_1io6lkpmz1", "actualCoverage", "s", "express_1", "require", "auth_1", "adminMiddleware_1", "auditService_1", "cacheService_1", "sessionService_1", "tokenService_1", "queryOptimization_1", "rateLimiting_1", "rateLimiting_2", "sanitization_1", "router", "Router", "use", "adminRateLimit", "authenticate", "adminMiddleware", "strictSanitization", "get", "req", "res", "f", "filters", "userId", "query", "eventType", "riskLevel", "startDate", "b", "Date", "undefined", "endDate", "page", "parseInt", "limit", "result", "auditService", "getAuditLogs", "logEvent", "AuditEventType", "DATA_VIEWED", "resource", "metadata", "json", "success", "data", "error", "status", "timeframe", "stats", "getAuditStats", "cacheService", "getStats", "isConnected", "queryOptimizer", "getQueryStats", "getRateLimitStats", "sessionService", "getSessionStats", "tokenService", "getTokenStats", "post", "cleanupType", "body", "cleanupRateLimitData", "tokenCount", "cleanupExpiredTokens", "sessionCount", "cleanupExpiredSessions", "clearMetrics", "MAINTENANCE_MODE", "message", "health", "timestamp", "toISOString", "services", "cache", "connected", "sessions", "tokens", "rateLimiting", "queries", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { authenticate as authMiddleware } from '../middleware/auth';\r\nimport { adminMiddleware } from '../middleware/adminMiddleware';\r\nimport { auditService, AuditEventType, RiskLevel } from '../services/auditService';\r\nimport { cacheService } from '../services/cacheService';\r\nimport { sessionService } from '../services/sessionService';\r\nimport { tokenService } from '../services/tokenService';\r\nimport { queryOptimizer } from '../utils/queryOptimization';\r\nimport { getRateLimitStats, cleanupRateLimitData } from '../middleware/rateLimiting';\r\nimport { adminRateLimit } from '../middleware/rateLimiting';\r\nimport { strictSanitization } from '../middleware/sanitization';\r\n\r\nconst router = Router();\r\n\r\n// Apply admin-specific middleware\r\nrouter.use(adminRateLimit);\r\nrouter.use(authMiddleware);\r\nrouter.use(adminMiddleware);\r\nrouter.use(strictSanitization());\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/audit/logs:\r\n *   get:\r\n *     summary: Get audit logs with filtering\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: userId\r\n *         schema:\r\n *           type: string\r\n *         description: Filter by user ID\r\n *       - in: query\r\n *         name: eventType\r\n *         schema:\r\n *           type: string\r\n *         description: Filter by event type\r\n *       - in: query\r\n *         name: riskLevel\r\n *         schema:\r\n *           type: string\r\n *           enum: [low, medium, high, critical]\r\n *         description: Filter by risk level\r\n *       - in: query\r\n *         name: startDate\r\n *         schema:\r\n *           type: string\r\n *           format: date-time\r\n *         description: Start date for filtering\r\n *       - in: query\r\n *         name: endDate\r\n *         schema:\r\n *           type: string\r\n *           format: date-time\r\n *         description: End date for filtering\r\n *       - in: query\r\n *         name: page\r\n *         schema:\r\n *           type: integer\r\n *           default: 1\r\n *         description: Page number\r\n *       - in: query\r\n *         name: limit\r\n *         schema:\r\n *           type: integer\r\n *           default: 20\r\n *         description: Items per page\r\n *     responses:\r\n *       200:\r\n *         description: Audit logs retrieved successfully\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n *       403:\r\n *         $ref: '#/components/responses/ForbiddenError'\r\n */\r\nrouter.get('/audit/logs', async (req, res) => {\r\n  try {\r\n    const filters = {\r\n      userId: req.query.userId as string,\r\n      eventType: req.query.eventType as AuditEventType,\r\n      riskLevel: req.query.riskLevel as RiskLevel,\r\n      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,\r\n      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,\r\n      page: parseInt(req.query.page as string) || 1,\r\n      limit: parseInt(req.query.limit as string) || 20\r\n    };\r\n\r\n    const result = await auditService.getAuditLogs(filters);\r\n\r\n    // Log admin access to audit logs\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'audit_logs',\r\n      metadata: { filters }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: result\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve audit logs'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/audit/stats:\r\n *   get:\r\n *     summary: Get audit statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: timeframe\r\n *         schema:\r\n *           type: string\r\n *           enum: [hour, day, week, month]\r\n *           default: day\r\n *         description: Timeframe for statistics\r\n *     responses:\r\n *       200:\r\n *         description: Audit statistics retrieved successfully\r\n */\r\nrouter.get('/audit/stats', async (req, res) => {\r\n  try {\r\n    const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week' | 'month') || 'day';\r\n    const stats = await auditService.getAuditStats(timeframe);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve audit statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/performance/cache:\r\n *   get:\r\n *     summary: Get cache statistics\r\n *     tags: [Admin, Performance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Cache statistics retrieved successfully\r\n */\r\nrouter.get('/performance/cache', async (req, res) => {\r\n  try {\r\n    const stats = await cacheService.getStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: cacheService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve cache statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/performance/queries:\r\n *   get:\r\n *     summary: Get query performance statistics\r\n *     tags: [Admin, Performance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Query statistics retrieved successfully\r\n */\r\nrouter.get('/performance/queries', async (req, res) => {\r\n  try {\r\n    const stats = queryOptimizer.getQueryStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve query statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/rate-limits:\r\n *   get:\r\n *     summary: Get rate limiting statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: timeframe\r\n *         schema:\r\n *           type: string\r\n *           enum: [hour, day, week]\r\n *           default: hour\r\n *         description: Timeframe for rate limit statistics\r\n *     responses:\r\n *       200:\r\n *         description: Rate limit statistics retrieved successfully\r\n */\r\nrouter.get('/security/rate-limits', async (req, res) => {\r\n  try {\r\n    const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week') || 'hour';\r\n    const stats = await getRateLimitStats(timeframe);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve rate limit statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/sessions:\r\n *   get:\r\n *     summary: Get session statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Session statistics retrieved successfully\r\n */\r\nrouter.get('/security/sessions', async (req, res) => {\r\n  try {\r\n    const stats = await sessionService.getSessionStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: sessionService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve session statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/tokens:\r\n *   get:\r\n *     summary: Get token statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Token statistics retrieved successfully\r\n */\r\nrouter.get('/security/tokens', async (req, res) => {\r\n  try {\r\n    const stats = await tokenService.getTokenStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: tokenService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve token statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/maintenance/cleanup:\r\n *   post:\r\n *     summary: Perform system cleanup\r\n *     tags: [Admin, Maintenance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     requestBody:\r\n *       required: true\r\n *       content:\r\n *         application/json:\r\n *           schema:\r\n *             type: object\r\n *             properties:\r\n *               cleanupType:\r\n *                 type: string\r\n *                 enum: [rate-limits, expired-tokens, expired-sessions, query-metrics]\r\n *                 description: Type of cleanup to perform\r\n *             required:\r\n *               - cleanupType\r\n *     responses:\r\n *       200:\r\n *         description: Cleanup completed successfully\r\n */\r\nrouter.post('/maintenance/cleanup', async (req, res) => {\r\n  try {\r\n    const { cleanupType } = req.body;\r\n    let result;\r\n\r\n    switch (cleanupType) {\r\n      case 'rate-limits':\r\n        await cleanupRateLimitData();\r\n        result = 'Rate limit data cleaned up';\r\n        break;\r\n      case 'expired-tokens':\r\n        const tokenCount = await tokenService.cleanupExpiredTokens();\r\n        result = `${tokenCount} expired tokens cleaned up`;\r\n        break;\r\n      case 'expired-sessions':\r\n        const sessionCount = await sessionService.cleanupExpiredSessions();\r\n        result = `${sessionCount} expired sessions cleaned up`;\r\n        break;\r\n      case 'query-metrics':\r\n        queryOptimizer.clearMetrics();\r\n        result = 'Query metrics cleared';\r\n        break;\r\n      default:\r\n        return res.status(400).json({\r\n          success: false,\r\n          error: 'Invalid cleanup type'\r\n        });\r\n    }\r\n\r\n    // Log maintenance action\r\n    await auditService.logEvent(AuditEventType.MAINTENANCE_MODE, req, {\r\n      metadata: { cleanupType, result }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      message: result\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to perform cleanup'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/system/health:\r\n *   get:\r\n *     summary: Get detailed system health status\r\n *     tags: [Admin, System]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: System health status retrieved successfully\r\n */\r\nrouter.get('/system/health', async (req, res) => {\r\n  try {\r\n    const health = {\r\n      timestamp: new Date().toISOString(),\r\n      services: {\r\n        cache: {\r\n          connected: cacheService.isConnected(),\r\n          stats: await cacheService.getStats()\r\n        },\r\n        sessions: {\r\n          connected: sessionService.isConnected(),\r\n          stats: await sessionService.getSessionStats()\r\n        },\r\n        tokens: {\r\n          connected: tokenService.isConnected(),\r\n          stats: await tokenService.getTokenStats()\r\n        },\r\n        rateLimiting: {\r\n          stats: await getRateLimitStats('hour')\r\n        },\r\n        queries: {\r\n          stats: queryOptimizer.getQueryStats()\r\n        }\r\n      }\r\n    };\r\n\r\n    res.json({\r\n      success: true,\r\n      data: health\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve system health'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeW;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AAfX,MAAAC,SAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,iBAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,cAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,cAAA;AAAA;AAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAK,gBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAM,cAAA;AAAA;AAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAO,mBAAA;AAAA;AAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAQ,cAAA;AAAA;AAAA,CAAAZ,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAS,cAAA;AAAA;AAAA,CAAAb,cAAA,GAAAE,CAAA,QAAAE,OAAA;AACA,MAAAU,cAAA;AAAA;AAAA,CAAAd,cAAA,GAAAE,CAAA,QAAAE,OAAA;AAEA,MAAMW,MAAM;AAAA;AAAA,CAAAf,cAAA,GAAAE,CAAA,QAAG,IAAAC,SAAA,CAAAa,MAAM,GAAE;AAEvB;AAAA;AAAAhB,cAAA,GAAAE,CAAA;AACAa,MAAM,CAACE,GAAG,CAACJ,cAAA,CAAAK,cAAc,CAAC;AAAC;AAAAlB,cAAA,GAAAE,CAAA;AAC3Ba,MAAM,CAACE,GAAG,CAACZ,MAAA,CAAAc,YAAc,CAAC;AAAC;AAAAnB,cAAA,GAAAE,CAAA;AAC3Ba,MAAM,CAACE,GAAG,CAACX,iBAAA,CAAAc,eAAe,CAAC;AAAC;AAAApB,cAAA,GAAAE,CAAA;AAC5Ba,MAAM,CAACE,GAAG,CAAC,IAAAH,cAAA,CAAAO,kBAAkB,GAAE,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAArB,cAAA,GAAAE,CAAA;AAyDAa,MAAM,CAACO,GAAG,CAAC,aAAa,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAC3C,IAAI;IACF,MAAMwB,OAAO;IAAA;IAAA,CAAA1B,cAAA,GAAAE,CAAA,QAAG;MACdyB,MAAM,EAAEJ,GAAG,CAACK,KAAK,CAACD,MAAgB;MAClCE,SAAS,EAAEN,GAAG,CAACK,KAAK,CAACC,SAA2B;MAChDC,SAAS,EAAEP,GAAG,CAACK,KAAK,CAACE,SAAsB;MAC3CC,SAAS,EAAER,GAAG,CAACK,KAAK,CAACG,SAAS;MAAA;MAAA,CAAA/B,cAAA,GAAAgC,CAAA,UAAG,IAAIC,IAAI,CAACV,GAAG,CAACK,KAAK,CAACG,SAAmB,CAAC;MAAA;MAAA,CAAA/B,cAAA,GAAAgC,CAAA,UAAGE,SAAS;MACpFC,OAAO,EAAEZ,GAAG,CAACK,KAAK,CAACO,OAAO;MAAA;MAAA,CAAAnC,cAAA,GAAAgC,CAAA,UAAG,IAAIC,IAAI,CAACV,GAAG,CAACK,KAAK,CAACO,OAAiB,CAAC;MAAA;MAAA,CAAAnC,cAAA,GAAAgC,CAAA,UAAGE,SAAS;MAC9EE,IAAI;MAAE;MAAA,CAAApC,cAAA,GAAAgC,CAAA,UAAAK,QAAQ,CAACd,GAAG,CAACK,KAAK,CAACQ,IAAc,CAAC;MAAA;MAAA,CAAApC,cAAA,GAAAgC,CAAA,UAAI,CAAC;MAC7CM,KAAK;MAAE;MAAA,CAAAtC,cAAA,GAAAgC,CAAA,UAAAK,QAAQ,CAACd,GAAG,CAACK,KAAK,CAACU,KAAe,CAAC;MAAA;MAAA,CAAAtC,cAAA,GAAAgC,CAAA,UAAI,EAAE;KACjD;IAED,MAAMO,MAAM;IAAA;IAAA,CAAAvC,cAAA,GAAAE,CAAA,QAAG,MAAMK,cAAA,CAAAiC,YAAY,CAACC,YAAY,CAACf,OAAO,CAAC;IAEvD;IAAA;IAAA1B,cAAA,GAAAE,CAAA;IACA,MAAMK,cAAA,CAAAiC,YAAY,CAACE,QAAQ,CAACnC,cAAA,CAAAoC,cAAc,CAACC,WAAW,EAAErB,GAAG,EAAE;MAC3DsB,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE;QAAEpB;MAAO;KACpB,CAAC;IAAC;IAAA1B,cAAA,GAAAE,CAAA;IAEHsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEV;KACP,CAAC;EACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAoBAa,MAAM,CAACO,GAAG,CAAC,cAAc,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAC5C,IAAI;IACF,MAAMkD,SAAS;IAAA;IAAA,CAAApD,cAAA,GAAAE,CAAA;IAAI;IAAA,CAAAF,cAAA,GAAAgC,CAAA,UAAAT,GAAG,CAACK,KAAK,CAACwB,SAA+C;IAAA;IAAA,CAAApD,cAAA,GAAAgC,CAAA,UAAI,KAAK;IACrF,MAAMqB,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAG,MAAMK,cAAA,CAAAiC,YAAY,CAACc,aAAa,CAACF,SAAS,CAAC;IAAC;IAAApD,cAAA,GAAAE,CAAA;IAE1DsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEI;KACP,CAAC;EACJ,CAAC,CAAC,OAAOH,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAYAa,MAAM,CAACO,GAAG,CAAC,oBAAoB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAClD,IAAI;IACF,MAAMmD,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAG,MAAMM,cAAA,CAAA+C,YAAY,CAACC,QAAQ,EAAE;IAAC;IAAAxD,cAAA,GAAAE,CAAA;IAE5CsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ,GAAGI,KAAK;QACRI,WAAW,EAAEjD,cAAA,CAAA+C,YAAY,CAACE,WAAW;;KAExC,CAAC;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAYAa,MAAM,CAACO,GAAG,CAAC,sBAAsB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EACpD,IAAI;IACF,MAAMmD,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAGS,mBAAA,CAAA+C,cAAc,CAACC,aAAa,EAAE;IAAC;IAAA3D,cAAA,GAAAE,CAAA;IAE7CsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEI;KACP,CAAC;EACJ,CAAC,CAAC,OAAOH,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAoBAa,MAAM,CAACO,GAAG,CAAC,uBAAuB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EACrD,IAAI;IACF,MAAMkD,SAAS;IAAA;IAAA,CAAApD,cAAA,GAAAE,CAAA;IAAI;IAAA,CAAAF,cAAA,GAAAgC,CAAA,UAAAT,GAAG,CAACK,KAAK,CAACwB,SAAqC;IAAA;IAAA,CAAApD,cAAA,GAAAgC,CAAA,UAAI,MAAM;IAC5E,MAAMqB,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAU,cAAA,CAAAgD,iBAAiB,EAACR,SAAS,CAAC;IAAC;IAAApD,cAAA,GAAAE,CAAA;IAEjDsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEI;KACP,CAAC;EACJ,CAAC,CAAC,OAAOH,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAYAa,MAAM,CAACO,GAAG,CAAC,oBAAoB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAClD,IAAI;IACF,MAAMmD,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAG,MAAMO,gBAAA,CAAAoD,cAAc,CAACC,eAAe,EAAE;IAAC;IAAA9D,cAAA,GAAAE,CAAA;IAErDsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ,GAAGI,KAAK;QACRI,WAAW,EAAEhD,gBAAA,CAAAoD,cAAc,CAACJ,WAAW;;KAE1C,CAAC;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAYAa,MAAM,CAACO,GAAG,CAAC,kBAAkB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAChD,IAAI;IACF,MAAMmD,KAAK;IAAA;IAAA,CAAArD,cAAA,GAAAE,CAAA,QAAG,MAAMQ,cAAA,CAAAqD,YAAY,CAACC,aAAa,EAAE;IAAC;IAAAhE,cAAA,GAAAE,CAAA;IAEjDsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ,GAAGI,KAAK;QACRI,WAAW,EAAE/C,cAAA,CAAAqD,YAAY,CAACN,WAAW;;KAExC,CAAC;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAyBAa,MAAM,CAACkD,IAAI,CAAC,sBAAsB,EAAE,OAAO1C,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EACrD,IAAI;IACF,MAAM;MAAEgE;IAAW,CAAE;IAAA;IAAA,CAAAlE,cAAA,GAAAE,CAAA,QAAGqB,GAAG,CAAC4C,IAAI;IAChC,IAAI5B,MAAM;IAAC;IAAAvC,cAAA,GAAAE,CAAA;IAEX,QAAQgE,WAAW;MACjB,KAAK,aAAa;QAAA;QAAAlE,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAE,CAAA;QAChB,MAAM,IAAAU,cAAA,CAAAwD,oBAAoB,GAAE;QAAC;QAAApE,cAAA,GAAAE,CAAA;QAC7BqC,MAAM,GAAG,4BAA4B;QAAC;QAAAvC,cAAA,GAAAE,CAAA;QACtC;MACF,KAAK,gBAAgB;QAAA;QAAAF,cAAA,GAAAgC,CAAA;QACnB,MAAMqC,UAAU;QAAA;QAAA,CAAArE,cAAA,GAAAE,CAAA,QAAG,MAAMQ,cAAA,CAAAqD,YAAY,CAACO,oBAAoB,EAAE;QAAC;QAAAtE,cAAA,GAAAE,CAAA;QAC7DqC,MAAM,GAAG,GAAG8B,UAAU,4BAA4B;QAAC;QAAArE,cAAA,GAAAE,CAAA;QACnD;MACF,KAAK,kBAAkB;QAAA;QAAAF,cAAA,GAAAgC,CAAA;QACrB,MAAMuC,YAAY;QAAA;QAAA,CAAAvE,cAAA,GAAAE,CAAA,QAAG,MAAMO,gBAAA,CAAAoD,cAAc,CAACW,sBAAsB,EAAE;QAAC;QAAAxE,cAAA,GAAAE,CAAA;QACnEqC,MAAM,GAAG,GAAGgC,YAAY,8BAA8B;QAAC;QAAAvE,cAAA,GAAAE,CAAA;QACvD;MACF,KAAK,eAAe;QAAA;QAAAF,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAE,CAAA;QAClBS,mBAAA,CAAA+C,cAAc,CAACe,YAAY,EAAE;QAAC;QAAAzE,cAAA,GAAAE,CAAA;QAC9BqC,MAAM,GAAG,uBAAuB;QAAC;QAAAvC,cAAA,GAAAE,CAAA;QACjC;MACF;QAAA;QAAAF,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAE,CAAA;QACE,OAAOsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;UAC1BC,OAAO,EAAE,KAAK;UACdE,KAAK,EAAE;SACR,CAAC;IACN;IAEA;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACA,MAAMK,cAAA,CAAAiC,YAAY,CAACE,QAAQ,CAACnC,cAAA,CAAAoC,cAAc,CAAC+B,gBAAgB,EAAEnD,GAAG,EAAE;MAChEuB,QAAQ,EAAE;QAAEoB,WAAW;QAAE3B;MAAM;KAChC,CAAC;IAAC;IAAAvC,cAAA,GAAAE,CAAA;IAEHsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACb2B,OAAO,EAAEpC;KACV,CAAC;EACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;AAAA;AAAAlD,cAAA,GAAAE,CAAA;AAYAa,MAAM,CAACO,GAAG,CAAC,gBAAgB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAxB,cAAA,GAAAyB,CAAA;EAAAzB,cAAA,GAAAE,CAAA;EAC9C,IAAI;IACF,MAAM0E,MAAM;IAAA;IAAA,CAAA5E,cAAA,GAAAE,CAAA,QAAG;MACb2E,SAAS,EAAE,IAAI5C,IAAI,EAAE,CAAC6C,WAAW,EAAE;MACnCC,QAAQ,EAAE;QACRC,KAAK,EAAE;UACLC,SAAS,EAAEzE,cAAA,CAAA+C,YAAY,CAACE,WAAW,EAAE;UACrCJ,KAAK,EAAE,MAAM7C,cAAA,CAAA+C,YAAY,CAACC,QAAQ;SACnC;QACD0B,QAAQ,EAAE;UACRD,SAAS,EAAExE,gBAAA,CAAAoD,cAAc,CAACJ,WAAW,EAAE;UACvCJ,KAAK,EAAE,MAAM5C,gBAAA,CAAAoD,cAAc,CAACC,eAAe;SAC5C;QACDqB,MAAM,EAAE;UACNF,SAAS,EAAEvE,cAAA,CAAAqD,YAAY,CAACN,WAAW,EAAE;UACrCJ,KAAK,EAAE,MAAM3C,cAAA,CAAAqD,YAAY,CAACC,aAAa;SACxC;QACDoB,YAAY,EAAE;UACZ/B,KAAK,EAAE,MAAM,IAAAzC,cAAA,CAAAgD,iBAAiB,EAAC,MAAM;SACtC;QACDyB,OAAO,EAAE;UACPhC,KAAK,EAAE1C,mBAAA,CAAA+C,cAAc,CAACC,aAAa;;;KAGxC;IAAC;IAAA3D,cAAA,GAAAE,CAAA;IAEFsB,GAAG,CAACuB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE2B;KACP,CAAC;EACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;IAAA;IAAAlD,cAAA,GAAAE,CAAA;IACdsB,GAAG,CAAC2B,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAAC;AAAAlD,cAAA,GAAAE,CAAA;AAEHoF,OAAA,CAAAC,OAAA,GAAexE,MAAM", "ignoreList": []}