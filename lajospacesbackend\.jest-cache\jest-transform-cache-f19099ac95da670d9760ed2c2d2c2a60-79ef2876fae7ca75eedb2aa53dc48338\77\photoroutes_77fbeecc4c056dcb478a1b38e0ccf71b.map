{"version": 3, "names": ["cov_h3x8wp4e7", "actualCoverage", "express_1", "s", "require", "multer_1", "__importDefault", "auth_1", "photo_controller_1", "router", "Router", "upload", "default", "storage", "memoryStorage", "limits", "fileSize", "files", "fileFilter", "_req", "file", "cb", "f", "allowedTypes", "includes", "mimetype", "b", "Error", "get", "res", "json", "message", "timestamp", "Date", "toISOString", "endpoints", "uploadPhoto", "getPhotos", "deletePhoto", "setPrimary", "reorderPhotos", "guidelines", "getUploadGuidelines", "use", "authenticate", "post", "single", "delete", "patch", "setPrimaryPhoto", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\photo.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport multer from 'multer';\r\nimport { authenticate } from '../middleware/auth';\r\nimport {\r\n  uploadPhoto,\r\n  deletePhoto,\r\n  setPrimaryPhoto,\r\n  getPhotos,\r\n  reorderPhotos,\r\n  getUploadGuidelines\r\n} from '../controllers/photo.controller';\r\n\r\nconst router = Router();\r\n\r\n// Configure multer for memory storage\r\nconst upload = multer({\r\n  storage: multer.memoryStorage(),\r\n  limits: {\r\n    fileSize: 10 * 1024 * 1024, // 10MB limit\r\n    files: 1 // Single file upload\r\n  },\r\n  fileFilter: (_req, file, cb) => {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\r\n    if (allowedTypes.includes(file.mimetype)) {\r\n      cb(null, true);\r\n    } else {\r\n      cb(new Error('Only JPEG, PNG, and WebP images are allowed'));\r\n    }\r\n  }\r\n});\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ \r\n    message: 'Photo routes working', \r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      uploadPhoto: 'POST /upload',\r\n      getPhotos: 'GET /',\r\n      deletePhoto: 'DELETE /:photoId',\r\n      setPrimary: 'PATCH /:photoId/primary',\r\n      reorderPhotos: 'PATCH /reorder',\r\n      guidelines: 'GET /guidelines'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes\r\nrouter.get('/guidelines', getUploadGuidelines);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Get user's photos\r\nrouter.get('/', getPhotos);\r\n\r\n// Upload photo\r\nrouter.post('/upload',\r\n  upload.single('photo'),\r\n  uploadPhoto\r\n);\r\n\r\n// Delete photo\r\nrouter.delete('/:photoId', deletePhoto);\r\n\r\n// Set primary photo\r\nrouter.patch('/:photoId/primary', setPrimaryPhoto);\r\n\r\n// Reorder photos\r\nrouter.patch('/reorder', reorderPhotos);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBI;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBJ,MAAAE,SAAA;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,MAAAG,MAAA;AAAA;AAAA,CAAAP,aAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAI,kBAAA;AAAA;AAAA,CAAAR,aAAA,GAAAG,CAAA,OAAAC,OAAA;AASA,MAAMK,MAAM;AAAA;AAAA,CAAAT,aAAA,GAAAG,CAAA,OAAG,IAAAD,SAAA,CAAAQ,MAAM,GAAE;AAEvB;AACA,MAAMC,MAAM;AAAA;AAAA,CAAAX,aAAA,GAAAG,CAAA,OAAG,IAAAE,QAAA,CAAAO,OAAM,EAAC;EACpBC,OAAO,EAAER,QAAA,CAAAO,OAAM,CAACE,aAAa,EAAE;EAC/BC,MAAM,EAAE;IACNC,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;IAAE;IAC5BC,KAAK,EAAE,CAAC,CAAC;GACV;EACDC,UAAU,EAAEA,CAACC,IAAI,EAAEC,IAAI,EAAEC,EAAE,KAAI;IAAA;IAAArB,aAAA,GAAAsB,CAAA;IAC7B;IACA,MAAMC,YAAY;IAAA;IAAA,CAAAvB,aAAA,GAAAG,CAAA,OAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAAC;IAAAH,aAAA,GAAAG,CAAA;IAC5E,IAAIoB,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAAC,EAAE;MAAA;MAAAzB,aAAA,GAAA0B,CAAA;MAAA1B,aAAA,GAAAG,CAAA;MACxCkB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IAChB,CAAC,MAAM;MAAA;MAAArB,aAAA,GAAA0B,CAAA;MAAA1B,aAAA,GAAAG,CAAA;MACLkB,EAAE,CAAC,IAAIM,KAAK,CAAC,6CAA6C,CAAC,CAAC;IAC9D;EACF;CACD,CAAC;AAEF;AAAA;AAAA3B,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACmB,GAAG,CAAC,SAAS,EAAE,CAACT,IAAI,EAAEU,GAAG,KAAI;EAAA;EAAA7B,aAAA,GAAAsB,CAAA;EAAAtB,aAAA,GAAAG,CAAA;EAClC0B,GAAG,CAACC,IAAI,CAAC;IACPC,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,SAAS,EAAE;MACTC,WAAW,EAAE,cAAc;MAC3BC,SAAS,EAAE,OAAO;MAClBC,WAAW,EAAE,kBAAkB;MAC/BC,UAAU,EAAE,yBAAyB;MACrCC,aAAa,EAAE,gBAAgB;MAC/BC,UAAU,EAAE;;GAEf,CAAC;AACJ,CAAC,CAAC;AAEF;AAAA;AAAAzC,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACmB,GAAG,CAAC,aAAa,EAAEpB,kBAAA,CAAAkC,mBAAmB,CAAC;AAE9C;AAAA;AAAA1C,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACkC,GAAG,CAACpC,MAAA,CAAAqC,YAAY,CAAC;AAExB;AAAA;AAAA5C,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACmB,GAAG,CAAC,GAAG,EAAEpB,kBAAA,CAAA6B,SAAS,CAAC;AAE1B;AAAA;AAAArC,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACoC,IAAI,CAAC,SAAS,EACnBlC,MAAM,CAACmC,MAAM,CAAC,OAAO,CAAC,EACtBtC,kBAAA,CAAA4B,WAAW,CACZ;AAED;AAAA;AAAApC,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACsC,MAAM,CAAC,WAAW,EAAEvC,kBAAA,CAAA8B,WAAW,CAAC;AAEvC;AAAA;AAAAtC,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACuC,KAAK,CAAC,mBAAmB,EAAExC,kBAAA,CAAAyC,eAAe,CAAC;AAElD;AAAA;AAAAjD,aAAA,GAAAG,CAAA;AACAM,MAAM,CAACuC,KAAK,CAAC,UAAU,EAAExC,kBAAA,CAAAgC,aAAa,CAAC;AAAC;AAAAxC,aAAA,GAAAG,CAAA;AAExC+C,OAAA,CAAAtC,OAAA,GAAeH,MAAM", "ignoreList": []}