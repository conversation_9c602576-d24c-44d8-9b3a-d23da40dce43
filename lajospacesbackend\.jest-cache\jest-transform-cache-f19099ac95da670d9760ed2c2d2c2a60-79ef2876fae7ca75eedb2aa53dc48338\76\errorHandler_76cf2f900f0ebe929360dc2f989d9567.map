{"version": 3, "names": ["cov_1vly55lmil", "actualCoverage", "s", "exports", "<PERSON><PERSON><PERSON><PERSON>", "catchAsync", "createError", "logger_1", "require", "environment_1", "AppError", "Error", "constructor", "message", "statusCode", "isOperational", "b", "code", "f", "captureStackTrace", "handleValidationError", "error", "errors", "Object", "values", "map", "err", "join", "handleDuplicateKeyError", "field", "keys", "keyValue", "value", "handleCastError", "path", "handleJWTError", "handleJWTExpiredError", "send<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "errorResponse", "success", "timestamp", "Date", "toISOString", "method", "stack", "details", "status", "json", "sendErrorProd", "logger", "_next", "name", "request", "url", "ip", "userAgent", "get", "user", "id", "email", "undefined", "config", "NODE_ENV", "fn", "next", "Promise", "resolve", "catch", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\errorHandler.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\n\r\n// Custom error class\r\nexport class AppError extends Error {\r\n  public statusCode: number;\r\n  public isOperational: boolean;\r\n  public code: string | undefined;\r\n\r\n  constructor(message: string, statusCode: number, isOperational = true, code?: string) {\r\n    super(message);\r\n    this.statusCode = statusCode;\r\n    this.isOperational = isOperational;\r\n    this.code = code;\r\n\r\n    Error.captureStackTrace(this, this.constructor);\r\n  }\r\n}\r\n\r\n// Error response interface\r\ninterface ErrorResponse {\r\n  success: false;\r\n  error: {\r\n    message: string;\r\n    code?: string | undefined;\r\n    statusCode: number;\r\n    timestamp: string;\r\n    path: string;\r\n    method: string;\r\n    stack?: string | undefined;\r\n    details?: any;\r\n  };\r\n}\r\n\r\n/**\r\n * Handle MongoDB validation errors\r\n */\r\nfunction handleValidationError(error: any): AppError {\r\n  const errors = Object.values(error.errors).map((err: any) => err.message);\r\n  const message = `Validation Error: ${errors.join('. ')}`;\r\n  return new AppError(message, 400, true, 'VALIDATION_ERROR');\r\n}\r\n\r\n/**\r\n * Handle MongoDB duplicate key errors\r\n */\r\nfunction handleDuplicateKeyError(error: any): AppError {\r\n  const field = Object.keys(error.keyValue)[0];\r\n  const value = error.keyValue[field];\r\n  const message = `Duplicate value for field '${field}': ${value}. Please use another value.`;\r\n  return new AppError(message, 409, true, 'DUPLICATE_KEY_ERROR');\r\n}\r\n\r\n/**\r\n * Handle MongoDB cast errors\r\n */\r\nfunction handleCastError(error: any): AppError {\r\n  const message = `Invalid ${error.path}: ${error.value}`;\r\n  return new AppError(message, 400, true, 'CAST_ERROR');\r\n}\r\n\r\n/**\r\n * Handle JWT errors\r\n */\r\nfunction handleJWTError(): AppError {\r\n  return new AppError('Invalid token. Please log in again.', 401, true, 'INVALID_TOKEN');\r\n}\r\n\r\n/**\r\n * Handle JWT expired errors\r\n */\r\nfunction handleJWTExpiredError(): AppError {\r\n  return new AppError('Your token has expired. Please log in again.', 401, true, 'TOKEN_EXPIRED');\r\n}\r\n\r\n/**\r\n * Send error response in development\r\n */\r\nfunction sendErrorDev(err: AppError, req: Request, res: Response): void {\r\n  const errorResponse: ErrorResponse = {\r\n    success: false,\r\n    error: {\r\n      message: err.message,\r\n      code: err.code,\r\n      statusCode: err.statusCode,\r\n      timestamp: new Date().toISOString(),\r\n      path: req.path,\r\n      method: req.method,\r\n      stack: err.stack,\r\n      details: err\r\n    }\r\n  };\r\n\r\n  res.status(err.statusCode).json(errorResponse);\r\n}\r\n\r\n/**\r\n * Send error response in production\r\n */\r\nfunction sendErrorProd(err: AppError, req: Request, res: Response): void {\r\n  // Operational, trusted error: send message to client\r\n  if (err.isOperational) {\r\n    const errorResponse: ErrorResponse = {\r\n      success: false,\r\n      error: {\r\n        message: err.message,\r\n        code: err.code,\r\n        statusCode: err.statusCode,\r\n        timestamp: new Date().toISOString(),\r\n        path: req.path,\r\n        method: req.method\r\n      }\r\n    };\r\n\r\n    res.status(err.statusCode).json(errorResponse);\r\n  } else {\r\n    // Programming or other unknown error: don't leak error details\r\n    logger.error('Unknown error occurred:', err);\r\n\r\n    const errorResponse: ErrorResponse = {\r\n      success: false,\r\n      error: {\r\n        message: 'Something went wrong!',\r\n        statusCode: 500,\r\n        timestamp: new Date().toISOString(),\r\n        path: req.path,\r\n        method: req.method\r\n      }\r\n    };\r\n\r\n    res.status(500).json(errorResponse);\r\n  }\r\n}\r\n\r\n/**\r\n * Global error handling middleware\r\n */\r\nexport function errorHandler(\r\n  err: any,\r\n  req: Request,\r\n  res: Response,\r\n  _next: NextFunction\r\n): void {\r\n  let error = { ...err };\r\n  error.message = err.message;\r\n\r\n  // Log error\r\n  logger.error(`Error ${err.statusCode || 500}: ${err.message}`, {\r\n    error: {\r\n      name: err.name,\r\n      message: err.message,\r\n      stack: err.stack\r\n    },\r\n    request: {\r\n      method: req.method,\r\n      url: req.url,\r\n      ip: req.ip,\r\n      userAgent: req.get('User-Agent')\r\n    },\r\n    user: (req as any).user ? { id: (req as any).user.id, email: (req as any).user.email } : null\r\n  });\r\n\r\n  // MongoDB validation error\r\n  if (err.name === 'ValidationError') {\r\n    error = handleValidationError(err);\r\n  }\r\n\r\n  // MongoDB duplicate key error\r\n  if (err.code === 11000) {\r\n    error = handleDuplicateKeyError(err);\r\n  }\r\n\r\n  // MongoDB cast error\r\n  if (err.name === 'CastError') {\r\n    error = handleCastError(err);\r\n  }\r\n\r\n  // JWT invalid signature\r\n  if (err.name === 'JsonWebTokenError') {\r\n    error = handleJWTError();\r\n  }\r\n\r\n  // JWT expired\r\n  if (err.name === 'TokenExpiredError') {\r\n    error = handleJWTExpiredError();\r\n  }\r\n\r\n  // Multer file upload errors\r\n  if (err.code === 'LIMIT_FILE_SIZE') {\r\n    error = new AppError('File too large', 413, true, 'FILE_TOO_LARGE');\r\n  }\r\n\r\n  if (err.code === 'LIMIT_UNEXPECTED_FILE') {\r\n    error = new AppError('Unexpected file field', 400, true, 'UNEXPECTED_FILE');\r\n  }\r\n\r\n  // Set default values\r\n  error.statusCode = error.statusCode || 500;\r\n  error.isOperational = error.isOperational !== undefined ? error.isOperational : false;\r\n\r\n  // Send error response\r\n  if (config.NODE_ENV === 'development') {\r\n    sendErrorDev(error, req, res);\r\n  } else {\r\n    sendErrorProd(error, req, res);\r\n  }\r\n}\r\n\r\n/**\r\n * Catch async errors wrapper\r\n */\r\nexport function catchAsync(fn: Function) {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    Promise.resolve(fn(req, res, next)).catch(next);\r\n  };\r\n}\r\n\r\n/**\r\n * Create operational error\r\n */\r\nexport function createError(message: string, statusCode: number, code?: string): AppError {\r\n  return new AppError(message, statusCode, true, code);\r\n}\r\n\r\nexport default errorHandler;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgBI;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;;;AA0HJC,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAqEC;AAAAJ,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAIC;AAAAL,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAG,WAAA,GAAAA,WAAA;AA5NA,MAAAC,QAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAM,OAAA;AACA,MAAAC,aAAA;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAAM,OAAA;AAEA;AACA,MAAaE,QAAS,SAAQC,KAAK;EAKjCC,YAAYC,OAAe,EAAEC,UAAkB,EAAEC,aAAa;EAAA;EAAA,CAAAf,cAAA,GAAAgB,CAAA,UAAG,IAAI,GAAEC,IAAa;IAAA;IAAAjB,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAE,CAAA;IAClF,KAAK,CAACW,OAAO,CAAC;IAAC;IAAAb,cAAA,GAAAE,CAAA;IACf,IAAI,CAACY,UAAU,GAAGA,UAAU;IAAC;IAAAd,cAAA,GAAAE,CAAA;IAC7B,IAAI,CAACa,aAAa,GAAGA,aAAa;IAAC;IAAAf,cAAA,GAAAE,CAAA;IACnC,IAAI,CAACe,IAAI,GAAGA,IAAI;IAAC;IAAAjB,cAAA,GAAAE,CAAA;IAEjBS,KAAK,CAACQ,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACP,WAAW,CAAC;EACjD;;AACD;AAAAZ,cAAA,GAAAE,CAAA;AAbDC,OAAA,CAAAO,QAAA,GAAAA,QAAA;AA8BA;;;AAGA,SAASU,qBAAqBA,CAACC,KAAU;EAAA;EAAArB,cAAA,GAAAkB,CAAA;EACvC,MAAMI,MAAM;EAAA;EAAA,CAAAtB,cAAA,GAAAE,CAAA,QAAGqB,MAAM,CAACC,MAAM,CAACH,KAAK,CAACC,MAAM,CAAC,CAACG,GAAG,CAAEC,GAAQ,IAAK;IAAA;IAAA1B,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAE,CAAA;IAAA,OAAAwB,GAAG,CAACb,OAAO;EAAP,CAAO,CAAC;EACzE,MAAMA,OAAO;EAAA;EAAA,CAAAb,cAAA,GAAAE,CAAA,QAAG,qBAAqBoB,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE;EAAC;EAAA3B,cAAA,GAAAE,CAAA;EACzD,OAAO,IAAIQ,QAAQ,CAACG,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,kBAAkB,CAAC;AAC7D;AAEA;;;AAGA,SAASe,uBAAuBA,CAACP,KAAU;EAAA;EAAArB,cAAA,GAAAkB,CAAA;EACzC,MAAMW,KAAK;EAAA;EAAA,CAAA7B,cAAA,GAAAE,CAAA,QAAGqB,MAAM,CAACO,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMC,KAAK;EAAA;EAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAGmB,KAAK,CAACU,QAAQ,CAACF,KAAK,CAAC;EACnC,MAAMhB,OAAO;EAAA;EAAA,CAAAb,cAAA,GAAAE,CAAA,QAAG,8BAA8B2B,KAAK,MAAMG,KAAK,6BAA6B;EAAC;EAAAhC,cAAA,GAAAE,CAAA;EAC5F,OAAO,IAAIQ,QAAQ,CAACG,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC;AAChE;AAEA;;;AAGA,SAASoB,eAAeA,CAACZ,KAAU;EAAA;EAAArB,cAAA,GAAAkB,CAAA;EACjC,MAAML,OAAO;EAAA;EAAA,CAAAb,cAAA,GAAAE,CAAA,QAAG,WAAWmB,KAAK,CAACa,IAAI,KAAKb,KAAK,CAACW,KAAK,EAAE;EAAC;EAAAhC,cAAA,GAAAE,CAAA;EACxD,OAAO,IAAIQ,QAAQ,CAACG,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC;AACvD;AAEA;;;AAGA,SAASsB,cAAcA,CAAA;EAAA;EAAAnC,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAE,CAAA;EACrB,OAAO,IAAIQ,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;AACxF;AAEA;;;AAGA,SAAS0B,qBAAqBA,CAAA;EAAA;EAAApC,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAE,CAAA;EAC5B,OAAO,IAAIQ,QAAQ,CAAC,8CAA8C,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;AACjG;AAEA;;;AAGA,SAAS2B,YAAYA,CAACX,GAAa,EAAEY,GAAY,EAAEC,GAAa;EAAA;EAAAvC,cAAA,GAAAkB,CAAA;EAC9D,MAAMsB,aAAa;EAAA;EAAA,CAAAxC,cAAA,GAAAE,CAAA,QAAkB;IACnCuC,OAAO,EAAE,KAAK;IACdpB,KAAK,EAAE;MACLR,OAAO,EAAEa,GAAG,CAACb,OAAO;MACpBI,IAAI,EAAES,GAAG,CAACT,IAAI;MACdH,UAAU,EAAEY,GAAG,CAACZ,UAAU;MAC1B4B,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;MACnCV,IAAI,EAAEI,GAAG,CAACJ,IAAI;MACdW,MAAM,EAAEP,GAAG,CAACO,MAAM;MAClBC,KAAK,EAAEpB,GAAG,CAACoB,KAAK;MAChBC,OAAO,EAAErB;;GAEZ;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EAEFqC,GAAG,CAACS,MAAM,CAACtB,GAAG,CAACZ,UAAU,CAAC,CAACmC,IAAI,CAACT,aAAa,CAAC;AAChD;AAEA;;;AAGA,SAASU,aAAaA,CAACxB,GAAa,EAAEY,GAAY,EAAEC,GAAa;EAAA;EAAAvC,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAE,CAAA;EAC/D;EACA,IAAIwB,GAAG,CAACX,aAAa,EAAE;IAAA;IAAAf,cAAA,GAAAgB,CAAA;IACrB,MAAMwB,aAAa;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA,QAAkB;MACnCuC,OAAO,EAAE,KAAK;MACdpB,KAAK,EAAE;QACLR,OAAO,EAAEa,GAAG,CAACb,OAAO;QACpBI,IAAI,EAAES,GAAG,CAACT,IAAI;QACdH,UAAU,EAAEY,GAAG,CAACZ,UAAU;QAC1B4B,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;QACnCV,IAAI,EAAEI,GAAG,CAACJ,IAAI;QACdW,MAAM,EAAEP,GAAG,CAACO;;KAEf;IAAC;IAAA7C,cAAA,GAAAE,CAAA;IAEFqC,GAAG,CAACS,MAAM,CAACtB,GAAG,CAACZ,UAAU,CAAC,CAACmC,IAAI,CAACT,aAAa,CAAC;EAChD,CAAC,MAAM;IAAA;IAAAxC,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACL;IACAK,QAAA,CAAA4C,MAAM,CAAC9B,KAAK,CAAC,yBAAyB,EAAEK,GAAG,CAAC;IAE5C,MAAMc,aAAa;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA,QAAkB;MACnCuC,OAAO,EAAE,KAAK;MACdpB,KAAK,EAAE;QACLR,OAAO,EAAE,uBAAuB;QAChCC,UAAU,EAAE,GAAG;QACf4B,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;QACnCV,IAAI,EAAEI,GAAG,CAACJ,IAAI;QACdW,MAAM,EAAEP,GAAG,CAACO;;KAEf;IAAC;IAAA7C,cAAA,GAAAE,CAAA;IAEFqC,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAACT,aAAa,CAAC;EACrC;AACF;AAEA;;;AAGA,SAAgBpC,YAAYA,CAC1BsB,GAAQ,EACRY,GAAY,EACZC,GAAa,EACba,KAAmB;EAAA;EAAApD,cAAA,GAAAkB,CAAA;EAEnB,IAAIG,KAAK;EAAA;EAAA,CAAArB,cAAA,GAAAE,CAAA,QAAG;IAAE,GAAGwB;EAAG,CAAE;EAAC;EAAA1B,cAAA,GAAAE,CAAA;EACvBmB,KAAK,CAACR,OAAO,GAAGa,GAAG,CAACb,OAAO;EAE3B;EAAA;EAAAb,cAAA,GAAAE,CAAA;EACAK,QAAA,CAAA4C,MAAM,CAAC9B,KAAK,CAAC;EAAS;EAAA,CAAArB,cAAA,GAAAgB,CAAA,UAAAU,GAAG,CAACZ,UAAU;EAAA;EAAA,CAAAd,cAAA,GAAAgB,CAAA,UAAI,GAAG,MAAKU,GAAG,CAACb,OAAO,EAAE,EAAE;IAC7DQ,KAAK,EAAE;MACLgC,IAAI,EAAE3B,GAAG,CAAC2B,IAAI;MACdxC,OAAO,EAAEa,GAAG,CAACb,OAAO;MACpBiC,KAAK,EAAEpB,GAAG,CAACoB;KACZ;IACDQ,OAAO,EAAE;MACPT,MAAM,EAAEP,GAAG,CAACO,MAAM;MAClBU,GAAG,EAAEjB,GAAG,CAACiB,GAAG;MACZC,EAAE,EAAElB,GAAG,CAACkB,EAAE;MACVC,SAAS,EAAEnB,GAAG,CAACoB,GAAG,CAAC,YAAY;KAChC;IACDC,IAAI,EAAGrB,GAAW,CAACqB,IAAI;IAAA;IAAA,CAAA3D,cAAA,GAAAgB,CAAA,UAAG;MAAE4C,EAAE,EAAGtB,GAAW,CAACqB,IAAI,CAACC,EAAE;MAAEC,KAAK,EAAGvB,GAAW,CAACqB,IAAI,CAACE;IAAK,CAAE;IAAA;IAAA,CAAA7D,cAAA,GAAAgB,CAAA,UAAG,IAAI;GAC9F,CAAC;EAEF;EAAA;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAAC2B,IAAI,KAAK,iBAAiB,EAAE;IAAA;IAAArD,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IAClCmB,KAAK,GAAGD,qBAAqB,CAACM,GAAG,CAAC;EACpC,CAAC;EAAA;EAAA;IAAA1B,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAACT,IAAI,KAAK,KAAK,EAAE;IAAA;IAAAjB,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACtBmB,KAAK,GAAGO,uBAAuB,CAACF,GAAG,CAAC;EACtC,CAAC;EAAA;EAAA;IAAA1B,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAAC2B,IAAI,KAAK,WAAW,EAAE;IAAA;IAAArD,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IAC5BmB,KAAK,GAAGY,eAAe,CAACP,GAAG,CAAC;EAC9B,CAAC;EAAA;EAAA;IAAA1B,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAAC2B,IAAI,KAAK,mBAAmB,EAAE;IAAA;IAAArD,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACpCmB,KAAK,GAAGc,cAAc,EAAE;EAC1B,CAAC;EAAA;EAAA;IAAAnC,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAAC2B,IAAI,KAAK,mBAAmB,EAAE;IAAA;IAAArD,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACpCmB,KAAK,GAAGe,qBAAqB,EAAE;EACjC,CAAC;EAAA;EAAA;IAAApC,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIwB,GAAG,CAACT,IAAI,KAAK,iBAAiB,EAAE;IAAA;IAAAjB,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IAClCmB,KAAK,GAAG,IAAIX,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC;EACrE,CAAC;EAAA;EAAA;IAAAV,cAAA,GAAAgB,CAAA;EAAA;EAAAhB,cAAA,GAAAE,CAAA;EAED,IAAIwB,GAAG,CAACT,IAAI,KAAK,uBAAuB,EAAE;IAAA;IAAAjB,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACxCmB,KAAK,GAAG,IAAIX,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC;EAC7E,CAAC;EAAA;EAAA;IAAAV,cAAA,GAAAgB,CAAA;EAAA;EAED;EAAAhB,cAAA,GAAAE,CAAA;EACAmB,KAAK,CAACP,UAAU;EAAG;EAAA,CAAAd,cAAA,GAAAgB,CAAA,WAAAK,KAAK,CAACP,UAAU;EAAA;EAAA,CAAAd,cAAA,GAAAgB,CAAA,WAAI,GAAG;EAAC;EAAAhB,cAAA,GAAAE,CAAA;EAC3CmB,KAAK,CAACN,aAAa,GAAGM,KAAK,CAACN,aAAa,KAAK+C,SAAS;EAAA;EAAA,CAAA9D,cAAA,GAAAgB,CAAA,WAAGK,KAAK,CAACN,aAAa;EAAA;EAAA,CAAAf,cAAA,GAAAgB,CAAA,WAAG,KAAK;EAErF;EAAA;EAAAhB,cAAA,GAAAE,CAAA;EACA,IAAIO,aAAA,CAAAsD,MAAM,CAACC,QAAQ,KAAK,aAAa,EAAE;IAAA;IAAAhE,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACrCmC,YAAY,CAAChB,KAAK,EAAEiB,GAAG,EAAEC,GAAG,CAAC;EAC/B,CAAC,MAAM;IAAA;IAAAvC,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAE,CAAA;IACLgD,aAAa,CAAC7B,KAAK,EAAEiB,GAAG,EAAEC,GAAG,CAAC;EAChC;AACF;AAEA;;;AAGA,SAAgBlC,UAAUA,CAAC4D,EAAY;EAAA;EAAAjE,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAE,CAAA;EACrC,OAAO,CAACoC,GAAY,EAAEC,GAAa,EAAE2B,IAAkB,KAAI;IAAA;IAAAlE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAE,CAAA;IACzDiE,OAAO,CAACC,OAAO,CAACH,EAAE,CAAC3B,GAAG,EAAEC,GAAG,EAAE2B,IAAI,CAAC,CAAC,CAACG,KAAK,CAACH,IAAI,CAAC;EACjD,CAAC;AACH;AAEA;;;AAGA,SAAgB5D,WAAWA,CAACO,OAAe,EAAEC,UAAkB,EAAEG,IAAa;EAAA;EAAAjB,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAE,CAAA;EAC5E,OAAO,IAAIQ,QAAQ,CAACG,OAAO,EAAEC,UAAU,EAAE,IAAI,EAAEG,IAAI,CAAC;AACtD;AAAC;AAAAjB,cAAA,GAAAE,CAAA;AAEDC,OAAA,CAAAmE,OAAA,GAAelE,YAAY", "ignoreList": []}