{"version": 3, "names": ["cov_11fi0njlkb", "actualCoverage", "s", "AppError", "Error", "constructor", "message", "statusCode", "b", "details", "isOperational", "f", "captureStackTrace", "Object", "setPrototypeOf", "prototype", "validationError", "unauthorized", "forbidden", "notFound", "conflict", "internal", "badRequest", "tooManyRequests", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\appError.ts"], "sourcesContent": ["export class AppError extends <PERSON>rror {\r\n  public statusCode: number;\r\n  public isOperational: boolean;\r\n  public details?: any;\r\n\r\n  constructor(\r\n    message: string,\r\n    statusCode: number = 500,\r\n    details?: any,\r\n    isOperational: boolean = true\r\n  ) {\r\n    super(message);\r\n    \r\n    this.statusCode = statusCode;\r\n    this.isOperational = isOperational;\r\n    this.details = details;\r\n\r\n    // Maintains proper stack trace for where our error was thrown (only available on V8)\r\n    if (Error.captureStackTrace) {\r\n      Error.captureStackTrace(this, AppError);\r\n    }\r\n\r\n    // Set the prototype explicitly\r\n    Object.setPrototypeOf(this, AppError.prototype);\r\n  }\r\n\r\n  /**\r\n   * Create a validation error\r\n   */\r\n  static validationError(message: string, details?: any) {\r\n    return new AppError(message, 400, details);\r\n  }\r\n\r\n  /**\r\n   * Create an unauthorized error\r\n   */\r\n  static unauthorized(message: string = 'Unauthorized access') {\r\n    return new AppError(message, 401);\r\n  }\r\n\r\n  /**\r\n   * Create a forbidden error\r\n   */\r\n  static forbidden(message: string = 'Access forbidden') {\r\n    return new AppError(message, 403);\r\n  }\r\n\r\n  /**\r\n   * Create a not found error\r\n   */\r\n  static notFound(message: string = 'Resource not found') {\r\n    return new AppError(message, 404);\r\n  }\r\n\r\n  /**\r\n   * Create a conflict error\r\n   */\r\n  static conflict(message: string = 'Resource conflict') {\r\n    return new AppError(message, 409);\r\n  }\r\n\r\n  /**\r\n   * Create an internal server error\r\n   */\r\n  static internal(message: string = 'Internal server error') {\r\n    return new AppError(message, 500);\r\n  }\r\n\r\n  /**\r\n   * Create a bad request error\r\n   */\r\n  static badRequest(message: string = 'Bad request') {\r\n    return new AppError(message, 400);\r\n  }\r\n\r\n  /**\r\n   * Create a too many requests error\r\n   */\r\n  static tooManyRequests(message: string = 'Too many requests') {\r\n    return new AppError(message, 429);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwBG;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAxBH,MAAaC,QAAS,SAAQC,KAAK;EAKjCC,YACEC,OAAe,EACfC,UAAA;EAAA;EAAA,CAAAP,cAAA,GAAAQ,CAAA,UAAqB,GAAG,GACxBC,OAAa,EACbC,aAAA;EAAA;EAAA,CAAAV,cAAA,GAAAQ,CAAA,UAAyB,IAAI;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IAE7B,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAEf,IAAI,CAACK,UAAU,GAAGA,UAAU;IAAC;IAAAP,cAAA,GAAAE,CAAA;IAC7B,IAAI,CAACQ,aAAa,GAAGA,aAAa;IAAC;IAAAV,cAAA,GAAAE,CAAA;IACnC,IAAI,CAACO,OAAO,GAAGA,OAAO;IAEtB;IAAA;IAAAT,cAAA,GAAAE,CAAA;IACA,IAAIE,KAAK,CAACQ,iBAAiB,EAAE;MAAA;MAAAZ,cAAA,GAAAQ,CAAA;MAAAR,cAAA,GAAAE,CAAA;MAC3BE,KAAK,CAACQ,iBAAiB,CAAC,IAAI,EAAET,QAAQ,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAH,cAAA,GAAAQ,CAAA;IAAA;IAED;IAAAR,cAAA,GAAAE,CAAA;IACAW,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEX,QAAQ,CAACY,SAAS,CAAC;EACjD;EAEA;;;EAGA,OAAOC,eAAeA,CAACV,OAAe,EAAEG,OAAa;IAAA;IAAAT,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACnD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,EAAEG,OAAO,CAAC;EAC5C;EAEA;;;EAGA,OAAOQ,YAAYA,CAACX,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,qBAAqB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACzD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOY,SAASA,CAACZ,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,kBAAkB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACnD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOa,QAAQA,CAACb,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,oBAAoB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACpD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOc,QAAQA,CAACd,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,mBAAmB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACnD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOe,QAAQA,CAACf,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,uBAAuB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IACvD,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOgB,UAAUA,CAAChB,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,aAAa;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IAC/C,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;EAEA;;;EAGA,OAAOiB,eAAeA,CAACjB,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAQ,CAAA,UAAkB,mBAAmB;IAAA;IAAAR,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAE,CAAA;IAC1D,OAAO,IAAIC,QAAQ,CAACG,OAAO,EAAE,GAAG,CAAC;EACnC;;AACD;AAAAN,cAAA,GAAAE,CAAA;AAjFDsB,OAAA,CAAArB,QAAA,GAAAA,QAAA", "ignoreList": []}