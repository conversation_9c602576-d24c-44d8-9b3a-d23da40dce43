e510596de83457e716b0f0a2a0fbf0a8
"use strict";

/* istanbul ignore next */
function cov_f7mb1mrh6() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\property.validators.ts";
  var hash = "9c90f6c7a1a9293a811d79c9eb4a255407aa7aef";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\property.validators.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 357
        }
      },
      "4": {
        start: {
          line: 7,
          column: 14
        },
        end: {
          line: 7,
          column: 45
        }
      },
      "5": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 15,
          column: 1
        }
      },
      "6": {
        start: {
          line: 17,
          column: 23
        },
        end: {
          line: 17,
          column: 97
        }
      },
      "7": {
        start: {
          line: 18,
          column: 22
        },
        end: {
          line: 18,
          column: 52
        }
      },
      "8": {
        start: {
          line: 19,
          column: 20
        },
        end: {
          line: 19,
          column: 54
        }
      },
      "9": {
        start: {
          line: 20,
          column: 28
        },
        end: {
          line: 20,
          column: 78
        }
      },
      "10": {
        start: {
          line: 21,
          column: 26
        },
        end: {
          line: 21,
          column: 80
        }
      },
      "11": {
        start: {
          line: 23,
          column: 23
        },
        end: {
          line: 49,
          column: 2
        }
      },
      "12": {
        start: {
          line: 51,
          column: 22
        },
        end: {
          line: 73,
          column: 2
        }
      },
      "13": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 101,
          column: 2
        }
      },
      "14": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 118,
          column: 2
        }
      },
      "15": {
        start: {
          line: 120,
          column: 34
        },
        end: {
          line: 133,
          column: 2
        }
      },
      "16": {
        start: {
          line: 137,
          column: 0
        },
        end: {
          line: 200,
          column: 3
        }
      },
      "17": {
        start: {
          line: 204,
          column: 0
        },
        end: {
          line: 232,
          column: 3
        }
      },
      "18": {
        start: {
          line: 236,
          column: 0
        },
        end: {
          line: 256,
          column: 3
        }
      },
      "19": {
        start: {
          line: 260,
          column: 0
        },
        end: {
          line: 290,
          column: 3
        }
      },
      "20": {
        start: {
          line: 284,
          column: 4
        },
        end: {
          line: 286,
          column: 5
        }
      },
      "21": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 285,
          column: 57
        }
      },
      "22": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 287,
          column: 17
        }
      },
      "23": {
        start: {
          line: 294,
          column: 0
        },
        end: {
          line: 302,
          column: 3
        }
      },
      "24": {
        start: {
          line: 306,
          column: 0
        },
        end: {
          line: 311,
          column: 3
        }
      },
      "25": {
        start: {
          line: 315,
          column: 0
        },
        end: {
          line: 321,
          column: 3
        }
      },
      "26": {
        start: {
          line: 325,
          column: 0
        },
        end: {
          line: 341,
          column: 3
        }
      },
      "27": {
        start: {
          line: 345,
          column: 0
        },
        end: {
          line: 350,
          column: 3
        }
      },
      "28": {
        start: {
          line: 354,
          column: 0
        },
        end: {
          line: 368,
          column: 3
        }
      },
      "29": {
        start: {
          line: 372,
          column: 0
        },
        end: {
          line: 403,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 282,
            column: 10
          },
          end: {
            line: 282,
            column: 11
          }
        },
        loc: {
          start: {
            line: 282,
            column: 30
          },
          end: {
            line: 288,
            column: 1
          }
        },
        line: 282
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 286,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 286,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "4": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 22
          }
        }, {
          start: {
            line: 284,
            column: 26
          },
          end: {
            line: 284,
            column: 40
          }
        }, {
          start: {
            line: 284,
            column: 44
          },
          end: {
            line: 284,
            column: 75
          }
        }],
        line: 284
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\property.validators.ts",
      mappings: ";;;;;;AAAA,8CAAsB;AAEtB,iCAAiC;AACjC,MAAM,eAAe,GAAG;IACtB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IAChF,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;IACzE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;IACtE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS;IACtE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;CAChD,CAAC;AAEF,iBAAiB;AACjB,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAClG,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AACrD,MAAM,WAAW,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AACvD,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC/E,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AAEjF,kBAAkB;AAClB,MAAM,cAAc,GAAG,aAAG,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACxD,cAAc,EAAE,qBAAqB;QACrC,YAAY,EAAE,sCAAsC;KACrD,CAAC;IACF,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrD,cAAc,EAAE,kBAAkB;QAClC,YAAY,EAAE,mCAAmC;KAClD,CAAC;IACF,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChE,UAAU,EAAE,sCAAsC;QAClD,cAAc,EAAE,mBAAmB;KACpC,CAAC;IACF,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;IACzD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrD,YAAY,EAAE,mCAAmC;KAClD,CAAC;IACF,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACzD,YAAY,EAAE,uCAAuC;KACtD,CAAC;IACF,WAAW,EAAE,aAAG,CAAC,MAAM,CAAC;QACtB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QAClD,WAAW,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACzE,cAAc,EAAE,kEAAkE;SACnF,CAAC;KACH,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH,iBAAiB;AACjB,MAAM,aAAa,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/B,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrE,YAAY,EAAE,8BAA8B;QAC5C,YAAY,EAAE,gCAAgC;QAC9C,cAAc,EAAE,0BAA0B;KAC3C,CAAC;IACF,eAAe,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACvD,YAAY,EAAE,qCAAqC;QACnD,cAAc,EAAE,8BAA8B;KAC/C,CAAC;IACF,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACxC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACxC,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1C,aAAa,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7C,mBAAmB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACjD,aAAa,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC3C,gBAAgB,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC9C,gBAAgB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,mBAAmB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;IAChF,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC;QAC/D,YAAY,EAAE,0CAA0C;QACxD,YAAY,EAAE,yCAAyC;KACxD,CAAC;CACH,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,eAAe,GAAG,aAAG,CAAC,MAAM,CAAC;IACjC,kBAAkB;IAClB,IAAI,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACrC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACtC,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACvC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACtC,eAAe,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAE7C,oBAAoB;IACpB,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACpC,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC1C,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACvC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAEtC,mBAAmB;IACnB,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACvC,EAAE,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAChC,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAE5C,qBAAqB;IACrB,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACtC,GAAG,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACjC,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC1C,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAExC,YAAY;IACZ,YAAY,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC1C,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACrC,eAAe,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CAC9C,CAAC,CAAC;AAEH,eAAe;AACf,MAAM,WAAW,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7B,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5C,WAAW,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACzC,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5C,aAAa,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACtC,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC3D,YAAY,EAAE,uCAAuC;QACrD,YAAY,EAAE,sCAAsC;KACrD,CAAC;IACF,gBAAgB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChE,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,oCAAoC;QAClD,cAAc,EAAE,+BAA+B;KAChD,CAAC;CACH,CAAC,CAAC;AAEH,8BAA8B;AAC9B,MAAM,yBAAyB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC3C,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAClE,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9C,GAAG,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;KAC/C,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;IAChC,UAAU,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAC9D,SAAS,EAAE,aAAG,CAAC,MAAM,CAAC;QACpB,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACrC,QAAQ,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACtC,IAAI,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAClC,OAAO,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;KACtC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CACf,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC9D,cAAc,EAAE,4BAA4B;QAC5C,YAAY,EAAE,sCAAsC;QACpD,YAAY,EAAE,oCAAoC;KACnD,CAAC;IACF,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrE,cAAc,EAAE,kCAAkC;QAClD,YAAY,EAAE,4CAA4C;QAC1D,YAAY,EAAE,2CAA2C;KAC1D,CAAC;IACF,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACtE,UAAU,EAAE,qCAAqC;QACjD,cAAc,EAAE,2BAA2B;KAC5C,CAAC;IACF,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpE,UAAU,EAAE,oCAAoC;QAChD,cAAc,EAAE,0BAA0B;KAC3C,CAAC;IACF,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IAEnE,mBAAmB;IACnB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACxD,YAAY,EAAE,6BAA6B;QAC3C,YAAY,EAAE,2BAA2B;QACzC,cAAc,EAAE,gCAAgC;KACjD,CAAC;IACF,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACzD,YAAY,EAAE,+BAA+B;QAC7C,YAAY,EAAE,4BAA4B;QAC1C,cAAc,EAAE,iCAAiC;KAClD,CAAC;IACF,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC1D,YAAY,EAAE,2BAA2B;QACzC,YAAY,EAAE,8BAA8B;QAC5C,cAAc,EAAE,mCAAmC;KACpD,CAAC;IACF,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC7D,YAAY,EAAE,8CAA8C;QAC5D,YAAY,EAAE,+CAA+C;KAC9D,CAAC;IACF,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC9C,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACpD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;IAE9E,mBAAmB;IACnB,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE;IACnC,OAAO,EAAE,aAAa,CAAC,QAAQ,EAAE;IACjC,SAAS,EAAE,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;IACtC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;IAE7B,mBAAmB;IACnB,mBAAmB,EAAE,yBAAyB,CAAC,QAAQ,EAAE;IAEzD,eAAe;IACf,WAAW,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACxC,aAAa,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACvD,UAAU,EAAE,2CAA2C;QACvD,cAAc,EAAE,iCAAiC;KAClD,CAAC;IACF,WAAW,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,aAAG,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACxE,UAAU,EAAE,qDAAqD;KAClE,CAAC;IAEF,oBAAoB;IACpB,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC;QAChF,WAAW,EAAE,+BAA+B;KAC7C,CAAC;CACH,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACtD,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IAC7D,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,QAAQ,EAAE;IAC9D,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ,EAAE;IAC5D,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,QAAQ,EAAE;IAExD,mBAAmB;IACnB,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAChD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;IACrD,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC9C,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACpD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;IAE9E,mBAAmB;IACnB,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE;IACnC,OAAO,EAAE,aAAa,CAAC,QAAQ,EAAE;IACjC,SAAS,EAAE,eAAe,CAAC,QAAQ,EAAE;IACrC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;IAC7B,mBAAmB,EAAE,yBAAyB,CAAC,QAAQ,EAAE;IAEzD,eAAe;IACf,WAAW,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACrC,aAAa,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;IAC/C,WAAW,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAElC,SAAS;IACT,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,iBAAiB,CAAC,CAAC,QAAQ,EAAE;IAE3D,OAAO;IACP,IAAI,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;CACxE,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpC,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/C,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,QAAQ,EAAE;IAC9D,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ,EAAE;IAC5D,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACxC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACxC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAChD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,CAAC,QAAQ,EAAE;IACxD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC7C,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,uBAAuB;IAC3D,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,iBAAiB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAClE,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CACxB,WAAW,EAAE,WAAW,EAAE,sBAAsB,EAAE,iBAAiB,EACnE,UAAU,EAAE,WAAW,EAAE,OAAO,CACjC,CAAC,OAAO,CAAC,WAAW,CAAC;IACtB,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAC5D,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC/C,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAClD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACrD,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY;CACpE,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACtD,YAAY,EAAE,2CAA2C;KAC1D,CAAC;IACF,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,QAAQ,EAAE;IAC9D,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ,EAAE;IAC5D,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChD,YAAY,EAAE,kCAAkC;KACjD,CAAC;IACF,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChD,YAAY,EAAE,kCAAkC;KACjD,CAAC;IACF,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IAChD,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IACjD,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,CAAC,QAAQ,EAAE;QACxD,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;KAC9C,CAAC,CAAC,QAAQ,EAAE;IACb,SAAS,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC5D,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpC,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CAChD,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IAC3B,uBAAuB;IACvB,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QACxE,OAAO,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACnD,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,CAAC,QAAQ,CAAC;IACV,0BAA0B,EAAE,oDAAoD;CACjF,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACxD,YAAY,EAAE,4CAA4C;KAC3D,CAAC;IACF,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpD,YAAY,EAAE,uCAAuC;KACtD,CAAC;IACF,SAAS,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACxC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,mBAAmB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5C,UAAU,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC5E,WAAW,EAAE,gDAAgD;QAC7D,cAAc,EAAE,yBAAyB;KAC1C,CAAC;CACH,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,oBAAoB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC7C,SAAS,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAChC,OAAO,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,aAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChE,UAAU,EAAE,mCAAmC;KAChD,CAAC;IACF,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;CACnE,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC1D,YAAY,EAAE,qCAAqC;QACnD,YAAY,EAAE,qCAAqC;QACnD,cAAc,EAAE,sBAAsB;KACvC,CAAC;IACF,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC7D,YAAY,EAAE,wCAAwC;QACtD,YAAY,EAAE,wCAAwC;QACtD,cAAc,EAAE,uBAAuB;KACxC,CAAC;IACF,MAAM,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;QAC9D,YAAY,EAAE,oCAAoC;QAClD,YAAY,EAAE,oCAAoC;KACnD,CAAC;IACF,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CAChD,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,sBAAsB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/C,UAAU,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAClD,cAAc,EAAE,yBAAyB;QACzC,cAAc,EAAE,yBAAyB;KAC1C,CAAC;CACH,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,qBAAqB,GAAG,aAAG,CAAC,MAAM,CAAC;IAC9C,OAAO,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACjE,cAAc,EAAE,6BAA6B;QAC7C,YAAY,EAAE,wCAAwC;QACtD,YAAY,EAAE,uCAAuC;QACrD,cAAc,EAAE,6BAA6B;KAC9C,CAAC;IACF,iBAAiB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAChF,UAAU,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpD,UAAU,EAAE,oCAAoC;KACjD,CAAC;IACF,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC/D,YAAY,EAAE,qDAAqD;KACpE,CAAC;CACH,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,yBAAyB,GAAG,aAAG,CAAC,MAAM,CAAC;IAClD,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrE,cAAc,EAAE,0BAA0B;QAC1C,YAAY,EAAE,6CAA6C;QAC3D,YAAY,EAAE,4CAA4C;QAC1D,cAAc,EAAE,0BAA0B;KAC3C,CAAC;IACF,UAAU,EAAE,aAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACpD,UAAU,EAAE,oCAAoC;QAChD,cAAc,EAAE,oCAAoC;KACrD,CAAC;IACF,aAAa,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC7D,YAAY,EAAE,yCAAyC;QACvD,YAAY,EAAE,wCAAwC;QACtD,cAAc,EAAE,sCAAsC;KACvD,CAAC;IACF,aAAa,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QACrD,YAAY,EAAE,mCAAmC;KAClD,CAAC;IACF,gBAAgB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAClC,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,CAChE,CAAC,QAAQ,EAAE;IACZ,UAAU,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAC3B,aAAG,CAAC,MAAM,CAAC;QACT,IAAI,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,YAAY,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QACrD,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;QAC3C,KAAK,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;KACvC,CAAC,CACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAC3B,WAAW,EAAE,uCAAuC;KACrD,CAAC;IACF,cAAc,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;QAChE,YAAY,EAAE,sDAAsD;KACrE,CAAC;CACH,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\validators\\property.validators.ts"],
      sourcesContent: ["import Joi from 'joi';\r\n\r\n// Nigerian states for validation\r\nconst NIGERIAN_STATES = [\r\n  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',\r\n  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe',\r\n  'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara',\r\n  'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau',\r\n  'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'\r\n];\r\n\r\n// Property types\r\nconst PROPERTY_TYPES = ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion'];\r\nconst LISTING_TYPES = ['rent', 'roommate', 'sublet'];\r\nconst OWNER_TYPES = ['individual', 'agent', 'company'];\r\nconst PAYMENT_FREQUENCIES = ['monthly', 'quarterly', 'biannually', 'annually'];\r\nconst PROPERTY_STATUSES = ['draft', 'active', 'inactive', 'rented', 'suspended'];\r\n\r\n// Location schema\r\nconst locationSchema = Joi.object({\r\n  address: Joi.string().trim().max(300).required().messages({\r\n    'string.empty': 'Address is required',\r\n    'string.max': 'Address cannot exceed 300 characters'\r\n  }),\r\n  city: Joi.string().trim().max(100).required().messages({\r\n    'string.empty': 'City is required',\r\n    'string.max': 'City cannot exceed 100 characters'\r\n  }),\r\n  state: Joi.string().valid(...NIGERIAN_STATES).required().messages({\r\n    'any.only': 'Please select a valid Nigerian state',\r\n    'any.required': 'State is required'\r\n  }),\r\n  country: Joi.string().valid('Nigeria').default('Nigeria'),\r\n  area: Joi.string().trim().max(100).optional().messages({\r\n    'string.max': 'Area cannot exceed 100 characters'\r\n  }),\r\n  landmark: Joi.string().trim().max(200).optional().messages({\r\n    'string.max': 'Landmark cannot exceed 200 characters'\r\n  }),\r\n  coordinates: Joi.object({\r\n    type: Joi.string().valid('Point').default('Point'),\r\n    coordinates: Joi.array().items(Joi.number()).length(2).required().messages({\r\n      'array.length': 'Coordinates must contain exactly 2 numbers [longitude, latitude]'\r\n    })\r\n  }).optional()\r\n});\r\n\r\n// Pricing schema\r\nconst pricingSchema = Joi.object({\r\n  rentPerMonth: Joi.number().min(1000).max(10000000).required().messages({\r\n    'number.min': 'Rent must be at least \u20A61,000',\r\n    'number.max': 'Rent cannot exceed \u20A610,000,000',\r\n    'any.required': 'Monthly rent is required'\r\n  }),\r\n  securityDeposit: Joi.number().min(0).required().messages({\r\n    'number.min': 'Security deposit cannot be negative',\r\n    'any.required': 'Security deposit is required'\r\n  }),\r\n  agentFee: Joi.number().min(0).default(0),\r\n  legalFee: Joi.number().min(0).default(0),\r\n  cautionFee: Joi.number().min(0).default(0),\r\n  serviceCharge: Joi.number().min(0).default(0),\r\n  electricityIncluded: Joi.boolean().default(false),\r\n  waterIncluded: Joi.boolean().default(false),\r\n  internetIncluded: Joi.boolean().default(false),\r\n  paymentFrequency: Joi.string().valid(...PAYMENT_FREQUENCIES).default('annually'),\r\n  advancePayment: Joi.number().min(1).max(24).default(12).messages({\r\n    'number.min': 'Advance payment must be at least 1 month',\r\n    'number.max': 'Advance payment cannot exceed 24 months'\r\n  })\r\n});\r\n\r\n// Amenities schema\r\nconst amenitiesSchema = Joi.object({\r\n  // Basic amenities\r\n  wifi: Joi.boolean().default(false),\r\n  parking: Joi.boolean().default(false),\r\n  security: Joi.boolean().default(false),\r\n  generator: Joi.boolean().default(false),\r\n  borehole: Joi.boolean().default(false),\r\n  airConditioning: Joi.boolean().default(false),\r\n  \r\n  // Kitchen amenities\r\n  kitchen: Joi.boolean().default(true),\r\n  refrigerator: Joi.boolean().default(false),\r\n  microwave: Joi.boolean().default(false),\r\n  gasStove: Joi.boolean().default(false),\r\n  \r\n  // Living amenities\r\n  furnished: Joi.boolean().default(false),\r\n  tv: Joi.boolean().default(false),\r\n  washingMachine: Joi.boolean().default(false),\r\n  \r\n  // Building amenities\r\n  elevator: Joi.boolean().default(false),\r\n  gym: Joi.boolean().default(false),\r\n  swimmingPool: Joi.boolean().default(false),\r\n  playground: Joi.boolean().default(false),\r\n  \r\n  // Utilities\r\n  prepaidMeter: Joi.boolean().default(false),\r\n  cableTV: Joi.boolean().default(false),\r\n  cleaningService: Joi.boolean().default(false)\r\n});\r\n\r\n// Rules schema\r\nconst rulesSchema = Joi.object({\r\n  smokingAllowed: Joi.boolean().default(false),\r\n  petsAllowed: Joi.boolean().default(false),\r\n  partiesAllowed: Joi.boolean().default(false),\r\n  guestsAllowed: Joi.boolean().default(true),\r\n  curfew: Joi.string().trim().optional(),\r\n  minimumStay: Joi.number().min(1).max(24).optional().messages({\r\n    'number.min': 'Minimum stay must be at least 1 month',\r\n    'number.max': 'Minimum stay cannot exceed 24 months'\r\n  }),\r\n  maximumOccupants: Joi.number().min(1).max(20).required().messages({\r\n    'number.min': 'Maximum occupants must be at least 1',\r\n    'number.max': 'Maximum occupants cannot exceed 20',\r\n    'any.required': 'Maximum occupants is required'\r\n  })\r\n});\r\n\r\n// Roommate preferences schema\r\nconst roommatePreferencesSchema = Joi.object({\r\n  gender: Joi.string().valid('male', 'female', 'any').default('any'),\r\n  ageRange: Joi.object({\r\n    min: Joi.number().min(18).max(100).default(18),\r\n    max: Joi.number().min(18).max(100).default(65)\r\n  }).default({ min: 18, max: 65 }),\r\n  occupation: Joi.array().items(Joi.string().trim()).default([]),\r\n  lifestyle: Joi.object({\r\n    smoking: Joi.boolean().default(false),\r\n    drinking: Joi.boolean().default(false),\r\n    pets: Joi.boolean().default(false),\r\n    parties: Joi.boolean().default(false)\r\n  }).default({})\r\n});\r\n\r\n/**\r\n * Create property validation schema\r\n */\r\nexport const createPropertySchema = Joi.object({\r\n  title: Joi.string().trim().min(10).max(200).required().messages({\r\n    'string.empty': 'Property title is required',\r\n    'string.min': 'Title must be at least 10 characters',\r\n    'string.max': 'Title cannot exceed 200 characters'\r\n  }),\r\n  description: Joi.string().trim().min(50).max(2000).required().messages({\r\n    'string.empty': 'Property description is required',\r\n    'string.min': 'Description must be at least 50 characters',\r\n    'string.max': 'Description cannot exceed 2000 characters'\r\n  }),\r\n  propertyType: Joi.string().valid(...PROPERTY_TYPES).required().messages({\r\n    'any.only': 'Please select a valid property type',\r\n    'any.required': 'Property type is required'\r\n  }),\r\n  listingType: Joi.string().valid(...LISTING_TYPES).required().messages({\r\n    'any.only': 'Please select a valid listing type',\r\n    'any.required': 'Listing type is required'\r\n  }),\r\n  ownerType: Joi.string().valid(...OWNER_TYPES).default('individual'),\r\n  \r\n  // Property details\r\n  bedrooms: Joi.number().min(0).max(20).required().messages({\r\n    'number.min': 'Bedrooms cannot be negative',\r\n    'number.max': 'Bedrooms cannot exceed 20',\r\n    'any.required': 'Number of bedrooms is required'\r\n  }),\r\n  bathrooms: Joi.number().min(1).max(20).required().messages({\r\n    'number.min': 'Must have at least 1 bathroom',\r\n    'number.max': 'Bathrooms cannot exceed 20',\r\n    'any.required': 'Number of bathrooms is required'\r\n  }),\r\n  totalRooms: Joi.number().min(1).max(50).required().messages({\r\n    'number.min': 'Must have at least 1 room',\r\n    'number.max': 'Total rooms cannot exceed 50',\r\n    'any.required': 'Total number of rooms is required'\r\n  }),\r\n  floorArea: Joi.number().min(10).max(10000).optional().messages({\r\n    'number.min': 'Floor area must be at least 10 square meters',\r\n    'number.max': 'Floor area cannot exceed 10,000 square meters'\r\n  }),\r\n  floor: Joi.number().min(0).max(100).optional(),\r\n  totalFloors: Joi.number().min(1).max(100).optional(),\r\n  yearBuilt: Joi.number().min(1900).max(new Date().getFullYear() + 5).optional(),\r\n  \r\n  // Required schemas\r\n  location: locationSchema.required(),\r\n  pricing: pricingSchema.required(),\r\n  amenities: amenitiesSchema.default({}),\r\n  rules: rulesSchema.required(),\r\n  \r\n  // Optional schemas\r\n  roommatePreferences: roommatePreferencesSchema.optional(),\r\n  \r\n  // Availability\r\n  isAvailable: Joi.boolean().default(true),\r\n  availableFrom: Joi.date().min('now').required().messages({\r\n    'date.min': 'Available from date cannot be in the past',\r\n    'any.required': 'Available from date is required'\r\n  }),\r\n  availableTo: Joi.date().min(Joi.ref('availableFrom')).optional().messages({\r\n    'date.min': 'Available to date must be after available from date'\r\n  }),\r\n  \r\n  // Tags and keywords\r\n  tags: Joi.array().items(Joi.string().trim().max(50)).max(10).default([]).messages({\r\n    'array.max': 'Cannot have more than 10 tags'\r\n  })\r\n});\r\n\r\n/**\r\n * Update property validation schema\r\n */\r\nexport const updatePropertySchema = Joi.object({\r\n  title: Joi.string().trim().min(10).max(200).optional(),\r\n  description: Joi.string().trim().min(50).max(2000).optional(),\r\n  propertyType: Joi.string().valid(...PROPERTY_TYPES).optional(),\r\n  listingType: Joi.string().valid(...LISTING_TYPES).optional(),\r\n  ownerType: Joi.string().valid(...OWNER_TYPES).optional(),\r\n  \r\n  // Property details\r\n  bedrooms: Joi.number().min(0).max(20).optional(),\r\n  bathrooms: Joi.number().min(1).max(20).optional(),\r\n  totalRooms: Joi.number().min(1).max(50).optional(),\r\n  floorArea: Joi.number().min(10).max(10000).optional(),\r\n  floor: Joi.number().min(0).max(100).optional(),\r\n  totalFloors: Joi.number().min(1).max(100).optional(),\r\n  yearBuilt: Joi.number().min(1900).max(new Date().getFullYear() + 5).optional(),\r\n  \r\n  // Optional schemas\r\n  location: locationSchema.optional(),\r\n  pricing: pricingSchema.optional(),\r\n  amenities: amenitiesSchema.optional(),\r\n  rules: rulesSchema.optional(),\r\n  roommatePreferences: roommatePreferencesSchema.optional(),\r\n  \r\n  // Availability\r\n  isAvailable: Joi.boolean().optional(),\r\n  availableFrom: Joi.date().min('now').optional(),\r\n  availableTo: Joi.date().optional(),\r\n  \r\n  // Status\r\n  status: Joi.string().valid(...PROPERTY_STATUSES).optional(),\r\n  \r\n  // Tags\r\n  tags: Joi.array().items(Joi.string().trim().max(50)).max(10).optional()\r\n});\r\n\r\n/**\r\n * Property query validation schema (for GET /properties)\r\n */\r\nexport const propertyQuerySchema = Joi.object({\r\n  page: Joi.number().min(1).default(1),\r\n  limit: Joi.number().min(1).max(100).default(20),\r\n  propertyType: Joi.string().valid(...PROPERTY_TYPES).optional(),\r\n  listingType: Joi.string().valid(...LISTING_TYPES).optional(),\r\n  minPrice: Joi.number().min(0).optional(),\r\n  maxPrice: Joi.number().min(0).optional(),\r\n  bedrooms: Joi.number().min(0).max(20).optional(),\r\n  bathrooms: Joi.number().min(1).max(20).optional(),\r\n  city: Joi.string().trim().max(100).optional(),\r\n  state: Joi.string().valid(...NIGERIAN_STATES).optional(),\r\n  area: Joi.string().trim().max(100).optional(),\r\n  amenities: Joi.string().optional(), // Comma-separated list\r\n  status: Joi.string().valid(...PROPERTY_STATUSES).default('active'),\r\n  sortBy: Joi.string().valid(\r\n    'createdAt', 'updatedAt', 'pricing.rentPerMonth', 'analytics.views',\r\n    'bedrooms', 'bathrooms', 'title'\r\n  ).default('createdAt'),\r\n  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),\r\n  search: Joi.string().trim().max(200).optional(),\r\n  latitude: Joi.number().min(-90).max(90).optional(),\r\n  longitude: Joi.number().min(-180).max(180).optional(),\r\n  radius: Joi.number().min(100).max(50000).default(5000) // in meters\r\n});\r\n\r\n/**\r\n * Search properties validation schema (for POST /properties/search)\r\n */\r\nexport const searchPropertiesSchema = Joi.object({\r\n  query: Joi.string().trim().max(200).optional().messages({\r\n    'string.max': 'Search query cannot exceed 200 characters'\r\n  }),\r\n  propertyType: Joi.string().valid(...PROPERTY_TYPES).optional(),\r\n  listingType: Joi.string().valid(...LISTING_TYPES).optional(),\r\n  minPrice: Joi.number().min(0).optional().messages({\r\n    'number.min': 'Minimum price cannot be negative'\r\n  }),\r\n  maxPrice: Joi.number().min(0).optional().messages({\r\n    'number.min': 'Maximum price cannot be negative'\r\n  }),\r\n  bedrooms: Joi.number().min(0).max(20).optional(),\r\n  bathrooms: Joi.number().min(1).max(20).optional(),\r\n  location: Joi.object({\r\n    city: Joi.string().trim().max(100).optional(),\r\n    state: Joi.string().valid(...NIGERIAN_STATES).optional(),\r\n    area: Joi.string().trim().max(100).optional()\r\n  }).optional(),\r\n  amenities: Joi.array().items(Joi.string().trim()).optional(),\r\n  page: Joi.number().min(1).default(1),\r\n  limit: Joi.number().min(1).max(100).default(20)\r\n}).custom((value, helpers) => {\r\n  // Validate price range\r\n  if (value.minPrice && value.maxPrice && value.minPrice > value.maxPrice) {\r\n    return helpers.error('custom.invalidPriceRange');\r\n  }\r\n  return value;\r\n}).messages({\r\n  'custom.invalidPriceRange': 'Minimum price cannot be greater than maximum price'\r\n});\r\n\r\n/**\r\n * Property photo validation schema\r\n */\r\nexport const propertyPhotoSchema = Joi.object({\r\n  caption: Joi.string().trim().max(200).optional().messages({\r\n    'string.max': 'Photo caption cannot exceed 200 characters'\r\n  }),\r\n  room: Joi.string().trim().max(50).optional().messages({\r\n    'string.max': 'Room name cannot exceed 50 characters'\r\n  }),\r\n  isPrimary: Joi.boolean().default(false)\r\n});\r\n\r\n/**\r\n * Property photo reorder validation schema\r\n */\r\nexport const reorderPhotosSchema = Joi.object({\r\n  photoOrder: Joi.array().items(Joi.string().trim()).min(1).required().messages({\r\n    'array.min': 'Photo order must contain at least one photo ID',\r\n    'any.required': 'Photo order is required'\r\n  })\r\n});\r\n\r\n/**\r\n * Property analytics query validation schema\r\n */\r\nexport const analyticsQuerySchema = Joi.object({\r\n  startDate: Joi.date().optional(),\r\n  endDate: Joi.date().min(Joi.ref('startDate')).optional().messages({\r\n    'date.min': 'End date must be after start date'\r\n  }),\r\n  groupBy: Joi.string().valid('day', 'week', 'month').default('day')\r\n});\r\n\r\n/**\r\n * Nearby properties validation schema\r\n */\r\nexport const nearbyPropertiesSchema = Joi.object({\r\n  latitude: Joi.number().min(-90).max(90).required().messages({\r\n    'number.min': 'Latitude must be between -90 and 90',\r\n    'number.max': 'Latitude must be between -90 and 90',\r\n    'any.required': 'Latitude is required'\r\n  }),\r\n  longitude: Joi.number().min(-180).max(180).required().messages({\r\n    'number.min': 'Longitude must be between -180 and 180',\r\n    'number.max': 'Longitude must be between -180 and 180',\r\n    'any.required': 'Longitude is required'\r\n  }),\r\n  radius: Joi.number().min(100).max(50000).default(5000).messages({\r\n    'number.min': 'Radius must be at least 100 meters',\r\n    'number.max': 'Radius cannot exceed 50 kilometers'\r\n  }),\r\n  limit: Joi.number().min(1).max(100).default(20)\r\n});\r\n\r\n/**\r\n * Property favorites validation schema\r\n */\r\nexport const favoritePropertySchema = Joi.object({\r\n  propertyId: Joi.string().trim().required().messages({\r\n    'string.empty': 'Property ID is required',\r\n    'any.required': 'Property ID is required'\r\n  })\r\n});\r\n\r\n/**\r\n * Property inquiry validation schema\r\n */\r\nexport const propertyInquirySchema = Joi.object({\r\n  message: Joi.string().trim().min(10).max(1000).required().messages({\r\n    'string.empty': 'Inquiry message is required',\r\n    'string.min': 'Message must be at least 10 characters',\r\n    'string.max': 'Message cannot exceed 1000 characters',\r\n    'any.required': 'Inquiry message is required'\r\n  }),\r\n  contactPreference: Joi.string().valid('email', 'phone', 'both').default('email'),\r\n  moveInDate: Joi.date().min('now').optional().messages({\r\n    'date.min': 'Move-in date cannot be in the past'\r\n  }),\r\n  additionalInfo: Joi.string().trim().max(500).optional().messages({\r\n    'string.max': 'Additional information cannot exceed 500 characters'\r\n  })\r\n});\r\n\r\n/**\r\n * Property application validation schema\r\n */\r\nexport const propertyApplicationSchema = Joi.object({\r\n  coverLetter: Joi.string().trim().min(50).max(2000).required().messages({\r\n    'string.empty': 'Cover letter is required',\r\n    'string.min': 'Cover letter must be at least 50 characters',\r\n    'string.max': 'Cover letter cannot exceed 2000 characters',\r\n    'any.required': 'Cover letter is required'\r\n  }),\r\n  moveInDate: Joi.date().min('now').required().messages({\r\n    'date.min': 'Move-in date cannot be in the past',\r\n    'any.required': 'Preferred move-in date is required'\r\n  }),\r\n  leaseDuration: Joi.number().min(1).max(24).required().messages({\r\n    'number.min': 'Lease duration must be at least 1 month',\r\n    'number.max': 'Lease duration cannot exceed 24 months',\r\n    'any.required': 'Preferred lease duration is required'\r\n  }),\r\n  monthlyIncome: Joi.number().min(0).optional().messages({\r\n    'number.min': 'Monthly income cannot be negative'\r\n  }),\r\n  employmentStatus: Joi.string().valid(\r\n    'employed', 'self-employed', 'student', 'unemployed', 'retired'\r\n  ).optional(),\r\n  references: Joi.array().items(\r\n    Joi.object({\r\n      name: Joi.string().trim().max(100).required(),\r\n      relationship: Joi.string().trim().max(100).required(),\r\n      phoneNumber: Joi.string().trim().required(),\r\n      email: Joi.string().email().optional()\r\n    })\r\n  ).max(3).optional().messages({\r\n    'array.max': 'Cannot provide more than 3 references'\r\n  }),\r\n  additionalInfo: Joi.string().trim().max(1000).optional().messages({\r\n    'string.max': 'Additional information cannot exceed 1000 characters'\r\n  })\r\n});\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9c90f6c7a1a9293a811d79c9eb4a255407aa7aef"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_f7mb1mrh6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_f7mb1mrh6();
var __importDefault =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[0]++,
/* istanbul ignore next */
(cov_f7mb1mrh6().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_f7mb1mrh6().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_f7mb1mrh6().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_f7mb1mrh6().f[0]++;
  cov_f7mb1mrh6().s[1]++;
  return /* istanbul ignore next */(cov_f7mb1mrh6().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_f7mb1mrh6().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_f7mb1mrh6().s[3]++;
exports.propertyApplicationSchema = exports.propertyInquirySchema = exports.favoritePropertySchema = exports.nearbyPropertiesSchema = exports.analyticsQuerySchema = exports.reorderPhotosSchema = exports.propertyPhotoSchema = exports.searchPropertiesSchema = exports.propertyQuerySchema = exports.updatePropertySchema = exports.createPropertySchema = void 0;
const joi_1 =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[4]++, __importDefault(require("joi")));
// Nigerian states for validation
const NIGERIAN_STATES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[5]++, ['Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara']);
// Property types
const PROPERTY_TYPES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[6]++, ['apartment', 'house', 'condo', 'studio', 'duplex', 'bungalow', 'mansion']);
const LISTING_TYPES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[7]++, ['rent', 'roommate', 'sublet']);
const OWNER_TYPES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[8]++, ['individual', 'agent', 'company']);
const PAYMENT_FREQUENCIES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[9]++, ['monthly', 'quarterly', 'biannually', 'annually']);
const PROPERTY_STATUSES =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[10]++, ['draft', 'active', 'inactive', 'rented', 'suspended']);
// Location schema
const locationSchema =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[11]++, joi_1.default.object({
  address: joi_1.default.string().trim().max(300).required().messages({
    'string.empty': 'Address is required',
    'string.max': 'Address cannot exceed 300 characters'
  }),
  city: joi_1.default.string().trim().max(100).required().messages({
    'string.empty': 'City is required',
    'string.max': 'City cannot exceed 100 characters'
  }),
  state: joi_1.default.string().valid(...NIGERIAN_STATES).required().messages({
    'any.only': 'Please select a valid Nigerian state',
    'any.required': 'State is required'
  }),
  country: joi_1.default.string().valid('Nigeria').default('Nigeria'),
  area: joi_1.default.string().trim().max(100).optional().messages({
    'string.max': 'Area cannot exceed 100 characters'
  }),
  landmark: joi_1.default.string().trim().max(200).optional().messages({
    'string.max': 'Landmark cannot exceed 200 characters'
  }),
  coordinates: joi_1.default.object({
    type: joi_1.default.string().valid('Point').default('Point'),
    coordinates: joi_1.default.array().items(joi_1.default.number()).length(2).required().messages({
      'array.length': 'Coordinates must contain exactly 2 numbers [longitude, latitude]'
    })
  }).optional()
}));
// Pricing schema
const pricingSchema =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[12]++, joi_1.default.object({
  rentPerMonth: joi_1.default.number().min(1000).max(10000000).required().messages({
    'number.min': 'Rent must be at least ₦1,000',
    'number.max': 'Rent cannot exceed ₦10,000,000',
    'any.required': 'Monthly rent is required'
  }),
  securityDeposit: joi_1.default.number().min(0).required().messages({
    'number.min': 'Security deposit cannot be negative',
    'any.required': 'Security deposit is required'
  }),
  agentFee: joi_1.default.number().min(0).default(0),
  legalFee: joi_1.default.number().min(0).default(0),
  cautionFee: joi_1.default.number().min(0).default(0),
  serviceCharge: joi_1.default.number().min(0).default(0),
  electricityIncluded: joi_1.default.boolean().default(false),
  waterIncluded: joi_1.default.boolean().default(false),
  internetIncluded: joi_1.default.boolean().default(false),
  paymentFrequency: joi_1.default.string().valid(...PAYMENT_FREQUENCIES).default('annually'),
  advancePayment: joi_1.default.number().min(1).max(24).default(12).messages({
    'number.min': 'Advance payment must be at least 1 month',
    'number.max': 'Advance payment cannot exceed 24 months'
  })
}));
// Amenities schema
const amenitiesSchema =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[13]++, joi_1.default.object({
  // Basic amenities
  wifi: joi_1.default.boolean().default(false),
  parking: joi_1.default.boolean().default(false),
  security: joi_1.default.boolean().default(false),
  generator: joi_1.default.boolean().default(false),
  borehole: joi_1.default.boolean().default(false),
  airConditioning: joi_1.default.boolean().default(false),
  // Kitchen amenities
  kitchen: joi_1.default.boolean().default(true),
  refrigerator: joi_1.default.boolean().default(false),
  microwave: joi_1.default.boolean().default(false),
  gasStove: joi_1.default.boolean().default(false),
  // Living amenities
  furnished: joi_1.default.boolean().default(false),
  tv: joi_1.default.boolean().default(false),
  washingMachine: joi_1.default.boolean().default(false),
  // Building amenities
  elevator: joi_1.default.boolean().default(false),
  gym: joi_1.default.boolean().default(false),
  swimmingPool: joi_1.default.boolean().default(false),
  playground: joi_1.default.boolean().default(false),
  // Utilities
  prepaidMeter: joi_1.default.boolean().default(false),
  cableTV: joi_1.default.boolean().default(false),
  cleaningService: joi_1.default.boolean().default(false)
}));
// Rules schema
const rulesSchema =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[14]++, joi_1.default.object({
  smokingAllowed: joi_1.default.boolean().default(false),
  petsAllowed: joi_1.default.boolean().default(false),
  partiesAllowed: joi_1.default.boolean().default(false),
  guestsAllowed: joi_1.default.boolean().default(true),
  curfew: joi_1.default.string().trim().optional(),
  minimumStay: joi_1.default.number().min(1).max(24).optional().messages({
    'number.min': 'Minimum stay must be at least 1 month',
    'number.max': 'Minimum stay cannot exceed 24 months'
  }),
  maximumOccupants: joi_1.default.number().min(1).max(20).required().messages({
    'number.min': 'Maximum occupants must be at least 1',
    'number.max': 'Maximum occupants cannot exceed 20',
    'any.required': 'Maximum occupants is required'
  })
}));
// Roommate preferences schema
const roommatePreferencesSchema =
/* istanbul ignore next */
(cov_f7mb1mrh6().s[15]++, joi_1.default.object({
  gender: joi_1.default.string().valid('male', 'female', 'any').default('any'),
  ageRange: joi_1.default.object({
    min: joi_1.default.number().min(18).max(100).default(18),
    max: joi_1.default.number().min(18).max(100).default(65)
  }).default({
    min: 18,
    max: 65
  }),
  occupation: joi_1.default.array().items(joi_1.default.string().trim()).default([]),
  lifestyle: joi_1.default.object({
    smoking: joi_1.default.boolean().default(false),
    drinking: joi_1.default.boolean().default(false),
    pets: joi_1.default.boolean().default(false),
    parties: joi_1.default.boolean().default(false)
  }).default({})
}));
/**
 * Create property validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[16]++;
exports.createPropertySchema = joi_1.default.object({
  title: joi_1.default.string().trim().min(10).max(200).required().messages({
    'string.empty': 'Property title is required',
    'string.min': 'Title must be at least 10 characters',
    'string.max': 'Title cannot exceed 200 characters'
  }),
  description: joi_1.default.string().trim().min(50).max(2000).required().messages({
    'string.empty': 'Property description is required',
    'string.min': 'Description must be at least 50 characters',
    'string.max': 'Description cannot exceed 2000 characters'
  }),
  propertyType: joi_1.default.string().valid(...PROPERTY_TYPES).required().messages({
    'any.only': 'Please select a valid property type',
    'any.required': 'Property type is required'
  }),
  listingType: joi_1.default.string().valid(...LISTING_TYPES).required().messages({
    'any.only': 'Please select a valid listing type',
    'any.required': 'Listing type is required'
  }),
  ownerType: joi_1.default.string().valid(...OWNER_TYPES).default('individual'),
  // Property details
  bedrooms: joi_1.default.number().min(0).max(20).required().messages({
    'number.min': 'Bedrooms cannot be negative',
    'number.max': 'Bedrooms cannot exceed 20',
    'any.required': 'Number of bedrooms is required'
  }),
  bathrooms: joi_1.default.number().min(1).max(20).required().messages({
    'number.min': 'Must have at least 1 bathroom',
    'number.max': 'Bathrooms cannot exceed 20',
    'any.required': 'Number of bathrooms is required'
  }),
  totalRooms: joi_1.default.number().min(1).max(50).required().messages({
    'number.min': 'Must have at least 1 room',
    'number.max': 'Total rooms cannot exceed 50',
    'any.required': 'Total number of rooms is required'
  }),
  floorArea: joi_1.default.number().min(10).max(10000).optional().messages({
    'number.min': 'Floor area must be at least 10 square meters',
    'number.max': 'Floor area cannot exceed 10,000 square meters'
  }),
  floor: joi_1.default.number().min(0).max(100).optional(),
  totalFloors: joi_1.default.number().min(1).max(100).optional(),
  yearBuilt: joi_1.default.number().min(1900).max(new Date().getFullYear() + 5).optional(),
  // Required schemas
  location: locationSchema.required(),
  pricing: pricingSchema.required(),
  amenities: amenitiesSchema.default({}),
  rules: rulesSchema.required(),
  // Optional schemas
  roommatePreferences: roommatePreferencesSchema.optional(),
  // Availability
  isAvailable: joi_1.default.boolean().default(true),
  availableFrom: joi_1.default.date().min('now').required().messages({
    'date.min': 'Available from date cannot be in the past',
    'any.required': 'Available from date is required'
  }),
  availableTo: joi_1.default.date().min(joi_1.default.ref('availableFrom')).optional().messages({
    'date.min': 'Available to date must be after available from date'
  }),
  // Tags and keywords
  tags: joi_1.default.array().items(joi_1.default.string().trim().max(50)).max(10).default([]).messages({
    'array.max': 'Cannot have more than 10 tags'
  })
});
/**
 * Update property validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[17]++;
exports.updatePropertySchema = joi_1.default.object({
  title: joi_1.default.string().trim().min(10).max(200).optional(),
  description: joi_1.default.string().trim().min(50).max(2000).optional(),
  propertyType: joi_1.default.string().valid(...PROPERTY_TYPES).optional(),
  listingType: joi_1.default.string().valid(...LISTING_TYPES).optional(),
  ownerType: joi_1.default.string().valid(...OWNER_TYPES).optional(),
  // Property details
  bedrooms: joi_1.default.number().min(0).max(20).optional(),
  bathrooms: joi_1.default.number().min(1).max(20).optional(),
  totalRooms: joi_1.default.number().min(1).max(50).optional(),
  floorArea: joi_1.default.number().min(10).max(10000).optional(),
  floor: joi_1.default.number().min(0).max(100).optional(),
  totalFloors: joi_1.default.number().min(1).max(100).optional(),
  yearBuilt: joi_1.default.number().min(1900).max(new Date().getFullYear() + 5).optional(),
  // Optional schemas
  location: locationSchema.optional(),
  pricing: pricingSchema.optional(),
  amenities: amenitiesSchema.optional(),
  rules: rulesSchema.optional(),
  roommatePreferences: roommatePreferencesSchema.optional(),
  // Availability
  isAvailable: joi_1.default.boolean().optional(),
  availableFrom: joi_1.default.date().min('now').optional(),
  availableTo: joi_1.default.date().optional(),
  // Status
  status: joi_1.default.string().valid(...PROPERTY_STATUSES).optional(),
  // Tags
  tags: joi_1.default.array().items(joi_1.default.string().trim().max(50)).max(10).optional()
});
/**
 * Property query validation schema (for GET /properties)
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[18]++;
exports.propertyQuerySchema = joi_1.default.object({
  page: joi_1.default.number().min(1).default(1),
  limit: joi_1.default.number().min(1).max(100).default(20),
  propertyType: joi_1.default.string().valid(...PROPERTY_TYPES).optional(),
  listingType: joi_1.default.string().valid(...LISTING_TYPES).optional(),
  minPrice: joi_1.default.number().min(0).optional(),
  maxPrice: joi_1.default.number().min(0).optional(),
  bedrooms: joi_1.default.number().min(0).max(20).optional(),
  bathrooms: joi_1.default.number().min(1).max(20).optional(),
  city: joi_1.default.string().trim().max(100).optional(),
  state: joi_1.default.string().valid(...NIGERIAN_STATES).optional(),
  area: joi_1.default.string().trim().max(100).optional(),
  amenities: joi_1.default.string().optional(),
  // Comma-separated list
  status: joi_1.default.string().valid(...PROPERTY_STATUSES).default('active'),
  sortBy: joi_1.default.string().valid('createdAt', 'updatedAt', 'pricing.rentPerMonth', 'analytics.views', 'bedrooms', 'bathrooms', 'title').default('createdAt'),
  sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
  search: joi_1.default.string().trim().max(200).optional(),
  latitude: joi_1.default.number().min(-90).max(90).optional(),
  longitude: joi_1.default.number().min(-180).max(180).optional(),
  radius: joi_1.default.number().min(100).max(50000).default(5000) // in meters
});
/**
 * Search properties validation schema (for POST /properties/search)
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[19]++;
exports.searchPropertiesSchema = joi_1.default.object({
  query: joi_1.default.string().trim().max(200).optional().messages({
    'string.max': 'Search query cannot exceed 200 characters'
  }),
  propertyType: joi_1.default.string().valid(...PROPERTY_TYPES).optional(),
  listingType: joi_1.default.string().valid(...LISTING_TYPES).optional(),
  minPrice: joi_1.default.number().min(0).optional().messages({
    'number.min': 'Minimum price cannot be negative'
  }),
  maxPrice: joi_1.default.number().min(0).optional().messages({
    'number.min': 'Maximum price cannot be negative'
  }),
  bedrooms: joi_1.default.number().min(0).max(20).optional(),
  bathrooms: joi_1.default.number().min(1).max(20).optional(),
  location: joi_1.default.object({
    city: joi_1.default.string().trim().max(100).optional(),
    state: joi_1.default.string().valid(...NIGERIAN_STATES).optional(),
    area: joi_1.default.string().trim().max(100).optional()
  }).optional(),
  amenities: joi_1.default.array().items(joi_1.default.string().trim()).optional(),
  page: joi_1.default.number().min(1).default(1),
  limit: joi_1.default.number().min(1).max(100).default(20)
}).custom((value, helpers) => {
  /* istanbul ignore next */
  cov_f7mb1mrh6().f[1]++;
  cov_f7mb1mrh6().s[20]++;
  // Validate price range
  if (
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[4][0]++, value.minPrice) &&
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[4][1]++, value.maxPrice) &&
  /* istanbul ignore next */
  (cov_f7mb1mrh6().b[4][2]++, value.minPrice > value.maxPrice)) {
    /* istanbul ignore next */
    cov_f7mb1mrh6().b[3][0]++;
    cov_f7mb1mrh6().s[21]++;
    return helpers.error('custom.invalidPriceRange');
  } else
  /* istanbul ignore next */
  {
    cov_f7mb1mrh6().b[3][1]++;
  }
  cov_f7mb1mrh6().s[22]++;
  return value;
}).messages({
  'custom.invalidPriceRange': 'Minimum price cannot be greater than maximum price'
});
/**
 * Property photo validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[23]++;
exports.propertyPhotoSchema = joi_1.default.object({
  caption: joi_1.default.string().trim().max(200).optional().messages({
    'string.max': 'Photo caption cannot exceed 200 characters'
  }),
  room: joi_1.default.string().trim().max(50).optional().messages({
    'string.max': 'Room name cannot exceed 50 characters'
  }),
  isPrimary: joi_1.default.boolean().default(false)
});
/**
 * Property photo reorder validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[24]++;
exports.reorderPhotosSchema = joi_1.default.object({
  photoOrder: joi_1.default.array().items(joi_1.default.string().trim()).min(1).required().messages({
    'array.min': 'Photo order must contain at least one photo ID',
    'any.required': 'Photo order is required'
  })
});
/**
 * Property analytics query validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[25]++;
exports.analyticsQuerySchema = joi_1.default.object({
  startDate: joi_1.default.date().optional(),
  endDate: joi_1.default.date().min(joi_1.default.ref('startDate')).optional().messages({
    'date.min': 'End date must be after start date'
  }),
  groupBy: joi_1.default.string().valid('day', 'week', 'month').default('day')
});
/**
 * Nearby properties validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[26]++;
exports.nearbyPropertiesSchema = joi_1.default.object({
  latitude: joi_1.default.number().min(-90).max(90).required().messages({
    'number.min': 'Latitude must be between -90 and 90',
    'number.max': 'Latitude must be between -90 and 90',
    'any.required': 'Latitude is required'
  }),
  longitude: joi_1.default.number().min(-180).max(180).required().messages({
    'number.min': 'Longitude must be between -180 and 180',
    'number.max': 'Longitude must be between -180 and 180',
    'any.required': 'Longitude is required'
  }),
  radius: joi_1.default.number().min(100).max(50000).default(5000).messages({
    'number.min': 'Radius must be at least 100 meters',
    'number.max': 'Radius cannot exceed 50 kilometers'
  }),
  limit: joi_1.default.number().min(1).max(100).default(20)
});
/**
 * Property favorites validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[27]++;
exports.favoritePropertySchema = joi_1.default.object({
  propertyId: joi_1.default.string().trim().required().messages({
    'string.empty': 'Property ID is required',
    'any.required': 'Property ID is required'
  })
});
/**
 * Property inquiry validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[28]++;
exports.propertyInquirySchema = joi_1.default.object({
  message: joi_1.default.string().trim().min(10).max(1000).required().messages({
    'string.empty': 'Inquiry message is required',
    'string.min': 'Message must be at least 10 characters',
    'string.max': 'Message cannot exceed 1000 characters',
    'any.required': 'Inquiry message is required'
  }),
  contactPreference: joi_1.default.string().valid('email', 'phone', 'both').default('email'),
  moveInDate: joi_1.default.date().min('now').optional().messages({
    'date.min': 'Move-in date cannot be in the past'
  }),
  additionalInfo: joi_1.default.string().trim().max(500).optional().messages({
    'string.max': 'Additional information cannot exceed 500 characters'
  })
});
/**
 * Property application validation schema
 */
/* istanbul ignore next */
cov_f7mb1mrh6().s[29]++;
exports.propertyApplicationSchema = joi_1.default.object({
  coverLetter: joi_1.default.string().trim().min(50).max(2000).required().messages({
    'string.empty': 'Cover letter is required',
    'string.min': 'Cover letter must be at least 50 characters',
    'string.max': 'Cover letter cannot exceed 2000 characters',
    'any.required': 'Cover letter is required'
  }),
  moveInDate: joi_1.default.date().min('now').required().messages({
    'date.min': 'Move-in date cannot be in the past',
    'any.required': 'Preferred move-in date is required'
  }),
  leaseDuration: joi_1.default.number().min(1).max(24).required().messages({
    'number.min': 'Lease duration must be at least 1 month',
    'number.max': 'Lease duration cannot exceed 24 months',
    'any.required': 'Preferred lease duration is required'
  }),
  monthlyIncome: joi_1.default.number().min(0).optional().messages({
    'number.min': 'Monthly income cannot be negative'
  }),
  employmentStatus: joi_1.default.string().valid('employed', 'self-employed', 'student', 'unemployed', 'retired').optional(),
  references: joi_1.default.array().items(joi_1.default.object({
    name: joi_1.default.string().trim().max(100).required(),
    relationship: joi_1.default.string().trim().max(100).required(),
    phoneNumber: joi_1.default.string().trim().required(),
    email: joi_1.default.string().email().optional()
  })).max(3).optional().messages({
    'array.max': 'Cannot provide more than 3 references'
  }),
  additionalInfo: joi_1.default.string().trim().max(1000).optional().messages({
    'string.max': 'Additional information cannot exceed 1000 characters'
  })
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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