059d8d835879ca233fa12ae34ce2b09a
"use strict";

/* istanbul ignore next */
function cov_3y1ppby04() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\auditService.ts";
  var hash = "aaee55d97ee762983f94fdf79c4186aee3316be3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\auditService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 62
        }
      },
      "36": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 94
        }
      },
      "37": {
        start: {
          line: 37,
          column: 19
        },
        end: {
          line: 37,
          column: 52
        }
      },
      "38": {
        start: {
          line: 38,
          column: 17
        },
        end: {
          line: 38,
          column: 43
        }
      },
      "39": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 85,
          column: 69
        }
      },
      "40": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 43,
          column: 54
        }
      },
      "41": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 52
        }
      },
      "42": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 40
        }
      },
      "43": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 72
        }
      },
      "44": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 72
        }
      },
      "45": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 60
        }
      },
      "46": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "47": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 56
        }
      },
      "48": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 60
        }
      },
      "49": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 56
        }
      },
      "50": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 54
        }
      },
      "51": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 70
        }
      },
      "52": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 52
        }
      },
      "53": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 52
        }
      },
      "54": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 52
        }
      },
      "55": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 52
        }
      },
      "56": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 50
        }
      },
      "57": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 54
        }
      },
      "58": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 56
        }
      },
      "59": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 66
        }
      },
      "60": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 66
        }
      },
      "61": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 54
        }
      },
      "62": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 70
        }
      },
      "63": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 50
        }
      },
      "64": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 52
        }
      },
      "65": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 66
        }
      },
      "66": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 52
        }
      },
      "67": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 70
        }
      },
      "68": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 56
        }
      },
      "69": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 60
        }
      },
      "70": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 58
        }
      },
      "71": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 60
        }
      },
      "72": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 80,
          column: 60
        }
      },
      "73": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 81,
          column: 54
        }
      },
      "74": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 52
        }
      },
      "75": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 62
        }
      },
      "76": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 68
        }
      },
      "77": {
        start: {
          line: 88,
          column: 0
        },
        end: {
          line: 93,
          column: 54
        }
      },
      "78": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 29
        }
      },
      "79": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 35
        }
      },
      "80": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 31
        }
      },
      "81": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 39
        }
      },
      "82": {
        start: {
          line: 95,
          column: 23
        },
        end: {
          line: 189,
          column: 2
        }
      },
      "83": {
        start: {
          line: 191,
          column: 0
        },
        end: {
          line: 191,
          column: 51
        }
      },
      "84": {
        start: {
          line: 192,
          column: 0
        },
        end: {
          line: 192,
          column: 54
        }
      },
      "85": {
        start: {
          line: 193,
          column: 0
        },
        end: {
          line: 193,
          column: 54
        }
      },
      "86": {
        start: {
          line: 194,
          column: 0
        },
        end: {
          line: 194,
          column: 54
        }
      },
      "87": {
        start: {
          line: 195,
          column: 0
        },
        end: {
          line: 195,
          column: 52
        }
      },
      "88": {
        start: {
          line: 197,
          column: 0
        },
        end: {
          line: 197,
          column: 83
        }
      },
      "89": {
        start: {
          line: 198,
          column: 0
        },
        end: {
          line: 198,
          column: 72
        }
      },
      "90": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 206,
          column: 10
        }
      },
      "91": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 251,
          column: 9
        }
      },
      "92": {
        start: {
          line: 213,
          column: 30
        },
        end: {
          line: 234,
          column: 13
        }
      },
      "93": {
        start: {
          line: 236,
          column: 29
        },
        end: {
          line: 236,
          column: 60
        }
      },
      "94": {
        start: {
          line: 237,
          column: 12
        },
        end: {
          line: 237,
          column: 34
        }
      },
      "95": {
        start: {
          line: 239,
          column: 12
        },
        end: {
          line: 245,
          column: 15
        }
      },
      "96": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 247,
          column: 58
        }
      },
      "97": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 250,
          column: 71
        }
      },
      "98": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 266,
          column: 11
        }
      },
      "99": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 279,
          column: 11
        }
      },
      "100": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 291,
          column: 11
        }
      },
      "101": {
        start: {
          line: 297,
          column: 21
        },
        end: {
          line: 297,
          column: 51
        }
      },
      "102": {
        start: {
          line: 298,
          column: 22
        },
        end: {
          line: 298,
          column: 69
        }
      },
      "103": {
        start: {
          line: 299,
          column: 21
        },
        end: {
          line: 299,
          column: 39
        }
      },
      "104": {
        start: {
          line: 301,
          column: 22
        },
        end: {
          line: 301,
          column: 24
        }
      },
      "105": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 303,
          column: 42
        }
      },
      "106": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 303,
          column: 42
        }
      },
      "107": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 305,
          column: 48
        }
      },
      "108": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 305,
          column: 48
        }
      },
      "109": {
        start: {
          line: 306,
          column: 8
        },
        end: {
          line: 307,
          column: 48
        }
      },
      "110": {
        start: {
          line: 307,
          column: 12
        },
        end: {
          line: 307,
          column: 48
        }
      },
      "111": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 309,
          column: 48
        }
      },
      "112": {
        start: {
          line: 309,
          column: 12
        },
        end: {
          line: 309,
          column: 48
        }
      },
      "113": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 311,
          column: 44
        }
      },
      "114": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 311,
          column: 44
        }
      },
      "115": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "116": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 313,
          column: 33
        }
      },
      "117": {
        start: {
          line: 314,
          column: 12
        },
        end: {
          line: 315,
          column: 57
        }
      },
      "118": {
        start: {
          line: 315,
          column: 16
        },
        end: {
          line: 315,
          column: 57
        }
      },
      "119": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 317,
          column: 55
        }
      },
      "120": {
        start: {
          line: 317,
          column: 16
        },
        end: {
          line: 317,
          column: 55
        }
      },
      "121": {
        start: {
          line: 320,
          column: 30
        },
        end: {
          line: 327,
          column: 10
        }
      },
      "122": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 333,
          column: 10
        }
      },
      "123": {
        start: {
          line: 339,
          column: 20
        },
        end: {
          line: 339,
          column: 30
        }
      },
      "124": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 354,
          column: 9
        }
      },
      "125": {
        start: {
          line: 343,
          column: 16
        },
        end: {
          line: 343,
          column: 69
        }
      },
      "126": {
        start: {
          line: 344,
          column: 16
        },
        end: {
          line: 344,
          column: 22
        }
      },
      "127": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 74
        }
      },
      "128": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 347,
          column: 22
        }
      },
      "129": {
        start: {
          line: 349,
          column: 16
        },
        end: {
          line: 349,
          column: 78
        }
      },
      "130": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 350,
          column: 22
        }
      },
      "131": {
        start: {
          line: 352,
          column: 16
        },
        end: {
          line: 352,
          column: 79
        }
      },
      "132": {
        start: {
          line: 353,
          column: 16
        },
        end: {
          line: 353,
          column: 22
        }
      },
      "133": {
        start: {
          line: 355,
          column: 106
        },
        end: {
          line: 387,
          column: 10
        }
      },
      "134": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 406,
          column: 10
        }
      },
      "135": {
        start: {
          line: 412,
          column: 20
        },
        end: {
          line: 412,
          column: 30
        }
      },
      "136": {
        start: {
          line: 413,
          column: 24
        },
        end: {
          line: 413,
          column: 64
        }
      },
      "137": {
        start: {
          line: 414,
          column: 31
        },
        end: {
          line: 414,
          column: 71
        }
      },
      "138": {
        start: {
          line: 415,
          column: 8
        },
        end: {
          line: 451,
          column: 9
        }
      },
      "139": {
        start: {
          line: 417,
          column: 12
        },
        end: {
          line: 426,
          column: 13
        }
      },
      "140": {
        start: {
          line: 418,
          column: 39
        },
        end: {
          line: 422,
          column: 18
        }
      },
      "141": {
        start: {
          line: 423,
          column: 16
        },
        end: {
          line: 425,
          column: 17
        }
      },
      "142": {
        start: {
          line: 424,
          column: 20
        },
        end: {
          line: 424,
          column: 98
        }
      },
      "143": {
        start: {
          line: 428,
          column: 12
        },
        end: {
          line: 437,
          column: 13
        }
      },
      "144": {
        start: {
          line: 429,
          column: 41
        },
        end: {
          line: 433,
          column: 18
        }
      },
      "145": {
        start: {
          line: 434,
          column: 16
        },
        end: {
          line: 436,
          column: 17
        }
      },
      "146": {
        start: {
          line: 435,
          column: 20
        },
        end: {
          line: 435,
          column: 91
        }
      },
      "147": {
        start: {
          line: 439,
          column: 12
        },
        end: {
          line: 447,
          column: 13
        }
      },
      "148": {
        start: {
          line: 440,
          column: 36
        },
        end: {
          line: 443,
          column: 18
        }
      },
      "149": {
        start: {
          line: 444,
          column: 16
        },
        end: {
          line: 446,
          column: 17
        }
      },
      "150": {
        start: {
          line: 445,
          column: 20
        },
        end: {
          line: 445,
          column: 98
        }
      },
      "151": {
        start: {
          line: 450,
          column: 12
        },
        end: {
          line: 450,
          column: 80
        }
      },
      "152": {
        start: {
          line: 457,
          column: 8
        },
        end: {
          line: 488,
          column: 9
        }
      },
      "153": {
        start: {
          line: 458,
          column: 34
        },
        end: {
          line: 476,
          column: 14
        }
      },
      "154": {
        start: {
          line: 477,
          column: 12
        },
        end: {
          line: 477,
          column: 39
        }
      },
      "155": {
        start: {
          line: 479,
          column: 12
        },
        end: {
          line: 484,
          column: 15
        }
      },
      "156": {
        start: {
          line: 487,
          column: 12
        },
        end: {
          line: 487,
          column: 79
        }
      },
      "157": {
        start: {
          line: 494,
          column: 31
        },
        end: {
          line: 500,
          column: 9
        }
      },
      "158": {
        start: {
          line: 501,
          column: 33
        },
        end: {
          line: 507,
          column: 9
        }
      },
      "159": {
        start: {
          line: 508,
          column: 35
        },
        end: {
          line: 513,
          column: 9
        }
      },
      "160": {
        start: {
          line: 514,
          column: 8
        },
        end: {
          line: 515,
          column: 38
        }
      },
      "161": {
        start: {
          line: 515,
          column: 12
        },
        end: {
          line: 515,
          column: 38
        }
      },
      "162": {
        start: {
          line: 516,
          column: 8
        },
        end: {
          line: 517,
          column: 34
        }
      },
      "163": {
        start: {
          line: 517,
          column: 12
        },
        end: {
          line: 517,
          column: 34
        }
      },
      "164": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 519,
          column: 36
        }
      },
      "165": {
        start: {
          line: 519,
          column: 12
        },
        end: {
          line: 519,
          column: 36
        }
      },
      "166": {
        start: {
          line: 520,
          column: 8
        },
        end: {
          line: 520,
          column: 29
        }
      },
      "167": {
        start: {
          line: 526,
          column: 8
        },
        end: {
          line: 530,
          column: 22
        }
      },
      "168": {
        start: {
          line: 536,
          column: 8
        },
        end: {
          line: 538,
          column: 9
        }
      },
      "169": {
        start: {
          line: 537,
          column: 12
        },
        end: {
          line: 537,
          column: 93
        }
      },
      "170": {
        start: {
          line: 539,
          column: 24
        },
        end: {
          line: 539,
          column: 53
        }
      },
      "171": {
        start: {
          line: 540,
          column: 19
        },
        end: {
          line: 540,
          column: 43
        }
      },
      "172": {
        start: {
          line: 541,
          column: 23
        },
        end: {
          line: 541,
          column: 51
        }
      },
      "173": {
        start: {
          line: 542,
          column: 25
        },
        end: {
          line: 542,
          column: 69
        }
      },
      "174": {
        start: {
          line: 543,
          column: 8
        },
        end: {
          line: 543,
          column: 49
        }
      },
      "175": {
        start: {
          line: 546,
          column: 8
        },
        end: {
          line: 547,
          column: 28
        }
      },
      "176": {
        start: {
          line: 547,
          column: 12
        },
        end: {
          line: 547,
          column: 28
        }
      },
      "177": {
        start: {
          line: 548,
          column: 8
        },
        end: {
          line: 549,
          column: 29
        }
      },
      "178": {
        start: {
          line: 549,
          column: 12
        },
        end: {
          line: 549,
          column: 29
        }
      },
      "179": {
        start: {
          line: 550,
          column: 8
        },
        end: {
          line: 551,
          column: 28
        }
      },
      "180": {
        start: {
          line: 551,
          column: 12
        },
        end: {
          line: 551,
          column: 28
        }
      },
      "181": {
        start: {
          line: 552,
          column: 8
        },
        end: {
          line: 553,
          column: 26
        }
      },
      "182": {
        start: {
          line: 553,
          column: 12
        },
        end: {
          line: 553,
          column: 26
        }
      },
      "183": {
        start: {
          line: 554,
          column: 8
        },
        end: {
          line: 555,
          column: 27
        }
      },
      "184": {
        start: {
          line: 555,
          column: 12
        },
        end: {
          line: 555,
          column: 27
        }
      },
      "185": {
        start: {
          line: 556,
          column: 8
        },
        end: {
          line: 556,
          column: 25
        }
      },
      "186": {
        start: {
          line: 559,
          column: 8
        },
        end: {
          line: 560,
          column: 29
        }
      },
      "187": {
        start: {
          line: 560,
          column: 12
        },
        end: {
          line: 560,
          column: 29
        }
      },
      "188": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 562,
          column: 27
        }
      },
      "189": {
        start: {
          line: 562,
          column: 12
        },
        end: {
          line: 562,
          column: 27
        }
      },
      "190": {
        start: {
          line: 563,
          column: 8
        },
        end: {
          line: 564,
          column: 27
        }
      },
      "191": {
        start: {
          line: 564,
          column: 12
        },
        end: {
          line: 564,
          column: 27
        }
      },
      "192": {
        start: {
          line: 565,
          column: 8
        },
        end: {
          line: 566,
          column: 29
        }
      },
      "193": {
        start: {
          line: 566,
          column: 12
        },
        end: {
          line: 566,
          column: 29
        }
      },
      "194": {
        start: {
          line: 567,
          column: 8
        },
        end: {
          line: 568,
          column: 25
        }
      },
      "195": {
        start: {
          line: 568,
          column: 12
        },
        end: {
          line: 568,
          column: 25
        }
      },
      "196": {
        start: {
          line: 569,
          column: 8
        },
        end: {
          line: 569,
          column: 25
        }
      },
      "197": {
        start: {
          line: 572,
          column: 8
        },
        end: {
          line: 573,
          column: 28
        }
      },
      "198": {
        start: {
          line: 573,
          column: 12
        },
        end: {
          line: 573,
          column: 28
        }
      },
      "199": {
        start: {
          line: 574,
          column: 8
        },
        end: {
          line: 575,
          column: 28
        }
      },
      "200": {
        start: {
          line: 575,
          column: 12
        },
        end: {
          line: 575,
          column: 28
        }
      },
      "201": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 576,
          column: 25
        }
      },
      "202": {
        start: {
          line: 580,
          column: 0
        },
        end: {
          line: 580,
          column: 42
        }
      },
      "203": {
        start: {
          line: 581,
          column: 0
        },
        end: {
          line: 581,
          column: 39
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 41,
            column: 1
          },
          end: {
            line: 41,
            column: 2
          }
        },
        loc: {
          start: {
            line: 41,
            column: 27
          },
          end: {
            line: 85,
            column: 1
          }
        },
        line: 41
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 88,
            column: 1
          },
          end: {
            line: 88,
            column: 2
          }
        },
        loc: {
          start: {
            line: 88,
            column: 22
          },
          end: {
            line: 93,
            column: 1
          }
        },
        line: 88
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 18
          },
          end: {
            line: 207,
            column: 5
          }
        },
        line: 200
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 211,
            column: 5
          }
        },
        loc: {
          start: {
            line: 211,
            column: 49
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 211
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        loc: {
          start: {
            line: 256,
            column: 56
          },
          end: {
            line: 267,
            column: 5
          }
        },
        line: 256
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 271,
            column: 4
          },
          end: {
            line: 271,
            column: 5
          }
        },
        loc: {
          start: {
            line: 271,
            column: 84
          },
          end: {
            line: 280,
            column: 5
          }
        },
        line: 271
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 284,
            column: 5
          }
        },
        loc: {
          start: {
            line: 284,
            column: 52
          },
          end: {
            line: 292,
            column: 5
          }
        },
        line: 284
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 296,
            column: 4
          },
          end: {
            line: 296,
            column: 5
          }
        },
        loc: {
          start: {
            line: 296,
            column: 37
          },
          end: {
            line: 334,
            column: 5
          }
        },
        line: 296
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 338,
            column: 5
          }
        },
        loc: {
          start: {
            line: 338,
            column: 43
          },
          end: {
            line: 407,
            column: 5
          }
        },
        line: 338
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 5
          }
        },
        loc: {
          start: {
            line: 411,
            column: 45
          },
          end: {
            line: 452,
            column: 5
          }
        },
        line: 411
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 456,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        },
        loc: {
          start: {
            line: 456,
            column: 55
          },
          end: {
            line: 489,
            column: 5
          }
        },
        line: 456
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 493,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        },
        loc: {
          start: {
            line: 493,
            column: 34
          },
          end: {
            line: 521,
            column: 5
          }
        },
        line: 493
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 525,
            column: 4
          },
          end: {
            line: 525,
            column: 5
          }
        },
        loc: {
          start: {
            line: 525,
            column: 21
          },
          end: {
            line: 531,
            column: 5
          }
        },
        line: 525
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 535,
            column: 5
          }
        },
        loc: {
          start: {
            line: 535,
            column: 30
          },
          end: {
            line: 544,
            column: 5
          }
        },
        line: 535
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 545,
            column: 4
          },
          end: {
            line: 545,
            column: 5
          }
        },
        loc: {
          start: {
            line: 545,
            column: 29
          },
          end: {
            line: 557,
            column: 5
          }
        },
        line: 545
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 558,
            column: 4
          },
          end: {
            line: 558,
            column: 5
          }
        },
        loc: {
          start: {
            line: 558,
            column: 24
          },
          end: {
            line: 570,
            column: 5
          }
        },
        line: 558
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 571,
            column: 4
          },
          end: {
            line: 571,
            column: 5
          }
        },
        loc: {
          start: {
            line: 571,
            column: 28
          },
          end: {
            line: 577,
            column: 5
          }
        },
        line: 571
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "17": {
        loc: {
          start: {
            line: 85,
            column: 3
          },
          end: {
            line: 85,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 3
          },
          end: {
            line: 85,
            column: 17
          }
        }, {
          start: {
            line: 85,
            column: 22
          },
          end: {
            line: 85,
            column: 66
          }
        }],
        line: 85
      },
      "18": {
        loc: {
          start: {
            line: 93,
            column: 3
          },
          end: {
            line: 93,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 3
          },
          end: {
            line: 93,
            column: 12
          }
        }, {
          start: {
            line: 93,
            column: 17
          },
          end: {
            line: 93,
            column: 51
          }
        }],
        line: 93
      },
      "19": {
        loc: {
          start: {
            line: 211,
            column: 35
          },
          end: {
            line: 211,
            column: 47
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 211,
            column: 45
          },
          end: {
            line: 211,
            column: 47
          }
        }],
        line: 211
      },
      "20": {
        loc: {
          start: {
            line: 215,
            column: 27
          },
          end: {
            line: 215,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 27
          },
          end: {
            line: 215,
            column: 44
          }
        }, {
          start: {
            line: 215,
            column: 48
          },
          end: {
            line: 215,
            column: 82
          }
        }],
        line: 215
      },
      "21": {
        loc: {
          start: {
            line: 219,
            column: 27
          },
          end: {
            line: 219,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 219,
            column: 27
          },
          end: {
            line: 219,
            column: 48
          }
        }, {
          start: {
            line: 219,
            column: 52
          },
          end: {
            line: 219,
            column: 61
          }
        }],
        line: 219
      },
      "22": {
        loc: {
          start: {
            line: 220,
            column: 26
          },
          end: {
            line: 220,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 220,
            column: 26
          },
          end: {
            line: 220,
            column: 41
          }
        }, {
          start: {
            line: 220,
            column: 45
          },
          end: {
            line: 220,
            column: 52
          }
        }],
        line: 220
      },
      "23": {
        loc: {
          start: {
            line: 229,
            column: 25
          },
          end: {
            line: 229,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 229,
            column: 57
          },
          end: {
            line: 229,
            column: 72
          }
        }, {
          start: {
            line: 229,
            column: 75
          },
          end: {
            line: 229,
            column: 79
          }
        }],
        line: 229
      },
      "24": {
        loc: {
          start: {
            line: 232,
            column: 22
          },
          end: {
            line: 232,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 22
          },
          end: {
            line: 232,
            column: 34
          }
        }, {
          start: {
            line: 232,
            column: 38
          },
          end: {
            line: 232,
            column: 40
          }
        }],
        line: 232
      },
      "25": {
        loc: {
          start: {
            line: 258,
            column: 23
          },
          end: {
            line: 258,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 258,
            column: 67
          },
          end: {
            line: 258,
            column: 83
          }
        }, {
          start: {
            line: 258,
            column: 86
          },
          end: {
            line: 258,
            column: 99
          }
        }],
        line: 258
      },
      "26": {
        loc: {
          start: {
            line: 262,
            column: 24
          },
          end: {
            line: 262,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 24
          },
          end: {
            line: 262,
            column: 30
          }
        }, {
          start: {
            line: 262,
            column: 34
          },
          end: {
            line: 262,
            column: 47
          }
        }],
        line: 262
      },
      "27": {
        loc: {
          start: {
            line: 273,
            column: 23
          },
          end: {
            line: 273,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 273,
            column: 67
          },
          end: {
            line: 273,
            column: 81
          }
        }, {
          start: {
            line: 273,
            column: 84
          },
          end: {
            line: 273,
            column: 97
          }
        }],
        line: 273
      },
      "28": {
        loc: {
          start: {
            line: 284,
            column: 38
          },
          end: {
            line: 284,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 284,
            column: 48
          },
          end: {
            line: 284,
            column: 50
          }
        }],
        line: 284
      },
      "29": {
        loc: {
          start: {
            line: 286,
            column: 23
          },
          end: {
            line: 286,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 23
          },
          end: {
            line: 286,
            column: 40
          }
        }, {
          start: {
            line: 286,
            column: 44
          },
          end: {
            line: 286,
            column: 58
          }
        }],
        line: 286
      },
      "30": {
        loc: {
          start: {
            line: 296,
            column: 23
          },
          end: {
            line: 296,
            column: 35
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 296,
            column: 33
          },
          end: {
            line: 296,
            column: 35
          }
        }],
        line: 296
      },
      "31": {
        loc: {
          start: {
            line: 297,
            column: 33
          },
          end: {
            line: 297,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 297,
            column: 33
          },
          end: {
            line: 297,
            column: 45
          }
        }, {
          start: {
            line: 297,
            column: 49
          },
          end: {
            line: 297,
            column: 50
          }
        }],
        line: 297
      },
      "32": {
        loc: {
          start: {
            line: 298,
            column: 48
          },
          end: {
            line: 298,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 48
          },
          end: {
            line: 298,
            column: 61
          }
        }, {
          start: {
            line: 298,
            column: 65
          },
          end: {
            line: 298,
            column: 67
          }
        }],
        line: 298
      },
      "33": {
        loc: {
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 303,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 303,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "34": {
        loc: {
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 305,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 305,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 304
      },
      "35": {
        loc: {
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 307,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 307,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "36": {
        loc: {
          start: {
            line: 308,
            column: 8
          },
          end: {
            line: 309,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 8
          },
          end: {
            line: 309,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 308
      },
      "37": {
        loc: {
          start: {
            line: 310,
            column: 8
          },
          end: {
            line: 311,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 8
          },
          end: {
            line: 311,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      },
      "38": {
        loc: {
          start: {
            line: 312,
            column: 8
          },
          end: {
            line: 318,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 312,
            column: 8
          },
          end: {
            line: 318,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 312
      },
      "39": {
        loc: {
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 312,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 312,
            column: 12
          },
          end: {
            line: 312,
            column: 29
          }
        }, {
          start: {
            line: 312,
            column: 33
          },
          end: {
            line: 312,
            column: 48
          }
        }],
        line: 312
      },
      "40": {
        loc: {
          start: {
            line: 314,
            column: 12
          },
          end: {
            line: 315,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 314,
            column: 12
          },
          end: {
            line: 315,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 314
      },
      "41": {
        loc: {
          start: {
            line: 316,
            column: 12
          },
          end: {
            line: 317,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 316,
            column: 12
          },
          end: {
            line: 317,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 316
      },
      "42": {
        loc: {
          start: {
            line: 338,
            column: 24
          },
          end: {
            line: 338,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 338,
            column: 36
          },
          end: {
            line: 338,
            column: 41
          }
        }],
        line: 338
      },
      "43": {
        loc: {
          start: {
            line: 341,
            column: 8
          },
          end: {
            line: 354,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 344,
            column: 22
          }
        }, {
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 347,
            column: 22
          }
        }, {
          start: {
            line: 348,
            column: 12
          },
          end: {
            line: 350,
            column: 22
          }
        }, {
          start: {
            line: 351,
            column: 12
          },
          end: {
            line: 353,
            column: 22
          }
        }],
        line: 341
      },
      "44": {
        loc: {
          start: {
            line: 398,
            column: 29
          },
          end: {
            line: 398,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 398,
            column: 47
          },
          end: {
            line: 398,
            column: 108
          }
        }, {
          start: {
            line: 398,
            column: 111
          },
          end: {
            line: 398,
            column: 112
          }
        }],
        line: 398
      },
      "45": {
        loc: {
          start: {
            line: 417,
            column: 12
          },
          end: {
            line: 426,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 417,
            column: 12
          },
          end: {
            line: 426,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 417
      },
      "46": {
        loc: {
          start: {
            line: 423,
            column: 16
          },
          end: {
            line: 425,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 423,
            column: 16
          },
          end: {
            line: 425,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 423
      },
      "47": {
        loc: {
          start: {
            line: 428,
            column: 12
          },
          end: {
            line: 437,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 12
          },
          end: {
            line: 437,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "48": {
        loc: {
          start: {
            line: 434,
            column: 16
          },
          end: {
            line: 436,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 434,
            column: 16
          },
          end: {
            line: 436,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 434
      },
      "49": {
        loc: {
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 447,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 447,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 439
      },
      "50": {
        loc: {
          start: {
            line: 444,
            column: 16
          },
          end: {
            line: 446,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 444,
            column: 16
          },
          end: {
            line: 446,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 444
      },
      "51": {
        loc: {
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 515,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 514,
            column: 8
          },
          end: {
            line: 515,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 514
      },
      "52": {
        loc: {
          start: {
            line: 516,
            column: 8
          },
          end: {
            line: 517,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 516,
            column: 8
          },
          end: {
            line: 517,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 516
      },
      "53": {
        loc: {
          start: {
            line: 518,
            column: 8
          },
          end: {
            line: 519,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 518,
            column: 8
          },
          end: {
            line: 519,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 518
      },
      "54": {
        loc: {
          start: {
            line: 526,
            column: 15
          },
          end: {
            line: 530,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 526,
            column: 15
          },
          end: {
            line: 526,
            column: 21
          }
        }, {
          start: {
            line: 527,
            column: 12
          },
          end: {
            line: 527,
            column: 40
          }
        }, {
          start: {
            line: 528,
            column: 12
          },
          end: {
            line: 528,
            column: 36
          }
        }, {
          start: {
            line: 529,
            column: 12
          },
          end: {
            line: 529,
            column: 49
          }
        }, {
          start: {
            line: 530,
            column: 12
          },
          end: {
            line: 530,
            column: 21
          }
        }],
        line: 526
      },
      "55": {
        loc: {
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 538,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 538,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 536
      },
      "56": {
        loc: {
          start: {
            line: 546,
            column: 8
          },
          end: {
            line: 547,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 546,
            column: 8
          },
          end: {
            line: 547,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 546
      },
      "57": {
        loc: {
          start: {
            line: 548,
            column: 8
          },
          end: {
            line: 549,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 548,
            column: 8
          },
          end: {
            line: 549,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 548
      },
      "58": {
        loc: {
          start: {
            line: 550,
            column: 8
          },
          end: {
            line: 551,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 550,
            column: 8
          },
          end: {
            line: 551,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 550
      },
      "59": {
        loc: {
          start: {
            line: 552,
            column: 8
          },
          end: {
            line: 553,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 552,
            column: 8
          },
          end: {
            line: 553,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 552
      },
      "60": {
        loc: {
          start: {
            line: 554,
            column: 8
          },
          end: {
            line: 555,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 554,
            column: 8
          },
          end: {
            line: 555,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 554
      },
      "61": {
        loc: {
          start: {
            line: 559,
            column: 8
          },
          end: {
            line: 560,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 559,
            column: 8
          },
          end: {
            line: 560,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 559
      },
      "62": {
        loc: {
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 562,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 561,
            column: 8
          },
          end: {
            line: 562,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 561
      },
      "63": {
        loc: {
          start: {
            line: 563,
            column: 8
          },
          end: {
            line: 564,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 563,
            column: 8
          },
          end: {
            line: 564,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 563
      },
      "64": {
        loc: {
          start: {
            line: 565,
            column: 8
          },
          end: {
            line: 566,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 565,
            column: 8
          },
          end: {
            line: 566,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 565
      },
      "65": {
        loc: {
          start: {
            line: 567,
            column: 8
          },
          end: {
            line: 568,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 567,
            column: 8
          },
          end: {
            line: 568,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 567
      },
      "66": {
        loc: {
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 573,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 572,
            column: 8
          },
          end: {
            line: 573,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 572
      },
      "67": {
        loc: {
          start: {
            line: 574,
            column: 8
          },
          end: {
            line: 575,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 574,
            column: 8
          },
          end: {
            line: 575,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 574
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0],
      "29": [0, 0],
      "30": [0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0],
      "43": [0, 0, 0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0, 0, 0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\auditService.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,qDAAsD;AACtD,4CAAyC;AAGzC,oBAAoB;AACpB,IAAY,cAiDX;AAjDD,WAAY,cAAc;IACxB,wBAAwB;IACxB,iDAA+B,CAAA;IAC/B,+CAA6B,CAAA;IAC7B,mCAAiB,CAAA;IACjB,mEAAiD,CAAA;IACjD,mEAAiD,CAAA;IACjD,uDAAqC,CAAA;IACrC,2DAAyC,CAAA;IACzC,mDAAiC,CAAA;IACjC,uDAAqC,CAAA;IAErC,uBAAuB;IACvB,mDAAiC,CAAA;IACjC,iDAA+B,CAAA;IAC/B,iEAA+C,CAAA;IAC/C,+CAA6B,CAAA;IAE7B,cAAc;IACd,+CAA6B,CAAA;IAC7B,+CAA6B,CAAA;IAC7B,+CAA6B,CAAA;IAC7B,6CAA2B,CAAA;IAC3B,iDAA+B,CAAA;IAC/B,mDAAiC,CAAA;IAEjC,kBAAkB;IAClB,6DAA2C,CAAA;IAC3C,6DAA2C,CAAA;IAC3C,iDAA+B,CAAA;IAC/B,iEAA+C,CAAA;IAC/C,6CAA2B,CAAA;IAC3B,+CAA6B,CAAA;IAC7B,6DAA2C,CAAA;IAE3C,gBAAgB;IAChB,+CAA6B,CAAA;IAC7B,iEAA+C,CAAA;IAC/C,mDAAiC,CAAA;IACjC,uDAAqC,CAAA;IAErC,kBAAkB;IAClB,qDAAmC,CAAA;IACnC,uDAAqC,CAAA;IACrC,uDAAqC,CAAA;IACrC,iDAA+B,CAAA;IAC/B,+CAA6B,CAAA;IAC7B,yDAAuC,CAAA;IACvC,+DAA6C,CAAA;AAC/C,CAAC,EAjDW,cAAc,8BAAd,cAAc,QAiDzB;AAED,cAAc;AACd,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,wBAAW,CAAA;IACX,8BAAiB,CAAA;IACjB,0BAAa,CAAA;IACb,kCAAqB,CAAA;AACvB,CAAC,EALW,SAAS,yBAAT,SAAS,QAKpB;AAwCD,mBAAmB;AACnB,MAAM,cAAc,GAAG,IAAI,iBAAM,CAAY;IAC3C,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;QACnC,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;QAC9B,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,MAAM,EAAE;QACN,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,MAAM,EAAE;QACN,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;KACf;IACD,UAAU,EAAE;QACV,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,UAAU,EAAE;QACV,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;KACZ;IACD,SAAS,EAAE;QACT,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,KAAK;KACzB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,KAAK;KACzB;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,KAAK;KACzB;IACD,WAAW,EAAE;QACX,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE;YACX,QAAQ,EAAE,MAAM;YAChB,SAAS,EAAE,MAAM;SAClB;KACF;IACD,UAAU,EAAE;QACV,OAAO,EAAE,MAAM;QACf,EAAE,EAAE,MAAM;QACV,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,OAAO;KAClB;IACD,OAAO,EAAE;QACP,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,YAAY,EAAE,MAAM;IACpB,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE;QACT,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;QACjB,KAAK,EAAE,IAAI;KACZ;IACD,IAAI,EAAE,CAAC;YACL,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,IAAI;SACZ,CAAC;CACH,EAAE;IACD,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,YAAY;CACzB,CAAC,CAAC;AAEH,sCAAsC;AACtC,cAAc,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnD,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACtD,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACtD,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACtD,cAAc,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAEpD,yDAAyD;AACzD,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;AAEtE,QAAA,QAAQ,GAAG,kBAAQ,CAAC,KAAK,CAAY,UAAU,EAAE,cAAc,CAAC,CAAC;AAE9E,MAAM,YAAY;IAAlB;QACU,iCAA4B,GAAG;YACrC,YAAY,EAAE,CAAC,EAAE,iBAAiB;YAClC,iBAAiB,EAAE,EAAE,EAAE,WAAW;YAClC,aAAa,EAAE,EAAE,EAAE,WAAW;YAC9B,YAAY,EAAE,CAAC,CAAC,yBAAyB;SAC1C,CAAC;IA6bJ,CAAC;IA3bC;;OAEG;IACH,KAAK,CAAC,QAAQ,CACZ,SAAyB,EACzB,GAAY,EACZ,UAWI,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,SAAS,GAAuB;gBACpC,SAAS;gBACT,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;gBAClE,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG;gBACrB,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,EAAE;gBAC1B,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;gBAChC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;gBAC7C,QAAQ,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;gBACpC,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU;gBAC/B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBACtD,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;gBAC/D,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,yBAAyB;YACzB,MAAM,QAAQ,GAAG,IAAI,gBAAQ,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtB,oCAAoC;YACpC,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAChC,SAAS;gBACT,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,SAA6F,EAC7F,GAAY,EACZ,MAAe,EACf,YAAqB;QAErB,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YAClC,SAAS,EAAE,SAAS,KAAK,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG;YACvF,OAAO,EAAE,SAAS,KAAK,cAAc,CAAC,YAAY;YAClD,YAAY;YACZ,QAAQ,EAAE;gBACR,MAAM,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG;gBAC/B,WAAW,EAAE,gBAAgB,CAAC,oCAAoC;aACnE;YACD,IAAI,EAAE,CAAC,gBAAgB,CAAC;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,SAA+H,EAC/H,GAAY,EACZ,QAAgB,EAChB,UAAkB,EAClB,SAA+B,EAC/B,SAA+B;QAE/B,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YAClC,SAAS,EAAE,SAAS,KAAK,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG;YACrF,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;YACT,IAAI,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,SAAyB,EACzB,GAAY,EACZ,UAII,EAAE;QAEN,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YAClC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI;YAC9C,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;SAC7B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,UAUf,EAAE;QAMJ,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,cAAc;QACd,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,OAAO,CAAC,MAAM;YAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAClD,IAAI,OAAO,CAAC,SAAS;YAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAC3D,IAAI,OAAO,CAAC,SAAS;YAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAC3D,IAAI,OAAO,CAAC,SAAS;YAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAC3D,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS;YAAE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEnE,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACzC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,OAAO,CAAC,SAAS;gBAAE,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;YAChE,IAAI,OAAO,CAAC,OAAO;gBAAE,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9D,CAAC;QAED,kBAAkB;QAClB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtC,gBAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;iBACjB,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;iBACvB,IAAI,CAAC,IAAI,CAAC;iBACV,KAAK,CAAC,KAAK,CAAC;iBACZ,IAAI,EAAE;YACT,gBAAQ,CAAC,cAAc,CAAC,KAAK,CAAC;SAC/B,CAAC,CAAC;QAEH,OAAO;YACL,IAAI;YACJ,KAAK;YACL,IAAI;YACJ,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,YAA+C,KAAK;QACtE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,SAAe,CAAC;QAEpB,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,KAAK;gBACR,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,MAAM;gBACT,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,OAAO;gBACV,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC/D,MAAM;QACV,CAAC;QAED,MAAM,CACJ,WAAW,EACX,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,QAAQ,CACT,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,gBAAQ,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;YAC3D,gBAAQ,CAAC,cAAc,CAAC;gBACtB,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC9B,IAAI,EAAE,UAAU;aACjB,CAAC;YACF,gBAAQ,CAAC,cAAc,CAAC;gBACtB,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC9B,OAAO,EAAE,KAAK;aACf,CAAC;YACF,gBAAQ,CAAC,SAAS,CAAC;gBACjB,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC9C,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;gBACrD,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;gBACxB,EAAE,MAAM,EAAE,EAAE,EAAE;aACf,CAAC;YACF,gBAAQ,CAAC,SAAS,CAAC;gBACjB,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC9C,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;aACtD,CAAC;YACF,gBAAQ,CAAC,SAAS,CAAC;gBACjB,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC9C,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;gBACrD,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;gBACxB,EAAE,MAAM,EAAE,EAAE,EAAE;aACf,CAAC;YACF,gBAAQ,CAAC,SAAS,CAAC;gBACjB,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBACzE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;gBAClD,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;gBACxB,EAAE,MAAM,EAAE,EAAE,EAAE;aACf,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,SAAS;YACT,MAAM,EAAE;gBACN,KAAK,EAAE,SAAS;gBAChB,GAAG,EAAE,GAAG;aACT;YACD,OAAO,EAAE;gBACP,WAAW;gBACX,cAAc;gBACd,YAAY;gBACZ,WAAW,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjG;YACD,SAAS,EAAE;gBACT,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,YAAY;gBACpB,MAAM;gBACN,QAAQ;aACT;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,SAA6B;QACjE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACzD,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEhE,IAAI,CAAC;YACH,kCAAkC;YAClC,IAAI,SAAS,CAAC,SAAS,KAAK,cAAc,CAAC,YAAY,EAAE,CAAC;gBACxD,MAAM,cAAc,GAAG,MAAM,gBAAQ,CAAC,cAAc,CAAC;oBACnD,SAAS,EAAE,cAAc,CAAC,YAAY;oBACtC,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,SAAS,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;iBACpC,CAAC,CAAC;gBAEH,IAAI,cAAc,IAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,CAAC;oBACrE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YAED,uCAAuC;YACvC,IAAI,SAAS,CAAC,SAAS,KAAK,cAAc,CAAC,mBAAmB,EAAE,CAAC;gBAC/D,MAAM,gBAAgB,GAAG,MAAM,gBAAQ,CAAC,cAAc,CAAC;oBACrD,SAAS,EAAE,cAAc,CAAC,mBAAmB;oBAC7C,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;iBAC7B,CAAC,CAAC;gBAEH,IAAI,gBAAgB,IAAI,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,EAAE,CAAC;oBAC5E,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;YAED,uCAAuC;YACvC,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrB,MAAM,WAAW,GAAG,MAAM,gBAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE;oBACvD,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;iBAC7B,CAAC,CAAC;gBAEH,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,CAAC;oBACzE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,aAAiC;QACnF,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,gBAAQ,CAAC;gBACjC,SAAS,EAAE,cAAc,CAAC,mBAAmB;gBAC7C,SAAS,EAAE,SAAS,CAAC,QAAQ;gBAC7B,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,MAAM;gBACpB,QAAQ,EAAE;oBACR,aAAa,EAAE,aAAa,CAAC,SAAS;oBACtC,eAAe,EAAE,MAAM;oBACvB,SAAS,EAAE,aAAa,CAAC,SAAS;iBACnC;gBACD,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,qBAAqB,CAAC;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;YAE3B,oDAAoD;YACpD,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,MAAM;gBACN,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,aAAa,EAAE,aAAa,CAAC,SAAS;aACvC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAyB;QAClD,MAAM,cAAc,GAAG;YACrB,cAAc,CAAC,YAAY;YAC3B,cAAc,CAAC,qBAAqB;YACpC,cAAc,CAAC,YAAY;YAC3B,cAAc,CAAC,qBAAqB;YACpC,cAAc,CAAC,cAAc;SAC9B,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACvB,cAAc,CAAC,YAAY;YAC3B,cAAc,CAAC,aAAa;YAC5B,cAAc,CAAC,sBAAsB;YACrC,cAAc,CAAC,YAAY;YAC3B,cAAc,CAAC,mBAAmB;SACnC,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,cAAc,CAAC,mBAAmB;YAClC,cAAc,CAAC,qBAAqB;YACpC,cAAc,CAAC,WAAW;YAC1B,cAAc,CAAC,mBAAmB;SACnC,CAAC;QAEF,IAAI,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC,QAAQ,CAAC;QACtE,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC,IAAI,CAAC;QAC9D,IAAI,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC,MAAM,CAAC;QAClE,OAAO,SAAS,CAAC,GAAG,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,GAAY;QAC9B,OAAO,GAAG,CAAC,EAAE;YACN,GAAG,CAAC,UAAU,CAAC,aAAa;YAC5B,GAAG,CAAC,MAAM,CAAC,aAAa;YACvB,GAAG,CAAC,UAAkB,EAAE,MAAM,EAAE,aAAa;YAC9C,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,SAAkB;QACvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACnF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC9C,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE9D,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC3C,CAAC;IAEO,aAAa,CAAC,SAAiB;QACrC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9C,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAChD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,QAAQ,CAAC,SAAiB;QAChC,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,OAAO,CAAC;QACjD,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAChD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC5C,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,SAAiB;QACpC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAED,uCAAuC;AAC1B,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAE/C,kBAAe,oBAAY,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\auditService.ts"],
      sourcesContent: ["import { Request } from 'express';\r\nimport mongoose, { Schema, Document } from 'mongoose';\r\nimport { logger } from '../utils/logger';\r\nimport { cacheService } from './cacheService';\r\n\r\n// Audit event types\r\nexport enum AuditEventType {\r\n  // Authentication events\r\n  LOGIN_SUCCESS = 'login_success',\r\n  LOGIN_FAILED = 'login_failed',\r\n  LOGOUT = 'logout',\r\n  PASSWORD_RESET_REQUEST = 'password_reset_request',\r\n  PASSWORD_RESET_SUCCESS = 'password_reset_success',\r\n  PASSWORD_CHANGED = 'password_changed',\r\n  EMAIL_VERIFICATION = 'email_verification',\r\n  ACCOUNT_LOCKED = 'account_locked',\r\n  ACCOUNT_UNLOCKED = 'account_unlocked',\r\n\r\n  // Authorization events\r\n  ACCESS_GRANTED = 'access_granted',\r\n  ACCESS_DENIED = 'access_denied',\r\n  PERMISSION_ESCALATION = 'permission_escalation',\r\n  ROLE_CHANGED = 'role_changed',\r\n\r\n  // Data events\r\n  DATA_CREATED = 'data_created',\r\n  DATA_UPDATED = 'data_updated',\r\n  DATA_DELETED = 'data_deleted',\r\n  DATA_VIEWED = 'data_viewed',\r\n  DATA_EXPORTED = 'data_exported',\r\n  BULK_OPERATION = 'bulk_operation',\r\n\r\n  // Security events\r\n  SUSPICIOUS_ACTIVITY = 'suspicious_activity',\r\n  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',\r\n  INVALID_INPUT = 'invalid_input',\r\n  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',\r\n  XSS_ATTEMPT = 'xss_attempt',\r\n  CSRF_ATTEMPT = 'csrf_attempt',\r\n  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',\r\n\r\n  // System events\r\n  SYSTEM_ERROR = 'system_error',\r\n  CONFIGURATION_CHANGED = 'configuration_changed',\r\n  BACKUP_CREATED = 'backup_created',\r\n  MAINTENANCE_MODE = 'maintenance_mode',\r\n\r\n  // Business events\r\n  PROPERTY_POSTED = 'property_posted',\r\n  PROPERTY_UPDATED = 'property_updated',\r\n  PROPERTY_DELETED = 'property_deleted',\r\n  MATCH_CREATED = 'match_created',\r\n  MESSAGE_SENT = 'message_sent',\r\n  PAYMENT_PROCESSED = 'payment_processed',\r\n  SUBSCRIPTION_CHANGED = 'subscription_changed'\r\n}\r\n\r\n// Risk levels\r\nexport enum RiskLevel {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n// Audit log interface\r\nexport interface IAuditLog extends Document {\r\n  eventType: AuditEventType;\r\n  riskLevel: RiskLevel;\r\n  userId?: string;\r\n  sessionId?: string;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  endpoint: string;\r\n  method: string;\r\n  statusCode?: number;\r\n  resource?: string;\r\n  resourceId?: string;\r\n  oldValues?: Record<string, any>;\r\n  newValues?: Record<string, any>;\r\n  metadata?: Record<string, any>;\r\n  geolocation?: {\r\n    country?: string;\r\n    region?: string;\r\n    city?: string;\r\n    coordinates?: {\r\n      latitude: number;\r\n      longitude: number;\r\n    };\r\n  };\r\n  deviceInfo?: {\r\n    browser: string;\r\n    os: string;\r\n    device: string;\r\n    isMobile: boolean;\r\n  };\r\n  success: boolean;\r\n  errorMessage?: string;\r\n  duration?: number;\r\n  timestamp: Date;\r\n  tags?: string[];\r\n}\r\n\r\n// Audit log schema\r\nconst auditLogSchema = new Schema<IAuditLog>({\r\n  eventType: {\r\n    type: String,\r\n    enum: Object.values(AuditEventType),\r\n    required: true,\r\n    index: true\r\n  },\r\n  riskLevel: {\r\n    type: String,\r\n    enum: Object.values(RiskLevel),\r\n    required: true,\r\n    index: true\r\n  },\r\n  userId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  sessionId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  ipAddress: {\r\n    type: String,\r\n    required: true,\r\n    index: true\r\n  },\r\n  userAgent: {\r\n    type: String,\r\n    required: true\r\n  },\r\n  endpoint: {\r\n    type: String,\r\n    required: true,\r\n    index: true\r\n  },\r\n  method: {\r\n    type: String,\r\n    required: true\r\n  },\r\n  statusCode: {\r\n    type: Number,\r\n    index: true\r\n  },\r\n  resource: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  resourceId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  oldValues: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  newValues: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  metadata: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  geolocation: {\r\n    country: String,\r\n    region: String,\r\n    city: String,\r\n    coordinates: {\r\n      latitude: Number,\r\n      longitude: Number\r\n    }\r\n  },\r\n  deviceInfo: {\r\n    browser: String,\r\n    os: String,\r\n    device: String,\r\n    isMobile: Boolean\r\n  },\r\n  success: {\r\n    type: Boolean,\r\n    required: true,\r\n    index: true\r\n  },\r\n  errorMessage: String,\r\n  duration: Number,\r\n  timestamp: {\r\n    type: Date,\r\n    default: Date.now,\r\n    index: true\r\n  },\r\n  tags: [{\r\n    type: String,\r\n    index: true\r\n  }]\r\n}, {\r\n  timestamps: true,\r\n  collection: 'audit_logs'\r\n});\r\n\r\n// Compound indexes for common queries\r\nauditLogSchema.index({ userId: 1, timestamp: -1 });\r\nauditLogSchema.index({ eventType: 1, timestamp: -1 });\r\nauditLogSchema.index({ riskLevel: 1, timestamp: -1 });\r\nauditLogSchema.index({ ipAddress: 1, timestamp: -1 });\r\nauditLogSchema.index({ success: 1, timestamp: -1 });\r\n\r\n// TTL index for automatic cleanup (keep logs for 1 year)\r\nauditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });\r\n\r\nexport const AuditLog = mongoose.model<IAuditLog>('AuditLog', auditLogSchema);\r\n\r\nclass AuditService {\r\n  private suspiciousActivityThresholds = {\r\n    failedLogins: 5, // per 15 minutes\r\n    rateLimitExceeded: 10, // per hour\r\n    invalidInputs: 20, // per hour\r\n    differentIPs: 3 // per hour for same user\r\n  };\r\n\r\n  /**\r\n   * Log an audit event\r\n   */\r\n  async logEvent(\r\n    eventType: AuditEventType,\r\n    req: Request,\r\n    options: {\r\n      riskLevel?: RiskLevel;\r\n      resource?: string;\r\n      resourceId?: string;\r\n      oldValues?: Record<string, any>;\r\n      newValues?: Record<string, any>;\r\n      metadata?: Record<string, any>;\r\n      success?: boolean;\r\n      errorMessage?: string;\r\n      duration?: number;\r\n      tags?: string[];\r\n    } = {}\r\n  ): Promise<void> {\r\n    try {\r\n      const auditData: Partial<IAuditLog> = {\r\n        eventType,\r\n        riskLevel: options.riskLevel || this.determineRiskLevel(eventType),\r\n        userId: req.user?._id,\r\n        sessionId: req.session?.id,\r\n        ipAddress: this.getClientIP(req),\r\n        userAgent: req.get('User-Agent') || 'unknown',\r\n        endpoint: req.originalUrl || req.url,\r\n        method: req.method,\r\n        statusCode: req.res?.statusCode,\r\n        resource: options.resource,\r\n        resourceId: options.resourceId,\r\n        oldValues: options.oldValues,\r\n        newValues: options.newValues,\r\n        metadata: options.metadata,\r\n        deviceInfo: this.parseUserAgent(req.get('User-Agent')),\r\n        success: options.success !== undefined ? options.success : true,\r\n        errorMessage: options.errorMessage,\r\n        duration: options.duration,\r\n        tags: options.tags || [],\r\n        timestamp: new Date()\r\n      };\r\n\r\n      // Create audit log entry\r\n      const auditLog = new AuditLog(auditData);\r\n      await auditLog.save();\r\n\r\n      // Log to application logger as well\r\n      logger.info('Audit event logged', {\r\n        eventType,\r\n        userId: auditData.userId,\r\n        ipAddress: auditData.ipAddress,\r\n        endpoint: auditData.endpoint,\r\n        success: auditData.success\r\n      });\r\n\r\n      // Check for suspicious activity\r\n      await this.checkSuspiciousActivity(auditData);\r\n\r\n    } catch (error) {\r\n      logger.error('Failed to log audit event:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log authentication events\r\n   */\r\n  async logAuth(\r\n    eventType: AuditEventType.LOGIN_SUCCESS | AuditEventType.LOGIN_FAILED | AuditEventType.LOGOUT,\r\n    req: Request,\r\n    userId?: string,\r\n    errorMessage?: string\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: eventType === AuditEventType.LOGIN_FAILED ? RiskLevel.MEDIUM : RiskLevel.LOW,\r\n      success: eventType !== AuditEventType.LOGIN_FAILED,\r\n      errorMessage,\r\n      metadata: {\r\n        userId: userId || req.user?._id,\r\n        loginMethod: 'email_password' // Could be extended for OAuth, etc.\r\n      },\r\n      tags: ['authentication']\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Log data access events\r\n   */\r\n  async logDataAccess(\r\n    eventType: AuditEventType.DATA_VIEWED | AuditEventType.DATA_CREATED | AuditEventType.DATA_UPDATED | AuditEventType.DATA_DELETED,\r\n    req: Request,\r\n    resource: string,\r\n    resourceId: string,\r\n    oldValues?: Record<string, any>,\r\n    newValues?: Record<string, any>\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: eventType === AuditEventType.DATA_DELETED ? RiskLevel.HIGH : RiskLevel.LOW,\r\n      resource,\r\n      resourceId,\r\n      oldValues,\r\n      newValues,\r\n      tags: ['data_access', resource]\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Log security events\r\n   */\r\n  async logSecurity(\r\n    eventType: AuditEventType,\r\n    req: Request,\r\n    details: {\r\n      riskLevel?: RiskLevel;\r\n      errorMessage?: string;\r\n      metadata?: Record<string, any>;\r\n    } = {}\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: details.riskLevel || RiskLevel.HIGH,\r\n      success: false,\r\n      errorMessage: details.errorMessage,\r\n      metadata: details.metadata,\r\n      tags: ['security', 'threat']\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get audit logs with filtering and pagination\r\n   */\r\n  async getAuditLogs(filters: {\r\n    userId?: string;\r\n    eventType?: AuditEventType;\r\n    riskLevel?: RiskLevel;\r\n    startDate?: Date;\r\n    endDate?: Date;\r\n    ipAddress?: string;\r\n    success?: boolean;\r\n    page?: number;\r\n    limit?: number;\r\n  } = {}): Promise<{\r\n    logs: IAuditLog[];\r\n    total: number;\r\n    page: number;\r\n    pages: number;\r\n  }> {\r\n    const page = Math.max(1, filters.page || 1);\r\n    const limit = Math.min(100, Math.max(1, filters.limit || 20));\r\n    const skip = (page - 1) * limit;\r\n\r\n    // Build query\r\n    const query: any = {};\r\n    \r\n    if (filters.userId) query.userId = filters.userId;\r\n    if (filters.eventType) query.eventType = filters.eventType;\r\n    if (filters.riskLevel) query.riskLevel = filters.riskLevel;\r\n    if (filters.ipAddress) query.ipAddress = filters.ipAddress;\r\n    if (filters.success !== undefined) query.success = filters.success;\r\n    \r\n    if (filters.startDate || filters.endDate) {\r\n      query.timestamp = {};\r\n      if (filters.startDate) query.timestamp.$gte = filters.startDate;\r\n      if (filters.endDate) query.timestamp.$lte = filters.endDate;\r\n    }\r\n\r\n    // Execute queries\r\n    const [logs, total] = await Promise.all([\r\n      AuditLog.find(query)\r\n        .sort({ timestamp: -1 })\r\n        .skip(skip)\r\n        .limit(limit)\r\n        .lean(),\r\n      AuditLog.countDocuments(query)\r\n    ]);\r\n\r\n    return {\r\n      logs,\r\n      total,\r\n      page,\r\n      pages: Math.ceil(total / limit)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get audit statistics\r\n   */\r\n  async getAuditStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<any> {\r\n    const now = new Date();\r\n    let startDate: Date;\r\n\r\n    switch (timeframe) {\r\n      case 'hour':\r\n        startDate = new Date(now.getTime() - 60 * 60 * 1000);\r\n        break;\r\n      case 'day':\r\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'week':\r\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'month':\r\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\r\n        break;\r\n    }\r\n\r\n    const [\r\n      totalEvents,\r\n      securityEvents,\r\n      failedEvents,\r\n      eventsByType,\r\n      eventsByRisk,\r\n      topIPs,\r\n      topUsers\r\n    ] = await Promise.all([\r\n      AuditLog.countDocuments({ timestamp: { $gte: startDate } }),\r\n      AuditLog.countDocuments({ \r\n        timestamp: { $gte: startDate },\r\n        tags: 'security'\r\n      }),\r\n      AuditLog.countDocuments({ \r\n        timestamp: { $gte: startDate },\r\n        success: false\r\n      }),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$eventType', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$riskLevel', count: { $sum: 1 } } }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$ipAddress', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate }, userId: { $exists: true } } },\r\n        { $group: { _id: '$userId', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ])\r\n    ]);\r\n\r\n    return {\r\n      timeframe,\r\n      period: {\r\n        start: startDate,\r\n        end: now\r\n      },\r\n      summary: {\r\n        totalEvents,\r\n        securityEvents,\r\n        failedEvents,\r\n        successRate: totalEvents > 0 ? ((totalEvents - failedEvents) / totalEvents * 100).toFixed(2) : 0\r\n      },\r\n      breakdown: {\r\n        byType: eventsByType,\r\n        byRisk: eventsByRisk,\r\n        topIPs,\r\n        topUsers\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check for suspicious activity patterns\r\n   */\r\n  private async checkSuspiciousActivity(auditData: Partial<IAuditLog>): Promise<void> {\r\n    const now = new Date();\r\n    const oneHour = new Date(now.getTime() - 60 * 60 * 1000);\r\n    const fifteenMinutes = new Date(now.getTime() - 15 * 60 * 1000);\r\n\r\n    try {\r\n      // Check for failed login attempts\r\n      if (auditData.eventType === AuditEventType.LOGIN_FAILED) {\r\n        const recentFailures = await AuditLog.countDocuments({\r\n          eventType: AuditEventType.LOGIN_FAILED,\r\n          ipAddress: auditData.ipAddress,\r\n          timestamp: { $gte: fifteenMinutes }\r\n        });\r\n\r\n        if (recentFailures >= this.suspiciousActivityThresholds.failedLogins) {\r\n          await this.logSuspiciousActivity('Multiple failed login attempts', auditData);\r\n        }\r\n      }\r\n\r\n      // Check for rate limit exceeded events\r\n      if (auditData.eventType === AuditEventType.RATE_LIMIT_EXCEEDED) {\r\n        const recentRateLimits = await AuditLog.countDocuments({\r\n          eventType: AuditEventType.RATE_LIMIT_EXCEEDED,\r\n          ipAddress: auditData.ipAddress,\r\n          timestamp: { $gte: oneHour }\r\n        });\r\n\r\n        if (recentRateLimits >= this.suspiciousActivityThresholds.rateLimitExceeded) {\r\n          await this.logSuspiciousActivity('Excessive rate limiting', auditData);\r\n        }\r\n      }\r\n\r\n      // Check for multiple IPs for same user\r\n      if (auditData.userId) {\r\n        const distinctIPs = await AuditLog.distinct('ipAddress', {\r\n          userId: auditData.userId,\r\n          timestamp: { $gte: oneHour }\r\n        });\r\n\r\n        if (distinctIPs.length >= this.suspiciousActivityThresholds.differentIPs) {\r\n          await this.logSuspiciousActivity('Multiple IP addresses for user', auditData);\r\n        }\r\n      }\r\n\r\n    } catch (error) {\r\n      logger.error('Error checking suspicious activity:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log suspicious activity\r\n   */\r\n  private async logSuspiciousActivity(reason: string, originalEvent: Partial<IAuditLog>): Promise<void> {\r\n    try {\r\n      const suspiciousLog = new AuditLog({\r\n        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,\r\n        riskLevel: RiskLevel.CRITICAL,\r\n        userId: originalEvent.userId,\r\n        sessionId: originalEvent.sessionId,\r\n        ipAddress: originalEvent.ipAddress,\r\n        userAgent: originalEvent.userAgent,\r\n        endpoint: originalEvent.endpoint,\r\n        method: originalEvent.method,\r\n        success: false,\r\n        errorMessage: reason,\r\n        metadata: {\r\n          originalEvent: originalEvent.eventType,\r\n          detectionReason: reason,\r\n          timestamp: originalEvent.timestamp\r\n        },\r\n        tags: ['suspicious', 'security', 'automated_detection'],\r\n        timestamp: new Date()\r\n      });\r\n\r\n      await suspiciousLog.save();\r\n\r\n      // Also log to application logger with high priority\r\n      logger.warn('Suspicious activity detected', {\r\n        reason,\r\n        userId: originalEvent.userId,\r\n        ipAddress: originalEvent.ipAddress,\r\n        originalEvent: originalEvent.eventType\r\n      });\r\n\r\n    } catch (error) {\r\n      logger.error('Failed to log suspicious activity:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine risk level based on event type\r\n   */\r\n  private determineRiskLevel(eventType: AuditEventType): RiskLevel {\r\n    const highRiskEvents = [\r\n      AuditEventType.DATA_DELETED,\r\n      AuditEventType.PERMISSION_ESCALATION,\r\n      AuditEventType.ROLE_CHANGED,\r\n      AuditEventType.CONFIGURATION_CHANGED,\r\n      AuditEventType.ACCOUNT_LOCKED\r\n    ];\r\n\r\n    const mediumRiskEvents = [\r\n      AuditEventType.LOGIN_FAILED,\r\n      AuditEventType.ACCESS_DENIED,\r\n      AuditEventType.PASSWORD_RESET_REQUEST,\r\n      AuditEventType.DATA_UPDATED,\r\n      AuditEventType.RATE_LIMIT_EXCEEDED\r\n    ];\r\n\r\n    const criticalRiskEvents = [\r\n      AuditEventType.SUSPICIOUS_ACTIVITY,\r\n      AuditEventType.SQL_INJECTION_ATTEMPT,\r\n      AuditEventType.XSS_ATTEMPT,\r\n      AuditEventType.BRUTE_FORCE_ATTEMPT\r\n    ];\r\n\r\n    if (criticalRiskEvents.includes(eventType)) return RiskLevel.CRITICAL;\r\n    if (highRiskEvents.includes(eventType)) return RiskLevel.HIGH;\r\n    if (mediumRiskEvents.includes(eventType)) return RiskLevel.MEDIUM;\r\n    return RiskLevel.LOW;\r\n  }\r\n\r\n  /**\r\n   * Get client IP address\r\n   */\r\n  private getClientIP(req: Request): string {\r\n    return req.ip || \r\n           req.connection.remoteAddress || \r\n           req.socket.remoteAddress || \r\n           (req.connection as any)?.socket?.remoteAddress || \r\n           'unknown';\r\n  }\r\n\r\n  /**\r\n   * Parse user agent for device info\r\n   */\r\n  private parseUserAgent(userAgent?: string): any {\r\n    if (!userAgent) {\r\n      return { browser: 'unknown', os: 'unknown', device: 'unknown', isMobile: false };\r\n    }\r\n\r\n    const browser = this.detectBrowser(userAgent);\r\n    const os = this.detectOS(userAgent);\r\n    const device = this.detectDevice(userAgent);\r\n    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);\r\n\r\n    return { browser, os, device, isMobile };\r\n  }\r\n\r\n  private detectBrowser(userAgent: string): string {\r\n    if (userAgent.includes('Chrome')) return 'Chrome';\r\n    if (userAgent.includes('Firefox')) return 'Firefox';\r\n    if (userAgent.includes('Safari')) return 'Safari';\r\n    if (userAgent.includes('Edge')) return 'Edge';\r\n    if (userAgent.includes('Opera')) return 'Opera';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectOS(userAgent: string): string {\r\n    if (userAgent.includes('Windows')) return 'Windows';\r\n    if (userAgent.includes('Mac OS')) return 'macOS';\r\n    if (userAgent.includes('Linux')) return 'Linux';\r\n    if (userAgent.includes('Android')) return 'Android';\r\n    if (userAgent.includes('iOS')) return 'iOS';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectDevice(userAgent: string): string {\r\n    if (userAgent.includes('Mobile')) return 'mobile';\r\n    if (userAgent.includes('Tablet')) return 'tablet';\r\n    return 'desktop';\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const auditService = new AuditService();\r\n\r\nexport default auditService;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "aaee55d97ee762983f94fdf79c4186aee3316be3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_3y1ppby04 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_3y1ppby04();
var __createBinding =
/* istanbul ignore next */
(cov_3y1ppby04().s[0]++,
/* istanbul ignore next */
(cov_3y1ppby04().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_3y1ppby04().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_3y1ppby04().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_3y1ppby04().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[0]++;
  cov_3y1ppby04().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_3y1ppby04().b[2][0]++;
    cov_3y1ppby04().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_3y1ppby04().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_3y1ppby04().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_3y1ppby04().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_3y1ppby04().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_3y1ppby04().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_3y1ppby04().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_3y1ppby04().b[5][1]++,
  /* istanbul ignore next */
  (cov_3y1ppby04().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_3y1ppby04().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_3y1ppby04().b[3][0]++;
    cov_3y1ppby04().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_3y1ppby04().f[1]++;
        cov_3y1ppby04().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_3y1ppby04().b[3][1]++;
  }
  cov_3y1ppby04().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_3y1ppby04().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[2]++;
  cov_3y1ppby04().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_3y1ppby04().b[7][0]++;
    cov_3y1ppby04().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_3y1ppby04().b[7][1]++;
  }
  cov_3y1ppby04().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_3y1ppby04().s[11]++,
/* istanbul ignore next */
(cov_3y1ppby04().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_3y1ppby04().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_3y1ppby04().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_3y1ppby04().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[3]++;
  cov_3y1ppby04().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_3y1ppby04().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[4]++;
  cov_3y1ppby04().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_3y1ppby04().s[14]++,
/* istanbul ignore next */
(cov_3y1ppby04().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_3y1ppby04().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_3y1ppby04().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_3y1ppby04().f[5]++;
  cov_3y1ppby04().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[6]++;
    cov_3y1ppby04().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_3y1ppby04().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_3y1ppby04().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_3y1ppby04().s[17]++, []);
      /* istanbul ignore next */
      cov_3y1ppby04().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_3y1ppby04().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_3y1ppby04().b[12][0]++;
          cov_3y1ppby04().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_3y1ppby04().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_3y1ppby04().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_3y1ppby04().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_3y1ppby04().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[8]++;
    cov_3y1ppby04().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_3y1ppby04().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_3y1ppby04().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[13][0]++;
      cov_3y1ppby04().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[26]++, {});
    /* istanbul ignore next */
    cov_3y1ppby04().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[15][0]++;
      cov_3y1ppby04().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_3y1ppby04().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_3y1ppby04().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_3y1ppby04().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_3y1ppby04().b[16][0]++;
          cov_3y1ppby04().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_3y1ppby04().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[15][1]++;
    }
    cov_3y1ppby04().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_3y1ppby04().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_3y1ppby04().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_3y1ppby04().s[36]++;
exports.auditService = exports.AuditLog = exports.RiskLevel = exports.AuditEventType = void 0;
const mongoose_1 =
/* istanbul ignore next */
(cov_3y1ppby04().s[37]++, __importStar(require("mongoose")));
const logger_1 =
/* istanbul ignore next */
(cov_3y1ppby04().s[38]++, require("../utils/logger"));
// Audit event types
var AuditEventType;
/* istanbul ignore next */
cov_3y1ppby04().s[39]++;
(function (AuditEventType) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[9]++;
  cov_3y1ppby04().s[40]++;
  // Authentication events
  AuditEventType["LOGIN_SUCCESS"] = "login_success";
  /* istanbul ignore next */
  cov_3y1ppby04().s[41]++;
  AuditEventType["LOGIN_FAILED"] = "login_failed";
  /* istanbul ignore next */
  cov_3y1ppby04().s[42]++;
  AuditEventType["LOGOUT"] = "logout";
  /* istanbul ignore next */
  cov_3y1ppby04().s[43]++;
  AuditEventType["PASSWORD_RESET_REQUEST"] = "password_reset_request";
  /* istanbul ignore next */
  cov_3y1ppby04().s[44]++;
  AuditEventType["PASSWORD_RESET_SUCCESS"] = "password_reset_success";
  /* istanbul ignore next */
  cov_3y1ppby04().s[45]++;
  AuditEventType["PASSWORD_CHANGED"] = "password_changed";
  /* istanbul ignore next */
  cov_3y1ppby04().s[46]++;
  AuditEventType["EMAIL_VERIFICATION"] = "email_verification";
  /* istanbul ignore next */
  cov_3y1ppby04().s[47]++;
  AuditEventType["ACCOUNT_LOCKED"] = "account_locked";
  /* istanbul ignore next */
  cov_3y1ppby04().s[48]++;
  AuditEventType["ACCOUNT_UNLOCKED"] = "account_unlocked";
  // Authorization events
  /* istanbul ignore next */
  cov_3y1ppby04().s[49]++;
  AuditEventType["ACCESS_GRANTED"] = "access_granted";
  /* istanbul ignore next */
  cov_3y1ppby04().s[50]++;
  AuditEventType["ACCESS_DENIED"] = "access_denied";
  /* istanbul ignore next */
  cov_3y1ppby04().s[51]++;
  AuditEventType["PERMISSION_ESCALATION"] = "permission_escalation";
  /* istanbul ignore next */
  cov_3y1ppby04().s[52]++;
  AuditEventType["ROLE_CHANGED"] = "role_changed";
  // Data events
  /* istanbul ignore next */
  cov_3y1ppby04().s[53]++;
  AuditEventType["DATA_CREATED"] = "data_created";
  /* istanbul ignore next */
  cov_3y1ppby04().s[54]++;
  AuditEventType["DATA_UPDATED"] = "data_updated";
  /* istanbul ignore next */
  cov_3y1ppby04().s[55]++;
  AuditEventType["DATA_DELETED"] = "data_deleted";
  /* istanbul ignore next */
  cov_3y1ppby04().s[56]++;
  AuditEventType["DATA_VIEWED"] = "data_viewed";
  /* istanbul ignore next */
  cov_3y1ppby04().s[57]++;
  AuditEventType["DATA_EXPORTED"] = "data_exported";
  /* istanbul ignore next */
  cov_3y1ppby04().s[58]++;
  AuditEventType["BULK_OPERATION"] = "bulk_operation";
  // Security events
  /* istanbul ignore next */
  cov_3y1ppby04().s[59]++;
  AuditEventType["SUSPICIOUS_ACTIVITY"] = "suspicious_activity";
  /* istanbul ignore next */
  cov_3y1ppby04().s[60]++;
  AuditEventType["RATE_LIMIT_EXCEEDED"] = "rate_limit_exceeded";
  /* istanbul ignore next */
  cov_3y1ppby04().s[61]++;
  AuditEventType["INVALID_INPUT"] = "invalid_input";
  /* istanbul ignore next */
  cov_3y1ppby04().s[62]++;
  AuditEventType["SQL_INJECTION_ATTEMPT"] = "sql_injection_attempt";
  /* istanbul ignore next */
  cov_3y1ppby04().s[63]++;
  AuditEventType["XSS_ATTEMPT"] = "xss_attempt";
  /* istanbul ignore next */
  cov_3y1ppby04().s[64]++;
  AuditEventType["CSRF_ATTEMPT"] = "csrf_attempt";
  /* istanbul ignore next */
  cov_3y1ppby04().s[65]++;
  AuditEventType["BRUTE_FORCE_ATTEMPT"] = "brute_force_attempt";
  // System events
  /* istanbul ignore next */
  cov_3y1ppby04().s[66]++;
  AuditEventType["SYSTEM_ERROR"] = "system_error";
  /* istanbul ignore next */
  cov_3y1ppby04().s[67]++;
  AuditEventType["CONFIGURATION_CHANGED"] = "configuration_changed";
  /* istanbul ignore next */
  cov_3y1ppby04().s[68]++;
  AuditEventType["BACKUP_CREATED"] = "backup_created";
  /* istanbul ignore next */
  cov_3y1ppby04().s[69]++;
  AuditEventType["MAINTENANCE_MODE"] = "maintenance_mode";
  // Business events
  /* istanbul ignore next */
  cov_3y1ppby04().s[70]++;
  AuditEventType["PROPERTY_POSTED"] = "property_posted";
  /* istanbul ignore next */
  cov_3y1ppby04().s[71]++;
  AuditEventType["PROPERTY_UPDATED"] = "property_updated";
  /* istanbul ignore next */
  cov_3y1ppby04().s[72]++;
  AuditEventType["PROPERTY_DELETED"] = "property_deleted";
  /* istanbul ignore next */
  cov_3y1ppby04().s[73]++;
  AuditEventType["MATCH_CREATED"] = "match_created";
  /* istanbul ignore next */
  cov_3y1ppby04().s[74]++;
  AuditEventType["MESSAGE_SENT"] = "message_sent";
  /* istanbul ignore next */
  cov_3y1ppby04().s[75]++;
  AuditEventType["PAYMENT_PROCESSED"] = "payment_processed";
  /* istanbul ignore next */
  cov_3y1ppby04().s[76]++;
  AuditEventType["SUBSCRIPTION_CHANGED"] = "subscription_changed";
})(
/* istanbul ignore next */
(cov_3y1ppby04().b[17][0]++, AuditEventType) ||
/* istanbul ignore next */
(cov_3y1ppby04().b[17][1]++, exports.AuditEventType = AuditEventType = {}));
// Risk levels
var RiskLevel;
/* istanbul ignore next */
cov_3y1ppby04().s[77]++;
(function (RiskLevel) {
  /* istanbul ignore next */
  cov_3y1ppby04().f[10]++;
  cov_3y1ppby04().s[78]++;
  RiskLevel["LOW"] = "low";
  /* istanbul ignore next */
  cov_3y1ppby04().s[79]++;
  RiskLevel["MEDIUM"] = "medium";
  /* istanbul ignore next */
  cov_3y1ppby04().s[80]++;
  RiskLevel["HIGH"] = "high";
  /* istanbul ignore next */
  cov_3y1ppby04().s[81]++;
  RiskLevel["CRITICAL"] = "critical";
})(
/* istanbul ignore next */
(cov_3y1ppby04().b[18][0]++, RiskLevel) ||
/* istanbul ignore next */
(cov_3y1ppby04().b[18][1]++, exports.RiskLevel = RiskLevel = {}));
// Audit log schema
const auditLogSchema =
/* istanbul ignore next */
(cov_3y1ppby04().s[82]++, new mongoose_1.Schema({
  eventType: {
    type: String,
    enum: Object.values(AuditEventType),
    required: true,
    index: true
  },
  riskLevel: {
    type: String,
    enum: Object.values(RiskLevel),
    required: true,
    index: true
  },
  userId: {
    type: String,
    index: true
  },
  sessionId: {
    type: String,
    index: true
  },
  ipAddress: {
    type: String,
    required: true,
    index: true
  },
  userAgent: {
    type: String,
    required: true
  },
  endpoint: {
    type: String,
    required: true,
    index: true
  },
  method: {
    type: String,
    required: true
  },
  statusCode: {
    type: Number,
    index: true
  },
  resource: {
    type: String,
    index: true
  },
  resourceId: {
    type: String,
    index: true
  },
  oldValues: {
    type: mongoose_1.Schema.Types.Mixed
  },
  newValues: {
    type: mongoose_1.Schema.Types.Mixed
  },
  metadata: {
    type: mongoose_1.Schema.Types.Mixed
  },
  geolocation: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  deviceInfo: {
    browser: String,
    os: String,
    device: String,
    isMobile: Boolean
  },
  success: {
    type: Boolean,
    required: true,
    index: true
  },
  errorMessage: String,
  duration: Number,
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  tags: [{
    type: String,
    index: true
  }]
}, {
  timestamps: true,
  collection: 'audit_logs'
}));
// Compound indexes for common queries
/* istanbul ignore next */
cov_3y1ppby04().s[83]++;
auditLogSchema.index({
  userId: 1,
  timestamp: -1
});
/* istanbul ignore next */
cov_3y1ppby04().s[84]++;
auditLogSchema.index({
  eventType: 1,
  timestamp: -1
});
/* istanbul ignore next */
cov_3y1ppby04().s[85]++;
auditLogSchema.index({
  riskLevel: 1,
  timestamp: -1
});
/* istanbul ignore next */
cov_3y1ppby04().s[86]++;
auditLogSchema.index({
  ipAddress: 1,
  timestamp: -1
});
/* istanbul ignore next */
cov_3y1ppby04().s[87]++;
auditLogSchema.index({
  success: 1,
  timestamp: -1
});
// TTL index for automatic cleanup (keep logs for 1 year)
/* istanbul ignore next */
cov_3y1ppby04().s[88]++;
auditLogSchema.index({
  timestamp: 1
}, {
  expireAfterSeconds: 365 * 24 * 60 * 60
});
/* istanbul ignore next */
cov_3y1ppby04().s[89]++;
exports.AuditLog = mongoose_1.default.model('AuditLog', auditLogSchema);
class AuditService {
  constructor() {
    /* istanbul ignore next */
    cov_3y1ppby04().f[11]++;
    cov_3y1ppby04().s[90]++;
    this.suspiciousActivityThresholds = {
      failedLogins: 5,
      // per 15 minutes
      rateLimitExceeded: 10,
      // per hour
      invalidInputs: 20,
      // per hour
      differentIPs: 3 // per hour for same user
    };
  }
  /**
   * Log an audit event
   */
  async logEvent(eventType, req, options =
  /* istanbul ignore next */
  (cov_3y1ppby04().b[19][0]++, {})) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[12]++;
    cov_3y1ppby04().s[91]++;
    try {
      const auditData =
      /* istanbul ignore next */
      (cov_3y1ppby04().s[92]++, {
        eventType,
        riskLevel:
        /* istanbul ignore next */
        (cov_3y1ppby04().b[20][0]++, options.riskLevel) ||
        /* istanbul ignore next */
        (cov_3y1ppby04().b[20][1]++, this.determineRiskLevel(eventType)),
        userId: req.user?._id,
        sessionId: req.session?.id,
        ipAddress: this.getClientIP(req),
        userAgent:
        /* istanbul ignore next */
        (cov_3y1ppby04().b[21][0]++, req.get('User-Agent')) ||
        /* istanbul ignore next */
        (cov_3y1ppby04().b[21][1]++, 'unknown'),
        endpoint:
        /* istanbul ignore next */
        (cov_3y1ppby04().b[22][0]++, req.originalUrl) ||
        /* istanbul ignore next */
        (cov_3y1ppby04().b[22][1]++, req.url),
        method: req.method,
        statusCode: req.res?.statusCode,
        resource: options.resource,
        resourceId: options.resourceId,
        oldValues: options.oldValues,
        newValues: options.newValues,
        metadata: options.metadata,
        deviceInfo: this.parseUserAgent(req.get('User-Agent')),
        success: options.success !== undefined ?
        /* istanbul ignore next */
        (cov_3y1ppby04().b[23][0]++, options.success) :
        /* istanbul ignore next */
        (cov_3y1ppby04().b[23][1]++, true),
        errorMessage: options.errorMessage,
        duration: options.duration,
        tags:
        /* istanbul ignore next */
        (cov_3y1ppby04().b[24][0]++, options.tags) ||
        /* istanbul ignore next */
        (cov_3y1ppby04().b[24][1]++, []),
        timestamp: new Date()
      });
      // Create audit log entry
      const auditLog =
      /* istanbul ignore next */
      (cov_3y1ppby04().s[93]++, new exports.AuditLog(auditData));
      /* istanbul ignore next */
      cov_3y1ppby04().s[94]++;
      await auditLog.save();
      // Log to application logger as well
      /* istanbul ignore next */
      cov_3y1ppby04().s[95]++;
      logger_1.logger.info('Audit event logged', {
        eventType,
        userId: auditData.userId,
        ipAddress: auditData.ipAddress,
        endpoint: auditData.endpoint,
        success: auditData.success
      });
      // Check for suspicious activity
      /* istanbul ignore next */
      cov_3y1ppby04().s[96]++;
      await this.checkSuspiciousActivity(auditData);
    } catch (error) {
      /* istanbul ignore next */
      cov_3y1ppby04().s[97]++;
      logger_1.logger.error('Failed to log audit event:', error);
    }
  }
  /**
   * Log authentication events
   */
  async logAuth(eventType, req, userId, errorMessage) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[13]++;
    cov_3y1ppby04().s[98]++;
    await this.logEvent(eventType, req, {
      riskLevel: eventType === AuditEventType.LOGIN_FAILED ?
      /* istanbul ignore next */
      (cov_3y1ppby04().b[25][0]++, RiskLevel.MEDIUM) :
      /* istanbul ignore next */
      (cov_3y1ppby04().b[25][1]++, RiskLevel.LOW),
      success: eventType !== AuditEventType.LOGIN_FAILED,
      errorMessage,
      metadata: {
        userId:
        /* istanbul ignore next */
        (cov_3y1ppby04().b[26][0]++, userId) ||
        /* istanbul ignore next */
        (cov_3y1ppby04().b[26][1]++, req.user?._id),
        loginMethod: 'email_password' // Could be extended for OAuth, etc.
      },
      tags: ['authentication']
    });
  }
  /**
   * Log data access events
   */
  async logDataAccess(eventType, req, resource, resourceId, oldValues, newValues) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[14]++;
    cov_3y1ppby04().s[99]++;
    await this.logEvent(eventType, req, {
      riskLevel: eventType === AuditEventType.DATA_DELETED ?
      /* istanbul ignore next */
      (cov_3y1ppby04().b[27][0]++, RiskLevel.HIGH) :
      /* istanbul ignore next */
      (cov_3y1ppby04().b[27][1]++, RiskLevel.LOW),
      resource,
      resourceId,
      oldValues,
      newValues,
      tags: ['data_access', resource]
    });
  }
  /**
   * Log security events
   */
  async logSecurity(eventType, req, details =
  /* istanbul ignore next */
  (cov_3y1ppby04().b[28][0]++, {})) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[15]++;
    cov_3y1ppby04().s[100]++;
    await this.logEvent(eventType, req, {
      riskLevel:
      /* istanbul ignore next */
      (cov_3y1ppby04().b[29][0]++, details.riskLevel) ||
      /* istanbul ignore next */
      (cov_3y1ppby04().b[29][1]++, RiskLevel.HIGH),
      success: false,
      errorMessage: details.errorMessage,
      metadata: details.metadata,
      tags: ['security', 'threat']
    });
  }
  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(filters =
  /* istanbul ignore next */
  (cov_3y1ppby04().b[30][0]++, {})) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[16]++;
    const page =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[101]++, Math.max(1,
    /* istanbul ignore next */
    (cov_3y1ppby04().b[31][0]++, filters.page) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[31][1]++, 1)));
    const limit =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[102]++, Math.min(100, Math.max(1,
    /* istanbul ignore next */
    (cov_3y1ppby04().b[32][0]++, filters.limit) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[32][1]++, 20))));
    const skip =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[103]++, (page - 1) * limit);
    // Build query
    const query =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[104]++, {});
    /* istanbul ignore next */
    cov_3y1ppby04().s[105]++;
    if (filters.userId) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[33][0]++;
      cov_3y1ppby04().s[106]++;
      query.userId = filters.userId;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[33][1]++;
    }
    cov_3y1ppby04().s[107]++;
    if (filters.eventType) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[34][0]++;
      cov_3y1ppby04().s[108]++;
      query.eventType = filters.eventType;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[34][1]++;
    }
    cov_3y1ppby04().s[109]++;
    if (filters.riskLevel) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[35][0]++;
      cov_3y1ppby04().s[110]++;
      query.riskLevel = filters.riskLevel;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[35][1]++;
    }
    cov_3y1ppby04().s[111]++;
    if (filters.ipAddress) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[36][0]++;
      cov_3y1ppby04().s[112]++;
      query.ipAddress = filters.ipAddress;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[36][1]++;
    }
    cov_3y1ppby04().s[113]++;
    if (filters.success !== undefined) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[37][0]++;
      cov_3y1ppby04().s[114]++;
      query.success = filters.success;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[37][1]++;
    }
    cov_3y1ppby04().s[115]++;
    if (
    /* istanbul ignore next */
    (cov_3y1ppby04().b[39][0]++, filters.startDate) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[39][1]++, filters.endDate)) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[38][0]++;
      cov_3y1ppby04().s[116]++;
      query.timestamp = {};
      /* istanbul ignore next */
      cov_3y1ppby04().s[117]++;
      if (filters.startDate) {
        /* istanbul ignore next */
        cov_3y1ppby04().b[40][0]++;
        cov_3y1ppby04().s[118]++;
        query.timestamp.$gte = filters.startDate;
      } else
      /* istanbul ignore next */
      {
        cov_3y1ppby04().b[40][1]++;
      }
      cov_3y1ppby04().s[119]++;
      if (filters.endDate) {
        /* istanbul ignore next */
        cov_3y1ppby04().b[41][0]++;
        cov_3y1ppby04().s[120]++;
        query.timestamp.$lte = filters.endDate;
      } else
      /* istanbul ignore next */
      {
        cov_3y1ppby04().b[41][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[38][1]++;
    }
    // Execute queries
    const [logs, total] =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[121]++, await Promise.all([exports.AuditLog.find(query).sort({
      timestamp: -1
    }).skip(skip).limit(limit).lean(), exports.AuditLog.countDocuments(query)]));
    /* istanbul ignore next */
    cov_3y1ppby04().s[122]++;
    return {
      logs,
      total,
      page,
      pages: Math.ceil(total / limit)
    };
  }
  /**
   * Get audit statistics
   */
  async getAuditStats(timeframe =
  /* istanbul ignore next */
  (cov_3y1ppby04().b[42][0]++, 'day')) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[17]++;
    const now =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[123]++, new Date());
    let startDate;
    /* istanbul ignore next */
    cov_3y1ppby04().s[124]++;
    switch (timeframe) {
      case 'hour':
        /* istanbul ignore next */
        cov_3y1ppby04().b[43][0]++;
        cov_3y1ppby04().s[125]++;
        startDate = new Date(now.getTime() - 60 * 60 * 1000);
        /* istanbul ignore next */
        cov_3y1ppby04().s[126]++;
        break;
      case 'day':
        /* istanbul ignore next */
        cov_3y1ppby04().b[43][1]++;
        cov_3y1ppby04().s[127]++;
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        /* istanbul ignore next */
        cov_3y1ppby04().s[128]++;
        break;
      case 'week':
        /* istanbul ignore next */
        cov_3y1ppby04().b[43][2]++;
        cov_3y1ppby04().s[129]++;
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        /* istanbul ignore next */
        cov_3y1ppby04().s[130]++;
        break;
      case 'month':
        /* istanbul ignore next */
        cov_3y1ppby04().b[43][3]++;
        cov_3y1ppby04().s[131]++;
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        /* istanbul ignore next */
        cov_3y1ppby04().s[132]++;
        break;
    }
    const [totalEvents, securityEvents, failedEvents, eventsByType, eventsByRisk, topIPs, topUsers] =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[133]++, await Promise.all([exports.AuditLog.countDocuments({
      timestamp: {
        $gte: startDate
      }
    }), exports.AuditLog.countDocuments({
      timestamp: {
        $gte: startDate
      },
      tags: 'security'
    }), exports.AuditLog.countDocuments({
      timestamp: {
        $gte: startDate
      },
      success: false
    }), exports.AuditLog.aggregate([{
      $match: {
        timestamp: {
          $gte: startDate
        }
      }
    }, {
      $group: {
        _id: '$eventType',
        count: {
          $sum: 1
        }
      }
    }, {
      $sort: {
        count: -1
      }
    }, {
      $limit: 10
    }]), exports.AuditLog.aggregate([{
      $match: {
        timestamp: {
          $gte: startDate
        }
      }
    }, {
      $group: {
        _id: '$riskLevel',
        count: {
          $sum: 1
        }
      }
    }]), exports.AuditLog.aggregate([{
      $match: {
        timestamp: {
          $gte: startDate
        }
      }
    }, {
      $group: {
        _id: '$ipAddress',
        count: {
          $sum: 1
        }
      }
    }, {
      $sort: {
        count: -1
      }
    }, {
      $limit: 10
    }]), exports.AuditLog.aggregate([{
      $match: {
        timestamp: {
          $gte: startDate
        },
        userId: {
          $exists: true
        }
      }
    }, {
      $group: {
        _id: '$userId',
        count: {
          $sum: 1
        }
      }
    }, {
      $sort: {
        count: -1
      }
    }, {
      $limit: 10
    }])]));
    /* istanbul ignore next */
    cov_3y1ppby04().s[134]++;
    return {
      timeframe,
      period: {
        start: startDate,
        end: now
      },
      summary: {
        totalEvents,
        securityEvents,
        failedEvents,
        successRate: totalEvents > 0 ?
        /* istanbul ignore next */
        (cov_3y1ppby04().b[44][0]++, ((totalEvents - failedEvents) / totalEvents * 100).toFixed(2)) :
        /* istanbul ignore next */
        (cov_3y1ppby04().b[44][1]++, 0)
      },
      breakdown: {
        byType: eventsByType,
        byRisk: eventsByRisk,
        topIPs,
        topUsers
      }
    };
  }
  /**
   * Check for suspicious activity patterns
   */
  async checkSuspiciousActivity(auditData) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[18]++;
    const now =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[135]++, new Date());
    const oneHour =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[136]++, new Date(now.getTime() - 60 * 60 * 1000));
    const fifteenMinutes =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[137]++, new Date(now.getTime() - 15 * 60 * 1000));
    /* istanbul ignore next */
    cov_3y1ppby04().s[138]++;
    try {
      /* istanbul ignore next */
      cov_3y1ppby04().s[139]++;
      // Check for failed login attempts
      if (auditData.eventType === AuditEventType.LOGIN_FAILED) {
        /* istanbul ignore next */
        cov_3y1ppby04().b[45][0]++;
        const recentFailures =
        /* istanbul ignore next */
        (cov_3y1ppby04().s[140]++, await exports.AuditLog.countDocuments({
          eventType: AuditEventType.LOGIN_FAILED,
          ipAddress: auditData.ipAddress,
          timestamp: {
            $gte: fifteenMinutes
          }
        }));
        /* istanbul ignore next */
        cov_3y1ppby04().s[141]++;
        if (recentFailures >= this.suspiciousActivityThresholds.failedLogins) {
          /* istanbul ignore next */
          cov_3y1ppby04().b[46][0]++;
          cov_3y1ppby04().s[142]++;
          await this.logSuspiciousActivity('Multiple failed login attempts', auditData);
        } else
        /* istanbul ignore next */
        {
          cov_3y1ppby04().b[46][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_3y1ppby04().b[45][1]++;
      }
      // Check for rate limit exceeded events
      cov_3y1ppby04().s[143]++;
      if (auditData.eventType === AuditEventType.RATE_LIMIT_EXCEEDED) {
        /* istanbul ignore next */
        cov_3y1ppby04().b[47][0]++;
        const recentRateLimits =
        /* istanbul ignore next */
        (cov_3y1ppby04().s[144]++, await exports.AuditLog.countDocuments({
          eventType: AuditEventType.RATE_LIMIT_EXCEEDED,
          ipAddress: auditData.ipAddress,
          timestamp: {
            $gte: oneHour
          }
        }));
        /* istanbul ignore next */
        cov_3y1ppby04().s[145]++;
        if (recentRateLimits >= this.suspiciousActivityThresholds.rateLimitExceeded) {
          /* istanbul ignore next */
          cov_3y1ppby04().b[48][0]++;
          cov_3y1ppby04().s[146]++;
          await this.logSuspiciousActivity('Excessive rate limiting', auditData);
        } else
        /* istanbul ignore next */
        {
          cov_3y1ppby04().b[48][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_3y1ppby04().b[47][1]++;
      }
      // Check for multiple IPs for same user
      cov_3y1ppby04().s[147]++;
      if (auditData.userId) {
        /* istanbul ignore next */
        cov_3y1ppby04().b[49][0]++;
        const distinctIPs =
        /* istanbul ignore next */
        (cov_3y1ppby04().s[148]++, await exports.AuditLog.distinct('ipAddress', {
          userId: auditData.userId,
          timestamp: {
            $gte: oneHour
          }
        }));
        /* istanbul ignore next */
        cov_3y1ppby04().s[149]++;
        if (distinctIPs.length >= this.suspiciousActivityThresholds.differentIPs) {
          /* istanbul ignore next */
          cov_3y1ppby04().b[50][0]++;
          cov_3y1ppby04().s[150]++;
          await this.logSuspiciousActivity('Multiple IP addresses for user', auditData);
        } else
        /* istanbul ignore next */
        {
          cov_3y1ppby04().b[50][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_3y1ppby04().b[49][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_3y1ppby04().s[151]++;
      logger_1.logger.error('Error checking suspicious activity:', error);
    }
  }
  /**
   * Log suspicious activity
   */
  async logSuspiciousActivity(reason, originalEvent) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[19]++;
    cov_3y1ppby04().s[152]++;
    try {
      const suspiciousLog =
      /* istanbul ignore next */
      (cov_3y1ppby04().s[153]++, new exports.AuditLog({
        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,
        riskLevel: RiskLevel.CRITICAL,
        userId: originalEvent.userId,
        sessionId: originalEvent.sessionId,
        ipAddress: originalEvent.ipAddress,
        userAgent: originalEvent.userAgent,
        endpoint: originalEvent.endpoint,
        method: originalEvent.method,
        success: false,
        errorMessage: reason,
        metadata: {
          originalEvent: originalEvent.eventType,
          detectionReason: reason,
          timestamp: originalEvent.timestamp
        },
        tags: ['suspicious', 'security', 'automated_detection'],
        timestamp: new Date()
      }));
      /* istanbul ignore next */
      cov_3y1ppby04().s[154]++;
      await suspiciousLog.save();
      // Also log to application logger with high priority
      /* istanbul ignore next */
      cov_3y1ppby04().s[155]++;
      logger_1.logger.warn('Suspicious activity detected', {
        reason,
        userId: originalEvent.userId,
        ipAddress: originalEvent.ipAddress,
        originalEvent: originalEvent.eventType
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_3y1ppby04().s[156]++;
      logger_1.logger.error('Failed to log suspicious activity:', error);
    }
  }
  /**
   * Determine risk level based on event type
   */
  determineRiskLevel(eventType) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[20]++;
    const highRiskEvents =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[157]++, [AuditEventType.DATA_DELETED, AuditEventType.PERMISSION_ESCALATION, AuditEventType.ROLE_CHANGED, AuditEventType.CONFIGURATION_CHANGED, AuditEventType.ACCOUNT_LOCKED]);
    const mediumRiskEvents =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[158]++, [AuditEventType.LOGIN_FAILED, AuditEventType.ACCESS_DENIED, AuditEventType.PASSWORD_RESET_REQUEST, AuditEventType.DATA_UPDATED, AuditEventType.RATE_LIMIT_EXCEEDED]);
    const criticalRiskEvents =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[159]++, [AuditEventType.SUSPICIOUS_ACTIVITY, AuditEventType.SQL_INJECTION_ATTEMPT, AuditEventType.XSS_ATTEMPT, AuditEventType.BRUTE_FORCE_ATTEMPT]);
    /* istanbul ignore next */
    cov_3y1ppby04().s[160]++;
    if (criticalRiskEvents.includes(eventType)) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[51][0]++;
      cov_3y1ppby04().s[161]++;
      return RiskLevel.CRITICAL;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[51][1]++;
    }
    cov_3y1ppby04().s[162]++;
    if (highRiskEvents.includes(eventType)) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[52][0]++;
      cov_3y1ppby04().s[163]++;
      return RiskLevel.HIGH;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[52][1]++;
    }
    cov_3y1ppby04().s[164]++;
    if (mediumRiskEvents.includes(eventType)) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[53][0]++;
      cov_3y1ppby04().s[165]++;
      return RiskLevel.MEDIUM;
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[53][1]++;
    }
    cov_3y1ppby04().s[166]++;
    return RiskLevel.LOW;
  }
  /**
   * Get client IP address
   */
  getClientIP(req) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[21]++;
    cov_3y1ppby04().s[167]++;
    return /* istanbul ignore next */(cov_3y1ppby04().b[54][0]++, req.ip) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[54][1]++, req.connection.remoteAddress) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[54][2]++, req.socket.remoteAddress) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[54][3]++, req.connection?.socket?.remoteAddress) ||
    /* istanbul ignore next */
    (cov_3y1ppby04().b[54][4]++, 'unknown');
  }
  /**
   * Parse user agent for device info
   */
  parseUserAgent(userAgent) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[22]++;
    cov_3y1ppby04().s[168]++;
    if (!userAgent) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[55][0]++;
      cov_3y1ppby04().s[169]++;
      return {
        browser: 'unknown',
        os: 'unknown',
        device: 'unknown',
        isMobile: false
      };
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[55][1]++;
    }
    const browser =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[170]++, this.detectBrowser(userAgent));
    const os =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[171]++, this.detectOS(userAgent));
    const device =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[172]++, this.detectDevice(userAgent));
    const isMobile =
    /* istanbul ignore next */
    (cov_3y1ppby04().s[173]++, /Mobile|Android|iPhone|iPad/.test(userAgent));
    /* istanbul ignore next */
    cov_3y1ppby04().s[174]++;
    return {
      browser,
      os,
      device,
      isMobile
    };
  }
  detectBrowser(userAgent) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[23]++;
    cov_3y1ppby04().s[175]++;
    if (userAgent.includes('Chrome')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[56][0]++;
      cov_3y1ppby04().s[176]++;
      return 'Chrome';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[56][1]++;
    }
    cov_3y1ppby04().s[177]++;
    if (userAgent.includes('Firefox')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[57][0]++;
      cov_3y1ppby04().s[178]++;
      return 'Firefox';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[57][1]++;
    }
    cov_3y1ppby04().s[179]++;
    if (userAgent.includes('Safari')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[58][0]++;
      cov_3y1ppby04().s[180]++;
      return 'Safari';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[58][1]++;
    }
    cov_3y1ppby04().s[181]++;
    if (userAgent.includes('Edge')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[59][0]++;
      cov_3y1ppby04().s[182]++;
      return 'Edge';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[59][1]++;
    }
    cov_3y1ppby04().s[183]++;
    if (userAgent.includes('Opera')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[60][0]++;
      cov_3y1ppby04().s[184]++;
      return 'Opera';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[60][1]++;
    }
    cov_3y1ppby04().s[185]++;
    return 'unknown';
  }
  detectOS(userAgent) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[24]++;
    cov_3y1ppby04().s[186]++;
    if (userAgent.includes('Windows')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[61][0]++;
      cov_3y1ppby04().s[187]++;
      return 'Windows';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[61][1]++;
    }
    cov_3y1ppby04().s[188]++;
    if (userAgent.includes('Mac OS')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[62][0]++;
      cov_3y1ppby04().s[189]++;
      return 'macOS';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[62][1]++;
    }
    cov_3y1ppby04().s[190]++;
    if (userAgent.includes('Linux')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[63][0]++;
      cov_3y1ppby04().s[191]++;
      return 'Linux';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[63][1]++;
    }
    cov_3y1ppby04().s[192]++;
    if (userAgent.includes('Android')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[64][0]++;
      cov_3y1ppby04().s[193]++;
      return 'Android';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[64][1]++;
    }
    cov_3y1ppby04().s[194]++;
    if (userAgent.includes('iOS')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[65][0]++;
      cov_3y1ppby04().s[195]++;
      return 'iOS';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[65][1]++;
    }
    cov_3y1ppby04().s[196]++;
    return 'unknown';
  }
  detectDevice(userAgent) {
    /* istanbul ignore next */
    cov_3y1ppby04().f[25]++;
    cov_3y1ppby04().s[197]++;
    if (userAgent.includes('Mobile')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[66][0]++;
      cov_3y1ppby04().s[198]++;
      return 'mobile';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[66][1]++;
    }
    cov_3y1ppby04().s[199]++;
    if (userAgent.includes('Tablet')) {
      /* istanbul ignore next */
      cov_3y1ppby04().b[67][0]++;
      cov_3y1ppby04().s[200]++;
      return 'tablet';
    } else
    /* istanbul ignore next */
    {
      cov_3y1ppby04().b[67][1]++;
    }
    cov_3y1ppby04().s[201]++;
    return 'desktop';
  }
}
// Create and export singleton instance
/* istanbul ignore next */
cov_3y1ppby04().s[202]++;
exports.auditService = new AuditService();
/* istanbul ignore next */
cov_3y1ppby04().s[203]++;
exports.default = exports.auditService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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