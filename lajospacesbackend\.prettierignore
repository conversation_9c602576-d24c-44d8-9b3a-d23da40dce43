# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Coverage reports
coverage/
*.lcov

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Cache
.cache/
.parcel-cache/
.eslintcache

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Package files
*.tgz
*.tar.gz

# MongoDB
*.mongodb

# Generated files
*.generated.*
auto-generated/

# Documentation
docs/api/

# Test files that shouldn't be formatted
tests/fixtures/
tests/snapshots/

# Configuration files that have specific formatting
.github/
docker-compose.yml
Dockerfile*

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Husky
.husky/
