a8aae4dd0520495e182d0305d35e85f4
"use strict";

/* istanbul ignore next */
function cov_1fiultajuk() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyFavorite.routes.ts";
  var hash = "65d7f252411eb1d8b89a209f8bddc2c69f2a86ce";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyFavorite.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 38
        },
        end: {
          line: 4,
          column: 91
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 30
        },
        end: {
          line: 7,
          column: 74
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 75
        }
      },
      "8": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 32
        }
      },
      "9": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 147
        }
      },
      "10": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 131
        }
      },
      "11": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 64
        }
      },
      "12": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 70
        }
      },
      "13": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 135
        }
      },
      "14": {
        start: {
          line: 22,
          column: 0
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "15": {
        start: {
          line: 23,
          column: 0
        },
        end: {
          line: 23,
          column: 25
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyFavorite.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,4FAQoD;AACpD,6CAAkD;AAClD,yDAA6E;AAC7E,2EAA2E;AAE3E,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,gBAAgB;AAChB,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,kDAAoB,CAAC,CAAC;AAE7C,6CAA6C;AAC7C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB,sBAAsB;AACtB,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAA,4BAAe,EAAC,4CAAsB,CAAC,EAAE,4CAAc,CAAC,CAAC;AAC7E,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,IAAA,6BAAgB,EAAC,YAAY,CAAC,EAAE,iDAAmB,CAAC,CAAC;AAEnF,iBAAiB;AACjB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,8CAAgB,CAAC,CAAC;AAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,+CAAiB,CAAC,CAAC;AAExC,wBAAwB;AACxB,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAA,6BAAgB,EAAC,YAAY,CAAC,EAAE,iDAAmB,CAAC,CAAC;AAEvF,kBAAkB;AAClB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,iDAAmB,CAAC,CAAC;AAE1C,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyFavorite.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  addToFavorites,\r\n  removeFromFavorites,\r\n  getUserFavorites,\r\n  checkFavoriteStatus,\r\n  getFavoritesCount,\r\n  getPopularProperties,\r\n  bulkUpdateFavorites\r\n} from '../controllers/propertyFavorite.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport { favoritePropertySchema } from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n// Public routes\r\nrouter.get('/popular', getPopularProperties);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Favorite management\r\nrouter.post('/add', validateRequest(favoritePropertySchema), addToFavorites);\r\nrouter.delete('/:propertyId', validateObjectId('propertyId'), removeFromFavorites);\r\n\r\n// User favorites\r\nrouter.get('/', getUserFavorites);\r\nrouter.get('/count', getFavoritesCount);\r\n\r\n// Check favorite status\r\nrouter.get('/:propertyId/status', validateObjectId('propertyId'), checkFavoriteStatus);\r\n\r\n// Bulk operations\r\nrouter.post('/bulk', bulkUpdateFavorites);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "65d7f252411eb1d8b89a209f8bddc2c69f2a86ce"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1fiultajuk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fiultajuk();
cov_1fiultajuk().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_1fiultajuk().s[1]++, require("express"));
const propertyFavorite_controller_1 =
/* istanbul ignore next */
(cov_1fiultajuk().s[2]++, require("../controllers/propertyFavorite.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_1fiultajuk().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_1fiultajuk().s[4]++, require("../middleware/validation"));
const property_validators_1 =
/* istanbul ignore next */
(cov_1fiultajuk().s[5]++, require("../validators/property.validators"));
const router =
/* istanbul ignore next */
(cov_1fiultajuk().s[6]++, (0, express_1.Router)());
// Public routes
/* istanbul ignore next */
cov_1fiultajuk().s[7]++;
router.get('/popular', propertyFavorite_controller_1.getPopularProperties);
// Protected routes (authentication required)
/* istanbul ignore next */
cov_1fiultajuk().s[8]++;
router.use(auth_1.authenticate);
// Favorite management
/* istanbul ignore next */
cov_1fiultajuk().s[9]++;
router.post('/add', (0, validation_1.validateRequest)(property_validators_1.favoritePropertySchema), propertyFavorite_controller_1.addToFavorites);
/* istanbul ignore next */
cov_1fiultajuk().s[10]++;
router.delete('/:propertyId', (0, validation_1.validateObjectId)('propertyId'), propertyFavorite_controller_1.removeFromFavorites);
// User favorites
/* istanbul ignore next */
cov_1fiultajuk().s[11]++;
router.get('/', propertyFavorite_controller_1.getUserFavorites);
/* istanbul ignore next */
cov_1fiultajuk().s[12]++;
router.get('/count', propertyFavorite_controller_1.getFavoritesCount);
// Check favorite status
/* istanbul ignore next */
cov_1fiultajuk().s[13]++;
router.get('/:propertyId/status', (0, validation_1.validateObjectId)('propertyId'), propertyFavorite_controller_1.checkFavoriteStatus);
// Bulk operations
/* istanbul ignore next */
cov_1fiultajuk().s[14]++;
router.post('/bulk', propertyFavorite_controller_1.bulkUpdateFavorites);
/* istanbul ignore next */
cov_1fiultajuk().s[15]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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