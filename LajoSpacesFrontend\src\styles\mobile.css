/* Mobile-specific enhancements for LajoSpaces */

/* Safe area support for devices with notches */
.safe-area-pt {
  padding-top: env(safe-area-inset-top);
}

.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-pl {
  padding-left: env(safe-area-inset-left);
}

.safe-area-pr {
  padding-right: env(safe-area-inset-right);
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Improved scrolling on mobile */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Better focus states for mobile */
.focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile-optimized card hover states */
@media (hover: none) and (pointer: coarse) {
  .mobile-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Improved button touch targets */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
}

/* Mobile-specific breakpoints */
@media (max-width: 374px) {
  .xs\:hidden {
    display: none;
  }
  
  .xs\:inline {
    display: inline;
  }
  
  .xs\:block {
    display: block;
  }
  
  .xs\:flex {
    display: flex;
  }
}

/* Prevent zoom on input focus */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
textarea,
select {
  font-size: 16px;
}

@media (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Mobile navigation improvements */
.mobile-nav-item {
  transition: all 0.2s ease;
}

.mobile-nav-item:active {
  transform: scale(0.95);
  background-color: rgba(147, 51, 234, 0.1);
}

/* Improved modal positioning on mobile */
@media (max-width: 640px) {
  .mobile-modal {
    margin: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .mobile-modal-content {
    height: 100%;
    overflow-y: auto;
  }
}

/* Better image loading states */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Improved swipe gestures */
.swipe-container {
  touch-action: pan-y pinch-zoom;
}

.swipe-item {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Mobile-optimized form layouts */
.mobile-form-group {
  margin-bottom: 1rem;
}

.mobile-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.mobile-form-group input,
.mobile-form-group textarea,
.mobile-form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 16px;
}

/* Improved accessibility for mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile-specific utility classes */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* Improved mobile typography */
@media (max-width: 640px) {
  .mobile-text-responsive {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  .mobile-heading-responsive {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* Better mobile card layouts */
.mobile-card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .mobile-card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .mobile-card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile-optimized loading states */
.mobile-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Improved mobile search */
.mobile-search-container {
  position: relative;
  width: 100%;
}

.mobile-search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 16px;
}

.mobile-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.mobile-search-clear {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.mobile-search-clear:hover {
  background-color: #f3f4f6;
}
