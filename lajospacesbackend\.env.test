# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Database Configuration
MONGODB_URI=mongodb://localhost:27017/lajospaces_test
REDIS_URL=redis://localhost:6379/15

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=test_refresh_secret_key_for_testing_only
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=test_session_secret_key_for_testing_only

# Email Configuration (Mock)
EMAIL_SERVICE=test
EMAIL_HOST=smtp.test.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=test_password
EMAIL_FROM=<EMAIL>

# Cloudinary Configuration (Mock)
CLOUDINARY_CLOUD_NAME=test_cloud
CLOUDINARY_API_KEY=test_api_key
CLOUDINARY_API_SECRET=test_api_secret

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging Configuration
LOG_LEVEL=error
LOG_FILE=false

# Security Configuration
BCRYPT_ROUNDS=10
CORS_ORIGIN=http://localhost:3000

# Test-specific Configuration
TEST_TIMEOUT=30000
TEST_PARALLEL=true
TEST_COVERAGE=true

# Nigerian Market Configuration
DEFAULT_TIMEZONE=Africa/Lagos
DEFAULT_CURRENCY=NGN
DEFAULT_COUNTRY=Nigeria

# Feature Flags for Testing
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SMS_VERIFICATION=false
ENABLE_RATE_LIMITING=false
ENABLE_AUDIT_LOGGING=false
