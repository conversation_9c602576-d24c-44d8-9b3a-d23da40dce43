f5d473afc69b5f9d1dd967c6b5b4277e
"use strict";

/* istanbul ignore next */
function cov_3ygeh07az() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\presenceService.ts";
  var hash = "5a923655188a239ee8cf5260c6790d2e2f356aea";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\presenceService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 4,
          column: 21
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "3": {
        start: {
          line: 5,
          column: 17
        },
        end: {
          line: 5,
          column: 43
        }
      },
      "4": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 37
        }
      },
      "5": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 40
        }
      },
      "6": {
        start: {
          line: 11,
          column: 8
        },
        end: {
          line: 13,
          column: 18
        }
      },
      "7": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 36
        }
      },
      "8": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 18,
          column: 9
        }
      },
      "9": {
        start: {
          line: 17,
          column: 12
        },
        end: {
          line: 17,
          column: 61
        }
      },
      "10": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 40
        }
      },
      "11": {
        start: {
          line: 25,
          column: 33
        },
        end: {
          line: 25,
          column: 61
        }
      },
      "12": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 42,
          column: 9
        }
      },
      "13": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 30,
          column: 13
        }
      },
      "14": {
        start: {
          line: 29,
          column: 16
        },
        end: {
          line: 29,
          column: 58
        }
      },
      "15": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 31,
          column: 45
        }
      },
      "16": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 51
        }
      },
      "17": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 41,
          column: 15
        }
      },
      "18": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 42
        }
      },
      "19": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 89
        }
      },
      "20": {
        start: {
          line: 51,
          column: 25
        },
        end: {
          line: 51,
          column: 53
        }
      },
      "21": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 70,
          column: 9
        }
      },
      "22": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 69,
          column: 13
        }
      },
      "23": {
        start: {
          line: 55,
          column: 16
        },
        end: {
          line: 55,
          column: 86
        }
      },
      "24": {
        start: {
          line: 55,
          column: 69
        },
        end: {
          line: 55,
          column: 84
        }
      },
      "25": {
        start: {
          line: 57,
          column: 16
        },
        end: {
          line: 61,
          column: 17
        }
      },
      "26": {
        start: {
          line: 58,
          column: 20
        },
        end: {
          line: 58,
          column: 48
        }
      },
      "27": {
        start: {
          line: 59,
          column: 20
        },
        end: {
          line: 59,
          column: 51
        }
      },
      "28": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 54
        }
      },
      "29": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 44
        }
      },
      "30": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 47
        }
      },
      "31": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 67,
          column: 40
        }
      },
      "32": {
        start: {
          line: 68,
          column: 16
        },
        end: {
          line: 68,
          column: 50
        }
      },
      "33": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 43
        }
      },
      "34": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 73,
          column: 102
        }
      },
      "35": {
        start: {
          line: 79,
          column: 25
        },
        end: {
          line: 79,
          column: 53
        }
      },
      "36": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "37": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 37
        }
      },
      "38": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 43
        }
      },
      "39": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 46
        }
      },
      "40": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 76
        }
      },
      "41": {
        start: {
          line: 91,
          column: 25
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "42": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 102,
          column: 9
        }
      },
      "43": {
        start: {
          line: 93,
          column: 29
        },
        end: {
          line: 96,
          column: 13
        }
      },
      "44": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 99,
          column: 13
        }
      },
      "45": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 98,
          column: 43
        }
      },
      "46": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 48
        }
      },
      "47": {
        start: {
          line: 101,
          column: 12
        },
        end: {
          line: 101,
          column: 43
        }
      },
      "48": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 112
        }
      },
      "49": {
        start: {
          line: 109,
          column: 25
        },
        end: {
          line: 109,
          column: 53
        }
      },
      "50": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 113,
          column: 9
        }
      },
      "51": {
        start: {
          line: 111,
          column: 12
        },
        end: {
          line: 111,
          column: 44
        }
      },
      "52": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 43
        }
      },
      "53": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 52
        }
      },
      "54": {
        start: {
          line: 125,
          column: 25
        },
        end: {
          line: 125,
          column: 53
        }
      },
      "55": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 54
        }
      },
      "56": {
        start: {
          line: 132,
          column: 25
        },
        end: {
          line: 132,
          column: 53
        }
      },
      "57": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 133,
          column: 97
        }
      },
      "58": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 136
        }
      },
      "59": {
        start: {
          line: 139,
          column: 72
        },
        end: {
          line: 139,
          column: 134
        }
      },
      "60": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 44
        }
      },
      "61": {
        start: {
          line: 151,
          column: 25
        },
        end: {
          line: 151,
          column: 53
        }
      },
      "62": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 152,
          column: 50
        }
      },
      "63": {
        start: {
          line: 158,
          column: 20
        },
        end: {
          line: 158,
          column: 49
        }
      },
      "64": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 169,
          column: 9
        }
      },
      "65": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 165,
          column: 15
        }
      },
      "66": {
        start: {
          line: 168,
          column: 12
        },
        end: {
          line: 168,
          column: 44
        }
      },
      "67": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 170,
          column: 125
        }
      },
      "68": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 176,
          column: 134
        }
      },
      "69": {
        start: {
          line: 176,
          column: 73
        },
        end: {
          line: 176,
          column: 132
        }
      },
      "70": {
        start: {
          line: 182,
          column: 29
        },
        end: {
          line: 182,
          column: 31
        }
      },
      "71": {
        start: {
          line: 183,
          column: 8
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "72": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 186,
          column: 13
        }
      },
      "73": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 185,
          column: 39
        }
      },
      "74": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 69
        }
      },
      "75": {
        start: {
          line: 188,
          column: 36
        },
        end: {
          line: 188,
          column: 67
        }
      },
      "76": {
        start: {
          line: 194,
          column: 27
        },
        end: {
          line: 194,
          column: 72
        }
      },
      "77": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 195,
          column: 105
        }
      },
      "78": {
        start: {
          line: 195,
          column: 72
        },
        end: {
          line: 195,
          column: 103
        }
      },
      "79": {
        start: {
          line: 201,
          column: 26
        },
        end: {
          line: 201,
          column: 63
        }
      },
      "80": {
        start: {
          line: 202,
          column: 22
        },
        end: {
          line: 209,
          column: 9
        }
      },
      "81": {
        start: {
          line: 203,
          column: 47
        },
        end: {
          line: 203,
          column: 94
        }
      },
      "82": {
        start: {
          line: 204,
          column: 45
        },
        end: {
          line: 204,
          column: 64
        }
      },
      "83": {
        start: {
          line: 205,
          column: 45
        },
        end: {
          line: 205,
          column: 64
        }
      },
      "84": {
        start: {
          line: 206,
          column: 48
        },
        end: {
          line: 206,
          column: 98
        }
      },
      "85": {
        start: {
          line: 211,
          column: 31
        },
        end: {
          line: 211,
          column: 76
        }
      },
      "86": {
        start: {
          line: 211,
          column: 53
        },
        end: {
          line: 211,
          column: 75
        }
      },
      "87": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 218,
          column: 9
        }
      },
      "88": {
        start: {
          line: 213,
          column: 34
        },
        end: {
          line: 216,
          column: 17
        }
      },
      "89": {
        start: {
          line: 214,
          column: 40
        },
        end: {
          line: 214,
          column: 80
        }
      },
      "90": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 45
        }
      },
      "91": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 105
        }
      },
      "92": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 21
        }
      },
      "93": {
        start: {
          line: 225,
          column: 20
        },
        end: {
          line: 225,
          column: 30
        }
      },
      "94": {
        start: {
          line: 226,
          column: 31
        },
        end: {
          line: 226,
          column: 44
        }
      },
      "95": {
        start: {
          line: 227,
          column: 32
        },
        end: {
          line: 227,
          column: 41
        }
      },
      "96": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "97": {
        start: {
          line: 230,
          column: 38
        },
        end: {
          line: 230,
          column: 81
        }
      },
      "98": {
        start: {
          line: 231,
          column: 12
        },
        end: {
          line: 236,
          column: 13
        }
      },
      "99": {
        start: {
          line: 232,
          column: 16
        },
        end: {
          line: 232,
          column: 44
        }
      },
      "100": {
        start: {
          line: 233,
          column: 16
        },
        end: {
          line: 233,
          column: 40
        }
      },
      "101": {
        start: {
          line: 234,
          column: 16
        },
        end: {
          line: 234,
          column: 50
        }
      },
      "102": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 92
        }
      },
      "103": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "104": {
        start: {
          line: 240,
          column: 37
        },
        end: {
          line: 240,
          column: 85
        }
      },
      "105": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 244,
          column: 13
        }
      },
      "106": {
        start: {
          line: 242,
          column: 16
        },
        end: {
          line: 242,
          column: 48
        }
      },
      "107": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 243,
          column: 148
        }
      },
      "108": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 258,
          column: 9
        }
      },
      "109": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 254,
          column: 15
        }
      },
      "110": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 257,
          column: 96
        }
      },
      "111": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 283,
          column: 9
        }
      },
      "112": {
        start: {
          line: 266,
          column: 40
        },
        end: {
          line: 268,
          column: 48
        }
      },
      "113": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 278,
          column: 13
        }
      },
      "114": {
        start: {
          line: 271,
          column: 33
        },
        end: {
          line: 271,
          column: 74
        }
      },
      "115": {
        start: {
          line: 272,
          column: 16
        },
        end: {
          line: 277,
          column: 17
        }
      },
      "116": {
        start: {
          line: 274,
          column: 20
        },
        end: {
          line: 276,
          column: 21
        }
      },
      "117": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 275,
          column: 62
        }
      },
      "118": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 279,
          column: 98
        }
      },
      "119": {
        start: {
          line: 282,
          column: 12
        },
        end: {
          line: 282,
          column: 82
        }
      },
      "120": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 291,
          column: 9
        }
      },
      "121": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 290,
          column: 48
        }
      },
      "122": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 297,
          column: 9
        }
      },
      "123": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 294,
          column: 40
        }
      },
      "124": {
        start: {
          line: 295,
          column: 12
        },
        end: {
          line: 295,
          column: 36
        }
      },
      "125": {
        start: {
          line: 296,
          column: 12
        },
        end: {
          line: 296,
          column: 46
        }
      },
      "126": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 298,
          column: 33
        }
      },
      "127": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 299,
          column: 36
        }
      },
      "128": {
        start: {
          line: 300,
          column: 8
        },
        end: {
          line: 300,
          column: 60
        }
      },
      "129": {
        start: {
          line: 303,
          column: 0
        },
        end: {
          line: 303,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        loc: {
          start: {
            line: 7,
            column: 18
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 11,
            column: 43
          },
          end: {
            line: 11,
            column: 44
          }
        },
        loc: {
          start: {
            line: 11,
            column: 49
          },
          end: {
            line: 13,
            column: 9
          }
        },
        line: 11
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 5
          }
        },
        loc: {
          start: {
            line: 15,
            column: 25
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 24,
            column: 5
          }
        },
        loc: {
          start: {
            line: 24,
            column: 55
          },
          end: {
            line: 46,
            column: 5
          }
        },
        line: 24
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        },
        loc: {
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 74,
            column: 5
          }
        },
        line: 50
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 55,
            column: 63
          },
          end: {
            line: 55,
            column: 64
          }
        },
        loc: {
          start: {
            line: 55,
            column: 69
          },
          end: {
            line: 55,
            column: 84
          }
        },
        line: 55
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 5
          }
        },
        loc: {
          start: {
            line: 78,
            column: 37
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 78
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 90,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        },
        loc: {
          start: {
            line: 90,
            column: 51
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 90
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        loc: {
          start: {
            line: 108,
            column: 30
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 108
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 28
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 118
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 124,
            column: 5
          }
        },
        loc: {
          start: {
            line: 124,
            column: 26
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 124
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        },
        loc: {
          start: {
            line: 131,
            column: 25
          },
          end: {
            line: 134,
            column: 5
          }
        },
        line: 131
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 138,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        loc: {
          start: {
            line: 138,
            column: 21
          },
          end: {
            line: 140,
            column: 5
          }
        },
        line: 138
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 139,
            column: 60
          },
          end: {
            line: 139,
            column: 61
          }
        },
        loc: {
          start: {
            line: 139,
            column: 72
          },
          end: {
            line: 139,
            column: 134
          }
        },
        line: 139
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 144,
            column: 4
          },
          end: {
            line: 144,
            column: 5
          }
        },
        loc: {
          start: {
            line: 144,
            column: 26
          },
          end: {
            line: 146,
            column: 5
          }
        },
        line: 144
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 150,
            column: 4
          },
          end: {
            line: 150,
            column: 5
          }
        },
        loc: {
          start: {
            line: 150,
            column: 29
          },
          end: {
            line: 153,
            column: 5
          }
        },
        line: 150
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        },
        loc: {
          start: {
            line: 157,
            column: 54
          },
          end: {
            line: 171,
            column: 5
          }
        },
        line: 157
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        },
        loc: {
          start: {
            line: 175,
            column: 49
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 175
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 176,
            column: 63
          },
          end: {
            line: 176,
            column: 64
          }
        },
        loc: {
          start: {
            line: 176,
            column: 73
          },
          end: {
            line: 176,
            column: 132
          }
        },
        line: 176
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        loc: {
          start: {
            line: 181,
            column: 34
          },
          end: {
            line: 189,
            column: 5
          }
        },
        line: 181
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 188,
            column: 29
          },
          end: {
            line: 188,
            column: 30
          }
        },
        loc: {
          start: {
            line: 188,
            column: 36
          },
          end: {
            line: 188,
            column: 67
          }
        },
        line: 188
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        },
        loc: {
          start: {
            line: 193,
            column: 44
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 193
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 195,
            column: 60
          },
          end: {
            line: 195,
            column: 61
          }
        },
        loc: {
          start: {
            line: 195,
            column: 72
          },
          end: {
            line: 195,
            column: 103
          }
        },
        line: 195
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 23
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 200
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 203,
            column: 42
          },
          end: {
            line: 203,
            column: 43
          }
        },
        loc: {
          start: {
            line: 203,
            column: 47
          },
          end: {
            line: 203,
            column: 94
          }
        },
        line: 203
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 204,
            column: 40
          },
          end: {
            line: 204,
            column: 41
          }
        },
        loc: {
          start: {
            line: 204,
            column: 45
          },
          end: {
            line: 204,
            column: 64
          }
        },
        line: 204
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 205,
            column: 40
          },
          end: {
            line: 205,
            column: 41
          }
        },
        loc: {
          start: {
            line: 205,
            column: 45
          },
          end: {
            line: 205,
            column: 64
          }
        },
        line: 205
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 206,
            column: 43
          },
          end: {
            line: 206,
            column: 44
          }
        },
        loc: {
          start: {
            line: 206,
            column: 48
          },
          end: {
            line: 206,
            column: 98
          }
        },
        line: 206
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 211,
            column: 48
          },
          end: {
            line: 211,
            column: 49
          }
        },
        loc: {
          start: {
            line: 211,
            column: 53
          },
          end: {
            line: 211,
            column: 75
          }
        },
        line: 211
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 213,
            column: 56
          },
          end: {
            line: 213,
            column: 57
          }
        },
        loc: {
          start: {
            line: 213,
            column: 75
          },
          end: {
            line: 216,
            column: 13
          }
        },
        line: 213
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        loc: {
          start: {
            line: 224,
            column: 23
          },
          end: {
            line: 246,
            column: 5
          }
        },
        line: 224
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 250,
            column: 5
          }
        },
        loc: {
          start: {
            line: 250,
            column: 39
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 250
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 263,
            column: 5
          }
        },
        loc: {
          start: {
            line: 263,
            column: 29
          },
          end: {
            line: 284,
            column: 5
          }
        },
        line: 263
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 288,
            column: 4
          },
          end: {
            line: 288,
            column: 5
          }
        },
        loc: {
          start: {
            line: 288,
            column: 14
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 288
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 18,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 18,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "1": {
        loc: {
          start: {
            line: 24,
            column: 36
          },
          end: {
            line: 24,
            column: 53
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 24,
            column: 45
          },
          end: {
            line: 24,
            column: 53
          }
        }],
        line: 24
      },
      "2": {
        loc: {
          start: {
            line: 26,
            column: 8
          },
          end: {
            line: 42,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 8
          },
          end: {
            line: 42,
            column: 9
          }
        }, {
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 42,
            column: 9
          }
        }],
        line: 26
      },
      "3": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 30,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 30,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "4": {
        loc: {
          start: {
            line: 52,
            column: 8
          },
          end: {
            line: 70,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 8
          },
          end: {
            line: 70,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "5": {
        loc: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 69,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 69,
            column: 13
          }
        }, {
          start: {
            line: 63,
            column: 17
          },
          end: {
            line: 69,
            column: 13
          }
        }],
        line: 53
      },
      "6": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 61,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 61,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "7": {
        loc: {
          start: {
            line: 73,
            column: 59
          },
          end: {
            line: 73,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 73,
            column: 70
          },
          end: {
            line: 73,
            column: 93
          }
        }, {
          start: {
            line: 73,
            column: 96
          },
          end: {
            line: 73,
            column: 98
          }
        }],
        line: 73
      },
      "8": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "9": {
        loc: {
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 102,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 102,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "10": {
        loc: {
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 99,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 99,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "11": {
        loc: {
          start: {
            line: 103,
            column: 79
          },
          end: {
            line: 103,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 103,
            column: 89
          },
          end: {
            line: 103,
            column: 103
          }
        }, {
          start: {
            line: 103,
            column: 106
          },
          end: {
            line: 103,
            column: 108
          }
        }],
        line: 103
      },
      "12": {
        loc: {
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 113,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 113,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "13": {
        loc: {
          start: {
            line: 119,
            column: 15
          },
          end: {
            line: 119,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 15
          },
          end: {
            line: 119,
            column: 43
          }
        }, {
          start: {
            line: 119,
            column: 47
          },
          end: {
            line: 119,
            column: 51
          }
        }],
        line: 119
      },
      "14": {
        loc: {
          start: {
            line: 126,
            column: 15
          },
          end: {
            line: 126,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 26
          },
          end: {
            line: 126,
            column: 41
          }
        }, {
          start: {
            line: 126,
            column: 44
          },
          end: {
            line: 126,
            column: 53
          }
        }],
        line: 126
      },
      "15": {
        loc: {
          start: {
            line: 133,
            column: 15
          },
          end: {
            line: 133,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 26
          },
          end: {
            line: 133,
            column: 88
          }
        }, {
          start: {
            line: 133,
            column: 91
          },
          end: {
            line: 133,
            column: 96
          }
        }],
        line: 133
      },
      "16": {
        loc: {
          start: {
            line: 133,
            column: 26
          },
          end: {
            line: 133,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 26
          },
          end: {
            line: 133,
            column: 55
          }
        }, {
          start: {
            line: 133,
            column: 59
          },
          end: {
            line: 133,
            column: 88
          }
        }],
        line: 133
      },
      "17": {
        loc: {
          start: {
            line: 139,
            column: 72
          },
          end: {
            line: 139,
            column: 134
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 72
          },
          end: {
            line: 139,
            column: 101
          }
        }, {
          start: {
            line: 139,
            column: 105
          },
          end: {
            line: 139,
            column: 134
          }
        }],
        line: 139
      },
      "18": {
        loc: {
          start: {
            line: 152,
            column: 15
          },
          end: {
            line: 152,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 152,
            column: 26
          },
          end: {
            line: 152,
            column: 44
          }
        }, {
          start: {
            line: 152,
            column: 47
          },
          end: {
            line: 152,
            column: 49
          }
        }],
        line: 152
      },
      "19": {
        loc: {
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 169,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 169,
            column: 9
          }
        }, {
          start: {
            line: 167,
            column: 13
          },
          end: {
            line: 169,
            column: 9
          }
        }],
        line: 159
      },
      "20": {
        loc: {
          start: {
            line: 170,
            column: 48
          },
          end: {
            line: 170,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 170,
            column: 59
          },
          end: {
            line: 170,
            column: 68
          }
        }, {
          start: {
            line: 170,
            column: 71
          },
          end: {
            line: 170,
            column: 80
          }
        }],
        line: 170
      },
      "21": {
        loc: {
          start: {
            line: 176,
            column: 73
          },
          end: {
            line: 176,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 73
          },
          end: {
            line: 176,
            column: 113
          }
        }, {
          start: {
            line: 176,
            column: 117
          },
          end: {
            line: 176,
            column: 132
          }
        }],
        line: 176
      },
      "22": {
        loc: {
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "23": {
        loc: {
          start: {
            line: 193,
            column: 27
          },
          end: {
            line: 193,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 193,
            column: 40
          },
          end: {
            line: 193,
            column: 42
          }
        }],
        line: 193
      },
      "24": {
        loc: {
          start: {
            line: 203,
            column: 47
          },
          end: {
            line: 203,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 203,
            column: 47
          },
          end: {
            line: 203,
            column: 68
          }
        }, {
          start: {
            line: 203,
            column: 72
          },
          end: {
            line: 203,
            column: 94
          }
        }],
        line: 203
      },
      "25": {
        loc: {
          start: {
            line: 206,
            column: 48
          },
          end: {
            line: 206,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 48
          },
          end: {
            line: 206,
            column: 70
          }
        }, {
          start: {
            line: 206,
            column: 74
          },
          end: {
            line: 206,
            column: 98
          }
        }],
        line: 206
      },
      "26": {
        loc: {
          start: {
            line: 212,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "27": {
        loc: {
          start: {
            line: 231,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "28": {
        loc: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 231,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 231,
            column: 50
          }
        }, {
          start: {
            line: 231,
            column: 54
          },
          end: {
            line: 231,
            column: 83
          }
        }],
        line: 231
      },
      "29": {
        loc: {
          start: {
            line: 241,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "30": {
        loc: {
          start: {
            line: 272,
            column: 16
          },
          end: {
            line: 277,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 16
          },
          end: {
            line: 277,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "31": {
        loc: {
          start: {
            line: 274,
            column: 20
          },
          end: {
            line: 276,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 20
          },
          end: {
            line: 276,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "32": {
        loc: {
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 291,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 8
          },
          end: {
            line: 291,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 289
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\presenceService.ts",
      mappings: ";;;AACA,qDAA4C;AAC5C,4CAAyC;AAqBzC,MAAa,eAAe;IAM1B;QAJQ,gBAAW,GAA8B,IAAI,GAAG,EAAE,CAAC;QACnD,mBAAc,GAA8B,IAAI,GAAG,EAAE,CAAC,CAAC,6BAA6B;QAI1F,wCAAwC;QACxC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,mBAAmB;IAChC,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,MAAc,EAAE,QAAgB,EAAE,SAAqC,QAAQ;QAClG,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEtD,IAAI,gBAAgB,EAAE,CAAC;YACrB,uCAAuC;YACvC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnD,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YACD,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;YACjC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,6BAA6B;YAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE;gBAC3B,MAAM;gBACN,MAAM;gBACN,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,SAAS,EAAE,CAAC,QAAQ,CAAC;aACtB,CAAC,CAAC;QACL,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAElC,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,WAAW,MAAM,gBAAgB,QAAQ,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAc,EAAE,QAAiB;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,QAAQ,EAAE,CAAC;gBACb,yBAAyB;gBACzB,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;gBAEtE,kCAAkC;gBAClC,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;oBAC5B,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC/B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC5B,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC/B,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;gBACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,oCAAoC;QACpC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEnC,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,eAAe,QAAQ,CAAC,CAAC,CAAC,YAAY,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,MAAc,EAAE,MAAkC;QACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;YACzB,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,sBAAsB,MAAM,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,eAAe,CACpB,MAAc,EACd,YAAwE,EACxE,OAAgB;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,QAAQ,GAAQ;gBACpB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;YAC7B,CAAC;YACD,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC;YACpC,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,oBAAoB,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,MAAc;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC,eAAe,CAAC;YAChC,QAAQ,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,MAAc;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,MAAc;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAc;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3F,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAC7D,QAAQ,CAAC,MAAM,KAAK,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAC/D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,MAAc;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,MAAc,EAAE,cAAsB,EAAE,QAAiB;QAC9E,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,cAAc,EAAE,CAAC;QAE1C,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3B,MAAM;gBACN,cAAc;gBACd,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,2BAA2B,cAAc,EAAE,CAAC,CAAC;IAC9G,CAAC;IAED;;OAEG;IACI,4BAA4B,CAAC,cAAsB;QACxD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CACpD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,cAAc,KAAK,cAAc,IAAI,MAAM,CAAC,QAAQ,CACtE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc;QACzC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1D,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC7B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,aAAqB,EAAE;QACnD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEjE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAC7D,QAAQ,CAAC,QAAQ,IAAI,UAAU,CAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB;QAQrB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG;YACZ,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM;YAC1F,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;YAC5D,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;YAC5D,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,MAAM;YAC9F,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YACrC,sBAAsB,EAAE,CAAC;SAC1B,CAAC;QAEF,kDAAkD;QAClD,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACjE,OAAO,GAAG,GAAG,eAAe,CAAC;YAC/B,CAAC,EAAE,CAAC,CAAC,CAAC;YACN,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,cAAc,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,aAAa;QAC7G,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;QAClD,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;QAEhD,+BAA+B;QAC/B,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,MAAM,iBAAiB,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAEtE,IAAI,iBAAiB,GAAG,cAAc,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACxE,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC5B,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;gBACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAClC,eAAM,CAAC,KAAK,CAAC,eAAe,MAAM,+BAA+B,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,KAAK,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAE1E,IAAI,gBAAgB,GAAG,eAAe,EAAE,CAAC;gBACvC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAChC,eAAM,CAAC,KAAK,CAAC,wCAAwC,YAAY,CAAC,MAAM,oBAAoB,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7H,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAC/C,IAAI,CAAC;YACH,MAAM,iBAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBACnC,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,MAAM,iBAAI,CAAC,IAAI,CAAC;gBAC1C,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;aAC9D,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,CAAC;YAErC,uBAAuB;YACvB,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;gBACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3D,IAAI,QAAQ,EAAE,CAAC;oBACb,qDAAqD;oBACrD,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBAC1C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;oBACxC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,4BAA4B,mBAAmB,CAAC,MAAM,QAAQ,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;YAC5B,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;CACF;AAzWD,0CAyWC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\presenceService.ts"],
      sourcesContent: ["\r\nimport { User } from '../models/User.model';\r\nimport { logger } from '../utils/logger';\r\n\r\nexport interface UserPresence {\r\n  userId: string;\r\n  status: 'online' | 'away' | 'busy' | 'offline';\r\n  lastSeen: Date;\r\n  socketIds: string[];\r\n  currentActivity?: {\r\n    type: 'browsing' | 'messaging' | 'viewing_property' | 'matching';\r\n    details?: string;\r\n    startedAt: Date;\r\n  };\r\n}\r\n\r\nexport interface TypingStatus {\r\n  userId: string;\r\n  conversationId: string;\r\n  isTyping: boolean;\r\n  startedAt: Date;\r\n}\r\n\r\nexport class PresenceService {\r\n  private static instance: PresenceService;\r\n  private onlineUsers: Map<string, UserPresence> = new Map();\r\n  private typingStatuses: Map<string, TypingStatus> = new Map(); // key: userId_conversationId\r\n  private cleanupInterval: NodeJS.Timeout;\r\n\r\n  constructor() {\r\n    // Start cleanup interval for stale data\r\n    this.cleanupInterval = setInterval(() => {\r\n      this.cleanupStaleData();\r\n    }, 30000); // Every 30 seconds\r\n  }\r\n\r\n  public static getInstance(): PresenceService {\r\n    if (!PresenceService.instance) {\r\n      PresenceService.instance = new PresenceService();\r\n    }\r\n    return PresenceService.instance;\r\n  }\r\n\r\n  /**\r\n   * Set user online status\r\n   */\r\n  public setUserOnline(userId: string, socketId: string, status: 'online' | 'away' | 'busy' = 'online'): void {\r\n    const existingPresence = this.onlineUsers.get(userId);\r\n    \r\n    if (existingPresence) {\r\n      // Add socket ID if not already present\r\n      if (!existingPresence.socketIds.includes(socketId)) {\r\n        existingPresence.socketIds.push(socketId);\r\n      }\r\n      existingPresence.status = status;\r\n      existingPresence.lastSeen = new Date();\r\n    } else {\r\n      // Create new presence record\r\n      this.onlineUsers.set(userId, {\r\n        userId,\r\n        status,\r\n        lastSeen: new Date(),\r\n        socketIds: [socketId]\r\n      });\r\n    }\r\n\r\n    // Update user's last active time in database\r\n    this.updateUserLastActive(userId);\r\n\r\n    logger.debug(`User ${userId} set to ${status} with socket ${socketId}`);\r\n  }\r\n\r\n  /**\r\n   * Set user offline\r\n   */\r\n  public setUserOffline(userId: string, socketId?: string): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      if (socketId) {\r\n        // Remove specific socket\r\n        presence.socketIds = presence.socketIds.filter(id => id !== socketId);\r\n        \r\n        // If no more sockets, set offline\r\n        if (presence.socketIds.length === 0) {\r\n          presence.status = 'offline';\r\n          presence.lastSeen = new Date();\r\n          this.updateUserLastActive(userId);\r\n        }\r\n      } else {\r\n        // Remove all sockets and set offline\r\n        presence.status = 'offline';\r\n        presence.lastSeen = new Date();\r\n        presence.socketIds = [];\r\n        this.updateUserLastActive(userId);\r\n      }\r\n    }\r\n\r\n    // Clear typing status for this user\r\n    this.clearUserTypingStatus(userId);\r\n\r\n    logger.debug(`User ${userId} set offline${socketId ? ` (socket ${socketId})` : ''}`);\r\n  }\r\n\r\n  /**\r\n   * Update user status\r\n   */\r\n  public updateUserStatus(userId: string, status: 'online' | 'away' | 'busy'): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      presence.status = status;\r\n      presence.lastSeen = new Date();\r\n      this.updateUserLastActive(userId);\r\n    }\r\n\r\n    logger.debug(`User ${userId} status updated to ${status}`);\r\n  }\r\n\r\n  /**\r\n   * Set user activity\r\n   */\r\n  public setUserActivity(\r\n    userId: string, \r\n    activityType: 'browsing' | 'messaging' | 'viewing_property' | 'matching',\r\n    details?: string\r\n  ): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      const activity: any = {\r\n        type: activityType,\r\n        startedAt: new Date()\r\n      };\r\n      if (details) {\r\n        activity.details = details;\r\n      }\r\n      presence.currentActivity = activity;\r\n      presence.lastSeen = new Date();\r\n    }\r\n\r\n    logger.debug(`User ${userId} activity set to ${activityType}${details ? `: ${details}` : ''}`);\r\n  }\r\n\r\n  /**\r\n   * Clear user activity\r\n   */\r\n  public clearUserActivity(userId: string): void {\r\n    const presence = this.onlineUsers.get(userId);\r\n    \r\n    if (presence) {\r\n      delete presence.currentActivity;\r\n      presence.lastSeen = new Date();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user presence\r\n   */\r\n  public getUserPresence(userId: string): UserPresence | null {\r\n    return this.onlineUsers.get(userId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get user status\r\n   */\r\n  public getUserStatus(userId: string): 'online' | 'away' | 'busy' | 'offline' {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.status : 'offline';\r\n  }\r\n\r\n  /**\r\n   * Check if user is online\r\n   */\r\n  public isUserOnline(userId: string): boolean {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.status !== 'offline' && presence.socketIds.length > 0 : false;\r\n  }\r\n\r\n  /**\r\n   * Get all online users\r\n   */\r\n  public getOnlineUsers(): UserPresence[] {\r\n    return Array.from(this.onlineUsers.values()).filter(presence => \r\n      presence.status !== 'offline' && presence.socketIds.length > 0\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get online users count\r\n   */\r\n  public getOnlineUsersCount(): number {\r\n    return this.getOnlineUsers().length;\r\n  }\r\n\r\n  /**\r\n   * Get user's socket IDs\r\n   */\r\n  public getUserSocketIds(userId: string): string[] {\r\n    const presence = this.onlineUsers.get(userId);\r\n    return presence ? presence.socketIds : [];\r\n  }\r\n\r\n  /**\r\n   * Set typing status\r\n   */\r\n  public setTypingStatus(userId: string, conversationId: string, isTyping: boolean): void {\r\n    const key = `${userId}_${conversationId}`;\r\n    \r\n    if (isTyping) {\r\n      this.typingStatuses.set(key, {\r\n        userId,\r\n        conversationId,\r\n        isTyping: true,\r\n        startedAt: new Date()\r\n      });\r\n    } else {\r\n      this.typingStatuses.delete(key);\r\n    }\r\n\r\n    logger.debug(`User ${userId} ${isTyping ? 'started' : 'stopped'} typing in conversation ${conversationId}`);\r\n  }\r\n\r\n  /**\r\n   * Get typing status for conversation\r\n   */\r\n  public getTypingUsersInConversation(conversationId: string): TypingStatus[] {\r\n    return Array.from(this.typingStatuses.values()).filter(\r\n      status => status.conversationId === conversationId && status.isTyping\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear all typing status for a user\r\n   */\r\n  public clearUserTypingStatus(userId: string): void {\r\n    const keysToDelete: string[] = [];\r\n    \r\n    for (const [key, status] of this.typingStatuses.entries()) {\r\n      if (status.userId === userId) {\r\n        keysToDelete.push(key);\r\n      }\r\n    }\r\n    \r\n    keysToDelete.forEach(key => this.typingStatuses.delete(key));\r\n  }\r\n\r\n  /**\r\n   * Get users who were recently online\r\n   */\r\n  public getRecentlyOnlineUsers(minutesAgo: number = 15): UserPresence[] {\r\n    const cutoffTime = new Date(Date.now() - minutesAgo * 60 * 1000);\r\n    \r\n    return Array.from(this.onlineUsers.values()).filter(presence => \r\n      presence.lastSeen >= cutoffTime\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get presence statistics\r\n   */\r\n  public getPresenceStats(): {\r\n    totalOnline: number;\r\n    totalAway: number;\r\n    totalBusy: number;\r\n    totalOffline: number;\r\n    totalTyping: number;\r\n    averageSessionDuration: number;\r\n  } {\r\n    const presences = Array.from(this.onlineUsers.values());\r\n    \r\n    const stats = {\r\n      totalOnline: presences.filter(p => p.status === 'online' && p.socketIds.length > 0).length,\r\n      totalAway: presences.filter(p => p.status === 'away').length,\r\n      totalBusy: presences.filter(p => p.status === 'busy').length,\r\n      totalOffline: presences.filter(p => p.status === 'offline' || p.socketIds.length === 0).length,\r\n      totalTyping: this.typingStatuses.size,\r\n      averageSessionDuration: 0\r\n    };\r\n\r\n    // Calculate average session duration (simplified)\r\n    const activeSessions = presences.filter(p => p.socketIds.length > 0);\r\n    if (activeSessions.length > 0) {\r\n      const totalDuration = activeSessions.reduce((sum, presence) => {\r\n        const sessionDuration = Date.now() - presence.lastSeen.getTime();\r\n        return sum + sessionDuration;\r\n      }, 0);\r\n      stats.averageSessionDuration = Math.round(totalDuration / activeSessions.length / 1000 / 60); // in minutes\r\n    }\r\n\r\n    return stats;\r\n  }\r\n\r\n  /**\r\n   * Cleanup stale data\r\n   */\r\n  private cleanupStaleData(): void {\r\n    const now = new Date();\r\n    const staleThreshold = 5 * 60 * 1000; // 5 minutes\r\n    const typingThreshold = 30 * 1000; // 30 seconds\r\n\r\n    // Clean up stale presence data\r\n    for (const [userId, presence] of this.onlineUsers.entries()) {\r\n      const timeSinceLastSeen = now.getTime() - presence.lastSeen.getTime();\r\n      \r\n      if (timeSinceLastSeen > staleThreshold && presence.status !== 'offline') {\r\n        presence.status = 'offline';\r\n        presence.socketIds = [];\r\n        this.updateUserLastActive(userId);\r\n        logger.debug(`Marked user ${userId} as offline due to inactivity`);\r\n      }\r\n    }\r\n\r\n    // Clean up stale typing indicators\r\n    for (const [key, typingStatus] of this.typingStatuses.entries()) {\r\n      const timeSinceStarted = now.getTime() - typingStatus.startedAt.getTime();\r\n      \r\n      if (timeSinceStarted > typingThreshold) {\r\n        this.typingStatuses.delete(key);\r\n        logger.debug(`Cleared stale typing status for user ${typingStatus.userId} in conversation ${typingStatus.conversationId}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update user's last active time in database\r\n   */\r\n  private async updateUserLastActive(userId: string): Promise<void> {\r\n    try {\r\n      await User.findByIdAndUpdate(userId, {\r\n        lastActiveAt: new Date()\r\n      });\r\n    } catch (error) {\r\n      logger.error(`Error updating last active time for user ${userId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Bulk update user statuses from database\r\n   */\r\n  public async syncWithDatabase(): Promise<void> {\r\n    try {\r\n      // Get users who were active in the last hour\r\n      const recentlyActiveUsers = await User.find({\r\n        lastActiveAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }\r\n      }).select('_id lastActiveAt').lean();\r\n\r\n      // Update presence data\r\n      for (const user of recentlyActiveUsers) {\r\n        const presence = this.onlineUsers.get(user._id.toString());\r\n        if (presence) {\r\n          // Update last seen from database if it's more recent\r\n          if (user.lastActiveAt > presence.lastSeen) {\r\n            presence.lastSeen = user.lastActiveAt;\r\n          }\r\n        }\r\n      }\r\n\r\n      logger.debug(`Synced presence data for ${recentlyActiveUsers.length} users`);\r\n    } catch (error) {\r\n      logger.error('Error syncing presence with database:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup on service shutdown\r\n   */\r\n  public cleanup(): void {\r\n    if (this.cleanupInterval) {\r\n      clearInterval(this.cleanupInterval);\r\n    }\r\n    \r\n    // Mark all users as offline\r\n    for (const [userId, presence] of this.onlineUsers.entries()) {\r\n      presence.status = 'offline';\r\n      presence.socketIds = [];\r\n      this.updateUserLastActive(userId);\r\n    }\r\n    \r\n    this.onlineUsers.clear();\r\n    this.typingStatuses.clear();\r\n    \r\n    logger.info('Presence service cleaned up');\r\n  }\r\n}\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5a923655188a239ee8cf5260c6790d2e2f356aea"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_3ygeh07az = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_3ygeh07az();
cov_3ygeh07az().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_3ygeh07az().s[1]++;
exports.PresenceService = void 0;
const User_model_1 =
/* istanbul ignore next */
(cov_3ygeh07az().s[2]++, require("../models/User.model"));
const logger_1 =
/* istanbul ignore next */
(cov_3ygeh07az().s[3]++, require("../utils/logger"));
class PresenceService {
  constructor() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[0]++;
    cov_3ygeh07az().s[4]++;
    this.onlineUsers = new Map();
    /* istanbul ignore next */
    cov_3ygeh07az().s[5]++;
    this.typingStatuses = new Map(); // key: userId_conversationId
    // Start cleanup interval for stale data
    /* istanbul ignore next */
    cov_3ygeh07az().s[6]++;
    this.cleanupInterval = setInterval(() => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[1]++;
      cov_3ygeh07az().s[7]++;
      this.cleanupStaleData();
    }, 30000); // Every 30 seconds
  }
  static getInstance() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[2]++;
    cov_3ygeh07az().s[8]++;
    if (!PresenceService.instance) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[0][0]++;
      cov_3ygeh07az().s[9]++;
      PresenceService.instance = new PresenceService();
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[0][1]++;
    }
    cov_3ygeh07az().s[10]++;
    return PresenceService.instance;
  }
  /**
   * Set user online status
   */
  setUserOnline(userId, socketId, status =
  /* istanbul ignore next */
  (cov_3ygeh07az().b[1][0]++, 'online')) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[3]++;
    const existingPresence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[11]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[12]++;
    if (existingPresence) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[2][0]++;
      cov_3ygeh07az().s[13]++;
      // Add socket ID if not already present
      if (!existingPresence.socketIds.includes(socketId)) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[3][0]++;
        cov_3ygeh07az().s[14]++;
        existingPresence.socketIds.push(socketId);
      } else
      /* istanbul ignore next */
      {
        cov_3ygeh07az().b[3][1]++;
      }
      cov_3ygeh07az().s[15]++;
      existingPresence.status = status;
      /* istanbul ignore next */
      cov_3ygeh07az().s[16]++;
      existingPresence.lastSeen = new Date();
    } else {
      /* istanbul ignore next */
      cov_3ygeh07az().b[2][1]++;
      cov_3ygeh07az().s[17]++;
      // Create new presence record
      this.onlineUsers.set(userId, {
        userId,
        status,
        lastSeen: new Date(),
        socketIds: [socketId]
      });
    }
    // Update user's last active time in database
    /* istanbul ignore next */
    cov_3ygeh07az().s[18]++;
    this.updateUserLastActive(userId);
    /* istanbul ignore next */
    cov_3ygeh07az().s[19]++;
    logger_1.logger.debug(`User ${userId} set to ${status} with socket ${socketId}`);
  }
  /**
   * Set user offline
   */
  setUserOffline(userId, socketId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[4]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[20]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[21]++;
    if (presence) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[4][0]++;
      cov_3ygeh07az().s[22]++;
      if (socketId) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[5][0]++;
        cov_3ygeh07az().s[23]++;
        // Remove specific socket
        presence.socketIds = presence.socketIds.filter(id => {
          /* istanbul ignore next */
          cov_3ygeh07az().f[5]++;
          cov_3ygeh07az().s[24]++;
          return id !== socketId;
        });
        // If no more sockets, set offline
        /* istanbul ignore next */
        cov_3ygeh07az().s[25]++;
        if (presence.socketIds.length === 0) {
          /* istanbul ignore next */
          cov_3ygeh07az().b[6][0]++;
          cov_3ygeh07az().s[26]++;
          presence.status = 'offline';
          /* istanbul ignore next */
          cov_3ygeh07az().s[27]++;
          presence.lastSeen = new Date();
          /* istanbul ignore next */
          cov_3ygeh07az().s[28]++;
          this.updateUserLastActive(userId);
        } else
        /* istanbul ignore next */
        {
          cov_3ygeh07az().b[6][1]++;
        }
      } else {
        /* istanbul ignore next */
        cov_3ygeh07az().b[5][1]++;
        cov_3ygeh07az().s[29]++;
        // Remove all sockets and set offline
        presence.status = 'offline';
        /* istanbul ignore next */
        cov_3ygeh07az().s[30]++;
        presence.lastSeen = new Date();
        /* istanbul ignore next */
        cov_3ygeh07az().s[31]++;
        presence.socketIds = [];
        /* istanbul ignore next */
        cov_3ygeh07az().s[32]++;
        this.updateUserLastActive(userId);
      }
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[4][1]++;
    }
    // Clear typing status for this user
    cov_3ygeh07az().s[33]++;
    this.clearUserTypingStatus(userId);
    /* istanbul ignore next */
    cov_3ygeh07az().s[34]++;
    logger_1.logger.debug(`User ${userId} set offline${socketId ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[7][0]++, ` (socket ${socketId})`) :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[7][1]++, '')}`);
  }
  /**
   * Update user status
   */
  updateUserStatus(userId, status) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[6]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[35]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[36]++;
    if (presence) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[8][0]++;
      cov_3ygeh07az().s[37]++;
      presence.status = status;
      /* istanbul ignore next */
      cov_3ygeh07az().s[38]++;
      presence.lastSeen = new Date();
      /* istanbul ignore next */
      cov_3ygeh07az().s[39]++;
      this.updateUserLastActive(userId);
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[8][1]++;
    }
    cov_3ygeh07az().s[40]++;
    logger_1.logger.debug(`User ${userId} status updated to ${status}`);
  }
  /**
   * Set user activity
   */
  setUserActivity(userId, activityType, details) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[7]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[41]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[42]++;
    if (presence) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[9][0]++;
      const activity =
      /* istanbul ignore next */
      (cov_3ygeh07az().s[43]++, {
        type: activityType,
        startedAt: new Date()
      });
      /* istanbul ignore next */
      cov_3ygeh07az().s[44]++;
      if (details) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[10][0]++;
        cov_3ygeh07az().s[45]++;
        activity.details = details;
      } else
      /* istanbul ignore next */
      {
        cov_3ygeh07az().b[10][1]++;
      }
      cov_3ygeh07az().s[46]++;
      presence.currentActivity = activity;
      /* istanbul ignore next */
      cov_3ygeh07az().s[47]++;
      presence.lastSeen = new Date();
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[9][1]++;
    }
    cov_3ygeh07az().s[48]++;
    logger_1.logger.debug(`User ${userId} activity set to ${activityType}${details ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[11][0]++, `: ${details}`) :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[11][1]++, '')}`);
  }
  /**
   * Clear user activity
   */
  clearUserActivity(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[8]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[49]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[50]++;
    if (presence) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[12][0]++;
      cov_3ygeh07az().s[51]++;
      delete presence.currentActivity;
      /* istanbul ignore next */
      cov_3ygeh07az().s[52]++;
      presence.lastSeen = new Date();
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[12][1]++;
    }
  }
  /**
   * Get user presence
   */
  getUserPresence(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[9]++;
    cov_3ygeh07az().s[53]++;
    return /* istanbul ignore next */(cov_3ygeh07az().b[13][0]++, this.onlineUsers.get(userId)) ||
    /* istanbul ignore next */
    (cov_3ygeh07az().b[13][1]++, null);
  }
  /**
   * Get user status
   */
  getUserStatus(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[10]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[54]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[55]++;
    return presence ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[14][0]++, presence.status) :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[14][1]++, 'offline');
  }
  /**
   * Check if user is online
   */
  isUserOnline(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[11]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[56]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[57]++;
    return presence ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[15][0]++,
    /* istanbul ignore next */
    (cov_3ygeh07az().b[16][0]++, presence.status !== 'offline') &&
    /* istanbul ignore next */
    (cov_3ygeh07az().b[16][1]++, presence.socketIds.length > 0)) :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[15][1]++, false);
  }
  /**
   * Get all online users
   */
  getOnlineUsers() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[12]++;
    cov_3ygeh07az().s[58]++;
    return Array.from(this.onlineUsers.values()).filter(presence => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[13]++;
      cov_3ygeh07az().s[59]++;
      return /* istanbul ignore next */(cov_3ygeh07az().b[17][0]++, presence.status !== 'offline') &&
      /* istanbul ignore next */
      (cov_3ygeh07az().b[17][1]++, presence.socketIds.length > 0);
    });
  }
  /**
   * Get online users count
   */
  getOnlineUsersCount() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[14]++;
    cov_3ygeh07az().s[60]++;
    return this.getOnlineUsers().length;
  }
  /**
   * Get user's socket IDs
   */
  getUserSocketIds(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[15]++;
    const presence =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[61]++, this.onlineUsers.get(userId));
    /* istanbul ignore next */
    cov_3ygeh07az().s[62]++;
    return presence ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[18][0]++, presence.socketIds) :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[18][1]++, []);
  }
  /**
   * Set typing status
   */
  setTypingStatus(userId, conversationId, isTyping) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[16]++;
    const key =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[63]++, `${userId}_${conversationId}`);
    /* istanbul ignore next */
    cov_3ygeh07az().s[64]++;
    if (isTyping) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[19][0]++;
      cov_3ygeh07az().s[65]++;
      this.typingStatuses.set(key, {
        userId,
        conversationId,
        isTyping: true,
        startedAt: new Date()
      });
    } else {
      /* istanbul ignore next */
      cov_3ygeh07az().b[19][1]++;
      cov_3ygeh07az().s[66]++;
      this.typingStatuses.delete(key);
    }
    /* istanbul ignore next */
    cov_3ygeh07az().s[67]++;
    logger_1.logger.debug(`User ${userId} ${isTyping ?
    /* istanbul ignore next */
    (cov_3ygeh07az().b[20][0]++, 'started') :
    /* istanbul ignore next */
    (cov_3ygeh07az().b[20][1]++, 'stopped')} typing in conversation ${conversationId}`);
  }
  /**
   * Get typing status for conversation
   */
  getTypingUsersInConversation(conversationId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[17]++;
    cov_3ygeh07az().s[68]++;
    return Array.from(this.typingStatuses.values()).filter(status => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[18]++;
      cov_3ygeh07az().s[69]++;
      return /* istanbul ignore next */(cov_3ygeh07az().b[21][0]++, status.conversationId === conversationId) &&
      /* istanbul ignore next */
      (cov_3ygeh07az().b[21][1]++, status.isTyping);
    });
  }
  /**
   * Clear all typing status for a user
   */
  clearUserTypingStatus(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[19]++;
    const keysToDelete =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[70]++, []);
    /* istanbul ignore next */
    cov_3ygeh07az().s[71]++;
    for (const [key, status] of this.typingStatuses.entries()) {
      /* istanbul ignore next */
      cov_3ygeh07az().s[72]++;
      if (status.userId === userId) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[22][0]++;
        cov_3ygeh07az().s[73]++;
        keysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_3ygeh07az().b[22][1]++;
      }
    }
    /* istanbul ignore next */
    cov_3ygeh07az().s[74]++;
    keysToDelete.forEach(key => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[20]++;
      cov_3ygeh07az().s[75]++;
      return this.typingStatuses.delete(key);
    });
  }
  /**
   * Get users who were recently online
   */
  getRecentlyOnlineUsers(minutesAgo =
  /* istanbul ignore next */
  (cov_3ygeh07az().b[23][0]++, 15)) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[21]++;
    const cutoffTime =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[76]++, new Date(Date.now() - minutesAgo * 60 * 1000));
    /* istanbul ignore next */
    cov_3ygeh07az().s[77]++;
    return Array.from(this.onlineUsers.values()).filter(presence => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[22]++;
      cov_3ygeh07az().s[78]++;
      return presence.lastSeen >= cutoffTime;
    });
  }
  /**
   * Get presence statistics
   */
  getPresenceStats() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[23]++;
    const presences =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[79]++, Array.from(this.onlineUsers.values()));
    const stats =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[80]++, {
      totalOnline: presences.filter(p => {
        /* istanbul ignore next */
        cov_3ygeh07az().f[24]++;
        cov_3ygeh07az().s[81]++;
        return /* istanbul ignore next */(cov_3ygeh07az().b[24][0]++, p.status === 'online') &&
        /* istanbul ignore next */
        (cov_3ygeh07az().b[24][1]++, p.socketIds.length > 0);
      }).length,
      totalAway: presences.filter(p => {
        /* istanbul ignore next */
        cov_3ygeh07az().f[25]++;
        cov_3ygeh07az().s[82]++;
        return p.status === 'away';
      }).length,
      totalBusy: presences.filter(p => {
        /* istanbul ignore next */
        cov_3ygeh07az().f[26]++;
        cov_3ygeh07az().s[83]++;
        return p.status === 'busy';
      }).length,
      totalOffline: presences.filter(p => {
        /* istanbul ignore next */
        cov_3ygeh07az().f[27]++;
        cov_3ygeh07az().s[84]++;
        return /* istanbul ignore next */(cov_3ygeh07az().b[25][0]++, p.status === 'offline') ||
        /* istanbul ignore next */
        (cov_3ygeh07az().b[25][1]++, p.socketIds.length === 0);
      }).length,
      totalTyping: this.typingStatuses.size,
      averageSessionDuration: 0
    });
    // Calculate average session duration (simplified)
    const activeSessions =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[85]++, presences.filter(p => {
      /* istanbul ignore next */
      cov_3ygeh07az().f[28]++;
      cov_3ygeh07az().s[86]++;
      return p.socketIds.length > 0;
    }));
    /* istanbul ignore next */
    cov_3ygeh07az().s[87]++;
    if (activeSessions.length > 0) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[26][0]++;
      const totalDuration =
      /* istanbul ignore next */
      (cov_3ygeh07az().s[88]++, activeSessions.reduce((sum, presence) => {
        /* istanbul ignore next */
        cov_3ygeh07az().f[29]++;
        const sessionDuration =
        /* istanbul ignore next */
        (cov_3ygeh07az().s[89]++, Date.now() - presence.lastSeen.getTime());
        /* istanbul ignore next */
        cov_3ygeh07az().s[90]++;
        return sum + sessionDuration;
      }, 0));
      /* istanbul ignore next */
      cov_3ygeh07az().s[91]++;
      stats.averageSessionDuration = Math.round(totalDuration / activeSessions.length / 1000 / 60); // in minutes
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[26][1]++;
    }
    cov_3ygeh07az().s[92]++;
    return stats;
  }
  /**
   * Cleanup stale data
   */
  cleanupStaleData() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[30]++;
    const now =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[93]++, new Date());
    const staleThreshold =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[94]++, 5 * 60 * 1000); // 5 minutes
    const typingThreshold =
    /* istanbul ignore next */
    (cov_3ygeh07az().s[95]++, 30 * 1000); // 30 seconds
    // Clean up stale presence data
    /* istanbul ignore next */
    cov_3ygeh07az().s[96]++;
    for (const [userId, presence] of this.onlineUsers.entries()) {
      const timeSinceLastSeen =
      /* istanbul ignore next */
      (cov_3ygeh07az().s[97]++, now.getTime() - presence.lastSeen.getTime());
      /* istanbul ignore next */
      cov_3ygeh07az().s[98]++;
      if (
      /* istanbul ignore next */
      (cov_3ygeh07az().b[28][0]++, timeSinceLastSeen > staleThreshold) &&
      /* istanbul ignore next */
      (cov_3ygeh07az().b[28][1]++, presence.status !== 'offline')) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[27][0]++;
        cov_3ygeh07az().s[99]++;
        presence.status = 'offline';
        /* istanbul ignore next */
        cov_3ygeh07az().s[100]++;
        presence.socketIds = [];
        /* istanbul ignore next */
        cov_3ygeh07az().s[101]++;
        this.updateUserLastActive(userId);
        /* istanbul ignore next */
        cov_3ygeh07az().s[102]++;
        logger_1.logger.debug(`Marked user ${userId} as offline due to inactivity`);
      } else
      /* istanbul ignore next */
      {
        cov_3ygeh07az().b[27][1]++;
      }
    }
    // Clean up stale typing indicators
    /* istanbul ignore next */
    cov_3ygeh07az().s[103]++;
    for (const [key, typingStatus] of this.typingStatuses.entries()) {
      const timeSinceStarted =
      /* istanbul ignore next */
      (cov_3ygeh07az().s[104]++, now.getTime() - typingStatus.startedAt.getTime());
      /* istanbul ignore next */
      cov_3ygeh07az().s[105]++;
      if (timeSinceStarted > typingThreshold) {
        /* istanbul ignore next */
        cov_3ygeh07az().b[29][0]++;
        cov_3ygeh07az().s[106]++;
        this.typingStatuses.delete(key);
        /* istanbul ignore next */
        cov_3ygeh07az().s[107]++;
        logger_1.logger.debug(`Cleared stale typing status for user ${typingStatus.userId} in conversation ${typingStatus.conversationId}`);
      } else
      /* istanbul ignore next */
      {
        cov_3ygeh07az().b[29][1]++;
      }
    }
  }
  /**
   * Update user's last active time in database
   */
  async updateUserLastActive(userId) {
    /* istanbul ignore next */
    cov_3ygeh07az().f[31]++;
    cov_3ygeh07az().s[108]++;
    try {
      /* istanbul ignore next */
      cov_3ygeh07az().s[109]++;
      await User_model_1.User.findByIdAndUpdate(userId, {
        lastActiveAt: new Date()
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_3ygeh07az().s[110]++;
      logger_1.logger.error(`Error updating last active time for user ${userId}:`, error);
    }
  }
  /**
   * Bulk update user statuses from database
   */
  async syncWithDatabase() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[32]++;
    cov_3ygeh07az().s[111]++;
    try {
      // Get users who were active in the last hour
      const recentlyActiveUsers =
      /* istanbul ignore next */
      (cov_3ygeh07az().s[112]++, await User_model_1.User.find({
        lastActiveAt: {
          $gte: new Date(Date.now() - 60 * 60 * 1000)
        }
      }).select('_id lastActiveAt').lean());
      // Update presence data
      /* istanbul ignore next */
      cov_3ygeh07az().s[113]++;
      for (const user of recentlyActiveUsers) {
        const presence =
        /* istanbul ignore next */
        (cov_3ygeh07az().s[114]++, this.onlineUsers.get(user._id.toString()));
        /* istanbul ignore next */
        cov_3ygeh07az().s[115]++;
        if (presence) {
          /* istanbul ignore next */
          cov_3ygeh07az().b[30][0]++;
          cov_3ygeh07az().s[116]++;
          // Update last seen from database if it's more recent
          if (user.lastActiveAt > presence.lastSeen) {
            /* istanbul ignore next */
            cov_3ygeh07az().b[31][0]++;
            cov_3ygeh07az().s[117]++;
            presence.lastSeen = user.lastActiveAt;
          } else
          /* istanbul ignore next */
          {
            cov_3ygeh07az().b[31][1]++;
          }
        } else
        /* istanbul ignore next */
        {
          cov_3ygeh07az().b[30][1]++;
        }
      }
      /* istanbul ignore next */
      cov_3ygeh07az().s[118]++;
      logger_1.logger.debug(`Synced presence data for ${recentlyActiveUsers.length} users`);
    } catch (error) {
      /* istanbul ignore next */
      cov_3ygeh07az().s[119]++;
      logger_1.logger.error('Error syncing presence with database:', error);
    }
  }
  /**
   * Cleanup on service shutdown
   */
  cleanup() {
    /* istanbul ignore next */
    cov_3ygeh07az().f[33]++;
    cov_3ygeh07az().s[120]++;
    if (this.cleanupInterval) {
      /* istanbul ignore next */
      cov_3ygeh07az().b[32][0]++;
      cov_3ygeh07az().s[121]++;
      clearInterval(this.cleanupInterval);
    } else
    /* istanbul ignore next */
    {
      cov_3ygeh07az().b[32][1]++;
    }
    // Mark all users as offline
    cov_3ygeh07az().s[122]++;
    for (const [userId, presence] of this.onlineUsers.entries()) {
      /* istanbul ignore next */
      cov_3ygeh07az().s[123]++;
      presence.status = 'offline';
      /* istanbul ignore next */
      cov_3ygeh07az().s[124]++;
      presence.socketIds = [];
      /* istanbul ignore next */
      cov_3ygeh07az().s[125]++;
      this.updateUserLastActive(userId);
    }
    /* istanbul ignore next */
    cov_3ygeh07az().s[126]++;
    this.onlineUsers.clear();
    /* istanbul ignore next */
    cov_3ygeh07az().s[127]++;
    this.typingStatuses.clear();
    /* istanbul ignore next */
    cov_3ygeh07az().s[128]++;
    logger_1.logger.info('Presence service cleaned up');
  }
}
/* istanbul ignore next */
cov_3ygeh07az().s[129]++;
exports.PresenceService = PresenceService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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