name: Automated Testing Pipeline

on:
  push:
    branches: [ main, development, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, development ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18.x'
  MONGODB_VERSION: '6.0'
  REDIS_VERSION: '7.0'

jobs:
  # Detect changes to determine which tests to run
  changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.changes.outputs.backend }}
      tests: ${{ steps.changes.outputs.tests }}
      docs: ${{ steps.changes.outputs.docs }}
      config: ${{ steps.changes.outputs.config }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Detect changes
      uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          backend:
            - 'src/**'
            - 'package*.json'
            - 'tsconfig.json'
          tests:
            - 'tests/**'
            - 'jest.config.js'
          docs:
            - 'docs/**'
            - '*.md'
          config:
            - '.github/**'
            - '.eslintrc.js'
            - '.prettierrc.js'
            - 'commitlint.config.js'

  # Fast feedback loop - quick checks
  quick-checks:
    name: Quick Checks
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.backend == 'true' || needs.changes.outputs.tests == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: TypeScript check
      run: npm run build

    - name: Lint check
      run: npm run lint

    - name: Format check
      run: npm run format:check

  # Unit tests with matrix strategy
  unit-tests:
    name: Unit Tests
    runs-on: ${{ matrix.os }}
    needs: [changes, quick-checks]
    if: needs.changes.outputs.backend == 'true' || needs.changes.outputs.tests == 'true'
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: ['16.x', '18.x', '20.x']
        exclude:
          # Reduce matrix size for faster execution
          - os: windows-latest
            node-version: '16.x'
          - os: macos-latest
            node-version: '16.x'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run unit tests
      run: npm run test:unit
      env:
        NODE_ENV: test

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: unit-test-results-${{ matrix.os }}-${{ matrix.node-version }}
        path: |
          coverage/
          test-results.xml

  # Integration tests with services
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [changes, quick-checks]
    if: needs.changes.outputs.backend == 'true' || needs.changes.outputs.tests == 'true'
    
    services:
      mongodb:
        image: mongo:${{ env.MONGODB_VERSION }}
        env:
          MONGO_INITDB_ROOT_USERNAME: root
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd mongo
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Wait for services
      run: |
        # Wait for MongoDB
        until mongo --host localhost:27017 --eval "print('MongoDB ready')" > /dev/null 2>&1; do
          echo "Waiting for MongoDB..."
          sleep 2
        done
        
        # Wait for Redis
        until redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; do
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        MONGODB_URI: ************************************************************************
        REDIS_URL: redis://localhost:6379/15

    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: integration-test-results
        path: |
          coverage/
          test-results.xml

  # End-to-end tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [changes, unit-tests, integration-tests]
    if: needs.changes.outputs.backend == 'true' && (github.event_name == 'push' || github.event_name == 'schedule')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run E2E tests
      run: npm run test:e2e
      env:
        NODE_ENV: test

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: e2e-test-results
        path: |
          coverage/
          test-results.xml
          screenshots/

  # Coverage analysis
  coverage-analysis:
    name: Coverage Analysis
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: always() && (needs.unit-tests.result == 'success' || needs.integration-tests.result == 'success')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run coverage tests
      run: npm run test:coverage
      env:
        NODE_ENV: test

    - name: Check coverage requirements
      run: node scripts/coverage-check.js

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: lajospaces-backend
        fail_ci_if_error: false

    - name: Coverage comment on PR
      if: github.event_name == 'pull_request'
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        lcov-file: ./coverage/lcov.info
        delete-old-comments: true

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage/

  # Performance tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [changes, integration-tests]
    if: needs.changes.outputs.backend == 'true' && github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run performance benchmarks
      run: npm run test:performance
      env:
        NODE_ENV: test

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json

  # Security tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.backend == 'true' || needs.changes.outputs.config == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level=moderate

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=medium

  # Test summary and notification
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, coverage-analysis, performance-tests, security-tests]
    if: always()
    
    steps:
    - name: Generate test summary
      run: |
        echo "## Test Pipeline Summary" >> $GITHUB_STEP_SUMMARY
        echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ${{ needs.unit-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Integration Tests | ${{ needs.integration-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| E2E Tests | ${{ needs.e2e-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Coverage Analysis | ${{ needs.coverage-analysis.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Performance Tests | ${{ needs.performance-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Security Tests | ${{ needs.security-tests.result }} |" >> $GITHUB_STEP_SUMMARY

    - name: Notify on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        text: 'Test pipeline failed for LajoSpaces Backend'
