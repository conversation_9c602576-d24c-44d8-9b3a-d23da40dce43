{"version": 3, "names": ["cov_o1hqs8l86", "actualCoverage", "exports", "sanitizeString", "s", "sanitizeEmail", "sanitizePhoneNumber", "validateNigerianData", "detectDangerousPatterns", "sanitizeObject", "sanitizeRequest", "strictSanitization", "lenientSanitization", "emailSanitization", "phoneNumberSanitization", "fileUploadSanitization", "searchQuerySanitization", "nigerianDataValidation", "contentSanitization", "isomorphic_dompurify_1", "__importDefault", "require", "validator_1", "logger_1", "appError_1", "defaultOptions", "allowedTags", "allowedAttributes", "stripTags", "escapeHtml", "normalizeEmail", "trimWhitespace", "removeNullBytes", "max<PERSON><PERSON><PERSON>", "dangerousPatterns", "nigerianPatterns", "phoneNumber", "bankAccount", "bvn", "nin", "postalCode", "value", "options", "b", "f", "String", "opts", "sanitized", "replace", "trim", "length", "substring", "default", "sanitize", "ALLOWED_TAGS", "ALLOWED_ATTR", "KEEP_CONTENT", "escape", "email", "normalize", "toLowerCase", "error", "phone", "startsWith", "type", "pattern", "test", "input", "detected", "push", "source", "obj", "undefined", "Array", "isArray", "map", "item", "key", "Object", "entries", "sanitized<PERSON>ey", "req", "res", "next", "body", "query", "params", "criticalFields", "values", "field", "dangerous", "logger", "warn", "ip", "userAgent", "get", "path", "method", "patterns", "userId", "user", "_id", "AppError", "phoneFields", "file", "originalname", "files", "for<PERSON>ach", "searchFields", "validationRules", "mobile", "accountNumber", "zipCode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\sanitization.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport DOMPurify from 'isomorphic-dompurify';\r\nimport validator from 'validator';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\n// Sanitization options\r\nexport interface SanitizationOptions {\r\n  allowedTags?: string[];\r\n  allowedAttributes?: { [key: string]: string[] };\r\n  stripTags?: boolean;\r\n  escapeHtml?: boolean;\r\n  normalizeEmail?: boolean;\r\n  trimWhitespace?: boolean;\r\n  removeNullBytes?: boolean;\r\n  maxLength?: number;\r\n}\r\n\r\n// Default sanitization options\r\nconst defaultOptions: SanitizationOptions = {\r\n  allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],\r\n  allowedAttributes: {},\r\n  stripTags: true,\r\n  escapeHtml: true,\r\n  normalizeEmail: true,\r\n  trimWhitespace: true,\r\n  removeNullBytes: true,\r\n  maxLength: 10000\r\n};\r\n\r\n// Dangerous patterns to detect and block\r\nconst dangerousPatterns = [\r\n  // SQL Injection patterns\r\n  /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\\b)/gi,\r\n  // XSS patterns\r\n  /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\r\n  /javascript:/gi,\r\n  /on\\w+\\s*=/gi,\r\n  // NoSQL Injection patterns\r\n  /\\$where/gi,\r\n  /\\$ne/gi,\r\n  /\\$gt/gi,\r\n  /\\$lt/gi,\r\n  // Command injection patterns\r\n  /[;&|`$()]/g,\r\n  // Path traversal patterns\r\n  /\\.\\.\\//g,\r\n  /\\.\\.\\\\/g\r\n];\r\n\r\n// Nigerian-specific validation patterns\r\nconst nigerianPatterns = {\r\n  phoneNumber: /^(\\+234|234|0)[789][01]\\d{8}$/,\r\n  bankAccount: /^\\d{10}$/,\r\n  bvn: /^\\d{11}$/,\r\n  nin: /^\\d{11}$/,\r\n  postalCode: /^\\d{6}$/\r\n};\r\n\r\n/**\r\n * Sanitize a string value\r\n */\r\nexport function sanitizeString(value: string, options: SanitizationOptions = {}): string {\r\n  if (typeof value !== 'string') {\r\n    return String(value);\r\n  }\r\n\r\n  const opts = { ...defaultOptions, ...options };\r\n  let sanitized = value;\r\n\r\n  // Remove null bytes\r\n  if (opts.removeNullBytes) {\r\n    sanitized = sanitized.replace(/\\0/g, '');\r\n  }\r\n\r\n  // Trim whitespace\r\n  if (opts.trimWhitespace) {\r\n    sanitized = sanitized.trim();\r\n  }\r\n\r\n  // Check length\r\n  if (opts.maxLength && sanitized.length > opts.maxLength) {\r\n    sanitized = sanitized.substring(0, opts.maxLength);\r\n  }\r\n\r\n  // Strip or escape HTML\r\n  if (opts.stripTags) {\r\n    sanitized = DOMPurify.sanitize(sanitized, {\r\n      ALLOWED_TAGS: opts.allowedTags || [],\r\n      ALLOWED_ATTR: opts.allowedAttributes || {},\r\n      KEEP_CONTENT: true\r\n    });\r\n  } else if (opts.escapeHtml) {\r\n    sanitized = validator.escape(sanitized);\r\n  }\r\n\r\n  return sanitized;\r\n}\r\n\r\n/**\r\n * Sanitize email address\r\n */\r\nexport function sanitizeEmail(email: string, normalize: boolean = true): string {\r\n  if (!email || typeof email !== 'string') {\r\n    return '';\r\n  }\r\n\r\n  let sanitized = email.trim().toLowerCase();\r\n\r\n  if (normalize) {\r\n    try {\r\n      sanitized = validator.normalizeEmail(sanitized) || sanitized;\r\n    } catch (error) {\r\n      // If normalization fails, continue with basic sanitization\r\n    }\r\n  }\r\n\r\n  return sanitized;\r\n}\r\n\r\n/**\r\n * Sanitize phone number (Nigerian format)\r\n */\r\nexport function sanitizePhoneNumber(phone: string): string {\r\n  if (!phone || typeof phone !== 'string') {\r\n    return '';\r\n  }\r\n\r\n  // Remove all non-digit characters except +\r\n  let sanitized = phone.replace(/[^\\d+]/g, '');\r\n\r\n  // Normalize Nigerian phone numbers\r\n  if (sanitized.startsWith('0')) {\r\n    sanitized = '+234' + sanitized.substring(1);\r\n  } else if (sanitized.startsWith('234')) {\r\n    sanitized = '+' + sanitized;\r\n  } else if (!sanitized.startsWith('+234')) {\r\n    // Assume it's a local number without country code\r\n    if (sanitized.length === 10) {\r\n      sanitized = '+234' + sanitized;\r\n    }\r\n  }\r\n\r\n  return sanitized;\r\n}\r\n\r\n/**\r\n * Validate Nigerian-specific data\r\n */\r\nexport function validateNigerianData(type: string, value: string): boolean {\r\n  if (!value || typeof value !== 'string') {\r\n    return false;\r\n  }\r\n\r\n  const pattern = nigerianPatterns[type as keyof typeof nigerianPatterns];\r\n  return pattern ? pattern.test(value) : false;\r\n}\r\n\r\n/**\r\n * Detect dangerous patterns in input\r\n */\r\nexport function detectDangerousPatterns(input: string): string[] {\r\n  const detected: string[] = [];\r\n\r\n  for (const pattern of dangerousPatterns) {\r\n    if (pattern.test(input)) {\r\n      detected.push(pattern.source);\r\n    }\r\n  }\r\n\r\n  return detected;\r\n}\r\n\r\n/**\r\n * Sanitize object recursively\r\n */\r\nexport function sanitizeObject(obj: any, options: SanitizationOptions = {}): any {\r\n  if (obj === null || obj === undefined) {\r\n    return obj;\r\n  }\r\n\r\n  if (typeof obj === 'string') {\r\n    return sanitizeString(obj, options);\r\n  }\r\n\r\n  if (typeof obj === 'number' || typeof obj === 'boolean') {\r\n    return obj;\r\n  }\r\n\r\n  if (Array.isArray(obj)) {\r\n    return obj.map(item => sanitizeObject(item, options));\r\n  }\r\n\r\n  if (typeof obj === 'object') {\r\n    const sanitized: any = {};\r\n    for (const [key, value] of Object.entries(obj)) {\r\n      // Sanitize the key as well\r\n      const sanitizedKey = sanitizeString(key, { stripTags: true, maxLength: 100 });\r\n      sanitized[sanitizedKey] = sanitizeObject(value, options);\r\n    }\r\n    return sanitized;\r\n  }\r\n\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Middleware for request sanitization\r\n */\r\nexport function sanitizeRequest(options: SanitizationOptions = {}) {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    try {\r\n      // Sanitize request body\r\n      if (req.body && typeof req.body === 'object') {\r\n        req.body = sanitizeObject(req.body, options);\r\n      }\r\n\r\n      // Sanitize query parameters\r\n      if (req.query && typeof req.query === 'object') {\r\n        req.query = sanitizeObject(req.query, options);\r\n      }\r\n\r\n      // Sanitize URL parameters\r\n      if (req.params && typeof req.params === 'object') {\r\n        req.params = sanitizeObject(req.params, options);\r\n      }\r\n\r\n      // Check for dangerous patterns in critical fields\r\n      const criticalFields = [\r\n        ...(req.body ? Object.values(req.body) : []),\r\n        ...(req.query ? Object.values(req.query) : []),\r\n        ...(req.params ? Object.values(req.params) : [])\r\n      ];\r\n\r\n      for (const field of criticalFields) {\r\n        if (typeof field === 'string') {\r\n          const dangerous = detectDangerousPatterns(field);\r\n          if (dangerous.length > 0) {\r\n            logger.warn('Dangerous patterns detected in request', {\r\n              ip: req.ip,\r\n              userAgent: req.get('User-Agent'),\r\n              path: req.path,\r\n              method: req.method,\r\n              patterns: dangerous,\r\n              userId: req.user?._id\r\n            });\r\n\r\n            // Optionally block the request\r\n            if (options.stripTags) {\r\n              throw new AppError('Invalid input detected', 400, true, 'INVALID_INPUT');\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      next();\r\n    } catch (error) {\r\n      logger.error('Request sanitization error:', error);\r\n      next(error);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Strict sanitization for sensitive operations\r\n */\r\nexport function strictSanitization() {\r\n  return sanitizeRequest({\r\n    allowedTags: [],\r\n    allowedAttributes: {},\r\n    stripTags: true,\r\n    escapeHtml: true,\r\n    normalizeEmail: true,\r\n    trimWhitespace: true,\r\n    removeNullBytes: true,\r\n    maxLength: 1000\r\n  });\r\n}\r\n\r\n/**\r\n * Lenient sanitization for content that may contain formatting\r\n */\r\nexport function lenientSanitization() {\r\n  return sanitizeRequest({\r\n    allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],\r\n    allowedAttributes: {},\r\n    stripTags: false,\r\n    escapeHtml: false,\r\n    normalizeEmail: true,\r\n    trimWhitespace: true,\r\n    removeNullBytes: true,\r\n    maxLength: 50000\r\n  });\r\n}\r\n\r\n/**\r\n * Email-specific sanitization\r\n */\r\nexport function emailSanitization() {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    if (req.body.email) {\r\n      req.body.email = sanitizeEmail(req.body.email);\r\n    }\r\n    if (req.query.email) {\r\n      req.query.email = sanitizeEmail(req.query.email as string);\r\n    }\r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * Phone number sanitization for Nigerian numbers\r\n */\r\nexport function phoneNumberSanitization() {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    const phoneFields = ['phone', 'phoneNumber', 'mobile', 'contact'];\r\n    \r\n    for (const field of phoneFields) {\r\n      if (req.body[field]) {\r\n        req.body[field] = sanitizePhoneNumber(req.body[field]);\r\n      }\r\n    }\r\n    \r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * File upload sanitization\r\n */\r\nexport function fileUploadSanitization() {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    if (req.file) {\r\n      // Sanitize filename\r\n      req.file.originalname = sanitizeString(req.file.originalname, {\r\n        stripTags: true,\r\n        maxLength: 255\r\n      });\r\n    }\r\n\r\n    if (req.files && Array.isArray(req.files)) {\r\n      req.files.forEach(file => {\r\n        file.originalname = sanitizeString(file.originalname, {\r\n          stripTags: true,\r\n          maxLength: 255\r\n        });\r\n      });\r\n    }\r\n\r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * Search query sanitization\r\n */\r\nexport function searchQuerySanitization() {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    const searchFields = ['q', 'query', 'search', 'term', 'keyword'];\r\n    \r\n    for (const field of searchFields) {\r\n      if (req.query[field]) {\r\n        req.query[field] = sanitizeString(req.query[field] as string, {\r\n          stripTags: true,\r\n          maxLength: 500\r\n        });\r\n      }\r\n    }\r\n    \r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * Nigerian data validation middleware\r\n */\r\nexport function nigerianDataValidation() {\r\n  return (req: Request, res: Response, next: NextFunction) => {\r\n    const validationRules = {\r\n      phoneNumber: 'phoneNumber',\r\n      phone: 'phoneNumber',\r\n      mobile: 'phoneNumber',\r\n      bankAccount: 'bankAccount',\r\n      accountNumber: 'bankAccount',\r\n      bvn: 'bvn',\r\n      nin: 'nin',\r\n      postalCode: 'postalCode',\r\n      zipCode: 'postalCode'\r\n    };\r\n\r\n    for (const [field, type] of Object.entries(validationRules)) {\r\n      if (req.body[field]) {\r\n        const isValid = validateNigerianData(type, req.body[field]);\r\n        if (!isValid) {\r\n          throw new AppError(`Invalid ${field} format for Nigerian data`, 400, true, 'INVALID_FORMAT');\r\n        }\r\n      }\r\n    }\r\n\r\n    next();\r\n  };\r\n}\r\n\r\n/**\r\n * Content sanitization for user-generated content\r\n */\r\nexport function contentSanitization() {\r\n  return sanitizeRequest({\r\n    allowedTags: ['p', 'br', 'b', 'i', 'em', 'strong', 'ul', 'ol', 'li'],\r\n    allowedAttributes: {},\r\n    stripTags: false,\r\n    escapeHtml: false,\r\n    trimWhitespace: true,\r\n    removeNullBytes: true,\r\n    maxLength: 10000\r\n  });\r\n}\r\n\r\nexport default {\r\n  sanitizeString,\r\n  sanitizeEmail,\r\n  sanitizePhoneNumber,\r\n  validateNigerianData,\r\n  detectDangerousPatterns,\r\n  sanitizeObject,\r\n  sanitizeRequest,\r\n  strictSanitization,\r\n  lenientSanitization,\r\n  emailSanitization,\r\n  phoneNumberSanitization,\r\n  fileUploadSanitization,\r\n  searchQuerySanitization,\r\n  nigerianDataValidation,\r\n  contentSanitization\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyTA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3PAE,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAmCC;AAAAH,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAG,aAAA,GAAAA,aAAA;AAgBC;AAAAL,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAI,mBAAA,GAAAA,mBAAA;AAqBC;AAAAN,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAK,oBAAA,GAAAA,oBAAA;AAOC;AAAAP,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAM,uBAAA,GAAAA,uBAAA;AAUC;AAAAR,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAO,cAAA,GAAAA,cAAA;AA4BC;AAAAT,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAQ,eAAA,GAAAA,eAAA;AAoDC;AAAAV,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAS,kBAAA,GAAAA,kBAAA;AAWC;AAAAX,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAU,mBAAA,GAAAA,mBAAA;AAWC;AAAAZ,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAW,iBAAA,GAAAA,iBAAA;AAUC;AAAAb,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAY,uBAAA,GAAAA,uBAAA;AAYC;AAAAd,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAa,sBAAA,GAAAA,sBAAA;AAqBC;AAAAf,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAc,uBAAA,GAAAA,uBAAA;AAeC;AAAAhB,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAe,sBAAA,GAAAA,sBAAA;AAyBC;AAAAjB,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAgB,mBAAA,GAAAA,mBAAA;AArZA,MAAAC,sBAAA;AAAA;AAAA,CAAAnB,aAAA,GAAAI,CAAA,QAAAgB,eAAA,CAAAC,OAAA;AACA,MAAAC,WAAA;AAAA;AAAA,CAAAtB,aAAA,GAAAI,CAAA,QAAAgB,eAAA,CAAAC,OAAA;AACA,MAAAE,QAAA;AAAA;AAAA,CAAAvB,aAAA,GAAAI,CAAA,QAAAiB,OAAA;AACA,MAAAG,UAAA;AAAA;AAAA,CAAAxB,aAAA,GAAAI,CAAA,QAAAiB,OAAA;AAcA;AACA,MAAMI,cAAc;AAAA;AAAA,CAAAzB,aAAA,GAAAI,CAAA,QAAwB;EAC1CsB,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC;EAClDC,iBAAiB,EAAE,EAAE;EACrBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE;CACZ;AAED;AACA,MAAMC,iBAAiB;AAAA;AAAA,CAAAlC,aAAA,GAAAI,CAAA,QAAG;AACxB;AACA,2EAA2E;AAC3E;AACA,qDAAqD,EACrD,eAAe,EACf,aAAa;AACb;AACA,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,QAAQ;AACR;AACA,YAAY;AACZ;AACA,SAAS,EACT,SAAS,CACV;AAED;AACA,MAAM+B,gBAAgB;AAAA;AAAA,CAAAnC,aAAA,GAAAI,CAAA,QAAG;EACvBgC,WAAW,EAAE,+BAA+B;EAC5CC,WAAW,EAAE,UAAU;EACvBC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE,UAAU;EACfC,UAAU,EAAE;CACb;AAED;;;AAGA,SAAgBrC,cAAcA,CAACsC,KAAa,EAAEC,OAAA;AAAA;AAAA,CAAA1C,aAAA,GAAA2C,CAAA,UAA+B,EAAE;EAAA;EAAA3C,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAC7E,IAAI,OAAOqC,KAAK,KAAK,QAAQ,EAAE;IAAA;IAAAzC,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAC7B,OAAOyC,MAAM,CAACJ,KAAK,CAAC;EACtB,CAAC;EAAA;EAAA;IAAAzC,aAAA,GAAA2C,CAAA;EAAA;EAED,MAAMG,IAAI;EAAA;EAAA,CAAA9C,aAAA,GAAAI,CAAA,QAAG;IAAE,GAAGqB,cAAc;IAAE,GAAGiB;EAAO,CAAE;EAC9C,IAAIK,SAAS;EAAA;EAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGqC,KAAK;EAErB;EAAA;EAAAzC,aAAA,GAAAI,CAAA;EACA,IAAI0C,IAAI,CAACd,eAAe,EAAE;IAAA;IAAAhC,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACxB2C,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAC1C,CAAC;EAAA;EAAA;IAAAhD,aAAA,GAAA2C,CAAA;EAAA;EAED;EAAA3C,aAAA,GAAAI,CAAA;EACA,IAAI0C,IAAI,CAACf,cAAc,EAAE;IAAA;IAAA/B,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvB2C,SAAS,GAAGA,SAAS,CAACE,IAAI,EAAE;EAC9B,CAAC;EAAA;EAAA;IAAAjD,aAAA,GAAA2C,CAAA;EAAA;EAED;EAAA3C,aAAA,GAAAI,CAAA;EACA;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,UAAAG,IAAI,CAACb,SAAS;EAAA;EAAA,CAAAjC,aAAA,GAAA2C,CAAA,UAAII,SAAS,CAACG,MAAM,GAAGJ,IAAI,CAACb,SAAS,GAAE;IAAA;IAAAjC,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvD2C,SAAS,GAAGA,SAAS,CAACI,SAAS,CAAC,CAAC,EAAEL,IAAI,CAACb,SAAS,CAAC;EACpD,CAAC;EAAA;EAAA;IAAAjC,aAAA,GAAA2C,CAAA;EAAA;EAED;EAAA3C,aAAA,GAAAI,CAAA;EACA,IAAI0C,IAAI,CAAClB,SAAS,EAAE;IAAA;IAAA5B,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAClB2C,SAAS,GAAG5B,sBAAA,CAAAiC,OAAS,CAACC,QAAQ,CAACN,SAAS,EAAE;MACxCO,YAAY;MAAE;MAAA,CAAAtD,aAAA,GAAA2C,CAAA,WAAAG,IAAI,CAACpB,WAAW;MAAA;MAAA,CAAA1B,aAAA,GAAA2C,CAAA,WAAI,EAAE;MACpCY,YAAY;MAAE;MAAA,CAAAvD,aAAA,GAAA2C,CAAA,WAAAG,IAAI,CAACnB,iBAAiB;MAAA;MAAA,CAAA3B,aAAA,GAAA2C,CAAA,WAAI,EAAE;MAC1Ca,YAAY,EAAE;KACf,CAAC;EACJ,CAAC,MAAM;IAAA;IAAAxD,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAAA,IAAI0C,IAAI,CAACjB,UAAU,EAAE;MAAA;MAAA7B,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MAC1B2C,SAAS,GAAGzB,WAAA,CAAA8B,OAAS,CAACK,MAAM,CAACV,SAAS,CAAC;IACzC,CAAC;IAAA;IAAA;MAAA/C,aAAA,GAAA2C,CAAA;IAAA;EAAD;EAAC;EAAA3C,aAAA,GAAAI,CAAA;EAED,OAAO2C,SAAS;AAClB;AAEA;;;AAGA,SAAgB1C,aAAaA,CAACqD,KAAa,EAAEC,SAAA;AAAA;AAAA,CAAA3D,aAAA,GAAA2C,CAAA,WAAqB,IAAI;EAAA;EAAA3C,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACpE;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,YAACe,KAAK;EAAA;EAAA,CAAA1D,aAAA,GAAA2C,CAAA,WAAI,OAAOe,KAAK,KAAK,QAAQ,GAAE;IAAA;IAAA1D,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvC,OAAO,EAAE;EACX,CAAC;EAAA;EAAA;IAAAJ,aAAA,GAAA2C,CAAA;EAAA;EAED,IAAII,SAAS;EAAA;EAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGsD,KAAK,CAACT,IAAI,EAAE,CAACW,WAAW,EAAE;EAAC;EAAA5D,aAAA,GAAAI,CAAA;EAE3C,IAAIuD,SAAS,EAAE;IAAA;IAAA3D,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACb,IAAI;MAAA;MAAAJ,aAAA,GAAAI,CAAA;MACF2C,SAAS;MAAG;MAAA,CAAA/C,aAAA,GAAA2C,CAAA,WAAArB,WAAA,CAAA8B,OAAS,CAACtB,cAAc,CAACiB,SAAS,CAAC;MAAA;MAAA,CAAA/C,aAAA,GAAA2C,CAAA,WAAII,SAAS;IAC9D,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAAA;EAAA;IAAA7D,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED,OAAO2C,SAAS;AAClB;AAEA;;;AAGA,SAAgBzC,mBAAmBA,CAACwD,KAAa;EAAA;EAAA9D,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAC/C;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,YAACmB,KAAK;EAAA;EAAA,CAAA9D,aAAA,GAAA2C,CAAA,WAAI,OAAOmB,KAAK,KAAK,QAAQ,GAAE;IAAA;IAAA9D,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvC,OAAO,EAAE;EACX,CAAC;EAAA;EAAA;IAAAJ,aAAA,GAAA2C,CAAA;EAAA;EAED;EACA,IAAII,SAAS;EAAA;EAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAG0D,KAAK,CAACd,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EAE5C;EAAA;EAAAhD,aAAA,GAAAI,CAAA;EACA,IAAI2C,SAAS,CAACgB,UAAU,CAAC,GAAG,CAAC,EAAE;IAAA;IAAA/D,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAC7B2C,SAAS,GAAG,MAAM,GAAGA,SAAS,CAACI,SAAS,CAAC,CAAC,CAAC;EAC7C,CAAC,MAAM;IAAA;IAAAnD,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAAA,IAAI2C,SAAS,CAACgB,UAAU,CAAC,KAAK,CAAC,EAAE;MAAA;MAAA/D,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MACtC2C,SAAS,GAAG,GAAG,GAAGA,SAAS;IAC7B,CAAC,MAAM;MAAA;MAAA/C,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MAAA,IAAI,CAAC2C,SAAS,CAACgB,UAAU,CAAC,MAAM,CAAC,EAAE;QAAA;QAAA/D,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QACxC;QACA,IAAI2C,SAAS,CAACG,MAAM,KAAK,EAAE,EAAE;UAAA;UAAAlD,aAAA,GAAA2C,CAAA;UAAA3C,aAAA,GAAAI,CAAA;UAC3B2C,SAAS,GAAG,MAAM,GAAGA,SAAS;QAChC,CAAC;QAAA;QAAA;UAAA/C,aAAA,GAAA2C,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAA3C,aAAA,GAAA2C,CAAA;MAAA;IAAD;EAAA;EAAC;EAAA3C,aAAA,GAAAI,CAAA;EAED,OAAO2C,SAAS;AAClB;AAEA;;;AAGA,SAAgBxC,oBAAoBA,CAACyD,IAAY,EAAEvB,KAAa;EAAA;EAAAzC,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAC9D;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,YAACF,KAAK;EAAA;EAAA,CAAAzC,aAAA,GAAA2C,CAAA,WAAI,OAAOF,KAAK,KAAK,QAAQ,GAAE;IAAA;IAAAzC,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvC,OAAO,KAAK;EACd,CAAC;EAAA;EAAA;IAAAJ,aAAA,GAAA2C,CAAA;EAAA;EAED,MAAMsB,OAAO;EAAA;EAAA,CAAAjE,aAAA,GAAAI,CAAA,QAAG+B,gBAAgB,CAAC6B,IAAqC,CAAC;EAAC;EAAAhE,aAAA,GAAAI,CAAA;EACxE,OAAO6D,OAAO;EAAA;EAAA,CAAAjE,aAAA,GAAA2C,CAAA,WAAGsB,OAAO,CAACC,IAAI,CAACzB,KAAK,CAAC;EAAA;EAAA,CAAAzC,aAAA,GAAA2C,CAAA,WAAG,KAAK;AAC9C;AAEA;;;AAGA,SAAgBnC,uBAAuBA,CAAC2D,KAAa;EAAA;EAAAnE,aAAA,GAAA4C,CAAA;EACnD,MAAMwB,QAAQ;EAAA;EAAA,CAAApE,aAAA,GAAAI,CAAA,QAAa,EAAE;EAAC;EAAAJ,aAAA,GAAAI,CAAA;EAE9B,KAAK,MAAM6D,OAAO,IAAI/B,iBAAiB,EAAE;IAAA;IAAAlC,aAAA,GAAAI,CAAA;IACvC,IAAI6D,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE;MAAA;MAAAnE,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MACvBgE,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAACK,MAAM,CAAC;IAC/B,CAAC;IAAA;IAAA;MAAAtE,aAAA,GAAA2C,CAAA;IAAA;EACH;EAAC;EAAA3C,aAAA,GAAAI,CAAA;EAED,OAAOgE,QAAQ;AACjB;AAEA;;;AAGA,SAAgB3D,cAAcA,CAAC8D,GAAQ,EAAE7B,OAAA;AAAA;AAAA,CAAA1C,aAAA,GAAA2C,CAAA,WAA+B,EAAE;EAAA;EAAA3C,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACxE;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,WAAA4B,GAAG,KAAK,IAAI;EAAA;EAAA,CAAAvE,aAAA,GAAA2C,CAAA,WAAI4B,GAAG,KAAKC,SAAS,GAAE;IAAA;IAAAxE,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACrC,OAAOmE,GAAG;EACZ,CAAC;EAAA;EAAA;IAAAvE,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED,IAAI,OAAOmE,GAAG,KAAK,QAAQ,EAAE;IAAA;IAAAvE,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IAC3B,OAAOD,cAAc,CAACoE,GAAG,EAAE7B,OAAO,CAAC;EACrC,CAAC;EAAA;EAAA;IAAA1C,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED;EAAI;EAAA,CAAAJ,aAAA,GAAA2C,CAAA,kBAAO4B,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAvE,aAAA,GAAA2C,CAAA,WAAI,OAAO4B,GAAG,KAAK,SAAS,GAAE;IAAA;IAAAvE,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACvD,OAAOmE,GAAG;EACZ,CAAC;EAAA;EAAA;IAAAvE,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED,IAAIqE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;IAAA;IAAAvE,aAAA,GAAA2C,CAAA;IAAA3C,aAAA,GAAAI,CAAA;IACtB,OAAOmE,GAAG,CAACI,GAAG,CAACC,IAAI,IAAI;MAAA;MAAA5E,aAAA,GAAA4C,CAAA;MAAA5C,aAAA,GAAAI,CAAA;MAAA,OAAAK,cAAc,CAACmE,IAAI,EAAElC,OAAO,CAAC;IAAD,CAAC,CAAC;EACvD,CAAC;EAAA;EAAA;IAAA1C,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED,IAAI,OAAOmE,GAAG,KAAK,QAAQ,EAAE;IAAA;IAAAvE,aAAA,GAAA2C,CAAA;IAC3B,MAAMI,SAAS;IAAA;IAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAQ,EAAE;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAC1B,KAAK,MAAM,CAACyE,GAAG,EAAEpC,KAAK,CAAC,IAAIqC,MAAM,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;MAC9C;MACA,MAAMS,YAAY;MAAA;MAAA,CAAAhF,aAAA,GAAAI,CAAA,QAAGD,cAAc,CAAC0E,GAAG,EAAE;QAAEjD,SAAS,EAAE,IAAI;QAAEK,SAAS,EAAE;MAAG,CAAE,CAAC;MAAC;MAAAjC,aAAA,GAAAI,CAAA;MAC9E2C,SAAS,CAACiC,YAAY,CAAC,GAAGvE,cAAc,CAACgC,KAAK,EAAEC,OAAO,CAAC;IAC1D;IAAC;IAAA1C,aAAA,GAAAI,CAAA;IACD,OAAO2C,SAAS;EAClB,CAAC;EAAA;EAAA;IAAA/C,aAAA,GAAA2C,CAAA;EAAA;EAAA3C,aAAA,GAAAI,CAAA;EAED,OAAOmE,GAAG;AACZ;AAEA;;;AAGA,SAAgB7D,eAAeA,CAACgC,OAAA;AAAA;AAAA,CAAA1C,aAAA,GAAA2C,CAAA,WAA+B,EAAE;EAAA;EAAA3C,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAC/D,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IAAA5C,aAAA,GAAAI,CAAA;IACzD,IAAI;MAAA;MAAAJ,aAAA,GAAAI,CAAA;MACF;MACA;MAAI;MAAA,CAAAJ,aAAA,GAAA2C,CAAA,WAAAsC,GAAG,CAACG,IAAI;MAAA;MAAA,CAAApF,aAAA,GAAA2C,CAAA,WAAI,OAAOsC,GAAG,CAACG,IAAI,KAAK,QAAQ,GAAE;QAAA;QAAApF,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QAC5C6E,GAAG,CAACG,IAAI,GAAG3E,cAAc,CAACwE,GAAG,CAACG,IAAI,EAAE1C,OAAO,CAAC;MAC9C,CAAC;MAAA;MAAA;QAAA1C,aAAA,GAAA2C,CAAA;MAAA;MAED;MAAA3C,aAAA,GAAAI,CAAA;MACA;MAAI;MAAA,CAAAJ,aAAA,GAAA2C,CAAA,WAAAsC,GAAG,CAACI,KAAK;MAAA;MAAA,CAAArF,aAAA,GAAA2C,CAAA,WAAI,OAAOsC,GAAG,CAACI,KAAK,KAAK,QAAQ,GAAE;QAAA;QAAArF,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QAC9C6E,GAAG,CAACI,KAAK,GAAG5E,cAAc,CAACwE,GAAG,CAACI,KAAK,EAAE3C,OAAO,CAAC;MAChD,CAAC;MAAA;MAAA;QAAA1C,aAAA,GAAA2C,CAAA;MAAA;MAED;MAAA3C,aAAA,GAAAI,CAAA;MACA;MAAI;MAAA,CAAAJ,aAAA,GAAA2C,CAAA,WAAAsC,GAAG,CAACK,MAAM;MAAA;MAAA,CAAAtF,aAAA,GAAA2C,CAAA,WAAI,OAAOsC,GAAG,CAACK,MAAM,KAAK,QAAQ,GAAE;QAAA;QAAAtF,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QAChD6E,GAAG,CAACK,MAAM,GAAG7E,cAAc,CAACwE,GAAG,CAACK,MAAM,EAAE5C,OAAO,CAAC;MAClD,CAAC;MAAA;MAAA;QAAA1C,aAAA,GAAA2C,CAAA;MAAA;MAED;MACA,MAAM4C,cAAc;MAAA;MAAA,CAAAvF,aAAA,GAAAI,CAAA,QAAG,CACrB,IAAI6E,GAAG,CAACG,IAAI;MAAA;MAAA,CAAApF,aAAA,GAAA2C,CAAA,WAAGmC,MAAM,CAACU,MAAM,CAACP,GAAG,CAACG,IAAI,CAAC;MAAA;MAAA,CAAApF,aAAA,GAAA2C,CAAA,WAAG,EAAE,EAAC,EAC5C,IAAIsC,GAAG,CAACI,KAAK;MAAA;MAAA,CAAArF,aAAA,GAAA2C,CAAA,WAAGmC,MAAM,CAACU,MAAM,CAACP,GAAG,CAACI,KAAK,CAAC;MAAA;MAAA,CAAArF,aAAA,GAAA2C,CAAA,WAAG,EAAE,EAAC,EAC9C,IAAIsC,GAAG,CAACK,MAAM;MAAA;MAAA,CAAAtF,aAAA,GAAA2C,CAAA,WAAGmC,MAAM,CAACU,MAAM,CAACP,GAAG,CAACK,MAAM,CAAC;MAAA;MAAA,CAAAtF,aAAA,GAAA2C,CAAA,WAAG,EAAE,EAAC,CACjD;MAAC;MAAA3C,aAAA,GAAAI,CAAA;MAEF,KAAK,MAAMqF,KAAK,IAAIF,cAAc,EAAE;QAAA;QAAAvF,aAAA,GAAAI,CAAA;QAClC,IAAI,OAAOqF,KAAK,KAAK,QAAQ,EAAE;UAAA;UAAAzF,aAAA,GAAA2C,CAAA;UAC7B,MAAM+C,SAAS;UAAA;UAAA,CAAA1F,aAAA,GAAAI,CAAA,QAAGI,uBAAuB,CAACiF,KAAK,CAAC;UAAC;UAAAzF,aAAA,GAAAI,CAAA;UACjD,IAAIsF,SAAS,CAACxC,MAAM,GAAG,CAAC,EAAE;YAAA;YAAAlD,aAAA,GAAA2C,CAAA;YAAA3C,aAAA,GAAAI,CAAA;YACxBmB,QAAA,CAAAoE,MAAM,CAACC,IAAI,CAAC,wCAAwC,EAAE;cACpDC,EAAE,EAAEZ,GAAG,CAACY,EAAE;cACVC,SAAS,EAAEb,GAAG,CAACc,GAAG,CAAC,YAAY,CAAC;cAChCC,IAAI,EAAEf,GAAG,CAACe,IAAI;cACdC,MAAM,EAAEhB,GAAG,CAACgB,MAAM;cAClBC,QAAQ,EAAER,SAAS;cACnBS,MAAM,EAAElB,GAAG,CAACmB,IAAI,EAAEC;aACnB,CAAC;YAEF;YAAA;YAAArG,aAAA,GAAAI,CAAA;YACA,IAAIsC,OAAO,CAACd,SAAS,EAAE;cAAA;cAAA5B,aAAA,GAAA2C,CAAA;cAAA3C,aAAA,GAAAI,CAAA;cACrB,MAAM,IAAIoB,UAAA,CAAA8E,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;YAC1E,CAAC;YAAA;YAAA;cAAAtG,aAAA,GAAA2C,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAA3C,aAAA,GAAA2C,CAAA;UAAA;QACH,CAAC;QAAA;QAAA;UAAA3C,aAAA,GAAA2C,CAAA;QAAA;MACH;MAAC;MAAA3C,aAAA,GAAAI,CAAA;MAED+E,IAAI,EAAE;IACR,CAAC,CAAC,OAAOtB,KAAK,EAAE;MAAA;MAAA7D,aAAA,GAAAI,CAAA;MACdmB,QAAA,CAAAoE,MAAM,CAAC9B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAA7D,aAAA,GAAAI,CAAA;MACnD+E,IAAI,CAACtB,KAAK,CAAC;IACb;EACF,CAAC;AACH;AAEA;;;AAGA,SAAgBlD,kBAAkBA,CAAA;EAAA;EAAAX,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAChC,OAAOM,eAAe,CAAC;IACrBgB,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE;GACZ,CAAC;AACJ;AAEA;;;AAGA,SAAgBrB,mBAAmBA,CAAA;EAAA;EAAAZ,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACjC,OAAOM,eAAe,CAAC;IACrBgB,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACpEC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE;GACZ,CAAC;AACJ;AAEA;;;AAGA,SAAgBpB,iBAAiBA,CAAA;EAAA;EAAAb,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EAC/B,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IAAA5C,aAAA,GAAAI,CAAA;IACzD,IAAI6E,GAAG,CAACG,IAAI,CAAC1B,KAAK,EAAE;MAAA;MAAA1D,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MAClB6E,GAAG,CAACG,IAAI,CAAC1B,KAAK,GAAGrD,aAAa,CAAC4E,GAAG,CAACG,IAAI,CAAC1B,KAAK,CAAC;IAChD,CAAC;IAAA;IAAA;MAAA1D,aAAA,GAAA2C,CAAA;IAAA;IAAA3C,aAAA,GAAAI,CAAA;IACD,IAAI6E,GAAG,CAACI,KAAK,CAAC3B,KAAK,EAAE;MAAA;MAAA1D,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MACnB6E,GAAG,CAACI,KAAK,CAAC3B,KAAK,GAAGrD,aAAa,CAAC4E,GAAG,CAACI,KAAK,CAAC3B,KAAe,CAAC;IAC5D,CAAC;IAAA;IAAA;MAAA1D,aAAA,GAAA2C,CAAA;IAAA;IAAA3C,aAAA,GAAAI,CAAA;IACD+E,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBrE,uBAAuBA,CAAA;EAAA;EAAAd,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACrC,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IACzD,MAAM2D,WAAW;IAAA;IAAA,CAAAvG,aAAA,GAAAI,CAAA,SAAG,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAElE,KAAK,MAAMqF,KAAK,IAAIc,WAAW,EAAE;MAAA;MAAAvG,aAAA,GAAAI,CAAA;MAC/B,IAAI6E,GAAG,CAACG,IAAI,CAACK,KAAK,CAAC,EAAE;QAAA;QAAAzF,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QACnB6E,GAAG,CAACG,IAAI,CAACK,KAAK,CAAC,GAAGnF,mBAAmB,CAAC2E,GAAG,CAACG,IAAI,CAACK,KAAK,CAAC,CAAC;MACxD,CAAC;MAAA;MAAA;QAAAzF,aAAA,GAAA2C,CAAA;MAAA;IACH;IAAC;IAAA3C,aAAA,GAAAI,CAAA;IAED+E,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBpE,sBAAsBA,CAAA;EAAA;EAAAf,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACpC,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IAAA5C,aAAA,GAAAI,CAAA;IACzD,IAAI6E,GAAG,CAACuB,IAAI,EAAE;MAAA;MAAAxG,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MACZ;MACA6E,GAAG,CAACuB,IAAI,CAACC,YAAY,GAAGtG,cAAc,CAAC8E,GAAG,CAACuB,IAAI,CAACC,YAAY,EAAE;QAC5D7E,SAAS,EAAE,IAAI;QACfK,SAAS,EAAE;OACZ,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAjC,aAAA,GAAA2C,CAAA;IAAA;IAAA3C,aAAA,GAAAI,CAAA;IAED;IAAI;IAAA,CAAAJ,aAAA,GAAA2C,CAAA,WAAAsC,GAAG,CAACyB,KAAK;IAAA;IAAA,CAAA1G,aAAA,GAAA2C,CAAA,WAAI8B,KAAK,CAACC,OAAO,CAACO,GAAG,CAACyB,KAAK,CAAC,GAAE;MAAA;MAAA1G,aAAA,GAAA2C,CAAA;MAAA3C,aAAA,GAAAI,CAAA;MACzC6E,GAAG,CAACyB,KAAK,CAACC,OAAO,CAACH,IAAI,IAAG;QAAA;QAAAxG,aAAA,GAAA4C,CAAA;QAAA5C,aAAA,GAAAI,CAAA;QACvBoG,IAAI,CAACC,YAAY,GAAGtG,cAAc,CAACqG,IAAI,CAACC,YAAY,EAAE;UACpD7E,SAAS,EAAE,IAAI;UACfK,SAAS,EAAE;SACZ,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAjC,aAAA,GAAA2C,CAAA;IAAA;IAAA3C,aAAA,GAAAI,CAAA;IAED+E,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBnE,uBAAuBA,CAAA;EAAA;EAAAhB,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACrC,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IACzD,MAAMgE,YAAY;IAAA;IAAA,CAAA5G,aAAA,GAAAI,CAAA,SAAG,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAEjE,KAAK,MAAMqF,KAAK,IAAImB,YAAY,EAAE;MAAA;MAAA5G,aAAA,GAAAI,CAAA;MAChC,IAAI6E,GAAG,CAACI,KAAK,CAACI,KAAK,CAAC,EAAE;QAAA;QAAAzF,aAAA,GAAA2C,CAAA;QAAA3C,aAAA,GAAAI,CAAA;QACpB6E,GAAG,CAACI,KAAK,CAACI,KAAK,CAAC,GAAGtF,cAAc,CAAC8E,GAAG,CAACI,KAAK,CAACI,KAAK,CAAW,EAAE;UAC5D7D,SAAS,EAAE,IAAI;UACfK,SAAS,EAAE;SACZ,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAjC,aAAA,GAAA2C,CAAA;MAAA;IACH;IAAC;IAAA3C,aAAA,GAAAI,CAAA;IAED+E,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBlE,sBAAsBA,CAAA;EAAA;EAAAjB,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACpC,OAAO,CAAC6E,GAAY,EAAEC,GAAa,EAAEC,IAAkB,KAAI;IAAA;IAAAnF,aAAA,GAAA4C,CAAA;IACzD,MAAMiE,eAAe;IAAA;IAAA,CAAA7G,aAAA,GAAAI,CAAA,SAAG;MACtBgC,WAAW,EAAE,aAAa;MAC1B0B,KAAK,EAAE,aAAa;MACpBgD,MAAM,EAAE,aAAa;MACrBzE,WAAW,EAAE,aAAa;MAC1B0E,aAAa,EAAE,aAAa;MAC5BzE,GAAG,EAAE,KAAK;MACVC,GAAG,EAAE,KAAK;MACVC,UAAU,EAAE,YAAY;MACxBwE,OAAO,EAAE;KACV;IAAC;IAAAhH,aAAA,GAAAI,CAAA;IAEF,KAAK,MAAM,CAACqF,KAAK,EAAEzB,IAAI,CAAC,IAAIc,MAAM,CAACC,OAAO,CAAC8B,eAAe,CAAC,EAAE;MAAA;MAAA7G,aAAA,GAAAI,CAAA;MAC3D,IAAI6E,GAAG,CAACG,IAAI,CAACK,KAAK,CAAC,EAAE;QAAA;QAAAzF,aAAA,GAAA2C,CAAA;QACnB,MAAMsE,OAAO;QAAA;QAAA,CAAAjH,aAAA,GAAAI,CAAA,SAAGG,oBAAoB,CAACyD,IAAI,EAAEiB,GAAG,CAACG,IAAI,CAACK,KAAK,CAAC,CAAC;QAAC;QAAAzF,aAAA,GAAAI,CAAA;QAC5D,IAAI,CAAC6G,OAAO,EAAE;UAAA;UAAAjH,aAAA,GAAA2C,CAAA;UAAA3C,aAAA,GAAAI,CAAA;UACZ,MAAM,IAAIoB,UAAA,CAAA8E,QAAQ,CAAC,WAAWb,KAAK,2BAA2B,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC;QAC9F,CAAC;QAAA;QAAA;UAAAzF,aAAA,GAAA2C,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAA3C,aAAA,GAAA2C,CAAA;MAAA;IACH;IAAC;IAAA3C,aAAA,GAAAI,CAAA;IAED+E,IAAI,EAAE;EACR,CAAC;AACH;AAEA;;;AAGA,SAAgBjE,mBAAmBA,CAAA;EAAA;EAAAlB,aAAA,GAAA4C,CAAA;EAAA5C,aAAA,GAAAI,CAAA;EACjC,OAAOM,eAAe,CAAC;IACrBgB,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACpEC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBE,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE;GACZ,CAAC;AACJ;AAAC;AAAAjC,aAAA,GAAAI,CAAA;AAEDF,OAAA,CAAAkD,OAAA,GAAe;EACbjD,cAAc;EACdE,aAAa;EACbC,mBAAmB;EACnBC,oBAAoB;EACpBC,uBAAuB;EACvBC,cAAc;EACdC,eAAe;EACfC,kBAAkB;EAClBC,mBAAmB;EACnBC,iBAAiB;EACjBC,uBAAuB;EACvBC,sBAAsB;EACtBC,uBAAuB;EACvBC,sBAAsB;EACtBC;CACD", "ignoreList": []}