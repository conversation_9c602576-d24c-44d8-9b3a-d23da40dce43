{"version": 3, "names": ["cov_fgezjy1xw", "actualCoverage", "s", "express_1", "require", "auth_1", "adminAuth_1", "errorTrackingService_1", "performanceMonitoringService_1", "backupService_1", "logger_1", "router", "Router", "get", "auth", "adminAuth", "req", "res", "f", "performanceMetrics", "performanceMonitoringService", "getMetrics", "errorMetrics", "errorTrackingService", "performanceHealth", "getHealthStatus", "errorHealth", "getHealthSummary", "json", "success", "data", "performance", "errors", "health", "timestamp", "Date", "toISOString", "error", "logger", "message", "status", "report", "generateReport", "slowEndpoints", "getSlowEndpoints", "limit", "b", "parseInt", "query", "category", "metrics", "recentErrors", "getErrorsByCategory", "getRecentErrors", "map", "id", "name", "getTime", "severity", "context", "memoryUsage", "process", "cpuUsage", "uptime", "memory", "heapUsed", "toFixed", "heapTotal", "rss", "external", "heapUsedPercent", "cpu", "user", "system", "Math", "floor", "platform", "nodeVersion", "version", "pid", "environment", "nodeEnv", "env", "NODE_ENV", "port", "PORT", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "backup<PERSON><PERSON><PERSON>", "backupService", "getBackupStatus", "post", "type", "body", "result", "createMongoDBBackup", "createFilesBackup", "createFullBackup", "cleanupOldBackups", "level", "suggestion", "logFiles", "memoryUsagePercent", "errorRate", "requests", "total", "failed", "overview", "requestRate", "rate", "averageResponseTime", "responseTime", "average", "backups", "summary", "lastBackup", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\monitoring.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { auth } from '../middleware/auth';\r\nimport { adminAuth } from '../middleware/adminAuth';\r\nimport { errorTrackingService } from '../services/errorTrackingService';\r\nimport { performanceMonitoringService } from '../services/performanceMonitoringService';\r\nimport { backupService } from '../services/backupService';\r\nimport { logger } from '../utils/logger';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route GET /api/monitoring/metrics\r\n * @desc Get performance metrics (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/metrics', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const performanceMetrics = performanceMonitoringService.getMetrics();\r\n    const errorMetrics = errorTrackingService.getMetrics();\r\n    const performanceHealth = performanceMonitoringService.getHealthStatus();\r\n    const errorHealth = errorTrackingService.getHealthSummary();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        performance: performanceMetrics,\r\n        errors: errorMetrics,\r\n        health: {\r\n          performance: performanceHealth,\r\n          errors: errorHealth\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get monitoring metrics', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve monitoring metrics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/performance\r\n * @desc Get detailed performance report (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/performance', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const report = performanceMonitoringService.generateReport();\r\n    const slowEndpoints = performanceMonitoringService.getSlowEndpoints(10);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        report,\r\n        slowEndpoints,\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get performance report', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve performance report'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/errors\r\n * @desc Get error tracking information (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/errors', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const limit = parseInt(req.query.limit as string) || 50;\r\n    const category = req.query.category as string;\r\n\r\n    const metrics = errorTrackingService.getMetrics();\r\n    const recentErrors = category \r\n      ? errorTrackingService.getErrorsByCategory(category as any, limit)\r\n      : errorTrackingService.getRecentErrors(limit);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        metrics,\r\n        recentErrors: recentErrors.map(error => ({\r\n          id: error.error.name + '_' + error.timestamp.getTime(),\r\n          message: error.error.message,\r\n          severity: error.severity,\r\n          category: error.category,\r\n          timestamp: error.timestamp,\r\n          context: error.context\r\n        })),\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get error tracking data', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve error tracking data'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/system\r\n * @desc Get system information (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/system', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const memoryUsage = process.memoryUsage();\r\n    const cpuUsage = process.cpuUsage();\r\n    const uptime = process.uptime();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        memory: {\r\n          heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,\r\n          heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,\r\n          rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,\r\n          external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,\r\n          heapUsedPercent: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(2)\r\n        },\r\n        cpu: {\r\n          user: cpuUsage.user,\r\n          system: cpuUsage.system\r\n        },\r\n        system: {\r\n          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m`,\r\n          platform: process.platform,\r\n          nodeVersion: process.version,\r\n          pid: process.pid\r\n        },\r\n        environment: {\r\n          nodeEnv: process.env.NODE_ENV,\r\n          port: process.env.PORT,\r\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get system information', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve system information'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/backups\r\n * @desc Get backup status and history (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/backups', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const backupStatus = backupService.getBackupStatus();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...backupStatus,\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get backup status', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve backup status'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route POST /api/monitoring/backups/create\r\n * @desc Create a new backup (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.post('/backups/create', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const { type } = req.body;\r\n\r\n    let result;\r\n    switch (type) {\r\n      case 'mongodb':\r\n        result = await backupService.createMongoDBBackup();\r\n        break;\r\n      case 'files':\r\n        result = await backupService.createFilesBackup();\r\n        break;\r\n      case 'full':\r\n        result = await backupService.createFullBackup();\r\n        break;\r\n      default:\r\n        return res.status(400).json({\r\n          success: false,\r\n          error: 'Invalid backup type. Use: mongodb, files, or full'\r\n        });\r\n    }\r\n\r\n    res.json({\r\n      success: true,\r\n      data: result,\r\n      message: `${type} backup created successfully`\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to create backup', { error: error.message, type: req.body.type });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to create backup'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route POST /api/monitoring/backups/cleanup\r\n * @desc Clean up old backups (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.post('/backups/cleanup', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    await backupService.cleanupOldBackups();\r\n\r\n    res.json({\r\n      success: true,\r\n      message: 'Backup cleanup completed successfully'\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to cleanup backups', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to cleanup backups'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/logs\r\n * @desc Get recent log entries (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/logs', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const level = req.query.level as string || 'info';\r\n    const limit = parseInt(req.query.limit as string) || 100;\r\n\r\n    // This is a simplified implementation\r\n    // In production, you might want to use a log aggregation service\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        message: 'Log retrieval not implemented in this version',\r\n        suggestion: 'Use log files directly or implement log aggregation service',\r\n        logFiles: [\r\n          '/logs/app.log',\r\n          '/logs/error.log',\r\n          '/logs/http.log',\r\n          '/logs/exceptions.log'\r\n        ]\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get logs', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve logs'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @route GET /api/monitoring/dashboard\r\n * @desc Get monitoring dashboard data (admin only)\r\n * @access Private/Admin\r\n */\r\nrouter.get('/dashboard', auth, adminAuth, async (req, res) => {\r\n  try {\r\n    const performanceMetrics = performanceMonitoringService.getMetrics();\r\n    const errorMetrics = errorTrackingService.getMetrics();\r\n    const performanceHealth = performanceMonitoringService.getHealthStatus();\r\n    const errorHealth = errorTrackingService.getHealthSummary();\r\n    const backupStatus = backupService.getBackupStatus();\r\n    const memoryUsage = process.memoryUsage();\r\n\r\n    // Calculate key metrics\r\n    const uptime = process.uptime();\r\n    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;\r\n    const errorRate = performanceMetrics.requests.total > 0 \r\n      ? (performanceMetrics.requests.failed / performanceMetrics.requests.total) * 100 \r\n      : 0;\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        overview: {\r\n          status: performanceHealth.status === 'healthy' && errorHealth.status === 'healthy' ? 'healthy' : 'warning',\r\n          uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m`,\r\n          memoryUsage: `${memoryUsagePercent.toFixed(1)}%`,\r\n          errorRate: `${errorRate.toFixed(2)}%`,\r\n          requestRate: `${performanceMetrics.requests.rate}/min`,\r\n          averageResponseTime: `${performanceMetrics.responseTime.average.toFixed(0)}ms`\r\n        },\r\n        performance: {\r\n          health: performanceHealth,\r\n          metrics: performanceMetrics\r\n        },\r\n        errors: {\r\n          health: errorHealth,\r\n          metrics: errorMetrics\r\n        },\r\n        backups: {\r\n          summary: backupStatus.summary,\r\n          lastBackup: backupStatus.summary.lastBackup\r\n        },\r\n        timestamp: new Date().toISOString()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to get dashboard data', { error: error.message });\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve dashboard data'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeW;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AAfX,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,WAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,sBAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,8BAAA;AAAA;AAAA,CAAAR,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAK,eAAA;AAAA;AAAA,CAAAT,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAM,QAAA;AAAA;AAAA,CAAAV,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA,MAAMO,MAAM;AAAA;AAAA,CAAAX,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAS,MAAM,GAAE;AAEvB;;;;;AAAA;AAAAZ,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,UAAU,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACzD,IAAI;IACF,MAAMiB,kBAAkB;IAAA;IAAA,CAAAnB,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACC,UAAU,EAAE;IACpE,MAAMC,YAAY;IAAA;IAAA,CAAAtB,aAAA,GAAAE,CAAA,QAAGK,sBAAA,CAAAgB,oBAAoB,CAACF,UAAU,EAAE;IACtD,MAAMG,iBAAiB;IAAA;IAAA,CAAAxB,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACK,eAAe,EAAE;IACxE,MAAMC,WAAW;IAAA;IAAA,CAAA1B,aAAA,GAAAE,CAAA,QAAGK,sBAAA,CAAAgB,oBAAoB,CAACI,gBAAgB,EAAE;IAAC;IAAA3B,aAAA,GAAAE,CAAA;IAE5De,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJC,WAAW,EAAEZ,kBAAkB;QAC/Ba,MAAM,EAAEV,YAAY;QACpBW,MAAM,EAAE;UACNF,WAAW,EAAEP,iBAAiB;UAC9BQ,MAAM,EAAEN;SACT;QACDQ,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,kCAAkC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IAC3Ee,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,cAAc,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EAC7D,IAAI;IACF,MAAMuC,MAAM;IAAA;IAAA,CAAAzC,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACsB,cAAc,EAAE;IAC5D,MAAMC,aAAa;IAAA;IAAA,CAAA3C,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACwB,gBAAgB,CAAC,EAAE,CAAC;IAAC;IAAA5C,aAAA,GAAAE,CAAA;IAExEe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJW,MAAM;QACNE,aAAa;QACbT,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,kCAAkC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IAC3Ee,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,SAAS,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACxD,IAAI;IACF,MAAM2C,KAAK;IAAA;IAAA,CAAA7C,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAA8C,CAAA,UAAAC,QAAQ,CAAC/B,GAAG,CAACgC,KAAK,CAACH,KAAe,CAAC;IAAA;IAAA,CAAA7C,aAAA,GAAA8C,CAAA,UAAI,EAAE;IACvD,MAAMG,QAAQ;IAAA;IAAA,CAAAjD,aAAA,GAAAE,CAAA,QAAGc,GAAG,CAACgC,KAAK,CAACC,QAAkB;IAE7C,MAAMC,OAAO;IAAA;IAAA,CAAAlD,aAAA,GAAAE,CAAA,QAAGK,sBAAA,CAAAgB,oBAAoB,CAACF,UAAU,EAAE;IACjD,MAAM8B,YAAY;IAAA;IAAA,CAAAnD,aAAA,GAAAE,CAAA,QAAG+C,QAAQ;IAAA;IAAA,CAAAjD,aAAA,GAAA8C,CAAA,UACzBvC,sBAAA,CAAAgB,oBAAoB,CAAC6B,mBAAmB,CAACH,QAAe,EAAEJ,KAAK,CAAC;IAAA;IAAA,CAAA7C,aAAA,GAAA8C,CAAA,UAChEvC,sBAAA,CAAAgB,oBAAoB,CAAC8B,eAAe,CAACR,KAAK,CAAC;IAAC;IAAA7C,aAAA,GAAAE,CAAA;IAEhDe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJoB,OAAO;QACPC,YAAY,EAAEA,YAAY,CAACG,GAAG,CAACjB,KAAK,IAAK;UAAA;UAAArC,aAAA,GAAAkB,CAAA;UAAAlB,aAAA,GAAAE,CAAA;UAAA;YACvCqD,EAAE,EAAElB,KAAK,CAACA,KAAK,CAACmB,IAAI,GAAG,GAAG,GAAGnB,KAAK,CAACH,SAAS,CAACuB,OAAO,EAAE;YACtDlB,OAAO,EAAEF,KAAK,CAACA,KAAK,CAACE,OAAO;YAC5BmB,QAAQ,EAAErB,KAAK,CAACqB,QAAQ;YACxBT,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;YACxBf,SAAS,EAAEG,KAAK,CAACH,SAAS;YAC1ByB,OAAO,EAAEtB,KAAK,CAACsB;WAChB;SAAC,CAAC;QACHzB,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,mCAAmC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IAC5Ee,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,SAAS,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACxD,IAAI;IACF,MAAM0D,WAAW;IAAA;IAAA,CAAA5D,aAAA,GAAAE,CAAA,QAAG2D,OAAO,CAACD,WAAW,EAAE;IACzC,MAAME,QAAQ;IAAA;IAAA,CAAA9D,aAAA,GAAAE,CAAA,QAAG2D,OAAO,CAACC,QAAQ,EAAE;IACnC,MAAMC,MAAM;IAAA;IAAA,CAAA/D,aAAA,GAAAE,CAAA,QAAG2D,OAAO,CAACE,MAAM,EAAE;IAAC;IAAA/D,aAAA,GAAAE,CAAA;IAEhCe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJkC,MAAM,EAAE;UACNC,QAAQ,EAAE,GAAG,CAACL,WAAW,CAACK,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK;UACjEC,SAAS,EAAE,GAAG,CAACP,WAAW,CAACO,SAAS,GAAG,IAAI,GAAG,IAAI,EAAED,OAAO,CAAC,CAAC,CAAC,KAAK;UACnEE,GAAG,EAAE,GAAG,CAACR,WAAW,CAACQ,GAAG,GAAG,IAAI,GAAG,IAAI,EAAEF,OAAO,CAAC,CAAC,CAAC,KAAK;UACvDG,QAAQ,EAAE,GAAG,CAACT,WAAW,CAACS,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEH,OAAO,CAAC,CAAC,CAAC,KAAK;UACjEI,eAAe,EAAE,CAAEV,WAAW,CAACK,QAAQ,GAAGL,WAAW,CAACO,SAAS,GAAI,GAAG,EAAED,OAAO,CAAC,CAAC;SAClF;QACDK,GAAG,EAAE;UACHC,IAAI,EAAEV,QAAQ,CAACU,IAAI;UACnBC,MAAM,EAAEX,QAAQ,CAACW;SAClB;QACDA,MAAM,EAAE;UACNV,MAAM,EAAE,GAAGW,IAAI,CAACC,KAAK,CAACZ,MAAM,GAAG,IAAI,CAAC,KAAKW,IAAI,CAACC,KAAK,CAAEZ,MAAM,GAAG,IAAI,GAAI,EAAE,CAAC,GAAG;UAC5Ea,QAAQ,EAAEf,OAAO,CAACe,QAAQ;UAC1BC,WAAW,EAAEhB,OAAO,CAACiB,OAAO;UAC5BC,GAAG,EAAElB,OAAO,CAACkB;SACd;QACDC,WAAW,EAAE;UACXC,OAAO,EAAEpB,OAAO,CAACqB,GAAG,CAACC,QAAQ;UAC7BC,IAAI,EAAEvB,OAAO,CAACqB,GAAG,CAACG,IAAI;UACtBC,QAAQ,EAAEC,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,EAAE,CAACC;SACnD;QACDxD,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,kCAAkC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IAC3Ee,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,UAAU,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACzD,IAAI;IACF,MAAMyF,YAAY;IAAA;IAAA,CAAA3F,aAAA,GAAAE,CAAA,QAAGO,eAAA,CAAAmF,aAAa,CAACC,eAAe,EAAE;IAAC;IAAA7F,aAAA,GAAAE,CAAA;IAErDe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJ,GAAG6D,YAAY;QACfzD,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,6BAA6B,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IACtEe,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACmF,IAAI,CAAC,iBAAiB,EAAEzF,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACjE,IAAI;IACF,MAAM;MAAE6F;IAAI,CAAE;IAAA;IAAA,CAAA/F,aAAA,GAAAE,CAAA,QAAGc,GAAG,CAACgF,IAAI;IAEzB,IAAIC,MAAM;IAAC;IAAAjG,aAAA,GAAAE,CAAA;IACX,QAAQ6F,IAAI;MACV,KAAK,SAAS;QAAA;QAAA/F,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAE,CAAA;QACZ+F,MAAM,GAAG,MAAMxF,eAAA,CAAAmF,aAAa,CAACM,mBAAmB,EAAE;QAAC;QAAAlG,aAAA,GAAAE,CAAA;QACnD;MACF,KAAK,OAAO;QAAA;QAAAF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAE,CAAA;QACV+F,MAAM,GAAG,MAAMxF,eAAA,CAAAmF,aAAa,CAACO,iBAAiB,EAAE;QAAC;QAAAnG,aAAA,GAAAE,CAAA;QACjD;MACF,KAAK,MAAM;QAAA;QAAAF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAE,CAAA;QACT+F,MAAM,GAAG,MAAMxF,eAAA,CAAAmF,aAAa,CAACQ,gBAAgB,EAAE;QAAC;QAAApG,aAAA,GAAAE,CAAA;QAChD;MACF;QAAA;QAAAF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAE,CAAA;QACE,OAAOe,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;UAC1BC,OAAO,EAAE,KAAK;UACdQ,KAAK,EAAE;SACR,CAAC;IACN;IAAC;IAAArC,aAAA,GAAAE,CAAA;IAEDe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEmE,MAAM;MACZ1D,OAAO,EAAE,GAAGwD,IAAI;KACjB,CAAC;EACJ,CAAC,CAAC,OAAO1D,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,yBAAyB,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE,OAAO;MAAEwD,IAAI,EAAE/E,GAAG,CAACgF,IAAI,CAACD;IAAI,CAAE,CAAC;IAAC;IAAA/F,aAAA,GAAAE,CAAA;IACvFe,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACmF,IAAI,CAAC,kBAAkB,EAAEzF,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EAClE,IAAI;IAAA;IAAAF,aAAA,GAAAE,CAAA;IACF,MAAMO,eAAA,CAAAmF,aAAa,CAACS,iBAAiB,EAAE;IAAC;IAAArG,aAAA,GAAAE,CAAA;IAExCe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbU,OAAO,EAAE;KACV,CAAC;EACJ,CAAC,CAAC,OAAOF,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,2BAA2B,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IACpEe,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,OAAO,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EACtD,IAAI;IACF,MAAMoG,KAAK;IAAA;IAAA,CAAAtG,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAA8C,CAAA,UAAA9B,GAAG,CAACgC,KAAK,CAACsD,KAAe;IAAA;IAAA,CAAAtG,aAAA,GAAA8C,CAAA,UAAI,MAAM;IACjD,MAAMD,KAAK;IAAA;IAAA,CAAA7C,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAA8C,CAAA,UAAAC,QAAQ,CAAC/B,GAAG,CAACgC,KAAK,CAACH,KAAe,CAAC;IAAA;IAAA,CAAA7C,aAAA,GAAA8C,CAAA,UAAI,GAAG;IAExD;IACA;IAAA;IAAA9C,aAAA,GAAAE,CAAA;IACAe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJS,OAAO,EAAE,+CAA+C;QACxDgE,UAAU,EAAE,6DAA6D;QACzEC,QAAQ,EAAE,CACR,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,sBAAsB;;KAG3B,CAAC;EACJ,CAAC,CAAC,OAAOnE,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,oBAAoB,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IAC7De,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAS,MAAM,CAACE,GAAG,CAAC,YAAY,EAAER,MAAA,CAAAS,IAAI,EAAER,WAAA,CAAAS,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAjB,aAAA,GAAAkB,CAAA;EAAAlB,aAAA,GAAAE,CAAA;EAC3D,IAAI;IACF,MAAMiB,kBAAkB;IAAA;IAAA,CAAAnB,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACC,UAAU,EAAE;IACpE,MAAMC,YAAY;IAAA;IAAA,CAAAtB,aAAA,GAAAE,CAAA,QAAGK,sBAAA,CAAAgB,oBAAoB,CAACF,UAAU,EAAE;IACtD,MAAMG,iBAAiB;IAAA;IAAA,CAAAxB,aAAA,GAAAE,CAAA,QAAGM,8BAAA,CAAAY,4BAA4B,CAACK,eAAe,EAAE;IACxE,MAAMC,WAAW;IAAA;IAAA,CAAA1B,aAAA,GAAAE,CAAA,QAAGK,sBAAA,CAAAgB,oBAAoB,CAACI,gBAAgB,EAAE;IAC3D,MAAMgE,YAAY;IAAA;IAAA,CAAA3F,aAAA,GAAAE,CAAA,QAAGO,eAAA,CAAAmF,aAAa,CAACC,eAAe,EAAE;IACpD,MAAMjC,WAAW;IAAA;IAAA,CAAA5D,aAAA,GAAAE,CAAA,QAAG2D,OAAO,CAACD,WAAW,EAAE;IAEzC;IACA,MAAMG,MAAM;IAAA;IAAA,CAAA/D,aAAA,GAAAE,CAAA,QAAG2D,OAAO,CAACE,MAAM,EAAE;IAC/B,MAAM0C,kBAAkB;IAAA;IAAA,CAAAzG,aAAA,GAAAE,CAAA,QAAI0D,WAAW,CAACK,QAAQ,GAAGL,WAAW,CAACO,SAAS,GAAI,GAAG;IAC/E,MAAMuC,SAAS;IAAA;IAAA,CAAA1G,aAAA,GAAAE,CAAA,QAAGiB,kBAAkB,CAACwF,QAAQ,CAACC,KAAK,GAAG,CAAC;IAAA;IAAA,CAAA5G,aAAA,GAAA8C,CAAA,UAClD3B,kBAAkB,CAACwF,QAAQ,CAACE,MAAM,GAAG1F,kBAAkB,CAACwF,QAAQ,CAACC,KAAK,GAAI,GAAG;IAAA;IAAA,CAAA5G,aAAA,GAAA8C,CAAA,UAC9E,CAAC;IAAC;IAAA9C,aAAA,GAAAE,CAAA;IAENe,GAAG,CAACW,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJgF,QAAQ,EAAE;UACRtE,MAAM;UAAE;UAAA,CAAAxC,aAAA,GAAA8C,CAAA,UAAAtB,iBAAiB,CAACgB,MAAM,KAAK,SAAS;UAAA;UAAA,CAAAxC,aAAA,GAAA8C,CAAA,UAAIpB,WAAW,CAACc,MAAM,KAAK,SAAS;UAAA;UAAA,CAAAxC,aAAA,GAAA8C,CAAA,UAAG,SAAS;UAAA;UAAA,CAAA9C,aAAA,GAAA8C,CAAA,UAAG,SAAS;UAC1GiB,MAAM,EAAE,GAAGW,IAAI,CAACC,KAAK,CAACZ,MAAM,GAAG,IAAI,CAAC,KAAKW,IAAI,CAACC,KAAK,CAAEZ,MAAM,GAAG,IAAI,GAAI,EAAE,CAAC,GAAG;UAC5EH,WAAW,EAAE,GAAG6C,kBAAkB,CAACvC,OAAO,CAAC,CAAC,CAAC,GAAG;UAChDwC,SAAS,EAAE,GAAGA,SAAS,CAACxC,OAAO,CAAC,CAAC,CAAC,GAAG;UACrC6C,WAAW,EAAE,GAAG5F,kBAAkB,CAACwF,QAAQ,CAACK,IAAI,MAAM;UACtDC,mBAAmB,EAAE,GAAG9F,kBAAkB,CAAC+F,YAAY,CAACC,OAAO,CAACjD,OAAO,CAAC,CAAC,CAAC;SAC3E;QACDnC,WAAW,EAAE;UACXE,MAAM,EAAET,iBAAiB;UACzB0B,OAAO,EAAE/B;SACV;QACDa,MAAM,EAAE;UACNC,MAAM,EAAEP,WAAW;UACnBwB,OAAO,EAAE5B;SACV;QACD8F,OAAO,EAAE;UACPC,OAAO,EAAE1B,YAAY,CAAC0B,OAAO;UAC7BC,UAAU,EAAE3B,YAAY,CAAC0B,OAAO,CAACC;SAClC;QACDpF,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEpC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAE,CAAA;IACdQ,QAAA,CAAA4B,MAAM,CAACD,KAAK,CAAC,8BAA8B,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACE;IAAO,CAAE,CAAC;IAAC;IAAAvC,aAAA,GAAAE,CAAA;IACvEe,GAAG,CAACuB,MAAM,CAAC,GAAG,CAAC,CAACZ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdQ,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAAC;AAAArC,aAAA,GAAAE,CAAA;AAEHqH,OAAA,CAAAC,OAAA,GAAe7G,MAAM", "ignoreList": []}