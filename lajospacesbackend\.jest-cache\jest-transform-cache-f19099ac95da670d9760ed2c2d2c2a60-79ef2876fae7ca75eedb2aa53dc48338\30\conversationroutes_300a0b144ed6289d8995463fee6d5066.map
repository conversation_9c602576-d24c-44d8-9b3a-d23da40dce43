{"version": 3, "names": ["cov_fl3ndj0gj", "actualCoverage", "s", "express_1", "require", "conversation_controller_1", "message_controller_1", "auth_1", "validation_1", "conversation_validators_1", "router", "Router", "use", "authenticate", "post", "validateRequest", "createConversationSchema", "createConversation", "get", "conversationQuerySchema", "getUserConversations", "validateObjectId", "getConversationById", "put", "updateConversationSchema", "updateConversation", "archiveConversation", "delete", "deleteConversation", "toggleMuteConversation", "messageQuerySchema", "getConversationMessages", "sendMessageSchema", "sendMessage", "editMessageSchema", "editMessage", "deleteMessageSchema", "deleteMessage", "reactToMessageSchema", "reactToMessage", "removeReaction", "markAsReadSchema", "markMessagesAsRead", "searchMessagesSchema", "searchMessages", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\conversation.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  createConversation,\r\n  getUserConversations,\r\n  getConversationById,\r\n  updateConversation,\r\n  archiveConversation,\r\n  deleteConversation,\r\n  toggleMuteConversation\r\n} from '../controllers/conversation.controller';\r\nimport {\r\n  getConversationMessages,\r\n  sendMessage,\r\n  editMessage,\r\n  deleteMessage,\r\n  reactToMessage,\r\n  removeReaction,\r\n  markMessagesAsRead,\r\n  searchMessages\r\n} from '../controllers/message.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  createConversationSchema,\r\n  updateConversationSchema,\r\n  sendMessageSchema,\r\n  editMessageSchema,\r\n  deleteMessageSchema,\r\n  reactToMessageSchema,\r\n  markAsReadSchema,\r\n  searchMessagesSchema,\r\n  conversationQuerySchema,\r\n  messageQuerySchema\r\n} from '../validators/conversation.validators';\r\n\r\nconst router = Router();\r\n\r\n// All conversation routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   POST /api/conversations\r\n * @desc    Create a new conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/',\r\n  validateRequest(createConversationSchema, 'body'),\r\n  createConversation\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations\r\n * @desc    Get user's conversations\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  validateRequest(conversationQuerySchema, 'query'),\r\n  getUserConversations\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations/:id\r\n * @desc    Get conversation by ID\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getConversationById\r\n);\r\n\r\n/**\r\n * @route   PUT /api/conversations/:id\r\n * @desc    Update conversation\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  validateRequest(updateConversationSchema, 'body'),\r\n  updateConversation\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:id/archive\r\n * @desc    Archive conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:id/archive',\r\n  validateObjectId('id'),\r\n  archiveConversation\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:id\r\n * @desc    Delete conversation\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  deleteConversation\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:id/mute\r\n * @desc    Mute/unmute conversation\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:id/mute',\r\n  validateObjectId('id'),\r\n  toggleMuteConversation\r\n);\r\n\r\n// Message routes\r\n\r\n/**\r\n * @route   GET /api/conversations/:conversationId/messages\r\n * @desc    Get messages for a conversation\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:conversationId/messages',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(messageQuerySchema, 'query'),\r\n  getConversationMessages\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages\r\n * @desc    Send a message\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(sendMessageSchema, 'body'),\r\n  sendMessage\r\n);\r\n\r\n/**\r\n * @route   PUT /api/conversations/:conversationId/messages/:messageId\r\n * @desc    Edit a message\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/:conversationId/messages/:messageId',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(editMessageSchema, 'body'),\r\n  editMessage\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:conversationId/messages/:messageId\r\n * @desc    Delete a message\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:conversationId/messages/:messageId',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(deleteMessageSchema, 'body'),\r\n  deleteMessage\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages/:messageId/react\r\n * @desc    React to a message\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages/:messageId/react',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  validateRequest(reactToMessageSchema, 'body'),\r\n  reactToMessage\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/conversations/:conversationId/messages/:messageId/react\r\n * @desc    Remove reaction from message\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/:conversationId/messages/:messageId/react',\r\n  validateObjectId('conversationId'),\r\n  validateObjectId('messageId'),\r\n  removeReaction\r\n);\r\n\r\n/**\r\n * @route   POST /api/conversations/:conversationId/messages/read\r\n * @desc    Mark messages as read\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/:conversationId/messages/read',\r\n  validateObjectId('conversationId'),\r\n  validateRequest(markAsReadSchema, 'body'),\r\n  markMessagesAsRead\r\n);\r\n\r\n/**\r\n * @route   GET /api/conversations/search/messages\r\n * @desc    Search messages across conversations\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/search/messages',\r\n  validateRequest(searchMessagesSchema, 'query'),\r\n  searchMessages\r\n);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4CG;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AA5CH,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,yBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAAE,oBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAUA,MAAAG,MAAA;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,YAAA;AAAA;AAAA,CAAAR,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAK,yBAAA;AAAA;AAAA,CAAAT,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAaA,MAAMM,MAAM;AAAA;AAAA,CAAAV,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAQ,MAAM,GAAE;AAEvB;AAAA;AAAAX,aAAA,GAAAE,CAAA;AACAQ,MAAM,CAACE,GAAG,CAACL,MAAA,CAAAM,YAAY,CAAC;AAExB;;;;;AAAA;AAAAb,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,GAAG,EACH,IAAAN,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAO,wBAAwB,EAAE,MAAM,CAAC,EACjDX,yBAAA,CAAAY,kBAAkB,CACnB;AAED;;;;;AAAA;AAAAjB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACQ,GAAG,CACR,GAAG,EACH,IAAAV,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAU,uBAAuB,EAAE,OAAO,CAAC,EACjDd,yBAAA,CAAAe,oBAAoB,CACrB;AAED;;;;;AAAA;AAAApB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACQ,GAAG,CACR,MAAM,EACN,IAAAV,YAAA,CAAAa,gBAAgB,EAAC,IAAI,CAAC,EACtBhB,yBAAA,CAAAiB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAAtB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACa,GAAG,CACR,MAAM,EACN,IAAAf,YAAA,CAAAa,gBAAgB,EAAC,IAAI,CAAC,EACtB,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAe,wBAAwB,EAAE,MAAM,CAAC,EACjDnB,yBAAA,CAAAoB,kBAAkB,CACnB;AAED;;;;;AAAA;AAAAzB,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,cAAc,EACd,IAAAN,YAAA,CAAAa,gBAAgB,EAAC,IAAI,CAAC,EACtBhB,yBAAA,CAAAqB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAA1B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACiB,MAAM,CACX,MAAM,EACN,IAAAnB,YAAA,CAAAa,gBAAgB,EAAC,IAAI,CAAC,EACtBhB,yBAAA,CAAAuB,kBAAkB,CACnB;AAED;;;;;AAAA;AAAA5B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,WAAW,EACX,IAAAN,YAAA,CAAAa,gBAAgB,EAAC,IAAI,CAAC,EACtBhB,yBAAA,CAAAwB,sBAAsB,CACvB;AAED;AAEA;;;;;AAAA;AAAA7B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACQ,GAAG,CACR,2BAA2B,EAC3B,IAAAV,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAqB,kBAAkB,EAAE,OAAO,CAAC,EAC5CxB,oBAAA,CAAAyB,uBAAuB,CACxB;AAED;;;;;AAAA;AAAA/B,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,2BAA2B,EAC3B,IAAAN,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAuB,iBAAiB,EAAE,MAAM,CAAC,EAC1C1B,oBAAA,CAAA2B,WAAW,CACZ;AAED;;;;;AAAA;AAAAjC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACa,GAAG,CACR,sCAAsC,EACtC,IAAAf,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAa,gBAAgB,EAAC,WAAW,CAAC,EAC7B,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAyB,iBAAiB,EAAE,MAAM,CAAC,EAC1C5B,oBAAA,CAAA6B,WAAW,CACZ;AAED;;;;;AAAA;AAAAnC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACiB,MAAM,CACX,sCAAsC,EACtC,IAAAnB,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAa,gBAAgB,EAAC,WAAW,CAAC,EAC7B,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAA2B,mBAAmB,EAAE,MAAM,CAAC,EAC5C9B,oBAAA,CAAA+B,aAAa,CACd;AAED;;;;;AAAA;AAAArC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,4CAA4C,EAC5C,IAAAN,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAa,gBAAgB,EAAC,WAAW,CAAC,EAC7B,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAA6B,oBAAoB,EAAE,MAAM,CAAC,EAC7ChC,oBAAA,CAAAiC,cAAc,CACf;AAED;;;;;AAAA;AAAAvC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACiB,MAAM,CACX,4CAA4C,EAC5C,IAAAnB,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAa,gBAAgB,EAAC,WAAW,CAAC,EAC7Bf,oBAAA,CAAAkC,cAAc,CACf;AAED;;;;;AAAA;AAAAxC,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACI,IAAI,CACT,gCAAgC,EAChC,IAAAN,YAAA,CAAAa,gBAAgB,EAAC,gBAAgB,CAAC,EAClC,IAAAb,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAgC,gBAAgB,EAAE,MAAM,CAAC,EACzCnC,oBAAA,CAAAoC,kBAAkB,CACnB;AAED;;;;;AAAA;AAAA1C,aAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACQ,GAAG,CACR,kBAAkB,EAClB,IAAAV,YAAA,CAAAO,eAAe,EAACN,yBAAA,CAAAkC,oBAAoB,EAAE,OAAO,CAAC,EAC9CrC,oBAAA,CAAAsC,cAAc,CACf;AAAC;AAAA5C,aAAA,GAAAE,CAAA;AAEF2C,OAAA,CAAAC,OAAA,GAAepC,MAAM", "ignoreList": []}