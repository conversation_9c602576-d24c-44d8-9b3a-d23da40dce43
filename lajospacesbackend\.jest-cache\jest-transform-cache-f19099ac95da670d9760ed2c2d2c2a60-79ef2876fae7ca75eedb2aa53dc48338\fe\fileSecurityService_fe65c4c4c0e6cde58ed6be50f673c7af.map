{"version": 3, "names": ["cov_1pll6sdcsd", "actualCoverage", "s", "exports", "validateFileSignature", "checkDangerousExtension", "checkDangerousMimeType", "scanFor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "validateFileSize", "checkForEmbeddedFiles", "performSecurityValidation", "sanitizeFilename", "generateSecureFilename", "isFileTypeAllowed", "logger_1", "require", "FILE_SIGNATURES", "DANGEROUS_EXTENSIONS", "DANGEROUS_MIME_TYPES", "SUSPICIOUS_PATTERNS", "buffer", "mimeType", "f", "signatures", "b", "logger", "warn", "signature", "length", "match", "every", "byte", "index", "bufferStart", "Array", "from", "slice", "expectedSignatures", "error", "filename", "extension", "toLowerCase", "substring", "lastIndexOf", "includes", "content", "toString", "Math", "min", "foundPatterns", "pattern", "test", "push", "source", "isSuspicious", "patterns", "size", "maxSize", "fileType", "typeLimits", "image", "document", "video", "audio", "typeLimit", "effectiveLimit", "Object", "values", "flat", "signatureCount", "i", "file", "issues", "warnings", "originalname", "mimetype", "contentScan", "join", "startsWith", "isSecure", "sanitized", "replace", "nameWithoutExt", "originalFilename", "timestamp", "Date", "now", "random", "context", "allowedTypes", "avatar", "property", "message", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\fileSecurityService.ts"], "sourcesContent": ["import { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\n// File type signatures for validation\r\nconst FILE_SIGNATURES = {\r\n  // Image formats\r\n  'image/jpeg': [\r\n    [0xFF, 0xD8, 0xFF],\r\n    [0xFF, 0xD8, 0xFF, 0xE0],\r\n    [0xFF, 0xD8, 0xFF, 0xE1],\r\n    [0xFF, 0xD8, 0xFF, 0xE2],\r\n    [0xFF, 0xD8, 0xFF, 0xE3],\r\n    [0xFF, 0xD8, 0xFF, 0xE8]\r\n  ],\r\n  'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]],\r\n  'image/gif': [\r\n    [0x47, 0x49, 0x46, 0x38, 0x37, 0x61],\r\n    [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]\r\n  ],\r\n  'image/webp': [[0x52, 0x49, 0x46, 0x46]],\r\n  'image/bmp': [[0x42, 0x4D]],\r\n  'image/tiff': [\r\n    [0x49, 0x49, 0x2A, 0x00],\r\n    [0x4D, 0x4D, 0x00, 0x2A]\r\n  ],\r\n  \r\n  // Document formats\r\n  'application/pdf': [[0x25, 0x50, 0x44, 0x46]],\r\n  'application/msword': [[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]],\r\n  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [\r\n    [0x50, 0x4B, 0x03, 0x04],\r\n    [0x50, 0x4B, 0x05, 0x06],\r\n    [0x50, 0x4B, 0x07, 0x08]\r\n  ],\r\n  \r\n  // Video formats\r\n  'video/mp4': [\r\n    [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70],\r\n    [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]\r\n  ],\r\n  'video/quicktime': [[0x00, 0x00, 0x00, 0x14, 0x66, 0x74, 0x79, 0x70]],\r\n  'video/webm': [[0x1A, 0x45, 0xDF, 0xA3]],\r\n  \r\n  // Audio formats\r\n  'audio/mpeg': [[0xFF, 0xFB], [0xFF, 0xF3], [0xFF, 0xF2]],\r\n  'audio/wav': [[0x52, 0x49, 0x46, 0x46]],\r\n  'audio/ogg': [[0x4F, 0x67, 0x67, 0x53]]\r\n};\r\n\r\n// Dangerous file extensions and MIME types\r\nconst DANGEROUS_EXTENSIONS = [\r\n  '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',\r\n  '.app', '.deb', '.pkg', '.dmg', '.rpm', '.msi', '.run', '.bin',\r\n  '.sh', '.bash', '.zsh', '.fish', '.ps1', '.psm1', '.psd1',\r\n  '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.cgi'\r\n];\r\n\r\nconst DANGEROUS_MIME_TYPES = [\r\n  'application/x-executable',\r\n  'application/x-msdownload',\r\n  'application/x-msdos-program',\r\n  'application/x-winexe',\r\n  'application/x-javascript',\r\n  'text/javascript',\r\n  'application/javascript',\r\n  'text/x-php',\r\n  'application/x-php',\r\n  'text/x-python',\r\n  'application/x-python-code',\r\n  'application/x-shellscript'\r\n];\r\n\r\n// Suspicious patterns in file content\r\nconst SUSPICIOUS_PATTERNS = [\r\n  // Script tags\r\n  /<script[\\s\\S]*?>[\\s\\S]*?<\\/script>/gi,\r\n  // PHP tags\r\n  /<\\?php[\\s\\S]*?\\?>/gi,\r\n  // ASP tags\r\n  /<%[\\s\\S]*?%>/gi,\r\n  // JavaScript protocols\r\n  /javascript:/gi,\r\n  // Data URLs with scripts\r\n  /data:.*script/gi,\r\n  // Common malware signatures\r\n  /eval\\s*\\(/gi,\r\n  /exec\\s*\\(/gi,\r\n  /system\\s*\\(/gi,\r\n  /shell_exec\\s*\\(/gi,\r\n  /passthru\\s*\\(/gi\r\n];\r\n\r\n/**\r\n * Validate file signature against MIME type\r\n */\r\nexport function validateFileSignature(buffer: Buffer, mimeType: string): boolean {\r\n  try {\r\n    const signatures = FILE_SIGNATURES[mimeType as keyof typeof FILE_SIGNATURES];\r\n    \r\n    if (!signatures) {\r\n      logger.warn('No signature validation available for MIME type:', mimeType);\r\n      return true; // Allow if no signature defined\r\n    }\r\n\r\n    // Check if buffer matches any of the valid signatures\r\n    for (const signature of signatures) {\r\n      if (buffer.length >= signature.length) {\r\n        const match = signature.every((byte, index) => buffer[index] === byte);\r\n        if (match) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n\r\n    logger.warn('File signature validation failed:', {\r\n      mimeType,\r\n      bufferStart: Array.from(buffer.slice(0, 16)),\r\n      expectedSignatures: signatures\r\n    });\r\n\r\n    return false;\r\n  } catch (error) {\r\n    logger.error('Error validating file signature:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Check for dangerous file extensions\r\n */\r\nexport function checkDangerousExtension(filename: string): boolean {\r\n  try {\r\n    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));\r\n    return DANGEROUS_EXTENSIONS.includes(extension);\r\n  } catch (error) {\r\n    logger.error('Error checking file extension:', error);\r\n    return true; // Err on the side of caution\r\n  }\r\n}\r\n\r\n/**\r\n * Check for dangerous MIME types\r\n */\r\nexport function checkDangerousMimeType(mimeType: string): boolean {\r\n  try {\r\n    return DANGEROUS_MIME_TYPES.includes(mimeType.toLowerCase());\r\n  } catch (error) {\r\n    logger.error('Error checking MIME type:', error);\r\n    return true; // Err on the side of caution\r\n  }\r\n}\r\n\r\n/**\r\n * Scan file content for suspicious patterns\r\n */\r\nexport function scanForSuspiciousContent(buffer: Buffer): {\r\n  isSuspicious: boolean;\r\n  patterns: string[];\r\n} {\r\n  try {\r\n    const content = buffer.toString('utf8', 0, Math.min(buffer.length, 10240)); // Check first 10KB\r\n    const foundPatterns: string[] = [];\r\n\r\n    for (const pattern of SUSPICIOUS_PATTERNS) {\r\n      if (pattern.test(content)) {\r\n        foundPatterns.push(pattern.source);\r\n      }\r\n    }\r\n\r\n    return {\r\n      isSuspicious: foundPatterns.length > 0,\r\n      patterns: foundPatterns\r\n    };\r\n  } catch (error) {\r\n    logger.error('Error scanning file content:', error);\r\n    return {\r\n      isSuspicious: true,\r\n      patterns: ['scan_error']\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Validate file size limits\r\n */\r\nexport function validateFileSize(\r\n  size: number,\r\n  maxSize: number,\r\n  fileType: 'image' | 'document' | 'video' | 'audio' = 'image'\r\n): boolean {\r\n  try {\r\n    // Type-specific size limits (in bytes)\r\n    const typeLimits = {\r\n      image: 15 * 1024 * 1024, // 15MB\r\n      document: 10 * 1024 * 1024, // 10MB\r\n      video: 100 * 1024 * 1024, // 100MB\r\n      audio: 20 * 1024 * 1024 // 20MB\r\n    };\r\n\r\n    const typeLimit = typeLimits[fileType];\r\n    const effectiveLimit = Math.min(maxSize, typeLimit);\r\n\r\n    if (size > effectiveLimit) {\r\n      logger.warn('File size exceeds limit:', {\r\n        size,\r\n        maxSize,\r\n        typeLimit,\r\n        effectiveLimit,\r\n        fileType\r\n      });\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  } catch (error) {\r\n    logger.error('Error validating file size:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Check for embedded files or polyglot attacks\r\n */\r\nexport function checkForEmbeddedFiles(buffer: Buffer): boolean {\r\n  try {\r\n    // Look for multiple file signatures in the same buffer\r\n    const signatures = Object.values(FILE_SIGNATURES).flat();\r\n    let signatureCount = 0;\r\n\r\n    for (let i = 0; i < buffer.length - 8; i++) {\r\n      for (const signature of signatures) {\r\n        if (signature.length <= buffer.length - i) {\r\n          const match = signature.every((byte, index) => buffer[i + index] === byte);\r\n          if (match) {\r\n            signatureCount++;\r\n            if (signatureCount > 1) {\r\n              logger.warn('Multiple file signatures detected - possible polyglot attack');\r\n              return true;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return false;\r\n  } catch (error) {\r\n    logger.error('Error checking for embedded files:', error);\r\n    return true; // Err on the side of caution\r\n  }\r\n}\r\n\r\n/**\r\n * Comprehensive file security validation\r\n */\r\nexport async function performSecurityValidation(\r\n  file: Express.Multer.File\r\n): Promise<{\r\n  isSecure: boolean;\r\n  issues: string[];\r\n  warnings: string[];\r\n}> {\r\n  const issues: string[] = [];\r\n  const warnings: string[] = [];\r\n\r\n  try {\r\n    // 1. Check dangerous extensions\r\n    if (checkDangerousExtension(file.originalname)) {\r\n      issues.push('Dangerous file extension detected');\r\n    }\r\n\r\n    // 2. Check dangerous MIME types\r\n    if (checkDangerousMimeType(file.mimetype)) {\r\n      issues.push('Dangerous MIME type detected');\r\n    }\r\n\r\n    // 3. Validate file signature\r\n    if (!validateFileSignature(file.buffer, file.mimetype)) {\r\n      issues.push('File signature does not match MIME type');\r\n    }\r\n\r\n    // 4. Scan for suspicious content\r\n    const contentScan = scanForSuspiciousContent(file.buffer);\r\n    if (contentScan.isSuspicious) {\r\n      issues.push(`Suspicious content patterns detected: ${contentScan.patterns.join(', ')}`);\r\n    }\r\n\r\n    // 5. Check for embedded files\r\n    if (checkForEmbeddedFiles(file.buffer)) {\r\n      warnings.push('Multiple file signatures detected - possible polyglot file');\r\n    }\r\n\r\n    // 6. Validate file size\r\n    const fileType = file.mimetype.startsWith('image/') ? 'image' :\r\n                    file.mimetype.startsWith('video/') ? 'video' :\r\n                    file.mimetype.startsWith('audio/') ? 'audio' : 'document';\r\n    \r\n    if (!validateFileSize(file.size, file.size, fileType)) {\r\n      issues.push('File size exceeds security limits');\r\n    }\r\n\r\n    // 7. Check filename for suspicious characters\r\n    if (/[<>:\"|?*\\x00-\\x1f]/.test(file.originalname)) {\r\n      warnings.push('Filename contains suspicious characters');\r\n    }\r\n\r\n    // 8. Check for excessively long filename\r\n    if (file.originalname.length > 255) {\r\n      issues.push('Filename is excessively long');\r\n    }\r\n\r\n    const isSecure = issues.length === 0;\r\n\r\n    if (!isSecure) {\r\n      logger.warn('File security validation failed:', {\r\n        filename: file.originalname,\r\n        mimetype: file.mimetype,\r\n        size: file.size,\r\n        issues,\r\n        warnings\r\n      });\r\n    }\r\n\r\n    return {\r\n      isSecure,\r\n      issues,\r\n      warnings\r\n    };\r\n\r\n  } catch (error) {\r\n    logger.error('Error during security validation:', error);\r\n    return {\r\n      isSecure: false,\r\n      issues: ['Security validation error'],\r\n      warnings: []\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Sanitize filename for safe storage\r\n */\r\nexport function sanitizeFilename(filename: string): string {\r\n  try {\r\n    // Remove path separators and dangerous characters\r\n    let sanitized = filename.replace(/[<>:\"|?*\\x00-\\x1f]/g, '');\r\n    \r\n    // Remove leading/trailing dots and spaces\r\n    sanitized = sanitized.replace(/^[.\\s]+|[.\\s]+$/g, '');\r\n    \r\n    // Limit length\r\n    if (sanitized.length > 255) {\r\n      const extension = sanitized.substring(sanitized.lastIndexOf('.'));\r\n      const nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));\r\n      sanitized = nameWithoutExt.substring(0, 255 - extension.length) + extension;\r\n    }\r\n    \r\n    // Ensure filename is not empty\r\n    if (!sanitized) {\r\n      sanitized = 'file';\r\n    }\r\n    \r\n    return sanitized;\r\n  } catch (error) {\r\n    logger.error('Error sanitizing filename:', error);\r\n    return 'file';\r\n  }\r\n}\r\n\r\n/**\r\n * Generate secure random filename\r\n */\r\nexport function generateSecureFilename(originalFilename: string): string {\r\n  try {\r\n    const extension = originalFilename.substring(originalFilename.lastIndexOf('.'));\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 15);\r\n    \r\n    return `${timestamp}_${random}${extension}`;\r\n  } catch (error) {\r\n    logger.error('Error generating secure filename:', error);\r\n    return `${Date.now()}_file`;\r\n  }\r\n}\r\n\r\n/**\r\n * Check if file type is allowed for specific context\r\n */\r\nexport function isFileTypeAllowed(\r\n  mimeType: string,\r\n  context: 'avatar' | 'property' | 'message' | 'document'\r\n): boolean {\r\n  const allowedTypes = {\r\n    avatar: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],\r\n    property: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],\r\n    message: [\r\n      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',\r\n      'application/pdf', 'application/msword',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n      'video/mp4', 'video/quicktime', 'video/webm',\r\n      'audio/mpeg', 'audio/wav', 'audio/ogg'\r\n    ],\r\n    document: [\r\n      'application/pdf', 'application/msword',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n      'text/plain', 'text/csv'\r\n    ]\r\n  };\r\n\r\n  return allowedTypes[context].includes(mimeType);\r\n}\r\n\r\nexport default {\r\n  validateFileSignature,\r\n  checkDangerousExtension,\r\n  checkDangerousMimeType,\r\n  scanForSuspiciousContent,\r\n  validateFileSize,\r\n  checkForEmbeddedFiles,\r\n  performSecurityValidation,\r\n  sanitizeFilename,\r\n  generateSecureFilename,\r\n  isFileTypeAllowed\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;AA0FFC,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AA8BC;AAAAJ,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAE,uBAAA,GAAAA,uBAAA;AAQC;AAAAL,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAG,sBAAA,GAAAA,sBAAA;AAOC;AAAAN,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAI,wBAAA,GAAAA,wBAAA;AAyBC;AAAAP,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAK,gBAAA,GAAAA,gBAAA;AAiCC;AAAAR,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAM,qBAAA,GAAAA,qBAAA;AA0BC;AAAAT,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAO,yBAAA,GAAAA,yBAAA;AAkFC;AAAAV,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAQ,gBAAA,GAAAA,gBAAA;AAyBC;AAAAX,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAS,sBAAA,GAAAA,sBAAA;AAWC;AAAAZ,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAU,iBAAA,GAAAA,iBAAA;AAnYA,MAAAC,QAAA;AAAA;AAAA,CAAAd,cAAA,GAAAE,CAAA,QAAAa,OAAA;AAGA;AACA,MAAMC,eAAe;AAAA;AAAA,CAAAhB,cAAA,GAAAE,CAAA,QAAG;EACtB;EACA,YAAY,EAAE,CACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAClB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACzB;EACD,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC/D,WAAW,EAAE,CACX,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACpC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACrC;EACD,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACxC,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC3B,YAAY,EAAE,CACZ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACzB;EAED;EACA,iBAAiB,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC7C,oBAAoB,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACxE,yEAAyE,EAAE,CACzE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACxB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACzB;EAED;EACA,WAAW,EAAE,CACX,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAChD,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACjD;EACD,iBAAiB,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACrE,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAExC;EACA,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EACxD,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACvC,WAAW,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;CACvC;AAED;AACA,MAAMe,oBAAoB;AAAA;AAAA,CAAAjB,cAAA,GAAAE,CAAA,QAAG,CAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EACrE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9D,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EACzD,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAC7D;AAED,MAAMgB,oBAAoB;AAAA;AAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAG,CAC3B,0BAA0B,EAC1B,0BAA0B,EAC1B,6BAA6B,EAC7B,sBAAsB,EACtB,0BAA0B,EAC1B,iBAAiB,EACjB,wBAAwB,EACxB,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,2BAA2B,EAC3B,2BAA2B,CAC5B;AAED;AACA,MAAMiB,mBAAmB;AAAA;AAAA,CAAAnB,cAAA,GAAAE,CAAA,QAAG;AAC1B;AACA,sCAAsC;AACtC;AACA,qBAAqB;AACrB;AACA,gBAAgB;AAChB;AACA,eAAe;AACf;AACA,iBAAiB;AACjB;AACA,aAAa,EACb,aAAa,EACb,eAAe,EACf,mBAAmB,EACnB,iBAAiB,CAClB;AAED;;;AAGA,SAAgBE,qBAAqBA,CAACgB,MAAc,EAAEC,QAAgB;EAAA;EAAArB,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EACpE,IAAI;IACF,MAAMqB,UAAU;IAAA;IAAA,CAAAvB,cAAA,GAAAE,CAAA,QAAGc,eAAe,CAACK,QAAwC,CAAC;IAAC;IAAArB,cAAA,GAAAE,CAAA;IAE7E,IAAI,CAACqB,UAAU,EAAE;MAAA;MAAAvB,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACfY,QAAA,CAAAW,MAAM,CAACC,IAAI,CAAC,kDAAkD,EAAEL,QAAQ,CAAC;MAAC;MAAArB,cAAA,GAAAE,CAAA;MAC1E,OAAO,IAAI,CAAC,CAAC;IACf,CAAC;IAAA;IAAA;MAAAF,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,KAAK,MAAMyB,SAAS,IAAIJ,UAAU,EAAE;MAAA;MAAAvB,cAAA,GAAAE,CAAA;MAClC,IAAIkB,MAAM,CAACQ,MAAM,IAAID,SAAS,CAACC,MAAM,EAAE;QAAA;QAAA5B,cAAA,GAAAwB,CAAA;QACrC,MAAMK,KAAK;QAAA;QAAA,CAAA7B,cAAA,GAAAE,CAAA,QAAGyB,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UAAA;UAAAhC,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAE,CAAA;UAAA,OAAAkB,MAAM,CAACY,KAAK,CAAC,KAAKD,IAAI;QAAJ,CAAI,CAAC;QAAC;QAAA/B,cAAA,GAAAE,CAAA;QACvE,IAAI2B,KAAK,EAAE;UAAA;UAAA7B,cAAA,GAAAwB,CAAA;UAAAxB,cAAA,GAAAE,CAAA;UACT,OAAO,IAAI;QACb,CAAC;QAAA;QAAA;UAAAF,cAAA,GAAAwB,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAxB,cAAA,GAAAwB,CAAA;MAAA;IACH;IAAC;IAAAxB,cAAA,GAAAE,CAAA;IAEDY,QAAA,CAAAW,MAAM,CAACC,IAAI,CAAC,mCAAmC,EAAE;MAC/CL,QAAQ;MACRY,WAAW,EAAEC,KAAK,CAACC,IAAI,CAACf,MAAM,CAACgB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC5CC,kBAAkB,EAAEd;KACrB,CAAC;IAAC;IAAAvB,cAAA,GAAAE,CAAA;IAEH,OAAO,KAAK;EACd,CAAC,CAAC,OAAOoC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACxD,OAAO,KAAK;EACd;AACF;AAEA;;;AAGA,SAAgBG,uBAAuBA,CAACkC,QAAgB;EAAA;EAAAvC,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EACtD,IAAI;IACF,MAAMsC,SAAS;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA,QAAGqC,QAAQ,CAACE,WAAW,EAAE,CAACC,SAAS,CAACH,QAAQ,CAACI,WAAW,CAAC,GAAG,CAAC,CAAC;IAAC;IAAA3C,cAAA,GAAAE,CAAA;IAC9E,OAAOe,oBAAoB,CAAC2B,QAAQ,CAACJ,SAAS,CAAC;EACjD,CAAC,CAAC,OAAOF,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACtD,OAAO,IAAI,CAAC,CAAC;EACf;AACF;AAEA;;;AAGA,SAAgBI,sBAAsBA,CAACe,QAAgB;EAAA;EAAArB,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EACrD,IAAI;IAAA;IAAAF,cAAA,GAAAE,CAAA;IACF,OAAOgB,oBAAoB,CAAC0B,QAAQ,CAACvB,QAAQ,CAACoB,WAAW,EAAE,CAAC;EAC9D,CAAC,CAAC,OAAOH,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACjD,OAAO,IAAI,CAAC,CAAC;EACf;AACF;AAEA;;;AAGA,SAAgBK,wBAAwBA,CAACa,MAAc;EAAA;EAAApB,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EAIrD,IAAI;IACF,MAAM2C,OAAO;IAAA;IAAA,CAAA7C,cAAA,GAAAE,CAAA,QAAGkB,MAAM,CAAC0B,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC5B,MAAM,CAACQ,MAAM,EAAE,KAAK,CAAC,CAAC,EAAC,CAAC;IAC5E,MAAMqB,aAAa;IAAA;IAAA,CAAAjD,cAAA,GAAAE,CAAA,QAAa,EAAE;IAAC;IAAAF,cAAA,GAAAE,CAAA;IAEnC,KAAK,MAAMgD,OAAO,IAAI/B,mBAAmB,EAAE;MAAA;MAAAnB,cAAA,GAAAE,CAAA;MACzC,IAAIgD,OAAO,CAACC,IAAI,CAACN,OAAO,CAAC,EAAE;QAAA;QAAA7C,cAAA,GAAAwB,CAAA;QAAAxB,cAAA,GAAAE,CAAA;QACzB+C,aAAa,CAACG,IAAI,CAACF,OAAO,CAACG,MAAM,CAAC;MACpC,CAAC;MAAA;MAAA;QAAArD,cAAA,GAAAwB,CAAA;MAAA;IACH;IAAC;IAAAxB,cAAA,GAAAE,CAAA;IAED,OAAO;MACLoD,YAAY,EAAEL,aAAa,CAACrB,MAAM,GAAG,CAAC;MACtC2B,QAAQ,EAAEN;KACX;EACH,CAAC,CAAC,OAAOX,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACpD,OAAO;MACLoD,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CAAC,YAAY;KACxB;EACH;AACF;AAEA;;;AAGA,SAAgB/C,gBAAgBA,CAC9BgD,IAAY,EACZC,OAAe,EACfC,QAAA;AAAA;AAAA,CAAA1D,cAAA,GAAAwB,CAAA,UAAqD,OAAO;EAAA;EAAAxB,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EAE5D,IAAI;IACF;IACA,MAAMyD,UAAU;IAAA;IAAA,CAAA3D,cAAA,GAAAE,CAAA,QAAG;MACjB0D,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;MAAE;MACzBC,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;MAAE;MAC5BC,KAAK,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;MAAE;MAC1BC,KAAK,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;KACzB;IAED,MAAMC,SAAS;IAAA;IAAA,CAAAhE,cAAA,GAAAE,CAAA,QAAGyD,UAAU,CAACD,QAAQ,CAAC;IACtC,MAAMO,cAAc;IAAA;IAAA,CAAAjE,cAAA,GAAAE,CAAA,QAAG6C,IAAI,CAACC,GAAG,CAACS,OAAO,EAAEO,SAAS,CAAC;IAAC;IAAAhE,cAAA,GAAAE,CAAA;IAEpD,IAAIsD,IAAI,GAAGS,cAAc,EAAE;MAAA;MAAAjE,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACzBY,QAAA,CAAAW,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAE;QACtC8B,IAAI;QACJC,OAAO;QACPO,SAAS;QACTC,cAAc;QACdP;OACD,CAAC;MAAC;MAAA1D,cAAA,GAAAE,CAAA;MACH,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAF,cAAA,GAAAwB,CAAA;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAED,OAAO,IAAI;EACb,CAAC,CAAC,OAAOoC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACnD,OAAO,KAAK;EACd;AACF;AAEA;;;AAGA,SAAgBO,qBAAqBA,CAACW,MAAc;EAAA;EAAApB,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EAClD,IAAI;IACF;IACA,MAAMqB,UAAU;IAAA;IAAA,CAAAvB,cAAA,GAAAE,CAAA,QAAGgE,MAAM,CAACC,MAAM,CAACnD,eAAe,CAAC,CAACoD,IAAI,EAAE;IACxD,IAAIC,cAAc;IAAA;IAAA,CAAArE,cAAA,GAAAE,CAAA,QAAG,CAAC;IAAC;IAAAF,cAAA,GAAAE,CAAA;IAEvB,KAAK,IAAIoE,CAAC;IAAA;IAAA,CAAAtE,cAAA,GAAAE,CAAA,QAAG,CAAC,GAAEoE,CAAC,GAAGlD,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE0C,CAAC,EAAE,EAAE;MAAA;MAAAtE,cAAA,GAAAE,CAAA;MAC1C,KAAK,MAAMyB,SAAS,IAAIJ,UAAU,EAAE;QAAA;QAAAvB,cAAA,GAAAE,CAAA;QAClC,IAAIyB,SAAS,CAACC,MAAM,IAAIR,MAAM,CAACQ,MAAM,GAAG0C,CAAC,EAAE;UAAA;UAAAtE,cAAA,GAAAwB,CAAA;UACzC,MAAMK,KAAK;UAAA;UAAA,CAAA7B,cAAA,GAAAE,CAAA,QAAGyB,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YAAA;YAAAhC,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAE,CAAA;YAAA,OAAAkB,MAAM,CAACkD,CAAC,GAAGtC,KAAK,CAAC,KAAKD,IAAI;UAAJ,CAAI,CAAC;UAAC;UAAA/B,cAAA,GAAAE,CAAA;UAC3E,IAAI2B,KAAK,EAAE;YAAA;YAAA7B,cAAA,GAAAwB,CAAA;YAAAxB,cAAA,GAAAE,CAAA;YACTmE,cAAc,EAAE;YAAC;YAAArE,cAAA,GAAAE,CAAA;YACjB,IAAImE,cAAc,GAAG,CAAC,EAAE;cAAA;cAAArE,cAAA,GAAAwB,CAAA;cAAAxB,cAAA,GAAAE,CAAA;cACtBY,QAAA,CAAAW,MAAM,CAACC,IAAI,CAAC,8DAA8D,CAAC;cAAC;cAAA1B,cAAA,GAAAE,CAAA;cAC5E,OAAO,IAAI;YACb,CAAC;YAAA;YAAA;cAAAF,cAAA,GAAAwB,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAAxB,cAAA,GAAAwB,CAAA;UAAA;QACH,CAAC;QAAA;QAAA;UAAAxB,cAAA,GAAAwB,CAAA;QAAA;MACH;IACF;IAAC;IAAAxB,cAAA,GAAAE,CAAA;IAED,OAAO,KAAK;EACd,CAAC,CAAC,OAAOoC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IAC1D,OAAO,IAAI,CAAC,CAAC;EACf;AACF;AAEA;;;AAGO,eAAeQ,yBAAyBA,CAC7C6D,IAAyB;EAAA;EAAAvE,cAAA,GAAAsB,CAAA;EAMzB,MAAMkD,MAAM;EAAA;EAAA,CAAAxE,cAAA,GAAAE,CAAA,QAAa,EAAE;EAC3B,MAAMuE,QAAQ;EAAA;EAAA,CAAAzE,cAAA,GAAAE,CAAA,QAAa,EAAE;EAAC;EAAAF,cAAA,GAAAE,CAAA;EAE9B,IAAI;IAAA;IAAAF,cAAA,GAAAE,CAAA;IACF;IACA,IAAIG,uBAAuB,CAACkE,IAAI,CAACG,YAAY,CAAC,EAAE;MAAA;MAAA1E,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MAC9CsE,MAAM,CAACpB,IAAI,CAAC,mCAAmC,CAAC;IAClD,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAII,sBAAsB,CAACiE,IAAI,CAACI,QAAQ,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACzCsE,MAAM,CAACpB,IAAI,CAAC,8BAA8B,CAAC;IAC7C,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAI,CAACE,qBAAqB,CAACmE,IAAI,CAACnD,MAAM,EAAEmD,IAAI,CAACI,QAAQ,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACtDsE,MAAM,CAACpB,IAAI,CAAC,yCAAyC,CAAC;IACxD,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IACA,MAAMoD,WAAW;IAAA;IAAA,CAAA5E,cAAA,GAAAE,CAAA,QAAGK,wBAAwB,CAACgE,IAAI,CAACnD,MAAM,CAAC;IAAC;IAAApB,cAAA,GAAAE,CAAA;IAC1D,IAAI0E,WAAW,CAACtB,YAAY,EAAE;MAAA;MAAAtD,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MAC5BsE,MAAM,CAACpB,IAAI,CAAC,yCAAyCwB,WAAW,CAACrB,QAAQ,CAACsB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACzF,CAAC;IAAA;IAAA;MAAA7E,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAIO,qBAAqB,CAAC8D,IAAI,CAACnD,MAAM,CAAC,EAAE;MAAA;MAAApB,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACtCuE,QAAQ,CAACrB,IAAI,CAAC,4DAA4D,CAAC;IAC7E,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IACA,MAAMkC,QAAQ;IAAA;IAAA,CAAA1D,cAAA,GAAAE,CAAA,QAAGqE,IAAI,CAACI,QAAQ,CAACG,UAAU,CAAC,QAAQ,CAAC;IAAA;IAAA,CAAA9E,cAAA,GAAAwB,CAAA,WAAG,OAAO;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAC7C+C,IAAI,CAACI,QAAQ,CAACG,UAAU,CAAC,QAAQ,CAAC;IAAA;IAAA,CAAA9E,cAAA,GAAAwB,CAAA,WAAG,OAAO;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAC5C+C,IAAI,CAACI,QAAQ,CAACG,UAAU,CAAC,QAAQ,CAAC;IAAA;IAAA,CAAA9E,cAAA,GAAAwB,CAAA,WAAG,OAAO;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAG,UAAU;IAAC;IAAAxB,cAAA,GAAAE,CAAA;IAE1E,IAAI,CAACM,gBAAgB,CAAC+D,IAAI,CAACf,IAAI,EAAEe,IAAI,CAACf,IAAI,EAAEE,QAAQ,CAAC,EAAE;MAAA;MAAA1D,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACrDsE,MAAM,CAACpB,IAAI,CAAC,mCAAmC,CAAC;IAClD,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAI,oBAAoB,CAACiD,IAAI,CAACoB,IAAI,CAACG,YAAY,CAAC,EAAE;MAAA;MAAA1E,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MAChDuE,QAAQ,CAACrB,IAAI,CAAC,yCAAyC,CAAC;IAC1D,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAIqE,IAAI,CAACG,YAAY,CAAC9C,MAAM,GAAG,GAAG,EAAE;MAAA;MAAA5B,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MAClCsE,MAAM,CAACpB,IAAI,CAAC,8BAA8B,CAAC;IAC7C,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAAwB,CAAA;IAAA;IAED,MAAMuD,QAAQ;IAAA;IAAA,CAAA/E,cAAA,GAAAE,CAAA,QAAGsE,MAAM,CAAC5C,MAAM,KAAK,CAAC;IAAC;IAAA5B,cAAA,GAAAE,CAAA;IAErC,IAAI,CAAC6E,QAAQ,EAAE;MAAA;MAAA/E,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACbY,QAAA,CAAAW,MAAM,CAACC,IAAI,CAAC,kCAAkC,EAAE;QAC9Ca,QAAQ,EAAEgC,IAAI,CAACG,YAAY;QAC3BC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;QACvBnB,IAAI,EAAEe,IAAI,CAACf,IAAI;QACfgB,MAAM;QACNC;OACD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAzE,cAAA,GAAAwB,CAAA;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAED,OAAO;MACL6E,QAAQ;MACRP,MAAM;MACNC;KACD;EAEH,CAAC,CAAC,OAAOnC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACzD,OAAO;MACL6E,QAAQ,EAAE,KAAK;MACfP,MAAM,EAAE,CAAC,2BAA2B,CAAC;MACrCC,QAAQ,EAAE;KACX;EACH;AACF;AAEA;;;AAGA,SAAgB9D,gBAAgBA,CAAC4B,QAAgB;EAAA;EAAAvC,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EAC/C,IAAI;IACF;IACA,IAAI8E,SAAS;IAAA;IAAA,CAAAhF,cAAA,GAAAE,CAAA,SAAGqC,QAAQ,CAAC0C,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;IAE3D;IAAA;IAAAjF,cAAA,GAAAE,CAAA;IACA8E,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAErD;IAAA;IAAAjF,cAAA,GAAAE,CAAA;IACA,IAAI8E,SAAS,CAACpD,MAAM,GAAG,GAAG,EAAE;MAAA;MAAA5B,cAAA,GAAAwB,CAAA;MAC1B,MAAMgB,SAAS;MAAA;MAAA,CAAAxC,cAAA,GAAAE,CAAA,SAAG8E,SAAS,CAACtC,SAAS,CAACsC,SAAS,CAACrC,WAAW,CAAC,GAAG,CAAC,CAAC;MACjE,MAAMuC,cAAc;MAAA;MAAA,CAAAlF,cAAA,GAAAE,CAAA,SAAG8E,SAAS,CAACtC,SAAS,CAAC,CAAC,EAAEsC,SAAS,CAACrC,WAAW,CAAC,GAAG,CAAC,CAAC;MAAC;MAAA3C,cAAA,GAAAE,CAAA;MAC1E8E,SAAS,GAAGE,cAAc,CAACxC,SAAS,CAAC,CAAC,EAAE,GAAG,GAAGF,SAAS,CAACZ,MAAM,CAAC,GAAGY,SAAS;IAC7E,CAAC;IAAA;IAAA;MAAAxC,cAAA,GAAAwB,CAAA;IAAA;IAED;IAAAxB,cAAA,GAAAE,CAAA;IACA,IAAI,CAAC8E,SAAS,EAAE;MAAA;MAAAhF,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAE,CAAA;MACd8E,SAAS,GAAG,MAAM;IACpB,CAAC;IAAA;IAAA;MAAAhF,cAAA,GAAAwB,CAAA;IAAA;IAAAxB,cAAA,GAAAE,CAAA;IAED,OAAO8E,SAAS;EAClB,CAAC,CAAC,OAAO1C,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IAClD,OAAO,MAAM;EACf;AACF;AAEA;;;AAGA,SAAgBU,sBAAsBA,CAACuE,gBAAwB;EAAA;EAAAnF,cAAA,GAAAsB,CAAA;EAAAtB,cAAA,GAAAE,CAAA;EAC7D,IAAI;IACF,MAAMsC,SAAS;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA,SAAGiF,gBAAgB,CAACzC,SAAS,CAACyC,gBAAgB,CAACxC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/E,MAAMyC,SAAS;IAAA;IAAA,CAAApF,cAAA,GAAAE,CAAA,SAAGmF,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAAvF,cAAA,GAAAE,CAAA,SAAG6C,IAAI,CAACwC,MAAM,EAAE,CAACzC,QAAQ,CAAC,EAAE,CAAC,CAACJ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;IAAC;IAAA1C,cAAA,GAAAE,CAAA;IAE3D,OAAO,GAAGkF,SAAS,IAAIG,MAAM,GAAG/C,SAAS,EAAE;EAC7C,CAAC,CAAC,OAAOF,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAE,CAAA;IACdY,QAAA,CAAAW,MAAM,CAACa,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAE,CAAA;IACzD,OAAO,GAAGmF,IAAI,CAACC,GAAG,EAAE,OAAO;EAC7B;AACF;AAEA;;;AAGA,SAAgBzE,iBAAiBA,CAC/BQ,QAAgB,EAChBmE,OAAuD;EAAA;EAAAxF,cAAA,GAAAsB,CAAA;EAEvD,MAAMmE,YAAY;EAAA;EAAA,CAAAzF,cAAA,GAAAE,CAAA,SAAG;IACnBwF,MAAM,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC9DC,QAAQ,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC7EC,OAAO,EAAE,CACP,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EACjE,iBAAiB,EAAE,oBAAoB,EACvC,yEAAyE,EACzE,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAC5C,YAAY,EAAE,WAAW,EAAE,WAAW,CACvC;IACD/B,QAAQ,EAAE,CACR,iBAAiB,EAAE,oBAAoB,EACvC,yEAAyE,EACzE,YAAY,EAAE,UAAU;GAE3B;EAAC;EAAA7D,cAAA,GAAAE,CAAA;EAEF,OAAOuF,YAAY,CAACD,OAAO,CAAC,CAAC5C,QAAQ,CAACvB,QAAQ,CAAC;AACjD;AAAC;AAAArB,cAAA,GAAAE,CAAA;AAEDC,OAAA,CAAA0F,OAAA,GAAe;EACbzF,qBAAqB;EACrBC,uBAAuB;EACvBC,sBAAsB;EACtBC,wBAAwB;EACxBC,gBAAgB;EAChBC,qBAAqB;EACrBC,yBAAyB;EACzBC,gBAAgB;EAChBC,sBAAsB;EACtBC;CACD", "ignoreList": []}