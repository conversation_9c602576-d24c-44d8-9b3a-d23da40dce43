{"version": 3, "names": ["cov_xwy5xvzrm", "actualCoverage", "exports", "generateAccessToken", "s", "generateRefreshToken", "generateTokenPair", "verifyAccessToken", "verifyRefreshToken", "revokeRefreshToken", "revokeAllRefreshTokens", "generatePasswordResetToken", "verifyPasswordResetToken", "generateEmailVerificationToken", "verifyEmailVerificationToken", "jsonwebtoken_1", "__importDefault", "require", "crypto_1", "environment_1", "redis_1", "logger_1", "payload", "f", "signOptions", "expiresIn", "config", "JWT_EXPIRES_IN", "issuer", "audience", "token", "default", "sign", "JWT_SECRET", "logger", "info", "userId", "error", "Error", "tokenId", "randomUUID", "JWT_REFRESH_EXPIRES_IN", "JWT_REFRESH_SECRET", "accessToken", "refreshToken", "refreshPayload", "decode", "refresh<PERSON><PERSON>", "redisKeys", "refreshExpiresIn", "getTokenExpirationTime", "redisUtils", "set", "userTokensKey", "userSockets", "sadd", "expire", "accessExpiresIn", "verify", "TokenExpiredError", "b", "JsonWebTokenError", "storedUserId", "get", "del", "srem", "tokenIds", "smembers", "email", "type", "PASSWORD_RESET_SECRET", "timeString", "timeValue", "parseInt", "slice", "timeUnit"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\jwt.ts"], "sourcesContent": ["import jwt, { SignOptions } from 'jsonwebtoken';\r\nimport crypto from 'crypto';\r\nimport { config } from '../config/environment';\r\nimport { redisUtils, redisKeys } from '../config/redis';\r\nimport { logger } from './logger';\r\n\r\n// JWT payload interface\r\nexport interface JWTPayload {\r\n  userId: string;\r\n  email: string;\r\n  accountType: 'seeker' | 'owner' | 'both';\r\n  isEmailVerified: boolean;\r\n  iat?: number;\r\n  exp?: number;\r\n}\r\n\r\n// Refresh token payload interface\r\nexport interface RefreshTokenPayload {\r\n  userId: string;\r\n  tokenId: string;\r\n  iat?: number;\r\n  exp?: number;\r\n}\r\n\r\n// Token pair interface\r\nexport interface TokenPair {\r\n  accessToken: string;\r\n  refreshToken: string;\r\n  expiresIn: number;\r\n  refreshExpiresIn: number;\r\n}\r\n\r\n/**\r\n * Generate access token\r\n */\r\nexport function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\r\n  try {\r\n    const signOptions: SignOptions = {\r\n      expiresIn: config.JWT_EXPIRES_IN as any,\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-users'\r\n    };\r\n\r\n    const token = jwt.sign(payload, config.JWT_SECRET, signOptions);\r\n\r\n    logger.info(`Access token generated for user: ${payload.userId}`);\r\n    return token;\r\n  } catch (error) {\r\n    logger.error('Error generating access token:', error);\r\n    throw new Error('Failed to generate access token');\r\n  }\r\n}\r\n\r\n/**\r\n * Generate refresh token\r\n */\r\nexport function generateRefreshToken(userId: string): string {\r\n  try {\r\n    const tokenId = crypto.randomUUID();\r\n    \r\n    const payload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {\r\n      userId,\r\n      tokenId\r\n    };\r\n\r\n    const signOptions: SignOptions = {\r\n      expiresIn: config.JWT_REFRESH_EXPIRES_IN as any,\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-refresh'\r\n    };\r\n\r\n    const token = jwt.sign(payload, config.JWT_REFRESH_SECRET, signOptions);\r\n\r\n    logger.info(`Refresh token generated for user: ${userId}`);\r\n    return token;\r\n  } catch (error) {\r\n    logger.error('Error generating refresh token:', error);\r\n    throw new Error('Failed to generate refresh token');\r\n  }\r\n}\r\n\r\n/**\r\n * Generate token pair (access + refresh)\r\n */\r\nexport async function generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<TokenPair> {\r\n  try {\r\n    const accessToken = generateAccessToken(payload);\r\n    const refreshToken = generateRefreshToken(payload.userId);\r\n\r\n    // Store refresh token in Redis\r\n    const refreshPayload = jwt.decode(refreshToken) as RefreshTokenPayload;\r\n    const refreshKey = redisKeys.refreshToken(refreshPayload.tokenId);\r\n    const refreshExpiresIn = getTokenExpirationTime(config.JWT_REFRESH_EXPIRES_IN);\r\n    \r\n    await redisUtils.set(refreshKey, payload.userId, refreshExpiresIn);\r\n\r\n    // Store in user's refresh token list (for revocation)\r\n    const userTokensKey = redisKeys.userSockets(payload.userId);\r\n    await redisUtils.sadd(userTokensKey, refreshPayload.tokenId);\r\n    await redisUtils.expire(userTokensKey, refreshExpiresIn);\r\n\r\n    const accessExpiresIn = getTokenExpirationTime(config.JWT_EXPIRES_IN);\r\n\r\n    logger.info(`Token pair generated for user: ${payload.userId}`);\r\n\r\n    return {\r\n      accessToken,\r\n      refreshToken,\r\n      expiresIn: accessExpiresIn,\r\n      refreshExpiresIn\r\n    };\r\n  } catch (error) {\r\n    logger.error('Error generating token pair:', error);\r\n    throw new Error('Failed to generate token pair');\r\n  }\r\n}\r\n\r\n/**\r\n * Verify access token\r\n */\r\nexport function verifyAccessToken(token: string): JWTPayload {\r\n  try {\r\n    const payload = jwt.verify(token, config.JWT_SECRET, {\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-users'\r\n    }) as JWTPayload;\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    if (error instanceof jwt.TokenExpiredError) {\r\n      throw new Error('Access token expired');\r\n    } else if (error instanceof jwt.JsonWebTokenError) {\r\n      throw new Error('Invalid access token');\r\n    } else {\r\n      logger.error('Error verifying access token:', error);\r\n      throw new Error('Token verification failed');\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Verify refresh token\r\n */\r\nexport async function verifyRefreshToken(token: string): Promise<RefreshTokenPayload> {\r\n  try {\r\n    const payload = jwt.verify(token, config.JWT_REFRESH_SECRET, {\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-refresh'\r\n    }) as RefreshTokenPayload;\r\n\r\n    // Check if token exists in Redis\r\n    const refreshKey = redisKeys.refreshToken(payload.tokenId);\r\n    const storedUserId = await redisUtils.get(refreshKey);\r\n\r\n    if (!storedUserId || storedUserId !== payload.userId) {\r\n      throw new Error('Refresh token revoked or invalid');\r\n    }\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    if (error instanceof jwt.TokenExpiredError) {\r\n      throw new Error('Refresh token expired');\r\n    } else if (error instanceof jwt.JsonWebTokenError) {\r\n      throw new Error('Invalid refresh token');\r\n    } else {\r\n      logger.error('Error verifying refresh token:', error);\r\n      throw new Error('Refresh token verification failed');\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Revoke refresh token\r\n */\r\nexport async function revokeRefreshToken(token: string): Promise<void> {\r\n  try {\r\n    const payload = jwt.decode(token) as RefreshTokenPayload;\r\n    \r\n    if (!payload || !payload.tokenId) {\r\n      throw new Error('Invalid token format');\r\n    }\r\n\r\n    // Remove from Redis\r\n    const refreshKey = redisKeys.refreshToken(payload.tokenId);\r\n    await redisUtils.del(refreshKey);\r\n\r\n    // Remove from user's token list\r\n    const userTokensKey = redisKeys.userSockets(payload.userId);\r\n    await redisUtils.srem(userTokensKey, payload.tokenId);\r\n\r\n    logger.info(`Refresh token revoked for user: ${payload.userId}`);\r\n  } catch (error) {\r\n    logger.error('Error revoking refresh token:', error);\r\n    throw new Error('Failed to revoke refresh token');\r\n  }\r\n}\r\n\r\n/**\r\n * Revoke all refresh tokens for a user\r\n */\r\nexport async function revokeAllRefreshTokens(userId: string): Promise<void> {\r\n  try {\r\n    const userTokensKey = redisKeys.userSockets(userId);\r\n    const tokenIds = await redisUtils.smembers(userTokensKey);\r\n\r\n    // Remove all refresh tokens\r\n    for (const tokenId of tokenIds) {\r\n      const refreshKey = redisKeys.refreshToken(tokenId);\r\n      await redisUtils.del(refreshKey);\r\n    }\r\n\r\n    // Clear user's token list\r\n    await redisUtils.del(userTokensKey);\r\n\r\n    logger.info(`All refresh tokens revoked for user: ${userId}`);\r\n  } catch (error) {\r\n    logger.error('Error revoking all refresh tokens:', error);\r\n    throw new Error('Failed to revoke all refresh tokens');\r\n  }\r\n}\r\n\r\n/**\r\n * Generate password reset token\r\n */\r\nexport function generatePasswordResetToken(userId: string, email: string): string {\r\n  try {\r\n    const payload = {\r\n      userId,\r\n      email,\r\n      type: 'password-reset'\r\n    };\r\n\r\n    const token = jwt.sign(payload, config.PASSWORD_RESET_SECRET, {\r\n      expiresIn: '1h',\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-reset'\r\n    });\r\n\r\n    logger.info(`Password reset token generated for user: ${userId}`);\r\n    return token;\r\n  } catch (error) {\r\n    logger.error('Error generating password reset token:', error);\r\n    throw new Error('Failed to generate password reset token');\r\n  }\r\n}\r\n\r\n/**\r\n * Verify password reset token\r\n */\r\nexport function verifyPasswordResetToken(token: string): { userId: string; email: string } {\r\n  try {\r\n    const payload = jwt.verify(token, config.PASSWORD_RESET_SECRET, {\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-reset'\r\n    }) as any;\r\n\r\n    if (payload.type !== 'password-reset') {\r\n      throw new Error('Invalid token type');\r\n    }\r\n\r\n    return {\r\n      userId: payload.userId,\r\n      email: payload.email\r\n    };\r\n  } catch (error) {\r\n    if (error instanceof jwt.TokenExpiredError) {\r\n      throw new Error('Password reset token expired');\r\n    } else if (error instanceof jwt.JsonWebTokenError) {\r\n      throw new Error('Invalid password reset token');\r\n    } else {\r\n      logger.error('Error verifying password reset token:', error);\r\n      throw new Error('Password reset token verification failed');\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Generate email verification token\r\n */\r\nexport function generateEmailVerificationToken(userId: string, email: string): string {\r\n  try {\r\n    const payload = {\r\n      userId,\r\n      email,\r\n      type: 'email-verification'\r\n    };\r\n\r\n    const token = jwt.sign(payload, config.JWT_SECRET, {\r\n      expiresIn: '24h',\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-verify'\r\n    });\r\n\r\n    logger.info(`Email verification token generated for user: ${userId}`);\r\n    return token;\r\n  } catch (error) {\r\n    logger.error('Error generating email verification token:', error);\r\n    throw new Error('Failed to generate email verification token');\r\n  }\r\n}\r\n\r\n/**\r\n * Verify email verification token\r\n */\r\nexport function verifyEmailVerificationToken(token: string): { userId: string; email: string } {\r\n  try {\r\n    const payload = jwt.verify(token, config.JWT_SECRET, {\r\n      issuer: 'lajospaces',\r\n      audience: 'lajospaces-verify'\r\n    }) as any;\r\n\r\n    if (payload.type !== 'email-verification') {\r\n      throw new Error('Invalid token type');\r\n    }\r\n\r\n    return {\r\n      userId: payload.userId,\r\n      email: payload.email\r\n    };\r\n  } catch (error) {\r\n    if (error instanceof jwt.TokenExpiredError) {\r\n      throw new Error('Email verification token expired');\r\n    } else if (error instanceof jwt.JsonWebTokenError) {\r\n      throw new Error('Invalid email verification token');\r\n    } else {\r\n      logger.error('Error verifying email verification token:', error);\r\n      throw new Error('Email verification token verification failed');\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to convert time string to seconds\r\n */\r\nfunction getTokenExpirationTime(timeString: string): number {\r\n  const timeValue = parseInt(timeString.slice(0, -1));\r\n  const timeUnit = timeString.slice(-1);\r\n\r\n  switch (timeUnit) {\r\n    case 's': return timeValue;\r\n    case 'm': return timeValue * 60;\r\n    case 'h': return timeValue * 60 * 60;\r\n    case 'd': return timeValue * 24 * 60 * 60;\r\n    default: return 900; // 15 minutes default\r\n  }\r\n}\r\n\r\nexport default {\r\n  generateAccessToken,\r\n  generateRefreshToken,\r\n  generateTokenPair,\r\n  verifyAccessToken,\r\n  verifyRefreshToken,\r\n  revokeRefreshToken,\r\n  revokeAllRefreshTokens,\r\n  generatePasswordResetToken,\r\n  verifyPasswordResetToken,\r\n  generateEmailVerificationToken,\r\n  verifyEmailVerificationToken\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgTA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7QAE,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAgBC;AAAAH,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAG,oBAAA,GAAAA,oBAAA;AAuBC;AAAAL,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AA+BC;AAAAN,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAK,iBAAA,GAAAA,iBAAA;AAkBC;AAAAP,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAM,kBAAA,GAAAA,kBAAA;AA0BC;AAAAR,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAO,kBAAA,GAAAA,kBAAA;AAqBC;AAAAT,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAQ,sBAAA,GAAAA,sBAAA;AAmBC;AAAAV,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAS,0BAAA,GAAAA,0BAAA;AAoBC;AAAAX,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAU,wBAAA,GAAAA,wBAAA;AAyBC;AAAAZ,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAW,8BAAA,GAAAA,8BAAA;AAoBC;AAAAb,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAY,4BAAA,GAAAA,4BAAA;AAhTA,MAAAC,cAAA;AAAA;AAAA,CAAAf,aAAA,GAAAI,CAAA,QAAAY,eAAA,CAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAlB,aAAA,GAAAI,CAAA,QAAAY,eAAA,CAAAC,OAAA;AACA,MAAAE,aAAA;AAAA;AAAA,CAAAnB,aAAA,GAAAI,CAAA,QAAAa,OAAA;AACA,MAAAG,OAAA;AAAA;AAAA,CAAApB,aAAA,GAAAI,CAAA,QAAAa,OAAA;AACA,MAAAI,QAAA;AAAA;AAAA,CAAArB,aAAA,GAAAI,CAAA,QAAAa,OAAA;AA4BA;;;AAGA,SAAgBd,mBAAmBA,CAACmB,OAAwC;EAAA;EAAAtB,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EAC1E,IAAI;IACF,MAAMoB,WAAW;IAAA;IAAA,CAAAxB,aAAA,GAAAI,CAAA,QAAgB;MAC/BqB,SAAS,EAAEN,aAAA,CAAAO,MAAM,CAACC,cAAqB;MACvCC,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX;IAED,MAAMC,KAAK;IAAA;IAAA,CAAA9B,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAACC,IAAI,CAACV,OAAO,EAAEH,aAAA,CAAAO,MAAM,CAACO,UAAU,EAAET,WAAW,CAAC;IAAC;IAAAxB,aAAA,GAAAI,CAAA;IAEhEiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,oCAAoCb,OAAO,CAACc,MAAM,EAAE,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAClE,OAAO0B,KAAK;EACd,CAAC,CAAC,OAAOO,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IACtD,MAAM,IAAIkC,KAAK,CAAC,iCAAiC,CAAC;EACpD;AACF;AAEA;;;AAGA,SAAgBjC,oBAAoBA,CAAC+B,MAAc;EAAA;EAAApC,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACjD,IAAI;IACF,MAAMmC,OAAO;IAAA;IAAA,CAAAvC,aAAA,GAAAI,CAAA,QAAGc,QAAA,CAAAa,OAAM,CAACS,UAAU,EAAE;IAEnC,MAAMlB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,QAA6C;MACxDgC,MAAM;MACNG;KACD;IAED,MAAMf,WAAW;IAAA;IAAA,CAAAxB,aAAA,GAAAI,CAAA,QAAgB;MAC/BqB,SAAS,EAAEN,aAAA,CAAAO,MAAM,CAACe,sBAA6B;MAC/Cb,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX;IAED,MAAMC,KAAK;IAAA;IAAA,CAAA9B,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAACC,IAAI,CAACV,OAAO,EAAEH,aAAA,CAAAO,MAAM,CAACgB,kBAAkB,EAAElB,WAAW,CAAC;IAAC;IAAAxB,aAAA,GAAAI,CAAA;IAExEiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,qCAAqCC,MAAM,EAAE,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAC3D,OAAO0B,KAAK;EACd,CAAC,CAAC,OAAOO,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IACvD,MAAM,IAAIkC,KAAK,CAAC,kCAAkC,CAAC;EACrD;AACF;AAEA;;;AAGO,eAAehC,iBAAiBA,CAACgB,OAAwC;EAAA;EAAAtB,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EAC9E,IAAI;IACF,MAAMuC,WAAW;IAAA;IAAA,CAAA3C,aAAA,GAAAI,CAAA,QAAGD,mBAAmB,CAACmB,OAAO,CAAC;IAChD,MAAMsB,YAAY;IAAA;IAAA,CAAA5C,aAAA,GAAAI,CAAA,QAAGC,oBAAoB,CAACiB,OAAO,CAACc,MAAM,CAAC;IAEzD;IACA,MAAMS,cAAc;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAACe,MAAM,CAACF,YAAY,CAAwB;IACtE,MAAMG,UAAU;IAAA;IAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACJ,YAAY,CAACC,cAAc,CAACN,OAAO,CAAC;IACjE,MAAMU,gBAAgB;IAAA;IAAA,CAAAjD,aAAA,GAAAI,CAAA,QAAG8C,sBAAsB,CAAC/B,aAAA,CAAAO,MAAM,CAACe,sBAAsB,CAAC;IAAC;IAAAzC,aAAA,GAAAI,CAAA;IAE/E,MAAMgB,OAAA,CAAA+B,UAAU,CAACC,GAAG,CAACL,UAAU,EAAEzB,OAAO,CAACc,MAAM,EAAEa,gBAAgB,CAAC;IAElE;IACA,MAAMI,aAAa;IAAA;IAAA,CAAArD,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACM,WAAW,CAAChC,OAAO,CAACc,MAAM,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAC5D,MAAMgB,OAAA,CAAA+B,UAAU,CAACI,IAAI,CAACF,aAAa,EAAER,cAAc,CAACN,OAAO,CAAC;IAAC;IAAAvC,aAAA,GAAAI,CAAA;IAC7D,MAAMgB,OAAA,CAAA+B,UAAU,CAACK,MAAM,CAACH,aAAa,EAAEJ,gBAAgB,CAAC;IAExD,MAAMQ,eAAe;IAAA;IAAA,CAAAzD,aAAA,GAAAI,CAAA,QAAG8C,sBAAsB,CAAC/B,aAAA,CAAAO,MAAM,CAACC,cAAc,CAAC;IAAC;IAAA3B,aAAA,GAAAI,CAAA;IAEtEiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,kCAAkCb,OAAO,CAACc,MAAM,EAAE,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAEhE,OAAO;MACLuC,WAAW;MACXC,YAAY;MACZnB,SAAS,EAAEgC,eAAe;MAC1BR;KACD;EACH,CAAC,CAAC,OAAOZ,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IACpD,MAAM,IAAIkC,KAAK,CAAC,+BAA+B,CAAC;EAClD;AACF;AAEA;;;AAGA,SAAgB/B,iBAAiBA,CAACuB,KAAa;EAAA;EAAA9B,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EAC7C,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAAC2B,MAAM,CAAC5B,KAAK,EAAEX,aAAA,CAAAO,MAAM,CAACO,UAAU,EAAE;MACnDL,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAe;IAAC;IAAA7B,aAAA,GAAAI,CAAA;IAEjB,OAAOkB,OAAO;EAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACd,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC4B,iBAAiB,EAAE;MAAA;MAAA3D,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAC1C,MAAM,IAAIkC,KAAK,CAAC,sBAAsB,CAAC;IACzC,CAAC,MAAM;MAAA;MAAAtC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAA,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC8B,iBAAiB,EAAE;QAAA;QAAA7D,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACjD,MAAM,IAAIkC,KAAK,CAAC,sBAAsB,CAAC;MACzC,CAAC,MAAM;QAAA;QAAAtC,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACLiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAAC;QAAArC,aAAA,GAAAI,CAAA;QACrD,MAAM,IAAIkC,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IAAA;EACF;AACF;AAEA;;;AAGO,eAAe9B,kBAAkBA,CAACsB,KAAa;EAAA;EAAA9B,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACpD,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAAC2B,MAAM,CAAC5B,KAAK,EAAEX,aAAA,CAAAO,MAAM,CAACgB,kBAAkB,EAAE;MAC3Dd,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAwB;IAEzB;IACA,MAAMkB,UAAU;IAAA;IAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACJ,YAAY,CAACtB,OAAO,CAACiB,OAAO,CAAC;IAC1D,MAAMuB,YAAY;IAAA;IAAA,CAAA9D,aAAA,GAAAI,CAAA,QAAG,MAAMgB,OAAA,CAAA+B,UAAU,CAACY,GAAG,CAAChB,UAAU,CAAC;IAAC;IAAA/C,aAAA,GAAAI,CAAA;IAEtD;IAAI;IAAA,CAAAJ,aAAA,GAAA4D,CAAA,WAACE,YAAY;IAAA;IAAA,CAAA9D,aAAA,GAAA4D,CAAA,UAAIE,YAAY,KAAKxC,OAAO,CAACc,MAAM,GAAE;MAAA;MAAApC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MACpD,MAAM,IAAIkC,KAAK,CAAC,kCAAkC,CAAC;IACrD,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAA4D,CAAA;IAAA;IAAA5D,aAAA,GAAAI,CAAA;IAED,OAAOkB,OAAO;EAChB,CAAC,CAAC,OAAOe,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACd,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC4B,iBAAiB,EAAE;MAAA;MAAA3D,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAC1C,MAAM,IAAIkC,KAAK,CAAC,uBAAuB,CAAC;IAC1C,CAAC,MAAM;MAAA;MAAAtC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAA,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC8B,iBAAiB,EAAE;QAAA;QAAA7D,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACjD,MAAM,IAAIkC,KAAK,CAAC,uBAAuB,CAAC;MAC1C,CAAC,MAAM;QAAA;QAAAtC,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACLiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QAAC;QAAArC,aAAA,GAAAI,CAAA;QACtD,MAAM,IAAIkC,KAAK,CAAC,mCAAmC,CAAC;MACtD;IAAA;EACF;AACF;AAEA;;;AAGO,eAAe7B,kBAAkBA,CAACqB,KAAa;EAAA;EAAA9B,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACpD,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAACe,MAAM,CAAChB,KAAK,CAAwB;IAAC;IAAA9B,aAAA,GAAAI,CAAA;IAEzD;IAAI;IAAA,CAAAJ,aAAA,GAAA4D,CAAA,YAACtC,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAA4D,CAAA,WAAI,CAACtC,OAAO,CAACiB,OAAO,GAAE;MAAA;MAAAvC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAChC,MAAM,IAAIkC,KAAK,CAAC,sBAAsB,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAA4D,CAAA;IAAA;IAED;IACA,MAAMb,UAAU;IAAA;IAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACJ,YAAY,CAACtB,OAAO,CAACiB,OAAO,CAAC;IAAC;IAAAvC,aAAA,GAAAI,CAAA;IAC3D,MAAMgB,OAAA,CAAA+B,UAAU,CAACa,GAAG,CAACjB,UAAU,CAAC;IAEhC;IACA,MAAMM,aAAa;IAAA;IAAA,CAAArD,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACM,WAAW,CAAChC,OAAO,CAACc,MAAM,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAC5D,MAAMgB,OAAA,CAAA+B,UAAU,CAACc,IAAI,CAACZ,aAAa,EAAE/B,OAAO,CAACiB,OAAO,CAAC;IAAC;IAAAvC,aAAA,GAAAI,CAAA;IAEtDiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,mCAAmCb,OAAO,CAACc,MAAM,EAAE,CAAC;EAClE,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IACrD,MAAM,IAAIkC,KAAK,CAAC,gCAAgC,CAAC;EACnD;AACF;AAEA;;;AAGO,eAAe5B,sBAAsBA,CAAC0B,MAAc;EAAA;EAAApC,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACzD,IAAI;IACF,MAAMiD,aAAa;IAAA;IAAA,CAAArD,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACM,WAAW,CAAClB,MAAM,CAAC;IACnD,MAAM8B,QAAQ;IAAA;IAAA,CAAAlE,aAAA,GAAAI,CAAA,QAAG,MAAMgB,OAAA,CAAA+B,UAAU,CAACgB,QAAQ,CAACd,aAAa,CAAC;IAEzD;IAAA;IAAArD,aAAA,GAAAI,CAAA;IACA,KAAK,MAAMmC,OAAO,IAAI2B,QAAQ,EAAE;MAC9B,MAAMnB,UAAU;MAAA;MAAA,CAAA/C,aAAA,GAAAI,CAAA,QAAGgB,OAAA,CAAA4B,SAAS,CAACJ,YAAY,CAACL,OAAO,CAAC;MAAC;MAAAvC,aAAA,GAAAI,CAAA;MACnD,MAAMgB,OAAA,CAAA+B,UAAU,CAACa,GAAG,CAACjB,UAAU,CAAC;IAClC;IAEA;IAAA;IAAA/C,aAAA,GAAAI,CAAA;IACA,MAAMgB,OAAA,CAAA+B,UAAU,CAACa,GAAG,CAACX,aAAa,CAAC;IAAC;IAAArD,aAAA,GAAAI,CAAA;IAEpCiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,wCAAwCC,MAAM,EAAE,CAAC;EAC/D,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IAC1D,MAAM,IAAIkC,KAAK,CAAC,qCAAqC,CAAC;EACxD;AACF;AAEA;;;AAGA,SAAgB3B,0BAA0BA,CAACyB,MAAc,EAAEgC,KAAa;EAAA;EAAApE,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACtE,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,QAAG;MACdgC,MAAM;MACNgC,KAAK;MACLC,IAAI,EAAE;KACP;IAED,MAAMvC,KAAK;IAAA;IAAA,CAAA9B,aAAA,GAAAI,CAAA,QAAGW,cAAA,CAAAgB,OAAG,CAACC,IAAI,CAACV,OAAO,EAAEH,aAAA,CAAAO,MAAM,CAAC4C,qBAAqB,EAAE;MAC5D7C,SAAS,EAAE,IAAI;MACfG,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAC;IAAC;IAAA7B,aAAA,GAAAI,CAAA;IAEHiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,4CAA4CC,MAAM,EAAE,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAClE,OAAO0B,KAAK;EACd,CAAC,CAAC,OAAOO,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IAC9D,MAAM,IAAIkC,KAAK,CAAC,yCAAyC,CAAC;EAC5D;AACF;AAEA;;;AAGA,SAAgB1B,wBAAwBA,CAACkB,KAAa;EAAA;EAAA9B,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACpD,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,SAAGW,cAAA,CAAAgB,OAAG,CAAC2B,MAAM,CAAC5B,KAAK,EAAEX,aAAA,CAAAO,MAAM,CAAC4C,qBAAqB,EAAE;MAC9D1C,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAQ;IAAC;IAAA7B,aAAA,GAAAI,CAAA;IAEV,IAAIkB,OAAO,CAAC+C,IAAI,KAAK,gBAAgB,EAAE;MAAA;MAAArE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MACrC,MAAM,IAAIkC,KAAK,CAAC,oBAAoB,CAAC;IACvC,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAA4D,CAAA;IAAA;IAAA5D,aAAA,GAAAI,CAAA;IAED,OAAO;MACLgC,MAAM,EAAEd,OAAO,CAACc,MAAM;MACtBgC,KAAK,EAAE9C,OAAO,CAAC8C;KAChB;EACH,CAAC,CAAC,OAAO/B,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACd,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC4B,iBAAiB,EAAE;MAAA;MAAA3D,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAC1C,MAAM,IAAIkC,KAAK,CAAC,8BAA8B,CAAC;IACjD,CAAC,MAAM;MAAA;MAAAtC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAA,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC8B,iBAAiB,EAAE;QAAA;QAAA7D,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACjD,MAAM,IAAIkC,KAAK,CAAC,8BAA8B,CAAC;MACjD,CAAC,MAAM;QAAA;QAAAtC,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACLiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAAC;QAAArC,aAAA,GAAAI,CAAA;QAC7D,MAAM,IAAIkC,KAAK,CAAC,0CAA0C,CAAC;MAC7D;IAAA;EACF;AACF;AAEA;;;AAGA,SAAgBzB,8BAA8BA,CAACuB,MAAc,EAAEgC,KAAa;EAAA;EAAApE,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EAC1E,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,SAAG;MACdgC,MAAM;MACNgC,KAAK;MACLC,IAAI,EAAE;KACP;IAED,MAAMvC,KAAK;IAAA;IAAA,CAAA9B,aAAA,GAAAI,CAAA,SAAGW,cAAA,CAAAgB,OAAG,CAACC,IAAI,CAACV,OAAO,EAAEH,aAAA,CAAAO,MAAM,CAACO,UAAU,EAAE;MACjDR,SAAS,EAAE,KAAK;MAChBG,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAC;IAAC;IAAA7B,aAAA,GAAAI,CAAA;IAEHiB,QAAA,CAAAa,MAAM,CAACC,IAAI,CAAC,gDAAgDC,MAAM,EAAE,CAAC;IAAC;IAAApC,aAAA,GAAAI,CAAA;IACtE,OAAO0B,KAAK;EACd,CAAC,CAAC,OAAOO,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACdiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IAAC;IAAArC,aAAA,GAAAI,CAAA;IAClE,MAAM,IAAIkC,KAAK,CAAC,6CAA6C,CAAC;EAChE;AACF;AAEA;;;AAGA,SAAgBxB,4BAA4BA,CAACgB,KAAa;EAAA;EAAA9B,aAAA,GAAAuB,CAAA;EAAAvB,aAAA,GAAAI,CAAA;EACxD,IAAI;IACF,MAAMkB,OAAO;IAAA;IAAA,CAAAtB,aAAA,GAAAI,CAAA,SAAGW,cAAA,CAAAgB,OAAG,CAAC2B,MAAM,CAAC5B,KAAK,EAAEX,aAAA,CAAAO,MAAM,CAACO,UAAU,EAAE;MACnDL,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE;KACX,CAAQ;IAAC;IAAA7B,aAAA,GAAAI,CAAA;IAEV,IAAIkB,OAAO,CAAC+C,IAAI,KAAK,oBAAoB,EAAE;MAAA;MAAArE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MACzC,MAAM,IAAIkC,KAAK,CAAC,oBAAoB,CAAC;IACvC,CAAC;IAAA;IAAA;MAAAtC,aAAA,GAAA4D,CAAA;IAAA;IAAA5D,aAAA,GAAAI,CAAA;IAED,OAAO;MACLgC,MAAM,EAAEd,OAAO,CAACc,MAAM;MACtBgC,KAAK,EAAE9C,OAAO,CAAC8C;KAChB;EACH,CAAC,CAAC,OAAO/B,KAAK,EAAE;IAAA;IAAArC,aAAA,GAAAI,CAAA;IACd,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC4B,iBAAiB,EAAE;MAAA;MAAA3D,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAC1C,MAAM,IAAIkC,KAAK,CAAC,kCAAkC,CAAC;IACrD,CAAC,MAAM;MAAA;MAAAtC,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAA,IAAIiC,KAAK,YAAYtB,cAAA,CAAAgB,OAAG,CAAC8B,iBAAiB,EAAE;QAAA;QAAA7D,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACjD,MAAM,IAAIkC,KAAK,CAAC,kCAAkC,CAAC;MACrD,CAAC,MAAM;QAAA;QAAAtC,aAAA,GAAA4D,CAAA;QAAA5D,aAAA,GAAAI,CAAA;QACLiB,QAAA,CAAAa,MAAM,CAACG,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QAAC;QAAArC,aAAA,GAAAI,CAAA;QACjE,MAAM,IAAIkC,KAAK,CAAC,8CAA8C,CAAC;MACjE;IAAA;EACF;AACF;AAEA;;;AAGA,SAASY,sBAAsBA,CAACqB,UAAkB;EAAA;EAAAvE,aAAA,GAAAuB,CAAA;EAChD,MAAMiD,SAAS;EAAA;EAAA,CAAAxE,aAAA,GAAAI,CAAA,SAAGqE,QAAQ,CAACF,UAAU,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnD,MAAMC,QAAQ;EAAA;EAAA,CAAA3E,aAAA,GAAAI,CAAA,SAAGmE,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EAAC;EAAA1E,aAAA,GAAAI,CAAA;EAEtC,QAAQuE,QAAQ;IACd,KAAK,GAAG;MAAA;MAAA3E,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAE,OAAOoE,SAAS;IAC1B,KAAK,GAAG;MAAA;MAAAxE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAE,OAAOoE,SAAS,GAAG,EAAE;IAC/B,KAAK,GAAG;MAAA;MAAAxE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAE,OAAOoE,SAAS,GAAG,EAAE,GAAG,EAAE;IACpC,KAAK,GAAG;MAAA;MAAAxE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAE,OAAOoE,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACzC;MAAA;MAAAxE,aAAA,GAAA4D,CAAA;MAAA5D,aAAA,GAAAI,CAAA;MAAS,OAAO,GAAG;IAAE;EACvB;AACF;AAAC;AAAAJ,aAAA,GAAAI,CAAA;AAEDF,OAAA,CAAA6B,OAAA,GAAe;EACb5B,mBAAmB;EACnBE,oBAAoB;EACpBC,iBAAiB;EACjBC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,8BAA8B;EAC9BC;CACD", "ignoreList": []}