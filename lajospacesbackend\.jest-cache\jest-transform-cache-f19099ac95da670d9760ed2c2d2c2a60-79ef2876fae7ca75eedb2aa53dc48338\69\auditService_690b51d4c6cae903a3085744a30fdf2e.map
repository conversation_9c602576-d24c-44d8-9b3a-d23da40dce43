{"version": 3, "names": ["mongoose_1", "cov_3y1ppby04", "s", "__importStar", "require", "logger_1", "AuditEventType", "f", "b", "exports", "RiskLevel", "auditLogSchema", "<PERSON><PERSON><PERSON>", "eventType", "type", "String", "enum", "Object", "values", "required", "index", "riskLevel", "userId", "sessionId", "ip<PERSON><PERSON><PERSON>", "userAgent", "endpoint", "method", "statusCode", "Number", "resource", "resourceId", "oldValues", "Types", "Mixed", "newValues", "metadata", "geolocation", "country", "region", "city", "coordinates", "latitude", "longitude", "deviceInfo", "browser", "os", "device", "isMobile", "Boolean", "success", "errorMessage", "duration", "timestamp", "Date", "default", "now", "tags", "timestamps", "collection", "expireAfterSeconds", "AuditLog", "model", "AuditService", "constructor", "suspiciousActivityThresholds", "failed<PERSON>ogins", "rateLimitExceeded", "invalidInputs", "differentIPs", "logEvent", "req", "options", "auditData", "determineRiskLevel", "user", "_id", "session", "id", "getClientIP", "get", "originalUrl", "url", "res", "parseUserAgent", "undefined", "auditLog", "save", "logger", "info", "checkSuspiciousActivity", "error", "logAuth", "LOGIN_FAILED", "MEDIUM", "LOW", "loginMethod", "logDataAccess", "DATA_DELETED", "HIGH", "logSecurity", "details", "getAuditLogs", "filters", "page", "Math", "max", "limit", "min", "skip", "query", "startDate", "endDate", "$gte", "$lte", "logs", "total", "Promise", "all", "find", "sort", "lean", "countDocuments", "pages", "ceil", "getAuditStats", "timeframe", "getTime", "totalEvents", "securityEvents", "failedEvents", "eventsByType", "eventsByRisk", "topIPs", "topUsers", "aggregate", "$match", "$group", "count", "$sum", "$sort", "$limit", "$exists", "period", "start", "end", "summary", "successRate", "toFixed", "breakdown", "byType", "byRisk", "oneHour", "fifteenMinutes", "recentFailures", "logSuspiciousActivity", "RATE_LIMIT_EXCEEDED", "recentRateLimits", "distinctIPs", "distinct", "length", "reason", "originalEvent", "suspiciousLog", "SUSPICIOUS_ACTIVITY", "CRITICAL", "detectionReason", "warn", "highRiskEvents", "PERMISSION_ESCALATION", "ROLE_CHANGED", "CONFIGURATION_CHANGED", "ACCOUNT_LOCKED", "mediumRiskEvents", "ACCESS_DENIED", "PASSWORD_RESET_REQUEST", "DATA_UPDATED", "criticalRiskEvents", "SQL_INJECTION_ATTEMPT", "XSS_ATTEMPT", "BRUTE_FORCE_ATTEMPT", "includes", "ip", "connection", "remoteAddress", "socket", "detectBrowser", "detectOS", "detectDevice", "test", "auditService"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\auditService.ts"], "sourcesContent": ["import { Request } from 'express';\r\nimport mongoose, { Schema, Document } from 'mongoose';\r\nimport { logger } from '../utils/logger';\r\nimport { cacheService } from './cacheService';\r\n\r\n// Audit event types\r\nexport enum AuditEventType {\r\n  // Authentication events\r\n  LOGIN_SUCCESS = 'login_success',\r\n  LOGIN_FAILED = 'login_failed',\r\n  LOGOUT = 'logout',\r\n  PASSWORD_RESET_REQUEST = 'password_reset_request',\r\n  PASSWORD_RESET_SUCCESS = 'password_reset_success',\r\n  PASSWORD_CHANGED = 'password_changed',\r\n  EMAIL_VERIFICATION = 'email_verification',\r\n  ACCOUNT_LOCKED = 'account_locked',\r\n  ACCOUNT_UNLOCKED = 'account_unlocked',\r\n\r\n  // Authorization events\r\n  ACCESS_GRANTED = 'access_granted',\r\n  ACCESS_DENIED = 'access_denied',\r\n  PERMISSION_ESCALATION = 'permission_escalation',\r\n  ROLE_CHANGED = 'role_changed',\r\n\r\n  // Data events\r\n  DATA_CREATED = 'data_created',\r\n  DATA_UPDATED = 'data_updated',\r\n  DATA_DELETED = 'data_deleted',\r\n  DATA_VIEWED = 'data_viewed',\r\n  DATA_EXPORTED = 'data_exported',\r\n  BULK_OPERATION = 'bulk_operation',\r\n\r\n  // Security events\r\n  SUSPICIOUS_ACTIVITY = 'suspicious_activity',\r\n  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',\r\n  INVALID_INPUT = 'invalid_input',\r\n  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',\r\n  XSS_ATTEMPT = 'xss_attempt',\r\n  CSRF_ATTEMPT = 'csrf_attempt',\r\n  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',\r\n\r\n  // System events\r\n  SYSTEM_ERROR = 'system_error',\r\n  CONFIGURATION_CHANGED = 'configuration_changed',\r\n  BACKUP_CREATED = 'backup_created',\r\n  MAINTENANCE_MODE = 'maintenance_mode',\r\n\r\n  // Business events\r\n  PROPERTY_POSTED = 'property_posted',\r\n  PROPERTY_UPDATED = 'property_updated',\r\n  PROPERTY_DELETED = 'property_deleted',\r\n  MATCH_CREATED = 'match_created',\r\n  MESSAGE_SENT = 'message_sent',\r\n  PAYMENT_PROCESSED = 'payment_processed',\r\n  SUBSCRIPTION_CHANGED = 'subscription_changed'\r\n}\r\n\r\n// Risk levels\r\nexport enum RiskLevel {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical'\r\n}\r\n\r\n// Audit log interface\r\nexport interface IAuditLog extends Document {\r\n  eventType: AuditEventType;\r\n  riskLevel: RiskLevel;\r\n  userId?: string;\r\n  sessionId?: string;\r\n  ipAddress: string;\r\n  userAgent: string;\r\n  endpoint: string;\r\n  method: string;\r\n  statusCode?: number;\r\n  resource?: string;\r\n  resourceId?: string;\r\n  oldValues?: Record<string, any>;\r\n  newValues?: Record<string, any>;\r\n  metadata?: Record<string, any>;\r\n  geolocation?: {\r\n    country?: string;\r\n    region?: string;\r\n    city?: string;\r\n    coordinates?: {\r\n      latitude: number;\r\n      longitude: number;\r\n    };\r\n  };\r\n  deviceInfo?: {\r\n    browser: string;\r\n    os: string;\r\n    device: string;\r\n    isMobile: boolean;\r\n  };\r\n  success: boolean;\r\n  errorMessage?: string;\r\n  duration?: number;\r\n  timestamp: Date;\r\n  tags?: string[];\r\n}\r\n\r\n// Audit log schema\r\nconst auditLogSchema = new Schema<IAuditLog>({\r\n  eventType: {\r\n    type: String,\r\n    enum: Object.values(AuditEventType),\r\n    required: true,\r\n    index: true\r\n  },\r\n  riskLevel: {\r\n    type: String,\r\n    enum: Object.values(RiskLevel),\r\n    required: true,\r\n    index: true\r\n  },\r\n  userId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  sessionId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  ipAddress: {\r\n    type: String,\r\n    required: true,\r\n    index: true\r\n  },\r\n  userAgent: {\r\n    type: String,\r\n    required: true\r\n  },\r\n  endpoint: {\r\n    type: String,\r\n    required: true,\r\n    index: true\r\n  },\r\n  method: {\r\n    type: String,\r\n    required: true\r\n  },\r\n  statusCode: {\r\n    type: Number,\r\n    index: true\r\n  },\r\n  resource: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  resourceId: {\r\n    type: String,\r\n    index: true\r\n  },\r\n  oldValues: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  newValues: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  metadata: {\r\n    type: Schema.Types.Mixed\r\n  },\r\n  geolocation: {\r\n    country: String,\r\n    region: String,\r\n    city: String,\r\n    coordinates: {\r\n      latitude: Number,\r\n      longitude: Number\r\n    }\r\n  },\r\n  deviceInfo: {\r\n    browser: String,\r\n    os: String,\r\n    device: String,\r\n    isMobile: Boolean\r\n  },\r\n  success: {\r\n    type: Boolean,\r\n    required: true,\r\n    index: true\r\n  },\r\n  errorMessage: String,\r\n  duration: Number,\r\n  timestamp: {\r\n    type: Date,\r\n    default: Date.now,\r\n    index: true\r\n  },\r\n  tags: [{\r\n    type: String,\r\n    index: true\r\n  }]\r\n}, {\r\n  timestamps: true,\r\n  collection: 'audit_logs'\r\n});\r\n\r\n// Compound indexes for common queries\r\nauditLogSchema.index({ userId: 1, timestamp: -1 });\r\nauditLogSchema.index({ eventType: 1, timestamp: -1 });\r\nauditLogSchema.index({ riskLevel: 1, timestamp: -1 });\r\nauditLogSchema.index({ ipAddress: 1, timestamp: -1 });\r\nauditLogSchema.index({ success: 1, timestamp: -1 });\r\n\r\n// TTL index for automatic cleanup (keep logs for 1 year)\r\nauditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });\r\n\r\nexport const AuditLog = mongoose.model<IAuditLog>('AuditLog', auditLogSchema);\r\n\r\nclass AuditService {\r\n  private suspiciousActivityThresholds = {\r\n    failedLogins: 5, // per 15 minutes\r\n    rateLimitExceeded: 10, // per hour\r\n    invalidInputs: 20, // per hour\r\n    differentIPs: 3 // per hour for same user\r\n  };\r\n\r\n  /**\r\n   * Log an audit event\r\n   */\r\n  async logEvent(\r\n    eventType: AuditEventType,\r\n    req: Request,\r\n    options: {\r\n      riskLevel?: RiskLevel;\r\n      resource?: string;\r\n      resourceId?: string;\r\n      oldValues?: Record<string, any>;\r\n      newValues?: Record<string, any>;\r\n      metadata?: Record<string, any>;\r\n      success?: boolean;\r\n      errorMessage?: string;\r\n      duration?: number;\r\n      tags?: string[];\r\n    } = {}\r\n  ): Promise<void> {\r\n    try {\r\n      const auditData: Partial<IAuditLog> = {\r\n        eventType,\r\n        riskLevel: options.riskLevel || this.determineRiskLevel(eventType),\r\n        userId: req.user?._id,\r\n        sessionId: req.session?.id,\r\n        ipAddress: this.getClientIP(req),\r\n        userAgent: req.get('User-Agent') || 'unknown',\r\n        endpoint: req.originalUrl || req.url,\r\n        method: req.method,\r\n        statusCode: req.res?.statusCode,\r\n        resource: options.resource,\r\n        resourceId: options.resourceId,\r\n        oldValues: options.oldValues,\r\n        newValues: options.newValues,\r\n        metadata: options.metadata,\r\n        deviceInfo: this.parseUserAgent(req.get('User-Agent')),\r\n        success: options.success !== undefined ? options.success : true,\r\n        errorMessage: options.errorMessage,\r\n        duration: options.duration,\r\n        tags: options.tags || [],\r\n        timestamp: new Date()\r\n      };\r\n\r\n      // Create audit log entry\r\n      const auditLog = new AuditLog(auditData);\r\n      await auditLog.save();\r\n\r\n      // Log to application logger as well\r\n      logger.info('Audit event logged', {\r\n        eventType,\r\n        userId: auditData.userId,\r\n        ipAddress: auditData.ipAddress,\r\n        endpoint: auditData.endpoint,\r\n        success: auditData.success\r\n      });\r\n\r\n      // Check for suspicious activity\r\n      await this.checkSuspiciousActivity(auditData);\r\n\r\n    } catch (error) {\r\n      logger.error('Failed to log audit event:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log authentication events\r\n   */\r\n  async logAuth(\r\n    eventType: AuditEventType.LOGIN_SUCCESS | AuditEventType.LOGIN_FAILED | AuditEventType.LOGOUT,\r\n    req: Request,\r\n    userId?: string,\r\n    errorMessage?: string\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: eventType === AuditEventType.LOGIN_FAILED ? RiskLevel.MEDIUM : RiskLevel.LOW,\r\n      success: eventType !== AuditEventType.LOGIN_FAILED,\r\n      errorMessage,\r\n      metadata: {\r\n        userId: userId || req.user?._id,\r\n        loginMethod: 'email_password' // Could be extended for OAuth, etc.\r\n      },\r\n      tags: ['authentication']\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Log data access events\r\n   */\r\n  async logDataAccess(\r\n    eventType: AuditEventType.DATA_VIEWED | AuditEventType.DATA_CREATED | AuditEventType.DATA_UPDATED | AuditEventType.DATA_DELETED,\r\n    req: Request,\r\n    resource: string,\r\n    resourceId: string,\r\n    oldValues?: Record<string, any>,\r\n    newValues?: Record<string, any>\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: eventType === AuditEventType.DATA_DELETED ? RiskLevel.HIGH : RiskLevel.LOW,\r\n      resource,\r\n      resourceId,\r\n      oldValues,\r\n      newValues,\r\n      tags: ['data_access', resource]\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Log security events\r\n   */\r\n  async logSecurity(\r\n    eventType: AuditEventType,\r\n    req: Request,\r\n    details: {\r\n      riskLevel?: RiskLevel;\r\n      errorMessage?: string;\r\n      metadata?: Record<string, any>;\r\n    } = {}\r\n  ): Promise<void> {\r\n    await this.logEvent(eventType, req, {\r\n      riskLevel: details.riskLevel || RiskLevel.HIGH,\r\n      success: false,\r\n      errorMessage: details.errorMessage,\r\n      metadata: details.metadata,\r\n      tags: ['security', 'threat']\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get audit logs with filtering and pagination\r\n   */\r\n  async getAuditLogs(filters: {\r\n    userId?: string;\r\n    eventType?: AuditEventType;\r\n    riskLevel?: RiskLevel;\r\n    startDate?: Date;\r\n    endDate?: Date;\r\n    ipAddress?: string;\r\n    success?: boolean;\r\n    page?: number;\r\n    limit?: number;\r\n  } = {}): Promise<{\r\n    logs: IAuditLog[];\r\n    total: number;\r\n    page: number;\r\n    pages: number;\r\n  }> {\r\n    const page = Math.max(1, filters.page || 1);\r\n    const limit = Math.min(100, Math.max(1, filters.limit || 20));\r\n    const skip = (page - 1) * limit;\r\n\r\n    // Build query\r\n    const query: any = {};\r\n    \r\n    if (filters.userId) query.userId = filters.userId;\r\n    if (filters.eventType) query.eventType = filters.eventType;\r\n    if (filters.riskLevel) query.riskLevel = filters.riskLevel;\r\n    if (filters.ipAddress) query.ipAddress = filters.ipAddress;\r\n    if (filters.success !== undefined) query.success = filters.success;\r\n    \r\n    if (filters.startDate || filters.endDate) {\r\n      query.timestamp = {};\r\n      if (filters.startDate) query.timestamp.$gte = filters.startDate;\r\n      if (filters.endDate) query.timestamp.$lte = filters.endDate;\r\n    }\r\n\r\n    // Execute queries\r\n    const [logs, total] = await Promise.all([\r\n      AuditLog.find(query)\r\n        .sort({ timestamp: -1 })\r\n        .skip(skip)\r\n        .limit(limit)\r\n        .lean(),\r\n      AuditLog.countDocuments(query)\r\n    ]);\r\n\r\n    return {\r\n      logs,\r\n      total,\r\n      page,\r\n      pages: Math.ceil(total / limit)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get audit statistics\r\n   */\r\n  async getAuditStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<any> {\r\n    const now = new Date();\r\n    let startDate: Date;\r\n\r\n    switch (timeframe) {\r\n      case 'hour':\r\n        startDate = new Date(now.getTime() - 60 * 60 * 1000);\r\n        break;\r\n      case 'day':\r\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'week':\r\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n        break;\r\n      case 'month':\r\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\r\n        break;\r\n    }\r\n\r\n    const [\r\n      totalEvents,\r\n      securityEvents,\r\n      failedEvents,\r\n      eventsByType,\r\n      eventsByRisk,\r\n      topIPs,\r\n      topUsers\r\n    ] = await Promise.all([\r\n      AuditLog.countDocuments({ timestamp: { $gte: startDate } }),\r\n      AuditLog.countDocuments({ \r\n        timestamp: { $gte: startDate },\r\n        tags: 'security'\r\n      }),\r\n      AuditLog.countDocuments({ \r\n        timestamp: { $gte: startDate },\r\n        success: false\r\n      }),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$eventType', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$riskLevel', count: { $sum: 1 } } }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate } } },\r\n        { $group: { _id: '$ipAddress', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ]),\r\n      AuditLog.aggregate([\r\n        { $match: { timestamp: { $gte: startDate }, userId: { $exists: true } } },\r\n        { $group: { _id: '$userId', count: { $sum: 1 } } },\r\n        { $sort: { count: -1 } },\r\n        { $limit: 10 }\r\n      ])\r\n    ]);\r\n\r\n    return {\r\n      timeframe,\r\n      period: {\r\n        start: startDate,\r\n        end: now\r\n      },\r\n      summary: {\r\n        totalEvents,\r\n        securityEvents,\r\n        failedEvents,\r\n        successRate: totalEvents > 0 ? ((totalEvents - failedEvents) / totalEvents * 100).toFixed(2) : 0\r\n      },\r\n      breakdown: {\r\n        byType: eventsByType,\r\n        byRisk: eventsByRisk,\r\n        topIPs,\r\n        topUsers\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check for suspicious activity patterns\r\n   */\r\n  private async checkSuspiciousActivity(auditData: Partial<IAuditLog>): Promise<void> {\r\n    const now = new Date();\r\n    const oneHour = new Date(now.getTime() - 60 * 60 * 1000);\r\n    const fifteenMinutes = new Date(now.getTime() - 15 * 60 * 1000);\r\n\r\n    try {\r\n      // Check for failed login attempts\r\n      if (auditData.eventType === AuditEventType.LOGIN_FAILED) {\r\n        const recentFailures = await AuditLog.countDocuments({\r\n          eventType: AuditEventType.LOGIN_FAILED,\r\n          ipAddress: auditData.ipAddress,\r\n          timestamp: { $gte: fifteenMinutes }\r\n        });\r\n\r\n        if (recentFailures >= this.suspiciousActivityThresholds.failedLogins) {\r\n          await this.logSuspiciousActivity('Multiple failed login attempts', auditData);\r\n        }\r\n      }\r\n\r\n      // Check for rate limit exceeded events\r\n      if (auditData.eventType === AuditEventType.RATE_LIMIT_EXCEEDED) {\r\n        const recentRateLimits = await AuditLog.countDocuments({\r\n          eventType: AuditEventType.RATE_LIMIT_EXCEEDED,\r\n          ipAddress: auditData.ipAddress,\r\n          timestamp: { $gte: oneHour }\r\n        });\r\n\r\n        if (recentRateLimits >= this.suspiciousActivityThresholds.rateLimitExceeded) {\r\n          await this.logSuspiciousActivity('Excessive rate limiting', auditData);\r\n        }\r\n      }\r\n\r\n      // Check for multiple IPs for same user\r\n      if (auditData.userId) {\r\n        const distinctIPs = await AuditLog.distinct('ipAddress', {\r\n          userId: auditData.userId,\r\n          timestamp: { $gte: oneHour }\r\n        });\r\n\r\n        if (distinctIPs.length >= this.suspiciousActivityThresholds.differentIPs) {\r\n          await this.logSuspiciousActivity('Multiple IP addresses for user', auditData);\r\n        }\r\n      }\r\n\r\n    } catch (error) {\r\n      logger.error('Error checking suspicious activity:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log suspicious activity\r\n   */\r\n  private async logSuspiciousActivity(reason: string, originalEvent: Partial<IAuditLog>): Promise<void> {\r\n    try {\r\n      const suspiciousLog = new AuditLog({\r\n        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,\r\n        riskLevel: RiskLevel.CRITICAL,\r\n        userId: originalEvent.userId,\r\n        sessionId: originalEvent.sessionId,\r\n        ipAddress: originalEvent.ipAddress,\r\n        userAgent: originalEvent.userAgent,\r\n        endpoint: originalEvent.endpoint,\r\n        method: originalEvent.method,\r\n        success: false,\r\n        errorMessage: reason,\r\n        metadata: {\r\n          originalEvent: originalEvent.eventType,\r\n          detectionReason: reason,\r\n          timestamp: originalEvent.timestamp\r\n        },\r\n        tags: ['suspicious', 'security', 'automated_detection'],\r\n        timestamp: new Date()\r\n      });\r\n\r\n      await suspiciousLog.save();\r\n\r\n      // Also log to application logger with high priority\r\n      logger.warn('Suspicious activity detected', {\r\n        reason,\r\n        userId: originalEvent.userId,\r\n        ipAddress: originalEvent.ipAddress,\r\n        originalEvent: originalEvent.eventType\r\n      });\r\n\r\n    } catch (error) {\r\n      logger.error('Failed to log suspicious activity:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine risk level based on event type\r\n   */\r\n  private determineRiskLevel(eventType: AuditEventType): RiskLevel {\r\n    const highRiskEvents = [\r\n      AuditEventType.DATA_DELETED,\r\n      AuditEventType.PERMISSION_ESCALATION,\r\n      AuditEventType.ROLE_CHANGED,\r\n      AuditEventType.CONFIGURATION_CHANGED,\r\n      AuditEventType.ACCOUNT_LOCKED\r\n    ];\r\n\r\n    const mediumRiskEvents = [\r\n      AuditEventType.LOGIN_FAILED,\r\n      AuditEventType.ACCESS_DENIED,\r\n      AuditEventType.PASSWORD_RESET_REQUEST,\r\n      AuditEventType.DATA_UPDATED,\r\n      AuditEventType.RATE_LIMIT_EXCEEDED\r\n    ];\r\n\r\n    const criticalRiskEvents = [\r\n      AuditEventType.SUSPICIOUS_ACTIVITY,\r\n      AuditEventType.SQL_INJECTION_ATTEMPT,\r\n      AuditEventType.XSS_ATTEMPT,\r\n      AuditEventType.BRUTE_FORCE_ATTEMPT\r\n    ];\r\n\r\n    if (criticalRiskEvents.includes(eventType)) return RiskLevel.CRITICAL;\r\n    if (highRiskEvents.includes(eventType)) return RiskLevel.HIGH;\r\n    if (mediumRiskEvents.includes(eventType)) return RiskLevel.MEDIUM;\r\n    return RiskLevel.LOW;\r\n  }\r\n\r\n  /**\r\n   * Get client IP address\r\n   */\r\n  private getClientIP(req: Request): string {\r\n    return req.ip || \r\n           req.connection.remoteAddress || \r\n           req.socket.remoteAddress || \r\n           (req.connection as any)?.socket?.remoteAddress || \r\n           'unknown';\r\n  }\r\n\r\n  /**\r\n   * Parse user agent for device info\r\n   */\r\n  private parseUserAgent(userAgent?: string): any {\r\n    if (!userAgent) {\r\n      return { browser: 'unknown', os: 'unknown', device: 'unknown', isMobile: false };\r\n    }\r\n\r\n    const browser = this.detectBrowser(userAgent);\r\n    const os = this.detectOS(userAgent);\r\n    const device = this.detectDevice(userAgent);\r\n    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);\r\n\r\n    return { browser, os, device, isMobile };\r\n  }\r\n\r\n  private detectBrowser(userAgent: string): string {\r\n    if (userAgent.includes('Chrome')) return 'Chrome';\r\n    if (userAgent.includes('Firefox')) return 'Firefox';\r\n    if (userAgent.includes('Safari')) return 'Safari';\r\n    if (userAgent.includes('Edge')) return 'Edge';\r\n    if (userAgent.includes('Opera')) return 'Opera';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectOS(userAgent: string): string {\r\n    if (userAgent.includes('Windows')) return 'Windows';\r\n    if (userAgent.includes('Mac OS')) return 'macOS';\r\n    if (userAgent.includes('Linux')) return 'Linux';\r\n    if (userAgent.includes('Android')) return 'Android';\r\n    if (userAgent.includes('iOS')) return 'iOS';\r\n    return 'unknown';\r\n  }\r\n\r\n  private detectDevice(userAgent: string): string {\r\n    if (userAgent.includes('Mobile')) return 'mobile';\r\n    if (userAgent.includes('Tablet')) return 'tablet';\r\n    return 'desktop';\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const auditService = new AuditService();\r\n\r\nexport default auditService;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,MAAAA,UAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,YAAA,CAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAE,OAAA;AAGA;AACA,IAAYE,cAiDX;AAAA;AAAAL,aAAA,GAAAC,CAAA;AAjDD,WAAYI,cAAc;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAC,CAAA;EACxB;EACAI,cAAA,mCAA+B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/BI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,qBAAiB;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjBI,cAAA,qDAAiD;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjDI,cAAA,qDAAiD;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjDI,cAAA,yCAAqC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACrCI,cAAA,6CAAyC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACzCI,cAAA,qCAAiC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjCI,cAAA,yCAAqC;EAErC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACAI,cAAA,qCAAiC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjCI,cAAA,mCAA+B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/BI,cAAA,mDAA+C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/CI,cAAA,iCAA6B;EAE7B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACAI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,+BAA2B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC3BI,cAAA,mCAA+B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/BI,cAAA,qCAAiC;EAEjC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACAI,cAAA,+CAA2C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC3CI,cAAA,+CAA2C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC3CI,cAAA,mCAA+B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/BI,cAAA,mDAA+C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/CI,cAAA,+BAA2B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC3BI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,+CAA2C;EAE3C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACAI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,mDAA+C;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/CI,cAAA,qCAAiC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACjCI,cAAA,yCAAqC;EAErC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACAI,cAAA,uCAAmC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACnCI,cAAA,yCAAqC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACrCI,cAAA,yCAAqC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACrCI,cAAA,mCAA+B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC/BI,cAAA,iCAA6B;EAAA;EAAAL,aAAA,GAAAC,CAAA;EAC7BI,cAAA,2CAAuC;EAAA;EAAAL,aAAA,GAAAC,CAAA;EACvCI,cAAA,iDAA6C;AAC/C,CAAC;AAjDW;AAAA,CAAAL,aAAA,GAAAO,CAAA,WAAAF,cAAc;AAAA;AAAA,CAAAL,aAAA,GAAAO,CAAA,WAAAC,OAAA,CAAAH,cAAA,GAAdA,cAAc;AAmD1B;AACA,IAAYI,SAKX;AAAA;AAAAT,aAAA,GAAAC,CAAA;AALD,WAAYQ,SAAS;EAAA;EAAAT,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAC,CAAA;EACnBQ,SAAA,eAAW;EAAA;EAAAT,aAAA,GAAAC,CAAA;EACXQ,SAAA,qBAAiB;EAAA;EAAAT,aAAA,GAAAC,CAAA;EACjBQ,SAAA,iBAAa;EAAA;EAAAT,aAAA,GAAAC,CAAA;EACbQ,SAAA,yBAAqB;AACvB,CAAC;AALW;AAAA,CAAAT,aAAA,GAAAO,CAAA,WAAAE,SAAS;AAAA;AAAA,CAAAT,aAAA,GAAAO,CAAA,WAAAC,OAAA,CAAAC,SAAA,GAATA,SAAS;AA6CrB;AACA,MAAMC,cAAc;AAAA;AAAA,CAAAV,aAAA,GAAAC,CAAA,QAAG,IAAIF,UAAA,CAAAY,MAAM,CAAY;EAC3CC,SAAS,EAAE;IACTC,IAAI,EAAEC,MAAM;IACZC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAACZ,cAAc,CAAC;IACnCa,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACDC,SAAS,EAAE;IACTP,IAAI,EAAEC,MAAM;IACZC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAACR,SAAS,CAAC;IAC9BS,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACDE,MAAM,EAAE;IACNR,IAAI,EAAEC,MAAM;IACZK,KAAK,EAAE;GACR;EACDG,SAAS,EAAE;IACTT,IAAI,EAAEC,MAAM;IACZK,KAAK,EAAE;GACR;EACDI,SAAS,EAAE;IACTV,IAAI,EAAEC,MAAM;IACZI,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACDK,SAAS,EAAE;IACTX,IAAI,EAAEC,MAAM;IACZI,QAAQ,EAAE;GACX;EACDO,QAAQ,EAAE;IACRZ,IAAI,EAAEC,MAAM;IACZI,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACDO,MAAM,EAAE;IACNb,IAAI,EAAEC,MAAM;IACZI,QAAQ,EAAE;GACX;EACDS,UAAU,EAAE;IACVd,IAAI,EAAEe,MAAM;IACZT,KAAK,EAAE;GACR;EACDU,QAAQ,EAAE;IACRhB,IAAI,EAAEC,MAAM;IACZK,KAAK,EAAE;GACR;EACDW,UAAU,EAAE;IACVjB,IAAI,EAAEC,MAAM;IACZK,KAAK,EAAE;GACR;EACDY,SAAS,EAAE;IACTlB,IAAI,EAAEd,UAAA,CAAAY,MAAM,CAACqB,KAAK,CAACC;GACpB;EACDC,SAAS,EAAE;IACTrB,IAAI,EAAEd,UAAA,CAAAY,MAAM,CAACqB,KAAK,CAACC;GACpB;EACDE,QAAQ,EAAE;IACRtB,IAAI,EAAEd,UAAA,CAAAY,MAAM,CAACqB,KAAK,CAACC;GACpB;EACDG,WAAW,EAAE;IACXC,OAAO,EAAEvB,MAAM;IACfwB,MAAM,EAAExB,MAAM;IACdyB,IAAI,EAAEzB,MAAM;IACZ0B,WAAW,EAAE;MACXC,QAAQ,EAAEb,MAAM;MAChBc,SAAS,EAAEd;;GAEd;EACDe,UAAU,EAAE;IACVC,OAAO,EAAE9B,MAAM;IACf+B,EAAE,EAAE/B,MAAM;IACVgC,MAAM,EAAEhC,MAAM;IACdiC,QAAQ,EAAEC;GACX;EACDC,OAAO,EAAE;IACPpC,IAAI,EAAEmC,OAAO;IACb9B,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;GACR;EACD+B,YAAY,EAAEpC,MAAM;EACpBqC,QAAQ,EAAEvB,MAAM;EAChBwB,SAAS,EAAE;IACTvC,IAAI,EAAEwC,IAAI;IACVC,OAAO,EAAED,IAAI,CAACE,GAAG;IACjBpC,KAAK,EAAE;GACR;EACDqC,IAAI,EAAE,CAAC;IACL3C,IAAI,EAAEC,MAAM;IACZK,KAAK,EAAE;GACR;CACF,EAAE;EACDsC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;CACb,CAAC;AAEF;AAAA;AAAA1D,aAAA,GAAAC,CAAA;AACAS,cAAc,CAACS,KAAK,CAAC;EAAEE,MAAM,EAAE,CAAC;EAAE+B,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAApD,aAAA,GAAAC,CAAA;AACnDS,cAAc,CAACS,KAAK,CAAC;EAAEP,SAAS,EAAE,CAAC;EAAEwC,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAApD,aAAA,GAAAC,CAAA;AACtDS,cAAc,CAACS,KAAK,CAAC;EAAEC,SAAS,EAAE,CAAC;EAAEgC,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAApD,aAAA,GAAAC,CAAA;AACtDS,cAAc,CAACS,KAAK,CAAC;EAAEI,SAAS,EAAE,CAAC;EAAE6B,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAAC;AAAApD,aAAA,GAAAC,CAAA;AACtDS,cAAc,CAACS,KAAK,CAAC;EAAE8B,OAAO,EAAE,CAAC;EAAEG,SAAS,EAAE,CAAC;AAAC,CAAE,CAAC;AAEnD;AAAA;AAAApD,aAAA,GAAAC,CAAA;AACAS,cAAc,CAACS,KAAK,CAAC;EAAEiC,SAAS,EAAE;AAAC,CAAE,EAAE;EAAEO,kBAAkB,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG;AAAE,CAAE,CAAC;AAAC;AAAA3D,aAAA,GAAAC,CAAA;AAEtEO,OAAA,CAAAoD,QAAQ,GAAG7D,UAAA,CAAAuD,OAAQ,CAACO,KAAK,CAAY,UAAU,EAAEnD,cAAc,CAAC;AAE7E,MAAMoD,YAAY;EAAlBC,YAAA;IAAA;IAAA/D,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACU,KAAA+D,4BAA4B,GAAG;MACrCC,YAAY,EAAE,CAAC;MAAE;MACjBC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,aAAa,EAAE,EAAE;MAAE;MACnBC,YAAY,EAAE,CAAC,CAAC;KACjB;EA6bH;EA3bE;;;EAGA,MAAMC,QAAQA,CACZzD,SAAyB,EACzB0D,GAAY,EACZC,OAAA;EAAA;EAAA,CAAAvE,aAAA,GAAAO,CAAA,WAWI,EAAE;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAEN,IAAI;MACF,MAAMuE,SAAS;MAAA;MAAA,CAAAxE,aAAA,GAAAC,CAAA,QAAuB;QACpCW,SAAS;QACTQ,SAAS;QAAE;QAAA,CAAApB,aAAA,GAAAO,CAAA,WAAAgE,OAAO,CAACnD,SAAS;QAAA;QAAA,CAAApB,aAAA,GAAAO,CAAA,WAAI,IAAI,CAACkE,kBAAkB,CAAC7D,SAAS,CAAC;QAClES,MAAM,EAAEiD,GAAG,CAACI,IAAI,EAAEC,GAAG;QACrBrD,SAAS,EAAEgD,GAAG,CAACM,OAAO,EAAEC,EAAE;QAC1BtD,SAAS,EAAE,IAAI,CAACuD,WAAW,CAACR,GAAG,CAAC;QAChC9C,SAAS;QAAE;QAAA,CAAAxB,aAAA,GAAAO,CAAA,WAAA+D,GAAG,CAACS,GAAG,CAAC,YAAY,CAAC;QAAA;QAAA,CAAA/E,aAAA,GAAAO,CAAA,WAAI,SAAS;QAC7CkB,QAAQ;QAAE;QAAA,CAAAzB,aAAA,GAAAO,CAAA,WAAA+D,GAAG,CAACU,WAAW;QAAA;QAAA,CAAAhF,aAAA,GAAAO,CAAA,WAAI+D,GAAG,CAACW,GAAG;QACpCvD,MAAM,EAAE4C,GAAG,CAAC5C,MAAM;QAClBC,UAAU,EAAE2C,GAAG,CAACY,GAAG,EAAEvD,UAAU;QAC/BE,QAAQ,EAAE0C,OAAO,CAAC1C,QAAQ;QAC1BC,UAAU,EAAEyC,OAAO,CAACzC,UAAU;QAC9BC,SAAS,EAAEwC,OAAO,CAACxC,SAAS;QAC5BG,SAAS,EAAEqC,OAAO,CAACrC,SAAS;QAC5BC,QAAQ,EAAEoC,OAAO,CAACpC,QAAQ;QAC1BQ,UAAU,EAAE,IAAI,CAACwC,cAAc,CAACb,GAAG,CAACS,GAAG,CAAC,YAAY,CAAC,CAAC;QACtD9B,OAAO,EAAEsB,OAAO,CAACtB,OAAO,KAAKmC,SAAS;QAAA;QAAA,CAAApF,aAAA,GAAAO,CAAA,WAAGgE,OAAO,CAACtB,OAAO;QAAA;QAAA,CAAAjD,aAAA,GAAAO,CAAA,WAAG,IAAI;QAC/D2C,YAAY,EAAEqB,OAAO,CAACrB,YAAY;QAClCC,QAAQ,EAAEoB,OAAO,CAACpB,QAAQ;QAC1BK,IAAI;QAAE;QAAA,CAAAxD,aAAA,GAAAO,CAAA,WAAAgE,OAAO,CAACf,IAAI;QAAA;QAAA,CAAAxD,aAAA,GAAAO,CAAA,WAAI,EAAE;QACxB6C,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED;MACA,MAAMgC,QAAQ;MAAA;MAAA,CAAArF,aAAA,GAAAC,CAAA,QAAG,IAAIO,OAAA,CAAAoD,QAAQ,CAACY,SAAS,CAAC;MAAC;MAAAxE,aAAA,GAAAC,CAAA;MACzC,MAAMoF,QAAQ,CAACC,IAAI,EAAE;MAErB;MAAA;MAAAtF,aAAA,GAAAC,CAAA;MACAG,QAAA,CAAAmF,MAAM,CAACC,IAAI,CAAC,oBAAoB,EAAE;QAChC5E,SAAS;QACTS,MAAM,EAAEmD,SAAS,CAACnD,MAAM;QACxBE,SAAS,EAAEiD,SAAS,CAACjD,SAAS;QAC9BE,QAAQ,EAAE+C,SAAS,CAAC/C,QAAQ;QAC5BwB,OAAO,EAAEuB,SAAS,CAACvB;OACpB,CAAC;MAEF;MAAA;MAAAjD,aAAA,GAAAC,CAAA;MACA,MAAM,IAAI,CAACwF,uBAAuB,CAACjB,SAAS,CAAC;IAE/C,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA;MAAA1F,aAAA,GAAAC,CAAA;MACdG,QAAA,CAAAmF,MAAM,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACnD;EACF;EAEA;;;EAGA,MAAMC,OAAOA,CACX/E,SAA6F,EAC7F0D,GAAY,EACZjD,MAAe,EACf6B,YAAqB;IAAA;IAAAlD,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAErB,MAAM,IAAI,CAACoE,QAAQ,CAACzD,SAAS,EAAE0D,GAAG,EAAE;MAClClD,SAAS,EAAER,SAAS,KAAKP,cAAc,CAACuF,YAAY;MAAA;MAAA,CAAA5F,aAAA,GAAAO,CAAA,WAAGE,SAAS,CAACoF,MAAM;MAAA;MAAA,CAAA7F,aAAA,GAAAO,CAAA,WAAGE,SAAS,CAACqF,GAAG;MACvF7C,OAAO,EAAErC,SAAS,KAAKP,cAAc,CAACuF,YAAY;MAClD1C,YAAY;MACZf,QAAQ,EAAE;QACRd,MAAM;QAAE;QAAA,CAAArB,aAAA,GAAAO,CAAA,WAAAc,MAAM;QAAA;QAAA,CAAArB,aAAA,GAAAO,CAAA,WAAI+D,GAAG,CAACI,IAAI,EAAEC,GAAG;QAC/BoB,WAAW,EAAE,gBAAgB,CAAC;OAC/B;MACDvC,IAAI,EAAE,CAAC,gBAAgB;KACxB,CAAC;EACJ;EAEA;;;EAGA,MAAMwC,aAAaA,CACjBpF,SAA+H,EAC/H0D,GAAY,EACZzC,QAAgB,EAChBC,UAAkB,EAClBC,SAA+B,EAC/BG,SAA+B;IAAA;IAAAlC,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAE/B,MAAM,IAAI,CAACoE,QAAQ,CAACzD,SAAS,EAAE0D,GAAG,EAAE;MAClClD,SAAS,EAAER,SAAS,KAAKP,cAAc,CAAC4F,YAAY;MAAA;MAAA,CAAAjG,aAAA,GAAAO,CAAA,WAAGE,SAAS,CAACyF,IAAI;MAAA;MAAA,CAAAlG,aAAA,GAAAO,CAAA,WAAGE,SAAS,CAACqF,GAAG;MACrFjE,QAAQ;MACRC,UAAU;MACVC,SAAS;MACTG,SAAS;MACTsB,IAAI,EAAE,CAAC,aAAa,EAAE3B,QAAQ;KAC/B,CAAC;EACJ;EAEA;;;EAGA,MAAMsE,WAAWA,CACfvF,SAAyB,EACzB0D,GAAY,EACZ8B,OAAA;EAAA;EAAA,CAAApG,aAAA,GAAAO,CAAA,WAII,EAAE;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAEN,MAAM,IAAI,CAACoE,QAAQ,CAACzD,SAAS,EAAE0D,GAAG,EAAE;MAClClD,SAAS;MAAE;MAAA,CAAApB,aAAA,GAAAO,CAAA,WAAA6F,OAAO,CAAChF,SAAS;MAAA;MAAA,CAAApB,aAAA,GAAAO,CAAA,WAAIE,SAAS,CAACyF,IAAI;MAC9CjD,OAAO,EAAE,KAAK;MACdC,YAAY,EAAEkD,OAAO,CAAClD,YAAY;MAClCf,QAAQ,EAAEiE,OAAO,CAACjE,QAAQ;MAC1BqB,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ;KAC5B,CAAC;EACJ;EAEA;;;EAGA,MAAM6C,YAAYA,CAACC,OAAA;EAAA;EAAA,CAAAtG,aAAA,GAAAO,CAAA,WAUf,EAAE;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAMJ,MAAMiG,IAAI;IAAA;IAAA,CAAAvG,aAAA,GAAAC,CAAA,SAAGuG,IAAI,CAACC,GAAG,CAAC,CAAC;IAAE;IAAA,CAAAzG,aAAA,GAAAO,CAAA,WAAA+F,OAAO,CAACC,IAAI;IAAA;IAAA,CAAAvG,aAAA,GAAAO,CAAA,WAAI,CAAC,EAAC;IAC3C,MAAMmG,KAAK;IAAA;IAAA,CAAA1G,aAAA,GAAAC,CAAA,SAAGuG,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC;IAAE;IAAA,CAAAzG,aAAA,GAAAO,CAAA,WAAA+F,OAAO,CAACI,KAAK;IAAA;IAAA,CAAA1G,aAAA,GAAAO,CAAA,WAAI,EAAE,EAAC,CAAC;IAC7D,MAAMqG,IAAI;IAAA;IAAA,CAAA5G,aAAA,GAAAC,CAAA,SAAG,CAACsG,IAAI,GAAG,CAAC,IAAIG,KAAK;IAE/B;IACA,MAAMG,KAAK;IAAA;IAAA,CAAA7G,aAAA,GAAAC,CAAA,SAAQ,EAAE;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEtB,IAAIqG,OAAO,CAACjF,MAAM,EAAE;MAAA;MAAArB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA4G,KAAK,CAACxF,MAAM,GAAGiF,OAAO,CAACjF,MAAM;IAAA,CAAC;IAAA;IAAA;MAAArB,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClD,IAAIqG,OAAO,CAAC1F,SAAS,EAAE;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA4G,KAAK,CAACjG,SAAS,GAAG0F,OAAO,CAAC1F,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAZ,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC3D,IAAIqG,OAAO,CAAClF,SAAS,EAAE;MAAA;MAAApB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA4G,KAAK,CAACzF,SAAS,GAAGkF,OAAO,CAAClF,SAAS;IAAA,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC3D,IAAIqG,OAAO,CAAC/E,SAAS,EAAE;MAAA;MAAAvB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA4G,KAAK,CAACtF,SAAS,GAAG+E,OAAO,CAAC/E,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAvB,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC3D,IAAIqG,OAAO,CAACrD,OAAO,KAAKmC,SAAS,EAAE;MAAA;MAAApF,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA4G,KAAK,CAAC5D,OAAO,GAAGqD,OAAO,CAACrD,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAjD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAEnE;IAAI;IAAA,CAAAD,aAAA,GAAAO,CAAA,WAAA+F,OAAO,CAACQ,SAAS;IAAA;IAAA,CAAA9G,aAAA,GAAAO,CAAA,WAAI+F,OAAO,CAACS,OAAO,GAAE;MAAA;MAAA/G,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MACxC4G,KAAK,CAACzD,SAAS,GAAG,EAAE;MAAC;MAAApD,aAAA,GAAAC,CAAA;MACrB,IAAIqG,OAAO,CAACQ,SAAS,EAAE;QAAA;QAAA9G,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAA4G,KAAK,CAACzD,SAAS,CAAC4D,IAAI,GAAGV,OAAO,CAACQ,SAAS;MAAA,CAAC;MAAA;MAAA;QAAA9G,aAAA,GAAAO,CAAA;MAAA;MAAAP,aAAA,GAAAC,CAAA;MAChE,IAAIqG,OAAO,CAACS,OAAO,EAAE;QAAA;QAAA/G,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAA4G,KAAK,CAACzD,SAAS,CAAC6D,IAAI,GAAGX,OAAO,CAACS,OAAO;MAAA,CAAC;MAAA;MAAA;QAAA/G,aAAA,GAAAO,CAAA;MAAA;IAC9D,CAAC;IAAA;IAAA;MAAAP,aAAA,GAAAO,CAAA;IAAA;IAED;IACA,MAAM,CAAC2G,IAAI,EAAEC,KAAK,CAAC;IAAA;IAAA,CAAAnH,aAAA,GAAAC,CAAA,SAAG,MAAMmH,OAAO,CAACC,GAAG,CAAC,CACtC7G,OAAA,CAAAoD,QAAQ,CAAC0D,IAAI,CAACT,KAAK,CAAC,CACjBU,IAAI,CAAC;MAAEnE,SAAS,EAAE,CAAC;IAAC,CAAE,CAAC,CACvBwD,IAAI,CAACA,IAAI,CAAC,CACVF,KAAK,CAACA,KAAK,CAAC,CACZc,IAAI,EAAE,EACThH,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAACZ,KAAK,CAAC,CAC/B,CAAC;IAAC;IAAA7G,aAAA,GAAAC,CAAA;IAEH,OAAO;MACLiH,IAAI;MACJC,KAAK;MACLZ,IAAI;MACJmB,KAAK,EAAElB,IAAI,CAACmB,IAAI,CAACR,KAAK,GAAGT,KAAK;KAC/B;EACH;EAEA;;;EAGA,MAAMkB,aAAaA,CAACC,SAAA;EAAA;EAAA,CAAA7H,aAAA,GAAAO,CAAA,WAA+C,KAAK;IAAA;IAAAP,aAAA,GAAAM,CAAA;IACtE,MAAMiD,GAAG;IAAA;IAAA,CAAAvD,aAAA,GAAAC,CAAA,SAAG,IAAIoD,IAAI,EAAE;IACtB,IAAIyD,SAAe;IAAC;IAAA9G,aAAA,GAAAC,CAAA;IAEpB,QAAQ4H,SAAS;MACf,KAAK,MAAM;QAAA;QAAA7H,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACT6G,SAAS,GAAG,IAAIzD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAAC;QAAA9H,aAAA,GAAAC,CAAA;QACrD;MACF,KAAK,KAAK;QAAA;QAAAD,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACR6G,SAAS,GAAG,IAAIzD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAAC;QAAA9H,aAAA,GAAAC,CAAA;QAC1D;MACF,KAAK,MAAM;QAAA;QAAAD,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACT6G,SAAS,GAAG,IAAIzD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAAC;QAAA9H,aAAA,GAAAC,CAAA;QAC9D;MACF,KAAK,OAAO;QAAA;QAAAD,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACV6G,SAAS,GAAG,IAAIzD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAAC;QAAA9H,aAAA,GAAAC,CAAA;QAC/D;IACJ;IAEA,MAAM,CACJ8H,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,QAAQ,CACT;IAAA;IAAA,CAAArI,aAAA,GAAAC,CAAA,SAAG,MAAMmH,OAAO,CAACC,GAAG,CAAC,CACpB7G,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAAC;MAAErE,SAAS,EAAE;QAAE4D,IAAI,EAAEF;MAAS;IAAE,CAAE,CAAC,EAC3DtG,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAAC;MACtBrE,SAAS,EAAE;QAAE4D,IAAI,EAAEF;MAAS,CAAE;MAC9BtD,IAAI,EAAE;KACP,CAAC,EACFhD,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAAC;MACtBrE,SAAS,EAAE;QAAE4D,IAAI,EAAEF;MAAS,CAAE;MAC9B7D,OAAO,EAAE;KACV,CAAC,EACFzC,OAAA,CAAAoD,QAAQ,CAAC0E,SAAS,CAAC,CACjB;MAAEC,MAAM,EAAE;QAAEnF,SAAS,EAAE;UAAE4D,IAAI,EAAEF;QAAS;MAAE;IAAE,CAAE,EAC9C;MAAE0B,MAAM,EAAE;QAAE7D,GAAG,EAAE,YAAY;QAAE8D,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAC;MAAE;IAAE,CAAE,EACrD;MAAEC,KAAK,EAAE;QAAEF,KAAK,EAAE,CAAC;MAAC;IAAE,CAAE,EACxB;MAAEG,MAAM,EAAE;IAAE,CAAE,CACf,CAAC,EACFpI,OAAA,CAAAoD,QAAQ,CAAC0E,SAAS,CAAC,CACjB;MAAEC,MAAM,EAAE;QAAEnF,SAAS,EAAE;UAAE4D,IAAI,EAAEF;QAAS;MAAE;IAAE,CAAE,EAC9C;MAAE0B,MAAM,EAAE;QAAE7D,GAAG,EAAE,YAAY;QAAE8D,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAC;MAAE;IAAE,CAAE,CACtD,CAAC,EACFlI,OAAA,CAAAoD,QAAQ,CAAC0E,SAAS,CAAC,CACjB;MAAEC,MAAM,EAAE;QAAEnF,SAAS,EAAE;UAAE4D,IAAI,EAAEF;QAAS;MAAE;IAAE,CAAE,EAC9C;MAAE0B,MAAM,EAAE;QAAE7D,GAAG,EAAE,YAAY;QAAE8D,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAC;MAAE;IAAE,CAAE,EACrD;MAAEC,KAAK,EAAE;QAAEF,KAAK,EAAE,CAAC;MAAC;IAAE,CAAE,EACxB;MAAEG,MAAM,EAAE;IAAE,CAAE,CACf,CAAC,EACFpI,OAAA,CAAAoD,QAAQ,CAAC0E,SAAS,CAAC,CACjB;MAAEC,MAAM,EAAE;QAAEnF,SAAS,EAAE;UAAE4D,IAAI,EAAEF;QAAS,CAAE;QAAEzF,MAAM,EAAE;UAAEwH,OAAO,EAAE;QAAI;MAAE;IAAE,CAAE,EACzE;MAAEL,MAAM,EAAE;QAAE7D,GAAG,EAAE,SAAS;QAAE8D,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAC;MAAE;IAAE,CAAE,EAClD;MAAEC,KAAK,EAAE;QAAEF,KAAK,EAAE,CAAC;MAAC;IAAE,CAAE,EACxB;MAAEG,MAAM,EAAE;IAAE,CAAE,CACf,CAAC,CACH,CAAC;IAAC;IAAA5I,aAAA,GAAAC,CAAA;IAEH,OAAO;MACL4H,SAAS;MACTiB,MAAM,EAAE;QACNC,KAAK,EAAEjC,SAAS;QAChBkC,GAAG,EAAEzF;OACN;MACD0F,OAAO,EAAE;QACPlB,WAAW;QACXC,cAAc;QACdC,YAAY;QACZiB,WAAW,EAAEnB,WAAW,GAAG,CAAC;QAAA;QAAA,CAAA/H,aAAA,GAAAO,CAAA,WAAG,CAAC,CAACwH,WAAW,GAAGE,YAAY,IAAIF,WAAW,GAAG,GAAG,EAAEoB,OAAO,CAAC,CAAC,CAAC;QAAA;QAAA,CAAAnJ,aAAA,GAAAO,CAAA,WAAG,CAAC;OACjG;MACD6I,SAAS,EAAE;QACTC,MAAM,EAAEnB,YAAY;QACpBoB,MAAM,EAAEnB,YAAY;QACpBC,MAAM;QACNC;;KAEH;EACH;EAEA;;;EAGQ,MAAM5C,uBAAuBA,CAACjB,SAA6B;IAAA;IAAAxE,aAAA,GAAAM,CAAA;IACjE,MAAMiD,GAAG;IAAA;IAAA,CAAAvD,aAAA,GAAAC,CAAA,SAAG,IAAIoD,IAAI,EAAE;IACtB,MAAMkG,OAAO;IAAA;IAAA,CAAAvJ,aAAA,GAAAC,CAAA,SAAG,IAAIoD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACxD,MAAM0B,cAAc;IAAA;IAAA,CAAAxJ,aAAA,GAAAC,CAAA,SAAG,IAAIoD,IAAI,CAACE,GAAG,CAACuE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAC;IAAA9H,aAAA,GAAAC,CAAA;IAEhE,IAAI;MAAA;MAAAD,aAAA,GAAAC,CAAA;MACF;MACA,IAAIuE,SAAS,CAAC5D,SAAS,KAAKP,cAAc,CAACuF,YAAY,EAAE;QAAA;QAAA5F,aAAA,GAAAO,CAAA;QACvD,MAAMkJ,cAAc;QAAA;QAAA,CAAAzJ,aAAA,GAAAC,CAAA,SAAG,MAAMO,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAAC;UACnD7G,SAAS,EAAEP,cAAc,CAACuF,YAAY;UACtCrE,SAAS,EAAEiD,SAAS,CAACjD,SAAS;UAC9B6B,SAAS,EAAE;YAAE4D,IAAI,EAAEwC;UAAc;SAClC,CAAC;QAAC;QAAAxJ,aAAA,GAAAC,CAAA;QAEH,IAAIwJ,cAAc,IAAI,IAAI,CAACzF,4BAA4B,CAACC,YAAY,EAAE;UAAA;UAAAjE,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UACpE,MAAM,IAAI,CAACyJ,qBAAqB,CAAC,gCAAgC,EAAElF,SAAS,CAAC;QAC/E,CAAC;QAAA;QAAA;UAAAxE,aAAA,GAAAO,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAP,aAAA,GAAAO,CAAA;MAAA;MAED;MAAAP,aAAA,GAAAC,CAAA;MACA,IAAIuE,SAAS,CAAC5D,SAAS,KAAKP,cAAc,CAACsJ,mBAAmB,EAAE;QAAA;QAAA3J,aAAA,GAAAO,CAAA;QAC9D,MAAMqJ,gBAAgB;QAAA;QAAA,CAAA5J,aAAA,GAAAC,CAAA,SAAG,MAAMO,OAAA,CAAAoD,QAAQ,CAAC6D,cAAc,CAAC;UACrD7G,SAAS,EAAEP,cAAc,CAACsJ,mBAAmB;UAC7CpI,SAAS,EAAEiD,SAAS,CAACjD,SAAS;UAC9B6B,SAAS,EAAE;YAAE4D,IAAI,EAAEuC;UAAO;SAC3B,CAAC;QAAC;QAAAvJ,aAAA,GAAAC,CAAA;QAEH,IAAI2J,gBAAgB,IAAI,IAAI,CAAC5F,4BAA4B,CAACE,iBAAiB,EAAE;UAAA;UAAAlE,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UAC3E,MAAM,IAAI,CAACyJ,qBAAqB,CAAC,yBAAyB,EAAElF,SAAS,CAAC;QACxE,CAAC;QAAA;QAAA;UAAAxE,aAAA,GAAAO,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAP,aAAA,GAAAO,CAAA;MAAA;MAED;MAAAP,aAAA,GAAAC,CAAA;MACA,IAAIuE,SAAS,CAACnD,MAAM,EAAE;QAAA;QAAArB,aAAA,GAAAO,CAAA;QACpB,MAAMsJ,WAAW;QAAA;QAAA,CAAA7J,aAAA,GAAAC,CAAA,SAAG,MAAMO,OAAA,CAAAoD,QAAQ,CAACkG,QAAQ,CAAC,WAAW,EAAE;UACvDzI,MAAM,EAAEmD,SAAS,CAACnD,MAAM;UACxB+B,SAAS,EAAE;YAAE4D,IAAI,EAAEuC;UAAO;SAC3B,CAAC;QAAC;QAAAvJ,aAAA,GAAAC,CAAA;QAEH,IAAI4J,WAAW,CAACE,MAAM,IAAI,IAAI,CAAC/F,4BAA4B,CAACI,YAAY,EAAE;UAAA;UAAApE,aAAA,GAAAO,CAAA;UAAAP,aAAA,GAAAC,CAAA;UACxE,MAAM,IAAI,CAACyJ,qBAAqB,CAAC,gCAAgC,EAAElF,SAAS,CAAC;QAC/E,CAAC;QAAA;QAAA;UAAAxE,aAAA,GAAAO,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAP,aAAA,GAAAO,CAAA;MAAA;IAEH,CAAC,CAAC,OAAOmF,KAAK,EAAE;MAAA;MAAA1F,aAAA,GAAAC,CAAA;MACdG,QAAA,CAAAmF,MAAM,CAACG,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC5D;EACF;EAEA;;;EAGQ,MAAMgE,qBAAqBA,CAACM,MAAc,EAAEC,aAAiC;IAAA;IAAAjK,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACnF,IAAI;MACF,MAAMiK,aAAa;MAAA;MAAA,CAAAlK,aAAA,GAAAC,CAAA,SAAG,IAAIO,OAAA,CAAAoD,QAAQ,CAAC;QACjChD,SAAS,EAAEP,cAAc,CAAC8J,mBAAmB;QAC7C/I,SAAS,EAAEX,SAAS,CAAC2J,QAAQ;QAC7B/I,MAAM,EAAE4I,aAAa,CAAC5I,MAAM;QAC5BC,SAAS,EAAE2I,aAAa,CAAC3I,SAAS;QAClCC,SAAS,EAAE0I,aAAa,CAAC1I,SAAS;QAClCC,SAAS,EAAEyI,aAAa,CAACzI,SAAS;QAClCC,QAAQ,EAAEwI,aAAa,CAACxI,QAAQ;QAChCC,MAAM,EAAEuI,aAAa,CAACvI,MAAM;QAC5BuB,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE8G,MAAM;QACpB7H,QAAQ,EAAE;UACR8H,aAAa,EAAEA,aAAa,CAACrJ,SAAS;UACtCyJ,eAAe,EAAEL,MAAM;UACvB5G,SAAS,EAAE6G,aAAa,CAAC7G;SAC1B;QACDI,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,qBAAqB,CAAC;QACvDJ,SAAS,EAAE,IAAIC,IAAI;OACpB,CAAC;MAAC;MAAArD,aAAA,GAAAC,CAAA;MAEH,MAAMiK,aAAa,CAAC5E,IAAI,EAAE;MAE1B;MAAA;MAAAtF,aAAA,GAAAC,CAAA;MACAG,QAAA,CAAAmF,MAAM,CAAC+E,IAAI,CAAC,8BAA8B,EAAE;QAC1CN,MAAM;QACN3I,MAAM,EAAE4I,aAAa,CAAC5I,MAAM;QAC5BE,SAAS,EAAE0I,aAAa,CAAC1I,SAAS;QAClC0I,aAAa,EAAEA,aAAa,CAACrJ;OAC9B,CAAC;IAEJ,CAAC,CAAC,OAAO8E,KAAK,EAAE;MAAA;MAAA1F,aAAA,GAAAC,CAAA;MACdG,QAAA,CAAAmF,MAAM,CAACG,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC3D;EACF;EAEA;;;EAGQjB,kBAAkBA,CAAC7D,SAAyB;IAAA;IAAAZ,aAAA,GAAAM,CAAA;IAClD,MAAMiK,cAAc;IAAA;IAAA,CAAAvK,aAAA,GAAAC,CAAA,SAAG,CACrBI,cAAc,CAAC4F,YAAY,EAC3B5F,cAAc,CAACmK,qBAAqB,EACpCnK,cAAc,CAACoK,YAAY,EAC3BpK,cAAc,CAACqK,qBAAqB,EACpCrK,cAAc,CAACsK,cAAc,CAC9B;IAED,MAAMC,gBAAgB;IAAA;IAAA,CAAA5K,aAAA,GAAAC,CAAA,SAAG,CACvBI,cAAc,CAACuF,YAAY,EAC3BvF,cAAc,CAACwK,aAAa,EAC5BxK,cAAc,CAACyK,sBAAsB,EACrCzK,cAAc,CAAC0K,YAAY,EAC3B1K,cAAc,CAACsJ,mBAAmB,CACnC;IAED,MAAMqB,kBAAkB;IAAA;IAAA,CAAAhL,aAAA,GAAAC,CAAA,SAAG,CACzBI,cAAc,CAAC8J,mBAAmB,EAClC9J,cAAc,CAAC4K,qBAAqB,EACpC5K,cAAc,CAAC6K,WAAW,EAC1B7K,cAAc,CAAC8K,mBAAmB,CACnC;IAAC;IAAAnL,aAAA,GAAAC,CAAA;IAEF,IAAI+K,kBAAkB,CAACI,QAAQ,CAACxK,SAAS,CAAC,EAAE;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAOQ,SAAS,CAAC2J,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAApK,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IACtE,IAAIsK,cAAc,CAACa,QAAQ,CAACxK,SAAS,CAAC,EAAE;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAOQ,SAAS,CAACyF,IAAI;IAAA,CAAC;IAAA;IAAA;MAAAlG,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC9D,IAAI2K,gBAAgB,CAACQ,QAAQ,CAACxK,SAAS,CAAC,EAAE;MAAA;MAAAZ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAOQ,SAAS,CAACoF,MAAM;IAAA,CAAC;IAAA;IAAA;MAAA7F,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClE,OAAOQ,SAAS,CAACqF,GAAG;EACtB;EAEA;;;EAGQhB,WAAWA,CAACR,GAAY;IAAA;IAAAtE,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAC9B,OAAO,2BAAAD,aAAA,GAAAO,CAAA,WAAA+D,GAAG,CAAC+G,EAAE;IAAA;IAAA,CAAArL,aAAA,GAAAO,CAAA,WACN+D,GAAG,CAACgH,UAAU,CAACC,aAAa;IAAA;IAAA,CAAAvL,aAAA,GAAAO,CAAA,WAC5B+D,GAAG,CAACkH,MAAM,CAACD,aAAa;IAAA;IAAA,CAAAvL,aAAA,GAAAO,CAAA,WACvB+D,GAAG,CAACgH,UAAkB,EAAEE,MAAM,EAAED,aAAa;IAAA;IAAA,CAAAvL,aAAA,GAAAO,CAAA,WAC9C,SAAS;EAClB;EAEA;;;EAGQ4E,cAAcA,CAAC3D,SAAkB;IAAA;IAAAxB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACvC,IAAI,CAACuB,SAAS,EAAE;MAAA;MAAAxB,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MACd,OAAO;QAAE2C,OAAO,EAAE,SAAS;QAAEC,EAAE,EAAE,SAAS;QAAEC,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAK,CAAE;IAClF,CAAC;IAAA;IAAA;MAAA/C,aAAA,GAAAO,CAAA;IAAA;IAED,MAAMqC,OAAO;IAAA;IAAA,CAAA5C,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACwL,aAAa,CAACjK,SAAS,CAAC;IAC7C,MAAMqB,EAAE;IAAA;IAAA,CAAA7C,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACyL,QAAQ,CAAClK,SAAS,CAAC;IACnC,MAAMsB,MAAM;IAAA;IAAA,CAAA9C,aAAA,GAAAC,CAAA,SAAG,IAAI,CAAC0L,YAAY,CAACnK,SAAS,CAAC;IAC3C,MAAMuB,QAAQ;IAAA;IAAA,CAAA/C,aAAA,GAAAC,CAAA,SAAG,4BAA4B,CAAC2L,IAAI,CAACpK,SAAS,CAAC;IAAC;IAAAxB,aAAA,GAAAC,CAAA;IAE9D,OAAO;MAAE2C,OAAO;MAAEC,EAAE;MAAEC,MAAM;MAAEC;IAAQ,CAAE;EAC1C;EAEQ0I,aAAaA,CAACjK,SAAiB;IAAA;IAAAxB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACrC,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IACpD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,MAAM,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC9C,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAChD,OAAO,SAAS;EAClB;EAEQyL,QAAQA,CAAClK,SAAiB;IAAA;IAAAxB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAChC,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IACpD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IACjD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,OAAO;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAChD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,SAAS,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,SAAS;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IACpD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,KAAK,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAC5C,OAAO,SAAS;EAClB;EAEQ0L,YAAYA,CAACnK,SAAiB;IAAA;IAAAxB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACpC,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClD,IAAIuB,SAAS,CAAC4J,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA;MAAApL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;IAAA;IAAA;MAAAD,aAAA,GAAAO,CAAA;IAAA;IAAAP,aAAA,GAAAC,CAAA;IAClD,OAAO,SAAS;EAClB;;AAGF;AAAA;AAAAD,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAqL,YAAY,GAAG,IAAI/H,YAAY,EAAE;AAAC;AAAA9D,aAAA,GAAAC,CAAA;AAE/CO,OAAA,CAAA8C,OAAA,GAAe9C,OAAA,CAAAqL,YAAY", "ignoreList": []}