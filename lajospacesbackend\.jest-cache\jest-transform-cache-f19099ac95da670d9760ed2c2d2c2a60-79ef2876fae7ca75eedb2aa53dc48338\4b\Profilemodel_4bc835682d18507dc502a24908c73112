3b2b627a70df69889d59fe1e74fcbfb1
"use strict";

/* istanbul ignore next */
function cov_2k1cvj7lqd() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\Profile.model.ts";
  var hash = "6f03520e1efdb00c74eb3350d2142d9ed899c6b8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\Profile.model.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 0
        },
        end: {
          line: 35,
          column: 62
        }
      },
      "36": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 25
        }
      },
      "37": {
        start: {
          line: 37,
          column: 19
        },
        end: {
          line: 37,
          column: 52
        }
      },
      "38": {
        start: {
          line: 39,
          column: 22
        },
        end: {
          line: 318,
          column: 2
        }
      },
      "39": {
        start: {
          line: 320,
          column: 0
        },
        end: {
          line: 320,
          column: 53
        }
      },
      "40": {
        start: {
          line: 321,
          column: 0
        },
        end: {
          line: 321,
          column: 65
        }
      },
      "41": {
        start: {
          line: 322,
          column: 0
        },
        end: {
          line: 322,
          column: 65
        }
      },
      "42": {
        start: {
          line: 323,
          column: 0
        },
        end: {
          line: 323,
          column: 60
        }
      },
      "43": {
        start: {
          line: 324,
          column: 0
        },
        end: {
          line: 324,
          column: 63
        }
      },
      "44": {
        start: {
          line: 325,
          column: 0
        },
        end: {
          line: 325,
          column: 63
        }
      },
      "45": {
        start: {
          line: 326,
          column: 0
        },
        end: {
          line: 326,
          column: 67
        }
      },
      "46": {
        start: {
          line: 327,
          column: 0
        },
        end: {
          line: 327,
          column: 38
        }
      },
      "47": {
        start: {
          line: 328,
          column: 0
        },
        end: {
          line: 328,
          column: 36
        }
      },
      "48": {
        start: {
          line: 329,
          column: 0
        },
        end: {
          line: 329,
          column: 46
        }
      },
      "49": {
        start: {
          line: 330,
          column: 0
        },
        end: {
          line: 330,
          column: 47
        }
      },
      "50": {
        start: {
          line: 332,
          column: 0
        },
        end: {
          line: 334,
          column: 3
        }
      },
      "51": {
        start: {
          line: 333,
          column: 4
        },
        end: {
          line: 333,
          column: 82
        }
      },
      "52": {
        start: {
          line: 333,
          column: 39
        },
        end: {
          line: 333,
          column: 54
        }
      },
      "53": {
        start: {
          line: 336,
          column: 0
        },
        end: {
          line: 338,
          column: 3
        }
      },
      "54": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 337,
          column: 30
        }
      },
      "55": {
        start: {
          line: 340,
          column: 0
        },
        end: {
          line: 344,
          column: 3
        }
      },
      "56": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 341,
          column: 64
        }
      },
      "57": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 40
        }
      },
      "58": {
        start: {
          line: 343,
          column: 4
        },
        end: {
          line: 343,
          column: 11
        }
      },
      "59": {
        start: {
          line: 346,
          column: 0
        },
        end: {
          line: 380,
          column: 2
        }
      },
      "60": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 347,
          column: 17
        }
      },
      "61": {
        start: {
          line: 348,
          column: 21
        },
        end: {
          line: 348,
          column: 24
        }
      },
      "62": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 351,
          column: 20
        }
      },
      "63": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 351,
          column: 20
        }
      },
      "64": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 353,
          column: 20
        }
      },
      "65": {
        start: {
          line: 353,
          column: 8
        },
        end: {
          line: 353,
          column: 20
        }
      },
      "66": {
        start: {
          line: 354,
          column: 4
        },
        end: {
          line: 355,
          column: 20
        }
      },
      "67": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 355,
          column: 20
        }
      },
      "68": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 357,
          column: 20
        }
      },
      "69": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 357,
          column: 20
        }
      },
      "70": {
        start: {
          line: 359,
          column: 28
        },
        end: {
          line: 359,
          column: 57
        }
      },
      "71": {
        start: {
          line: 360,
          column: 31
        },
        end: {
          line: 360,
          column: 96
        }
      },
      "72": {
        start: {
          line: 360,
          column: 63
        },
        end: {
          line: 360,
          column: 88
        }
      },
      "73": {
        start: {
          line: 361,
          column: 4
        },
        end: {
          line: 361,
          column: 76
        }
      },
      "74": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 364,
          column: 19
        }
      },
      "75": {
        start: {
          line: 364,
          column: 8
        },
        end: {
          line: 364,
          column: 19
        }
      },
      "76": {
        start: {
          line: 365,
          column: 4
        },
        end: {
          line: 366,
          column: 19
        }
      },
      "77": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 366,
          column: 19
        }
      },
      "78": {
        start: {
          line: 367,
          column: 32
        },
        end: {
          line: 367,
          column: 94
        }
      },
      "79": {
        start: {
          line: 368,
          column: 35
        },
        end: {
          line: 368,
          column: 104
        }
      },
      "80": {
        start: {
          line: 368,
          column: 71
        },
        end: {
          line: 368,
          column: 96
        }
      },
      "81": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 369,
          column: 84
        }
      },
      "82": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 372,
          column: 19
        }
      },
      "83": {
        start: {
          line: 372,
          column: 8
        },
        end: {
          line: 372,
          column: 19
        }
      },
      "84": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 374,
          column: 19
        }
      },
      "85": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 374,
          column: 19
        }
      },
      "86": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 376,
          column: 19
        }
      },
      "87": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 376,
          column: 19
        }
      },
      "88": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 378,
          column: 19
        }
      },
      "89": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 378,
          column: 19
        }
      },
      "90": {
        start: {
          line: 379,
          column: 4
        },
        end: {
          line: 379,
          column: 37
        }
      },
      "91": {
        start: {
          line: 382,
          column: 0
        },
        end: {
          line: 391,
          column: 2
        }
      },
      "92": {
        start: {
          line: 384,
          column: 22
        },
        end: {
          line: 384,
          column: 46
        }
      },
      "93": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 390,
          column: 7
        }
      },
      "94": {
        start: {
          line: 393,
          column: 0
        },
        end: {
          line: 403,
          column: 2
        }
      },
      "95": {
        start: {
          line: 394,
          column: 23
        },
        end: {
          line: 394,
          column: 84
        }
      },
      "96": {
        start: {
          line: 394,
          column: 56
        },
        end: {
          line: 394,
          column: 83
        }
      },
      "97": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 402,
          column: 5
        }
      },
      "98": {
        start: {
          line: 396,
          column: 35
        },
        end: {
          line: 396,
          column: 68
        }
      },
      "99": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 397,
          column: 42
        }
      },
      "100": {
        start: {
          line: 399,
          column: 8
        },
        end: {
          line: 401,
          column: 9
        }
      },
      "101": {
        start: {
          line: 400,
          column: 12
        },
        end: {
          line: 400,
          column: 44
        }
      },
      "102": {
        start: {
          line: 405,
          column: 0
        },
        end: {
          line: 415,
          column: 2
        }
      },
      "103": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 409,
          column: 7
        }
      },
      "104": {
        start: {
          line: 408,
          column: 8
        },
        end: {
          line: 408,
          column: 32
        }
      },
      "105": {
        start: {
          line: 411,
          column: 18
        },
        end: {
          line: 411,
          column: 74
        }
      },
      "106": {
        start: {
          line: 411,
          column: 46
        },
        end: {
          line: 411,
          column: 73
        }
      },
      "107": {
        start: {
          line: 412,
          column: 4
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "108": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 413,
          column: 31
        }
      },
      "109": {
        start: {
          line: 417,
          column: 0
        },
        end: {
          line: 419,
          column: 2
        }
      },
      "110": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 40
        }
      },
      "111": {
        start: {
          line: 421,
          column: 0
        },
        end: {
          line: 421,
          column: 69
        }
      },
      "112": {
        start: {
          line: 422,
          column: 0
        },
        end: {
          line: 422,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 332,
            column: 42
          },
          end: {
            line: 332,
            column: 43
          }
        },
        loc: {
          start: {
            line: 332,
            column: 54
          },
          end: {
            line: 334,
            column: 1
          }
        },
        line: 332
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 333,
            column: 28
          },
          end: {
            line: 333,
            column: 29
          }
        },
        loc: {
          start: {
            line: 333,
            column: 39
          },
          end: {
            line: 333,
            column: 54
          }
        },
        line: 333
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 336,
            column: 40
          },
          end: {
            line: 336,
            column: 41
          }
        },
        loc: {
          start: {
            line: 336,
            column: 52
          },
          end: {
            line: 338,
            column: 1
          }
        },
        line: 336
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 340,
            column: 26
          },
          end: {
            line: 340,
            column: 27
          }
        },
        loc: {
          start: {
            line: 340,
            column: 42
          },
          end: {
            line: 344,
            column: 1
          }
        },
        line: 340
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 346,
            column: 46
          },
          end: {
            line: 346,
            column: 47
          }
        },
        loc: {
          start: {
            line: 346,
            column: 58
          },
          end: {
            line: 380,
            column: 1
          }
        },
        line: 346
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 360,
            column: 54
          },
          end: {
            line: 360,
            column: 55
          }
        },
        loc: {
          start: {
            line: 360,
            column: 63
          },
          end: {
            line: 360,
            column: 88
          }
        },
        line: 360
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 368,
            column: 62
          },
          end: {
            line: 368,
            column: 63
          }
        },
        loc: {
          start: {
            line: 368,
            column: 71
          },
          end: {
            line: 368,
            column: 96
          }
        },
        line: 368
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 382,
            column: 33
          },
          end: {
            line: 382,
            column: 34
          }
        },
        loc: {
          start: {
            line: 382,
            column: 58
          },
          end: {
            line: 391,
            column: 1
          }
        },
        line: 382
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 393,
            column: 36
          },
          end: {
            line: 393,
            column: 37
          }
        },
        loc: {
          start: {
            line: 393,
            column: 56
          },
          end: {
            line: 403,
            column: 1
          }
        },
        line: 393
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 394,
            column: 45
          },
          end: {
            line: 394,
            column: 46
          }
        },
        loc: {
          start: {
            line: 394,
            column: 56
          },
          end: {
            line: 394,
            column: 83
          }
        },
        line: 394
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 405,
            column: 40
          },
          end: {
            line: 405,
            column: 41
          }
        },
        loc: {
          start: {
            line: 405,
            column: 60
          },
          end: {
            line: 415,
            column: 1
          }
        },
        line: 405
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 407,
            column: 24
          },
          end: {
            line: 407,
            column: 25
          }
        },
        loc: {
          start: {
            line: 407,
            column: 35
          },
          end: {
            line: 409,
            column: 5
          }
        },
        line: 407
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 411,
            column: 35
          },
          end: {
            line: 411,
            column: 36
          }
        },
        loc: {
          start: {
            line: 411,
            column: 46
          },
          end: {
            line: 411,
            column: 73
          }
        },
        line: 411
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 417,
            column: 43
          },
          end: {
            line: 417,
            column: 44
          }
        },
        loc: {
          start: {
            line: 417,
            column: 55
          },
          end: {
            line: 419,
            column: 1
          }
        },
        line: 417
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "17": {
        loc: {
          start: {
            line: 333,
            column: 11
          },
          end: {
            line: 333,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 333,
            column: 11
          },
          end: {
            line: 333,
            column: 55
          }
        }, {
          start: {
            line: 333,
            column: 59
          },
          end: {
            line: 333,
            column: 73
          }
        }, {
          start: {
            line: 333,
            column: 77
          },
          end: {
            line: 333,
            column: 81
          }
        }],
        line: 333
      },
      "18": {
        loc: {
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 351,
            column: 20
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 351,
            column: 20
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 350
      },
      "19": {
        loc: {
          start: {
            line: 350,
            column: 8
          },
          end: {
            line: 350,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 350,
            column: 8
          },
          end: {
            line: 350,
            column: 16
          }
        }, {
          start: {
            line: 350,
            column: 20
          },
          end: {
            line: 350,
            column: 41
          }
        }],
        line: 350
      },
      "20": {
        loc: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 353,
            column: 20
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 353,
            column: 20
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "21": {
        loc: {
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 355,
            column: 20
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 355,
            column: 20
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 354
      },
      "22": {
        loc: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 357,
            column: 20
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 357,
            column: 20
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "23": {
        loc: {
          start: {
            line: 363,
            column: 4
          },
          end: {
            line: 364,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 363,
            column: 4
          },
          end: {
            line: 364,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 363
      },
      "24": {
        loc: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 366,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 366,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 365
      },
      "25": {
        loc: {
          start: {
            line: 365,
            column: 8
          },
          end: {
            line: 365,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 365,
            column: 8
          },
          end: {
            line: 365,
            column: 52
          }
        }, {
          start: {
            line: 365,
            column: 56
          },
          end: {
            line: 365,
            column: 100
          }
        }],
        line: 365
      },
      "26": {
        loc: {
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 372,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 372,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 371
      },
      "27": {
        loc: {
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 374,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 374,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 373
      },
      "28": {
        loc: {
          start: {
            line: 375,
            column: 4
          },
          end: {
            line: 376,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 375,
            column: 4
          },
          end: {
            line: 376,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 375
      },
      "29": {
        loc: {
          start: {
            line: 377,
            column: 4
          },
          end: {
            line: 378,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 377,
            column: 4
          },
          end: {
            line: 378,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 377
      },
      "30": {
        loc: {
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 377,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 377,
            column: 8
          },
          end: {
            line: 377,
            column: 31
          }
        }, {
          start: {
            line: 377,
            column: 35
          },
          end: {
            line: 377,
            column: 83
          }
        }],
        line: 377
      },
      "31": {
        loc: {
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 402,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 402,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 395
      },
      "32": {
        loc: {
          start: {
            line: 399,
            column: 8
          },
          end: {
            line: 401,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 399,
            column: 8
          },
          end: {
            line: 401,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 399
      },
      "33": {
        loc: {
          start: {
            line: 399,
            column: 12
          },
          end: {
            line: 399,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 399,
            column: 12
          },
          end: {
            line: 399,
            column: 30
          }
        }, {
          start: {
            line: 399,
            column: 34
          },
          end: {
            line: 399,
            column: 56
          }
        }],
        line: 399
      },
      "34": {
        loc: {
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 414,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\Profile.model.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA6D;AA8G7D,iBAAiB;AACjB,MAAM,aAAa,GAAG,IAAI,iBAAM,CAAW;IACzC,iBAAiB;IACjB,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;QACvC,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,IAAI;KACZ;IAED,uBAAuB;IACvB,GAAG,EAAE;QACH,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,kCAAkC,CAAC;QACpD,OAAO,EAAE,EAAE;KACZ;IAED,UAAU,EAAE;QACV,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,yCAAyC,CAAC;QAC3D,OAAO,EAAE,EAAE;KACZ;IAED,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,CAAC,GAAG,EAAE,wCAAwC,CAAC;QAC1D,OAAO,EAAE,EAAE;KACZ;IAED,SAAS,EAAE,CAAC;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,CAAC,EAAE,EAAE,2CAA2C,CAAC;SAC7D,CAAC;IAEF,SAAS;IACT,MAAM,EAAE,CAAC;YACP,GAAG,EAAE;gBACH,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,KAAK;aACf;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI,CAAC,GAAG;aAClB;SACF,CAAC;IAEF,wBAAwB;IACxB,SAAS,EAAE;QACT,aAAa,EAAE;YACb,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,CAAC;YACxE,OAAO,EAAE,eAAe;SACzB;QACD,cAAc,EAAE;YACd,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,CAAC;YAC7E,OAAO,EAAE,eAAe;SACzB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC;YACxE,OAAO,EAAE,eAAe;SACzB;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,CAAC;YACpE,OAAO,EAAE,eAAe;SACzB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,CAAC;YAC3D,OAAO,EAAE,eAAe;SACzB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,WAAW,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,eAAe,CAAC;YAC5E,OAAO,EAAE,eAAe;SACzB;KACF;IAED,oCAAoC;IACpC,kBAAkB,EAAE;QAClB,aAAa,EAAE,CAAC;gBACd,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;aAC7D,CAAC;QACF,WAAW,EAAE;YACX,GAAG,EAAE;gBACH,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,CAAC;gBACN,OAAO,EAAE,CAAC;aACX;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,CAAC;gBACN,OAAO,EAAE,KAAK;aACf;SACF;QACD,cAAc,EAAE,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI;aACX,CAAC;QACF,UAAU,EAAE;YACV,IAAI,EAAE,IAAI;SACX;QACD,aAAa,EAAE;YACb,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC;YAC7C,OAAO,EAAE,UAAU;SACpB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,KAAK,CAAC;YAC9D,OAAO,EAAE,KAAK;SACf;QACD,SAAS,EAAE,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI;aACX,CAAC;KACH;IAED,uBAAuB;IACvB,mBAAmB,EAAE;QACnB,QAAQ,EAAE;YACR,GAAG,EAAE;gBACH,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,EAAE;gBACP,GAAG,EAAE,GAAG;gBACR,OAAO,EAAE,EAAE;aACZ;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,MAAM;gBACZ,GAAG,EAAE,EAAE;gBACP,GAAG,EAAE,GAAG;gBACR,OAAO,EAAE,EAAE;aACZ;SACF;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,kBAAkB,CAAC;YAClE,OAAO,EAAE,KAAK;SACf;QACD,oBAAoB,EAAE,CAAC;gBACrB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,IAAI;aACX,CAAC;QACF,sBAAsB,EAAE;YACtB,gBAAgB,EAAE;gBAChB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,CAAC;gBACxE,OAAO,EAAE,eAAe;aACzB;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,CAAC;gBAC7E,OAAO,EAAE,eAAe;aACzB;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC;gBACxE,OAAO,EAAE,eAAe;aACzB;YACD,sBAAsB,EAAE;gBACtB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,CAAC;gBACpE,OAAO,EAAE,eAAe;aACzB;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,CAAC;gBAC3D,OAAO,EAAE,eAAe;aACzB;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,WAAW,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,eAAe,CAAC;gBAC5E,OAAO,EAAE,eAAe;aACzB;SACF;KACF;IAED,sBAAsB;IACtB,SAAS,EAAE,CAAC;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,CAAC,EAAE,EAAE,sCAAsC,CAAC;SACxD,CAAC;IAEF,OAAO,EAAE,CAAC;YACR,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,CAAC,EAAE,EAAE,mCAAmC,CAAC;SACrD,CAAC;IAEF,yBAAyB;IACzB,WAAW,EAAE;QACX,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC,kBAAkB,EAAE,4BAA4B,CAAC;SAC1D;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;SACX;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;SACX;QACD,OAAO,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC,iBAAiB,EAAE,0BAA0B,CAAC;SACvD;KACF;IAED,uBAAuB;IACvB,aAAa,EAAE;QACb,mBAAmB,EAAE;YACnB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;QACD,gBAAgB,EAAE;YAChB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;QACD,kBAAkB,EAAE;YAClB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;QACD,kBAAkB,EAAE;YAClB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;KACF;IAED,mBAAmB;IACnB,OAAO,EAAE;QACP,YAAY,EAAE;YACZ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;SACd;QACD,OAAO,EAAE;YACP,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;SACd;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;SACd;QACD,cAAc,EAAE;YACd,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,IAAI;SACd;QACD,eAAe,EAAE;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;QACD,0BAA0B,EAAE;YAC1B,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAK;SACf;KACF;IAED,wBAAwB;IACxB,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC;KACP;IAED,iBAAiB,EAAE;QACjB,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;KAClB;IAED,iBAAiB,EAAE;QACjB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;CACF,EAAE;IACD,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC1B,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AAEH,0BAA0B;AAC1B,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACrD,aAAa,CAAC,KAAK,CAAC,EAAE,oCAAoC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjE,aAAa,CAAC,KAAK,CAAC,EAAE,oCAAoC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjE,aAAa,CAAC,KAAK,CAAC,EAAE,+BAA+B,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,aAAa,CAAC,KAAK,CAAC,EAAE,kCAAkC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,aAAa,CAAC,KAAK,CAAC,EAAE,kCAAkC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,aAAa,CAAC,KAAK,CAAC,EAAE,sCAAsC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnE,aAAa,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,aAAa,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;AACpC,aAAa,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,aAAa,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAE/C,4BAA4B;AAC5B,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC;IACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AACrF,CAAC,CAAC,CAAC;AAEH,0BAA0B;AAC1B,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;IACtC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAC5B,CAAC,CAAC,CAAC;AAEH,qDAAqD;AACrD,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,IAAI,EAAE,CAAC;IAC5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAEH,oDAAoD;AACpD,aAAa,CAAC,OAAO,CAAC,qBAAqB,GAAG;IAC5C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,MAAM,QAAQ,GAAG,GAAG,CAAC;IAErB,gCAAgC;IAChC,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE;QAAE,KAAK,IAAI,EAAE,CAAC;IACnD,IAAI,IAAI,CAAC,UAAU;QAAE,KAAK,IAAI,EAAE,CAAC;IACjC,IAAI,IAAI,CAAC,SAAS;QAAE,KAAK,IAAI,EAAE,CAAC;IAChC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,KAAK,IAAI,EAAE,CAAC;IAExC,oCAAoC;IACpC,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtD,MAAM,kBAAkB,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,eAAe,CAAC,CAAC,MAAM,CAAC;IAC7F,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IAExE,mCAAmC;IACnC,IAAI,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,KAAK,KAAK;QAAE,KAAK,IAAI,CAAC,CAAC;IACpE,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,KAAK,EAAE,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,KAAK,EAAE;QAAE,KAAK,IAAI,CAAC,CAAC;IAC7G,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;IAC3F,MAAM,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,eAAe,CAAC,CAAC,MAAM,CAAC;IACrG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IAEhF,iCAAiC;IACjC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;QAAE,KAAK,IAAI,CAAC,CAAC;IAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;QAAE,KAAK,IAAI,CAAC,CAAC;IACxC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;QAAE,KAAK,IAAI,CAAC,CAAC;IAC1C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;QAAE,KAAK,IAAI,CAAC,CAAC;IAE5F,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF,+BAA+B;AAC/B,aAAa,CAAC,OAAO,CAAC,QAAQ,GAAG,UAAS,GAAW,EAAE,QAAgB;IACrE,8CAA8C;IAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAE3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACf,GAAG;QACH,QAAQ;QACR,SAAS;QACT,UAAU,EAAE,IAAI,IAAI,EAAE;KACvB,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,kCAAkC;AAClC,aAAa,CAAC,OAAO,CAAC,WAAW,GAAG,UAAS,QAAgB;IAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAEtF,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;QACtB,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAElC,yFAAyF;QACzF,IAAI,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;QAClC,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF,uCAAuC;AACvC,aAAa,CAAC,OAAO,CAAC,eAAe,GAAG,UAAS,QAAgB;IAC/D,wCAAwC;IACxC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;QACjC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAC5E,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,CAAC;AACH,CAAC,CAAC;AAEF,0CAA0C;AAC1C,aAAa,CAAC,OAAO,CAAC,kBAAkB,GAAG;IACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;AACtC,CAAC,CAAC;AAEF,mBAAmB;AACN,QAAA,OAAO,GAAG,kBAAQ,CAAC,KAAK,CAAW,SAAS,EAAE,aAAa,CAAC,CAAC;AAC1E,kBAAe,eAAO,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\models\\Profile.model.ts"],
      sourcesContent: ["import mongoose, { Document, Schema, Types } from 'mongoose';\r\n\r\n// Profile interface\r\nexport interface IProfile extends Document {\r\n  // User Reference\r\n  userId: Types.ObjectId;\r\n\r\n  // Personal Information\r\n  bio: string;\r\n  occupation: string;\r\n  education: string;\r\n  languages: string[];\r\n  \r\n  // Photos\r\n  photos: {\r\n    url: string;\r\n    publicId: string;\r\n    isPrimary: boolean;\r\n    uploadedAt: Date;\r\n  }[];\r\n\r\n  // Lifestyle Preferences\r\n  lifestyle: {\r\n    smokingPolicy: 'no-smoking' | 'smoking-allowed' | 'outdoor-only' | 'no-preference';\r\n    drinkingPolicy: 'no-drinking' | 'social-drinking' | 'regular-drinking' | 'no-preference';\r\n    petPolicy: 'no-pets' | 'cats-only' | 'dogs-only' | 'all-pets' | 'no-preference';\r\n    cleanlinessLevel: 'very-clean' | 'moderately-clean' | 'relaxed' | 'no-preference';\r\n    noiseLevel: 'very-quiet' | 'moderate' | 'lively' | 'no-preference';\r\n    guestPolicy: 'no-guests' | 'occasional-guests' | 'frequent-guests' | 'no-preference';\r\n  };\r\n\r\n  // Housing Preferences (for seekers)\r\n  housingPreferences?: {\r\n    propertyTypes: ('apartment' | 'house' | 'condo' | 'townhouse' | 'studio')[];\r\n    budgetRange: {\r\n      min: number;\r\n      max: number;\r\n    };\r\n    preferredAreas: string[];\r\n    moveInDate: Date;\r\n    leaseDuration: 'short-term' | 'long-term' | 'flexible';\r\n    roomType: 'private-room' | 'shared-room' | 'master-bedroom' | 'any';\r\n    amenities: string[];\r\n  };\r\n\r\n  // Roommate Preferences\r\n  roommatePreferences: {\r\n    ageRange: {\r\n      min: number;\r\n      max: number;\r\n    };\r\n    genderPreference: 'male' | 'female' | 'any' | 'same-gender' | 'different-gender';\r\n    occupationPreference: string[];\r\n    lifestyleCompatibility: {\r\n      smokingTolerance: 'no-smoking' | 'smoking-allowed' | 'outdoor-only' | 'no-preference';\r\n      drinkingTolerance: 'no-drinking' | 'social-drinking' | 'regular-drinking' | 'no-preference';\r\n      petTolerance: 'no-pets' | 'cats-only' | 'dogs-only' | 'all-pets' | 'no-preference';\r\n      cleanlinessExpectation: 'very-clean' | 'moderately-clean' | 'relaxed' | 'no-preference';\r\n      noiseExpectation: 'very-quiet' | 'moderate' | 'lively' | 'no-preference';\r\n      guestTolerance: 'no-guests' | 'occasional-guests' | 'frequent-guests' | 'no-preference';\r\n    };\r\n  };\r\n\r\n  // Interests & Hobbies\r\n  interests: string[];\r\n  hobbies: string[];\r\n\r\n  // Social Media & Contact\r\n  socialMedia?: {\r\n    instagram?: string;\r\n    facebook?: string;\r\n    linkedin?: string;\r\n    twitter?: string;\r\n  };\r\n\r\n  // Verification & Trust\r\n  verifications: {\r\n    isBackgroundChecked: boolean;\r\n    isIncomeVerified: boolean;\r\n    isIdentityVerified: boolean;\r\n    isReferenceChecked: boolean;\r\n  };\r\n\r\n  // Privacy Settings\r\n  privacy: {\r\n    showFullName: boolean;\r\n    showAge: boolean;\r\n    showLocation: boolean;\r\n    showOccupation: boolean;\r\n    showSocialMedia: boolean;\r\n    allowMessagesFromUnmatched: boolean;\r\n  };\r\n\r\n  // Activity & Engagement\r\n  profileViews: number;\r\n  lastProfileUpdate: Date;\r\n  isProfileComplete: boolean;\r\n\r\n  // Timestamps\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n\r\n  // Methods\r\n  calculateCompleteness(): number;\r\n  addPhoto(url: string, publicId: string): void;\r\n  removePhoto(publicId: string): void;\r\n  setPrimaryPhoto(publicId: string): void;\r\n  updateLastActivity(): void;\r\n}\r\n\r\n// Profile Schema\r\nconst ProfileSchema = new Schema<IProfile>({\r\n  // User Reference\r\n  userId: {\r\n    type: Schema.Types.ObjectId,\r\n    ref: 'User',\r\n    required: [true, 'User ID is required'],\r\n    unique: true,\r\n    index: true\r\n  },\r\n\r\n  // Personal Information\r\n  bio: {\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [500, 'Bio cannot exceed 500 characters'],\r\n    default: ''\r\n  },\r\n\r\n  occupation: {\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [100, 'Occupation cannot exceed 100 characters'],\r\n    default: ''\r\n  },\r\n\r\n  education: {\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [100, 'Education cannot exceed 100 characters'],\r\n    default: ''\r\n  },\r\n\r\n  languages: [{\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [50, 'Language name cannot exceed 50 characters']\r\n  }],\r\n\r\n  // Photos\r\n  photos: [{\r\n    url: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    publicId: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    isPrimary: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    uploadedAt: {\r\n      type: Date,\r\n      default: Date.now\r\n    }\r\n  }],\r\n\r\n  // Lifestyle Preferences\r\n  lifestyle: {\r\n    smokingPolicy: {\r\n      type: String,\r\n      enum: ['no-smoking', 'smoking-allowed', 'outdoor-only', 'no-preference'],\r\n      default: 'no-preference'\r\n    },\r\n    drinkingPolicy: {\r\n      type: String,\r\n      enum: ['no-drinking', 'social-drinking', 'regular-drinking', 'no-preference'],\r\n      default: 'no-preference'\r\n    },\r\n    petPolicy: {\r\n      type: String,\r\n      enum: ['no-pets', 'cats-only', 'dogs-only', 'all-pets', 'no-preference'],\r\n      default: 'no-preference'\r\n    },\r\n    cleanlinessLevel: {\r\n      type: String,\r\n      enum: ['very-clean', 'moderately-clean', 'relaxed', 'no-preference'],\r\n      default: 'no-preference'\r\n    },\r\n    noiseLevel: {\r\n      type: String,\r\n      enum: ['very-quiet', 'moderate', 'lively', 'no-preference'],\r\n      default: 'no-preference'\r\n    },\r\n    guestPolicy: {\r\n      type: String,\r\n      enum: ['no-guests', 'occasional-guests', 'frequent-guests', 'no-preference'],\r\n      default: 'no-preference'\r\n    }\r\n  },\r\n\r\n  // Housing Preferences (for seekers)\r\n  housingPreferences: {\r\n    propertyTypes: [{\r\n      type: String,\r\n      enum: ['apartment', 'house', 'condo', 'townhouse', 'studio']\r\n    }],\r\n    budgetRange: {\r\n      min: {\r\n        type: Number,\r\n        min: 0,\r\n        default: 0\r\n      },\r\n      max: {\r\n        type: Number,\r\n        min: 0,\r\n        default: 10000\r\n      }\r\n    },\r\n    preferredAreas: [{\r\n      type: String,\r\n      trim: true\r\n    }],\r\n    moveInDate: {\r\n      type: Date\r\n    },\r\n    leaseDuration: {\r\n      type: String,\r\n      enum: ['short-term', 'long-term', 'flexible'],\r\n      default: 'flexible'\r\n    },\r\n    roomType: {\r\n      type: String,\r\n      enum: ['private-room', 'shared-room', 'master-bedroom', 'any'],\r\n      default: 'any'\r\n    },\r\n    amenities: [{\r\n      type: String,\r\n      trim: true\r\n    }]\r\n  },\r\n\r\n  // Roommate Preferences\r\n  roommatePreferences: {\r\n    ageRange: {\r\n      min: {\r\n        type: Number,\r\n        min: 18,\r\n        max: 100,\r\n        default: 18\r\n      },\r\n      max: {\r\n        type: Number,\r\n        min: 18,\r\n        max: 100,\r\n        default: 65\r\n      }\r\n    },\r\n    genderPreference: {\r\n      type: String,\r\n      enum: ['male', 'female', 'any', 'same-gender', 'different-gender'],\r\n      default: 'any'\r\n    },\r\n    occupationPreference: [{\r\n      type: String,\r\n      trim: true\r\n    }],\r\n    lifestyleCompatibility: {\r\n      smokingTolerance: {\r\n        type: String,\r\n        enum: ['no-smoking', 'smoking-allowed', 'outdoor-only', 'no-preference'],\r\n        default: 'no-preference'\r\n      },\r\n      drinkingTolerance: {\r\n        type: String,\r\n        enum: ['no-drinking', 'social-drinking', 'regular-drinking', 'no-preference'],\r\n        default: 'no-preference'\r\n      },\r\n      petTolerance: {\r\n        type: String,\r\n        enum: ['no-pets', 'cats-only', 'dogs-only', 'all-pets', 'no-preference'],\r\n        default: 'no-preference'\r\n      },\r\n      cleanlinessExpectation: {\r\n        type: String,\r\n        enum: ['very-clean', 'moderately-clean', 'relaxed', 'no-preference'],\r\n        default: 'no-preference'\r\n      },\r\n      noiseExpectation: {\r\n        type: String,\r\n        enum: ['very-quiet', 'moderate', 'lively', 'no-preference'],\r\n        default: 'no-preference'\r\n      },\r\n      guestTolerance: {\r\n        type: String,\r\n        enum: ['no-guests', 'occasional-guests', 'frequent-guests', 'no-preference'],\r\n        default: 'no-preference'\r\n      }\r\n    }\r\n  },\r\n\r\n  // Interests & Hobbies\r\n  interests: [{\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [50, 'Interest cannot exceed 50 characters']\r\n  }],\r\n\r\n  hobbies: [{\r\n    type: String,\r\n    trim: true,\r\n    maxlength: [50, 'Hobby cannot exceed 50 characters']\r\n  }],\r\n\r\n  // Social Media & Contact\r\n  socialMedia: {\r\n    instagram: {\r\n      type: String,\r\n      trim: true,\r\n      match: [/^[a-zA-Z0-9._]+$/, 'Invalid Instagram username']\r\n    },\r\n    facebook: {\r\n      type: String,\r\n      trim: true\r\n    },\r\n    linkedin: {\r\n      type: String,\r\n      trim: true\r\n    },\r\n    twitter: {\r\n      type: String,\r\n      trim: true,\r\n      match: [/^[a-zA-Z0-9_]+$/, 'Invalid Twitter username']\r\n    }\r\n  },\r\n\r\n  // Verification & Trust\r\n  verifications: {\r\n    isBackgroundChecked: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isIncomeVerified: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isIdentityVerified: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isReferenceChecked: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n\r\n  // Privacy Settings\r\n  privacy: {\r\n    showFullName: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showAge: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showLocation: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showOccupation: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showSocialMedia: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    allowMessagesFromUnmatched: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n\r\n  // Activity & Engagement\r\n  profileViews: {\r\n    type: Number,\r\n    default: 0,\r\n    min: 0\r\n  },\r\n\r\n  lastProfileUpdate: {\r\n    type: Date,\r\n    default: Date.now\r\n  },\r\n\r\n  isProfileComplete: {\r\n    type: Boolean,\r\n    default: false\r\n  }\r\n}, {\r\n  timestamps: true,\r\n  toJSON: { virtuals: true },\r\n  toObject: { virtuals: true }\r\n});\r\n\r\n// Indexes for performance\r\nProfileSchema.index({ userId: 1 }, { unique: true });\r\nProfileSchema.index({ 'housingPreferences.budgetRange.min': 1 });\r\nProfileSchema.index({ 'housingPreferences.budgetRange.max': 1 });\r\nProfileSchema.index({ 'housingPreferences.moveInDate': 1 });\r\nProfileSchema.index({ 'roommatePreferences.ageRange.min': 1 });\r\nProfileSchema.index({ 'roommatePreferences.ageRange.max': 1 });\r\nProfileSchema.index({ 'roommatePreferences.genderPreference': 1 });\r\nProfileSchema.index({ interests: 1 });\r\nProfileSchema.index({ hobbies: 1 });\r\nProfileSchema.index({ isProfileComplete: 1 });\r\nProfileSchema.index({ lastProfileUpdate: -1 });\r\n\r\n// Virtual for primary photo\r\nProfileSchema.virtual('primaryPhoto').get(function() {\r\n  return this.photos.find((photo: any) => photo.isPrimary) || this.photos[0] || null;\r\n});\r\n\r\n// Virtual for photo count\r\nProfileSchema.virtual('photoCount').get(function() {\r\n  return this.photos.length;\r\n});\r\n\r\n// Pre-save middleware to update profile completeness\r\nProfileSchema.pre('save', function(next) {\r\n  this.isProfileComplete = this.calculateCompleteness() >= 80;\r\n  this.lastProfileUpdate = new Date();\r\n  next();\r\n});\r\n\r\n// Instance method to calculate profile completeness\r\nProfileSchema.methods.calculateCompleteness = function(): number {\r\n  let score = 0;\r\n  const maxScore = 100;\r\n\r\n  // Basic information (40 points)\r\n  if (this.bio && this.bio.length >= 50) score += 10;\r\n  if (this.occupation) score += 10;\r\n  if (this.education) score += 10;\r\n  if (this.photos.length > 0) score += 10;\r\n\r\n  // Lifestyle preferences (20 points)\r\n  const lifestyleFields = Object.values(this.lifestyle);\r\n  const completedLifestyle = lifestyleFields.filter(field => field !== 'no-preference').length;\r\n  score += Math.round((completedLifestyle / lifestyleFields.length) * 20);\r\n\r\n  // Roommate preferences (20 points)\r\n  if (this.roommatePreferences.genderPreference !== 'any') score += 5;\r\n  if (this.roommatePreferences.ageRange.min !== 18 || this.roommatePreferences.ageRange.max !== 65) score += 5;\r\n  const compatibilityFields = Object.values(this.roommatePreferences.lifestyleCompatibility);\r\n  const completedCompatibility = compatibilityFields.filter(field => field !== 'no-preference').length;\r\n  score += Math.round((completedCompatibility / compatibilityFields.length) * 10);\r\n\r\n  // Additional details (20 points)\r\n  if (this.interests.length > 0) score += 5;\r\n  if (this.hobbies.length > 0) score += 5;\r\n  if (this.languages.length > 0) score += 5;\r\n  if (this.housingPreferences && this.housingPreferences.propertyTypes.length > 0) score += 5;\r\n\r\n  return Math.min(score, maxScore);\r\n};\r\n\r\n// Instance method to add photo\r\nProfileSchema.methods.addPhoto = function(url: string, publicId: string): void {\r\n  // If this is the first photo, make it primary\r\n  const isPrimary = this.photos.length === 0;\r\n\r\n  this.photos.push({\r\n    url,\r\n    publicId,\r\n    isPrimary,\r\n    uploadedAt: new Date()\r\n  });\r\n};\r\n\r\n// Instance method to remove photo\r\nProfileSchema.methods.removePhoto = function(publicId: string): void {\r\n  const photoIndex = this.photos.findIndex((photo: any) => photo.publicId === publicId);\r\n\r\n  if (photoIndex !== -1) {\r\n    const wasRemovingPrimary = this.photos[photoIndex].isPrimary;\r\n    this.photos.splice(photoIndex, 1);\r\n\r\n    // If we removed the primary photo and there are other photos, make the first one primary\r\n    if (wasRemovingPrimary && this.photos.length > 0) {\r\n      this.photos[0].isPrimary = true;\r\n    }\r\n  }\r\n};\r\n\r\n// Instance method to set primary photo\r\nProfileSchema.methods.setPrimaryPhoto = function(publicId: string): void {\r\n  // Remove primary status from all photos\r\n  this.photos.forEach((photo: any) => {\r\n    photo.isPrimary = false;\r\n  });\r\n\r\n  // Set the specified photo as primary\r\n  const photo = this.photos.find((photo: any) => photo.publicId === publicId);\r\n  if (photo) {\r\n    photo.isPrimary = true;\r\n  }\r\n};\r\n\r\n// Instance method to update last activity\r\nProfileSchema.methods.updateLastActivity = function(): void {\r\n  this.lastProfileUpdate = new Date();\r\n};\r\n\r\n// Export the model\r\nexport const Profile = mongoose.model<IProfile>('Profile', ProfileSchema);\r\nexport default Profile;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6f03520e1efdb00c74eb3350d2142d9ed899c6b8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2k1cvj7lqd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2k1cvj7lqd();
var __createBinding =
/* istanbul ignore next */
(cov_2k1cvj7lqd().s[0]++,
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[0]++;
  cov_2k1cvj7lqd().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[2][0]++;
    cov_2k1cvj7lqd().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[5][1]++,
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[3][0]++;
    cov_2k1cvj7lqd().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_2k1cvj7lqd().f[1]++;
        cov_2k1cvj7lqd().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[3][1]++;
  }
  cov_2k1cvj7lqd().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[2]++;
  cov_2k1cvj7lqd().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[7][0]++;
    cov_2k1cvj7lqd().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[7][1]++;
  }
  cov_2k1cvj7lqd().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_2k1cvj7lqd().s[11]++,
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[3]++;
  cov_2k1cvj7lqd().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[4]++;
  cov_2k1cvj7lqd().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_2k1cvj7lqd().s[14]++,
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_2k1cvj7lqd().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[5]++;
  cov_2k1cvj7lqd().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[6]++;
    cov_2k1cvj7lqd().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_2k1cvj7lqd().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_2k1cvj7lqd().s[17]++, []);
      /* istanbul ignore next */
      cov_2k1cvj7lqd().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_2k1cvj7lqd().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_2k1cvj7lqd().b[12][0]++;
          cov_2k1cvj7lqd().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_2k1cvj7lqd().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2k1cvj7lqd().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_2k1cvj7lqd().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[8]++;
    cov_2k1cvj7lqd().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_2k1cvj7lqd().b[13][0]++;
      cov_2k1cvj7lqd().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_2k1cvj7lqd().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().s[26]++, {});
    /* istanbul ignore next */
    cov_2k1cvj7lqd().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_2k1cvj7lqd().b[15][0]++;
      cov_2k1cvj7lqd().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_2k1cvj7lqd().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_2k1cvj7lqd().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_2k1cvj7lqd().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_2k1cvj7lqd().b[16][0]++;
          cov_2k1cvj7lqd().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_2k1cvj7lqd().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_2k1cvj7lqd().b[15][1]++;
    }
    cov_2k1cvj7lqd().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_2k1cvj7lqd().s[34]++;
    return result;
  };
}()));
/* istanbul ignore next */
cov_2k1cvj7lqd().s[35]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[36]++;
exports.Profile = void 0;
const mongoose_1 =
/* istanbul ignore next */
(cov_2k1cvj7lqd().s[37]++, __importStar(require("mongoose")));
// Profile Schema
const ProfileSchema =
/* istanbul ignore next */
(cov_2k1cvj7lqd().s[38]++, new mongoose_1.Schema({
  // User Reference
  userId: {
    type: mongoose_1.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    unique: true,
    index: true
  },
  // Personal Information
  bio: {
    type: String,
    trim: true,
    maxlength: [500, 'Bio cannot exceed 500 characters'],
    default: ''
  },
  occupation: {
    type: String,
    trim: true,
    maxlength: [100, 'Occupation cannot exceed 100 characters'],
    default: ''
  },
  education: {
    type: String,
    trim: true,
    maxlength: [100, 'Education cannot exceed 100 characters'],
    default: ''
  },
  languages: [{
    type: String,
    trim: true,
    maxlength: [50, 'Language name cannot exceed 50 characters']
  }],
  // Photos
  photos: [{
    url: {
      type: String,
      required: true
    },
    publicId: {
      type: String,
      required: true
    },
    isPrimary: {
      type: Boolean,
      default: false
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Lifestyle Preferences
  lifestyle: {
    smokingPolicy: {
      type: String,
      enum: ['no-smoking', 'smoking-allowed', 'outdoor-only', 'no-preference'],
      default: 'no-preference'
    },
    drinkingPolicy: {
      type: String,
      enum: ['no-drinking', 'social-drinking', 'regular-drinking', 'no-preference'],
      default: 'no-preference'
    },
    petPolicy: {
      type: String,
      enum: ['no-pets', 'cats-only', 'dogs-only', 'all-pets', 'no-preference'],
      default: 'no-preference'
    },
    cleanlinessLevel: {
      type: String,
      enum: ['very-clean', 'moderately-clean', 'relaxed', 'no-preference'],
      default: 'no-preference'
    },
    noiseLevel: {
      type: String,
      enum: ['very-quiet', 'moderate', 'lively', 'no-preference'],
      default: 'no-preference'
    },
    guestPolicy: {
      type: String,
      enum: ['no-guests', 'occasional-guests', 'frequent-guests', 'no-preference'],
      default: 'no-preference'
    }
  },
  // Housing Preferences (for seekers)
  housingPreferences: {
    propertyTypes: [{
      type: String,
      enum: ['apartment', 'house', 'condo', 'townhouse', 'studio']
    }],
    budgetRange: {
      min: {
        type: Number,
        min: 0,
        default: 0
      },
      max: {
        type: Number,
        min: 0,
        default: 10000
      }
    },
    preferredAreas: [{
      type: String,
      trim: true
    }],
    moveInDate: {
      type: Date
    },
    leaseDuration: {
      type: String,
      enum: ['short-term', 'long-term', 'flexible'],
      default: 'flexible'
    },
    roomType: {
      type: String,
      enum: ['private-room', 'shared-room', 'master-bedroom', 'any'],
      default: 'any'
    },
    amenities: [{
      type: String,
      trim: true
    }]
  },
  // Roommate Preferences
  roommatePreferences: {
    ageRange: {
      min: {
        type: Number,
        min: 18,
        max: 100,
        default: 18
      },
      max: {
        type: Number,
        min: 18,
        max: 100,
        default: 65
      }
    },
    genderPreference: {
      type: String,
      enum: ['male', 'female', 'any', 'same-gender', 'different-gender'],
      default: 'any'
    },
    occupationPreference: [{
      type: String,
      trim: true
    }],
    lifestyleCompatibility: {
      smokingTolerance: {
        type: String,
        enum: ['no-smoking', 'smoking-allowed', 'outdoor-only', 'no-preference'],
        default: 'no-preference'
      },
      drinkingTolerance: {
        type: String,
        enum: ['no-drinking', 'social-drinking', 'regular-drinking', 'no-preference'],
        default: 'no-preference'
      },
      petTolerance: {
        type: String,
        enum: ['no-pets', 'cats-only', 'dogs-only', 'all-pets', 'no-preference'],
        default: 'no-preference'
      },
      cleanlinessExpectation: {
        type: String,
        enum: ['very-clean', 'moderately-clean', 'relaxed', 'no-preference'],
        default: 'no-preference'
      },
      noiseExpectation: {
        type: String,
        enum: ['very-quiet', 'moderate', 'lively', 'no-preference'],
        default: 'no-preference'
      },
      guestTolerance: {
        type: String,
        enum: ['no-guests', 'occasional-guests', 'frequent-guests', 'no-preference'],
        default: 'no-preference'
      }
    }
  },
  // Interests & Hobbies
  interests: [{
    type: String,
    trim: true,
    maxlength: [50, 'Interest cannot exceed 50 characters']
  }],
  hobbies: [{
    type: String,
    trim: true,
    maxlength: [50, 'Hobby cannot exceed 50 characters']
  }],
  // Social Media & Contact
  socialMedia: {
    instagram: {
      type: String,
      trim: true,
      match: [/^[a-zA-Z0-9._]+$/, 'Invalid Instagram username']
    },
    facebook: {
      type: String,
      trim: true
    },
    linkedin: {
      type: String,
      trim: true
    },
    twitter: {
      type: String,
      trim: true,
      match: [/^[a-zA-Z0-9_]+$/, 'Invalid Twitter username']
    }
  },
  // Verification & Trust
  verifications: {
    isBackgroundChecked: {
      type: Boolean,
      default: false
    },
    isIncomeVerified: {
      type: Boolean,
      default: false
    },
    isIdentityVerified: {
      type: Boolean,
      default: false
    },
    isReferenceChecked: {
      type: Boolean,
      default: false
    }
  },
  // Privacy Settings
  privacy: {
    showFullName: {
      type: Boolean,
      default: true
    },
    showAge: {
      type: Boolean,
      default: true
    },
    showLocation: {
      type: Boolean,
      default: true
    },
    showOccupation: {
      type: Boolean,
      default: true
    },
    showSocialMedia: {
      type: Boolean,
      default: false
    },
    allowMessagesFromUnmatched: {
      type: Boolean,
      default: false
    }
  },
  // Activity & Engagement
  profileViews: {
    type: Number,
    default: 0,
    min: 0
  },
  lastProfileUpdate: {
    type: Date,
    default: Date.now
  },
  isProfileComplete: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true
  },
  toObject: {
    virtuals: true
  }
}));
// Indexes for performance
/* istanbul ignore next */
cov_2k1cvj7lqd().s[39]++;
ProfileSchema.index({
  userId: 1
}, {
  unique: true
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[40]++;
ProfileSchema.index({
  'housingPreferences.budgetRange.min': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[41]++;
ProfileSchema.index({
  'housingPreferences.budgetRange.max': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[42]++;
ProfileSchema.index({
  'housingPreferences.moveInDate': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[43]++;
ProfileSchema.index({
  'roommatePreferences.ageRange.min': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[44]++;
ProfileSchema.index({
  'roommatePreferences.ageRange.max': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[45]++;
ProfileSchema.index({
  'roommatePreferences.genderPreference': 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[46]++;
ProfileSchema.index({
  interests: 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[47]++;
ProfileSchema.index({
  hobbies: 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[48]++;
ProfileSchema.index({
  isProfileComplete: 1
});
/* istanbul ignore next */
cov_2k1cvj7lqd().s[49]++;
ProfileSchema.index({
  lastProfileUpdate: -1
});
// Virtual for primary photo
/* istanbul ignore next */
cov_2k1cvj7lqd().s[50]++;
ProfileSchema.virtual('primaryPhoto').get(function () {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[9]++;
  cov_2k1cvj7lqd().s[51]++;
  return /* istanbul ignore next */(cov_2k1cvj7lqd().b[17][0]++, this.photos.find(photo => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[10]++;
    cov_2k1cvj7lqd().s[52]++;
    return photo.isPrimary;
  })) ||
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[17][1]++, this.photos[0]) ||
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[17][2]++, null);
});
// Virtual for photo count
/* istanbul ignore next */
cov_2k1cvj7lqd().s[53]++;
ProfileSchema.virtual('photoCount').get(function () {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[11]++;
  cov_2k1cvj7lqd().s[54]++;
  return this.photos.length;
});
// Pre-save middleware to update profile completeness
/* istanbul ignore next */
cov_2k1cvj7lqd().s[55]++;
ProfileSchema.pre('save', function (next) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[12]++;
  cov_2k1cvj7lqd().s[56]++;
  this.isProfileComplete = this.calculateCompleteness() >= 80;
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[57]++;
  this.lastProfileUpdate = new Date();
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[58]++;
  next();
});
// Instance method to calculate profile completeness
/* istanbul ignore next */
cov_2k1cvj7lqd().s[59]++;
ProfileSchema.methods.calculateCompleteness = function () {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[13]++;
  let score =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[60]++, 0);
  const maxScore =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[61]++, 100);
  // Basic information (40 points)
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[62]++;
  if (
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[19][0]++, this.bio) &&
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[19][1]++, this.bio.length >= 50)) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[18][0]++;
    cov_2k1cvj7lqd().s[63]++;
    score += 10;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[18][1]++;
  }
  cov_2k1cvj7lqd().s[64]++;
  if (this.occupation) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[20][0]++;
    cov_2k1cvj7lqd().s[65]++;
    score += 10;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[20][1]++;
  }
  cov_2k1cvj7lqd().s[66]++;
  if (this.education) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[21][0]++;
    cov_2k1cvj7lqd().s[67]++;
    score += 10;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[21][1]++;
  }
  cov_2k1cvj7lqd().s[68]++;
  if (this.photos.length > 0) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[22][0]++;
    cov_2k1cvj7lqd().s[69]++;
    score += 10;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[22][1]++;
  }
  // Lifestyle preferences (20 points)
  const lifestyleFields =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[70]++, Object.values(this.lifestyle));
  const completedLifestyle =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[71]++, lifestyleFields.filter(field => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[14]++;
    cov_2k1cvj7lqd().s[72]++;
    return field !== 'no-preference';
  }).length);
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[73]++;
  score += Math.round(completedLifestyle / lifestyleFields.length * 20);
  // Roommate preferences (20 points)
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[74]++;
  if (this.roommatePreferences.genderPreference !== 'any') {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[23][0]++;
    cov_2k1cvj7lqd().s[75]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[23][1]++;
  }
  cov_2k1cvj7lqd().s[76]++;
  if (
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[25][0]++, this.roommatePreferences.ageRange.min !== 18) ||
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[25][1]++, this.roommatePreferences.ageRange.max !== 65)) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[24][0]++;
    cov_2k1cvj7lqd().s[77]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[24][1]++;
  }
  const compatibilityFields =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[78]++, Object.values(this.roommatePreferences.lifestyleCompatibility));
  const completedCompatibility =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[79]++, compatibilityFields.filter(field => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[15]++;
    cov_2k1cvj7lqd().s[80]++;
    return field !== 'no-preference';
  }).length);
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[81]++;
  score += Math.round(completedCompatibility / compatibilityFields.length * 10);
  // Additional details (20 points)
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[82]++;
  if (this.interests.length > 0) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[26][0]++;
    cov_2k1cvj7lqd().s[83]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[26][1]++;
  }
  cov_2k1cvj7lqd().s[84]++;
  if (this.hobbies.length > 0) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[27][0]++;
    cov_2k1cvj7lqd().s[85]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[27][1]++;
  }
  cov_2k1cvj7lqd().s[86]++;
  if (this.languages.length > 0) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[28][0]++;
    cov_2k1cvj7lqd().s[87]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[28][1]++;
  }
  cov_2k1cvj7lqd().s[88]++;
  if (
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[30][0]++, this.housingPreferences) &&
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().b[30][1]++, this.housingPreferences.propertyTypes.length > 0)) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[29][0]++;
    cov_2k1cvj7lqd().s[89]++;
    score += 5;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[29][1]++;
  }
  cov_2k1cvj7lqd().s[90]++;
  return Math.min(score, maxScore);
};
// Instance method to add photo
/* istanbul ignore next */
cov_2k1cvj7lqd().s[91]++;
ProfileSchema.methods.addPhoto = function (url, publicId) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[16]++;
  // If this is the first photo, make it primary
  const isPrimary =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[92]++, this.photos.length === 0);
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[93]++;
  this.photos.push({
    url,
    publicId,
    isPrimary,
    uploadedAt: new Date()
  });
};
// Instance method to remove photo
/* istanbul ignore next */
cov_2k1cvj7lqd().s[94]++;
ProfileSchema.methods.removePhoto = function (publicId) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[17]++;
  const photoIndex =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[95]++, this.photos.findIndex(photo => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[18]++;
    cov_2k1cvj7lqd().s[96]++;
    return photo.publicId === publicId;
  }));
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[97]++;
  if (photoIndex !== -1) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[31][0]++;
    const wasRemovingPrimary =
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().s[98]++, this.photos[photoIndex].isPrimary);
    /* istanbul ignore next */
    cov_2k1cvj7lqd().s[99]++;
    this.photos.splice(photoIndex, 1);
    // If we removed the primary photo and there are other photos, make the first one primary
    /* istanbul ignore next */
    cov_2k1cvj7lqd().s[100]++;
    if (
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[33][0]++, wasRemovingPrimary) &&
    /* istanbul ignore next */
    (cov_2k1cvj7lqd().b[33][1]++, this.photos.length > 0)) {
      /* istanbul ignore next */
      cov_2k1cvj7lqd().b[32][0]++;
      cov_2k1cvj7lqd().s[101]++;
      this.photos[0].isPrimary = true;
    } else
    /* istanbul ignore next */
    {
      cov_2k1cvj7lqd().b[32][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[31][1]++;
  }
};
// Instance method to set primary photo
/* istanbul ignore next */
cov_2k1cvj7lqd().s[102]++;
ProfileSchema.methods.setPrimaryPhoto = function (publicId) {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[19]++;
  cov_2k1cvj7lqd().s[103]++;
  // Remove primary status from all photos
  this.photos.forEach(photo => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[20]++;
    cov_2k1cvj7lqd().s[104]++;
    photo.isPrimary = false;
  });
  // Set the specified photo as primary
  const photo =
  /* istanbul ignore next */
  (cov_2k1cvj7lqd().s[105]++, this.photos.find(photo => {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().f[21]++;
    cov_2k1cvj7lqd().s[106]++;
    return photo.publicId === publicId;
  }));
  /* istanbul ignore next */
  cov_2k1cvj7lqd().s[107]++;
  if (photo) {
    /* istanbul ignore next */
    cov_2k1cvj7lqd().b[34][0]++;
    cov_2k1cvj7lqd().s[108]++;
    photo.isPrimary = true;
  } else
  /* istanbul ignore next */
  {
    cov_2k1cvj7lqd().b[34][1]++;
  }
};
// Instance method to update last activity
/* istanbul ignore next */
cov_2k1cvj7lqd().s[109]++;
ProfileSchema.methods.updateLastActivity = function () {
  /* istanbul ignore next */
  cov_2k1cvj7lqd().f[22]++;
  cov_2k1cvj7lqd().s[110]++;
  this.lastProfileUpdate = new Date();
};
// Export the model
/* istanbul ignore next */
cov_2k1cvj7lqd().s[111]++;
exports.Profile = mongoose_1.default.model('Profile', ProfileSchema);
/* istanbul ignore next */
cov_2k1cvj7lqd().s[112]++;
exports.default = exports.Profile;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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