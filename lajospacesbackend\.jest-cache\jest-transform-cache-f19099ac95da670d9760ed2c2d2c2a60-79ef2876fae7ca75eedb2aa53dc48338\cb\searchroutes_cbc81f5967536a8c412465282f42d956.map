{"version": 3, "names": ["cov_6hixwnljr", "actualCoverage", "s", "express_1", "require", "auth_1", "search_controller_1", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "endpoints", "searchUsers", "getSuggestions", "getPopularFilters", "optionalAuth", "getSearchSuggestions", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\search.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { optionalAuth } from '../middleware/auth';\r\nimport {\r\n  searchUsers,\r\n  getSearchSuggestions,\r\n  getPopularFilters\r\n} from '../controllers/search.controller';\r\n\r\nconst router = Router();\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ \r\n    message: 'Search routes working', \r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      searchUsers: 'GET /users',\r\n      getSuggestions: 'GET /suggestions',\r\n      getPopularFilters: 'GET /popular-filters'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes (optional authentication for personalization)\r\nrouter.get('/users', optionalAuth, searchUsers);\r\n\r\nrouter.get('/suggestions', getSearchSuggestions);\r\n\r\nrouter.get('/popular-filters', getPopularFilters);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBK;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;AAnBL,MAAAC,SAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,mBAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAMA,MAAMG,MAAM;AAAA;AAAA,CAAAP,aAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAK,MAAM,GAAE;AAEvB;AAAA;AAAAR,aAAA,GAAAE,CAAA;AACAK,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAX,aAAA,GAAAY,CAAA;EAAAZ,aAAA,GAAAE,CAAA;EAClCS,GAAG,CAACE,IAAI,CAAC;IACPC,OAAO,EAAE,uBAAuB;IAChCC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACnCC,SAAS,EAAE;MACTC,WAAW,EAAE,YAAY;MACzBC,cAAc,EAAE,kBAAkB;MAClCC,iBAAiB,EAAE;;GAEtB,CAAC;AACJ,CAAC,CAAC;AAEF;AAAA;AAAArB,aAAA,GAAAE,CAAA;AACAK,MAAM,CAACE,GAAG,CAAC,QAAQ,EAAEJ,MAAA,CAAAiB,YAAY,EAAEhB,mBAAA,CAAAa,WAAW,CAAC;AAAC;AAAAnB,aAAA,GAAAE,CAAA;AAEhDK,MAAM,CAACE,GAAG,CAAC,cAAc,EAAEH,mBAAA,CAAAiB,oBAAoB,CAAC;AAAC;AAAAvB,aAAA,GAAAE,CAAA;AAEjDK,MAAM,CAACE,GAAG,CAAC,kBAAkB,EAAEH,mBAAA,CAAAe,iBAAiB,CAAC;AAAC;AAAArB,aAAA,GAAAE,CAAA;AAElDsB,OAAA,CAAAC,OAAA,GAAelB,MAAM", "ignoreList": []}