{"file": "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\unit\\models\\Property.test.ts", "mappings": ";;AACA,2DAAmE;AACnE,mDAAgD;AAChD,mEAAyG;AACzG,uDAAmD;AAEnD,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,QAAa,CAAC;IAElB,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,IAAA,gCAAiB,GAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,IAAA,kCAAmB,GAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,IAAA,4BAAa,GAAE,CAAC;QAEtB,4CAA4C;QAC5C,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;QAC9C,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClE,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE5C,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,CAAC,qBAAqB,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,kBAAkB;gBAC/B,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,iBAAiB;oBACtB,OAAO,EAAE,iBAAiB;iBAC3B;gBACD,KAAK,EAAE,QAAQ,CAAC,GAAG;aACpB,CAAC;YACF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE5C,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/C,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChE,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,iBAAiB;oBACtB,OAAO,EAAE,iBAAiB;oBAC1B,WAAW,EAAE;wBACX,QAAQ,EAAE,MAAM;wBAChB,SAAS,EAAE,MAAM;qBAClB;iBACF;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE5C,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YACpC,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YACxF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC;YAC9F,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;YACnC,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YACvF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;YAC5F,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,UAAU,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAElF,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5E,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;gBAC5C,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAE5C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YACpC,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YACxF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YACpF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;YACpC,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YACxF,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAChG,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC;YAC3F,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChE,QAAQ,EAAE;oBACR,GAAG,EAAE,iBAAiB;oBACtB,OAAO,EAAE,iBAAiB;iBAC3B;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChE,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,iBAAiB;iBAC3B;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChE,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,iBAAiB;iBACvB;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChE,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,iBAAiB;oBACtB,OAAO,EAAE,iBAAiB;oBAC1B,WAAW,EAAE;wBACX,QAAQ,EAAE,GAAG,EAAE,mBAAmB;wBAClC,SAAS,EAAE,MAAM;qBAClB;iBACF;aACF,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,QAAmB,CAAC;QAExB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,YAAY,GAAG,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAClE,QAAQ,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC;YACpC,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;YAEhC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;YAC9B,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC3B,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG;gBAC9B,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,MAAM;aAClB,CAAC;YAEF,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,2BAA2B;YACjF,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;YACxB,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;YAE1B,MAAM,cAAc,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,yBAAyB;YACzB,MAAM,UAAU,GAAG;gBACjB,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC3C,MAAM,EAAE,WAAW;oBACnB,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAE,OAAO,EAAE,aAAa,EAAE;iBAC7E,CAAC;gBACF,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC3C,MAAM,EAAE,QAAQ;oBAChB,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE;iBACpE,CAAC;gBACF,sBAAS,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE;oBAC3C,MAAM,EAAE,WAAW;oBACnB,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE;iBAClE,CAAC;aACH,CAAC;YAEF,MAAM,mBAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,mBAAmB,GAAG,MAAM,mBAAQ,CAAC,aAAa,EAAE,CAAC;YAC3D,MAAM,CAAC,mBAAmB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,UAAU,GAAG,MAAM,mBAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,eAAe,GAAG,MAAM,mBAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACjC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,eAAe,GAAG,MAAM,mBAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC/D,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACjC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,iBAAiB,GAAG,MAAM,mBAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACzE,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,gBAAgB,CAAC;gBACpD,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;YACjF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACvC,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,OAAO,GAAG,MAAM,mBAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7E,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\unit\\models\\Property.test.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\nimport { Property, IProperty } from '../../../src/models/Property';\r\nimport { User } from '../../../src/models/User';\r\nimport { setupTestDatabase, clearTestData, cleanupTestDatabase } from '../../../src/config/testDatabase';\r\nimport { testUtils } from '../../setup/jest.setup';\r\n\r\ndescribe('Property Model', () => {\r\n  let testUser: any;\r\n\r\n  beforeAll(async () => {\r\n    await setupTestDatabase();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await cleanupTestDatabase();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await clearTestData();\r\n    \r\n    // Create a test user for property ownership\r\n    const userData = testUtils.generateTestUser();\r\n    testUser = await User.create(userData);\r\n  });\r\n\r\n  describe('Property Creation', () => {\r\n    it('should create a valid property', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id);\r\n      const property = new Property(propertyData);\r\n      const savedProperty = await property.save();\r\n\r\n      expect(savedProperty._id).toBeDefined();\r\n      expect(savedProperty.title).toBe(propertyData.title);\r\n      expect(savedProperty.description).toBe(propertyData.description);\r\n      expect(savedProperty.type).toBe(propertyData.type);\r\n      expect(savedProperty.price).toBe(propertyData.price);\r\n      expect(savedProperty.currency).toBe(propertyData.currency);\r\n      expect(savedProperty.owner.toString()).toBe(testUser._id.toString());\r\n      expect(savedProperty.status).toBe('available');\r\n      expect(savedProperty).toHaveValidTimestamps();\r\n    });\r\n\r\n    it('should set default values correctly', async () => {\r\n      const propertyData = {\r\n        title: 'Test Property',\r\n        description: 'Test description',\r\n        type: 'apartment',\r\n        price: 100000,\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Victoria Island',\r\n          address: '123 Test Street'\r\n        },\r\n        owner: testUser._id\r\n      };\r\n      const property = new Property(propertyData);\r\n      const savedProperty = await property.save();\r\n\r\n      expect(savedProperty.currency).toBe('NGN');\r\n      expect(savedProperty.status).toBe('available');\r\n      expect(savedProperty.amenities).toEqual([]);\r\n      expect(savedProperty.images).toEqual([]);\r\n      expect(savedProperty.views).toBe(0);\r\n      expect(savedProperty.featured).toBe(false);\r\n    });\r\n\r\n    it('should save location with coordinates', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, {\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Victoria Island',\r\n          address: '123 Test Street',\r\n          coordinates: {\r\n            latitude: 6.4281,\r\n            longitude: 3.4219\r\n          }\r\n        }\r\n      });\r\n      const property = new Property(propertyData);\r\n      const savedProperty = await property.save();\r\n\r\n      expect(savedProperty.location.coordinates.latitude).toBe(6.4281);\r\n      expect(savedProperty.location.coordinates.longitude).toBe(3.4219);\r\n    });\r\n  });\r\n\r\n  describe('Property Validation', () => {\r\n    it('should require title', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { title: undefined });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/title.*required/);\r\n    });\r\n\r\n    it('should require description', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { description: undefined });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/description.*required/);\r\n    });\r\n\r\n    it('should require type', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { type: undefined });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/type.*required/);\r\n    });\r\n\r\n    it('should validate type enum', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { type: 'invalid-type' });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/type.*enum/);\r\n    });\r\n\r\n    it('should accept valid property types', async () => {\r\n      const validTypes = ['apartment', 'house', 'room', 'studio', 'duplex', 'bungalow'];\r\n\r\n      for (const type of validTypes) {\r\n        const propertyData = testUtils.generateTestProperty(testUser._id, { type });\r\n        const property = new Property(propertyData);\r\n        const savedProperty = await property.save();\r\n\r\n        expect(savedProperty.type).toBe(type);\r\n      }\r\n    });\r\n\r\n    it('should require price', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { price: undefined });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/price.*required/);\r\n    });\r\n\r\n    it('should validate price is positive', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { price: -1000 });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/price.*positive/);\r\n    });\r\n\r\n    it('should require owner', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { owner: undefined });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/owner.*required/);\r\n    });\r\n\r\n    it('should validate owner is valid ObjectId', async () => {\r\n      const propertyData = testUtils.generateTestProperty('invalid-id');\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/Cast to ObjectId failed/);\r\n    });\r\n\r\n    it('should validate status enum', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { status: 'invalid-status' });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/status.*enum/);\r\n    });\r\n\r\n    it('should validate currency enum', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, { currency: 'INVALID' });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/currency.*enum/);\r\n    });\r\n  });\r\n\r\n  describe('Property Location Validation', () => {\r\n    it('should require location state', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, {\r\n        location: {\r\n          lga: 'Victoria Island',\r\n          address: '123 Test Street'\r\n        }\r\n      });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/state.*required/);\r\n    });\r\n\r\n    it('should require location LGA', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, {\r\n        location: {\r\n          state: 'Lagos',\r\n          address: '123 Test Street'\r\n        }\r\n      });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/lga.*required/);\r\n    });\r\n\r\n    it('should require location address', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, {\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Victoria Island'\r\n        }\r\n      });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/address.*required/);\r\n    });\r\n\r\n    it('should validate coordinates if provided', async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id, {\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Victoria Island',\r\n          address: '123 Test Street',\r\n          coordinates: {\r\n            latitude: 200, // Invalid latitude\r\n            longitude: 3.4219\r\n          }\r\n        }\r\n      });\r\n      const property = new Property(propertyData);\r\n\r\n      await expect(property.save()).rejects.toThrow(/latitude.*between/);\r\n    });\r\n  });\r\n\r\n  describe('Property Methods', () => {\r\n    let property: IProperty;\r\n\r\n    beforeEach(async () => {\r\n      const propertyData = testUtils.generateTestProperty(testUser._id);\r\n      property = new Property(propertyData);\r\n      await property.save();\r\n    });\r\n\r\n    it('should increment view count', async () => {\r\n      const initialViews = property.views;\r\n      await property.incrementViews();\r\n      \r\n      expect(property.views).toBe(initialViews + 1);\r\n    });\r\n\r\n    it('should check if property is available', () => {\r\n      property.status = 'available';\r\n      expect(property.isAvailable()).toBe(true);\r\n\r\n      property.status = 'rented';\r\n      expect(property.isAvailable()).toBe(false);\r\n    });\r\n\r\n    it('should calculate distance to coordinates', () => {\r\n      property.location.coordinates = {\r\n        latitude: 6.4281,\r\n        longitude: 3.4219\r\n      };\r\n\r\n      const distance = property.distanceTo(6.5244, 3.3792); // Lagos Island coordinates\r\n      expect(typeof distance).toBe('number');\r\n      expect(distance).toBeGreaterThan(0);\r\n    });\r\n\r\n    it('should format price with currency', () => {\r\n      property.price = 120000;\r\n      property.currency = 'NGN';\r\n\r\n      const formattedPrice = property.getFormattedPrice();\r\n      expect(formattedPrice).toBe('₦120,000');\r\n    });\r\n  });\r\n\r\n  describe('Property Statics', () => {\r\n    beforeEach(async () => {\r\n      // Create test properties\r\n      const properties = [\r\n        testUtils.generateTestProperty(testUser._id, { \r\n          status: 'available',\r\n          type: 'apartment',\r\n          price: 100000,\r\n          location: { state: 'Lagos', lga: 'Victoria Island', address: '123 Test St' }\r\n        }),\r\n        testUtils.generateTestProperty(testUser._id, { \r\n          status: 'rented',\r\n          type: 'house',\r\n          price: 200000,\r\n          location: { state: 'Lagos', lga: 'Ikeja', address: '456 Test Ave' }\r\n        }),\r\n        testUtils.generateTestProperty(testUser._id, { \r\n          status: 'available',\r\n          type: 'studio',\r\n          price: 80000,\r\n          location: { state: 'Abuja', lga: 'Wuse', address: '789 Test Rd' }\r\n        })\r\n      ];\r\n\r\n      await Property.insertMany(properties);\r\n    });\r\n\r\n    it('should find available properties', async () => {\r\n      const availableProperties = await Property.findAvailable();\r\n      expect(availableProperties).toHaveLength(2);\r\n      availableProperties.forEach(property => {\r\n        expect(property.status).toBe('available');\r\n      });\r\n    });\r\n\r\n    it('should find properties by type', async () => {\r\n      const apartments = await Property.findByType('apartment');\r\n      expect(apartments).toHaveLength(1);\r\n      expect(apartments[0].type).toBe('apartment');\r\n    });\r\n\r\n    it('should find properties by owner', async () => {\r\n      const ownerProperties = await Property.findByOwner(testUser._id);\r\n      expect(ownerProperties).toHaveLength(3);\r\n      ownerProperties.forEach(property => {\r\n        expect(property.owner.toString()).toBe(testUser._id.toString());\r\n      });\r\n    });\r\n\r\n    it('should find properties by location', async () => {\r\n      const lagosProperties = await Property.findByLocation('Lagos');\r\n      expect(lagosProperties).toHaveLength(2);\r\n      lagosProperties.forEach(property => {\r\n        expect(property.location.state).toBe('Lagos');\r\n      });\r\n    });\r\n\r\n    it('should find properties in price range', async () => {\r\n      const propertiesInRange = await Property.findInPriceRange(90000, 150000);\r\n      expect(propertiesInRange).toHaveLength(1);\r\n      expect(propertiesInRange[0].price).toBe(100000);\r\n    });\r\n\r\n    it('should search properties with filters', async () => {\r\n      const searchResults = await Property.searchProperties({\r\n        state: 'Lagos',\r\n        type: 'apartment',\r\n        minPrice: 50000,\r\n        maxPrice: 150000,\r\n        status: 'available'\r\n      });\r\n\r\n      expect(searchResults).toHaveLength(1);\r\n      expect(searchResults[0].type).toBe('apartment');\r\n      expect(searchResults[0].location.state).toBe('Lagos');\r\n      expect(searchResults[0].status).toBe('available');\r\n    });\r\n  });\r\n\r\n  describe('Property Indexes', () => {\r\n    it('should have location index', async () => {\r\n      const indexes = await Property.collection.getIndexes();\r\n      const locationIndex = Object.keys(indexes).find(key => key.includes('location'));\r\n      expect(locationIndex).toBeDefined();\r\n    });\r\n\r\n    it('should have owner index', async () => {\r\n      const indexes = await Property.collection.getIndexes();\r\n      const ownerIndex = Object.keys(indexes).find(key => key.includes('owner'));\r\n      expect(ownerIndex).toBeDefined();\r\n    });\r\n\r\n    it('should have status index', async () => {\r\n      const indexes = await Property.collection.getIndexes();\r\n      const statusIndex = Object.keys(indexes).find(key => key.includes('status'));\r\n      expect(statusIndex).toBeDefined();\r\n    });\r\n  });\r\n});\r\n"], "version": 3}