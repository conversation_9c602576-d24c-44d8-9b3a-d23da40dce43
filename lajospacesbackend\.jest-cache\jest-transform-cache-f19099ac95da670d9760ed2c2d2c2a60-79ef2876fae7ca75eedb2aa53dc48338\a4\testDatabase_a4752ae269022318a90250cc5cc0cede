ae464ed96611edeb49a54cbbfae55d45
"use strict";

/* istanbul ignore next */
function cov_19t72sdal8() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\testDatabase.ts";
  var hash = "822f12e798ef3d3d0e7c08bc201b45c54baf4d9e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\testDatabase.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "2": {
        start: {
          line: 3,
          column: 26
        },
        end: {
          line: 3,
          column: 33
        }
      },
      "3": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "4": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 7,
          column: 5
        }
      },
      "5": {
        start: {
          line: 6,
          column: 6
        },
        end: {
          line: 6,
          column: 68
        }
      },
      "6": {
        start: {
          line: 6,
          column: 51
        },
        end: {
          line: 6,
          column: 63
        }
      },
      "7": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 39
        }
      },
      "8": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "9": {
        start: {
          line: 10,
          column: 26
        },
        end: {
          line: 10,
          column: 33
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 17
        }
      },
      "11": {
        start: {
          line: 13,
          column: 25
        },
        end: {
          line: 17,
          column: 2
        }
      },
      "12": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 72
        }
      },
      "13": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 21
        }
      },
      "14": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 34,
          column: 4
        }
      },
      "15": {
        start: {
          line: 19,
          column: 18
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "16": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 24,
          column: 10
        }
      },
      "17": {
        start: {
          line: 21,
          column: 21
        },
        end: {
          line: 21,
          column: 23
        }
      },
      "18": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "19": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "20": {
        start: {
          line: 22,
          column: 77
        },
        end: {
          line: 22,
          column: 95
        }
      },
      "21": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 22
        }
      },
      "22": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 26
        }
      },
      "23": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "24": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "25": {
        start: {
          line: 28,
          column: 35
        },
        end: {
          line: 28,
          column: 46
        }
      },
      "26": {
        start: {
          line: 29,
          column: 21
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "28": {
        start: {
          line: 30,
          column: 25
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "29": {
        start: {
          line: 30,
          column: 38
        },
        end: {
          line: 30,
          column: 50
        }
      },
      "30": {
        start: {
          line: 30,
          column: 56
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "31": {
        start: {
          line: 30,
          column: 78
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "32": {
        start: {
          line: 30,
          column: 102
        },
        end: {
          line: 30,
          column: 137
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 40
        }
      },
      "34": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 22
        }
      },
      "35": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "36": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "37": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "38": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 51
        }
      },
      "39": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "40": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 50
        }
      },
      "41": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 38
        }
      },
      "42": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 36
        }
      },
      "43": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 54
        }
      },
      "44": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 50
        }
      },
      "45": {
        start: {
          line: 46,
          column: 19
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "46": {
        start: {
          line: 47,
          column: 32
        },
        end: {
          line: 47,
          column: 64
        }
      },
      "47": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 32
        }
      },
      "48": {
        start: {
          line: 49,
          column: 17
        },
        end: {
          line: 49,
          column: 43
        }
      },
      "49": {
        start: {
          line: 51,
          column: 18
        },
        end: {
          line: 51,
          column: 22
        }
      },
      "50": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 34
        }
      },
      "51": {
        start: {
          line: 53,
          column: 18
        },
        end: {
          line: 53,
          column: 22
        }
      },
      "52": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 34
        }
      },
      "53": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "54": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 70,
          column: 11
        }
      },
      "55": {
        start: {
          line: 71,
          column: 25
        },
        end: {
          line: 71,
          column: 45
        }
      },
      "56": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 78,
          column: 11
        }
      },
      "57": {
        start: {
          line: 80,
          column: 25
        },
        end: {
          line: 80,
          column: 52
        }
      },
      "58": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 88,
          column: 11
        }
      },
      "59": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "60": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 40
        }
      },
      "61": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 92
        }
      },
      "62": {
        start: {
          line: 96,
          column: 23
        },
        end: {
          line: 115,
          column: 9
        }
      },
      "63": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 119,
          column: 11
        }
      },
      "64": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 22
        }
      },
      "65": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 71
        }
      },
      "66": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 20
        }
      },
      "67": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "68": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "69": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 56
        }
      },
      "70": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "71": {
        start: {
          line: 138,
          column: 12
        },
        end: {
          line: 138,
          column: 37
        }
      },
      "72": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 53
        }
      },
      "73": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 145,
          column: 9
        }
      },
      "74": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 143,
          column: 37
        }
      },
      "75": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 53
        }
      },
      "76": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 64
        }
      },
      "77": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 73
        }
      },
      "78": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 20
        }
      },
      "79": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 174,
          column: 5
        }
      },
      "80": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 164,
          column: 9
        }
      },
      "81": {
        start: {
          line: 160,
          column: 32
        },
        end: {
          line: 160,
          column: 84
        }
      },
      "82": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 163,
          column: 13
        }
      },
      "83": {
        start: {
          line: 162,
          column: 16
        },
        end: {
          line: 162,
          column: 48
        }
      },
      "84": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "85": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 40
        }
      },
      "86": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 63
        }
      },
      "87": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 172,
          column: 67
        }
      },
      "88": {
        start: {
          line: 173,
          column: 8
        },
        end: {
          line: 173,
          column: 20
        }
      },
      "89": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 285,
          column: 5
        }
      },
      "90": {
        start: {
          line: 182,
          column: 25
        },
        end: {
          line: 182,
          column: 100
        }
      },
      "91": {
        start: {
          line: 182,
          column: 60
        },
        end: {
          line: 182,
          column: 99
        }
      },
      "92": {
        start: {
          line: 183,
          column: 29
        },
        end: {
          line: 183,
          column: 108
        }
      },
      "93": {
        start: {
          line: 183,
          column: 64
        },
        end: {
          line: 183,
          column: 107
        }
      },
      "94": {
        start: {
          line: 184,
          column: 33
        },
        end: {
          line: 184,
          column: 116
        }
      },
      "95": {
        start: {
          line: 184,
          column: 68
        },
        end: {
          line: 184,
          column: 115
        }
      },
      "96": {
        start: {
          line: 186,
          column: 26
        },
        end: {
          line: 217,
          column: 9
        }
      },
      "97": {
        start: {
          line: 218,
          column: 29
        },
        end: {
          line: 218,
          column: 61
        }
      },
      "98": {
        start: {
          line: 220,
          column: 31
        },
        end: {
          line: 261,
          column: 9
        }
      },
      "99": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 50
        }
      },
      "100": {
        start: {
          line: 264,
          column: 34
        },
        end: {
          line: 274,
          column: 9
        }
      },
      "101": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 275,
          column: 57
        }
      },
      "102": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 280,
          column: 11
        }
      },
      "103": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 283,
          column: 66
        }
      },
      "104": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 20
        }
      },
      "105": {
        start: {
          line: 291,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "106": {
        start: {
          line: 300,
          column: 20
        },
        end: {
          line: 300,
          column: 59
        }
      },
      "107": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 313,
          column: 5
        }
      },
      "108": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 35
        }
      },
      "109": {
        start: {
          line: 303,
          column: 23
        },
        end: {
          line: 303,
          column: 46
        }
      },
      "110": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 42
        }
      },
      "111": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 305,
          column: 22
        }
      },
      "112": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 308,
          column: 41
        }
      },
      "113": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 309,
          column: 20
        }
      },
      "114": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 312,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 2,
            column: 75
          }
        },
        loc: {
          start: {
            line: 2,
            column: 96
          },
          end: {
            line: 9,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 38
          },
          end: {
            line: 6,
            column: 39
          }
        },
        loc: {
          start: {
            line: 6,
            column: 49
          },
          end: {
            line: 6,
            column: 65
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 9,
            column: 7
          }
        },
        loc: {
          start: {
            line: 9,
            column: 28
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 9
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 13,
            column: 81
          }
        },
        loc: {
          start: {
            line: 13,
            column: 95
          },
          end: {
            line: 15,
            column: 1
          }
        },
        line: 13
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 15,
            column: 6
          }
        },
        loc: {
          start: {
            line: 15,
            column: 20
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 15
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 18,
            column: 51
          },
          end: {
            line: 18,
            column: 52
          }
        },
        loc: {
          start: {
            line: 18,
            column: 63
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 18
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 19,
            column: 18
          },
          end: {
            line: 19,
            column: 19
          }
        },
        loc: {
          start: {
            line: 19,
            column: 30
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 19
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 20,
            column: 49
          }
        },
        loc: {
          start: {
            line: 20,
            column: 61
          },
          end: {
            line: 24,
            column: 9
          }
        },
        line: 20
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 27,
            column: 11
          },
          end: {
            line: 27,
            column: 12
          }
        },
        loc: {
          start: {
            line: 27,
            column: 26
          },
          end: {
            line: 33,
            column: 5
          }
        },
        line: 27
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 35,
            column: 56
          },
          end: {
            line: 35,
            column: 57
          }
        },
        loc: {
          start: {
            line: 35,
            column: 71
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 35
      },
      "10": {
        name: "setupTestDatabase",
        decl: {
          start: {
            line: 58,
            column: 15
          },
          end: {
            line: 58,
            column: 32
          }
        },
        loc: {
          start: {
            line: 58,
            column: 35
          },
          end: {
            line: 126,
            column: 1
          }
        },
        line: 58
      },
      "11": {
        name: "cleanupTestDatabase",
        decl: {
          start: {
            line: 130,
            column: 15
          },
          end: {
            line: 130,
            column: 34
          }
        },
        loc: {
          start: {
            line: 130,
            column: 37
          },
          end: {
            line: 152,
            column: 1
          }
        },
        line: 130
      },
      "12": {
        name: "clearTestData",
        decl: {
          start: {
            line: 156,
            column: 15
          },
          end: {
            line: 156,
            column: 28
          }
        },
        loc: {
          start: {
            line: 156,
            column: 31
          },
          end: {
            line: 175,
            column: 1
          }
        },
        line: 156
      },
      "13": {
        name: "seedTestData",
        decl: {
          start: {
            line: 179,
            column: 15
          },
          end: {
            line: 179,
            column: 27
          }
        },
        loc: {
          start: {
            line: 179,
            column: 30
          },
          end: {
            line: 286,
            column: 1
          }
        },
        line: 179
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 182,
            column: 54
          },
          end: {
            line: 182,
            column: 55
          }
        },
        loc: {
          start: {
            line: 182,
            column: 60
          },
          end: {
            line: 182,
            column: 99
          }
        },
        line: 182
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 183,
            column: 58
          },
          end: {
            line: 183,
            column: 59
          }
        },
        loc: {
          start: {
            line: 183,
            column: 64
          },
          end: {
            line: 183,
            column: 107
          }
        },
        line: 183
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 184,
            column: 62
          },
          end: {
            line: 184,
            column: 63
          }
        },
        loc: {
          start: {
            line: 184,
            column: 68
          },
          end: {
            line: 184,
            column: 115
          }
        },
        line: 184
      },
      "17": {
        name: "getTestDatabaseStatus",
        decl: {
          start: {
            line: 290,
            column: 9
          },
          end: {
            line: 290,
            column: 30
          }
        },
        loc: {
          start: {
            line: 290,
            column: 33
          },
          end: {
            line: 295,
            column: 1
          }
        },
        line: 290
      },
      "18": {
        name: "withTestTransaction",
        decl: {
          start: {
            line: 299,
            column: 15
          },
          end: {
            line: 299,
            column: 34
          }
        },
        loc: {
          start: {
            line: 299,
            column: 45
          },
          end: {
            line: 314,
            column: 1
          }
        },
        line: 299
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 2,
            column: 57
          },
          end: {
            line: 12,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 2,
            column: 74
          },
          end: {
            line: 9,
            column: 1
          }
        }, {
          start: {
            line: 9,
            column: 6
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 3,
            column: 4
          },
          end: {
            line: 3,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 4
          },
          end: {
            line: 7,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      },
      "4": {
        loc: {
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 8
          },
          end: {
            line: 5,
            column: 13
          }
        }, {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "5": {
        loc: {
          start: {
            line: 5,
            column: 18
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 47
          }
        }, {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "6": {
        loc: {
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 50
          },
          end: {
            line: 5,
            column: 63
          }
        }, {
          start: {
            line: 5,
            column: 67
          },
          end: {
            line: 5,
            column: 84
          }
        }],
        line: 5
      },
      "7": {
        loc: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 17,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 26
          },
          end: {
            line: 13,
            column: 30
          }
        }, {
          start: {
            line: 13,
            column: 34
          },
          end: {
            line: 13,
            column: 57
          }
        }, {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 80
          },
          end: {
            line: 15,
            column: 1
          }
        }, {
          start: {
            line: 15,
            column: 5
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 13
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 34,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 20
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 45
          }
        }, {
          start: {
            line: 18,
            column: 50
          },
          end: {
            line: 34,
            column: 4
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 24,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 18
          },
          end: {
            line: 20,
            column: 44
          }
        }, {
          start: {
            line: 20,
            column: 48
          },
          end: {
            line: 24,
            column: 9
          }
        }],
        line: 20
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 28,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 12
          },
          end: {
            line: 28,
            column: 15
          }
        }, {
          start: {
            line: 28,
            column: 19
          },
          end: {
            line: 28,
            column: 33
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "16": {
        loc: {
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 78
          },
          end: {
            line: 30,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "17": {
        loc: {
          start: {
            line: 35,
            column: 22
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 35,
            column: 27
          }
        }, {
          start: {
            line: 35,
            column: 31
          },
          end: {
            line: 35,
            column: 51
          }
        }, {
          start: {
            line: 35,
            column: 56
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 35
      },
      "18": {
        loc: {
          start: {
            line: 36,
            column: 11
          },
          end: {
            line: 36,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 37
          },
          end: {
            line: 36,
            column: 40
          }
        }, {
          start: {
            line: 36,
            column: 43
          },
          end: {
            line: 36,
            column: 61
          }
        }],
        line: 36
      },
      "19": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 15
          }
        }, {
          start: {
            line: 36,
            column: 19
          },
          end: {
            line: 36,
            column: 33
          }
        }],
        line: 36
      },
      "20": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "21": {
        loc: {
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 140,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "22": {
        loc: {
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 145,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "23": {
        loc: {
          start: {
            line: 142,
            column: 12
          },
          end: {
            line: 142,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 12
          },
          end: {
            line: 142,
            column: 23
          }
        }, {
          start: {
            line: 142,
            column: 27
          },
          end: {
            line: 142,
            column: 45
          }
        }],
        line: 142
      },
      "24": {
        loc: {
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 164,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 164,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "25": {
        loc: {
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "26": {
        loc: {
          start: {
            line: 166,
            column: 12
          },
          end: {
            line: 166,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 12
          },
          end: {
            line: 166,
            column: 23
          }
        }, {
          start: {
            line: 166,
            column: 27
          },
          end: {
            line: 166,
            column: 45
          }
        }],
        line: 166
      },
      "27": {
        loc: {
          start: {
            line: 292,
            column: 17
          },
          end: {
            line: 292,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 292,
            column: 66
          },
          end: {
            line: 292,
            column: 77
          }
        }, {
          start: {
            line: 292,
            column: 80
          },
          end: {
            line: 292,
            column: 94
          }
        }],
        line: 292
      },
      "28": {
        loc: {
          start: {
            line: 293,
            column: 15
          },
          end: {
            line: 293,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 293,
            column: 51
          },
          end: {
            line: 293,
            column: 62
          }
        }, {
          start: {
            line: 293,
            column: 65
          },
          end: {
            line: 293,
            column: 79
          }
        }],
        line: 293
      },
      "29": {
        loc: {
          start: {
            line: 293,
            column: 15
          },
          end: {
            line: 293,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 15
          },
          end: {
            line: 293,
            column: 26
          }
        }, {
          start: {
            line: 293,
            column: 30
          },
          end: {
            line: 293,
            column: 48
          }
        }],
        line: 293
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\testDatabase.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,8CA0EC;AAKD,kDAwBC;AAKD,sCAqBC;AAKD,oCAiHC;AAKD,sDAQC;AAKD,kDAgBC;AAjTD,wDAAgC;AAChC,iEAA0D;AAC1D,iCAAsD;AACtD,4CAAyC;AAczC,6BAA6B;AAC7B,IAAI,WAAW,GAA6B,IAAI,CAAC;AAiSxC,kCAAW;AAhSpB,IAAI,WAAW,GAA2B,IAAI,CAAC;AAgSzB,kCAAW;AA9RjC;;GAEG;AACI,KAAK,UAAU,iBAAiB;IACrC,IAAI,CAAC;QACH,8BAA8B;QAC9B,sBAAA,WAAW,GAAG,MAAM,yCAAiB,CAAC,MAAM,CAAC;YAC3C,QAAQ,EAAE;gBACR,MAAM,EAAE,iBAAiB;gBACzB,IAAI,EAAE,CAAC,EAAE,wBAAwB;aAClC;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,oBAAoB;aAClC;SACF,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;QAEtC,qBAAqB;QACrB,MAAM,kBAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE;YAC/B,cAAc,EAAE,KAAK;YACrB,WAAW,EAAE,EAAE;YACf,wBAAwB,EAAE,IAAI;YAC9B,eAAe,EAAE,KAAK;SACvB,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,QAAQ,GAAG,2BAA2B,CAAC,CAAC,4BAA4B;QAE1E,gCAAgC;QAChC,sBAAA,WAAW,GAAG,IAAA,oBAAY,EAAC;YACzB,GAAG,EAAE,QAAQ;YACb,MAAM,EAAE;gBACN,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAEH,uDAAuD;QACvD,IAAI,CAAC;YACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,MAAM,GAAuB;YACjC,OAAO,EAAE;gBACP,GAAG,EAAE,QAAQ;gBACb,OAAO,EAAE;oBACP,cAAc,EAAE,KAAK;oBACrB,WAAW,EAAE,EAAE;oBACf,wBAAwB,EAAE,IAAI;oBAC9B,eAAe,EAAE,KAAK;iBACvB;aACF;YACD,KAAK,EAAE;gBACL,GAAG,EAAE,QAAQ;gBACb,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,cAAc,EAAE,IAAI;wBACpB,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;SACF,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;YAC3C,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB;IACvC,IAAI,CAAC;QACH,2BAA2B;QAC3B,IAAI,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,kBAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACpC,CAAC;QAED,6BAA6B;QAC7B,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,sBAAA,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,yBAAyB;QACzB,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,sBAAA,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa;IACjC,IAAI,CAAC;QACH,4BAA4B;QAC5B,IAAI,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,kBAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YAE/D,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,MAAM,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY;IAChC,IAAI,CAAC;QACH,gBAAgB;QAChB,MAAM,EAAE,IAAI,EAAE,GAAG,wDAAa,gBAAgB,GAAC,CAAC;QAChD,MAAM,EAAE,QAAQ,EAAE,GAAG,wDAAa,oBAAoB,GAAC,CAAC;QACxD,MAAM,EAAE,YAAY,EAAE,GAAG,wDAAa,wBAAwB,GAAC,CAAC;QAEhE,oBAAoB;QACpB,MAAM,SAAS,GAAG;YAChB;gBACE,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,gBAAgB;gBAC7B,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,qBAAqB;gBAC5B,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,gBAAgB;gBAC7B,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,gBAAgB;gBACvB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,gBAAgB;gBAC7B,IAAI,EAAE,OAAO;gBACb,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAEtD,yBAAyB;QACzB,MAAM,cAAc,GAAG;YACrB;gBACE,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,uCAAuC;gBACpD,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,iBAAiB;oBACtB,OAAO,EAAE,sBAAsB;oBAC/B,WAAW,EAAE;wBACX,QAAQ,EAAE,MAAM;wBAChB,SAAS,EAAE,MAAM;qBAClB;iBACF;gBACD,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;gBACxD,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG;gBAC1B,MAAM,EAAE,WAAW;aACpB;YACD;gBACE,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,iCAAiC;gBAC9C,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,OAAO;oBACZ,OAAO,EAAE,kBAAkB;oBAC3B,WAAW,EAAE;wBACX,QAAQ,EAAE,MAAM;wBAChB,SAAS,EAAE,MAAM;qBAClB;iBACF;gBACD,SAAS,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;gBACpC,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG;gBAC1B,MAAM,EAAE,WAAW;aACpB;SACF,CAAC;QAEF,MAAM,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAE1C,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG;YACxB;gBACE,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG;gBAC3B,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,wBAAwB;gBAC/B,OAAO,EAAE,qCAAqC;gBAC9C,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,KAAK;aACjB;SACF,CAAC;QAEF,MAAM,YAAY,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAEjD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;YAC3C,KAAK,EAAE,YAAY,CAAC,MAAM;YAC1B,UAAU,EAAE,cAAc,CAAC,MAAM;YACjC,aAAa,EAAE,iBAAiB,CAAC,MAAM;SACxC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB;IAInC,OAAO;QACL,OAAO,EAAE,kBAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;QAC5E,KAAK,EAAE,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc;KACxE,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CACvC,QAAyD;IAEzD,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,YAAY,EAAE,CAAC;IAE9C,IAAI,CAAC;QACH,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAClC,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjC,MAAM,KAAK,CAAC;IACd,CAAC;YAAS,CAAC;QACT,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\testDatabase.ts"],
      sourcesContent: ["import mongoose from 'mongoose';\r\nimport { MongoMemoryServer } from 'mongodb-memory-server';\r\nimport { createClient, RedisClientType } from 'redis';\r\nimport { logger } from '../utils/logger';\r\n\r\n// Test database configuration\r\nexport interface TestDatabaseConfig {\r\n  mongodb: {\r\n    uri: string;\r\n    options: mongoose.ConnectOptions;\r\n  };\r\n  redis: {\r\n    url: string;\r\n    options: any;\r\n  };\r\n}\r\n\r\n// In-memory MongoDB instance\r\nlet mongoServer: MongoMemoryServer | null = null;\r\nlet redisClient: RedisClientType | null = null;\r\n\r\n/**\r\n * Setup test database connections\r\n */\r\nexport async function setupTestDatabase(): Promise<TestDatabaseConfig> {\r\n  try {\r\n    // Setup MongoDB Memory Server\r\n    mongoServer = await MongoMemoryServer.create({\r\n      instance: {\r\n        dbName: 'lajospaces_test',\r\n        port: 0, // Random available port\r\n      },\r\n      binary: {\r\n        version: '6.0.0',\r\n        downloadDir: './mongodb-binaries'\r\n      }\r\n    });\r\n\r\n    const mongoUri = mongoServer.getUri();\r\n    \r\n    // Connect to MongoDB\r\n    await mongoose.connect(mongoUri, {\r\n      bufferCommands: false,\r\n      maxPoolSize: 10,\r\n      serverSelectionTimeoutMS: 5000,\r\n      socketTimeoutMS: 45000,\r\n    });\r\n\r\n    // Setup Redis Mock (using redis-mock for testing)\r\n    const redisUrl = 'redis://localhost:6379/15'; // Use database 15 for tests\r\n    \r\n    // Create Redis client for tests\r\n    redisClient = createClient({\r\n      url: redisUrl,\r\n      socket: {\r\n        connectTimeout: 5000,\r\n        lazyConnect: true\r\n      }\r\n    });\r\n\r\n    // Connect to Redis (will use mock in test environment)\r\n    try {\r\n      await redisClient.connect();\r\n    } catch (error) {\r\n      logger.warn('Redis connection failed in test environment, using mock');\r\n    }\r\n\r\n    const config: TestDatabaseConfig = {\r\n      mongodb: {\r\n        uri: mongoUri,\r\n        options: {\r\n          bufferCommands: false,\r\n          maxPoolSize: 10,\r\n          serverSelectionTimeoutMS: 5000,\r\n          socketTimeoutMS: 45000,\r\n        }\r\n      },\r\n      redis: {\r\n        url: redisUrl,\r\n        options: {\r\n          socket: {\r\n            connectTimeout: 5000,\r\n            lazyConnect: true\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    logger.info('Test database setup completed', {\r\n      mongodb: mongoUri,\r\n      redis: redisUrl\r\n    });\r\n\r\n    return config;\r\n  } catch (error) {\r\n    logger.error('Failed to setup test database:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Cleanup test database connections\r\n */\r\nexport async function cleanupTestDatabase(): Promise<void> {\r\n  try {\r\n    // Close MongoDB connection\r\n    if (mongoose.connection.readyState !== 0) {\r\n      await mongoose.connection.close();\r\n    }\r\n\r\n    // Stop MongoDB Memory Server\r\n    if (mongoServer) {\r\n      await mongoServer.stop();\r\n      mongoServer = null;\r\n    }\r\n\r\n    // Close Redis connection\r\n    if (redisClient && redisClient.isOpen) {\r\n      await redisClient.quit();\r\n      redisClient = null;\r\n    }\r\n\r\n    logger.info('Test database cleanup completed');\r\n  } catch (error) {\r\n    logger.error('Failed to cleanup test database:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Clear all test data from databases\r\n */\r\nexport async function clearTestData(): Promise<void> {\r\n  try {\r\n    // Clear MongoDB collections\r\n    if (mongoose.connection.readyState === 1) {\r\n      const collections = await mongoose.connection.db.collections();\r\n      \r\n      for (const collection of collections) {\r\n        await collection.deleteMany({});\r\n      }\r\n    }\r\n\r\n    // Clear Redis data\r\n    if (redisClient && redisClient.isOpen) {\r\n      await redisClient.flushDb();\r\n    }\r\n\r\n    logger.info('Test data cleared successfully');\r\n  } catch (error) {\r\n    logger.error('Failed to clear test data:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Seed test data\r\n */\r\nexport async function seedTestData(): Promise<void> {\r\n  try {\r\n    // Import models\r\n    const { User } = await import('../models/User');\r\n    const { Property } = await import('../models/Property');\r\n    const { Notification } = await import('../models/Notification');\r\n\r\n    // Create test users\r\n    const testUsers = [\r\n      {\r\n        firstName: 'John',\r\n        lastName: 'Doe',\r\n        email: '<EMAIL>',\r\n        password: 'password123',\r\n        phoneNumber: '+2348012345678',\r\n        role: 'user',\r\n        emailVerified: true,\r\n        isActive: true\r\n      },\r\n      {\r\n        firstName: 'Jane',\r\n        lastName: 'Smith',\r\n        email: '<EMAIL>',\r\n        password: 'password123',\r\n        phoneNumber: '+2348012345679',\r\n        role: 'user',\r\n        emailVerified: true,\r\n        isActive: true\r\n      },\r\n      {\r\n        firstName: 'Admin',\r\n        lastName: 'User',\r\n        email: '<EMAIL>',\r\n        password: 'admin123',\r\n        phoneNumber: '+2348012345680',\r\n        role: 'admin',\r\n        emailVerified: true,\r\n        isActive: true\r\n      }\r\n    ];\r\n\r\n    const createdUsers = await User.insertMany(testUsers);\r\n\r\n    // Create test properties\r\n    const testProperties = [\r\n      {\r\n        title: 'Beautiful 2-Bedroom Apartment',\r\n        description: 'Spacious apartment in Victoria Island',\r\n        type: 'apartment',\r\n        price: 120000,\r\n        currency: 'NGN',\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Victoria Island',\r\n          address: '123 Ahmadu Bello Way',\r\n          coordinates: {\r\n            latitude: 6.4281,\r\n            longitude: 3.4219\r\n          }\r\n        },\r\n        amenities: ['parking', 'security', 'generator', 'water'],\r\n        images: [],\r\n        owner: createdUsers[0]._id,\r\n        status: 'available'\r\n      },\r\n      {\r\n        title: 'Cozy Studio Apartment',\r\n        description: 'Perfect for young professionals',\r\n        type: 'studio',\r\n        price: 80000,\r\n        currency: 'NGN',\r\n        location: {\r\n          state: 'Lagos',\r\n          lga: 'Ikeja',\r\n          address: '456 Allen Avenue',\r\n          coordinates: {\r\n            latitude: 6.6018,\r\n            longitude: 3.3515\r\n          }\r\n        },\r\n        amenities: ['security', 'generator'],\r\n        images: [],\r\n        owner: createdUsers[1]._id,\r\n        status: 'available'\r\n      }\r\n    ];\r\n\r\n    await Property.insertMany(testProperties);\r\n\r\n    // Create test notifications\r\n    const testNotifications = [\r\n      {\r\n        userId: createdUsers[0]._id,\r\n        type: 'welcome',\r\n        title: 'Welcome to LajoSpaces!',\r\n        message: 'Thank you for joining our platform.',\r\n        priority: 'medium',\r\n        read: false,\r\n        dismissed: false\r\n      }\r\n    ];\r\n\r\n    await Notification.insertMany(testNotifications);\r\n\r\n    logger.info('Test data seeded successfully', {\r\n      users: createdUsers.length,\r\n      properties: testProperties.length,\r\n      notifications: testNotifications.length\r\n    });\r\n  } catch (error) {\r\n    logger.error('Failed to seed test data:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get test database connection status\r\n */\r\nexport function getTestDatabaseStatus(): {\r\n  mongodb: string;\r\n  redis: string;\r\n} {\r\n  return {\r\n    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',\r\n    redis: redisClient && redisClient.isOpen ? 'connected' : 'disconnected'\r\n  };\r\n}\r\n\r\n/**\r\n * Create test database transaction\r\n */\r\nexport async function withTestTransaction<T>(\r\n  callback: (session: mongoose.ClientSession) => Promise<T>\r\n): Promise<T> {\r\n  const session = await mongoose.startSession();\r\n  \r\n  try {\r\n    session.startTransaction();\r\n    const result = await callback(session);\r\n    await session.commitTransaction();\r\n    return result;\r\n  } catch (error) {\r\n    await session.abortTransaction();\r\n    throw error;\r\n  } finally {\r\n    await session.endSession();\r\n  }\r\n}\r\n\r\nexport { mongoServer, redisClient };\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "822f12e798ef3d3d0e7c08bc201b45c54baf4d9e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_19t72sdal8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_19t72sdal8();
var __createBinding =
/* istanbul ignore next */
(cov_19t72sdal8().s[0]++,
/* istanbul ignore next */
(cov_19t72sdal8().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_19t72sdal8().b[0][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_19t72sdal8().b[0][2]++, Object.create ?
/* istanbul ignore next */
(cov_19t72sdal8().b[1][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[0]++;
  cov_19t72sdal8().s[1]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_19t72sdal8().b[2][0]++;
    cov_19t72sdal8().s[2]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_19t72sdal8().b[2][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_19t72sdal8().s[3]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_19t72sdal8().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_19t72sdal8().b[4][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_19t72sdal8().b[4][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_19t72sdal8().b[5][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_19t72sdal8().b[5][1]++,
  /* istanbul ignore next */
  (cov_19t72sdal8().b[6][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_19t72sdal8().b[6][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_19t72sdal8().b[3][0]++;
    cov_19t72sdal8().s[5]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_19t72sdal8().f[1]++;
        cov_19t72sdal8().s[6]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_19t72sdal8().b[3][1]++;
  }
  cov_19t72sdal8().s[7]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_19t72sdal8().b[1][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[2]++;
  cov_19t72sdal8().s[8]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_19t72sdal8().b[7][0]++;
    cov_19t72sdal8().s[9]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_19t72sdal8().b[7][1]++;
  }
  cov_19t72sdal8().s[10]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_19t72sdal8().s[11]++,
/* istanbul ignore next */
(cov_19t72sdal8().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_19t72sdal8().b[8][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_19t72sdal8().b[8][2]++, Object.create ?
/* istanbul ignore next */
(cov_19t72sdal8().b[9][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[3]++;
  cov_19t72sdal8().s[12]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_19t72sdal8().b[9][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[4]++;
  cov_19t72sdal8().s[13]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_19t72sdal8().s[14]++,
/* istanbul ignore next */
(cov_19t72sdal8().b[10][0]++, this) &&
/* istanbul ignore next */
(cov_19t72sdal8().b[10][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_19t72sdal8().b[10][2]++, function () {
  /* istanbul ignore next */
  cov_19t72sdal8().f[5]++;
  cov_19t72sdal8().s[15]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_19t72sdal8().f[6]++;
    cov_19t72sdal8().s[16]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_19t72sdal8().b[11][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_19t72sdal8().b[11][1]++, function (o) {
      /* istanbul ignore next */
      cov_19t72sdal8().f[7]++;
      var ar =
      /* istanbul ignore next */
      (cov_19t72sdal8().s[17]++, []);
      /* istanbul ignore next */
      cov_19t72sdal8().s[18]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_19t72sdal8().s[19]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_19t72sdal8().b[12][0]++;
          cov_19t72sdal8().s[20]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_19t72sdal8().b[12][1]++;
        }
      }
      /* istanbul ignore next */
      cov_19t72sdal8().s[21]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_19t72sdal8().s[22]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_19t72sdal8().s[23]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_19t72sdal8().f[8]++;
    cov_19t72sdal8().s[24]++;
    if (
    /* istanbul ignore next */
    (cov_19t72sdal8().b[14][0]++, mod) &&
    /* istanbul ignore next */
    (cov_19t72sdal8().b[14][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[13][0]++;
      cov_19t72sdal8().s[25]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[13][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[26]++, {});
    /* istanbul ignore next */
    cov_19t72sdal8().s[27]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[15][0]++;
      cov_19t72sdal8().s[28]++;
      for (var k =
        /* istanbul ignore next */
        (cov_19t72sdal8().s[29]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_19t72sdal8().s[30]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_19t72sdal8().s[31]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_19t72sdal8().b[16][0]++;
          cov_19t72sdal8().s[32]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_19t72sdal8().b[16][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[15][1]++;
    }
    cov_19t72sdal8().s[33]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_19t72sdal8().s[34]++;
    return result;
  };
}()));
var __importDefault =
/* istanbul ignore next */
(cov_19t72sdal8().s[35]++,
/* istanbul ignore next */
(cov_19t72sdal8().b[17][0]++, this) &&
/* istanbul ignore next */
(cov_19t72sdal8().b[17][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_19t72sdal8().b[17][2]++, function (mod) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[9]++;
  cov_19t72sdal8().s[36]++;
  return /* istanbul ignore next */(cov_19t72sdal8().b[19][0]++, mod) &&
  /* istanbul ignore next */
  (cov_19t72sdal8().b[19][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_19t72sdal8().b[18][0]++, mod) :
  /* istanbul ignore next */
  (cov_19t72sdal8().b[18][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_19t72sdal8().s[37]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_19t72sdal8().s[38]++;
exports.redisClient = exports.mongoServer = void 0;
/* istanbul ignore next */
cov_19t72sdal8().s[39]++;
exports.setupTestDatabase = setupTestDatabase;
/* istanbul ignore next */
cov_19t72sdal8().s[40]++;
exports.cleanupTestDatabase = cleanupTestDatabase;
/* istanbul ignore next */
cov_19t72sdal8().s[41]++;
exports.clearTestData = clearTestData;
/* istanbul ignore next */
cov_19t72sdal8().s[42]++;
exports.seedTestData = seedTestData;
/* istanbul ignore next */
cov_19t72sdal8().s[43]++;
exports.getTestDatabaseStatus = getTestDatabaseStatus;
/* istanbul ignore next */
cov_19t72sdal8().s[44]++;
exports.withTestTransaction = withTestTransaction;
const mongoose_1 =
/* istanbul ignore next */
(cov_19t72sdal8().s[45]++, __importDefault(require("mongoose")));
const mongodb_memory_server_1 =
/* istanbul ignore next */
(cov_19t72sdal8().s[46]++, require("mongodb-memory-server"));
const redis_1 =
/* istanbul ignore next */
(cov_19t72sdal8().s[47]++, require("redis"));
const logger_1 =
/* istanbul ignore next */
(cov_19t72sdal8().s[48]++, require("../utils/logger"));
// In-memory MongoDB instance
let mongoServer =
/* istanbul ignore next */
(cov_19t72sdal8().s[49]++, null);
/* istanbul ignore next */
cov_19t72sdal8().s[50]++;
exports.mongoServer = mongoServer;
let redisClient =
/* istanbul ignore next */
(cov_19t72sdal8().s[51]++, null);
/* istanbul ignore next */
cov_19t72sdal8().s[52]++;
exports.redisClient = redisClient;
/**
 * Setup test database connections
 */
async function setupTestDatabase() {
  /* istanbul ignore next */
  cov_19t72sdal8().f[10]++;
  cov_19t72sdal8().s[53]++;
  try {
    /* istanbul ignore next */
    cov_19t72sdal8().s[54]++;
    // Setup MongoDB Memory Server
    exports.mongoServer = mongoServer = await mongodb_memory_server_1.MongoMemoryServer.create({
      instance: {
        dbName: 'lajospaces_test',
        port: 0 // Random available port
      },
      binary: {
        version: '6.0.0',
        downloadDir: './mongodb-binaries'
      }
    });
    const mongoUri =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[55]++, mongoServer.getUri());
    // Connect to MongoDB
    /* istanbul ignore next */
    cov_19t72sdal8().s[56]++;
    await mongoose_1.default.connect(mongoUri, {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    });
    // Setup Redis Mock (using redis-mock for testing)
    const redisUrl =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[57]++, 'redis://localhost:6379/15'); // Use database 15 for tests
    // Create Redis client for tests
    /* istanbul ignore next */
    cov_19t72sdal8().s[58]++;
    exports.redisClient = redisClient = (0, redis_1.createClient)({
      url: redisUrl,
      socket: {
        connectTimeout: 5000,
        lazyConnect: true
      }
    });
    // Connect to Redis (will use mock in test environment)
    /* istanbul ignore next */
    cov_19t72sdal8().s[59]++;
    try {
      /* istanbul ignore next */
      cov_19t72sdal8().s[60]++;
      await redisClient.connect();
    } catch (error) {
      /* istanbul ignore next */
      cov_19t72sdal8().s[61]++;
      logger_1.logger.warn('Redis connection failed in test environment, using mock');
    }
    const config =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[62]++, {
      mongodb: {
        uri: mongoUri,
        options: {
          bufferCommands: false,
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000
        }
      },
      redis: {
        url: redisUrl,
        options: {
          socket: {
            connectTimeout: 5000,
            lazyConnect: true
          }
        }
      }
    });
    /* istanbul ignore next */
    cov_19t72sdal8().s[63]++;
    logger_1.logger.info('Test database setup completed', {
      mongodb: mongoUri,
      redis: redisUrl
    });
    /* istanbul ignore next */
    cov_19t72sdal8().s[64]++;
    return config;
  } catch (error) {
    /* istanbul ignore next */
    cov_19t72sdal8().s[65]++;
    logger_1.logger.error('Failed to setup test database:', error);
    /* istanbul ignore next */
    cov_19t72sdal8().s[66]++;
    throw error;
  }
}
/**
 * Cleanup test database connections
 */
async function cleanupTestDatabase() {
  /* istanbul ignore next */
  cov_19t72sdal8().f[11]++;
  cov_19t72sdal8().s[67]++;
  try {
    /* istanbul ignore next */
    cov_19t72sdal8().s[68]++;
    // Close MongoDB connection
    if (mongoose_1.default.connection.readyState !== 0) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[20][0]++;
      cov_19t72sdal8().s[69]++;
      await mongoose_1.default.connection.close();
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[20][1]++;
    }
    // Stop MongoDB Memory Server
    cov_19t72sdal8().s[70]++;
    if (mongoServer) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[21][0]++;
      cov_19t72sdal8().s[71]++;
      await mongoServer.stop();
      /* istanbul ignore next */
      cov_19t72sdal8().s[72]++;
      exports.mongoServer = mongoServer = null;
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[21][1]++;
    }
    // Close Redis connection
    cov_19t72sdal8().s[73]++;
    if (
    /* istanbul ignore next */
    (cov_19t72sdal8().b[23][0]++, redisClient) &&
    /* istanbul ignore next */
    (cov_19t72sdal8().b[23][1]++, redisClient.isOpen)) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[22][0]++;
      cov_19t72sdal8().s[74]++;
      await redisClient.quit();
      /* istanbul ignore next */
      cov_19t72sdal8().s[75]++;
      exports.redisClient = redisClient = null;
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[22][1]++;
    }
    cov_19t72sdal8().s[76]++;
    logger_1.logger.info('Test database cleanup completed');
  } catch (error) {
    /* istanbul ignore next */
    cov_19t72sdal8().s[77]++;
    logger_1.logger.error('Failed to cleanup test database:', error);
    /* istanbul ignore next */
    cov_19t72sdal8().s[78]++;
    throw error;
  }
}
/**
 * Clear all test data from databases
 */
async function clearTestData() {
  /* istanbul ignore next */
  cov_19t72sdal8().f[12]++;
  cov_19t72sdal8().s[79]++;
  try {
    /* istanbul ignore next */
    cov_19t72sdal8().s[80]++;
    // Clear MongoDB collections
    if (mongoose_1.default.connection.readyState === 1) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[24][0]++;
      const collections =
      /* istanbul ignore next */
      (cov_19t72sdal8().s[81]++, await mongoose_1.default.connection.db.collections());
      /* istanbul ignore next */
      cov_19t72sdal8().s[82]++;
      for (const collection of collections) {
        /* istanbul ignore next */
        cov_19t72sdal8().s[83]++;
        await collection.deleteMany({});
      }
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[24][1]++;
    }
    // Clear Redis data
    cov_19t72sdal8().s[84]++;
    if (
    /* istanbul ignore next */
    (cov_19t72sdal8().b[26][0]++, redisClient) &&
    /* istanbul ignore next */
    (cov_19t72sdal8().b[26][1]++, redisClient.isOpen)) {
      /* istanbul ignore next */
      cov_19t72sdal8().b[25][0]++;
      cov_19t72sdal8().s[85]++;
      await redisClient.flushDb();
    } else
    /* istanbul ignore next */
    {
      cov_19t72sdal8().b[25][1]++;
    }
    cov_19t72sdal8().s[86]++;
    logger_1.logger.info('Test data cleared successfully');
  } catch (error) {
    /* istanbul ignore next */
    cov_19t72sdal8().s[87]++;
    logger_1.logger.error('Failed to clear test data:', error);
    /* istanbul ignore next */
    cov_19t72sdal8().s[88]++;
    throw error;
  }
}
/**
 * Seed test data
 */
async function seedTestData() {
  /* istanbul ignore next */
  cov_19t72sdal8().f[13]++;
  cov_19t72sdal8().s[89]++;
  try {
    // Import models
    const {
      User
    } =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[90]++, await Promise.resolve().then(() => {
      /* istanbul ignore next */
      cov_19t72sdal8().f[14]++;
      cov_19t72sdal8().s[91]++;
      return __importStar(require('../models/User'));
    }));
    const {
      Property
    } =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[92]++, await Promise.resolve().then(() => {
      /* istanbul ignore next */
      cov_19t72sdal8().f[15]++;
      cov_19t72sdal8().s[93]++;
      return __importStar(require('../models/Property'));
    }));
    const {
      Notification
    } =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[94]++, await Promise.resolve().then(() => {
      /* istanbul ignore next */
      cov_19t72sdal8().f[16]++;
      cov_19t72sdal8().s[95]++;
      return __importStar(require('../models/Notification'));
    }));
    // Create test users
    const testUsers =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[96]++, [{
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '+2348012345678',
      role: 'user',
      emailVerified: true,
      isActive: true
    }, {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '+2348012345679',
      role: 'user',
      emailVerified: true,
      isActive: true
    }, {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'admin123',
      phoneNumber: '+2348012345680',
      role: 'admin',
      emailVerified: true,
      isActive: true
    }]);
    const createdUsers =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[97]++, await User.insertMany(testUsers));
    // Create test properties
    const testProperties =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[98]++, [{
      title: 'Beautiful 2-Bedroom Apartment',
      description: 'Spacious apartment in Victoria Island',
      type: 'apartment',
      price: 120000,
      currency: 'NGN',
      location: {
        state: 'Lagos',
        lga: 'Victoria Island',
        address: '123 Ahmadu Bello Way',
        coordinates: {
          latitude: 6.4281,
          longitude: 3.4219
        }
      },
      amenities: ['parking', 'security', 'generator', 'water'],
      images: [],
      owner: createdUsers[0]._id,
      status: 'available'
    }, {
      title: 'Cozy Studio Apartment',
      description: 'Perfect for young professionals',
      type: 'studio',
      price: 80000,
      currency: 'NGN',
      location: {
        state: 'Lagos',
        lga: 'Ikeja',
        address: '456 Allen Avenue',
        coordinates: {
          latitude: 6.6018,
          longitude: 3.3515
        }
      },
      amenities: ['security', 'generator'],
      images: [],
      owner: createdUsers[1]._id,
      status: 'available'
    }]);
    /* istanbul ignore next */
    cov_19t72sdal8().s[99]++;
    await Property.insertMany(testProperties);
    // Create test notifications
    const testNotifications =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[100]++, [{
      userId: createdUsers[0]._id,
      type: 'welcome',
      title: 'Welcome to LajoSpaces!',
      message: 'Thank you for joining our platform.',
      priority: 'medium',
      read: false,
      dismissed: false
    }]);
    /* istanbul ignore next */
    cov_19t72sdal8().s[101]++;
    await Notification.insertMany(testNotifications);
    /* istanbul ignore next */
    cov_19t72sdal8().s[102]++;
    logger_1.logger.info('Test data seeded successfully', {
      users: createdUsers.length,
      properties: testProperties.length,
      notifications: testNotifications.length
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_19t72sdal8().s[103]++;
    logger_1.logger.error('Failed to seed test data:', error);
    /* istanbul ignore next */
    cov_19t72sdal8().s[104]++;
    throw error;
  }
}
/**
 * Get test database connection status
 */
function getTestDatabaseStatus() {
  /* istanbul ignore next */
  cov_19t72sdal8().f[17]++;
  cov_19t72sdal8().s[105]++;
  return {
    mongodb: mongoose_1.default.connection.readyState === 1 ?
    /* istanbul ignore next */
    (cov_19t72sdal8().b[27][0]++, 'connected') :
    /* istanbul ignore next */
    (cov_19t72sdal8().b[27][1]++, 'disconnected'),
    redis:
    /* istanbul ignore next */
    (cov_19t72sdal8().b[29][0]++, redisClient) &&
    /* istanbul ignore next */
    (cov_19t72sdal8().b[29][1]++, redisClient.isOpen) ?
    /* istanbul ignore next */
    (cov_19t72sdal8().b[28][0]++, 'connected') :
    /* istanbul ignore next */
    (cov_19t72sdal8().b[28][1]++, 'disconnected')
  };
}
/**
 * Create test database transaction
 */
async function withTestTransaction(callback) {
  /* istanbul ignore next */
  cov_19t72sdal8().f[18]++;
  const session =
  /* istanbul ignore next */
  (cov_19t72sdal8().s[106]++, await mongoose_1.default.startSession());
  /* istanbul ignore next */
  cov_19t72sdal8().s[107]++;
  try {
    /* istanbul ignore next */
    cov_19t72sdal8().s[108]++;
    session.startTransaction();
    const result =
    /* istanbul ignore next */
    (cov_19t72sdal8().s[109]++, await callback(session));
    /* istanbul ignore next */
    cov_19t72sdal8().s[110]++;
    await session.commitTransaction();
    /* istanbul ignore next */
    cov_19t72sdal8().s[111]++;
    return result;
  } catch (error) {
    /* istanbul ignore next */
    cov_19t72sdal8().s[112]++;
    await session.abortTransaction();
    /* istanbul ignore next */
    cov_19t72sdal8().s[113]++;
    throw error;
  } finally {
    /* istanbul ignore next */
    cov_19t72sdal8().s[114]++;
    await session.endSession();
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************