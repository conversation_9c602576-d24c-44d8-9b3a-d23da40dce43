{"version": 3, "names": ["cov_1nx6d89e15", "actualCoverage", "exports", "setupSwagger", "swagger_jsdoc_1", "s", "__importDefault", "require", "swagger_ui_express_1", "environment_1", "swaggerDefinition", "openapi", "info", "title", "version", "description", "contact", "name", "email", "url", "license", "servers", "config", "NODE_ENV", "b", "PORT", "components", "securitySchemes", "<PERSON><PERSON><PERSON>", "type", "scheme", "bearerFormat", "schemas", "Error", "properties", "success", "example", "error", "code", "timestamp", "format", "Success", "message", "data", "User", "_id", "firstName", "lastName", "phoneNumber", "role", "enum", "emailVerified", "isActive", "createdAt", "updatedAt", "Property", "price", "currency", "location", "state", "lga", "address", "coordinates", "latitude", "longitude", "amenities", "items", "images", "owner", "status", "Notification", "userId", "priority", "read", "dismissed", "PaginationResponse", "page", "limit", "total", "pages", "hasMore", "responses", "UnauthorizedError", "content", "schema", "$ref", "ForbiddenError", "NotFoundError", "ValidationError", "RateLimitError", "allOf", "retryAfter", "security", "tags", "swaggerOptions", "definition", "apis", "swaggerSpec", "default", "customCss", "swaggerUiOptions", "customSiteTitle", "customfavIcon", "persistAuthorization", "displayRequestDuration", "docExpansion", "filter", "showExtensions", "showCommonExtensions", "tryItOutEnabled", "app", "f", "use", "serve", "setup", "get", "req", "res", "<PERSON><PERSON><PERSON><PERSON>", "send", "console", "log"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\swagger.ts"], "sourcesContent": ["import swaggerJsdoc from 'swagger-jsdoc';\r\nimport swaggerUi from 'swagger-ui-express';\r\nimport { Express } from 'express';\r\nimport { config } from './environment';\r\n\r\n// Swagger definition\r\nconst swaggerDefinition = {\r\n  openapi: '3.0.0',\r\n  info: {\r\n    title: 'LajoSpaces API',\r\n    version: '1.0.0',\r\n    description: `\r\n      LajoSpaces is Nigeria's premier housing platform connecting property seekers with property owners and roommates.\r\n      \r\n      ## Features\r\n      - **User Management**: Registration, authentication, and profile management\r\n      - **Property Management**: Property listings, search, and favorites\r\n      - **Roommate Matching**: Smart matching system for compatible roommates\r\n      - **Messaging System**: Real-time communication between users\r\n      - **Email & Notifications**: Comprehensive notification system\r\n      - **Security & Performance**: Rate limiting, input validation, and caching\r\n      \r\n      ## Authentication\r\n      Most endpoints require JWT authentication. Include the token in the Authorization header:\r\n      \\`Authorization: Bearer <your-jwt-token>\\`\r\n      \r\n      ## Rate Limiting\r\n      API endpoints are rate-limited to ensure fair usage:\r\n      - General endpoints: 1000 requests per 15 minutes\r\n      - Authentication: 20 requests per 15 minutes\r\n      - Password reset: 5 requests per hour\r\n      - Email sending: 50 requests per hour\r\n      \r\n      ## Nigerian Market Focus\r\n      This API is optimized for the Nigerian market with:\r\n      - Nigerian phone number validation (+234 format)\r\n      - Nigerian states and LGAs\r\n      - Naira currency support\r\n      - Africa/Lagos timezone\r\n    `,\r\n    contact: {\r\n      name: 'LajoSpaces Support',\r\n      email: '<EMAIL>',\r\n      url: 'https://lajospaces.com'\r\n    },\r\n    license: {\r\n      name: 'MIT',\r\n      url: 'https://opensource.org/licenses/MIT'\r\n    }\r\n  },\r\n  servers: [\r\n    {\r\n      url: config.NODE_ENV === 'production' ? 'https://api.lajospaces.com' : `http://localhost:${config.PORT}`,\r\n      description: config.NODE_ENV === 'production' ? 'Production server' : 'Development server'\r\n    }\r\n  ],\r\n  components: {\r\n    securitySchemes: {\r\n      bearerAuth: {\r\n        type: 'http',\r\n        scheme: 'bearer',\r\n        bearerFormat: 'JWT',\r\n        description: 'JWT token obtained from login endpoint'\r\n      }\r\n    },\r\n    schemas: {\r\n      Error: {\r\n        type: 'object',\r\n        properties: {\r\n          success: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          error: {\r\n            type: 'string',\r\n            example: 'Error message'\r\n          },\r\n          code: {\r\n            type: 'string',\r\n            example: 'ERROR_CODE'\r\n          },\r\n          timestamp: {\r\n            type: 'string',\r\n            format: 'date-time',\r\n            example: '2024-01-01T00:00:00.000Z'\r\n          }\r\n        }\r\n      },\r\n      Success: {\r\n        type: 'object',\r\n        properties: {\r\n          success: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          message: {\r\n            type: 'string',\r\n            example: 'Operation completed successfully'\r\n          },\r\n          data: {\r\n            type: 'object',\r\n            description: 'Response data'\r\n          }\r\n        }\r\n      },\r\n      User: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          firstName: {\r\n            type: 'string',\r\n            example: 'John'\r\n          },\r\n          lastName: {\r\n            type: 'string',\r\n            example: 'Doe'\r\n          },\r\n          email: {\r\n            type: 'string',\r\n            format: 'email',\r\n            example: '<EMAIL>'\r\n          },\r\n          phoneNumber: {\r\n            type: 'string',\r\n            example: '+2348012345678'\r\n          },\r\n          role: {\r\n            type: 'string',\r\n            enum: ['user', 'admin'],\r\n            example: 'user'\r\n          },\r\n          emailVerified: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          isActive: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          },\r\n          updatedAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      Property: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          title: {\r\n            type: 'string',\r\n            example: 'Beautiful 2-Bedroom Apartment'\r\n          },\r\n          description: {\r\n            type: 'string',\r\n            example: 'Spacious apartment in a serene environment'\r\n          },\r\n          type: {\r\n            type: 'string',\r\n            enum: ['apartment', 'house', 'room', 'studio', 'duplex', 'bungalow'],\r\n            example: 'apartment'\r\n          },\r\n          price: {\r\n            type: 'number',\r\n            example: 120000\r\n          },\r\n          currency: {\r\n            type: 'string',\r\n            example: 'NGN'\r\n          },\r\n          location: {\r\n            type: 'object',\r\n            properties: {\r\n              state: {\r\n                type: 'string',\r\n                example: 'Lagos'\r\n              },\r\n              lga: {\r\n                type: 'string',\r\n                example: 'Victoria Island'\r\n              },\r\n              address: {\r\n                type: 'string',\r\n                example: '123 Ahmadu Bello Way'\r\n              },\r\n              coordinates: {\r\n                type: 'object',\r\n                properties: {\r\n                  latitude: {\r\n                    type: 'number',\r\n                    example: 6.4281\r\n                  },\r\n                  longitude: {\r\n                    type: 'number',\r\n                    example: 3.4219\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          amenities: {\r\n            type: 'array',\r\n            items: {\r\n              type: 'string'\r\n            },\r\n            example: ['parking', 'security', 'generator', 'water']\r\n          },\r\n          images: {\r\n            type: 'array',\r\n            items: {\r\n              type: 'string',\r\n              format: 'uri'\r\n            }\r\n          },\r\n          owner: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          status: {\r\n            type: 'string',\r\n            enum: ['available', 'rented', 'pending'],\r\n            example: 'available'\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      Notification: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          userId: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          type: {\r\n            type: 'string',\r\n            enum: ['welcome', 'email_verified', 'new_match', 'new_message', 'property_posted'],\r\n            example: 'new_match'\r\n          },\r\n          title: {\r\n            type: 'string',\r\n            example: 'New Roommate Match Found!'\r\n          },\r\n          message: {\r\n            type: 'string',\r\n            example: 'We found a potential roommate match for you.'\r\n          },\r\n          priority: {\r\n            type: 'string',\r\n            enum: ['low', 'medium', 'high', 'urgent'],\r\n            example: 'high'\r\n          },\r\n          read: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          dismissed: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      PaginationResponse: {\r\n        type: 'object',\r\n        properties: {\r\n          page: {\r\n            type: 'number',\r\n            example: 1\r\n          },\r\n          limit: {\r\n            type: 'number',\r\n            example: 20\r\n          },\r\n          total: {\r\n            type: 'number',\r\n            example: 100\r\n          },\r\n          pages: {\r\n            type: 'number',\r\n            example: 5\r\n          },\r\n          hasMore: {\r\n            type: 'boolean',\r\n            example: true\r\n          }\r\n        }\r\n      }\r\n    },\r\n    responses: {\r\n      UnauthorizedError: {\r\n        description: 'Authentication required',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Authentication required',\r\n              code: 'UNAUTHORIZED',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      ForbiddenError: {\r\n        description: 'Insufficient permissions',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Insufficient permissions',\r\n              code: 'FORBIDDEN',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      NotFoundError: {\r\n        description: 'Resource not found',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Resource not found',\r\n              code: 'NOT_FOUND',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      ValidationError: {\r\n        description: 'Validation error',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Validation failed',\r\n              code: 'VALIDATION_ERROR',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      RateLimitError: {\r\n        description: 'Rate limit exceeded',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              allOf: [\r\n                { $ref: '#/components/schemas/Error' },\r\n                {\r\n                  type: 'object',\r\n                  properties: {\r\n                    retryAfter: {\r\n                      type: 'string',\r\n                      example: '15 minutes'\r\n                    }\r\n                  }\r\n                }\r\n              ]\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Too many requests, please try again later',\r\n              retryAfter: '15 minutes',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  security: [\r\n    {\r\n      bearerAuth: []\r\n    }\r\n  ],\r\n  tags: [\r\n    {\r\n      name: 'Authentication',\r\n      description: 'User authentication and authorization'\r\n    },\r\n    {\r\n      name: 'Users',\r\n      description: 'User management operations'\r\n    },\r\n    {\r\n      name: 'Properties',\r\n      description: 'Property listing and management'\r\n    },\r\n    {\r\n      name: 'Search',\r\n      description: 'Property and user search functionality'\r\n    },\r\n    {\r\n      name: 'Matches',\r\n      description: 'Roommate matching system'\r\n    },\r\n    {\r\n      name: 'Messages',\r\n      description: 'Messaging and communication'\r\n    },\r\n    {\r\n      name: 'Notifications',\r\n      description: 'Notification management'\r\n    },\r\n    {\r\n      name: 'Emails',\r\n      description: 'Email services and templates'\r\n    },\r\n    {\r\n      name: 'Uploads',\r\n      description: 'File upload operations'\r\n    },\r\n    {\r\n      name: 'Admin',\r\n      description: 'Administrative operations'\r\n    }\r\n  ]\r\n};\r\n\r\n// Options for swagger-jsdoc\r\nconst swaggerOptions = {\r\n  definition: swaggerDefinition,\r\n  apis: [\r\n    './src/routes/*.ts',\r\n    './src/controllers/*.ts',\r\n    './src/models/*.ts'\r\n  ]\r\n};\r\n\r\n// Generate swagger specification\r\nconst swaggerSpec = swaggerJsdoc(swaggerOptions);\r\n\r\n// Custom CSS for Swagger UI\r\nconst customCss = `\r\n  .swagger-ui .topbar { display: none; }\r\n  .swagger-ui .info .title { color: #2563eb; }\r\n  .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; }\r\n  .swagger-ui .info .description { font-size: 14px; line-height: 1.6; }\r\n  .swagger-ui .btn.authorize { background-color: #2563eb; border-color: #2563eb; }\r\n  .swagger-ui .btn.authorize:hover { background-color: #1d4ed8; border-color: #1d4ed8; }\r\n`;\r\n\r\n// Swagger UI options\r\nconst swaggerUiOptions = {\r\n  customCss,\r\n  customSiteTitle: 'LajoSpaces API Documentation',\r\n  customfavIcon: '/favicon.ico',\r\n  swaggerOptions: {\r\n    persistAuthorization: true,\r\n    displayRequestDuration: true,\r\n    docExpansion: 'none',\r\n    filter: true,\r\n    showExtensions: true,\r\n    showCommonExtensions: true,\r\n    tryItOutEnabled: true\r\n  }\r\n};\r\n\r\n/**\r\n * Setup Swagger documentation\r\n */\r\nexport function setupSwagger(app: Express): void {\r\n  // Serve swagger documentation\r\n  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));\r\n  \r\n  // Serve swagger JSON\r\n  app.get('/api/docs.json', (req, res) => {\r\n    res.setHeader('Content-Type', 'application/json');\r\n    res.send(swaggerSpec);\r\n  });\r\n\r\n  console.log(`📚 Swagger documentation available at: http://localhost:${config.PORT}/api/docs`);\r\n}\r\n\r\nexport { swaggerSpec };\r\nexport default { setupSwagger, swaggerSpec };\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUI;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmeJE,OAAA,CAAAC,YAAA,GAAAA,YAAA;AA7eA,MAAAC,eAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,oBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAK,CAAA,OAAAC,eAAA,CAAAC,OAAA;AAEA,MAAAE,aAAA;AAAA;AAAA,CAAAT,cAAA,GAAAK,CAAA,OAAAE,OAAA;AAEA;AACA,MAAMG,iBAAiB;AAAA;AAAA,CAAAV,cAAA,GAAAK,CAAA,OAAG;EACxBM,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;IACJC,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BZ;IACDC,OAAO,EAAE;MACPC,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,wBAAwB;MAC/BC,GAAG,EAAE;KACN;IACDC,OAAO,EAAE;MACPH,IAAI,EAAE,KAAK;MACXE,GAAG,EAAE;;GAER;EACDE,OAAO,EAAE,CACP;IACEF,GAAG,EAAEV,aAAA,CAAAa,MAAM,CAACC,QAAQ,KAAK,YAAY;IAAA;IAAA,CAAAvB,cAAA,GAAAwB,CAAA,UAAG,4BAA4B;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAG,oBAAoBf,aAAA,CAAAa,MAAM,CAACG,IAAI,EAAE;IACxGV,WAAW,EAAEN,aAAA,CAAAa,MAAM,CAACC,QAAQ,KAAK,YAAY;IAAA;IAAA,CAAAvB,cAAA,GAAAwB,CAAA,UAAG,mBAAmB;IAAA;IAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAG,oBAAoB;GAC3F,CACF;EACDE,UAAU,EAAE;IACVC,eAAe,EAAE;MACfC,UAAU,EAAE;QACVC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBhB,WAAW,EAAE;;KAEhB;IACDiB,OAAO,EAAE;MACPC,KAAK,EAAE;QACLJ,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACVC,OAAO,EAAE;YACPN,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDC,KAAK,EAAE;YACLR,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDE,IAAI,EAAE;YACJT,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDG,SAAS,EAAE;YACTV,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE,WAAW;YACnBJ,OAAO,EAAE;;;OAGd;MACDK,OAAO,EAAE;QACPZ,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACVC,OAAO,EAAE;YACPN,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDM,OAAO,EAAE;YACPb,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDO,IAAI,EAAE;YACJd,IAAI,EAAE,QAAQ;YACdd,WAAW,EAAE;;;OAGlB;MACD6B,IAAI,EAAE;QACJf,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACVW,GAAG,EAAE;YACHhB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDU,SAAS,EAAE;YACTjB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDW,QAAQ,EAAE;YACRlB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDlB,KAAK,EAAE;YACLW,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE,OAAO;YACfJ,OAAO,EAAE;WACV;UACDY,WAAW,EAAE;YACXnB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDa,IAAI,EAAE;YACJpB,IAAI,EAAE,QAAQ;YACdqB,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;YACvBd,OAAO,EAAE;WACV;UACDe,aAAa,EAAE;YACbtB,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDgB,QAAQ,EAAE;YACRvB,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDiB,SAAS,EAAE;YACTxB,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE;WACT;UACDc,SAAS,EAAE;YACTzB,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE;;;OAGb;MACDe,QAAQ,EAAE;QACR1B,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACVW,GAAG,EAAE;YACHhB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDvB,KAAK,EAAE;YACLgB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDrB,WAAW,EAAE;YACXc,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDP,IAAI,EAAE;YACJA,IAAI,EAAE,QAAQ;YACdqB,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;YACpEd,OAAO,EAAE;WACV;UACDoB,KAAK,EAAE;YACL3B,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDqB,QAAQ,EAAE;YACR5B,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDsB,QAAQ,EAAE;YACR7B,IAAI,EAAE,QAAQ;YACdK,UAAU,EAAE;cACVyB,KAAK,EAAE;gBACL9B,IAAI,EAAE,QAAQ;gBACdO,OAAO,EAAE;eACV;cACDwB,GAAG,EAAE;gBACH/B,IAAI,EAAE,QAAQ;gBACdO,OAAO,EAAE;eACV;cACDyB,OAAO,EAAE;gBACPhC,IAAI,EAAE,QAAQ;gBACdO,OAAO,EAAE;eACV;cACD0B,WAAW,EAAE;gBACXjC,IAAI,EAAE,QAAQ;gBACdK,UAAU,EAAE;kBACV6B,QAAQ,EAAE;oBACRlC,IAAI,EAAE,QAAQ;oBACdO,OAAO,EAAE;mBACV;kBACD4B,SAAS,EAAE;oBACTnC,IAAI,EAAE,QAAQ;oBACdO,OAAO,EAAE;;;;;WAKlB;UACD6B,SAAS,EAAE;YACTpC,IAAI,EAAE,OAAO;YACbqC,KAAK,EAAE;cACLrC,IAAI,EAAE;aACP;YACDO,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO;WACtD;UACD+B,MAAM,EAAE;YACNtC,IAAI,EAAE,OAAO;YACbqC,KAAK,EAAE;cACLrC,IAAI,EAAE,QAAQ;cACdW,MAAM,EAAE;;WAEX;UACD4B,KAAK,EAAE;YACLvC,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDiC,MAAM,EAAE;YACNxC,IAAI,EAAE,QAAQ;YACdqB,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;YACxCd,OAAO,EAAE;WACV;UACDiB,SAAS,EAAE;YACTxB,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE;;;OAGb;MACD8B,YAAY,EAAE;QACZzC,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACVW,GAAG,EAAE;YACHhB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDmC,MAAM,EAAE;YACN1C,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDP,IAAI,EAAE;YACJA,IAAI,EAAE,QAAQ;YACdqB,IAAI,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,iBAAiB,CAAC;YAClFd,OAAO,EAAE;WACV;UACDvB,KAAK,EAAE;YACLgB,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDM,OAAO,EAAE;YACPb,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDoC,QAAQ,EAAE;YACR3C,IAAI,EAAE,QAAQ;YACdqB,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;YACzCd,OAAO,EAAE;WACV;UACDqC,IAAI,EAAE;YACJ5C,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDsC,SAAS,EAAE;YACT7C,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;WACV;UACDiB,SAAS,EAAE;YACTxB,IAAI,EAAE,QAAQ;YACdW,MAAM,EAAE;;;OAGb;MACDmC,kBAAkB,EAAE;QAClB9C,IAAI,EAAE,QAAQ;QACdK,UAAU,EAAE;UACV0C,IAAI,EAAE;YACJ/C,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACDyC,KAAK,EAAE;YACLhD,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACD0C,KAAK,EAAE;YACLjD,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACD2C,KAAK,EAAE;YACLlD,IAAI,EAAE,QAAQ;YACdO,OAAO,EAAE;WACV;UACD4C,OAAO,EAAE;YACPnD,IAAI,EAAE,SAAS;YACfO,OAAO,EAAE;;;;KAIhB;IACD6C,SAAS,EAAE;MACTC,iBAAiB,EAAE;QACjBnE,WAAW,EAAE,yBAAyB;QACtCoE,OAAO,EAAE;UACP,kBAAkB,EAAE;YAClBC,MAAM,EAAE;cACNC,IAAI,EAAE;aACP;YACDjD,OAAO,EAAE;cACPD,OAAO,EAAE,KAAK;cACdE,KAAK,EAAE,yBAAyB;cAChCC,IAAI,EAAE,cAAc;cACpBC,SAAS,EAAE;;;;OAIlB;MACD+C,cAAc,EAAE;QACdvE,WAAW,EAAE,0BAA0B;QACvCoE,OAAO,EAAE;UACP,kBAAkB,EAAE;YAClBC,MAAM,EAAE;cACNC,IAAI,EAAE;aACP;YACDjD,OAAO,EAAE;cACPD,OAAO,EAAE,KAAK;cACdE,KAAK,EAAE,0BAA0B;cACjCC,IAAI,EAAE,WAAW;cACjBC,SAAS,EAAE;;;;OAIlB;MACDgD,aAAa,EAAE;QACbxE,WAAW,EAAE,oBAAoB;QACjCoE,OAAO,EAAE;UACP,kBAAkB,EAAE;YAClBC,MAAM,EAAE;cACNC,IAAI,EAAE;aACP;YACDjD,OAAO,EAAE;cACPD,OAAO,EAAE,KAAK;cACdE,KAAK,EAAE,oBAAoB;cAC3BC,IAAI,EAAE,WAAW;cACjBC,SAAS,EAAE;;;;OAIlB;MACDiD,eAAe,EAAE;QACfzE,WAAW,EAAE,kBAAkB;QAC/BoE,OAAO,EAAE;UACP,kBAAkB,EAAE;YAClBC,MAAM,EAAE;cACNC,IAAI,EAAE;aACP;YACDjD,OAAO,EAAE;cACPD,OAAO,EAAE,KAAK;cACdE,KAAK,EAAE,mBAAmB;cAC1BC,IAAI,EAAE,kBAAkB;cACxBC,SAAS,EAAE;;;;OAIlB;MACDkD,cAAc,EAAE;QACd1E,WAAW,EAAE,qBAAqB;QAClCoE,OAAO,EAAE;UACP,kBAAkB,EAAE;YAClBC,MAAM,EAAE;cACNM,KAAK,EAAE,CACL;gBAAEL,IAAI,EAAE;cAA4B,CAAE,EACtC;gBACExD,IAAI,EAAE,QAAQ;gBACdK,UAAU,EAAE;kBACVyD,UAAU,EAAE;oBACV9D,IAAI,EAAE,QAAQ;oBACdO,OAAO,EAAE;;;eAGd;aAEJ;YACDA,OAAO,EAAE;cACPD,OAAO,EAAE,KAAK;cACdE,KAAK,EAAE,2CAA2C;cAClDsD,UAAU,EAAE,YAAY;cACxBpD,SAAS,EAAE;;;;;;GAMtB;EACDqD,QAAQ,EAAE,CACR;IACEhE,UAAU,EAAE;GACb,CACF;EACDiE,IAAI,EAAE,CACJ;IACE5E,IAAI,EAAE,gBAAgB;IACtBF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,OAAO;IACbF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,YAAY;IAClBF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,QAAQ;IACdF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,SAAS;IACfF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,UAAU;IAChBF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,eAAe;IACrBF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,QAAQ;IACdF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,SAAS;IACfF,WAAW,EAAE;GACd,EACD;IACEE,IAAI,EAAE,OAAO;IACbF,WAAW,EAAE;GACd;CAEJ;AAED;AACA,MAAM+E,cAAc;AAAA;AAAA,CAAA9F,cAAA,GAAAK,CAAA,OAAG;EACrB0F,UAAU,EAAErF,iBAAiB;EAC7BsF,IAAI,EAAE,CACJ,mBAAmB,EACnB,wBAAwB,EACxB,mBAAmB;CAEtB;AAED;AACA,MAAMC,WAAW;AAAA;AAAA,CAAAjG,cAAA,GAAAK,CAAA,QAAG,IAAAD,eAAA,CAAA8F,OAAY,EAACJ,cAAc,CAAC;AAAC;AAAA9F,cAAA,GAAAK,CAAA;AA4CxCH,OAAA,CAAA+F,WAAA,GAAAA,WAAA;AA1CT;AACA,MAAME,SAAS;AAAA;AAAA,CAAAnG,cAAA,GAAAK,CAAA,QAAG;;;;;;;CAOjB;AAED;AACA,MAAM+F,gBAAgB;AAAA;AAAA,CAAApG,cAAA,GAAAK,CAAA,QAAG;EACvB8F,SAAS;EACTE,eAAe,EAAE,8BAA8B;EAC/CC,aAAa,EAAE,cAAc;EAC7BR,cAAc,EAAE;IACdS,oBAAoB,EAAE,IAAI;IAC1BC,sBAAsB,EAAE,IAAI;IAC5BC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE,IAAI;IACZC,cAAc,EAAE,IAAI;IACpBC,oBAAoB,EAAE,IAAI;IAC1BC,eAAe,EAAE;;CAEpB;AAED;;;AAGA,SAAgB1G,YAAYA,CAAC2G,GAAY;EAAA;EAAA9G,cAAA,GAAA+G,CAAA;EAAA/G,cAAA,GAAAK,CAAA;EACvC;EACAyG,GAAG,CAACE,GAAG,CAAC,WAAW,EAAExG,oBAAA,CAAA0F,OAAS,CAACe,KAAK,EAAEzG,oBAAA,CAAA0F,OAAS,CAACgB,KAAK,CAACjB,WAAW,EAAEG,gBAAgB,CAAC,CAAC;EAErF;EAAA;EAAApG,cAAA,GAAAK,CAAA;EACAyG,GAAG,CAACK,GAAG,CAAC,gBAAgB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAI;IAAA;IAAArH,cAAA,GAAA+G,CAAA;IAAA/G,cAAA,GAAAK,CAAA;IACrCgH,GAAG,CAACC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAAC;IAAAtH,cAAA,GAAAK,CAAA;IAClDgH,GAAG,CAACE,IAAI,CAACtB,WAAW,CAAC;EACvB,CAAC,CAAC;EAAC;EAAAjG,cAAA,GAAAK,CAAA;EAEHmH,OAAO,CAACC,GAAG,CAAC,2DAA2DhH,aAAA,CAAAa,MAAM,CAACG,IAAI,WAAW,CAAC;AAChG;AAAC;AAAAzB,cAAA,GAAAK,CAAA;AAGDH,OAAA,CAAAgG,OAAA,GAAe;EAAE/F,YAAY;EAAE8F;AAAW,CAAE", "ignoreList": []}