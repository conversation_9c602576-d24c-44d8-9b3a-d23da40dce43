33214ab717c111481624e4f5e943b874
"use strict";

/* istanbul ignore next */
function cov_o4uw83gxa() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\email.routes.ts";
  var hash = "4a3f1054b551d1d094647bced4d9717765bb94eb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\email.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 27
        },
        end: {
          line: 4,
          column: 69
        }
      },
      "3": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 5,
          column: 44
        }
      },
      "4": {
        start: {
          line: 6,
          column: 21
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "5": {
        start: {
          line: 7,
          column: 27
        },
        end: {
          line: 7,
          column: 68
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 188
        }
      },
      "8": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 171
        }
      },
      "9": {
        start: {
          line: 26,
          column: 0
        },
        end: {
          line: 26,
          column: 204
        }
      },
      "10": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 32,
          column: 113
        }
      },
      "11": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 119
        }
      },
      "12": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 84
        }
      },
      "13": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 185
        }
      },
      "14": {
        start: {
          line: 56,
          column: 0
        },
        end: {
          line: 63,
          column: 3
        }
      },
      "15": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 62,
          column: 7
        }
      },
      "16": {
        start: {
          line: 64,
          column: 0
        },
        end: {
          line: 64,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 56,
            column: 22
          },
          end: {
            line: 56,
            column: 23
          }
        },
        loc: {
          start: {
            line: 56,
            column: 36
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 56
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\email.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,sEAQyC;AACzC,6CAA+D;AAC/D,yDAA2D;AAC3D,qEAKwC;AAExC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,oBAAoB,EACpB,mBAAY,EACZ,IAAA,4BAAe,EAAC,8CAA2B,EAAE,MAAM,CAAC,EACpD,wCAAqB,CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,sBAAsB,EACtB,IAAA,4BAAe,EAAC,+CAA4B,EAAE,MAAM,CAAC,EACrD,yCAAsB,CACvB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,cAAc,EACd,mBAAY,EACZ,IAAA,kBAAW,EAAC,OAAO,CAAC,EACpB,IAAA,4BAAe,EAAC,wCAAqB,EAAE,MAAM,CAAC,EAC9C,kCAAe,CAChB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,OAAO,EACP,mBAAY,EACZ,IAAA,kBAAW,EAAC,OAAO,CAAC,EACpB,mCAAgB,CACjB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,SAAS,EACT,mBAAY,EACZ,IAAA,kBAAW,EAAC,OAAO,CAAC,EACpB,wCAAqB,CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CACR,YAAY,EACZ,mBAAY,EACZ,oCAAiB,CAClB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAI,CACT,mBAAmB,EACnB,mBAAY,EACZ,IAAA,4BAAe,EAAC,6CAA0B,EAAE,MAAM,CAAC,EACnD,uCAAoB,CACrB,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,OAAO,EAAE,WAAW;KACrB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\email.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport {\r\n  sendVerificationEmail,\r\n  sendPasswordResetEmail,\r\n  sendCustomEmail,\r\n  testEmailService,\r\n  getEmailServiceStatus,\r\n  getEmailTemplates,\r\n  previewEmailTemplate\r\n} from '../controllers/email.controller';\r\nimport { authenticate, requireRole } from '../middleware/auth';\r\nimport { validateRequest } from '../middleware/validation';\r\nimport {\r\n  sendVerificationEmailSchema,\r\n  sendPasswordResetEmailSchema,\r\n  sendCustomEmailSchema,\r\n  previewEmailTemplateSchema\r\n} from '../validators/email.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   POST /api/emails/send-verification\r\n * @desc    Send email verification\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/send-verification',\r\n  authenticate,\r\n  validateRequest(sendVerificationEmailSchema, 'body'),\r\n  sendVerificationEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/send-password-reset\r\n * @desc    Send password reset email\r\n * @access  Public\r\n */\r\nrouter.post(\r\n  '/send-password-reset',\r\n  validateRequest(sendPasswordResetEmailSchema, 'body'),\r\n  sendPasswordResetEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/send-custom\r\n * @desc    Send custom email (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.post(\r\n  '/send-custom',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  validateRequest(sendCustomEmailSchema, 'body'),\r\n  sendCustomEmail\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/test\r\n * @desc    Test email service (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.post(\r\n  '/test',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  testEmailService\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/status\r\n * @desc    Get email service status (admin only)\r\n * @access  Private (Admin)\r\n */\r\nrouter.get(\r\n  '/status',\r\n  authenticate,\r\n  requireRole('admin'),\r\n  getEmailServiceStatus\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/templates\r\n * @desc    Get available email templates\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/templates',\r\n  authenticate,\r\n  getEmailTemplates\r\n);\r\n\r\n/**\r\n * @route   POST /api/emails/preview-template\r\n * @desc    Preview email template\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preview-template',\r\n  authenticate,\r\n  validateRequest(previewEmailTemplateSchema, 'body'),\r\n  previewEmailTemplate\r\n);\r\n\r\n/**\r\n * @route   GET /api/emails/health\r\n * @desc    Health check for email service\r\n * @access  Public\r\n */\r\nrouter.get('/health', (req, res) => {\r\n  res.json({\r\n    success: true,\r\n    message: 'Email service is healthy',\r\n    timestamp: new Date().toISOString(),\r\n    service: 'Zoho SMTP'\r\n  });\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4a3f1054b551d1d094647bced4d9717765bb94eb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_o4uw83gxa = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_o4uw83gxa();
cov_o4uw83gxa().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_o4uw83gxa().s[1]++, require("express"));
const email_controller_1 =
/* istanbul ignore next */
(cov_o4uw83gxa().s[2]++, require("../controllers/email.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_o4uw83gxa().s[3]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_o4uw83gxa().s[4]++, require("../middleware/validation"));
const email_validators_1 =
/* istanbul ignore next */
(cov_o4uw83gxa().s[5]++, require("../validators/email.validators"));
const router =
/* istanbul ignore next */
(cov_o4uw83gxa().s[6]++, (0, express_1.Router)());
/**
 * @route   POST /api/emails/send-verification
 * @desc    Send email verification
 * @access  Private
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[7]++;
router.post('/send-verification', auth_1.authenticate, (0, validation_1.validateRequest)(email_validators_1.sendVerificationEmailSchema, 'body'), email_controller_1.sendVerificationEmail);
/**
 * @route   POST /api/emails/send-password-reset
 * @desc    Send password reset email
 * @access  Public
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[8]++;
router.post('/send-password-reset', (0, validation_1.validateRequest)(email_validators_1.sendPasswordResetEmailSchema, 'body'), email_controller_1.sendPasswordResetEmail);
/**
 * @route   POST /api/emails/send-custom
 * @desc    Send custom email (admin only)
 * @access  Private (Admin)
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[9]++;
router.post('/send-custom', auth_1.authenticate, (0, auth_1.requireRole)('admin'), (0, validation_1.validateRequest)(email_validators_1.sendCustomEmailSchema, 'body'), email_controller_1.sendCustomEmail);
/**
 * @route   POST /api/emails/test
 * @desc    Test email service (admin only)
 * @access  Private (Admin)
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[10]++;
router.post('/test', auth_1.authenticate, (0, auth_1.requireRole)('admin'), email_controller_1.testEmailService);
/**
 * @route   GET /api/emails/status
 * @desc    Get email service status (admin only)
 * @access  Private (Admin)
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[11]++;
router.get('/status', auth_1.authenticate, (0, auth_1.requireRole)('admin'), email_controller_1.getEmailServiceStatus);
/**
 * @route   GET /api/emails/templates
 * @desc    Get available email templates
 * @access  Private
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[12]++;
router.get('/templates', auth_1.authenticate, email_controller_1.getEmailTemplates);
/**
 * @route   POST /api/emails/preview-template
 * @desc    Preview email template
 * @access  Private
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[13]++;
router.post('/preview-template', auth_1.authenticate, (0, validation_1.validateRequest)(email_validators_1.previewEmailTemplateSchema, 'body'), email_controller_1.previewEmailTemplate);
/**
 * @route   GET /api/emails/health
 * @desc    Health check for email service
 * @access  Public
 */
/* istanbul ignore next */
cov_o4uw83gxa().s[14]++;
router.get('/health', (req, res) => {
  /* istanbul ignore next */
  cov_o4uw83gxa().f[0]++;
  cov_o4uw83gxa().s[15]++;
  res.json({
    success: true,
    message: 'Email service is healthy',
    timestamp: new Date().toISOString(),
    service: 'Zoho SMTP'
  });
});
/* istanbul ignore next */
cov_o4uw83gxa().s[16]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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