83c7dec1591978de978ae88d5e6734d1
"use strict";

/* istanbul ignore next */
function cov_1nx6d89e15() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\swagger.ts";
  var hash = "ccb3141595d32e1eaa0b41fbeb73a9ddd2fac607";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\swagger.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 29
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 36
        }
      },
      "5": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 65
        }
      },
      "6": {
        start: {
          line: 9,
          column: 29
        },
        end: {
          line: 9,
          column: 75
        }
      },
      "7": {
        start: {
          line: 10,
          column: 22
        },
        end: {
          line: 10,
          column: 46
        }
      },
      "8": {
        start: {
          line: 12,
          column: 26
        },
        end: {
          line: 455,
          column: 1
        }
      },
      "9": {
        start: {
          line: 457,
          column: 23
        },
        end: {
          line: 464,
          column: 1
        }
      },
      "10": {
        start: {
          line: 466,
          column: 20
        },
        end: {
          line: 466,
          column: 64
        }
      },
      "11": {
        start: {
          line: 467,
          column: 0
        },
        end: {
          line: 467,
          column: 34
        }
      },
      "12": {
        start: {
          line: 469,
          column: 18
        },
        end: {
          line: 476,
          column: 1
        }
      },
      "13": {
        start: {
          line: 478,
          column: 25
        },
        end: {
          line: 491,
          column: 1
        }
      },
      "14": {
        start: {
          line: 497,
          column: 4
        },
        end: {
          line: 497,
          column: 128
        }
      },
      "15": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 502,
          column: 7
        }
      },
      "16": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 58
        }
      },
      "17": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 501,
          column: 30
        }
      },
      "18": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 503,
          column: 113
        }
      },
      "19": {
        start: {
          line: 505,
          column: 0
        },
        end: {
          line: 505,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "setupSwagger",
        decl: {
          start: {
            line: 495,
            column: 9
          },
          end: {
            line: 495,
            column: 21
          }
        },
        loc: {
          start: {
            line: 495,
            column: 27
          },
          end: {
            line: 504,
            column: 1
          }
        },
        line: 495
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 499,
            column: 30
          },
          end: {
            line: 499,
            column: 31
          }
        },
        loc: {
          start: {
            line: 499,
            column: 44
          },
          end: {
            line: 502,
            column: 5
          }
        },
        line: 499
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 58,
            column: 17
          },
          end: {
            line: 58,
            column: 144
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 58,
            column: 66
          },
          end: {
            line: 58,
            column: 94
          }
        }, {
          start: {
            line: 58,
            column: 97
          },
          end: {
            line: 58,
            column: 144
          }
        }],
        line: 58
      },
      "4": {
        loc: {
          start: {
            line: 59,
            column: 25
          },
          end: {
            line: 59,
            column: 116
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 74
          },
          end: {
            line: 59,
            column: 93
          }
        }, {
          start: {
            line: 59,
            column: 96
          },
          end: {
            line: 59,
            column: 116
          }
        }],
        line: 59
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\swagger.ts",
      mappings: ";;;;;;AA6eA,oCAWC;AAxfD,kEAAyC;AACzC,4EAA2C;AAE3C,+CAAuC;AAEvC,qBAAqB;AACrB,MAAM,iBAAiB,GAAG;IACxB,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE;QACJ,KAAK,EAAE,gBAAgB;QACvB,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BZ;QACD,OAAO,EAAE;YACP,IAAI,EAAE,oBAAoB;YAC1B,KAAK,EAAE,wBAAwB;YAC/B,GAAG,EAAE,wBAAwB;SAC9B;QACD,OAAO,EAAE;YACP,IAAI,EAAE,KAAK;YACX,GAAG,EAAE,qCAAqC;SAC3C;KACF;IACD,OAAO,EAAE;QACP;YACE,GAAG,EAAE,oBAAM,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,oBAAoB,oBAAM,CAAC,IAAI,EAAE;YACxG,WAAW,EAAE,oBAAM,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,oBAAoB;SAC3F;KACF;IACD,UAAU,EAAE;QACV,eAAe,EAAE;YACf,UAAU,EAAE;gBACV,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,QAAQ;gBAChB,YAAY,EAAE,KAAK;gBACnB,WAAW,EAAE,wCAAwC;aACtD;SACF;QACD,OAAO,EAAE;YACP,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;qBACf;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,eAAe;qBACzB;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,YAAY;qBACtB;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,0BAA0B;qBACpC;iBACF;aACF;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;qBACd;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,kCAAkC;qBAC5C;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;aACF;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,0BAA0B;qBACpC;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM;qBAChB;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,KAAK;qBACf;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,OAAO;wBACf,OAAO,EAAE,sBAAsB;qBAChC;oBACD,WAAW,EAAE;wBACX,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,gBAAgB;qBAC1B;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;wBACvB,OAAO,EAAE,MAAM;qBAChB;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;qBACd;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;qBACd;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;qBACpB;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;qBACpB;iBACF;aACF;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,0BAA0B;qBACpC;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,+BAA+B;qBACzC;oBACD,WAAW,EAAE;wBACX,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,4CAA4C;qBACtD;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;wBACpE,OAAO,EAAE,WAAW;qBACrB;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,MAAM;qBAChB;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,KAAK;qBACf;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;gCACd,OAAO,EAAE,OAAO;6BACjB;4BACD,GAAG,EAAE;gCACH,IAAI,EAAE,QAAQ;gCACd,OAAO,EAAE,iBAAiB;6BAC3B;4BACD,OAAO,EAAE;gCACP,IAAI,EAAE,QAAQ;gCACd,OAAO,EAAE,sBAAsB;6BAChC;4BACD,WAAW,EAAE;gCACX,IAAI,EAAE,QAAQ;gCACd,UAAU,EAAE;oCACV,QAAQ,EAAE;wCACR,IAAI,EAAE,QAAQ;wCACd,OAAO,EAAE,MAAM;qCAChB;oCACD,SAAS,EAAE;wCACT,IAAI,EAAE,QAAQ;wCACd,OAAO,EAAE,MAAM;qCAChB;iCACF;6BACF;yBACF;qBACF;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;wBACD,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;qBACvD;oBACD,MAAM,EAAE;wBACN,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;4BACd,MAAM,EAAE,KAAK;yBACd;qBACF;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,0BAA0B;qBACpC;oBACD,MAAM,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;wBACxC,OAAO,EAAE,WAAW;qBACrB;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;qBACpB;iBACF;aACF;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,0BAA0B;qBACpC;oBACD,MAAM,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,0BAA0B;qBACpC;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,iBAAiB,CAAC;wBAClF,OAAO,EAAE,WAAW;qBACrB;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,2BAA2B;qBACrC;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,8CAA8C;qBACxD;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;wBACzC,OAAO,EAAE,MAAM;qBAChB;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;qBACf;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;qBACf;oBACD,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,WAAW;qBACpB;iBACF;aACF;YACD,kBAAkB,EAAE;gBAClB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,CAAC;qBACX;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,EAAE;qBACZ;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,GAAG;qBACb;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,CAAC;qBACX;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;qBACd;iBACF;aACF;SACF;QACD,SAAS,EAAE;YACT,iBAAiB,EAAE;gBACjB,WAAW,EAAE,yBAAyB;gBACtC,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE;4BACN,IAAI,EAAE,4BAA4B;yBACnC;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,yBAAyB;4BAChC,IAAI,EAAE,cAAc;4BACpB,SAAS,EAAE,0BAA0B;yBACtC;qBACF;iBACF;aACF;YACD,cAAc,EAAE;gBACd,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE;4BACN,IAAI,EAAE,4BAA4B;yBACnC;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,0BAA0B;4BACjC,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,0BAA0B;yBACtC;qBACF;iBACF;aACF;YACD,aAAa,EAAE;gBACb,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE;4BACN,IAAI,EAAE,4BAA4B;yBACnC;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,oBAAoB;4BAC3B,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,0BAA0B;yBACtC;qBACF;iBACF;aACF;YACD,eAAe,EAAE;gBACf,WAAW,EAAE,kBAAkB;gBAC/B,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE;4BACN,IAAI,EAAE,4BAA4B;yBACnC;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,mBAAmB;4BAC1B,IAAI,EAAE,kBAAkB;4BACxB,SAAS,EAAE,0BAA0B;yBACtC;qBACF;iBACF;aACF;YACD,cAAc,EAAE;gBACd,WAAW,EAAE,qBAAqB;gBAClC,OAAO,EAAE;oBACP,kBAAkB,EAAE;wBAClB,MAAM,EAAE;4BACN,KAAK,EAAE;gCACL,EAAE,IAAI,EAAE,4BAA4B,EAAE;gCACtC;oCACE,IAAI,EAAE,QAAQ;oCACd,UAAU,EAAE;wCACV,UAAU,EAAE;4CACV,IAAI,EAAE,QAAQ;4CACd,OAAO,EAAE,YAAY;yCACtB;qCACF;iCACF;6BACF;yBACF;wBACD,OAAO,EAAE;4BACP,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,2CAA2C;4BAClD,UAAU,EAAE,YAAY;4BACxB,SAAS,EAAE,0BAA0B;yBACtC;qBACF;iBACF;aACF;SACF;KACF;IACD,QAAQ,EAAE;QACR;YACE,UAAU,EAAE,EAAE;SACf;KACF;IACD,IAAI,EAAE;QACJ;YACE,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,uCAAuC;SACrD;QACD;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,4BAA4B;SAC1C;QACD;YACE,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,iCAAiC;SAC/C;QACD;YACE,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,wCAAwC;SACtD;QACD;YACE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,0BAA0B;SACxC;QACD;YACE,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,6BAA6B;SAC3C;QACD;YACE,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,yBAAyB;SACvC;QACD;YACE,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,8BAA8B;SAC5C;QACD;YACE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,wBAAwB;SACtC;QACD;YACE,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,2BAA2B;SACzC;KACF;CACF,CAAC;AAEF,4BAA4B;AAC5B,MAAM,cAAc,GAAG;IACrB,UAAU,EAAE,iBAAiB;IAC7B,IAAI,EAAE;QACJ,mBAAmB;QACnB,wBAAwB;QACxB,mBAAmB;KACpB;CACF,CAAC;AAEF,iCAAiC;AACjC,MAAM,WAAW,GAAG,IAAA,uBAAY,EAAC,cAAc,CAAC,CAAC;AA4CxC,kCAAW;AA1CpB,4BAA4B;AAC5B,MAAM,SAAS,GAAG;;;;;;;CAOjB,CAAC;AAEF,qBAAqB;AACrB,MAAM,gBAAgB,GAAG;IACvB,SAAS;IACT,eAAe,EAAE,8BAA8B;IAC/C,aAAa,EAAE,cAAc;IAC7B,cAAc,EAAE;QACd,oBAAoB,EAAE,IAAI;QAC1B,sBAAsB,EAAE,IAAI;QAC5B,YAAY,EAAE,MAAM;QACpB,MAAM,EAAE,IAAI;QACZ,cAAc,EAAE,IAAI;QACpB,oBAAoB,EAAE,IAAI;QAC1B,eAAe,EAAE,IAAI;KACtB;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,YAAY,CAAC,GAAY;IACvC,8BAA8B;IAC9B,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,4BAAS,CAAC,KAAK,EAAE,4BAAS,CAAC,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEtF,qBAAqB;IACrB,GAAG,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACrC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAClD,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,2DAA2D,oBAAM,CAAC,IAAI,WAAW,CAAC,CAAC;AACjG,CAAC;AAGD,kBAAe,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\swagger.ts"],
      sourcesContent: ["import swaggerJsdoc from 'swagger-jsdoc';\r\nimport swaggerUi from 'swagger-ui-express';\r\nimport { Express } from 'express';\r\nimport { config } from './environment';\r\n\r\n// Swagger definition\r\nconst swaggerDefinition = {\r\n  openapi: '3.0.0',\r\n  info: {\r\n    title: 'LajoSpaces API',\r\n    version: '1.0.0',\r\n    description: `\r\n      LajoSpaces is Nigeria's premier housing platform connecting property seekers with property owners and roommates.\r\n      \r\n      ## Features\r\n      - **User Management**: Registration, authentication, and profile management\r\n      - **Property Management**: Property listings, search, and favorites\r\n      - **Roommate Matching**: Smart matching system for compatible roommates\r\n      - **Messaging System**: Real-time communication between users\r\n      - **Email & Notifications**: Comprehensive notification system\r\n      - **Security & Performance**: Rate limiting, input validation, and caching\r\n      \r\n      ## Authentication\r\n      Most endpoints require JWT authentication. Include the token in the Authorization header:\r\n      \\`Authorization: Bearer <your-jwt-token>\\`\r\n      \r\n      ## Rate Limiting\r\n      API endpoints are rate-limited to ensure fair usage:\r\n      - General endpoints: 1000 requests per 15 minutes\r\n      - Authentication: 20 requests per 15 minutes\r\n      - Password reset: 5 requests per hour\r\n      - Email sending: 50 requests per hour\r\n      \r\n      ## Nigerian Market Focus\r\n      This API is optimized for the Nigerian market with:\r\n      - Nigerian phone number validation (+234 format)\r\n      - Nigerian states and LGAs\r\n      - Naira currency support\r\n      - Africa/Lagos timezone\r\n    `,\r\n    contact: {\r\n      name: 'LajoSpaces Support',\r\n      email: '<EMAIL>',\r\n      url: 'https://lajospaces.com'\r\n    },\r\n    license: {\r\n      name: 'MIT',\r\n      url: 'https://opensource.org/licenses/MIT'\r\n    }\r\n  },\r\n  servers: [\r\n    {\r\n      url: config.NODE_ENV === 'production' ? 'https://api.lajospaces.com' : `http://localhost:${config.PORT}`,\r\n      description: config.NODE_ENV === 'production' ? 'Production server' : 'Development server'\r\n    }\r\n  ],\r\n  components: {\r\n    securitySchemes: {\r\n      bearerAuth: {\r\n        type: 'http',\r\n        scheme: 'bearer',\r\n        bearerFormat: 'JWT',\r\n        description: 'JWT token obtained from login endpoint'\r\n      }\r\n    },\r\n    schemas: {\r\n      Error: {\r\n        type: 'object',\r\n        properties: {\r\n          success: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          error: {\r\n            type: 'string',\r\n            example: 'Error message'\r\n          },\r\n          code: {\r\n            type: 'string',\r\n            example: 'ERROR_CODE'\r\n          },\r\n          timestamp: {\r\n            type: 'string',\r\n            format: 'date-time',\r\n            example: '2024-01-01T00:00:00.000Z'\r\n          }\r\n        }\r\n      },\r\n      Success: {\r\n        type: 'object',\r\n        properties: {\r\n          success: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          message: {\r\n            type: 'string',\r\n            example: 'Operation completed successfully'\r\n          },\r\n          data: {\r\n            type: 'object',\r\n            description: 'Response data'\r\n          }\r\n        }\r\n      },\r\n      User: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          firstName: {\r\n            type: 'string',\r\n            example: 'John'\r\n          },\r\n          lastName: {\r\n            type: 'string',\r\n            example: 'Doe'\r\n          },\r\n          email: {\r\n            type: 'string',\r\n            format: 'email',\r\n            example: '<EMAIL>'\r\n          },\r\n          phoneNumber: {\r\n            type: 'string',\r\n            example: '+2348012345678'\r\n          },\r\n          role: {\r\n            type: 'string',\r\n            enum: ['user', 'admin'],\r\n            example: 'user'\r\n          },\r\n          emailVerified: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          isActive: {\r\n            type: 'boolean',\r\n            example: true\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          },\r\n          updatedAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      Property: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          title: {\r\n            type: 'string',\r\n            example: 'Beautiful 2-Bedroom Apartment'\r\n          },\r\n          description: {\r\n            type: 'string',\r\n            example: 'Spacious apartment in a serene environment'\r\n          },\r\n          type: {\r\n            type: 'string',\r\n            enum: ['apartment', 'house', 'room', 'studio', 'duplex', 'bungalow'],\r\n            example: 'apartment'\r\n          },\r\n          price: {\r\n            type: 'number',\r\n            example: 120000\r\n          },\r\n          currency: {\r\n            type: 'string',\r\n            example: 'NGN'\r\n          },\r\n          location: {\r\n            type: 'object',\r\n            properties: {\r\n              state: {\r\n                type: 'string',\r\n                example: 'Lagos'\r\n              },\r\n              lga: {\r\n                type: 'string',\r\n                example: 'Victoria Island'\r\n              },\r\n              address: {\r\n                type: 'string',\r\n                example: '123 Ahmadu Bello Way'\r\n              },\r\n              coordinates: {\r\n                type: 'object',\r\n                properties: {\r\n                  latitude: {\r\n                    type: 'number',\r\n                    example: 6.4281\r\n                  },\r\n                  longitude: {\r\n                    type: 'number',\r\n                    example: 3.4219\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          },\r\n          amenities: {\r\n            type: 'array',\r\n            items: {\r\n              type: 'string'\r\n            },\r\n            example: ['parking', 'security', 'generator', 'water']\r\n          },\r\n          images: {\r\n            type: 'array',\r\n            items: {\r\n              type: 'string',\r\n              format: 'uri'\r\n            }\r\n          },\r\n          owner: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          status: {\r\n            type: 'string',\r\n            enum: ['available', 'rented', 'pending'],\r\n            example: 'available'\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      Notification: {\r\n        type: 'object',\r\n        properties: {\r\n          _id: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          userId: {\r\n            type: 'string',\r\n            example: '507f1f77bcf86cd799439011'\r\n          },\r\n          type: {\r\n            type: 'string',\r\n            enum: ['welcome', 'email_verified', 'new_match', 'new_message', 'property_posted'],\r\n            example: 'new_match'\r\n          },\r\n          title: {\r\n            type: 'string',\r\n            example: 'New Roommate Match Found!'\r\n          },\r\n          message: {\r\n            type: 'string',\r\n            example: 'We found a potential roommate match for you.'\r\n          },\r\n          priority: {\r\n            type: 'string',\r\n            enum: ['low', 'medium', 'high', 'urgent'],\r\n            example: 'high'\r\n          },\r\n          read: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          dismissed: {\r\n            type: 'boolean',\r\n            example: false\r\n          },\r\n          createdAt: {\r\n            type: 'string',\r\n            format: 'date-time'\r\n          }\r\n        }\r\n      },\r\n      PaginationResponse: {\r\n        type: 'object',\r\n        properties: {\r\n          page: {\r\n            type: 'number',\r\n            example: 1\r\n          },\r\n          limit: {\r\n            type: 'number',\r\n            example: 20\r\n          },\r\n          total: {\r\n            type: 'number',\r\n            example: 100\r\n          },\r\n          pages: {\r\n            type: 'number',\r\n            example: 5\r\n          },\r\n          hasMore: {\r\n            type: 'boolean',\r\n            example: true\r\n          }\r\n        }\r\n      }\r\n    },\r\n    responses: {\r\n      UnauthorizedError: {\r\n        description: 'Authentication required',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Authentication required',\r\n              code: 'UNAUTHORIZED',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      ForbiddenError: {\r\n        description: 'Insufficient permissions',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Insufficient permissions',\r\n              code: 'FORBIDDEN',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      NotFoundError: {\r\n        description: 'Resource not found',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Resource not found',\r\n              code: 'NOT_FOUND',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      ValidationError: {\r\n        description: 'Validation error',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              $ref: '#/components/schemas/Error'\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Validation failed',\r\n              code: 'VALIDATION_ERROR',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      },\r\n      RateLimitError: {\r\n        description: 'Rate limit exceeded',\r\n        content: {\r\n          'application/json': {\r\n            schema: {\r\n              allOf: [\r\n                { $ref: '#/components/schemas/Error' },\r\n                {\r\n                  type: 'object',\r\n                  properties: {\r\n                    retryAfter: {\r\n                      type: 'string',\r\n                      example: '15 minutes'\r\n                    }\r\n                  }\r\n                }\r\n              ]\r\n            },\r\n            example: {\r\n              success: false,\r\n              error: 'Too many requests, please try again later',\r\n              retryAfter: '15 minutes',\r\n              timestamp: '2024-01-01T00:00:00.000Z'\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  security: [\r\n    {\r\n      bearerAuth: []\r\n    }\r\n  ],\r\n  tags: [\r\n    {\r\n      name: 'Authentication',\r\n      description: 'User authentication and authorization'\r\n    },\r\n    {\r\n      name: 'Users',\r\n      description: 'User management operations'\r\n    },\r\n    {\r\n      name: 'Properties',\r\n      description: 'Property listing and management'\r\n    },\r\n    {\r\n      name: 'Search',\r\n      description: 'Property and user search functionality'\r\n    },\r\n    {\r\n      name: 'Matches',\r\n      description: 'Roommate matching system'\r\n    },\r\n    {\r\n      name: 'Messages',\r\n      description: 'Messaging and communication'\r\n    },\r\n    {\r\n      name: 'Notifications',\r\n      description: 'Notification management'\r\n    },\r\n    {\r\n      name: 'Emails',\r\n      description: 'Email services and templates'\r\n    },\r\n    {\r\n      name: 'Uploads',\r\n      description: 'File upload operations'\r\n    },\r\n    {\r\n      name: 'Admin',\r\n      description: 'Administrative operations'\r\n    }\r\n  ]\r\n};\r\n\r\n// Options for swagger-jsdoc\r\nconst swaggerOptions = {\r\n  definition: swaggerDefinition,\r\n  apis: [\r\n    './src/routes/*.ts',\r\n    './src/controllers/*.ts',\r\n    './src/models/*.ts'\r\n  ]\r\n};\r\n\r\n// Generate swagger specification\r\nconst swaggerSpec = swaggerJsdoc(swaggerOptions);\r\n\r\n// Custom CSS for Swagger UI\r\nconst customCss = `\r\n  .swagger-ui .topbar { display: none; }\r\n  .swagger-ui .info .title { color: #2563eb; }\r\n  .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; }\r\n  .swagger-ui .info .description { font-size: 14px; line-height: 1.6; }\r\n  .swagger-ui .btn.authorize { background-color: #2563eb; border-color: #2563eb; }\r\n  .swagger-ui .btn.authorize:hover { background-color: #1d4ed8; border-color: #1d4ed8; }\r\n`;\r\n\r\n// Swagger UI options\r\nconst swaggerUiOptions = {\r\n  customCss,\r\n  customSiteTitle: 'LajoSpaces API Documentation',\r\n  customfavIcon: '/favicon.ico',\r\n  swaggerOptions: {\r\n    persistAuthorization: true,\r\n    displayRequestDuration: true,\r\n    docExpansion: 'none',\r\n    filter: true,\r\n    showExtensions: true,\r\n    showCommonExtensions: true,\r\n    tryItOutEnabled: true\r\n  }\r\n};\r\n\r\n/**\r\n * Setup Swagger documentation\r\n */\r\nexport function setupSwagger(app: Express): void {\r\n  // Serve swagger documentation\r\n  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));\r\n  \r\n  // Serve swagger JSON\r\n  app.get('/api/docs.json', (req, res) => {\r\n    res.setHeader('Content-Type', 'application/json');\r\n    res.send(swaggerSpec);\r\n  });\r\n\r\n  console.log(`\uD83D\uDCDA Swagger documentation available at: http://localhost:${config.PORT}/api/docs`);\r\n}\r\n\r\nexport { swaggerSpec };\r\nexport default { setupSwagger, swaggerSpec };\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ccb3141595d32e1eaa0b41fbeb73a9ddd2fac607"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1nx6d89e15 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1nx6d89e15();
var __importDefault =
/* istanbul ignore next */
(cov_1nx6d89e15().s[0]++,
/* istanbul ignore next */
(cov_1nx6d89e15().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1nx6d89e15().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1nx6d89e15().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1nx6d89e15().f[0]++;
  cov_1nx6d89e15().s[1]++;
  return /* istanbul ignore next */(cov_1nx6d89e15().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1nx6d89e15().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1nx6d89e15().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_1nx6d89e15().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1nx6d89e15().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1nx6d89e15().s[3]++;
exports.swaggerSpec = void 0;
/* istanbul ignore next */
cov_1nx6d89e15().s[4]++;
exports.setupSwagger = setupSwagger;
const swagger_jsdoc_1 =
/* istanbul ignore next */
(cov_1nx6d89e15().s[5]++, __importDefault(require("swagger-jsdoc")));
const swagger_ui_express_1 =
/* istanbul ignore next */
(cov_1nx6d89e15().s[6]++, __importDefault(require("swagger-ui-express")));
const environment_1 =
/* istanbul ignore next */
(cov_1nx6d89e15().s[7]++, require("./environment"));
// Swagger definition
const swaggerDefinition =
/* istanbul ignore next */
(cov_1nx6d89e15().s[8]++, {
  openapi: '3.0.0',
  info: {
    title: 'LajoSpaces API',
    version: '1.0.0',
    description: `
      LajoSpaces is Nigeria's premier housing platform connecting property seekers with property owners and roommates.
      
      ## Features
      - **User Management**: Registration, authentication, and profile management
      - **Property Management**: Property listings, search, and favorites
      - **Roommate Matching**: Smart matching system for compatible roommates
      - **Messaging System**: Real-time communication between users
      - **Email & Notifications**: Comprehensive notification system
      - **Security & Performance**: Rate limiting, input validation, and caching
      
      ## Authentication
      Most endpoints require JWT authentication. Include the token in the Authorization header:
      \`Authorization: Bearer <your-jwt-token>\`
      
      ## Rate Limiting
      API endpoints are rate-limited to ensure fair usage:
      - General endpoints: 1000 requests per 15 minutes
      - Authentication: 20 requests per 15 minutes
      - Password reset: 5 requests per hour
      - Email sending: 50 requests per hour
      
      ## Nigerian Market Focus
      This API is optimized for the Nigerian market with:
      - Nigerian phone number validation (+234 format)
      - Nigerian states and LGAs
      - Naira currency support
      - Africa/Lagos timezone
    `,
    contact: {
      name: 'LajoSpaces Support',
      email: '<EMAIL>',
      url: 'https://lajospaces.com'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [{
    url: environment_1.config.NODE_ENV === 'production' ?
    /* istanbul ignore next */
    (cov_1nx6d89e15().b[3][0]++, 'https://api.lajospaces.com') :
    /* istanbul ignore next */
    (cov_1nx6d89e15().b[3][1]++, `http://localhost:${environment_1.config.PORT}`),
    description: environment_1.config.NODE_ENV === 'production' ?
    /* istanbul ignore next */
    (cov_1nx6d89e15().b[4][0]++, 'Production server') :
    /* istanbul ignore next */
    (cov_1nx6d89e15().b[4][1]++, 'Development server')
  }],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token obtained from login endpoint'
      }
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'string',
            example: 'Error message'
          },
          code: {
            type: 'string',
            example: 'ERROR_CODE'
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-01T00:00:00.000Z'
          }
        }
      },
      Success: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully'
          },
          data: {
            type: 'object',
            description: 'Response data'
          }
        }
      },
      User: {
        type: 'object',
        properties: {
          _id: {
            type: 'string',
            example: '507f1f77bcf86cd799439011'
          },
          firstName: {
            type: 'string',
            example: 'John'
          },
          lastName: {
            type: 'string',
            example: 'Doe'
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>'
          },
          phoneNumber: {
            type: 'string',
            example: '+2348012345678'
          },
          role: {
            type: 'string',
            enum: ['user', 'admin'],
            example: 'user'
          },
          emailVerified: {
            type: 'boolean',
            example: true
          },
          isActive: {
            type: 'boolean',
            example: true
          },
          createdAt: {
            type: 'string',
            format: 'date-time'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      Property: {
        type: 'object',
        properties: {
          _id: {
            type: 'string',
            example: '507f1f77bcf86cd799439011'
          },
          title: {
            type: 'string',
            example: 'Beautiful 2-Bedroom Apartment'
          },
          description: {
            type: 'string',
            example: 'Spacious apartment in a serene environment'
          },
          type: {
            type: 'string',
            enum: ['apartment', 'house', 'room', 'studio', 'duplex', 'bungalow'],
            example: 'apartment'
          },
          price: {
            type: 'number',
            example: 120000
          },
          currency: {
            type: 'string',
            example: 'NGN'
          },
          location: {
            type: 'object',
            properties: {
              state: {
                type: 'string',
                example: 'Lagos'
              },
              lga: {
                type: 'string',
                example: 'Victoria Island'
              },
              address: {
                type: 'string',
                example: '123 Ahmadu Bello Way'
              },
              coordinates: {
                type: 'object',
                properties: {
                  latitude: {
                    type: 'number',
                    example: 6.4281
                  },
                  longitude: {
                    type: 'number',
                    example: 3.4219
                  }
                }
              }
            }
          },
          amenities: {
            type: 'array',
            items: {
              type: 'string'
            },
            example: ['parking', 'security', 'generator', 'water']
          },
          images: {
            type: 'array',
            items: {
              type: 'string',
              format: 'uri'
            }
          },
          owner: {
            type: 'string',
            example: '507f1f77bcf86cd799439011'
          },
          status: {
            type: 'string',
            enum: ['available', 'rented', 'pending'],
            example: 'available'
          },
          createdAt: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      Notification: {
        type: 'object',
        properties: {
          _id: {
            type: 'string',
            example: '507f1f77bcf86cd799439011'
          },
          userId: {
            type: 'string',
            example: '507f1f77bcf86cd799439011'
          },
          type: {
            type: 'string',
            enum: ['welcome', 'email_verified', 'new_match', 'new_message', 'property_posted'],
            example: 'new_match'
          },
          title: {
            type: 'string',
            example: 'New Roommate Match Found!'
          },
          message: {
            type: 'string',
            example: 'We found a potential roommate match for you.'
          },
          priority: {
            type: 'string',
            enum: ['low', 'medium', 'high', 'urgent'],
            example: 'high'
          },
          read: {
            type: 'boolean',
            example: false
          },
          dismissed: {
            type: 'boolean',
            example: false
          },
          createdAt: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      PaginationResponse: {
        type: 'object',
        properties: {
          page: {
            type: 'number',
            example: 1
          },
          limit: {
            type: 'number',
            example: 20
          },
          total: {
            type: 'number',
            example: 100
          },
          pages: {
            type: 'number',
            example: 5
          },
          hasMore: {
            type: 'boolean',
            example: true
          }
        }
      }
    },
    responses: {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: 'Authentication required',
              code: 'UNAUTHORIZED',
              timestamp: '2024-01-01T00:00:00.000Z'
            }
          }
        }
      },
      ForbiddenError: {
        description: 'Insufficient permissions',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: 'Insufficient permissions',
              code: 'FORBIDDEN',
              timestamp: '2024-01-01T00:00:00.000Z'
            }
          }
        }
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: 'Resource not found',
              code: 'NOT_FOUND',
              timestamp: '2024-01-01T00:00:00.000Z'
            }
          }
        }
      },
      ValidationError: {
        description: 'Validation error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: 'Validation failed',
              code: 'VALIDATION_ERROR',
              timestamp: '2024-01-01T00:00:00.000Z'
            }
          }
        }
      },
      RateLimitError: {
        description: 'Rate limit exceeded',
        content: {
          'application/json': {
            schema: {
              allOf: [{
                $ref: '#/components/schemas/Error'
              }, {
                type: 'object',
                properties: {
                  retryAfter: {
                    type: 'string',
                    example: '15 minutes'
                  }
                }
              }]
            },
            example: {
              success: false,
              error: 'Too many requests, please try again later',
              retryAfter: '15 minutes',
              timestamp: '2024-01-01T00:00:00.000Z'
            }
          }
        }
      }
    }
  },
  security: [{
    bearerAuth: []
  }],
  tags: [{
    name: 'Authentication',
    description: 'User authentication and authorization'
  }, {
    name: 'Users',
    description: 'User management operations'
  }, {
    name: 'Properties',
    description: 'Property listing and management'
  }, {
    name: 'Search',
    description: 'Property and user search functionality'
  }, {
    name: 'Matches',
    description: 'Roommate matching system'
  }, {
    name: 'Messages',
    description: 'Messaging and communication'
  }, {
    name: 'Notifications',
    description: 'Notification management'
  }, {
    name: 'Emails',
    description: 'Email services and templates'
  }, {
    name: 'Uploads',
    description: 'File upload operations'
  }, {
    name: 'Admin',
    description: 'Administrative operations'
  }]
});
// Options for swagger-jsdoc
const swaggerOptions =
/* istanbul ignore next */
(cov_1nx6d89e15().s[9]++, {
  definition: swaggerDefinition,
  apis: ['./src/routes/*.ts', './src/controllers/*.ts', './src/models/*.ts']
});
// Generate swagger specification
const swaggerSpec =
/* istanbul ignore next */
(cov_1nx6d89e15().s[10]++, (0, swagger_jsdoc_1.default)(swaggerOptions));
/* istanbul ignore next */
cov_1nx6d89e15().s[11]++;
exports.swaggerSpec = swaggerSpec;
// Custom CSS for Swagger UI
const customCss =
/* istanbul ignore next */
(cov_1nx6d89e15().s[12]++, `
  .swagger-ui .topbar { display: none; }
  .swagger-ui .info .title { color: #2563eb; }
  .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; }
  .swagger-ui .info .description { font-size: 14px; line-height: 1.6; }
  .swagger-ui .btn.authorize { background-color: #2563eb; border-color: #2563eb; }
  .swagger-ui .btn.authorize:hover { background-color: #1d4ed8; border-color: #1d4ed8; }
`);
// Swagger UI options
const swaggerUiOptions =
/* istanbul ignore next */
(cov_1nx6d89e15().s[13]++, {
  customCss,
  customSiteTitle: 'LajoSpaces API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'none',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tryItOutEnabled: true
  }
});
/**
 * Setup Swagger documentation
 */
function setupSwagger(app) {
  /* istanbul ignore next */
  cov_1nx6d89e15().f[1]++;
  cov_1nx6d89e15().s[14]++;
  // Serve swagger documentation
  app.use('/api/docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec, swaggerUiOptions));
  // Serve swagger JSON
  /* istanbul ignore next */
  cov_1nx6d89e15().s[15]++;
  app.get('/api/docs.json', (req, res) => {
    /* istanbul ignore next */
    cov_1nx6d89e15().f[2]++;
    cov_1nx6d89e15().s[16]++;
    res.setHeader('Content-Type', 'application/json');
    /* istanbul ignore next */
    cov_1nx6d89e15().s[17]++;
    res.send(swaggerSpec);
  });
  /* istanbul ignore next */
  cov_1nx6d89e15().s[18]++;
  console.log(`📚 Swagger documentation available at: http://localhost:${environment_1.config.PORT}/api/docs`);
}
/* istanbul ignore next */
cov_1nx6d89e15().s[19]++;
exports.default = {
  setupSwagger,
  swaggerSpec
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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