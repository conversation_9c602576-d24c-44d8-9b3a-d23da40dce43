{"version": 3, "names": ["cov_1zz9lj91db", "actualCoverage", "s", "express_1", "require", "match_controller_1", "matchPreferences_controller_1", "auth_1", "validation_1", "match_validators_1", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "use", "authenticate", "getMatches", "post", "validateRequest", "swipeMatchSchema", "swipeMatch", "getMatchHistory", "getMatchPreferences", "put", "updatePreferencesSchema", "updateMatchPreferences", "togglePreferencesSchema", "toggleMatchPreferences", "updatePreferenceSection", "getPreferencesSummary", "dealBreakerSchema", "addDealBreaker", "delete", "removeDealBreaker", "validateObjectId", "getMatchById", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\match.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  getMatches,\r\n  swipeMatch,\r\n  getMatchHistory,\r\n  getMatchById\r\n} from '../controllers/match.controller';\r\nimport {\r\n  getMatchPreferences,\r\n  updateMatchPreferences,\r\n  toggleMatchPreferences,\r\n  updatePreferenceSection,\r\n  addDealBreaker,\r\n  removeDealBreaker,\r\n  getPreferencesSummary\r\n} from '../controllers/matchPreferences.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  swipeMatchSchema,\r\n  updatePreferencesSchema,\r\n  togglePreferencesSchema,\r\n  dealBreakerSchema\r\n} from '../validators/match.validators';\r\n\r\nconst router = Router();\r\n\r\n// Health check (public)\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ message: 'Match routes working', timestamp: new Date().toISOString() });\r\n});\r\n\r\n// All other match routes require authentication\r\nrouter.use(authenticate);\r\n\r\n/**\r\n * @route   GET /api/matches\r\n * @desc    Get potential matches for the authenticated user\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/',\r\n  getMatches\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/swipe\r\n * @desc    Swipe on a match (like, pass, super like)\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/swipe',\r\n  validateRequest(swipeMatchSchema, 'body'),\r\n  swipeMatch\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/history\r\n * @desc    Get user's match history\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/history',\r\n  getMatchHistory\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/preferences\r\n * @desc    Get user's match preferences\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/preferences',\r\n  getMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/matches/preferences\r\n * @desc    Update user's match preferences\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/preferences',\r\n  validateRequest(updatePreferencesSchema, 'body'),\r\n  updateMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/preferences/toggle\r\n * @desc    Toggle match preferences active status\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preferences/toggle',\r\n  validateRequest(togglePreferencesSchema, 'body'),\r\n  toggleMatchPreferences\r\n);\r\n\r\n/**\r\n * @route   PUT /api/matches/preferences/:section\r\n * @desc    Update specific preference section\r\n * @access  Private\r\n */\r\nrouter.put(\r\n  '/preferences/:section',\r\n  updatePreferenceSection\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/preferences/summary\r\n * @desc    Get match preferences summary/stats\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/preferences/summary',\r\n  getPreferencesSummary\r\n);\r\n\r\n/**\r\n * @route   POST /api/matches/preferences/deal-breakers\r\n * @desc    Add a deal breaker\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/preferences/deal-breakers',\r\n  validateRequest(dealBreakerSchema, 'body'),\r\n  addDealBreaker\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/matches/preferences/deal-breakers\r\n * @desc    Remove a deal breaker\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/preferences/deal-breakers',\r\n  validateRequest(dealBreakerSchema, 'body'),\r\n  removeDealBreaker\r\n);\r\n\r\n/**\r\n * @route   GET /api/matches/:id\r\n * @desc    Get a specific match by ID\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/:id',\r\n  validateObjectId('id'),\r\n  getMatchById\r\n);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;AAnCA,MAAAC,SAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,kBAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAMA,MAAAE,6BAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AASA,MAAAG,MAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,YAAA;AAAA;AAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAK,kBAAA;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAOA,MAAMM,MAAM;AAAA;AAAA,CAAAV,cAAA,GAAAE,CAAA,OAAG,IAAAC,SAAA,CAAAQ,MAAM,GAAE;AAEvB;AAAA;AAAAX,cAAA,GAAAE,CAAA;AACAQ,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAE,CAAA;EAClCY,GAAG,CAACE,IAAI,CAAC;IAAEC,OAAO,EAAE,sBAAsB;IAAEC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;EAAE,CAAE,CAAC;AACpF,CAAC,CAAC;AAEF;AAAA;AAAApB,cAAA,GAAAE,CAAA;AACAQ,MAAM,CAACW,GAAG,CAACd,MAAA,CAAAe,YAAY,CAAC;AAExB;;;;;AAAA;AAAAtB,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACE,GAAG,CACR,GAAG,EACHP,kBAAA,CAAAkB,UAAU,CACX;AAED;;;;;AAAA;AAAAvB,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACc,IAAI,CACT,QAAQ,EACR,IAAAhB,YAAA,CAAAiB,eAAe,EAAChB,kBAAA,CAAAiB,gBAAgB,EAAE,MAAM,CAAC,EACzCrB,kBAAA,CAAAsB,UAAU,CACX;AAED;;;;;AAAA;AAAA3B,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACE,GAAG,CACR,UAAU,EACVP,kBAAA,CAAAuB,eAAe,CAChB;AAED;;;;;AAAA;AAAA5B,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACE,GAAG,CACR,cAAc,EACdN,6BAAA,CAAAuB,mBAAmB,CACpB;AAED;;;;;AAAA;AAAA7B,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACoB,GAAG,CACR,cAAc,EACd,IAAAtB,YAAA,CAAAiB,eAAe,EAAChB,kBAAA,CAAAsB,uBAAuB,EAAE,MAAM,CAAC,EAChDzB,6BAAA,CAAA0B,sBAAsB,CACvB;AAED;;;;;AAAA;AAAAhC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACc,IAAI,CACT,qBAAqB,EACrB,IAAAhB,YAAA,CAAAiB,eAAe,EAAChB,kBAAA,CAAAwB,uBAAuB,EAAE,MAAM,CAAC,EAChD3B,6BAAA,CAAA4B,sBAAsB,CACvB;AAED;;;;;AAAA;AAAAlC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACoB,GAAG,CACR,uBAAuB,EACvBxB,6BAAA,CAAA6B,uBAAuB,CACxB;AAED;;;;;AAAA;AAAAnC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACE,GAAG,CACR,sBAAsB,EACtBN,6BAAA,CAAA8B,qBAAqB,CACtB;AAED;;;;;AAAA;AAAApC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACc,IAAI,CACT,4BAA4B,EAC5B,IAAAhB,YAAA,CAAAiB,eAAe,EAAChB,kBAAA,CAAA4B,iBAAiB,EAAE,MAAM,CAAC,EAC1C/B,6BAAA,CAAAgC,cAAc,CACf;AAED;;;;;AAAA;AAAAtC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAAC6B,MAAM,CACX,4BAA4B,EAC5B,IAAA/B,YAAA,CAAAiB,eAAe,EAAChB,kBAAA,CAAA4B,iBAAiB,EAAE,MAAM,CAAC,EAC1C/B,6BAAA,CAAAkC,iBAAiB,CAClB;AAED;;;;;AAAA;AAAAxC,cAAA,GAAAE,CAAA;AAKAQ,MAAM,CAACE,GAAG,CACR,MAAM,EACN,IAAAJ,YAAA,CAAAiC,gBAAgB,EAAC,IAAI,CAAC,EACtBpC,kBAAA,CAAAqC,YAAY,CACb;AAAC;AAAA1C,cAAA,GAAAE,CAAA;AAEFyC,OAAA,CAAAC,OAAA,GAAelC,MAAM", "ignoreList": []}