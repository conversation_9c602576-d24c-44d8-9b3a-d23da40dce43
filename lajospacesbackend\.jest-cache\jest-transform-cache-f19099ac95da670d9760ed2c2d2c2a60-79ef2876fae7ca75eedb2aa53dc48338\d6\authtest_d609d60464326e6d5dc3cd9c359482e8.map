{"file": "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\integration\\auth.test.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAgC;AAEhC,gDAA6C;AAC7C,gEAAsG;AACtG,oDAAgD;AAEhD,qCAAqC;AACrC,IAAI,GAAY,CAAC;AAEjB,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;IACpD,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,IAAA,gCAAiB,GAAE,CAAC;QAC1B,iDAAiD;QACjD,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,eAAe,GAAC,CAAC;QACpD,GAAG,GAAG,SAAS,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,IAAA,kCAAmB,GAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,IAAA,4BAAa,GAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,QAAQ,GAAG;gBACf,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,sBAAS,CAAC,WAAW,EAAE;gBAC9B,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,sBAAS,CAAC,iBAAiB,EAAE;aAC3C,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAE/C,sCAAsC;YACtC,MAAM,IAAI,GAAG,MAAM,WAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG;gBACf,SAAS,EAAE,MAAM;gBACjB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,aAAa;gBACvB,WAAW,EAAE,sBAAS,CAAC,iBAAiB,EAAE;aAC3C,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,sBAAS,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAEvD,oBAAoB;YACpB,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE5B,4CAA4C;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC;YAE9E,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC;iBACd,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,QAAa,CAAC;QAElB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,CAAC;YACzE,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,sBAAsB;gBAC7B,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,WAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;YAEhE,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,SAAS,CAAC;iBACf,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,QAAa,CAAC;QAClB,IAAI,SAAiB,CAAC;QAEtB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC,CAAC;YACzE,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvC,SAAS,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,kBAAkB,CAAC;iBACxB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,kBAAkB,CAAC;iBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,IAAI,QAAa,CAAC;QAElB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,2BAA2B,CAAC;iBACjC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;iBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,2BAA2B,CAAC;iBACjC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,IAAI,CAAC,2BAA2B,CAAC;iBACjC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;iBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,QAAa,CAAC;QAClB,IAAI,SAAiB,CAAC;QAEtB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvC,SAAS,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,cAAc,CAAC;iBACnB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,cAAc,CAAC;iBACnB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,cAAc,CAAC;iBACnB,GAAG,CAAC,eAAe,EAAE,sBAAsB,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,IAAI,QAAa,CAAC;QAClB,IAAI,SAAiB,CAAC;QAEtB,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC5E,QAAQ,GAAG,MAAM,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvC,SAAS,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,YAAY,GAAG;gBACnB,eAAe,EAAE,gBAAgB;gBACjC,WAAW,EAAE,gBAAgB;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC;iBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAEpD,8BAA8B;YAC9B,MAAM,WAAW,GAAG,MAAM,WAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACtD,MAAM,kBAAkB,GAAG,MAAM,WAAY,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAChF,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,YAAY,GAAG;gBACnB,eAAe,EAAE,eAAe;gBAChC,WAAW,EAAE,gBAAgB;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC;iBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,YAAY,GAAG;gBACnB,eAAe,EAAE,gBAAgB;gBACjC,WAAW,EAAE,KAAK;aACnB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC;iBAChC,GAAG,CAAC,2BAA2B,CAAC;iBAChC,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,IAAI,CAAC,YAAY,CAAC;iBAClB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,QAAQ,GAAG,sBAAS,CAAC,gBAAgB,EAAE,CAAC;YAE9C,iCAAiC;YACjC,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,IAAA,mBAAO,EAAC,GAAG,CAAC;iBACT,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,QAAQ,CAAC,CAClB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE9C,uCAAuC;YACvC,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;YACzE,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\tests\\integration\\auth.test.ts"], "sourcesContent": ["import request from 'supertest';\r\nimport { Express } from 'express';\r\nimport { User } from '../../src/models/User';\r\nimport { setupTestDatabase, clearTestData, cleanupTestDatabase } from '../../src/config/testDatabase';\r\nimport { testUtils } from '../setup/jest.setup';\r\n\r\n// Import app without starting server\r\nlet app: Express;\r\n\r\ndescribe('Authentication API Integration Tests', () => {\r\n  beforeAll(async () => {\r\n    await setupTestDatabase();\r\n    // Dynamically import app to avoid server startup\r\n    const { createApp } = await import('../../src/app');\r\n    app = createApp();\r\n  });\r\n\r\n  afterAll(async () => {\r\n    await cleanupTestDatabase();\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    await clearTestData();\r\n  });\r\n\r\n  describe('POST /api/auth/register', () => {\r\n    it('should register a new user successfully', async () => {\r\n      const userData = {\r\n        firstName: 'John',\r\n        lastName: 'Doe',\r\n        email: testUtils.randomEmail(),\r\n        password: 'password123',\r\n        phoneNumber: testUtils.randomPhoneNumber()\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/register')\r\n        .send(userData)\r\n        .expect(201);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.data.user).toBeDefined();\r\n      expect(response.body.data.user.email).toBe(userData.email);\r\n      expect(response.body.data.user.password).toBeUndefined();\r\n      expect(response.body.data.token).toBeDefined();\r\n\r\n      // Verify user was created in database\r\n      const user = await User.findOne({ email: userData.email });\r\n      expect(user).toBeDefined();\r\n      expect(user!.firstName).toBe(userData.firstName);\r\n    });\r\n\r\n    it('should return validation error for invalid email', async () => {\r\n      const userData = {\r\n        firstName: 'John',\r\n        lastName: 'Doe',\r\n        email: 'invalid-email',\r\n        password: 'password123',\r\n        phoneNumber: testUtils.randomPhoneNumber()\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/register')\r\n        .send(userData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('email');\r\n    });\r\n\r\n    it('should return error for duplicate email', async () => {\r\n      const email = testUtils.randomEmail();\r\n      const userData = testUtils.generateTestUser({ email });\r\n\r\n      // Create first user\r\n      await User.create(userData);\r\n\r\n      // Try to create second user with same email\r\n      const response = await request(app)\r\n        .post('/api/auth/register')\r\n        .send(userData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('email');\r\n    });\r\n\r\n    it('should return validation error for weak password', async () => {\r\n      const userData = testUtils.generateTestUser({ password: '123' });\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/register')\r\n        .send(userData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('password');\r\n    });\r\n\r\n    it('should return validation error for invalid phone number', async () => {\r\n      const userData = testUtils.generateTestUser({ phoneNumber: 'invalid-phone' });\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/register')\r\n        .send(userData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('phone');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/auth/login', () => {\r\n    let testUser: any;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser({ password: 'password123' });\r\n      testUser = await User.create(userData);\r\n    });\r\n\r\n    it('should login user with valid credentials', async () => {\r\n      const loginData = {\r\n        email: testUser.email,\r\n        password: 'password123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/login')\r\n        .send(loginData)\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.data.user).toBeDefined();\r\n      expect(response.body.data.user.email).toBe(testUser.email);\r\n      expect(response.body.data.user.password).toBeUndefined();\r\n      expect(response.body.data.token).toBeDefined();\r\n    });\r\n\r\n    it('should return error for invalid email', async () => {\r\n      const loginData = {\r\n        email: '<EMAIL>',\r\n        password: 'password123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/login')\r\n        .send(loginData)\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('Invalid');\r\n    });\r\n\r\n    it('should return error for invalid password', async () => {\r\n      const loginData = {\r\n        email: testUser.email,\r\n        password: 'wrongpassword'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/login')\r\n        .send(loginData)\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('Invalid');\r\n    });\r\n\r\n    it('should return error for inactive user', async () => {\r\n      await User.findByIdAndUpdate(testUser._id, { isActive: false });\r\n\r\n      const loginData = {\r\n        email: testUser.email,\r\n        password: 'password123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .post('/api/auth/login')\r\n        .send(loginData)\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('inactive');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/auth/logout', () => {\r\n    let testUser: any;\r\n    let authToken: string;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser({ password: 'password123' });\r\n      testUser = await User.create(userData);\r\n      authToken = testUser.generateAuthToken();\r\n    });\r\n\r\n    it('should logout user successfully', async () => {\r\n      const response = await request(app)\r\n        .post('/api/auth/logout')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.message).toContain('logout');\r\n    });\r\n\r\n    it('should return error without auth token', async () => {\r\n      const response = await request(app)\r\n        .post('/api/auth/logout')\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('token');\r\n    });\r\n  });\r\n\r\n  describe('POST /api/auth/forgot-password', () => {\r\n    let testUser: any;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser();\r\n      testUser = await User.create(userData);\r\n    });\r\n\r\n    it('should send password reset email for valid email', async () => {\r\n      const response = await request(app)\r\n        .post('/api/auth/forgot-password')\r\n        .send({ email: testUser.email })\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.message).toContain('reset');\r\n    });\r\n\r\n    it('should return success even for non-existent email (security)', async () => {\r\n      const response = await request(app)\r\n        .post('/api/auth/forgot-password')\r\n        .send({ email: '<EMAIL>' })\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.message).toContain('reset');\r\n    });\r\n\r\n    it('should return validation error for invalid email format', async () => {\r\n      const response = await request(app)\r\n        .post('/api/auth/forgot-password')\r\n        .send({ email: 'invalid-email' })\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('email');\r\n    });\r\n  });\r\n\r\n  describe('GET /api/auth/me', () => {\r\n    let testUser: any;\r\n    let authToken: string;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser();\r\n      testUser = await User.create(userData);\r\n      authToken = testUser.generateAuthToken();\r\n    });\r\n\r\n    it('should return current user profile', async () => {\r\n      const response = await request(app)\r\n        .get('/api/auth/me')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.data.user).toBeDefined();\r\n      expect(response.body.data.user.email).toBe(testUser.email);\r\n      expect(response.body.data.user.password).toBeUndefined();\r\n    });\r\n\r\n    it('should return error without auth token', async () => {\r\n      const response = await request(app)\r\n        .get('/api/auth/me')\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('token');\r\n    });\r\n\r\n    it('should return error with invalid token', async () => {\r\n      const response = await request(app)\r\n        .get('/api/auth/me')\r\n        .set('Authorization', 'Bearer invalid-token')\r\n        .expect(401);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('token');\r\n    });\r\n  });\r\n\r\n  describe('PUT /api/auth/change-password', () => {\r\n    let testUser: any;\r\n    let authToken: string;\r\n\r\n    beforeEach(async () => {\r\n      const userData = testUtils.generateTestUser({ password: 'oldpassword123' });\r\n      testUser = await User.create(userData);\r\n      authToken = testUser.generateAuthToken();\r\n    });\r\n\r\n    it('should change password successfully', async () => {\r\n      const passwordData = {\r\n        currentPassword: 'oldpassword123',\r\n        newPassword: 'newpassword123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .put('/api/auth/change-password')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(passwordData)\r\n        .expect(200);\r\n\r\n      expect(response.body.success).toBe(true);\r\n      expect(response.body.message).toContain('password');\r\n\r\n      // Verify password was changed\r\n      const updatedUser = await User.findById(testUser._id);\r\n      const isNewPasswordValid = await updatedUser!.comparePassword('newpassword123');\r\n      expect(isNewPasswordValid).toBe(true);\r\n    });\r\n\r\n    it('should return error for incorrect current password', async () => {\r\n      const passwordData = {\r\n        currentPassword: 'wrongpassword',\r\n        newPassword: 'newpassword123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .put('/api/auth/change-password')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(passwordData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('current password');\r\n    });\r\n\r\n    it('should return validation error for weak new password', async () => {\r\n      const passwordData = {\r\n        currentPassword: 'oldpassword123',\r\n        newPassword: '123'\r\n      };\r\n\r\n      const response = await request(app)\r\n        .put('/api/auth/change-password')\r\n        .set('Authorization', `Bearer ${authToken}`)\r\n        .send(passwordData)\r\n        .expect(400);\r\n\r\n      expect(response.body.success).toBe(false);\r\n      expect(response.body.error).toContain('password');\r\n    });\r\n  });\r\n\r\n  describe('Rate Limiting', () => {\r\n    it('should apply rate limiting to auth endpoints', async () => {\r\n      const userData = testUtils.generateTestUser();\r\n\r\n      // Make multiple requests quickly\r\n      const requests = Array(25).fill(null).map(() =>\r\n        request(app)\r\n          .post('/api/auth/register')\r\n          .send(userData)\r\n      );\r\n\r\n      const responses = await Promise.all(requests);\r\n      \r\n      // Some requests should be rate limited\r\n      const rateLimitedResponses = responses.filter(res => res.status === 429);\r\n      expect(rateLimitedResponses.length).toBeGreaterThan(0);\r\n    });\r\n  });\r\n});\r\n"], "version": 3}