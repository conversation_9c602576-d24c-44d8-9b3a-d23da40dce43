{"version": 3, "names": ["express_1", "cov_25hlrlnflg", "s", "require", "propertySearch_controller_1", "auth_1", "validation_1", "propertySearch_validators_1", "router", "Router", "post", "validateRequest", "searchPropertiesSchema", "searchProperties", "get", "nearbyPropertiesSchema", "getNearbyProperties", "getPropertyFilters", "getSearchSuggestions", "authenticate", "saveSearchSchema", "saveSearch", "getSavedSearches", "delete", "validateObjectId", "deleteSavedSearch", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertySearch.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport {\r\n  searchProperties,\r\n  getNearbyProperties,\r\n  getPropertyFilters,\r\n  getSearchSuggestions,\r\n  saveSearch,\r\n  getSavedSearches,\r\n  deleteSavedSearch\r\n} from '../controllers/propertySearch.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  searchPropertiesSchema,\r\n  nearbyPropertiesSchema,\r\n  saveSearchSchema\r\n} from '../validators/propertySearch.validators';\r\n\r\nconst router = Router();\r\n\r\n/**\r\n * @route   POST /api/properties/search\r\n * @desc    Advanced property search with filters\r\n * @access  Public\r\n */\r\nrouter.post(\r\n  '/search',\r\n  validateRequest(searchPropertiesSchema, 'body'),\r\n  searchProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/nearby\r\n * @desc    Find properties near a location\r\n * @access  Public\r\n */\r\nrouter.get(\r\n  '/search/nearby',\r\n  validateRequest(nearbyPropertiesSchema, 'query'),\r\n  getNearbyProperties\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/filters\r\n * @desc    Get available search filters and their options\r\n * @access  Public\r\n */\r\nrouter.get('/search/filters', getPropertyFilters);\r\n\r\n/**\r\n * @route   GET /api/properties/search/suggestions\r\n * @desc    Get search suggestions based on query\r\n * @access  Public\r\n */\r\nrouter.get('/search/suggestions', getSearchSuggestions);\r\n\r\n/**\r\n * @route   POST /api/properties/search/save\r\n * @desc    Save a search query for later\r\n * @access  Private\r\n */\r\nrouter.post(\r\n  '/search/save',\r\n  authenticate,\r\n  validateRequest(saveSearchSchema, 'body'),\r\n  saveSearch\r\n);\r\n\r\n/**\r\n * @route   GET /api/properties/search/saved\r\n * @desc    Get user's saved searches\r\n * @access  Private\r\n */\r\nrouter.get(\r\n  '/search/saved',\r\n  authenticate,\r\n  getSavedSearches\r\n);\r\n\r\n/**\r\n * @route   DELETE /api/properties/search/saved/:id\r\n * @desc    Delete a saved search\r\n * @access  Private\r\n */\r\nrouter.delete(\r\n  '/search/saved/:id',\r\n  authenticate,\r\n  validateObjectId('id'),\r\n  deleteSavedSearch\r\n);\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,2BAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,OAAAC,OAAA;AASA,MAAAE,MAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAG,YAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAI,2BAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAMA,MAAMK,MAAM;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAS,MAAM,GAAE;AAEvB;;;;;AAAA;AAAAR,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,SAAS,EACT,IAAAJ,YAAA,CAAAK,eAAe,EAACJ,2BAAA,CAAAK,sBAAsB,EAAE,MAAM,CAAC,EAC/CR,2BAAA,CAAAS,gBAAgB,CACjB;AAED;;;;;AAAA;AAAAZ,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACM,GAAG,CACR,gBAAgB,EAChB,IAAAR,YAAA,CAAAK,eAAe,EAACJ,2BAAA,CAAAQ,sBAAsB,EAAE,OAAO,CAAC,EAChDX,2BAAA,CAAAY,mBAAmB,CACpB;AAED;;;;;AAAA;AAAAf,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACM,GAAG,CAAC,iBAAiB,EAAEV,2BAAA,CAAAa,kBAAkB,CAAC;AAEjD;;;;;AAAA;AAAAhB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACM,GAAG,CAAC,qBAAqB,EAAEV,2BAAA,CAAAc,oBAAoB,CAAC;AAEvD;;;;;AAAA;AAAAjB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACE,IAAI,CACT,cAAc,EACdL,MAAA,CAAAc,YAAY,EACZ,IAAAb,YAAA,CAAAK,eAAe,EAACJ,2BAAA,CAAAa,gBAAgB,EAAE,MAAM,CAAC,EACzChB,2BAAA,CAAAiB,UAAU,CACX;AAED;;;;;AAAA;AAAApB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACM,GAAG,CACR,eAAe,EACfT,MAAA,CAAAc,YAAY,EACZf,2BAAA,CAAAkB,gBAAgB,CACjB;AAED;;;;;AAAA;AAAArB,cAAA,GAAAC,CAAA;AAKAM,MAAM,CAACe,MAAM,CACX,mBAAmB,EACnBlB,MAAA,CAAAc,YAAY,EACZ,IAAAb,YAAA,CAAAkB,gBAAgB,EAAC,IAAI,CAAC,EACtBpB,2BAAA,CAAAqB,iBAAiB,CAClB;AAAC;AAAAxB,cAAA,GAAAC,CAAA;AAEFwB,OAAA,CAAAC,OAAA,GAAenB,MAAM", "ignoreList": []}