{"version": 3, "names": ["cov_1utmstr375", "actualCoverage", "socket_io_1", "s", "require", "jsonwebtoken_1", "__importDefault", "mongoose_1", "User_model_1", "Conversation_1", "logger_1", "SocketService", "constructor", "server", "f", "onlineUsers", "Map", "typingUsers", "userSockets", "io", "Server", "cors", "origin", "b", "process", "env", "FRONTEND_URL", "methods", "credentials", "transports", "setupMiddleware", "setupEventHandlers", "startCleanupInterval", "use", "socket", "next", "token", "handshake", "auth", "headers", "authorization", "split", "Error", "decoded", "default", "verify", "JWT_SECRET", "user", "User", "findById", "id", "select", "userId", "_id", "toString", "error", "logger", "on", "info", "handleUserConnect", "data", "handleSendMessage", "handleMessageDelivered", "handleMessageRead", "handleEditMessage", "handleDeleteMessage", "handleMessageReaction", "handleJoinConversation", "handleLeaveConversation", "handleCreateConversation", "handleTypingStart", "handleTypingStop", "handleStatusChange", "handleUserDisconnect", "conversationId", "conversation", "Conversation", "findOne", "participants", "status", "join", "debug", "emit", "message", "leave", "participantIds", "conversationType", "title", "matchId", "allParticipants", "Set", "map", "Types", "ObjectId", "participantDetails", "participantId", "joinedAt", "Date", "role", "isActive", "lastSeenAt", "unreadCount", "isMuted", "undefined", "settings", "allowFileSharing", "allowLocationSharing", "allowPropertySharing", "maxParticipants", "autoDeleteMessages", "requireApprovalForNewMembers", "analytics", "totalMessages", "totalParticipants", "length", "averageResponseTime", "lastActivityAt", "messagesThisWeek", "messagesThisMonth", "save", "for<PERSON>ach", "userSocketIds", "get", "socketId", "to", "socketsJoin", "toObject", "messageId", "content", "Message", "senderId", "messageType", "isDeleted", "messageAge", "now", "createdAt", "getTime", "isEdited", "originalContent", "trim", "editedAt", "deleteForEveryone", "deletedAt", "deletedBy", "deletedForEveryone", "reaction", "validReactions", "includes", "existingReactionIndex", "reactions", "findIndex", "r", "push", "set", "lastSeen", "joinUserConversations", "broadcastUserStatus", "Array", "from", "values", "conversations", "find", "metadata", "canUserSendMessage", "receiverId", "p", "updateLastMessage", "populatedMessage", "populate", "tempId", "has", "deliveredAt", "_socket", "findByIdAndUpdate", "new", "readAt", "mark<PERSON><PERSON><PERSON>", "readBy", "<PERSON><PERSON><PERSON>", "timestamp", "isTyping", "delete", "updatedSocketIds", "filter", "key", "typingUser", "entries", "setInterval", "getOnlineUsers", "isUserOnline", "getUserStatus", "sendNotificationToUser", "notification", "getIO", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\socketService.ts"], "sourcesContent": ["import { Server as SocketIOServer, Socket } from 'socket.io';\r\nimport { Server as HTTPServer } from 'http';\r\nimport jwt from 'jsonwebtoken';\r\nimport { Types } from 'mongoose';\r\nimport { User } from '../models/User.model';\r\nimport { Conversation, Message } from '../models/Conversation';\r\nimport { logger } from '../utils/logger';\r\n\r\n\r\ninterface AuthenticatedSocket extends Socket {\r\n  userId?: string;\r\n  user?: any;\r\n}\r\n\r\ninterface OnlineUser {\r\n  userId: string;\r\n  socketId: string;\r\n  lastSeen: Date;\r\n  status: 'online' | 'away' | 'busy' | 'offline';\r\n}\r\n\r\ninterface TypingUser {\r\n  userId: string;\r\n  conversationId: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport class SocketService {\r\n  private io: SocketIOServer;\r\n  private onlineUsers: Map<string, OnlineUser> = new Map();\r\n  private typingUsers: Map<string, TypingUser> = new Map();\r\n  private userSockets: Map<string, string[]> = new Map(); // userId -> socketIds[]\r\n\r\n  constructor(server: HTTPServer) {\r\n    this.io = new SocketIOServer(server, {\r\n      cors: {\r\n        origin: process.env.FRONTEND_URL || \"http://localhost:3000\",\r\n        methods: [\"GET\", \"POST\"],\r\n        credentials: true\r\n      },\r\n      transports: ['websocket', 'polling']\r\n    });\r\n\r\n    this.setupMiddleware();\r\n    this.setupEventHandlers();\r\n    this.startCleanupInterval();\r\n  }\r\n\r\n  private setupMiddleware(): void {\r\n    // Authentication middleware\r\n    this.io.use(async (socket: any, next) => {\r\n      try {\r\n        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];\r\n        \r\n        if (!token) {\r\n          return next(new Error('Authentication token required'));\r\n        }\r\n\r\n        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;\r\n        const user = await User.findById(decoded.id).select('-password');\r\n        \r\n        if (!user) {\r\n          return next(new Error('User not found'));\r\n        }\r\n\r\n        socket.userId = (user as any)._id.toString();\r\n        socket.user = user;\r\n        next();\r\n      } catch (error) {\r\n        logger.error('Socket authentication error:', error);\r\n        next(new Error('Authentication failed'));\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    this.io.on('connection', (socket: AuthenticatedSocket) => {\r\n      logger.info(`User ${socket.userId} connected with socket ${socket.id}`);\r\n      \r\n      // Handle user connection\r\n      this.handleUserConnect(socket);\r\n\r\n      // Message events\r\n      socket.on('send_message', (data) => this.handleSendMessage(socket, data));\r\n      socket.on('message_delivered', (data) => this.handleMessageDelivered(socket, data));\r\n      socket.on('message_read', (data) => this.handleMessageRead(socket, data));\r\n      socket.on('edit_message', (data) => this.handleEditMessage(socket, data));\r\n      socket.on('delete_message', (data) => this.handleDeleteMessage(socket, data));\r\n      socket.on('react_to_message', (data) => this.handleMessageReaction(socket, data));\r\n\r\n      // Conversation events\r\n      socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));\r\n      socket.on('leave_conversation', (data) => this.handleLeaveConversation(socket, data));\r\n      socket.on('create_conversation', (data) => this.handleCreateConversation(socket, data));\r\n\r\n      // Typing events\r\n      socket.on('typing_start', (data) => this.handleTypingStart(socket, data));\r\n      socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));\r\n\r\n      // Status events\r\n      socket.on('status_change', (data) => this.handleStatusChange(socket, data));\r\n\r\n      // Disconnect event\r\n      socket.on('disconnect', () => this.handleUserDisconnect(socket));\r\n    });\r\n  }\r\n\r\n  private async handleJoinConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n\r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    try {\r\n      // Verify user is participant in conversation\r\n      const conversation = await Conversation.findOne({\r\n        _id: conversationId,\r\n        participants: socket.userId,\r\n        status: 'active'\r\n      });\r\n\r\n      if (conversation) {\r\n        socket.join(`conversation_${conversationId}`);\r\n        logger.debug(`User ${socket.userId} joined conversation ${conversationId}`);\r\n\r\n        socket.emit('conversation_joined', { conversationId });\r\n      } else {\r\n        socket.emit('error', { message: 'Cannot join conversation' });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error joining conversation:', error);\r\n      socket.emit('error', { message: 'Failed to join conversation' });\r\n    }\r\n  }\r\n\r\n  private async handleLeaveConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n\r\n    if (!conversationId) return;\r\n\r\n    socket.leave(`conversation_${conversationId}`);\r\n    logger.debug(`User ${socket.userId} left conversation ${conversationId}`);\r\n\r\n    socket.emit('conversation_left', { conversationId });\r\n  }\r\n\r\n  private async handleCreateConversation(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { participantIds, conversationType = 'direct', title, matchId } = data;\r\n\r\n      if (!socket.userId || !participantIds) {\r\n        socket.emit('error', { message: 'Invalid conversation data' });\r\n        return;\r\n      }\r\n\r\n      // Add current user to participants\r\n      const allParticipants = [...new Set([socket.userId, ...participantIds])];\r\n\r\n      // Create conversation (simplified version)\r\n      const conversation = new Conversation({\r\n        participants: allParticipants.map(id => new Types.ObjectId(id)),\r\n        participantDetails: allParticipants.map(participantId => ({\r\n          userId: new Types.ObjectId(participantId),\r\n          joinedAt: new Date(),\r\n          role: participantId === socket.userId ? 'admin' : 'member',\r\n          isActive: true,\r\n          lastSeenAt: new Date(),\r\n          unreadCount: 0,\r\n          isMuted: false\r\n        })),\r\n        conversationType,\r\n        title: conversationType === 'group' ? title : undefined,\r\n        matchId: matchId ? new Types.ObjectId(matchId) : undefined,\r\n        settings: {\r\n          allowFileSharing: true,\r\n          allowLocationSharing: true,\r\n          allowPropertySharing: true,\r\n          maxParticipants: conversationType === 'direct' ? 2 : 50,\r\n          autoDeleteMessages: false,\r\n          requireApprovalForNewMembers: false\r\n        },\r\n        analytics: {\r\n          totalMessages: 0,\r\n          totalParticipants: allParticipants.length,\r\n          averageResponseTime: 0,\r\n          lastActivityAt: new Date(),\r\n          messagesThisWeek: 0,\r\n          messagesThisMonth: 0\r\n        }\r\n      });\r\n\r\n      await conversation.save();\r\n\r\n      // Join all participants to the conversation room\r\n      allParticipants.forEach(participantId => {\r\n        const userSocketIds = this.userSockets.get(participantId);\r\n        if (userSocketIds) {\r\n          userSocketIds.forEach(socketId => {\r\n            this.io.to(socketId).socketsJoin(`conversation_${conversation._id}`);\r\n          });\r\n        }\r\n      });\r\n\r\n      // Notify all participants\r\n      this.io.to(`conversation_${conversation._id}`).emit('conversation_created', {\r\n        conversation: conversation.toObject()\r\n      });\r\n\r\n      logger.info(`Conversation ${conversation._id} created by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error creating conversation:', error);\r\n      socket.emit('error', { message: 'Failed to create conversation' });\r\n    }\r\n  }\r\n\r\n  private async handleEditMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, content } = data;\r\n\r\n      if (!socket.userId || !messageId || !content) {\r\n        socket.emit('error', { message: 'Invalid edit data' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        senderId: socket.userId,\r\n        messageType: 'text',\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found or cannot be edited' });\r\n        return;\r\n      }\r\n\r\n      // Check if message is too old to edit (24 hours)\r\n      const messageAge = Date.now() - message.createdAt.getTime();\r\n      if (messageAge > 24 * 60 * 60 * 1000) {\r\n        socket.emit('error', { message: 'Message is too old to edit' });\r\n        return;\r\n      }\r\n\r\n      // Store original content if not already edited\r\n      if (!message.isEdited) {\r\n        message.originalContent = message.content;\r\n      }\r\n\r\n      message.content = content.trim();\r\n      message.isEdited = true;\r\n      message.editedAt = new Date();\r\n\r\n      await message.save();\r\n\r\n      // Broadcast edit to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_edited', {\r\n        messageId,\r\n        content: message.content,\r\n        isEdited: true,\r\n        editedAt: message.editedAt\r\n      });\r\n\r\n      logger.info(`Message ${messageId} edited by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error editing message:', error);\r\n      socket.emit('error', { message: 'Failed to edit message' });\r\n    }\r\n  }\r\n\r\n  private async handleDeleteMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, deleteForEveryone = false } = data;\r\n\r\n      if (!socket.userId || !messageId) {\r\n        socket.emit('error', { message: 'Invalid delete data' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        senderId: socket.userId,\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found or already deleted' });\r\n        return;\r\n      }\r\n\r\n      // Check if user can delete for everyone (within 1 hour)\r\n      const messageAge = Date.now() - message.createdAt.getTime();\r\n      if (deleteForEveryone && messageAge > 60 * 60 * 1000) {\r\n        socket.emit('error', { message: 'Message is too old to delete for everyone' });\r\n        return;\r\n      }\r\n\r\n      message.isDeleted = true;\r\n      message.deletedAt = new Date();\r\n      message.deletedBy = new Types.ObjectId(socket.userId);\r\n\r\n      if (deleteForEveryone) {\r\n        message.content = 'This message was deleted';\r\n      }\r\n\r\n      await message.save();\r\n\r\n      // Broadcast deletion to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_deleted', {\r\n        messageId,\r\n        deletedForEveryone: deleteForEveryone,\r\n        deletedBy: socket.userId\r\n      });\r\n\r\n      logger.info(`Message ${messageId} deleted by user ${socket.userId}`);\r\n    } catch (error) {\r\n      logger.error('Error deleting message:', error);\r\n      socket.emit('error', { message: 'Failed to delete message' });\r\n    }\r\n  }\r\n\r\n  private async handleMessageReaction(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, reaction } = data;\r\n\r\n      if (!socket.userId || !messageId || !reaction) {\r\n        socket.emit('error', { message: 'Invalid reaction data' });\r\n        return;\r\n      }\r\n\r\n      const validReactions = ['like', 'love', 'laugh', 'wow', 'sad', 'angry'];\r\n      if (!validReactions.includes(reaction)) {\r\n        socket.emit('error', { message: 'Invalid reaction type' });\r\n        return;\r\n      }\r\n\r\n      const message = await Message.findOne({\r\n        _id: messageId,\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!message) {\r\n        socket.emit('error', { message: 'Message not found' });\r\n        return;\r\n      }\r\n\r\n      // Verify user is participant in the conversation\r\n      const conversation = await Conversation.findOne({\r\n        _id: message.conversationId,\r\n        participants: socket.userId\r\n      });\r\n\r\n      if (!conversation) {\r\n        socket.emit('error', { message: 'Access denied' });\r\n        return;\r\n      }\r\n\r\n      // Check if user already reacted\r\n      const existingReactionIndex = message.reactions?.findIndex(\r\n        r => r.userId.toString() === socket.userId\r\n      ) ?? -1;\r\n\r\n      if (existingReactionIndex > -1) {\r\n        // Update existing reaction\r\n        message.reactions![existingReactionIndex].reaction = reaction;\r\n        message.reactions![existingReactionIndex].createdAt = new Date();\r\n      } else {\r\n        // Add new reaction\r\n        if (!message.reactions) {\r\n          message.reactions = [];\r\n        }\r\n        message.reactions.push({\r\n          userId: new Types.ObjectId(socket.userId),\r\n          reaction,\r\n          createdAt: new Date()\r\n        });\r\n      }\r\n\r\n      await message.save();\r\n\r\n      // Broadcast reaction to conversation\r\n      this.io.to(`conversation_${message.conversationId}`).emit('message_reaction', {\r\n        messageId,\r\n        userId: socket.userId,\r\n        reaction,\r\n        reactions: message.reactions\r\n      });\r\n\r\n      logger.info(`User ${socket.userId} reacted to message ${messageId} with ${reaction}`);\r\n    } catch (error) {\r\n      logger.error('Error reacting to message:', error);\r\n      socket.emit('error', { message: 'Failed to react to message' });\r\n    }\r\n  }\r\n\r\n  private handleUserConnect(socket: AuthenticatedSocket): void {\r\n    if (!socket.userId) return;\r\n\r\n    // Add to online users\r\n    this.onlineUsers.set(socket.userId, {\r\n      userId: socket.userId,\r\n      socketId: socket.id,\r\n      lastSeen: new Date(),\r\n      status: 'online'\r\n    });\r\n\r\n    // Track multiple sockets per user\r\n    const userSocketIds = this.userSockets.get(socket.userId) || [];\r\n    userSocketIds.push(socket.id);\r\n    this.userSockets.set(socket.userId, userSocketIds);\r\n\r\n    // Join user to their conversation rooms\r\n    this.joinUserConversations(socket);\r\n\r\n    // Broadcast online status to contacts\r\n    this.broadcastUserStatus(socket.userId, 'online');\r\n\r\n    // Send online users list to the connected user\r\n    socket.emit('online_users', Array.from(this.onlineUsers.values()));\r\n  }\r\n\r\n  private async joinUserConversations(socket: AuthenticatedSocket): Promise<void> {\r\n    if (!socket.userId) return;\r\n\r\n    try {\r\n      const conversations = await Conversation.find({\r\n        participants: socket.userId,\r\n        status: 'active'\r\n      }).select('_id');\r\n\r\n      conversations.forEach(conversation => {\r\n        socket.join(`conversation_${conversation._id}`);\r\n      });\r\n\r\n      logger.info(`User ${socket.userId} joined ${conversations.length} conversation rooms`);\r\n    } catch (error) {\r\n      logger.error('Error joining user conversations:', error);\r\n    }\r\n  }\r\n\r\n  private async handleSendMessage(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { conversationId, content, messageType = 'text', metadata } = data;\r\n\r\n      if (!socket.userId || !conversationId || !content) {\r\n        socket.emit('error', { message: 'Invalid message data' });\r\n        return;\r\n      }\r\n\r\n      // Verify user can send message to this conversation\r\n      const conversation = await Conversation.findById(conversationId);\r\n      if (!conversation || !conversation.canUserSendMessage(new Types.ObjectId(socket.userId))) {\r\n        socket.emit('error', { message: 'Cannot send message to this conversation' });\r\n        return;\r\n      }\r\n\r\n      // Get receiver ID (for direct conversations)\r\n      const receiverId = conversation.participants.find(p => p.toString() !== socket.userId);\r\n\r\n      // Create message\r\n      const message = new Message({\r\n        conversationId,\r\n        senderId: socket.userId,\r\n        receiverId,\r\n        messageType,\r\n        content: content.trim(),\r\n        metadata,\r\n        status: 'sent'\r\n      });\r\n\r\n      await message.save();\r\n\r\n      // Update conversation last message\r\n      await conversation.updateLastMessage(message);\r\n\r\n      // Populate message for response\r\n      const populatedMessage = await Message.findById(message._id)\r\n        .populate('senderId', 'firstName lastName avatar')\r\n        .populate('receiverId', 'firstName lastName avatar');\r\n\r\n      // Emit to conversation room\r\n      this.io.to(`conversation_${conversationId}`).emit('new_message', {\r\n        message: populatedMessage,\r\n        conversationId\r\n      });\r\n\r\n      // Send delivery confirmation to sender\r\n      socket.emit('message_sent', {\r\n        tempId: data.tempId,\r\n        message: populatedMessage\r\n      });\r\n\r\n      // Update message status to delivered if receiver is online\r\n      if (receiverId && this.onlineUsers.has(receiverId.toString())) {\r\n        message.status = 'delivered';\r\n        message.deliveredAt = new Date();\r\n        await message.save();\r\n\r\n        this.io.to(`conversation_${conversationId}`).emit('message_delivered', {\r\n          messageId: message._id,\r\n          deliveredAt: message.deliveredAt\r\n        });\r\n      }\r\n\r\n      logger.info(`Message sent from ${socket.userId} to conversation ${conversationId}`);\r\n    } catch (error) {\r\n      logger.error('Error sending message:', error);\r\n      socket.emit('error', { message: 'Failed to send message' });\r\n    }\r\n  }\r\n\r\n  private async handleMessageDelivered(_socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId } = data;\r\n\r\n      const message = await Message.findByIdAndUpdate(messageId, {\r\n        status: 'delivered',\r\n        deliveredAt: new Date()\r\n      }, { new: true });\r\n\r\n      if (message) {\r\n        this.io.to(`conversation_${message.conversationId}`).emit('message_delivered', {\r\n          messageId,\r\n          deliveredAt: message.deliveredAt\r\n        });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error updating message delivery status:', error);\r\n    }\r\n  }\r\n\r\n  private async handleMessageRead(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    try {\r\n      const { messageId, conversationId } = data;\r\n\r\n      if (!socket.userId) return;\r\n\r\n      // Update message status\r\n      const message = await Message.findByIdAndUpdate(messageId, {\r\n        status: 'read',\r\n        readAt: new Date()\r\n      }, { new: true });\r\n\r\n      if (message) {\r\n        // Update conversation unread count\r\n        const conversation = await Conversation.findById(conversationId);\r\n        if (conversation) {\r\n          await conversation.markAsRead(new Types.ObjectId(socket.userId), new Types.ObjectId(messageId));\r\n        }\r\n\r\n        // Notify sender about read status\r\n        this.io.to(`conversation_${conversationId}`).emit('message_read', {\r\n          messageId,\r\n          readAt: message.readAt,\r\n          readBy: socket.userId\r\n        });\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error updating message read status:', error);\r\n    }\r\n  }\r\n\r\n  private async handleTypingStart(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n    \r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    const typingKey = `${socket.userId}_${conversationId}`;\r\n    this.typingUsers.set(typingKey, {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      timestamp: new Date()\r\n    });\r\n\r\n    // Broadcast typing status to other participants\r\n    socket.to(`conversation_${conversationId}`).emit('user_typing', {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      isTyping: true\r\n    });\r\n  }\r\n\r\n  private async handleTypingStop(socket: AuthenticatedSocket, data: any): Promise<void> {\r\n    const { conversationId } = data;\r\n    \r\n    if (!socket.userId || !conversationId) return;\r\n\r\n    const typingKey = `${socket.userId}_${conversationId}`;\r\n    this.typingUsers.delete(typingKey);\r\n\r\n    // Broadcast typing stop to other participants\r\n    socket.to(`conversation_${conversationId}`).emit('user_typing', {\r\n      userId: socket.userId,\r\n      conversationId,\r\n      isTyping: false\r\n    });\r\n  }\r\n\r\n  private handleStatusChange(socket: AuthenticatedSocket, data: any): void {\r\n    const { status } = data;\r\n    \r\n    if (!socket.userId || !['online', 'away', 'busy'].includes(status)) return;\r\n\r\n    const user = this.onlineUsers.get(socket.userId);\r\n    if (user) {\r\n      user.status = status;\r\n      user.lastSeen = new Date();\r\n      this.onlineUsers.set(socket.userId, user);\r\n      \r\n      this.broadcastUserStatus(socket.userId, status);\r\n    }\r\n  }\r\n\r\n  private handleUserDisconnect(socket: AuthenticatedSocket): void {\r\n    if (!socket.userId) return;\r\n\r\n    logger.info(`User ${socket.userId} disconnected from socket ${socket.id}`);\r\n\r\n    // Remove socket from user's socket list\r\n    const userSocketIds = this.userSockets.get(socket.userId) || [];\r\n    const updatedSocketIds = userSocketIds.filter(id => id !== socket.id);\r\n    \r\n    if (updatedSocketIds.length > 0) {\r\n      this.userSockets.set(socket.userId, updatedSocketIds);\r\n    } else {\r\n      // User has no more active sockets\r\n      this.userSockets.delete(socket.userId);\r\n      this.onlineUsers.delete(socket.userId);\r\n      \r\n      // Broadcast offline status\r\n      this.broadcastUserStatus(socket.userId, 'offline');\r\n    }\r\n\r\n    // Clear typing status\r\n    for (const [key, typingUser] of this.typingUsers.entries()) {\r\n      if (typingUser.userId === socket.userId) {\r\n        this.typingUsers.delete(key);\r\n        socket.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {\r\n          userId: socket.userId,\r\n          conversationId: typingUser.conversationId,\r\n          isTyping: false\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  private broadcastUserStatus(userId: string, status: string): void {\r\n    this.io.emit('user_status_change', {\r\n      userId,\r\n      status,\r\n      lastSeen: new Date()\r\n    });\r\n  }\r\n\r\n  private startCleanupInterval(): void {\r\n    // Clean up old typing indicators every 30 seconds\r\n    setInterval(() => {\r\n      const now = new Date();\r\n      for (const [key, typingUser] of this.typingUsers.entries()) {\r\n        if (now.getTime() - typingUser.timestamp.getTime() > 30000) { // 30 seconds\r\n          this.typingUsers.delete(key);\r\n          this.io.to(`conversation_${typingUser.conversationId}`).emit('user_typing', {\r\n            userId: typingUser.userId,\r\n            conversationId: typingUser.conversationId,\r\n            isTyping: false\r\n          });\r\n        }\r\n      }\r\n    }, 30000);\r\n  }\r\n\r\n  // Public methods for external use\r\n  public getOnlineUsers(): OnlineUser[] {\r\n    return Array.from(this.onlineUsers.values());\r\n  }\r\n\r\n  public isUserOnline(userId: string): boolean {\r\n    return this.onlineUsers.has(userId);\r\n  }\r\n\r\n  public getUserStatus(userId: string): string {\r\n    const user = this.onlineUsers.get(userId);\r\n    return user ? user.status : 'offline';\r\n  }\r\n\r\n  public sendNotificationToUser(userId: string, notification: any): void {\r\n    const userSocketIds = this.userSockets.get(userId);\r\n    if (userSocketIds) {\r\n      userSocketIds.forEach(socketId => {\r\n        this.io.to(socketId).emit('notification', notification);\r\n      });\r\n    }\r\n  }\r\n\r\n  public getIO(): SocketIOServer {\r\n    return this.io;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8BU;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9BV,MAAAE,WAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA,MAAAC,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAG,eAAA,CAAAF,OAAA;AACA,MAAAG,UAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAI,YAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAK,cAAA;AAAA;AAAA,CAAAT,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAM,QAAA;AAAA;AAAA,CAAAV,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAqBA,MAAaO,aAAa;EAMxBC,YAAYC,MAAkB;IAAA;IAAAb,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAJtB,KAAAY,WAAW,GAA4B,IAAIC,GAAG,EAAE;IAAC;IAAAhB,cAAA,GAAAG,CAAA;IACjD,KAAAc,WAAW,GAA4B,IAAID,GAAG,EAAE;IAAC;IAAAhB,cAAA,GAAAG,CAAA;IACjD,KAAAe,WAAW,GAA0B,IAAIF,GAAG,EAAE,CAAC,CAAC;IAAA;IAAAhB,cAAA,GAAAG,CAAA;IAGtD,IAAI,CAACgB,EAAE,GAAG,IAAIjB,WAAA,CAAAkB,MAAc,CAACP,MAAM,EAAE;MACnCQ,IAAI,EAAE;QACJC,MAAM;QAAE;QAAA,CAAAtB,cAAA,GAAAuB,CAAA,UAAAC,OAAO,CAACC,GAAG,CAACC,YAAY;QAAA;QAAA,CAAA1B,cAAA,GAAAuB,CAAA,UAAI,uBAAuB;QAC3DI,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;QACxBC,WAAW,EAAE;OACd;MACDC,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS;KACpC,CAAC;IAAC;IAAA7B,cAAA,GAAAG,CAAA;IAEH,IAAI,CAAC2B,eAAe,EAAE;IAAC;IAAA9B,cAAA,GAAAG,CAAA;IACvB,IAAI,CAAC4B,kBAAkB,EAAE;IAAC;IAAA/B,cAAA,GAAAG,CAAA;IAC1B,IAAI,CAAC6B,oBAAoB,EAAE;EAC7B;EAEQF,eAAeA,CAAA;IAAA;IAAA9B,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACrB;IACA,IAAI,CAACgB,EAAE,CAACc,GAAG,CAAC,OAAOC,MAAW,EAAEC,IAAI,KAAI;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MACtC,IAAI;QACF,MAAMiC,KAAK;QAAA;QAAA,CAAApC,cAAA,GAAAG,CAAA;QAAG;QAAA,CAAAH,cAAA,GAAAuB,CAAA,UAAAW,MAAM,CAACG,SAAS,CAACC,IAAI,CAACF,KAAK;QAAA;QAAA,CAAApC,cAAA,GAAAuB,CAAA,UAAIW,MAAM,CAACG,SAAS,CAACE,OAAO,CAACC,aAAa,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAC;QAAAzC,cAAA,GAAAG,CAAA;QAEnG,IAAI,CAACiC,KAAK,EAAE;UAAA;UAAApC,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UACV,OAAOgC,IAAI,CAAC,IAAIO,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACzD,CAAC;QAAA;QAAA;UAAA1C,cAAA,GAAAuB,CAAA;QAAA;QAED,MAAMoB,OAAO;QAAA;QAAA,CAAA3C,cAAA,GAAAG,CAAA,QAAGE,cAAA,CAAAuC,OAAG,CAACC,MAAM,CAACT,KAAK,EAAEZ,OAAO,CAACC,GAAG,CAACqB,UAAW,CAAQ;QACjE,MAAMC,IAAI;QAAA;QAAA,CAAA/C,cAAA,GAAAG,CAAA,QAAG,MAAMK,YAAA,CAAAwC,IAAI,CAACC,QAAQ,CAACN,OAAO,CAACO,EAAE,CAAC,CAACC,MAAM,CAAC,WAAW,CAAC;QAAC;QAAAnD,cAAA,GAAAG,CAAA;QAEjE,IAAI,CAAC4C,IAAI,EAAE;UAAA;UAAA/C,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UACT,OAAOgC,IAAI,CAAC,IAAIO,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QAAA;QAAA;UAAA1C,cAAA,GAAAuB,CAAA;QAAA;QAAAvB,cAAA,GAAAG,CAAA;QAED+B,MAAM,CAACkB,MAAM,GAAIL,IAAY,CAACM,GAAG,CAACC,QAAQ,EAAE;QAAC;QAAAtD,cAAA,GAAAG,CAAA;QAC7C+B,MAAM,CAACa,IAAI,GAAGA,IAAI;QAAC;QAAA/C,cAAA,GAAAG,CAAA;QACnBgC,IAAI,EAAE;MACR,CAAC,CAAC,OAAOoB,KAAK,EAAE;QAAA;QAAAvD,cAAA,GAAAG,CAAA;QACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QAAC;QAAAvD,cAAA,GAAAG,CAAA;QACpDgC,IAAI,CAAC,IAAIO,KAAK,CAAC,uBAAuB,CAAC,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ;EAEQX,kBAAkBA,CAAA;IAAA;IAAA/B,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxB,IAAI,CAACgB,EAAE,CAACsC,EAAE,CAAC,YAAY,EAAGvB,MAA2B,IAAI;MAAA;MAAAlC,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MACvDO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,QAAQxB,MAAM,CAACkB,MAAM,0BAA0BlB,MAAM,CAACgB,EAAE,EAAE,CAAC;MAEvE;MAAA;MAAAlD,cAAA,GAAAG,CAAA;MACA,IAAI,CAACwD,iBAAiB,CAACzB,MAAM,CAAC;MAE9B;MAAA;MAAAlC,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC0D,iBAAiB,CAAC3B,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAC1E+B,MAAM,CAACuB,EAAE,CAAC,mBAAmB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC2D,sBAAsB,CAAC5B,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MACpF+B,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC4D,iBAAiB,CAAC7B,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAC1E+B,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC6D,iBAAiB,CAAC9B,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAC1E+B,MAAM,CAACuB,EAAE,CAAC,gBAAgB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC8D,mBAAmB,CAAC/B,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAC9E+B,MAAM,CAACuB,EAAE,CAAC,kBAAkB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAAC+D,qBAAqB,CAAChC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAEjF;MAAA;MAAA5D,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACuB,EAAE,CAAC,mBAAmB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACgE,sBAAsB,CAACjC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MACpF+B,MAAM,CAACuB,EAAE,CAAC,oBAAoB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACiE,uBAAuB,CAAClC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MACtF+B,MAAM,CAACuB,EAAE,CAAC,qBAAqB,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACkE,wBAAwB,CAACnC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAEvF;MAAA;MAAA5D,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACuB,EAAE,CAAC,cAAc,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACmE,iBAAiB,CAACpC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAC1E+B,MAAM,CAACuB,EAAE,CAAC,aAAa,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACoE,gBAAgB,CAACrC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAEvE;MAAA;MAAA5D,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACuB,EAAE,CAAC,eAAe,EAAGG,IAAI,IAAK;QAAA;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACqE,kBAAkB,CAACtC,MAAM,EAAE0B,IAAI,CAAC;MAAD,CAAC,CAAC;MAE3E;MAAA;MAAA5D,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACuB,EAAE,CAAC,YAAY,EAAE,MAAM;QAAA;QAAAzD,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,WAAI,CAACsE,oBAAoB,CAACvC,MAAM,CAAC;MAAD,CAAC,CAAC;IAClE,CAAC,CAAC;EACJ;EAEQ,MAAMiC,sBAAsBA,CAACjC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IACzE,MAAM;MAAE4D;IAAc,CAAE;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,QAAGyD,IAAI;IAAC;IAAA5D,cAAA,GAAAG,CAAA;IAEhC;IAAI;IAAA,CAAAH,cAAA,GAAAuB,CAAA,WAACW,MAAM,CAACkB,MAAM;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,UAAI,CAACmD,cAAc,GAAE;MAAA;MAAA1E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IAE9C,IAAI;MACF;MACA,MAAMwE,YAAY;MAAA;MAAA,CAAA3E,cAAA,GAAAG,CAAA,QAAG,MAAMM,cAAA,CAAAmE,YAAY,CAACC,OAAO,CAAC;QAC9CxB,GAAG,EAAEqB,cAAc;QACnBI,YAAY,EAAE5C,MAAM,CAACkB,MAAM;QAC3B2B,MAAM,EAAE;OACT,CAAC;MAAC;MAAA/E,cAAA,GAAAG,CAAA;MAEH,IAAIwE,YAAY,EAAE;QAAA;QAAA3E,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAChB+B,MAAM,CAAC8C,IAAI,CAAC,gBAAgBN,cAAc,EAAE,CAAC;QAAC;QAAA1E,cAAA,GAAAG,CAAA;QAC9CO,QAAA,CAAA8C,MAAM,CAACyB,KAAK,CAAC,QAAQ/C,MAAM,CAACkB,MAAM,wBAAwBsB,cAAc,EAAE,CAAC;QAAC;QAAA1E,cAAA,GAAAG,CAAA;QAE5E+B,MAAM,CAACgD,IAAI,CAAC,qBAAqB,EAAE;UAAER;QAAc,CAAE,CAAC;MACxD,CAAC,MAAM;QAAA;QAAA1E,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACL+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAA0B,CAAE,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MACnD+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAA6B,CAAE,CAAC;IAClE;EACF;EAEQ,MAAMf,uBAAuBA,CAAClC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAC1E,MAAM;MAAE4D;IAAc,CAAE;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,QAAGyD,IAAI;IAAC;IAAA5D,cAAA,GAAAG,CAAA;IAEhC,IAAI,CAACuE,cAAc,EAAE;MAAA;MAAA1E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IAE5B+B,MAAM,CAACkD,KAAK,CAAC,gBAAgBV,cAAc,EAAE,CAAC;IAAC;IAAA1E,cAAA,GAAAG,CAAA;IAC/CO,QAAA,CAAA8C,MAAM,CAACyB,KAAK,CAAC,QAAQ/C,MAAM,CAACkB,MAAM,sBAAsBsB,cAAc,EAAE,CAAC;IAAC;IAAA1E,cAAA,GAAAG,CAAA;IAE1E+B,MAAM,CAACgD,IAAI,CAAC,mBAAmB,EAAE;MAAER;IAAc,CAAE,CAAC;EACtD;EAEQ,MAAML,wBAAwBA,CAACnC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC3E,IAAI;MACF,MAAM;QAAEkF,cAAc;QAAEC,gBAAgB;QAAA;QAAA,CAAAtF,cAAA,GAAAuB,CAAA,WAAG,QAAQ;QAAEgE,KAAK;QAAEC;MAAO,CAAE;MAAA;MAAA,CAAAxF,cAAA,GAAAG,CAAA,QAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAE7E;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAAC8D,cAAc,GAAE;QAAA;QAAArF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACrC+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAA2B,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC/D;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAMkE,eAAe;MAAA;MAAA,CAAAzF,cAAA,GAAAG,CAAA,QAAG,CAAC,GAAG,IAAIuF,GAAG,CAAC,CAACxD,MAAM,CAACkB,MAAM,EAAE,GAAGiC,cAAc,CAAC,CAAC,CAAC;MAExE;MACA,MAAMV,YAAY;MAAA;MAAA,CAAA3E,cAAA,GAAAG,CAAA,QAAG,IAAIM,cAAA,CAAAmE,YAAY,CAAC;QACpCE,YAAY,EAAEW,eAAe,CAACE,GAAG,CAACzC,EAAE,IAAI;UAAA;UAAAlD,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAG,CAAA;UAAA,WAAII,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAAC3C,EAAE,CAAC;QAAD,CAAC,CAAC;QAC/D4C,kBAAkB,EAAEL,eAAe,CAACE,GAAG,CAACI,aAAa,IAAK;UAAA;UAAA/F,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAG,CAAA;UAAA;YACxDiD,MAAM,EAAE,IAAI7C,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAACE,aAAa,CAAC;YACzCC,QAAQ,EAAE,IAAIC,IAAI,EAAE;YACpBC,IAAI,EAAEH,aAAa,KAAK7D,MAAM,CAACkB,MAAM;YAAA;YAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAG,OAAO;YAAA;YAAA,CAAAvB,cAAA,GAAAuB,CAAA,WAAG,QAAQ;YAC1D4E,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,IAAIH,IAAI,EAAE;YACtBI,WAAW,EAAE,CAAC;YACdC,OAAO,EAAE;WACV;SAAC,CAAC;QACHhB,gBAAgB;QAChBC,KAAK,EAAED,gBAAgB,KAAK,OAAO;QAAA;QAAA,CAAAtF,cAAA,GAAAuB,CAAA,WAAGgE,KAAK;QAAA;QAAA,CAAAvF,cAAA,GAAAuB,CAAA,WAAGgF,SAAS;QACvDf,OAAO,EAAEA,OAAO;QAAA;QAAA,CAAAxF,cAAA,GAAAuB,CAAA,WAAG,IAAIhB,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAACL,OAAO,CAAC;QAAA;QAAA,CAAAxF,cAAA,GAAAuB,CAAA,WAAGgF,SAAS;QAC1DC,QAAQ,EAAE;UACRC,gBAAgB,EAAE,IAAI;UACtBC,oBAAoB,EAAE,IAAI;UAC1BC,oBAAoB,EAAE,IAAI;UAC1BC,eAAe,EAAEtB,gBAAgB,KAAK,QAAQ;UAAA;UAAA,CAAAtF,cAAA,GAAAuB,CAAA,WAAG,CAAC;UAAA;UAAA,CAAAvB,cAAA,GAAAuB,CAAA,WAAG,EAAE;UACvDsF,kBAAkB,EAAE,KAAK;UACzBC,4BAA4B,EAAE;SAC/B;QACDC,SAAS,EAAE;UACTC,aAAa,EAAE,CAAC;UAChBC,iBAAiB,EAAExB,eAAe,CAACyB,MAAM;UACzCC,mBAAmB,EAAE,CAAC;UACtBC,cAAc,EAAE,IAAInB,IAAI,EAAE;UAC1BoB,gBAAgB,EAAE,CAAC;UACnBC,iBAAiB,EAAE;;OAEtB,CAAC;MAAC;MAAAtH,cAAA,GAAAG,CAAA;MAEH,MAAMwE,YAAY,CAAC4C,IAAI,EAAE;MAEzB;MAAA;MAAAvH,cAAA,GAAAG,CAAA;MACAsF,eAAe,CAAC+B,OAAO,CAACzB,aAAa,IAAG;QAAA;QAAA/F,cAAA,GAAAc,CAAA;QACtC,MAAM2G,aAAa;QAAA;QAAA,CAAAzH,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACe,WAAW,CAACwG,GAAG,CAAC3B,aAAa,CAAC;QAAC;QAAA/F,cAAA,GAAAG,CAAA;QAC1D,IAAIsH,aAAa,EAAE;UAAA;UAAAzH,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UACjBsH,aAAa,CAACD,OAAO,CAACG,QAAQ,IAAG;YAAA;YAAA3H,cAAA,GAAAc,CAAA;YAAAd,cAAA,GAAAG,CAAA;YAC/B,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAACD,QAAQ,CAAC,CAACE,WAAW,CAAC,gBAAgBlD,YAAY,CAACtB,GAAG,EAAE,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC;QAAA;QAAA;UAAArD,cAAA,GAAAuB,CAAA;QAAA;MACH,CAAC,CAAC;MAEF;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBjD,YAAY,CAACtB,GAAG,EAAE,CAAC,CAAC6B,IAAI,CAAC,sBAAsB,EAAE;QAC1EP,YAAY,EAAEA,YAAY,CAACmD,QAAQ;OACpC,CAAC;MAAC;MAAA9H,cAAA,GAAAG,CAAA;MAEHO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,gBAAgBiB,YAAY,CAACtB,GAAG,oBAAoBnB,MAAM,CAACkB,MAAM,EAAE,CAAC;IAClF,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MACpD+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAA+B,CAAE,CAAC;IACpE;EACF;EAEQ,MAAMnB,iBAAiBA,CAAC9B,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACpE,IAAI;MACF,MAAM;QAAE4H,SAAS;QAAEC;MAAO,CAAE;MAAA;MAAA,CAAAhI,cAAA,GAAAG,CAAA,QAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAEpC;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACwG,SAAS;MAAA;MAAA,CAAA/H,cAAA,GAAAuB,CAAA,WAAI,CAACyG,OAAO,GAAE;QAAA;QAAAhI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAC5C+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAmB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QACvD;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED,MAAM4D,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAACpD,OAAO,CAAC;QACpCxB,GAAG,EAAE0E,SAAS;QACdG,QAAQ,EAAEhG,MAAM,CAACkB,MAAM;QACvB+E,WAAW,EAAE,MAAM;QACnBC,SAAS,EAAE;OACZ,CAAC;MAAC;MAAApI,cAAA,GAAAG,CAAA;MAEH,IAAI,CAACgF,OAAO,EAAE;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACZ+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAuC,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC3E;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAM8G,UAAU;MAAA;MAAA,CAAArI,cAAA,GAAAG,CAAA,SAAG8F,IAAI,CAACqC,GAAG,EAAE,GAAGnD,OAAO,CAACoD,SAAS,CAACC,OAAO,EAAE;MAAC;MAAAxI,cAAA,GAAAG,CAAA;MAC5D,IAAIkI,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QAAA;QAAArI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACpC+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAA4B,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAChE;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MAAAvB,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgF,OAAO,CAACsD,QAAQ,EAAE;QAAA;QAAAzI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACrBgF,OAAO,CAACuD,eAAe,GAAGvD,OAAO,CAAC6C,OAAO;MAC3C,CAAC;MAAA;MAAA;QAAAhI,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAEDgF,OAAO,CAAC6C,OAAO,GAAGA,OAAO,CAACW,IAAI,EAAE;MAAC;MAAA3I,cAAA,GAAAG,CAAA;MACjCgF,OAAO,CAACsD,QAAQ,GAAG,IAAI;MAAC;MAAAzI,cAAA,GAAAG,CAAA;MACxBgF,OAAO,CAACyD,QAAQ,GAAG,IAAI3C,IAAI,EAAE;MAAC;MAAAjG,cAAA,GAAAG,CAAA;MAE9B,MAAMgF,OAAO,CAACoC,IAAI,EAAE;MAEpB;MAAA;MAAAvH,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBzC,OAAO,CAACT,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,gBAAgB,EAAE;QAC1E6C,SAAS;QACTC,OAAO,EAAE7C,OAAO,CAAC6C,OAAO;QACxBS,QAAQ,EAAE,IAAI;QACdG,QAAQ,EAAEzD,OAAO,CAACyD;OACnB,CAAC;MAAC;MAAA5I,cAAA,GAAAG,CAAA;MAEHO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,WAAWqE,SAAS,mBAAmB7F,MAAM,CAACkB,MAAM,EAAE,CAAC;IACrE,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MAC9C+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAwB,CAAE,CAAC;IAC7D;EACF;EAEQ,MAAMlB,mBAAmBA,CAAC/B,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACtE,IAAI;MACF,MAAM;QAAE4H,SAAS;QAAEc,iBAAiB;QAAA;QAAA,CAAA7I,cAAA,GAAAuB,CAAA,WAAG,KAAK;MAAA,CAAE;MAAA;MAAA,CAAAvB,cAAA,GAAAG,CAAA,SAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAEtD;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACwG,SAAS,GAAE;QAAA;QAAA/H,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAChC+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAqB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QACzD;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED,MAAM4D,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAACpD,OAAO,CAAC;QACpCxB,GAAG,EAAE0E,SAAS;QACdG,QAAQ,EAAEhG,MAAM,CAACkB,MAAM;QACvBgF,SAAS,EAAE;OACZ,CAAC;MAAC;MAAApI,cAAA,GAAAG,CAAA;MAEH,IAAI,CAACgF,OAAO,EAAE;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACZ+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAsC,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC1E;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAM8G,UAAU;MAAA;MAAA,CAAArI,cAAA,GAAAG,CAAA,SAAG8F,IAAI,CAACqC,GAAG,EAAE,GAAGnD,OAAO,CAACoD,SAAS,CAACC,OAAO,EAAE;MAAC;MAAAxI,cAAA,GAAAG,CAAA;MAC5D;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,WAAAsH,iBAAiB;MAAA;MAAA,CAAA7I,cAAA,GAAAuB,CAAA,WAAI8G,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAE;QAAA;QAAArI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACpD+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAA2C,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC/E;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAEDgF,OAAO,CAACiD,SAAS,GAAG,IAAI;MAAC;MAAApI,cAAA,GAAAG,CAAA;MACzBgF,OAAO,CAAC2D,SAAS,GAAG,IAAI7C,IAAI,EAAE;MAAC;MAAAjG,cAAA,GAAAG,CAAA;MAC/BgF,OAAO,CAAC4D,SAAS,GAAG,IAAIxI,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAAC3D,MAAM,CAACkB,MAAM,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAEtD,IAAI0I,iBAAiB,EAAE;QAAA;QAAA7I,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACrBgF,OAAO,CAAC6C,OAAO,GAAG,0BAA0B;MAC9C,CAAC;MAAA;MAAA;QAAAhI,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAED,MAAMgF,OAAO,CAACoC,IAAI,EAAE;MAEpB;MAAA;MAAAvH,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBzC,OAAO,CAACT,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,iBAAiB,EAAE;QAC3E6C,SAAS;QACTiB,kBAAkB,EAAEH,iBAAiB;QACrCE,SAAS,EAAE7G,MAAM,CAACkB;OACnB,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAEHO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,WAAWqE,SAAS,oBAAoB7F,MAAM,CAACkB,MAAM,EAAE,CAAC;IACtE,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MAC/C+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAA0B,CAAE,CAAC;IAC/D;EACF;EAEQ,MAAMjB,qBAAqBA,CAAChC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxE,IAAI;MACF,MAAM;QAAE4H,SAAS;QAAEkB;MAAQ,CAAE;MAAA;MAAA,CAAAjJ,cAAA,GAAAG,CAAA,SAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAErC;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACwG,SAAS;MAAA;MAAA,CAAA/H,cAAA,GAAAuB,CAAA,WAAI,CAAC0H,QAAQ,GAAE;QAAA;QAAAjJ,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAC7C+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAuB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC3D;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED,MAAM2H,cAAc;MAAA;MAAA,CAAAlJ,cAAA,GAAAG,CAAA,SAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;MAAC;MAAAH,cAAA,GAAAG,CAAA;MACxE,IAAI,CAAC+I,cAAc,CAACC,QAAQ,CAACF,QAAQ,CAAC,EAAE;QAAA;QAAAjJ,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACtC+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAuB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC3D;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED,MAAM4D,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAACpD,OAAO,CAAC;QACpCxB,GAAG,EAAE0E,SAAS;QACdK,SAAS,EAAE;OACZ,CAAC;MAAC;MAAApI,cAAA,GAAAG,CAAA;MAEH,IAAI,CAACgF,OAAO,EAAE;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACZ+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAmB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QACvD;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAMoD,YAAY;MAAA;MAAA,CAAA3E,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAmE,YAAY,CAACC,OAAO,CAAC;QAC9CxB,GAAG,EAAE8B,OAAO,CAACT,cAAc;QAC3BI,YAAY,EAAE5C,MAAM,CAACkB;OACtB,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAEH,IAAI,CAACwE,YAAY,EAAE;QAAA;QAAA3E,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACjB+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAe,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QACnD;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAM6H,qBAAqB;MAAA;MAAA,CAAApJ,cAAA,GAAAG,CAAA;MAAG;MAAA,CAAAH,cAAA,GAAAuB,CAAA,WAAA4D,OAAO,CAACkE,SAAS,EAAEC,SAAS,CACxDC,CAAC,IAAI;QAAA;QAAAvJ,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,OAAAoJ,CAAC,CAACnG,MAAM,CAACE,QAAQ,EAAE,KAAKpB,MAAM,CAACkB,MAAM;MAAN,CAAM,CAC3C;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAAC,CAAC;MAAC;MAAAvB,cAAA,GAAAG,CAAA;MAER,IAAIiJ,qBAAqB,GAAG,CAAC,CAAC,EAAE;QAAA;QAAApJ,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAC9B;QACAgF,OAAO,CAACkE,SAAU,CAACD,qBAAqB,CAAC,CAACH,QAAQ,GAAGA,QAAQ;QAAC;QAAAjJ,cAAA,GAAAG,CAAA;QAC9DgF,OAAO,CAACkE,SAAU,CAACD,qBAAqB,CAAC,CAACb,SAAS,GAAG,IAAItC,IAAI,EAAE;MAClE,CAAC,MAAM;QAAA;QAAAjG,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACL;QACA,IAAI,CAACgF,OAAO,CAACkE,SAAS,EAAE;UAAA;UAAArJ,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UACtBgF,OAAO,CAACkE,SAAS,GAAG,EAAE;QACxB,CAAC;QAAA;QAAA;UAAArJ,cAAA,GAAAuB,CAAA;QAAA;QAAAvB,cAAA,GAAAG,CAAA;QACDgF,OAAO,CAACkE,SAAS,CAACG,IAAI,CAAC;UACrBpG,MAAM,EAAE,IAAI7C,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAAC3D,MAAM,CAACkB,MAAM,CAAC;UACzC6F,QAAQ;UACRV,SAAS,EAAE,IAAItC,IAAI;SACpB,CAAC;MACJ;MAAC;MAAAjG,cAAA,GAAAG,CAAA;MAED,MAAMgF,OAAO,CAACoC,IAAI,EAAE;MAEpB;MAAA;MAAAvH,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBzC,OAAO,CAACT,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,kBAAkB,EAAE;QAC5E6C,SAAS;QACT3E,MAAM,EAAElB,MAAM,CAACkB,MAAM;QACrB6F,QAAQ;QACRI,SAAS,EAAElE,OAAO,CAACkE;OACpB,CAAC;MAAC;MAAArJ,cAAA,GAAAG,CAAA;MAEHO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,QAAQxB,MAAM,CAACkB,MAAM,uBAAuB2E,SAAS,SAASkB,QAAQ,EAAE,CAAC;IACvF,CAAC,CAAC,OAAO1F,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MAClD+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAA4B,CAAE,CAAC;IACjE;EACF;EAEQxB,iBAAiBA,CAACzB,MAA2B;IAAA;IAAAlC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACnD,IAAI,CAAC+B,MAAM,CAACkB,MAAM,EAAE;MAAA;MAAApD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAE3B;IAAAvB,cAAA,GAAAG,CAAA;IACA,IAAI,CAACY,WAAW,CAAC0I,GAAG,CAACvH,MAAM,CAACkB,MAAM,EAAE;MAClCA,MAAM,EAAElB,MAAM,CAACkB,MAAM;MACrBuE,QAAQ,EAAEzF,MAAM,CAACgB,EAAE;MACnBwG,QAAQ,EAAE,IAAIzD,IAAI,EAAE;MACpBlB,MAAM,EAAE;KACT,CAAC;IAEF;IACA,MAAM0C,aAAa;IAAA;IAAA,CAAAzH,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAuB,CAAA,eAAI,CAACL,WAAW,CAACwG,GAAG,CAACxF,MAAM,CAACkB,MAAM,CAAC;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,EAAE;IAAC;IAAAvB,cAAA,GAAAG,CAAA;IAChEsH,aAAa,CAAC+B,IAAI,CAACtH,MAAM,CAACgB,EAAE,CAAC;IAAC;IAAAlD,cAAA,GAAAG,CAAA;IAC9B,IAAI,CAACe,WAAW,CAACuI,GAAG,CAACvH,MAAM,CAACkB,MAAM,EAAEqE,aAAa,CAAC;IAElD;IAAA;IAAAzH,cAAA,GAAAG,CAAA;IACA,IAAI,CAACwJ,qBAAqB,CAACzH,MAAM,CAAC;IAElC;IAAA;IAAAlC,cAAA,GAAAG,CAAA;IACA,IAAI,CAACyJ,mBAAmB,CAAC1H,MAAM,CAACkB,MAAM,EAAE,QAAQ,CAAC;IAEjD;IAAA;IAAApD,cAAA,GAAAG,CAAA;IACA+B,MAAM,CAACgD,IAAI,CAAC,cAAc,EAAE2E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,MAAM,EAAE,CAAC,CAAC;EACpE;EAEQ,MAAMJ,qBAAqBA,CAACzH,MAA2B;IAAA;IAAAlC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC7D,IAAI,CAAC+B,MAAM,CAACkB,MAAM,EAAE;MAAA;MAAApD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IAE3B,IAAI;MACF,MAAM6J,aAAa;MAAA;MAAA,CAAAhK,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAmE,YAAY,CAACqF,IAAI,CAAC;QAC5CnF,YAAY,EAAE5C,MAAM,CAACkB,MAAM;QAC3B2B,MAAM,EAAE;OACT,CAAC,CAAC5B,MAAM,CAAC,KAAK,CAAC;MAAC;MAAAnD,cAAA,GAAAG,CAAA;MAEjB6J,aAAa,CAACxC,OAAO,CAAC7C,YAAY,IAAG;QAAA;QAAA3E,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QACnC+B,MAAM,CAAC8C,IAAI,CAAC,gBAAgBL,YAAY,CAACtB,GAAG,EAAE,CAAC;MACjD,CAAC,CAAC;MAAC;MAAArD,cAAA,GAAAG,CAAA;MAEHO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,QAAQxB,MAAM,CAACkB,MAAM,WAAW4G,aAAa,CAAC9C,MAAM,qBAAqB,CAAC;IACxF,CAAC,CAAC,OAAO3D,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC1D;EACF;EAEQ,MAAMM,iBAAiBA,CAAC3B,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACpE,IAAI;MACF,MAAM;QAAEuE,cAAc;QAAEsD,OAAO;QAAEG,WAAW;QAAA;QAAA,CAAAnI,cAAA,GAAAuB,CAAA,WAAG,MAAM;QAAE2I;MAAQ,CAAE;MAAA;MAAA,CAAAlK,cAAA,GAAAG,CAAA,SAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAEzE;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;MAAA;MAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACmD,cAAc;MAAA;MAAA,CAAA1E,cAAA,GAAAuB,CAAA,WAAI,CAACyG,OAAO,GAAE;QAAA;QAAAhI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACjD+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAsB,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC1D;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAMoD,YAAY;MAAA;MAAA,CAAA3E,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAmE,YAAY,CAAC3B,QAAQ,CAACyB,cAAc,CAAC;MAAC;MAAA1E,cAAA,GAAAG,CAAA;MACjE;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACoD,YAAY;MAAA;MAAA,CAAA3E,cAAA,GAAAuB,CAAA,WAAI,CAACoD,YAAY,CAACwF,kBAAkB,CAAC,IAAI5J,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAAC3D,MAAM,CAACkB,MAAM,CAAC,CAAC,GAAE;QAAA;QAAApD,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACxF+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAA0C,CAAE,CAAC;QAAC;QAAAnF,cAAA,GAAAG,CAAA;QAC9E;MACF,CAAC;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAED;MACA,MAAM6I,UAAU;MAAA;MAAA,CAAApK,cAAA,GAAAG,CAAA,SAAGwE,YAAY,CAACG,YAAY,CAACmF,IAAI,CAACI,CAAC,IAAI;QAAA;QAAArK,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAAA,OAAAkK,CAAC,CAAC/G,QAAQ,EAAE,KAAKpB,MAAM,CAACkB,MAAM;MAAN,CAAM,CAAC;MAEtF;MACA,MAAM+B,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,IAAIM,cAAA,CAAAwH,OAAO,CAAC;QAC1BvD,cAAc;QACdwD,QAAQ,EAAEhG,MAAM,CAACkB,MAAM;QACvBgH,UAAU;QACVjC,WAAW;QACXH,OAAO,EAAEA,OAAO,CAACW,IAAI,EAAE;QACvBuB,QAAQ;QACRnF,MAAM,EAAE;OACT,CAAC;MAAC;MAAA/E,cAAA,GAAAG,CAAA;MAEH,MAAMgF,OAAO,CAACoC,IAAI,EAAE;MAEpB;MAAA;MAAAvH,cAAA,GAAAG,CAAA;MACA,MAAMwE,YAAY,CAAC2F,iBAAiB,CAACnF,OAAO,CAAC;MAE7C;MACA,MAAMoF,gBAAgB;MAAA;MAAA,CAAAvK,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAAChF,QAAQ,CAACkC,OAAO,CAAC9B,GAAG,CAAC,CACzDmH,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC,CACjDA,QAAQ,CAAC,YAAY,EAAE,2BAA2B,CAAC;MAEtD;MAAA;MAAAxK,cAAA,GAAAG,CAAA;MACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBlD,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,aAAa,EAAE;QAC/DC,OAAO,EAAEoF,gBAAgB;QACzB7F;OACD,CAAC;MAEF;MAAA;MAAA1E,cAAA,GAAAG,CAAA;MACA+B,MAAM,CAACgD,IAAI,CAAC,cAAc,EAAE;QAC1BuF,MAAM,EAAE7G,IAAI,CAAC6G,MAAM;QACnBtF,OAAO,EAAEoF;OACV,CAAC;MAEF;MAAA;MAAAvK,cAAA,GAAAG,CAAA;MACA;MAAI;MAAA,CAAAH,cAAA,GAAAuB,CAAA,WAAA6I,UAAU;MAAA;MAAA,CAAApK,cAAA,GAAAuB,CAAA,WAAI,IAAI,CAACR,WAAW,CAAC2J,GAAG,CAACN,UAAU,CAAC9G,QAAQ,EAAE,CAAC,GAAE;QAAA;QAAAtD,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAC7DgF,OAAO,CAACJ,MAAM,GAAG,WAAW;QAAC;QAAA/E,cAAA,GAAAG,CAAA;QAC7BgF,OAAO,CAACwF,WAAW,GAAG,IAAI1E,IAAI,EAAE;QAAC;QAAAjG,cAAA,GAAAG,CAAA;QACjC,MAAMgF,OAAO,CAACoC,IAAI,EAAE;QAAC;QAAAvH,cAAA,GAAAG,CAAA;QAErB,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBlD,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,mBAAmB,EAAE;UACrE6C,SAAS,EAAE5C,OAAO,CAAC9B,GAAG;UACtBsH,WAAW,EAAExF,OAAO,CAACwF;SACtB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA3K,cAAA,GAAAuB,CAAA;MAAA;MAAAvB,cAAA,GAAAG,CAAA;MAEDO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,qBAAqBxB,MAAM,CAACkB,MAAM,oBAAoBsB,cAAc,EAAE,CAAC;IACrF,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAAC;MAAAvD,cAAA,GAAAG,CAAA;MAC9C+B,MAAM,CAACgD,IAAI,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAwB,CAAE,CAAC;IAC7D;EACF;EAEQ,MAAMrB,sBAAsBA,CAAC8G,OAA4B,EAAEhH,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC1E,IAAI;MACF,MAAM;QAAE4H;MAAS,CAAE;MAAA;MAAA,CAAA/H,cAAA,GAAAG,CAAA,SAAGyD,IAAI;MAE1B,MAAMuB,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAAC4C,iBAAiB,CAAC9C,SAAS,EAAE;QACzDhD,MAAM,EAAE,WAAW;QACnB4F,WAAW,EAAE,IAAI1E,IAAI;OACtB,EAAE;QAAE6E,GAAG,EAAE;MAAI,CAAE,CAAC;MAAC;MAAA9K,cAAA,GAAAG,CAAA;MAElB,IAAIgF,OAAO,EAAE;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACX,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBzC,OAAO,CAACT,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,mBAAmB,EAAE;UAC7E6C,SAAS;UACT4C,WAAW,EAAExF,OAAO,CAACwF;SACtB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA3K,cAAA,GAAAuB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAChE;EACF;EAEQ,MAAMQ,iBAAiBA,CAAC7B,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACpE,IAAI;MACF,MAAM;QAAE4H,SAAS;QAAErD;MAAc,CAAE;MAAA;MAAA,CAAA1E,cAAA,GAAAG,CAAA,SAAGyD,IAAI;MAAC;MAAA5D,cAAA,GAAAG,CAAA;MAE3C,IAAI,CAAC+B,MAAM,CAACkB,MAAM,EAAE;QAAA;QAAApD,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QAAA;MAAA,CAAO;MAAA;MAAA;QAAAH,cAAA,GAAAuB,CAAA;MAAA;MAE3B;MACA,MAAM4D,OAAO;MAAA;MAAA,CAAAnF,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAwH,OAAO,CAAC4C,iBAAiB,CAAC9C,SAAS,EAAE;QACzDhD,MAAM,EAAE,MAAM;QACdgG,MAAM,EAAE,IAAI9E,IAAI;OACjB,EAAE;QAAE6E,GAAG,EAAE;MAAI,CAAE,CAAC;MAAC;MAAA9K,cAAA,GAAAG,CAAA;MAElB,IAAIgF,OAAO,EAAE;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QACX;QACA,MAAMoD,YAAY;QAAA;QAAA,CAAA3E,cAAA,GAAAG,CAAA,SAAG,MAAMM,cAAA,CAAAmE,YAAY,CAAC3B,QAAQ,CAACyB,cAAc,CAAC;QAAC;QAAA1E,cAAA,GAAAG,CAAA;QACjE,IAAIwE,YAAY,EAAE;UAAA;UAAA3E,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UAChB,MAAMwE,YAAY,CAACqG,UAAU,CAAC,IAAIzK,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAAC3D,MAAM,CAACkB,MAAM,CAAC,EAAE,IAAI7C,UAAA,CAAAqF,KAAK,CAACC,QAAQ,CAACkC,SAAS,CAAC,CAAC;QACjG,CAAC;QAAA;QAAA;UAAA/H,cAAA,GAAAuB,CAAA;QAAA;QAED;QAAAvB,cAAA,GAAAG,CAAA;QACA,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgBlD,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,cAAc,EAAE;UAChE6C,SAAS;UACTgD,MAAM,EAAE5F,OAAO,CAAC4F,MAAM;UACtBE,MAAM,EAAE/I,MAAM,CAACkB;SAChB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApD,cAAA,GAAAuB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA;MAAAvD,cAAA,GAAAG,CAAA;MACdO,QAAA,CAAA8C,MAAM,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC5D;EACF;EAEQ,MAAMe,iBAAiBA,CAACpC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IACpE,MAAM;MAAE4D;IAAc,CAAE;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,SAAGyD,IAAI;IAAC;IAAA5D,cAAA,GAAAG,CAAA;IAEhC;IAAI;IAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACmD,cAAc,GAAE;MAAA;MAAA1E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAE9C,MAAM2J,SAAS;IAAA;IAAA,CAAAlL,cAAA,GAAAG,CAAA,SAAG,GAAG+B,MAAM,CAACkB,MAAM,IAAIsB,cAAc,EAAE;IAAC;IAAA1E,cAAA,GAAAG,CAAA;IACvD,IAAI,CAACc,WAAW,CAACwI,GAAG,CAACyB,SAAS,EAAE;MAC9B9H,MAAM,EAAElB,MAAM,CAACkB,MAAM;MACrBsB,cAAc;MACdyG,SAAS,EAAE,IAAIlF,IAAI;KACpB,CAAC;IAEF;IAAA;IAAAjG,cAAA,GAAAG,CAAA;IACA+B,MAAM,CAAC0F,EAAE,CAAC,gBAAgBlD,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,aAAa,EAAE;MAC9D9B,MAAM,EAAElB,MAAM,CAACkB,MAAM;MACrBsB,cAAc;MACd0G,QAAQ,EAAE;KACX,CAAC;EACJ;EAEQ,MAAM7G,gBAAgBA,CAACrC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IACnE,MAAM;MAAE4D;IAAc,CAAE;IAAA;IAAA,CAAA1E,cAAA,GAAAG,CAAA,SAAGyD,IAAI;IAAC;IAAA5D,cAAA,GAAAG,CAAA;IAEhC;IAAI;IAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAACmD,cAAc,GAAE;MAAA;MAAA1E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAE9C,MAAM2J,SAAS;IAAA;IAAA,CAAAlL,cAAA,GAAAG,CAAA,SAAG,GAAG+B,MAAM,CAACkB,MAAM,IAAIsB,cAAc,EAAE;IAAC;IAAA1E,cAAA,GAAAG,CAAA;IACvD,IAAI,CAACc,WAAW,CAACoK,MAAM,CAACH,SAAS,CAAC;IAElC;IAAA;IAAAlL,cAAA,GAAAG,CAAA;IACA+B,MAAM,CAAC0F,EAAE,CAAC,gBAAgBlD,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,aAAa,EAAE;MAC9D9B,MAAM,EAAElB,MAAM,CAACkB,MAAM;MACrBsB,cAAc;MACd0G,QAAQ,EAAE;KACX,CAAC;EACJ;EAEQ5G,kBAAkBA,CAACtC,MAA2B,EAAE0B,IAAS;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAC/D,MAAM;MAAEiE;IAAM,CAAE;IAAA;IAAA,CAAA/E,cAAA,GAAAG,CAAA,SAAGyD,IAAI;IAAC;IAAA5D,cAAA,GAAAG,CAAA;IAExB;IAAI;IAAA,CAAAH,cAAA,GAAAuB,CAAA,YAACW,MAAM,CAACkB,MAAM;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC4H,QAAQ,CAACpE,MAAM,CAAC,GAAE;MAAA;MAAA/E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAE3E,MAAMwB,IAAI;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACY,WAAW,CAAC2G,GAAG,CAACxF,MAAM,CAACkB,MAAM,CAAC;IAAC;IAAApD,cAAA,GAAAG,CAAA;IACjD,IAAI4C,IAAI,EAAE;MAAA;MAAA/C,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MACR4C,IAAI,CAACgC,MAAM,GAAGA,MAAM;MAAC;MAAA/E,cAAA,GAAAG,CAAA;MACrB4C,IAAI,CAAC2G,QAAQ,GAAG,IAAIzD,IAAI,EAAE;MAAC;MAAAjG,cAAA,GAAAG,CAAA;MAC3B,IAAI,CAACY,WAAW,CAAC0I,GAAG,CAACvH,MAAM,CAACkB,MAAM,EAAEL,IAAI,CAAC;MAAC;MAAA/C,cAAA,GAAAG,CAAA;MAE1C,IAAI,CAACyJ,mBAAmB,CAAC1H,MAAM,CAACkB,MAAM,EAAE2B,MAAM,CAAC;IACjD,CAAC;IAAA;IAAA;MAAA/E,cAAA,GAAAuB,CAAA;IAAA;EACH;EAEQkD,oBAAoBA,CAACvC,MAA2B;IAAA;IAAAlC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACtD,IAAI,CAAC+B,MAAM,CAACkB,MAAM,EAAE;MAAA;MAAApD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAAA;IAAA,CAAO;IAAA;IAAA;MAAAH,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IAE3BO,QAAA,CAAA8C,MAAM,CAACE,IAAI,CAAC,QAAQxB,MAAM,CAACkB,MAAM,6BAA6BlB,MAAM,CAACgB,EAAE,EAAE,CAAC;IAE1E;IACA,MAAMuE,aAAa;IAAA;IAAA,CAAAzH,cAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,cAAA,GAAAuB,CAAA,eAAI,CAACL,WAAW,CAACwG,GAAG,CAACxF,MAAM,CAACkB,MAAM,CAAC;IAAA;IAAA,CAAApD,cAAA,GAAAuB,CAAA,WAAI,EAAE;IAC/D,MAAM+J,gBAAgB;IAAA;IAAA,CAAAtL,cAAA,GAAAG,CAAA,SAAGsH,aAAa,CAAC8D,MAAM,CAACrI,EAAE,IAAI;MAAA;MAAAlD,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAG,CAAA;MAAA,OAAA+C,EAAE,KAAKhB,MAAM,CAACgB,EAAE;IAAF,CAAE,CAAC;IAAC;IAAAlD,cAAA,GAAAG,CAAA;IAEtE,IAAImL,gBAAgB,CAACpE,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAlH,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MAC/B,IAAI,CAACe,WAAW,CAACuI,GAAG,CAACvH,MAAM,CAACkB,MAAM,EAAEkI,gBAAgB,CAAC;IACvD,CAAC,MAAM;MAAA;MAAAtL,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MACL;MACA,IAAI,CAACe,WAAW,CAACmK,MAAM,CAACnJ,MAAM,CAACkB,MAAM,CAAC;MAAC;MAAApD,cAAA,GAAAG,CAAA;MACvC,IAAI,CAACY,WAAW,CAACsK,MAAM,CAACnJ,MAAM,CAACkB,MAAM,CAAC;MAEtC;MAAA;MAAApD,cAAA,GAAAG,CAAA;MACA,IAAI,CAACyJ,mBAAmB,CAAC1H,MAAM,CAACkB,MAAM,EAAE,SAAS,CAAC;IACpD;IAEA;IAAA;IAAApD,cAAA,GAAAG,CAAA;IACA,KAAK,MAAM,CAACqL,GAAG,EAAEC,UAAU,CAAC,IAAI,IAAI,CAACxK,WAAW,CAACyK,OAAO,EAAE,EAAE;MAAA;MAAA1L,cAAA,GAAAG,CAAA;MAC1D,IAAIsL,UAAU,CAACrI,MAAM,KAAKlB,MAAM,CAACkB,MAAM,EAAE;QAAA;QAAApD,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAG,CAAA;QACvC,IAAI,CAACc,WAAW,CAACoK,MAAM,CAACG,GAAG,CAAC;QAAC;QAAAxL,cAAA,GAAAG,CAAA;QAC7B+B,MAAM,CAAC0F,EAAE,CAAC,gBAAgB6D,UAAU,CAAC/G,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,aAAa,EAAE;UACzE9B,MAAM,EAAElB,MAAM,CAACkB,MAAM;UACrBsB,cAAc,EAAE+G,UAAU,CAAC/G,cAAc;UACzC0G,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApL,cAAA,GAAAuB,CAAA;MAAA;IACH;EACF;EAEQqI,mBAAmBA,CAACxG,MAAc,EAAE2B,MAAc;IAAA;IAAA/E,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACxD,IAAI,CAACgB,EAAE,CAAC+D,IAAI,CAAC,oBAAoB,EAAE;MACjC9B,MAAM;MACN2B,MAAM;MACN2E,QAAQ,EAAE,IAAIzD,IAAI;KACnB,CAAC;EACJ;EAEQjE,oBAAoBA,CAAA;IAAA;IAAAhC,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAC1B;IACAwL,WAAW,CAAC,MAAK;MAAA;MAAA3L,cAAA,GAAAc,CAAA;MACf,MAAMwH,GAAG;MAAA;MAAA,CAAAtI,cAAA,GAAAG,CAAA,SAAG,IAAI8F,IAAI,EAAE;MAAC;MAAAjG,cAAA,GAAAG,CAAA;MACvB,KAAK,MAAM,CAACqL,GAAG,EAAEC,UAAU,CAAC,IAAI,IAAI,CAACxK,WAAW,CAACyK,OAAO,EAAE,EAAE;QAAA;QAAA1L,cAAA,GAAAG,CAAA;QAC1D,IAAImI,GAAG,CAACE,OAAO,EAAE,GAAGiD,UAAU,CAACN,SAAS,CAAC3C,OAAO,EAAE,GAAG,KAAK,EAAE;UAAA;UAAAxI,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAG,CAAA;UAAE;UAC5D,IAAI,CAACc,WAAW,CAACoK,MAAM,CAACG,GAAG,CAAC;UAAC;UAAAxL,cAAA,GAAAG,CAAA;UAC7B,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAAC,gBAAgB6D,UAAU,CAAC/G,cAAc,EAAE,CAAC,CAACQ,IAAI,CAAC,aAAa,EAAE;YAC1E9B,MAAM,EAAEqI,UAAU,CAACrI,MAAM;YACzBsB,cAAc,EAAE+G,UAAU,CAAC/G,cAAc;YACzC0G,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApL,cAAA,GAAAuB,CAAA;QAAA;MACH;IACF,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;EACOqK,cAAcA,CAAA;IAAA;IAAA5L,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACnB,OAAO0J,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/I,WAAW,CAACgJ,MAAM,EAAE,CAAC;EAC9C;EAEO8B,YAAYA,CAACzI,MAAc;IAAA;IAAApD,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IAChC,OAAO,IAAI,CAACY,WAAW,CAAC2J,GAAG,CAACtH,MAAM,CAAC;EACrC;EAEO0I,aAAaA,CAAC1I,MAAc;IAAA;IAAApD,cAAA,GAAAc,CAAA;IACjC,MAAMiC,IAAI;IAAA;IAAA,CAAA/C,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACY,WAAW,CAAC2G,GAAG,CAACtE,MAAM,CAAC;IAAC;IAAApD,cAAA,GAAAG,CAAA;IAC1C,OAAO4C,IAAI;IAAA;IAAA,CAAA/C,cAAA,GAAAuB,CAAA,WAAGwB,IAAI,CAACgC,MAAM;IAAA;IAAA,CAAA/E,cAAA,GAAAuB,CAAA,WAAG,SAAS;EACvC;EAEOwK,sBAAsBA,CAAC3I,MAAc,EAAE4I,YAAiB;IAAA;IAAAhM,cAAA,GAAAc,CAAA;IAC7D,MAAM2G,aAAa;IAAA;IAAA,CAAAzH,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACe,WAAW,CAACwG,GAAG,CAACtE,MAAM,CAAC;IAAC;IAAApD,cAAA,GAAAG,CAAA;IACnD,IAAIsH,aAAa,EAAE;MAAA;MAAAzH,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAG,CAAA;MACjBsH,aAAa,CAACD,OAAO,CAACG,QAAQ,IAAG;QAAA;QAAA3H,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAG,CAAA;QAC/B,IAAI,CAACgB,EAAE,CAACyG,EAAE,CAACD,QAAQ,CAAC,CAACzC,IAAI,CAAC,cAAc,EAAE8G,YAAY,CAAC;MACzD,CAAC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAhM,cAAA,GAAAuB,CAAA;IAAA;EACH;EAEO0K,KAAKA,CAAA;IAAA;IAAAjM,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAG,CAAA;IACV,OAAO,IAAI,CAACgB,EAAE;EAChB;;AACD;AAAAnB,cAAA,GAAAG,CAAA;AA5pBD+L,OAAA,CAAAvL,aAAA,GAAAA,aAAA", "ignoreList": []}