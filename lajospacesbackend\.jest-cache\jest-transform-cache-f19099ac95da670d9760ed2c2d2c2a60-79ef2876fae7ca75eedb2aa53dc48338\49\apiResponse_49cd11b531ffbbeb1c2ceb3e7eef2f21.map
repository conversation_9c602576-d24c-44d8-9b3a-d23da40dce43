{"version": 3, "names": ["cov_20ffb56ueo", "actualCoverage", "s", "ApiResponse", "success", "res", "data", "b", "message", "statusCode", "f", "status", "json", "timestamp", "Date", "toISOString", "error", "errors", "validationError", "notFound", "unauthorized", "forbidden", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\apiResponse.ts"], "sourcesContent": ["import { Response } from 'express';\r\n\r\nexport class ApiResponse {\r\n  /**\r\n   * Send success response\r\n   */\r\n  static success(\r\n    res: Response,\r\n    data: any = null,\r\n    message: string = 'Success',\r\n    statusCode: number = 200\r\n  ) {\r\n    return res.status(statusCode).json({\r\n      success: true,\r\n      message,\r\n      data,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Send error response\r\n   */\r\n  static error(\r\n    res: Response,\r\n    message: string = 'Internal Server Error',\r\n    statusCode: number = 500,\r\n    errors: any = null\r\n  ) {\r\n    return res.status(statusCode).json({\r\n      success: false,\r\n      message,\r\n      errors,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Send validation error response\r\n   */\r\n  static validationError(\r\n    res: Response,\r\n    errors: any,\r\n    message: string = 'Validation failed'\r\n  ) {\r\n    return res.status(400).json({\r\n      success: false,\r\n      message,\r\n      errors,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Send not found response\r\n   */\r\n  static notFound(\r\n    res: Response,\r\n    message: string = 'Resource not found'\r\n  ) {\r\n    return res.status(404).json({\r\n      success: false,\r\n      message,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Send unauthorized response\r\n   */\r\n  static unauthorized(\r\n    res: Response,\r\n    message: string = 'Unauthorized access'\r\n  ) {\r\n    return res.status(401).json({\r\n      success: false,\r\n      message,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Send forbidden response\r\n   */\r\n  static forbidden(\r\n    res: Response,\r\n    message: string = 'Access forbidden'\r\n  ) {\r\n    return res.status(403).json({\r\n      success: false,\r\n      message,\r\n      timestamp: new Date().toISOString()\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoBE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAlBF,MAAaC,WAAW;EACtB;;;EAGA,OAAOC,OAAOA,CACZC,GAAa,EACbC,IAAA;EAAA;EAAA,CAAAN,cAAA,GAAAO,CAAA,UAAY,IAAI,GAChBC,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,SAAS,GAC3BE,UAAA;EAAA;EAAA,CAAAT,cAAA,GAAAO,CAAA,UAAqB,GAAG;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAExB,OAAOG,GAAG,CAACM,MAAM,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC;MACjCR,OAAO,EAAE,IAAI;MACbI,OAAO;MACPF,IAAI;MACJO,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;EAEA;;;EAGA,OAAOC,KAAKA,CACVX,GAAa,EACbG,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,uBAAuB,GACzCE,UAAA;EAAA;EAAA,CAAAT,cAAA,GAAAO,CAAA,UAAqB,GAAG,GACxBU,MAAA;EAAA;EAAA,CAAAjB,cAAA,GAAAO,CAAA,UAAc,IAAI;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAElB,OAAOG,GAAG,CAACM,MAAM,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC;MACjCR,OAAO,EAAE,KAAK;MACdI,OAAO;MACPS,MAAM;MACNJ,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;EAEA;;;EAGA,OAAOG,eAAeA,CACpBb,GAAa,EACbY,MAAW,EACXT,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,mBAAmB;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAErC,OAAOG,GAAG,CAACM,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BR,OAAO,EAAE,KAAK;MACdI,OAAO;MACPS,MAAM;MACNJ,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;EAEA;;;EAGA,OAAOI,QAAQA,CACbd,GAAa,EACbG,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,oBAAoB;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAEtC,OAAOG,GAAG,CAACM,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BR,OAAO,EAAE,KAAK;MACdI,OAAO;MACPK,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;EAEA;;;EAGA,OAAOK,YAAYA,CACjBf,GAAa,EACbG,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,qBAAqB;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAEvC,OAAOG,GAAG,CAACM,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BR,OAAO,EAAE,KAAK;MACdI,OAAO;MACPK,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;EAEA;;;EAGA,OAAOM,SAASA,CACdhB,GAAa,EACbG,OAAA;EAAA;EAAA,CAAAR,cAAA,GAAAO,CAAA,UAAkB,kBAAkB;IAAA;IAAAP,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAE,CAAA;IAEpC,OAAOG,GAAG,CAACM,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BR,OAAO,EAAE,KAAK;MACdI,OAAO;MACPK,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;KAClC,CAAC;EACJ;;AACD;AAAAf,cAAA,GAAAE,CAAA;AA5FDoB,OAAA,CAAAnB,WAAA,GAAAA,WAAA", "ignoreList": []}