{"version": 3, "names": ["cov_14g7p1rpoq", "actualCoverage", "errorHandler_1", "s", "require", "logger_1", "Profile_model_1", "__importDefault", "cloudinaryService_1", "exports", "uploadPhoto", "catchAsync", "req", "res", "_next", "f", "user", "b", "AppError", "file", "validation", "validateImageFile", "<PERSON><PERSON><PERSON><PERSON>", "error", "profile", "default", "findOne", "userId", "photos", "length", "isPrimary", "uploadResult", "uploadProfilePhoto", "buffer", "addPhoto", "secure_url", "public_id", "save", "imageSizes", "generateImageSizes", "logHelpers", "userAction", "photoId", "totalPhotos", "status", "json", "success", "message", "data", "photo", "id", "url", "sizes", "uploadedAt", "Date", "profileCompleteness", "calculateCompleteness", "logger", "deletePhoto", "params", "find", "p", "publicId", "deleteImage", "removePhoto", "remainingPhotos", "setPrimaryPhoto", "primaryPhoto", "getPhotos", "photosWithSizes", "map", "maxPhotos", "reorderPhotos", "photoOrder", "body", "Array", "isArray", "existingPhotoIds", "invalidIds", "filter", "includes", "reorderedPhotos", "newOrder", "getUploadGuidelines", "_req", "guidelines", "maxFileSize", "allowedFormats", "recommendedSize", "tips", "requirements", "minPhotos", "primaryPhotoRequired", "facePhotoRecommended"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\photo.controller.ts"], "sourcesContent": ["import { Request, Response, NextFunction } from 'express';\r\nimport { AppError, catchAsync } from '../middleware/errorHandler';\r\nimport { logger, logHelpers } from '../utils/logger';\r\nimport Profile from '../models/Profile.model';\r\nimport { uploadProfilePhoto, deleteImage, generateImageSizes, validateImageFile } from '../services/cloudinaryService';\r\n\r\n/**\r\n * Upload profile photo\r\n */\r\nexport const uploadPhoto = catchAsync(async (req: Request, res: Response, _next: NextFunction) => {\r\n  if (!req.user) {\r\n    throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n  }\r\n\r\n  if (!req.file) {\r\n    throw new AppError('No image file provided', 400, true, 'NO_FILE');\r\n  }\r\n\r\n  // Validate image file\r\n  const validation = validateImageFile(req.file);\r\n  if (!validation.isValid) {\r\n    throw new AppError(validation.error!, 400, true, 'INVALID_FILE');\r\n  }\r\n\r\n  const profile = await Profile.findOne({ userId: req.user.userId });\r\n  if (!profile) {\r\n    throw new AppError('Profile not found', 404, true, 'PROFILE_NOT_FOUND');\r\n  }\r\n\r\n  // Check photo limit (max 6 photos)\r\n  if (profile.photos.length >= 6) {\r\n    throw new AppError('Maximum 6 photos allowed', 400, true, 'PHOTO_LIMIT_EXCEEDED');\r\n  }\r\n\r\n  try {\r\n    // Upload to Cloudinary\r\n    const isPrimary = profile.photos.length === 0; // First photo is primary\r\n    const uploadResult = await uploadProfilePhoto(req.file.buffer, req.user.userId, isPrimary);\r\n\r\n    // Add photo to profile\r\n    profile.addPhoto(uploadResult.secure_url, uploadResult.public_id);\r\n    await profile.save();\r\n\r\n    // Generate different sizes\r\n    const imageSizes = generateImageSizes(uploadResult.public_id);\r\n\r\n    logHelpers.userAction(req.user.userId, 'photo_uploaded', {\r\n      photoId: uploadResult.public_id,\r\n      isPrimary,\r\n      totalPhotos: profile.photos.length\r\n    });\r\n\r\n    res.status(201).json({\r\n      success: true,\r\n      message: 'Photo uploaded successfully',\r\n      data: {\r\n        photo: {\r\n          id: uploadResult.public_id,\r\n          url: uploadResult.secure_url,\r\n          sizes: imageSizes,\r\n          isPrimary,\r\n          uploadedAt: new Date()\r\n        },\r\n        totalPhotos: profile.photos.length,\r\n        profileCompleteness: profile.calculateCompleteness()\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Photo upload error:', error);\r\n    throw new AppError('Failed to upload photo', 500, true, 'UPLOAD_FAILED');\r\n  }\r\n});\r\n\r\n/**\r\n * Delete profile photo\r\n */\r\nexport const deletePhoto = catchAsync(async (req: Request, res: Response, _next: NextFunction) => {\r\n  if (!req.user) {\r\n    throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n  }\r\n\r\n  const { photoId } = req.params;\r\n\r\n  const profile = await Profile.findOne({ userId: req.user.userId });\r\n  if (!profile) {\r\n    throw new AppError('Profile not found', 404, true, 'PROFILE_NOT_FOUND');\r\n  }\r\n\r\n  // Find photo\r\n  const photo = profile.photos.find(p => p.publicId === photoId);\r\n  if (!photo) {\r\n    throw new AppError('Photo not found', 404, true, 'PHOTO_NOT_FOUND');\r\n  }\r\n\r\n  try {\r\n    // Delete from Cloudinary\r\n    await deleteImage(photoId);\r\n\r\n    // Remove from profile\r\n    profile.removePhoto(photoId);\r\n    await profile.save();\r\n\r\n    logHelpers.userAction(req.user.userId, 'photo_deleted', {\r\n      photoId,\r\n      remainingPhotos: profile.photos.length\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      message: 'Photo deleted successfully',\r\n      data: {\r\n        remainingPhotos: profile.photos.length,\r\n        profileCompleteness: profile.calculateCompleteness()\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Photo deletion error:', error);\r\n    throw new AppError('Failed to delete photo', 500, true, 'DELETE_FAILED');\r\n  }\r\n});\r\n\r\n/**\r\n * Set primary photo\r\n */\r\nexport const setPrimaryPhoto = catchAsync(async (req: Request, res: Response, _next: NextFunction) => {\r\n  if (!req.user) {\r\n    throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n  }\r\n\r\n  const { photoId } = req.params;\r\n\r\n  const profile = await Profile.findOne({ userId: req.user.userId });\r\n  if (!profile) {\r\n    throw new AppError('Profile not found', 404, true, 'PROFILE_NOT_FOUND');\r\n  }\r\n\r\n  // Find photo\r\n  const photo = profile.photos.find(p => p.publicId === photoId);\r\n  if (!photo) {\r\n    throw new AppError('Photo not found', 404, true, 'PHOTO_NOT_FOUND');\r\n  }\r\n\r\n  // Set as primary\r\n  profile.setPrimaryPhoto(photoId);\r\n  await profile.save();\r\n\r\n  logHelpers.userAction(req.user.userId, 'primary_photo_changed', { photoId });\r\n\r\n  res.json({\r\n    success: true,\r\n    message: 'Primary photo updated successfully',\r\n    data: {\r\n      primaryPhoto: profile.photos.find(p => p.isPrimary) || profile.photos[0] || null\r\n    }\r\n  });\r\n});\r\n\r\n/**\r\n * Get all user photos\r\n */\r\nexport const getPhotos = catchAsync(async (req: Request, res: Response, _next: NextFunction) => {\r\n  if (!req.user) {\r\n    throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n  }\r\n\r\n  const profile = await Profile.findOne({ userId: req.user.userId });\r\n  if (!profile) {\r\n    throw new AppError('Profile not found', 404, true, 'PROFILE_NOT_FOUND');\r\n  }\r\n\r\n  // Generate different sizes for each photo\r\n  const photosWithSizes = profile.photos.map(photo => ({\r\n    id: photo.publicId,\r\n    url: photo.url,\r\n    sizes: generateImageSizes(photo.publicId),\r\n    isPrimary: photo.isPrimary,\r\n    uploadedAt: photo.uploadedAt\r\n  }));\r\n\r\n  res.json({\r\n    success: true,\r\n    data: {\r\n      photos: photosWithSizes,\r\n      totalPhotos: profile.photos.length,\r\n      maxPhotos: 6,\r\n      primaryPhoto: profile.photos.find(p => p.isPrimary) || profile.photos[0] || null\r\n    }\r\n  });\r\n});\r\n\r\n/**\r\n * Reorder photos\r\n */\r\nexport const reorderPhotos = catchAsync(async (req: Request, res: Response, _next: NextFunction) => {\r\n  if (!req.user) {\r\n    throw new AppError('Authentication required', 401, true, 'AUTH_REQUIRED');\r\n  }\r\n\r\n  const { photoOrder } = req.body; // Array of photo IDs in desired order\r\n\r\n  if (!Array.isArray(photoOrder)) {\r\n    throw new AppError('Photo order must be an array', 400, true, 'INVALID_ORDER');\r\n  }\r\n\r\n  const profile = await Profile.findOne({ userId: req.user.userId });\r\n  if (!profile) {\r\n    throw new AppError('Profile not found', 404, true, 'PROFILE_NOT_FOUND');\r\n  }\r\n\r\n  // Validate that all photo IDs exist\r\n  const existingPhotoIds = profile.photos.map(p => p.publicId);\r\n  const invalidIds = photoOrder.filter(id => !existingPhotoIds.includes(id));\r\n  \r\n  if (invalidIds.length > 0) {\r\n    throw new AppError('Invalid photo IDs provided', 400, true, 'INVALID_PHOTO_IDS');\r\n  }\r\n\r\n  if (photoOrder.length !== profile.photos.length) {\r\n    throw new AppError('Photo order must include all photos', 400, true, 'INCOMPLETE_ORDER');\r\n  }\r\n\r\n  // Reorder photos\r\n  const reorderedPhotos = photoOrder.map(photoId => \r\n    profile.photos.find(p => p.publicId === photoId)!\r\n  );\r\n\r\n  profile.photos = reorderedPhotos;\r\n  await profile.save();\r\n\r\n  logHelpers.userAction(req.user.userId, 'photos_reordered', { newOrder: photoOrder });\r\n\r\n  res.json({\r\n    success: true,\r\n    message: 'Photos reordered successfully',\r\n    data: {\r\n      photos: profile.photos.map(photo => ({\r\n        id: photo.publicId,\r\n        url: photo.url,\r\n        isPrimary: photo.isPrimary,\r\n        uploadedAt: photo.uploadedAt\r\n      }))\r\n    }\r\n  });\r\n});\r\n\r\n/**\r\n * Get photo upload guidelines\r\n */\r\nexport const getUploadGuidelines = catchAsync(async (_req: Request, res: Response, _next: NextFunction) => {\r\n  res.json({\r\n    success: true,\r\n    data: {\r\n      guidelines: {\r\n        maxPhotos: 6,\r\n        maxFileSize: '10MB',\r\n        allowedFormats: ['JPEG', 'PNG', 'WebP'],\r\n        recommendedSize: '800x800 pixels',\r\n        tips: [\r\n          'Use clear, well-lit photos',\r\n          'Include at least one face photo',\r\n          'Show your personality and interests',\r\n          'Avoid group photos as your primary photo',\r\n          'Keep photos recent (within 2 years)'\r\n        ]\r\n      },\r\n      requirements: {\r\n        minPhotos: 1,\r\n        primaryPhotoRequired: true,\r\n        facePhotoRecommended: true\r\n      }\r\n    }\r\n  });\r\n});\r\n\r\nexport default {\r\n  uploadPhoto,\r\n  deletePhoto,\r\n  setPrimaryPhoto,\r\n  getPhotos,\r\n  reorderPhotos,\r\n  getUploadGuidelines\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWI;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVJ,MAAAE,cAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAL,cAAA,GAAAG,CAAA,OAAAC,OAAA;AACA,MAAAE,eAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAI,eAAA,CAAAH,OAAA;AACA,MAAAI,mBAAA;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAAAC,OAAA;AAEA;;;AAAA;AAAAJ,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAAC,WAAW,GAAG,IAAAR,cAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EAC/F,IAAI,CAACS,GAAG,CAACI,IAAI,EAAE;IAAA;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAG,CAAA;EAED,IAAI,CAACS,GAAG,CAACO,IAAI,EAAE;IAAA;IAAAnB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC;EACpE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAMG,UAAU;EAAA;EAAA,CAAApB,cAAA,GAAAG,CAAA,QAAG,IAAAK,mBAAA,CAAAa,iBAAiB,EAACT,GAAG,CAACO,IAAI,CAAC;EAAC;EAAAnB,cAAA,GAAAG,CAAA;EAC/C,IAAI,CAACiB,UAAU,CAACE,OAAO,EAAE;IAAA;IAAAtB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACvB,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAACE,UAAU,CAACG,KAAM,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC;EAClE,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAMO,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAG,MAAMG,eAAA,CAAAmB,OAAO,CAACC,OAAO,CAAC;IAAEC,MAAM,EAAEf,GAAG,CAACI,IAAI,CAACW;EAAM,CAAE,CAAC;EAAC;EAAA3B,cAAA,GAAAG,CAAA;EACnE,IAAI,CAACqB,OAAO,EAAE;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACZ,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EAAAjB,cAAA,GAAAG,CAAA;EACA,IAAIqB,OAAO,CAACI,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAAA;IAAA7B,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IAC9B,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC;EACnF,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAG,CAAA;EAED,IAAI;IACF;IACA,MAAM2B,SAAS;IAAA;IAAA,CAAA9B,cAAA,GAAAG,CAAA,QAAGqB,OAAO,CAACI,MAAM,CAACC,MAAM,KAAK,CAAC,EAAC,CAAC;IAC/C,MAAME,YAAY;IAAA;IAAA,CAAA/B,cAAA,GAAAG,CAAA,QAAG,MAAM,IAAAK,mBAAA,CAAAwB,kBAAkB,EAACpB,GAAG,CAACO,IAAI,CAACc,MAAM,EAAErB,GAAG,CAACI,IAAI,CAACW,MAAM,EAAEG,SAAS,CAAC;IAE1F;IAAA;IAAA9B,cAAA,GAAAG,CAAA;IACAqB,OAAO,CAACU,QAAQ,CAACH,YAAY,CAACI,UAAU,EAAEJ,YAAY,CAACK,SAAS,CAAC;IAAC;IAAApC,cAAA,GAAAG,CAAA;IAClE,MAAMqB,OAAO,CAACa,IAAI,EAAE;IAEpB;IACA,MAAMC,UAAU;IAAA;IAAA,CAAAtC,cAAA,GAAAG,CAAA,QAAG,IAAAK,mBAAA,CAAA+B,kBAAkB,EAACR,YAAY,CAACK,SAAS,CAAC;IAAC;IAAApC,cAAA,GAAAG,CAAA;IAE9DE,QAAA,CAAAmC,UAAU,CAACC,UAAU,CAAC7B,GAAG,CAACI,IAAI,CAACW,MAAM,EAAE,gBAAgB,EAAE;MACvDe,OAAO,EAAEX,YAAY,CAACK,SAAS;MAC/BN,SAAS;MACTa,WAAW,EAAEnB,OAAO,CAACI,MAAM,CAACC;KAC7B,CAAC;IAAC;IAAA7B,cAAA,GAAAG,CAAA;IAEHU,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAE;QACJC,KAAK,EAAE;UACLC,EAAE,EAAEnB,YAAY,CAACK,SAAS;UAC1Be,GAAG,EAAEpB,YAAY,CAACI,UAAU;UAC5BiB,KAAK,EAAEd,UAAU;UACjBR,SAAS;UACTuB,UAAU,EAAE,IAAIC,IAAI;SACrB;QACDX,WAAW,EAAEnB,OAAO,CAACI,MAAM,CAACC,MAAM;QAClC0B,mBAAmB,EAAE/B,OAAO,CAACgC,qBAAqB;;KAErD,CAAC;EAEJ,CAAC,CAAC,OAAOjC,KAAK,EAAE;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IACdE,QAAA,CAAAoD,MAAM,CAAClC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAAC;IAAAvB,cAAA,GAAAG,CAAA;IAC3C,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC1E;AACF,CAAC,CAAC;AAEF;;;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAAiD,WAAW,GAAG,IAAAxD,cAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EAC/F,IAAI,CAACS,GAAG,CAACI,IAAI,EAAE;IAAA;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAM;IAAEyB;EAAO,CAAE;EAAA;EAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAGS,GAAG,CAAC+C,MAAM;EAE9B,MAAMnC,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAG,MAAMG,eAAA,CAAAmB,OAAO,CAACC,OAAO,CAAC;IAAEC,MAAM,EAAEf,GAAG,CAACI,IAAI,CAACW;EAAM,CAAE,CAAC;EAAC;EAAA3B,cAAA,GAAAG,CAAA;EACnE,IAAI,CAACqB,OAAO,EAAE;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACZ,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAMgC,KAAK;EAAA;EAAA,CAAAjD,cAAA,GAAAG,CAAA,QAAGqB,OAAO,CAACI,MAAM,CAACgC,IAAI,CAACC,CAAC,IAAI;IAAA;IAAA7D,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA,OAAA0D,CAAC,CAACC,QAAQ,KAAKpB,OAAO;EAAP,CAAO,CAAC;EAAC;EAAA1C,cAAA,GAAAG,CAAA;EAC/D,IAAI,CAAC8C,KAAK,EAAE;IAAA;IAAAjD,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACV,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC;EACrE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAG,CAAA;EAED,IAAI;IAAA;IAAAH,cAAA,GAAAG,CAAA;IACF;IACA,MAAM,IAAAK,mBAAA,CAAAuD,WAAW,EAACrB,OAAO,CAAC;IAE1B;IAAA;IAAA1C,cAAA,GAAAG,CAAA;IACAqB,OAAO,CAACwC,WAAW,CAACtB,OAAO,CAAC;IAAC;IAAA1C,cAAA,GAAAG,CAAA;IAC7B,MAAMqB,OAAO,CAACa,IAAI,EAAE;IAAC;IAAArC,cAAA,GAAAG,CAAA;IAErBE,QAAA,CAAAmC,UAAU,CAACC,UAAU,CAAC7B,GAAG,CAACI,IAAI,CAACW,MAAM,EAAE,eAAe,EAAE;MACtDe,OAAO;MACPuB,eAAe,EAAEzC,OAAO,CAACI,MAAM,CAACC;KACjC,CAAC;IAAC;IAAA7B,cAAA,GAAAG,CAAA;IAEHU,GAAG,CAACgC,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE;QACJiB,eAAe,EAAEzC,OAAO,CAACI,MAAM,CAACC,MAAM;QACtC0B,mBAAmB,EAAE/B,OAAO,CAACgC,qBAAqB;;KAErD,CAAC;EAEJ,CAAC,CAAC,OAAOjC,KAAK,EAAE;IAAA;IAAAvB,cAAA,GAAAG,CAAA;IACdE,QAAA,CAAAoD,MAAM,CAAClC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAAC;IAAAvB,cAAA,GAAAG,CAAA;IAC7C,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC1E;AACF,CAAC,CAAC;AAEF;;;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAAyD,eAAe,GAAG,IAAAhE,cAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EACnG,IAAI,CAACS,GAAG,CAACI,IAAI,EAAE;IAAA;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAM;IAAEyB;EAAO,CAAE;EAAA;EAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAGS,GAAG,CAAC+C,MAAM;EAE9B,MAAMnC,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAG,MAAMG,eAAA,CAAAmB,OAAO,CAACC,OAAO,CAAC;IAAEC,MAAM,EAAEf,GAAG,CAACI,IAAI,CAACW;EAAM,CAAE,CAAC;EAAC;EAAA3B,cAAA,GAAAG,CAAA;EACnE,IAAI,CAACqB,OAAO,EAAE;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACZ,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAMgC,KAAK;EAAA;EAAA,CAAAjD,cAAA,GAAAG,CAAA,QAAGqB,OAAO,CAACI,MAAM,CAACgC,IAAI,CAACC,CAAC,IAAI;IAAA;IAAA7D,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA,OAAA0D,CAAC,CAACC,QAAQ,KAAKpB,OAAO;EAAP,CAAO,CAAC;EAAC;EAAA1C,cAAA,GAAAG,CAAA;EAC/D,IAAI,CAAC8C,KAAK,EAAE;IAAA;IAAAjD,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACV,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC;EACrE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EAAAjB,cAAA,GAAAG,CAAA;EACAqB,OAAO,CAAC0C,eAAe,CAACxB,OAAO,CAAC;EAAC;EAAA1C,cAAA,GAAAG,CAAA;EACjC,MAAMqB,OAAO,CAACa,IAAI,EAAE;EAAC;EAAArC,cAAA,GAAAG,CAAA;EAErBE,QAAA,CAAAmC,UAAU,CAACC,UAAU,CAAC7B,GAAG,CAACI,IAAI,CAACW,MAAM,EAAE,uBAAuB,EAAE;IAAEe;EAAO,CAAE,CAAC;EAAC;EAAA1C,cAAA,GAAAG,CAAA;EAE7EU,GAAG,CAACgC,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,oCAAoC;IAC7CC,IAAI,EAAE;MACJmB,YAAY;MAAE;MAAA,CAAAnE,cAAA,GAAAiB,CAAA,WAAAO,OAAO,CAACI,MAAM,CAACgC,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA,OAAA0D,CAAC,CAAC/B,SAAS;MAAT,CAAS,CAAC;MAAA;MAAA,CAAA9B,cAAA,GAAAiB,CAAA,WAAIO,OAAO,CAACI,MAAM,CAAC,CAAC,CAAC;MAAA;MAAA,CAAA5B,cAAA,GAAAiB,CAAA,WAAI,IAAI;;GAEnF,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAjB,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAA2D,SAAS,GAAG,IAAAlE,cAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EAC7F,IAAI,CAACS,GAAG,CAACI,IAAI,EAAE;IAAA;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAMO,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAG,MAAMG,eAAA,CAAAmB,OAAO,CAACC,OAAO,CAAC;IAAEC,MAAM,EAAEf,GAAG,CAACI,IAAI,CAACW;EAAM,CAAE,CAAC;EAAC;EAAA3B,cAAA,GAAAG,CAAA;EACnE,IAAI,CAACqB,OAAO,EAAE;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACZ,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAMoD,eAAe;EAAA;EAAA,CAAArE,cAAA,GAAAG,CAAA,QAAGqB,OAAO,CAACI,MAAM,CAAC0C,GAAG,CAACrB,KAAK,IAAK;IAAA;IAAAjD,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA;MACnD+C,EAAE,EAAED,KAAK,CAACa,QAAQ;MAClBX,GAAG,EAAEF,KAAK,CAACE,GAAG;MACdC,KAAK,EAAE,IAAA5C,mBAAA,CAAA+B,kBAAkB,EAACU,KAAK,CAACa,QAAQ,CAAC;MACzChC,SAAS,EAAEmB,KAAK,CAACnB,SAAS;MAC1BuB,UAAU,EAAEJ,KAAK,CAACI;KACnB;GAAC,CAAC;EAAC;EAAArD,cAAA,GAAAG,CAAA;EAEJU,GAAG,CAACgC,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbE,IAAI,EAAE;MACJpB,MAAM,EAAEyC,eAAe;MACvB1B,WAAW,EAAEnB,OAAO,CAACI,MAAM,CAACC,MAAM;MAClC0C,SAAS,EAAE,CAAC;MACZJ,YAAY;MAAE;MAAA,CAAAnE,cAAA,GAAAiB,CAAA,WAAAO,OAAO,CAACI,MAAM,CAACgC,IAAI,CAACC,CAAC,IAAI;QAAA;QAAA7D,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA,OAAA0D,CAAC,CAAC/B,SAAS;MAAT,CAAS,CAAC;MAAA;MAAA,CAAA9B,cAAA,GAAAiB,CAAA,WAAIO,OAAO,CAACI,MAAM,CAAC,CAAC,CAAC;MAAA;MAAA,CAAA5B,cAAA,GAAAiB,CAAA,WAAI,IAAI;;GAEnF,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAjB,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAA+D,aAAa,GAAG,IAAAtE,cAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EACjG,IAAI,CAACS,GAAG,CAACI,IAAI,EAAE;IAAA;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACb,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAC3E,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAM;IAAEwD;EAAU,CAAE;EAAA;EAAA,CAAAzE,cAAA,GAAAG,CAAA,QAAGS,GAAG,CAAC8D,IAAI,EAAC,CAAC;EAAA;EAAA1E,cAAA,GAAAG,CAAA;EAEjC,IAAI,CAACwE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IAAA;IAAAzE,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IAC9B,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC;EAChF,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED,MAAMO,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAG,CAAA,QAAG,MAAMG,eAAA,CAAAmB,OAAO,CAACC,OAAO,CAAC;IAAEC,MAAM,EAAEf,GAAG,CAACI,IAAI,CAACW;EAAM,CAAE,CAAC;EAAC;EAAA3B,cAAA,GAAAG,CAAA;EACnE,IAAI,CAACqB,OAAO,EAAE;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACZ,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAM4D,gBAAgB;EAAA;EAAA,CAAA7E,cAAA,GAAAG,CAAA,QAAGqB,OAAO,CAACI,MAAM,CAAC0C,GAAG,CAACT,CAAC,IAAI;IAAA;IAAA7D,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA,OAAA0D,CAAC,CAACC,QAAQ;EAAR,CAAQ,CAAC;EAC5D,MAAMgB,UAAU;EAAA;EAAA,CAAA9E,cAAA,GAAAG,CAAA,QAAGsE,UAAU,CAACM,MAAM,CAAC7B,EAAE,IAAI;IAAA;IAAAlD,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA,QAAC0E,gBAAgB,CAACG,QAAQ,CAAC9B,EAAE,CAAC;EAAD,CAAC,CAAC;EAAC;EAAAlD,cAAA,GAAAG,CAAA;EAE3E,IAAI2E,UAAU,CAACjD,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA7B,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IACzB,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC;EAClF,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAG,CAAA;EAED,IAAIsE,UAAU,CAAC5C,MAAM,KAAKL,OAAO,CAACI,MAAM,CAACC,MAAM,EAAE;IAAA;IAAA7B,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAG,CAAA;IAC/C,MAAM,IAAID,cAAA,CAAAgB,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE,IAAI,EAAE,kBAAkB,CAAC;EAC1F,CAAC;EAAA;EAAA;IAAAlB,cAAA,GAAAiB,CAAA;EAAA;EAED;EACA,MAAMgE,eAAe;EAAA;EAAA,CAAAjF,cAAA,GAAAG,CAAA,QAAGsE,UAAU,CAACH,GAAG,CAAC5B,OAAO,IAC5C;IAAA;IAAA1C,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAG,CAAA;IAAA,OAAAqB,OAAO,CAACI,MAAM,CAACgC,IAAI,CAACC,CAAC,IAAI;MAAA;MAAA7D,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAG,CAAA;MAAA,OAAA0D,CAAC,CAACC,QAAQ,KAAKpB,OAAO;IAAP,CAAO,CAAE;EAAF,CAAE,CAClD;EAAC;EAAA1C,cAAA,GAAAG,CAAA;EAEFqB,OAAO,CAACI,MAAM,GAAGqD,eAAe;EAAC;EAAAjF,cAAA,GAAAG,CAAA;EACjC,MAAMqB,OAAO,CAACa,IAAI,EAAE;EAAC;EAAArC,cAAA,GAAAG,CAAA;EAErBE,QAAA,CAAAmC,UAAU,CAACC,UAAU,CAAC7B,GAAG,CAACI,IAAI,CAACW,MAAM,EAAE,kBAAkB,EAAE;IAAEuD,QAAQ,EAAET;EAAU,CAAE,CAAC;EAAC;EAAAzE,cAAA,GAAAG,CAAA;EAErFU,GAAG,CAACgC,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,+BAA+B;IACxCC,IAAI,EAAE;MACJpB,MAAM,EAAEJ,OAAO,CAACI,MAAM,CAAC0C,GAAG,CAACrB,KAAK,IAAK;QAAA;QAAAjD,cAAA,GAAAe,CAAA;QAAAf,cAAA,GAAAG,CAAA;QAAA;UACnC+C,EAAE,EAAED,KAAK,CAACa,QAAQ;UAClBX,GAAG,EAAEF,KAAK,CAACE,GAAG;UACdrB,SAAS,EAAEmB,KAAK,CAACnB,SAAS;UAC1BuB,UAAU,EAAEJ,KAAK,CAACI;SACnB;OAAC;;GAEL,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAArD,cAAA,GAAAG,CAAA;AAGaM,OAAA,CAAA0E,mBAAmB,GAAG,IAAAjF,cAAA,CAAAS,UAAU,EAAC,OAAOyE,IAAa,EAAEvE,GAAa,EAAEC,KAAmB,KAAI;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAG,CAAA;EACxGU,GAAG,CAACgC,IAAI,CAAC;IACPC,OAAO,EAAE,IAAI;IACbE,IAAI,EAAE;MACJqC,UAAU,EAAE;QACVd,SAAS,EAAE,CAAC;QACZe,WAAW,EAAE,MAAM;QACnBC,cAAc,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;QACvCC,eAAe,EAAE,gBAAgB;QACjCC,IAAI,EAAE,CACJ,4BAA4B,EAC5B,iCAAiC,EACjC,qCAAqC,EACrC,0CAA0C,EAC1C,qCAAqC;OAExC;MACDC,YAAY,EAAE;QACZC,SAAS,EAAE,CAAC;QACZC,oBAAoB,EAAE,IAAI;QAC1BC,oBAAoB,EAAE;;;GAG3B,CAAC;AACJ,CAAC,CAAC;AAAC;AAAA7F,cAAA,GAAAG,CAAA;AAEHM,OAAA,CAAAgB,OAAA,GAAe;EACbf,WAAW,EAAXD,OAAA,CAAAC,WAAW;EACXgD,WAAW,EAAXjD,OAAA,CAAAiD,WAAW;EACXQ,eAAe,EAAfzD,OAAA,CAAAyD,eAAe;EACfE,SAAS,EAAT3D,OAAA,CAAA2D,SAAS;EACTI,aAAa,EAAb/D,OAAA,CAAA+D,aAAa;EACbW,mBAAmB,EAAnB1E,OAAA,CAAA0E;CACD", "ignoreList": []}