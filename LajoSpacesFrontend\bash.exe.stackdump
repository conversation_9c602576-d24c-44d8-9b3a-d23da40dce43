Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF946EE0000 ntdll.dll
7FF945AB0000 KERNEL32.DLL
7FF944030000 KERNELBASE.dll
7FF946300000 USER32.dll
7FF944570000 win32u.dll
7FF946A20000 GDI32.dll
7FF9445A0000 gdi32full.dll
7FF944920000 msvcp_win.dll
7FF944420000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF945CA0000 advapi32.dll
7FF945890000 msvcrt.dll
7FF946DF0000 sechost.dll
7FF945B80000 RPCRT4.dll
7FF943540000 CRYPTBASE.DLL
7FF9449D0000 bcryptPrimitives.dll
7FF9469E0000 IMM32.DLL
