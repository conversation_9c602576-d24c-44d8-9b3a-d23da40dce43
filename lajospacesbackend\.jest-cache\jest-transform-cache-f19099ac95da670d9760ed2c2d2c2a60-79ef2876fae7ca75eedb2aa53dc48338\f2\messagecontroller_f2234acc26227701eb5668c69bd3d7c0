2cafdcb54cb8f4148e8b70e54ad745a1
"use strict";

/* istanbul ignore next */
function cov_1bqll5vvre() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\message.controller.ts";
  var hash = "773367da18908f03398884ea5a564a0a9e1bf244";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\message.controller.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 213
        }
      },
      "2": {
        start: {
          line: 4,
          column: 19
        },
        end: {
          line: 4,
          column: 38
        }
      },
      "3": {
        start: {
          line: 5,
          column: 23
        },
        end: {
          line: 5,
          column: 56
        }
      },
      "4": {
        start: {
          line: 6,
          column: 17
        },
        end: {
          line: 6,
          column: 43
        }
      },
      "5": {
        start: {
          line: 7,
          column: 19
        },
        end: {
          line: 7,
          column: 47
        }
      },
      "6": {
        start: {
          line: 8,
          column: 21
        },
        end: {
          line: 8,
          column: 51
        }
      },
      "7": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 128,
          column: 3
        }
      },
      "8": {
        start: {
          line: 13,
          column: 19
        },
        end: {
          line: 13,
          column: 32
        }
      },
      "9": {
        start: {
          line: 14,
          column: 31
        },
        end: {
          line: 14,
          column: 41
        }
      },
      "10": {
        start: {
          line: 17,
          column: 28
        },
        end: {
          line: 17,
          column: 37
        }
      },
      "11": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 20,
          column: 5
        }
      },
      "12": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 69
        }
      },
      "13": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 127,
          column: 5
        }
      },
      "14": {
        start: {
          line: 23,
          column: 29
        },
        end: {
          line: 27,
          column: 10
        }
      },
      "15": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 30,
          column: 9
        }
      },
      "16": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 90
        }
      },
      "17": {
        start: {
          line: 32,
          column: 22
        },
        end: {
          line: 35,
          column: 9
        }
      },
      "18": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 39,
          column: 9
        }
      },
      "19": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 44
        }
      },
      "20": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 43,
          column: 9
        }
      },
      "21": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "22": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 50,
          column: 9
        }
      },
      "23": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 79
        }
      },
      "24": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 49,
          column: 13
        }
      },
      "25": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 67
        }
      },
      "26": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 56,
          column: 9
        }
      },
      "27": {
        start: {
          line: 52,
          column: 33
        },
        end: {
          line: 52,
          column: 77
        }
      },
      "28": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 55,
          column: 13
        }
      },
      "29": {
        start: {
          line: 54,
          column: 16
        },
        end: {
          line: 54,
          column: 66
        }
      },
      "30": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "31": {
        start: {
          line: 59,
          column: 25
        },
        end: {
          line: 59,
          column: 55
        }
      },
      "32": {
        start: {
          line: 60,
          column: 21
        },
        end: {
          line: 60,
          column: 45
        }
      },
      "33": {
        start: {
          line: 61,
          column: 39
        },
        end: {
          line: 72,
          column: 10
        }
      },
      "34": {
        start: {
          line: 74,
          column: 36
        },
        end: {
          line: 75,
          column: 34
        }
      },
      "35": {
        start: {
          line: 74,
          column: 59
        },
        end: {
          line: 75,
          column: 33
        }
      },
      "36": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "37": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 83,
          column: 15
        }
      },
      "38": {
        start: {
          line: 78,
          column: 59
        },
        end: {
          line: 78,
          column: 66
        }
      },
      "39": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 94,
          column: 11
        }
      },
      "40": {
        start: {
          line: 86,
          column: 59
        },
        end: {
          line: 94,
          column: 9
        }
      },
      "41": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 57
        }
      },
      "42": {
        start: {
          line: 97,
          column: 28
        },
        end: {
          line: 97,
          column: 39
        }
      },
      "43": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 122,
          column: 11
        }
      },
      "44": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 77
        }
      },
      "45": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 69
        }
      },
      "46": {
        start: {
          line: 132,
          column: 0
        },
        end: {
          line: 192,
          column: 3
        }
      },
      "47": {
        start: {
          line: 133,
          column: 19
        },
        end: {
          line: 133,
          column: 32
        }
      },
      "48": {
        start: {
          line: 134,
          column: 31
        },
        end: {
          line: 134,
          column: 41
        }
      },
      "49": {
        start: {
          line: 135,
          column: 65
        },
        end: {
          line: 135,
          column: 73
        }
      },
      "50": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "51": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 69
        }
      },
      "52": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "53": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 74
        }
      },
      "54": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 191,
          column: 5
        }
      },
      "55": {
        start: {
          line: 144,
          column: 29
        },
        end: {
          line: 144,
          column: 87
        }
      },
      "56": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 147,
          column: 9
        }
      },
      "57": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 146,
          column: 91
        }
      },
      "58": {
        start: {
          line: 149,
          column: 27
        },
        end: {
          line: 149,
          column: 98
        }
      },
      "59": {
        start: {
          line: 149,
          column: 63
        },
        end: {
          line: 149,
          column: 97
        }
      },
      "60": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 160,
          column: 9
        }
      },
      "61": {
        start: {
          line: 152,
          column: 35
        },
        end: {
          line: 156,
          column: 14
        }
      },
      "62": {
        start: {
          line: 157,
          column: 12
        },
        end: {
          line: 159,
          column: 13
        }
      },
      "63": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 81
        }
      },
      "64": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 171,
          column: 10
        }
      },
      "65": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 172,
          column: 29
        }
      },
      "66": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 174,
          column: 54
        }
      },
      "67": {
        start: {
          line: 176,
          column: 33
        },
        end: {
          line: 180,
          column: 90
        }
      },
      "68": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 94
        }
      },
      "69": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 186,
          column: 11
        }
      },
      "70": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 63
        }
      },
      "71": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 69
        }
      },
      "72": {
        start: {
          line: 196,
          column: 0
        },
        end: {
          line: 246,
          column: 3
        }
      },
      "73": {
        start: {
          line: 197,
          column: 19
        },
        end: {
          line: 197,
          column: 32
        }
      },
      "74": {
        start: {
          line: 198,
          column: 26
        },
        end: {
          line: 198,
          column: 36
        }
      },
      "75": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 32
        }
      },
      "76": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "77": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 69
        }
      },
      "78": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 205,
          column: 5
        }
      },
      "79": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 74
        }
      },
      "80": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "81": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 212,
          column: 10
        }
      },
      "82": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 215,
          column: 9
        }
      },
      "83": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 88
        }
      },
      "84": {
        start: {
          line: 217,
          column: 27
        },
        end: {
          line: 217,
          column: 67
        }
      },
      "85": {
        start: {
          line: 218,
          column: 27
        },
        end: {
          line: 218,
          column: 46
        }
      },
      "86": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 221,
          column: 9
        }
      },
      "87": {
        start: {
          line: 220,
          column: 12
        },
        end: {
          line: 220,
          column: 77
        }
      },
      "88": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 225,
          column: 9
        }
      },
      "89": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 224,
          column: 54
        }
      },
      "90": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 41
        }
      },
      "91": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 32
        }
      },
      "92": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 38
        }
      },
      "93": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 29
        }
      },
      "94": {
        start: {
          line: 232,
          column: 33
        },
        end: {
          line: 234,
          column: 64
        }
      },
      "95": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 78
        }
      },
      "96": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 240,
          column: 11
        }
      },
      "97": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 63
        }
      },
      "98": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 69
        }
      },
      "99": {
        start: {
          line: 250,
          column: 0
        },
        end: {
          line: 294,
          column: 3
        }
      },
      "100": {
        start: {
          line: 251,
          column: 19
        },
        end: {
          line: 251,
          column: 32
        }
      },
      "101": {
        start: {
          line: 252,
          column: 26
        },
        end: {
          line: 252,
          column: 36
        }
      },
      "102": {
        start: {
          line: 253,
          column: 42
        },
        end: {
          line: 253,
          column: 50
        }
      },
      "103": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 256,
          column: 5
        }
      },
      "104": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 255,
          column: 69
        }
      },
      "105": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 293,
          column: 5
        }
      },
      "106": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 262,
          column: 10
        }
      },
      "107": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 265,
          column: 9
        }
      },
      "108": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 87
        }
      },
      "109": {
        start: {
          line: 267,
          column: 27
        },
        end: {
          line: 267,
          column: 67
        }
      },
      "110": {
        start: {
          line: 268,
          column: 40
        },
        end: {
          line: 268,
          column: 54
        }
      },
      "111": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "112": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 270,
          column: 92
        }
      },
      "113": {
        start: {
          line: 273,
          column: 8
        },
        end: {
          line: 273,
          column: 33
        }
      },
      "114": {
        start: {
          line: 274,
          column: 8
        },
        end: {
          line: 274,
          column: 39
        }
      },
      "115": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 275,
          column: 66
        }
      },
      "116": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 278,
          column: 9
        }
      },
      "117": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 57
        }
      },
      "118": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 29
        }
      },
      "119": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 280,
          column: 121
        }
      },
      "120": {
        start: {
          line: 281,
          column: 8
        },
        end: {
          line: 288,
          column: 11
        }
      },
      "121": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 291,
          column: 64
        }
      },
      "122": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 71
        }
      },
      "123": {
        start: {
          line: 298,
          column: 0
        },
        end: {
          line: 359,
          column: 3
        }
      },
      "124": {
        start: {
          line: 299,
          column: 19
        },
        end: {
          line: 299,
          column: 32
        }
      },
      "125": {
        start: {
          line: 300,
          column: 26
        },
        end: {
          line: 300,
          column: 36
        }
      },
      "126": {
        start: {
          line: 301,
          column: 25
        },
        end: {
          line: 301,
          column: 33
        }
      },
      "127": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 304,
          column: 5
        }
      },
      "128": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 303,
          column: 69
        }
      },
      "129": {
        start: {
          line: 305,
          column: 27
        },
        end: {
          line: 305,
          column: 75
        }
      },
      "130": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 308,
          column: 5
        }
      },
      "131": {
        start: {
          line: 307,
          column: 8
        },
        end: {
          line: 307,
          column: 68
        }
      },
      "132": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 358,
          column: 5
        }
      },
      "133": {
        start: {
          line: 310,
          column: 24
        },
        end: {
          line: 313,
          column: 10
        }
      },
      "134": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 316,
          column: 9
        }
      },
      "135": {
        start: {
          line: 315,
          column: 12
        },
        end: {
          line: 315,
          column: 68
        }
      },
      "136": {
        start: {
          line: 318,
          column: 29
        },
        end: {
          line: 321,
          column: 10
        }
      },
      "137": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 324,
          column: 9
        }
      },
      "138": {
        start: {
          line: 323,
          column: 12
        },
        end: {
          line: 323,
          column: 64
        }
      },
      "139": {
        start: {
          line: 326,
          column: 38
        },
        end: {
          line: 326,
          column: 120
        }
      },
      "140": {
        start: {
          line: 326,
          column: 72
        },
        end: {
          line: 326,
          column: 113
        }
      },
      "141": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 342,
          column: 9
        }
      },
      "142": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 329,
          column: 73
        }
      },
      "143": {
        start: {
          line: 330,
          column: 12
        },
        end: {
          line: 330,
          column: 76
        }
      },
      "144": {
        start: {
          line: 334,
          column: 12
        },
        end: {
          line: 336,
          column: 13
        }
      },
      "145": {
        start: {
          line: 335,
          column: 16
        },
        end: {
          line: 335,
          column: 39
        }
      },
      "146": {
        start: {
          line: 337,
          column: 12
        },
        end: {
          line: 341,
          column: 15
        }
      },
      "147": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 343,
          column: 29
        }
      },
      "148": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 344,
          column: 96
        }
      },
      "149": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 353,
          column: 11
        }
      },
      "150": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 67
        }
      },
      "151": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 357,
          column: 73
        }
      },
      "152": {
        start: {
          line: 363,
          column: 0
        },
        end: {
          line: 396,
          column: 3
        }
      },
      "153": {
        start: {
          line: 364,
          column: 19
        },
        end: {
          line: 364,
          column: 32
        }
      },
      "154": {
        start: {
          line: 365,
          column: 26
        },
        end: {
          line: 365,
          column: 36
        }
      },
      "155": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 368,
          column: 5
        }
      },
      "156": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 367,
          column: 69
        }
      },
      "157": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "158": {
        start: {
          line: 370,
          column: 24
        },
        end: {
          line: 373,
          column: 10
        }
      },
      "159": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 376,
          column: 9
        }
      },
      "160": {
        start: {
          line: 375,
          column: 12
        },
        end: {
          line: 375,
          column: 68
        }
      },
      "161": {
        start: {
          line: 378,
          column: 8
        },
        end: {
          line: 381,
          column: 9
        }
      },
      "162": {
        start: {
          line: 379,
          column: 12
        },
        end: {
          line: 379,
          column: 105
        }
      },
      "163": {
        start: {
          line: 379,
          column: 62
        },
        end: {
          line: 379,
          column: 103
        }
      },
      "164": {
        start: {
          line: 380,
          column: 12
        },
        end: {
          line: 380,
          column: 33
        }
      },
      "165": {
        start: {
          line: 382,
          column: 8
        },
        end: {
          line: 382,
          column: 90
        }
      },
      "166": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 390,
          column: 11
        }
      },
      "167": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 393,
          column: 65
        }
      },
      "168": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 394,
          column: 72
        }
      },
      "169": {
        start: {
          line: 400,
          column: 0
        },
        end: {
          line: 447,
          column: 3
        }
      },
      "170": {
        start: {
          line: 401,
          column: 19
        },
        end: {
          line: 401,
          column: 32
        }
      },
      "171": {
        start: {
          line: 402,
          column: 31
        },
        end: {
          line: 402,
          column: 41
        }
      },
      "172": {
        start: {
          line: 403,
          column: 27
        },
        end: {
          line: 403,
          column: 35
        }
      },
      "173": {
        start: {
          line: 404,
          column: 4
        },
        end: {
          line: 406,
          column: 5
        }
      },
      "174": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 405,
          column: 69
        }
      },
      "175": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 446,
          column: 5
        }
      },
      "176": {
        start: {
          line: 409,
          column: 29
        },
        end: {
          line: 413,
          column: 10
        }
      },
      "177": {
        start: {
          line: 414,
          column: 8
        },
        end: {
          line: 416,
          column: 9
        }
      },
      "178": {
        start: {
          line: 415,
          column: 12
        },
        end: {
          line: 415,
          column: 73
        }
      },
      "179": {
        start: {
          line: 417,
          column: 20
        },
        end: {
          line: 421,
          column: 9
        }
      },
      "180": {
        start: {
          line: 423,
          column: 8
        },
        end: {
          line: 425,
          column: 9
        }
      },
      "181": {
        start: {
          line: 424,
          column: 12
        },
        end: {
          line: 424,
          column: 44
        }
      },
      "182": {
        start: {
          line: 427,
          column: 23
        },
        end: {
          line: 430,
          column: 10
        }
      },
      "183": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 77
        }
      },
      "184": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 133
        }
      },
      "185": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 441,
          column: 11
        }
      },
      "186": {
        start: {
          line: 444,
          column: 8
        },
        end: {
          line: 444,
          column: 72
        }
      },
      "187": {
        start: {
          line: 445,
          column: 8
        },
        end: {
          line: 445,
          column: 78
        }
      },
      "188": {
        start: {
          line: 451,
          column: 0
        },
        end: {
          line: 515,
          column: 3
        }
      },
      "189": {
        start: {
          line: 452,
          column: 19
        },
        end: {
          line: 452,
          column: 32
        }
      },
      "190": {
        start: {
          line: 453,
          column: 104
        },
        end: {
          line: 453,
          column: 113
        }
      },
      "191": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 456,
          column: 5
        }
      },
      "192": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 69
        }
      },
      "193": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 459,
          column: 5
        }
      },
      "194": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 89
        }
      },
      "195": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 514,
          column: 5
        }
      },
      "196": {
        start: {
          line: 462,
          column: 34
        },
        end: {
          line: 465,
          column: 24
        }
      },
      "197": {
        start: {
          line: 466,
          column: 32
        },
        end: {
          line: 466,
          column: 65
        }
      },
      "198": {
        start: {
          line: 466,
          column: 59
        },
        end: {
          line: 466,
          column: 64
        }
      },
      "199": {
        start: {
          line: 468,
          column: 29
        },
        end: {
          line: 472,
          column: 9
        }
      },
      "200": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 475,
          column: 9
        }
      },
      "201": {
        start: {
          line: 474,
          column: 12
        },
        end: {
          line: 474,
          column: 51
        }
      },
      "202": {
        start: {
          line: 476,
          column: 8
        },
        end: {
          line: 482,
          column: 9
        }
      },
      "203": {
        start: {
          line: 477,
          column: 12
        },
        end: {
          line: 477,
          column: 40
        }
      },
      "204": {
        start: {
          line: 478,
          column: 12
        },
        end: {
          line: 479,
          column: 65
        }
      },
      "205": {
        start: {
          line: 479,
          column: 16
        },
        end: {
          line: 479,
          column: 65
        }
      },
      "206": {
        start: {
          line: 480,
          column: 12
        },
        end: {
          line: 481,
          column: 63
        }
      },
      "207": {
        start: {
          line: 481,
          column: 16
        },
        end: {
          line: 481,
          column: 63
        }
      },
      "208": {
        start: {
          line: 484,
          column: 24
        },
        end: {
          line: 484,
          column: 38
        }
      },
      "209": {
        start: {
          line: 485,
          column: 25
        },
        end: {
          line: 485,
          column: 40
        }
      },
      "210": {
        start: {
          line: 486,
          column: 21
        },
        end: {
          line: 486,
          column: 45
        }
      },
      "211": {
        start: {
          line: 487,
          column: 39
        },
        end: {
          line: 496,
          column: 10
        }
      },
      "212": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 509,
          column: 11
        }
      },
      "213": {
        start: {
          line: 512,
          column: 8
        },
        end: {
          line: 512,
          column: 66
        }
      },
      "214": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 513,
          column: 72
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 12,
            column: 63
          },
          end: {
            line: 12,
            column: 64
          }
        },
        loc: {
          start: {
            line: 12,
            column: 83
          },
          end: {
            line: 128,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 74,
            column: 52
          },
          end: {
            line: 74,
            column: 53
          }
        },
        loc: {
          start: {
            line: 74,
            column: 59
          },
          end: {
            line: 75,
            column: 33
          }
        },
        line: 74
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 78,
            column: 52
          },
          end: {
            line: 78,
            column: 53
          }
        },
        loc: {
          start: {
            line: 78,
            column: 59
          },
          end: {
            line: 78,
            column: 66
          }
        },
        line: 78
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 86,
            column: 47
          },
          end: {
            line: 86,
            column: 48
          }
        },
        loc: {
          start: {
            line: 86,
            column: 59
          },
          end: {
            line: 94,
            column: 9
          }
        },
        line: 86
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 132,
            column: 51
          },
          end: {
            line: 132,
            column: 52
          }
        },
        loc: {
          start: {
            line: 132,
            column: 71
          },
          end: {
            line: 192,
            column: 1
          }
        },
        line: 132
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 149,
            column: 58
          },
          end: {
            line: 149,
            column: 59
          }
        },
        loc: {
          start: {
            line: 149,
            column: 63
          },
          end: {
            line: 149,
            column: 97
          }
        },
        line: 149
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 196,
            column: 51
          },
          end: {
            line: 196,
            column: 52
          }
        },
        loc: {
          start: {
            line: 196,
            column: 71
          },
          end: {
            line: 246,
            column: 1
          }
        },
        line: 196
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 250,
            column: 53
          },
          end: {
            line: 250,
            column: 54
          }
        },
        loc: {
          start: {
            line: 250,
            column: 73
          },
          end: {
            line: 294,
            column: 1
          }
        },
        line: 250
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 298,
            column: 54
          },
          end: {
            line: 298,
            column: 55
          }
        },
        loc: {
          start: {
            line: 298,
            column: 74
          },
          end: {
            line: 359,
            column: 1
          }
        },
        line: 298
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 326,
            column: 67
          },
          end: {
            line: 326,
            column: 68
          }
        },
        loc: {
          start: {
            line: 326,
            column: 72
          },
          end: {
            line: 326,
            column: 113
          }
        },
        line: 326
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 363,
            column: 54
          },
          end: {
            line: 363,
            column: 55
          }
        },
        loc: {
          start: {
            line: 363,
            column: 74
          },
          end: {
            line: 396,
            column: 1
          }
        },
        line: 363
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 379,
            column: 57
          },
          end: {
            line: 379,
            column: 58
          }
        },
        loc: {
          start: {
            line: 379,
            column: 62
          },
          end: {
            line: 379,
            column: 103
          }
        },
        line: 379
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 400,
            column: 58
          },
          end: {
            line: 400,
            column: 59
          }
        },
        loc: {
          start: {
            line: 400,
            column: 78
          },
          end: {
            line: 447,
            column: 1
          }
        },
        line: 400
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 451,
            column: 54
          },
          end: {
            line: 451,
            column: 55
          }
        },
        loc: {
          start: {
            line: 451,
            column: 74
          },
          end: {
            line: 515,
            column: 1
          }
        },
        line: 451
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 466,
            column: 54
          },
          end: {
            line: 466,
            column: 55
          }
        },
        loc: {
          start: {
            line: 466,
            column: 59
          },
          end: {
            line: 466,
            column: 64
          }
        },
        line: 466
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 12
          },
          end: {
            line: 15,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 20
          }
        }],
        line: 15
      },
      "1": {
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 15,
            column: 30
          },
          end: {
            line: 15,
            column: 32
          }
        }],
        line: 15
      },
      "2": {
        loc: {
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "3": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 30,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 30,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "4": {
        loc: {
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "5": {
        loc: {
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 23
          }
        }, {
          start: {
            line: 37,
            column: 27
          },
          end: {
            line: 37,
            column: 48
          }
        }],
        line: 37
      },
      "6": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "7": {
        loc: {
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 50,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "8": {
        loc: {
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 49,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 49,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 47
      },
      "9": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 56,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 56,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "10": {
        loc: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 55,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 55,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "11": {
        loc: {
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 68,
            column: 40
          },
          end: {
            line: 68,
            column: 41
          }
        }, {
          start: {
            line: 68,
            column: 44
          },
          end: {
            line: 68,
            column: 48
          }
        }],
        line: 68
      },
      "12": {
        loc: {
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 22
          },
          end: {
            line: 68,
            column: 28
          }
        }, {
          start: {
            line: 68,
            column: 32
          },
          end: {
            line: 68,
            column: 37
          }
        }],
        line: 68
      },
      "13": {
        loc: {
          start: {
            line: 74,
            column: 59
          },
          end: {
            line: 75,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 59
          },
          end: {
            line: 74,
            column: 106
          }
        }, {
          start: {
            line: 75,
            column: 12
          },
          end: {
            line: 75,
            column: 33
          }
        }],
        line: 74
      },
      "14": {
        loc: {
          start: {
            line: 76,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "15": {
        loc: {
          start: {
            line: 89,
            column: 21
          },
          end: {
            line: 91,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 21
          },
          end: {
            line: 89,
            column: 74
          }
        }, {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 90,
            column: 46
          }
        }, {
          start: {
            line: 91,
            column: 16
          },
          end: {
            line: 91,
            column: 34
          }
        }],
        line: 89
      },
      "16": {
        loc: {
          start: {
            line: 92,
            column: 23
          },
          end: {
            line: 93,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 23
          },
          end: {
            line: 92,
            column: 76
          }
        }, {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 34
          }
        }],
        line: 92
      },
      "17": {
        loc: {
          start: {
            line: 111,
            column: 32
          },
          end: {
            line: 111,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 111,
            column: 54
          },
          end: {
            line: 111,
            column: 69
          }
        }, {
          start: {
            line: 111,
            column: 72
          },
          end: {
            line: 111,
            column: 76
          }
        }],
        line: 111
      },
      "18": {
        loc: {
          start: {
            line: 112,
            column: 31
          },
          end: {
            line: 112,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 53
          },
          end: {
            line: 112,
            column: 86
          }
        }, {
          start: {
            line: 112,
            column: 89
          },
          end: {
            line: 112,
            column: 93
          }
        }],
        line: 112
      },
      "19": {
        loc: {
          start: {
            line: 135,
            column: 21
          },
          end: {
            line: 135,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 135,
            column: 35
          },
          end: {
            line: 135,
            column: 41
          }
        }],
        line: 135
      },
      "20": {
        loc: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "21": {
        loc: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "22": {
        loc: {
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 139,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 139,
            column: 16
          }
        }, {
          start: {
            line: 139,
            column: 20
          },
          end: {
            line: 139,
            column: 47
          }
        }],
        line: 139
      },
      "23": {
        loc: {
          start: {
            line: 145,
            column: 8
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 8
          },
          end: {
            line: 147,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "24": {
        loc: {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 25
          }
        }, {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 100
          }
        }],
        line: 145
      },
      "25": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "26": {
        loc: {
          start: {
            line: 157,
            column: 12
          },
          end: {
            line: 159,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 12
          },
          end: {
            line: 159,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "27": {
        loc: {
          start: {
            line: 169,
            column: 21
          },
          end: {
            line: 169,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 31
          },
          end: {
            line: 169,
            column: 69
          }
        }, {
          start: {
            line: 169,
            column: 72
          },
          end: {
            line: 169,
            column: 81
          }
        }],
        line: 169
      },
      "28": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "29": {
        loc: {
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "30": {
        loc: {
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 203,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 203,
            column: 16
          }
        }, {
          start: {
            line: 203,
            column: 20
          },
          end: {
            line: 203,
            column: 47
          }
        }],
        line: 203
      },
      "31": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 215,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 215,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "32": {
        loc: {
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "33": {
        loc: {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 225,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 225,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "34": {
        loc: {
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 253,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 253,
            column: 32
          },
          end: {
            line: 253,
            column: 37
          }
        }],
        line: 253
      },
      "35": {
        loc: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "36": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 265,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 265,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "37": {
        loc: {
          start: {
            line: 269,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 8
          },
          end: {
            line: 271,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "38": {
        loc: {
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 269,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 269,
            column: 29
          }
        }, {
          start: {
            line: 269,
            column: 33
          },
          end: {
            line: 269,
            column: 69
          }
        }],
        line: 269
      },
      "39": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "40": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "41": {
        loc: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "42": {
        loc: {
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 306,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 306,
            column: 8
          },
          end: {
            line: 306,
            column: 17
          }
        }, {
          start: {
            line: 306,
            column: 21
          },
          end: {
            line: 306,
            column: 55
          }
        }],
        line: 306
      },
      "43": {
        loc: {
          start: {
            line: 314,
            column: 8
          },
          end: {
            line: 316,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 314,
            column: 8
          },
          end: {
            line: 316,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 314
      },
      "44": {
        loc: {
          start: {
            line: 322,
            column: 8
          },
          end: {
            line: 324,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 322,
            column: 8
          },
          end: {
            line: 324,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 322
      },
      "45": {
        loc: {
          start: {
            line: 326,
            column: 38
          },
          end: {
            line: 326,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 326,
            column: 38
          },
          end: {
            line: 326,
            column: 114
          }
        }, {
          start: {
            line: 326,
            column: 118
          },
          end: {
            line: 326,
            column: 120
          }
        }],
        line: 326
      },
      "46": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        }, {
          start: {
            line: 332,
            column: 13
          },
          end: {
            line: 342,
            column: 9
          }
        }],
        line: 327
      },
      "47": {
        loc: {
          start: {
            line: 334,
            column: 12
          },
          end: {
            line: 336,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 12
          },
          end: {
            line: 336,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "48": {
        loc: {
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "49": {
        loc: {
          start: {
            line: 374,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      },
      "50": {
        loc: {
          start: {
            line: 378,
            column: 8
          },
          end: {
            line: 381,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 378,
            column: 8
          },
          end: {
            line: 381,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 378
      },
      "51": {
        loc: {
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "52": {
        loc: {
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 416,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 416,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 414
      },
      "53": {
        loc: {
          start: {
            line: 423,
            column: 8
          },
          end: {
            line: 425,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 423,
            column: 8
          },
          end: {
            line: 425,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 423
      },
      "54": {
        loc: {
          start: {
            line: 423,
            column: 12
          },
          end: {
            line: 423,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 423,
            column: 12
          },
          end: {
            line: 423,
            column: 22
          }
        }, {
          start: {
            line: 423,
            column: 26
          },
          end: {
            line: 423,
            column: 51
          }
        }, {
          start: {
            line: 423,
            column: 55
          },
          end: {
            line: 423,
            column: 76
          }
        }],
        line: 423
      },
      "55": {
        loc: {
          start: {
            line: 453,
            column: 61
          },
          end: {
            line: 453,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 453,
            column: 68
          },
          end: {
            line: 453,
            column: 69
          }
        }],
        line: 453
      },
      "56": {
        loc: {
          start: {
            line: 453,
            column: 71
          },
          end: {
            line: 453,
            column: 81
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 453,
            column: 79
          },
          end: {
            line: 453,
            column: 81
          }
        }],
        line: 453
      },
      "57": {
        loc: {
          start: {
            line: 454,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 454,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 454
      },
      "58": {
        loc: {
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 459,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 459,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 457
      },
      "59": {
        loc: {
          start: {
            line: 457,
            column: 8
          },
          end: {
            line: 457,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 457,
            column: 8
          },
          end: {
            line: 457,
            column: 20
          }
        }, {
          start: {
            line: 457,
            column: 24
          },
          end: {
            line: 457,
            column: 53
          }
        }],
        line: 457
      },
      "60": {
        loc: {
          start: {
            line: 469,
            column: 28
          },
          end: {
            line: 469,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 469,
            column: 45
          },
          end: {
            line: 469,
            column: 59
          }
        }, {
          start: {
            line: 469,
            column: 62
          },
          end: {
            line: 469,
            column: 86
          }
        }],
        line: 469
      },
      "61": {
        loc: {
          start: {
            line: 473,
            column: 8
          },
          end: {
            line: 475,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 473,
            column: 8
          },
          end: {
            line: 475,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 473
      },
      "62": {
        loc: {
          start: {
            line: 473,
            column: 12
          },
          end: {
            line: 473,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 473,
            column: 12
          },
          end: {
            line: 473,
            column: 23
          }
        }, {
          start: {
            line: 473,
            column: 27
          },
          end: {
            line: 473,
            column: 48
          }
        }],
        line: 473
      },
      "63": {
        loc: {
          start: {
            line: 476,
            column: 8
          },
          end: {
            line: 482,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 476,
            column: 8
          },
          end: {
            line: 482,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 476
      },
      "64": {
        loc: {
          start: {
            line: 476,
            column: 12
          },
          end: {
            line: 476,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 476,
            column: 12
          },
          end: {
            line: 476,
            column: 20
          }
        }, {
          start: {
            line: 476,
            column: 24
          },
          end: {
            line: 476,
            column: 30
          }
        }],
        line: 476
      },
      "65": {
        loc: {
          start: {
            line: 478,
            column: 12
          },
          end: {
            line: 479,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 478,
            column: 12
          },
          end: {
            line: 479,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 478
      },
      "66": {
        loc: {
          start: {
            line: 480,
            column: 12
          },
          end: {
            line: 481,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 480,
            column: 12
          },
          end: {
            line: 481,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 480
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0, 0],
      "55": [0],
      "56": [0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\message.controller.ts",
      mappings: ";;;AACA,uCAAiC;AACjC,yDAA+D;AAC/D,4CAAyC;AACzC,gDAA6C;AAC7C,oDAAiD;AAEjD;;GAEG;AACU,QAAA,uBAAuB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACtF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACtC,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,EAAE,qDAAqD;IAC7D,KAAK,EAAG,mCAAmC;IAC3C,WAAW,EACX,MAAM,EACP,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC;QACH,6CAA6C;QAC7C,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;YAC9C,GAAG,EAAE,cAAc;YACnB,YAAY,EAAE,MAAM;YACpB,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,mBAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,cAAc;QACd,MAAM,KAAK,GAAQ;YACjB,cAAc;YACd,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,0BAA0B;QAC1B,IAAI,WAAW,IAAI,WAAW,KAAK,KAAK,EAAE,CAAC;YACzC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QAClC,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;QACpD,CAAC;QAED,iCAAiC;QACjC,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,aAAa,GAAG,MAAM,sBAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,aAAa,EAAE,CAAC;gBAClB,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,aAAa,CAAC,SAAS,EAAE,CAAC;YACrD,CAAC;QACH,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,sBAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACnD,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC;YACpD,CAAC;QACH,CAAC;QAED,aAAa;QACb,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAc,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAe,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,+BAA+B;QAC1F,MAAM,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;QAEtC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC/C,sBAAO,CAAC,IAAI,CAAC,KAAK,CAAC;iBAChB,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC;iBACjD,QAAQ,CAAC,YAAY,EAAE,2BAA2B,CAAC;iBACnD,QAAQ,CAAC,SAAS,EAAE,8BAA8B,CAAC;iBACnD,QAAQ,CAAC,qBAAqB,EAAE,4CAA4C,CAAC;iBAC7E,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,oBAAoB;iBAC5C,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,mCAAmC;iBACpE,KAAK,CAAC,QAAQ,CAAC;iBACf,IAAI,EAAE;YACT,sBAAO,CAAC,cAAc,CAAC,KAAK,CAAC;SAC9B,CAAC,CAAC;QAEH,qDAAqD;QACrD,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAChD,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;YAC/C,GAAG,CAAC,MAAM,KAAK,MAAM,CACtB,CAAC;QAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,sBAAO,CAAC,UAAU,CACtB;gBACE,GAAG,EAAE,EAAE,GAAG,EAAE,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrD,MAAM,EAAE,MAAM;aACf,EACD;gBACE,MAAM,EAAE,WAAW;gBACnB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CACF,CAAC;QACJ,CAAC;QAED,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACjD,GAAG,OAAO;YACV,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;gBACrD,OAAO,CAAC,WAAW,KAAK,MAAM;gBAC9B,CAAC,OAAO,CAAC,SAAS;YAC3B,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;gBACrD,CAAC,OAAO,CAAC,SAAS;SAC9B,CAAC,CAAC,CAAC;QAEJ,sBAAsB;QACtB,MAAM,OAAO,GAAG,UAAU,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,OAAO,GAAG,CAAC,CAAC;QAEhC,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;oBACvC,OAAO;oBACP,WAAW;oBACX,kCAAkC;oBAClC,OAAO,EAAE;wBACP,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;wBACpD,KAAK,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;qBACtE;iBACF;gBACD,gBAAgB,EAAE;oBAChB,EAAE,EAAE,YAAY,CAAC,GAAG;oBACpB,IAAI,EAAE,YAAY,CAAC,gBAAgB;oBACnC,gBAAgB,EAAE,YAAY,CAAC,YAAY,CAAC,MAAM;oBAClD,WAAW,EAAE,YAAY,CAAC,cAAc,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACrE;aACF;SACF,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC1E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACtC,MAAM,EAAE,OAAO,EAAE,WAAW,GAAG,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAEtE,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5C,MAAM,IAAI,mBAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,CAAC;QACH,2CAA2C;QAC3C,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YAClF,MAAM,IAAI,mBAAQ,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;QACtE,CAAC;QAED,6CAA6C;QAC7C,MAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE3F,wCAAwC;QACxC,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,cAAc,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;gBAC3C,GAAG,EAAE,OAAO;gBACZ,cAAc;gBACd,SAAS,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,mBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,MAAM,OAAO,GAAG,IAAI,sBAAO,CAAC;YAC1B,cAAc;YACd,QAAQ,EAAE,MAAM;YAChB,UAAU;YACV,WAAW;YACX,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,QAAQ;YACR,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;YAC1D,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAErB,sBAAsB;QACtB,MAAM,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE9C,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,MAAM,sBAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;aACzD,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC;aACjD,QAAQ,CAAC,YAAY,EAAE,2BAA2B,CAAC;aACnD,QAAQ,CAAC,SAAS,EAAE,8BAA8B,CAAC;aACnD,QAAQ,CAAC,qBAAqB,EAAE,4CAA4C,CAAC,CAAC;QAEjF,eAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,oBAAoB,cAAc,EAAE,CAAC,CAAC;QAE7E,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE;YACnC,OAAO,EAAE,2BAA2B;SACrC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC1E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE7B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5C,MAAM,IAAI,mBAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,SAAS;YACd,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,mBAAQ,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;QACnE,CAAC;QAED,iDAAiD;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC5D,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAEnD,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;YAC5B,MAAM,IAAI,mBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5C,CAAC;QAED,iBAAiB;QACjB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QACxB,OAAO,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAErB,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,MAAM,sBAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;aACzD,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC;aACjD,QAAQ,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;QAEvD,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,mBAAmB,MAAM,EAAE,CAAC,CAAC;QAE7D,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE;YACnC,OAAO,EAAE,6BAA6B;SACvC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,aAAa,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC5E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE/C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,SAAS;YACd,QAAQ,EAAE,MAAM;YAChB,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,mBAAQ,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;QAClE,CAAC;QAED,wDAAwD;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC5D,MAAM,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS;QAEzD,IAAI,iBAAiB,IAAI,UAAU,GAAG,uBAAuB,EAAE,CAAC;YAC9D,MAAM,IAAI,mBAAQ,CAAC,2CAA2C,EAAE,GAAG,CAAC,CAAC;QACvE,CAAC;QAED,sBAAsB;QACtB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,OAAO,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE/C,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,OAAO,GAAG,0BAA0B,CAAC;QAC/C,CAAC;QAED,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAErB,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,oBAAoB,MAAM,wBAAwB,iBAAiB,GAAG,CAAC,CAAC;QAExG,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,SAAS;gBACT,kBAAkB,EAAE,iBAAiB;aACtC;YACD,OAAO,EAAE,8BAA8B;SACxC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAC/C,MAAM,IAAI,mBAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE9B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACxE,IAAI,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACpD,MAAM,IAAI,mBAAQ,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,SAAS;YACd,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,mBAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAED,iDAAiD;QACjD,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;YAC9C,GAAG,EAAE,OAAO,CAAC,cAAc;YAC3B,YAAY,EAAE,MAAM;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,mBAAQ,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,gCAAgC;QAChC,MAAM,qBAAqB,GAAG,OAAO,CAAC,SAAS,EAAE,SAAS,CACxD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAC/C,IAAI,CAAC,CAAC,CAAC;QAER,IAAI,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC;YAC/B,2BAA2B;YAC3B,OAAO,CAAC,SAAU,CAAC,qBAAqB,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC9D,OAAO,CAAC,SAAU,CAAC,qBAAqB,CAAC,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBACvB,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;YACzB,CAAC;YACD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;gBACrB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAClC,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAErB,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,uBAAuB,SAAS,SAAS,QAAQ,EAAE,CAAC,CAAC;QAE/E,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,SAAS;gBACT,QAAQ;gBACR,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B;YACD,OAAO,EAAE,6BAA6B;SACvC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,IAAI,mBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,sBAAO,CAAC,OAAO,CAAC;YACpC,GAAG,EAAE,SAAS;YACd,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,mBAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAC/C,CAAC;YACF,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,kCAAkC,SAAS,EAAE,CAAC,CAAC;QAEzE,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,SAAS;gBACT,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B;YACD,OAAO,EAAE,+BAA+B;SACzC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,IAAI,mBAAQ,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,kBAAkB,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACjF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACtC,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,iDAAiD;IAElF,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,YAAY,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;YAC9C,GAAG,EAAE,cAAc;YACnB,YAAY,EAAE,MAAM;YACpB,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,KAAK,GAAQ;YACf,cAAc;YACd,UAAU,EAAE,MAAM;YAClB,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;SACvC,CAAC;QAEF,oDAAoD;QACpD,IAAI,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrE,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;QAClC,CAAC;QAED,iCAAiC;QACjC,MAAM,MAAM,GAAG,MAAM,sBAAO,CAAC,UAAU,CAAC,KAAK,EAAE;YAC7C,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,IAAI,IAAI,EAAE;SACnB,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,YAAY,CAAC,UAAU,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAE1D,eAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,aAAa,8BAA8B,MAAM,oBAAoB,cAAc,EAAE,CAAC,CAAC;QAEpH,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,WAAW,EAAE,MAAM,CAAC,aAAa;gBACjC,cAAc;aACf;YACD,OAAO,EAAE,yBAAyB;SACnC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,IAAI,mBAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,cAAc,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7E,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7B,MAAM,EACJ,KAAK,EAAE,WAAW,EAClB,cAAc,EACd,WAAW,EACX,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,QAAQ,EACR,MAAM,EACP,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,mBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC,WAAW,IAAK,WAAsB,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9D,MAAM,IAAI,mBAAQ,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,CAAC;QACH,2BAA2B;QAC3B,MAAM,iBAAiB,GAAG,MAAM,2BAAY,CAAC,IAAI,CAAC;YAChD,YAAY,EAAE,MAAM;YACpB,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SAC3B,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEjB,MAAM,eAAe,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAE1D,qBAAqB;QACrB,MAAM,YAAY,GAAQ;YACxB,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,eAAe,EAAE;YAC1E,OAAO,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE;YAC/C,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,IAAI,WAAW,IAAI,WAAW,KAAK,KAAK,EAAE,CAAC;YACzC,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;QACzC,CAAC;QAED,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;YACvB,YAAY,CAAC,SAAS,GAAG,EAAE,CAAC;YAC5B,IAAI,QAAQ;gBAAE,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,QAAkB,CAAC,CAAC;YACzE,IAAI,MAAM;gBAAE,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,MAAgB,CAAC,CAAC;QACvE,CAAC;QAED,aAAa;QACb,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAc,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAe,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;QAEtC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC/C,sBAAO,CAAC,IAAI,CAAC,YAAY,CAAC;iBACvB,QAAQ,CAAC,UAAU,EAAE,2BAA2B,CAAC;iBACjD,QAAQ,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;iBACjE,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;iBACvB,IAAI,CAAC,IAAI,CAAC;iBACV,KAAK,CAAC,QAAQ,CAAC;iBACf,IAAI,EAAE;YACT,sBAAO,CAAC,cAAc,CAAC,YAAY,CAAC;SACrC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,QAAQ;gBACR,UAAU,EAAE;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;iBACxC;gBACD,WAAW;aACZ;SACF,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,mBAAQ,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\message.controller.ts"],
      sourcesContent: ["import { Request, Response } from 'express';\r\nimport { Types } from 'mongoose';\r\nimport { Conversation, Message } from '../models/Conversation';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\nimport { catchAsync } from '../utils/catchAsync';\r\n\r\n/**\r\n * Get messages for a conversation with pagination\r\n */\r\nexport const getConversationMessages = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { conversationId } = req.params;\r\n  const { \r\n    page = 1, \r\n    limit = 50, \r\n    before, // Message ID to get messages before (for pagination)\r\n    after,  // Message ID to get messages after\r\n    messageType,\r\n    search\r\n  } = req.query;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  try {\r\n    // Verify user is participant in conversation\r\n    const conversation = await Conversation.findOne({\r\n      _id: conversationId,\r\n      participants: userId,\r\n      status: { $ne: 'deleted' }\r\n    });\r\n\r\n    if (!conversation) {\r\n      throw new AppError('Conversation not found or access denied', 404);\r\n    }\r\n\r\n    // Build query\r\n    const query: any = {\r\n      conversationId,\r\n      isDeleted: false\r\n    };\r\n\r\n    // Add message type filter\r\n    if (messageType && messageType !== 'all') {\r\n      query.messageType = messageType;\r\n    }\r\n\r\n    // Add search filter\r\n    if (search) {\r\n      query.content = { $regex: search, $options: 'i' };\r\n    }\r\n\r\n    // Handle cursor-based pagination\r\n    if (before) {\r\n      const beforeMessage = await Message.findById(before);\r\n      if (beforeMessage) {\r\n        query.createdAt = { $lt: beforeMessage.createdAt };\r\n      }\r\n    }\r\n\r\n    if (after) {\r\n      const afterMessage = await Message.findById(after);\r\n      if (afterMessage) {\r\n        query.createdAt = { $gt: afterMessage.createdAt };\r\n      }\r\n    }\r\n\r\n    // Pagination\r\n    const pageNum = parseInt(page as string);\r\n    const limitNum = Math.min(parseInt(limit as string), 100); // Max 100 messages per request\r\n    const skip = (pageNum - 1) * limitNum;\r\n\r\n    const [messages, totalCount] = await Promise.all([\r\n      Message.find(query)\r\n        .populate('senderId', 'firstName lastName avatar')\r\n        .populate('receiverId', 'firstName lastName avatar')\r\n        .populate('replyTo', 'content senderId messageType')\r\n        .populate('metadata.propertyId', 'title propertyType location pricing photos')\r\n        .sort({ createdAt: -1 }) // Most recent first\r\n        .skip(before || after ? 0 : skip) // Skip only for regular pagination\r\n        .limit(limitNum)\r\n        .lean(),\r\n      Message.countDocuments(query)\r\n    ]);\r\n\r\n    // Mark messages as delivered for the requesting user\r\n    const undeliveredMessages = messages.filter(msg => \r\n      msg.receiverId.toString() === userId.toString() && \r\n      msg.status === 'sent'\r\n    );\r\n\r\n    if (undeliveredMessages.length > 0) {\r\n      await Message.updateMany(\r\n        {\r\n          _id: { $in: undeliveredMessages.map(msg => msg._id) },\r\n          status: 'sent'\r\n        },\r\n        {\r\n          status: 'delivered',\r\n          deliveredAt: new Date()\r\n        }\r\n      );\r\n    }\r\n\r\n    // Format messages for response\r\n    const formattedMessages = messages.map(message => ({\r\n      ...message,\r\n      isSentByUser: message.senderId._id.toString() === userId.toString(),\r\n      canEdit: message.senderId._id.toString() === userId.toString() && \r\n               message.messageType === 'text' && \r\n               !message.isDeleted,\r\n      canDelete: message.senderId._id.toString() === userId.toString() && \r\n                 !message.isDeleted\r\n    }));\r\n\r\n    // Get pagination info\r\n    const hasMore = totalCount > (pageNum * limitNum);\r\n    const hasPrevious = pageNum > 1;\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        messages: formattedMessages,\r\n        pagination: {\r\n          page: pageNum,\r\n          limit: limitNum,\r\n          total: totalCount,\r\n          pages: Math.ceil(totalCount / limitNum),\r\n          hasMore,\r\n          hasPrevious,\r\n          // Cursor info for infinite scroll\r\n          cursors: {\r\n            before: messages.length > 0 ? messages[0]._id : null,\r\n            after: messages.length > 0 ? messages[messages.length - 1]._id : null\r\n          }\r\n        },\r\n        conversationInfo: {\r\n          id: conversation._id,\r\n          type: conversation.conversationType,\r\n          participantCount: conversation.participants.length,\r\n          unreadCount: conversation.getUnreadCount(new Types.ObjectId(userId))\r\n        }\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error getting conversation messages:', error);\r\n    throw new AppError('Failed to get messages', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Send a message\r\n */\r\nexport const sendMessage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { conversationId } = req.params;\r\n  const { content, messageType = 'text', metadata, replyTo } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!content || content.trim().length === 0) {\r\n    throw new AppError('Message content is required', 400);\r\n  }\r\n\r\n  try {\r\n    // Verify conversation and user permissions\r\n    const conversation = await Conversation.findById(conversationId);\r\n    if (!conversation || !conversation.canUserSendMessage(new Types.ObjectId(userId))) {\r\n      throw new AppError('Cannot send message to this conversation', 403);\r\n    }\r\n\r\n    // Get receiver ID (for direct conversations)\r\n    const receiverId = conversation.participants.find(p => p.toString() !== userId.toString());\r\n\r\n    // Validate reply-to message if provided\r\n    if (replyTo) {\r\n      const replyToMessage = await Message.findOne({\r\n        _id: replyTo,\r\n        conversationId,\r\n        isDeleted: false\r\n      });\r\n\r\n      if (!replyToMessage) {\r\n        throw new AppError('Reply-to message not found', 404);\r\n      }\r\n    }\r\n\r\n    // Create message\r\n    const message = new Message({\r\n      conversationId,\r\n      senderId: userId,\r\n      receiverId,\r\n      messageType,\r\n      content: content.trim(),\r\n      metadata,\r\n      replyTo: replyTo ? new Types.ObjectId(replyTo) : undefined,\r\n      status: 'sent'\r\n    });\r\n\r\n    await message.save();\r\n\r\n    // Update conversation\r\n    await conversation.updateLastMessage(message);\r\n\r\n    // Populate message for response\r\n    const populatedMessage = await Message.findById(message._id)\r\n      .populate('senderId', 'firstName lastName avatar')\r\n      .populate('receiverId', 'firstName lastName avatar')\r\n      .populate('replyTo', 'content senderId messageType')\r\n      .populate('metadata.propertyId', 'title propertyType location pricing photos');\r\n\r\n    logger.info(`Message sent from ${userId} to conversation ${conversationId}`);\r\n\r\n    return res.status(201).json({\r\n      success: true,\r\n      data: { message: populatedMessage },\r\n      message: 'Message sent successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error sending message:', error);\r\n    throw new AppError('Failed to send message', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Edit a message\r\n */\r\nexport const editMessage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { messageId } = req.params;\r\n  const { content } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!content || content.trim().length === 0) {\r\n    throw new AppError('Message content is required', 400);\r\n  }\r\n\r\n  try {\r\n    const message = await Message.findOne({\r\n      _id: messageId,\r\n      senderId: userId,\r\n      messageType: 'text',\r\n      isDeleted: false\r\n    });\r\n\r\n    if (!message) {\r\n      throw new AppError('Message not found or cannot be edited', 404);\r\n    }\r\n\r\n    // Check if message is too old to edit (24 hours)\r\n    const messageAge = Date.now() - message.createdAt.getTime();\r\n    const maxEditAge = 24 * 60 * 60 * 1000; // 24 hours\r\n\r\n    if (messageAge > maxEditAge) {\r\n      throw new AppError('Message is too old to edit', 400);\r\n    }\r\n\r\n    // Store original content if not already edited\r\n    if (!message.isEdited) {\r\n      message.originalContent = message.content;\r\n    }\r\n\r\n    // Update message\r\n    message.content = content.trim();\r\n    message.isEdited = true;\r\n    message.editedAt = new Date();\r\n\r\n    await message.save();\r\n\r\n    // Populate message for response\r\n    const populatedMessage = await Message.findById(message._id)\r\n      .populate('senderId', 'firstName lastName avatar')\r\n      .populate('receiverId', 'firstName lastName avatar');\r\n\r\n    logger.info(`Message ${messageId} edited by user ${userId}`);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: { message: populatedMessage },\r\n      message: 'Message edited successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error editing message:', error);\r\n    throw new AppError('Failed to edit message', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Delete a message\r\n */\r\nexport const deleteMessage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { messageId } = req.params;\r\n  const { deleteForEveryone = false } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  try {\r\n    const message = await Message.findOne({\r\n      _id: messageId,\r\n      senderId: userId,\r\n      isDeleted: false\r\n    });\r\n\r\n    if (!message) {\r\n      throw new AppError('Message not found or already deleted', 404);\r\n    }\r\n\r\n    // Check if user can delete for everyone (within 1 hour)\r\n    const messageAge = Date.now() - message.createdAt.getTime();\r\n    const maxDeleteForEveryoneAge = 60 * 60 * 1000; // 1 hour\r\n\r\n    if (deleteForEveryone && messageAge > maxDeleteForEveryoneAge) {\r\n      throw new AppError('Message is too old to delete for everyone', 400);\r\n    }\r\n\r\n    // Soft delete message\r\n    message.isDeleted = true;\r\n    message.deletedAt = new Date();\r\n    message.deletedBy = new Types.ObjectId(userId);\r\n\r\n    if (deleteForEveryone) {\r\n      message.content = 'This message was deleted';\r\n    }\r\n\r\n    await message.save();\r\n\r\n    logger.info(`Message ${messageId} deleted by user ${userId} (deleteForEveryone: ${deleteForEveryone})`);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        messageId,\r\n        deletedForEveryone: deleteForEveryone\r\n      },\r\n      message: 'Message deleted successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error deleting message:', error);\r\n    throw new AppError('Failed to delete message', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * React to a message\r\n */\r\nexport const reactToMessage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { messageId } = req.params;\r\n  const { reaction } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  const validReactions = ['like', 'love', 'laugh', 'wow', 'sad', 'angry'];\r\n  if (!reaction || !validReactions.includes(reaction)) {\r\n    throw new AppError('Invalid reaction type', 400);\r\n  }\r\n\r\n  try {\r\n    const message = await Message.findOne({\r\n      _id: messageId,\r\n      isDeleted: false\r\n    });\r\n\r\n    if (!message) {\r\n      throw new AppError('Message not found', 404);\r\n    }\r\n\r\n    // Verify user is participant in the conversation\r\n    const conversation = await Conversation.findOne({\r\n      _id: message.conversationId,\r\n      participants: userId\r\n    });\r\n\r\n    if (!conversation) {\r\n      throw new AppError('Access denied', 403);\r\n    }\r\n\r\n    // Check if user already reacted\r\n    const existingReactionIndex = message.reactions?.findIndex(\r\n      r => r.userId.toString() === userId.toString()\r\n    ) ?? -1;\r\n\r\n    if (existingReactionIndex > -1) {\r\n      // Update existing reaction\r\n      message.reactions![existingReactionIndex].reaction = reaction;\r\n      message.reactions![existingReactionIndex].createdAt = new Date();\r\n    } else {\r\n      // Add new reaction\r\n      if (!message.reactions) {\r\n        message.reactions = [];\r\n      }\r\n      message.reactions.push({\r\n        userId: new Types.ObjectId(userId),\r\n        reaction,\r\n        createdAt: new Date()\r\n      });\r\n    }\r\n\r\n    await message.save();\r\n\r\n    logger.info(`User ${userId} reacted to message ${messageId} with ${reaction}`);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        messageId,\r\n        reaction,\r\n        reactions: message.reactions\r\n      },\r\n      message: 'Reaction added successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error reacting to message:', error);\r\n    throw new AppError('Failed to react to message', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Remove reaction from message\r\n */\r\nexport const removeReaction = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { messageId } = req.params;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  try {\r\n    const message = await Message.findOne({\r\n      _id: messageId,\r\n      isDeleted: false\r\n    });\r\n\r\n    if (!message) {\r\n      throw new AppError('Message not found', 404);\r\n    }\r\n\r\n    // Remove user's reaction\r\n    if (message.reactions) {\r\n      message.reactions = message.reactions.filter(\r\n        r => r.userId.toString() !== userId.toString()\r\n      );\r\n      await message.save();\r\n    }\r\n\r\n    logger.info(`User ${userId} removed reaction from message ${messageId}`);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        messageId,\r\n        reactions: message.reactions\r\n      },\r\n      message: 'Reaction removed successfully'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error removing reaction:', error);\r\n    throw new AppError('Failed to remove reaction', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Mark messages as read\r\n */\r\nexport const markMessagesAsRead = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { conversationId } = req.params;\r\n  const { messageIds } = req.body; // Optional: specific message IDs to mark as read\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  try {\r\n    // Verify conversation access\r\n    const conversation = await Conversation.findOne({\r\n      _id: conversationId,\r\n      participants: userId,\r\n      status: { $ne: 'deleted' }\r\n    });\r\n\r\n    if (!conversation) {\r\n      throw new AppError('Conversation not found', 404);\r\n    }\r\n\r\n    let query: any = {\r\n      conversationId,\r\n      receiverId: userId,\r\n      status: { $in: ['sent', 'delivered'] }\r\n    };\r\n\r\n    // If specific message IDs provided, only mark those\r\n    if (messageIds && Array.isArray(messageIds) && messageIds.length > 0) {\r\n      query._id = { $in: messageIds };\r\n    }\r\n\r\n    // Update messages to read status\r\n    const result = await Message.updateMany(query, {\r\n      status: 'read',\r\n      readAt: new Date()\r\n    });\r\n\r\n    // Update conversation unread count\r\n    await conversation.markAsRead(new Types.ObjectId(userId));\r\n\r\n    logger.info(`Marked ${result.modifiedCount} messages as read for user ${userId} in conversation ${conversationId}`);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        markedCount: result.modifiedCount,\r\n        conversationId\r\n      },\r\n      message: 'Messages marked as read'\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error marking messages as read:', error);\r\n    throw new AppError('Failed to mark messages as read', 500);\r\n  }\r\n});\r\n\r\n/**\r\n * Search messages across conversations\r\n */\r\nexport const searchMessages = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { \r\n    query: searchQuery, \r\n    conversationId,\r\n    messageType,\r\n    page = 1,\r\n    limit = 20,\r\n    dateFrom,\r\n    dateTo\r\n  } = req.query;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!searchQuery || (searchQuery as string).trim().length < 2) {\r\n    throw new AppError('Search query must be at least 2 characters', 400);\r\n  }\r\n\r\n  try {\r\n    // Get user's conversations\r\n    const userConversations = await Conversation.find({\r\n      participants: userId,\r\n      status: { $ne: 'deleted' }\r\n    }).select('_id');\r\n\r\n    const conversationIds = userConversations.map(c => c._id);\r\n\r\n    // Build search query\r\n    const searchFilter: any = {\r\n      conversationId: conversationId ? conversationId : { $in: conversationIds },\r\n      content: { $regex: searchQuery, $options: 'i' },\r\n      isDeleted: false\r\n    };\r\n\r\n    if (messageType && messageType !== 'all') {\r\n      searchFilter.messageType = messageType;\r\n    }\r\n\r\n    if (dateFrom || dateTo) {\r\n      searchFilter.createdAt = {};\r\n      if (dateFrom) searchFilter.createdAt.$gte = new Date(dateFrom as string);\r\n      if (dateTo) searchFilter.createdAt.$lte = new Date(dateTo as string);\r\n    }\r\n\r\n    // Pagination\r\n    const pageNum = parseInt(page as string);\r\n    const limitNum = parseInt(limit as string);\r\n    const skip = (pageNum - 1) * limitNum;\r\n\r\n    const [messages, totalCount] = await Promise.all([\r\n      Message.find(searchFilter)\r\n        .populate('senderId', 'firstName lastName avatar')\r\n        .populate('conversationId', 'conversationType title participants')\r\n        .sort({ createdAt: -1 })\r\n        .skip(skip)\r\n        .limit(limitNum)\r\n        .lean(),\r\n      Message.countDocuments(searchFilter)\r\n    ]);\r\n\r\n    return res.json({\r\n      success: true,\r\n      data: {\r\n        messages,\r\n        pagination: {\r\n          page: pageNum,\r\n          limit: limitNum,\r\n          total: totalCount,\r\n          pages: Math.ceil(totalCount / limitNum)\r\n        },\r\n        searchQuery\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    logger.error('Error searching messages:', error);\r\n    throw new AppError('Failed to search messages', 500);\r\n  }\r\n});\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "773367da18908f03398884ea5a564a0a9e1bf244"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1bqll5vvre = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1bqll5vvre();
cov_1bqll5vvre().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1bqll5vvre().s[1]++;
exports.searchMessages = exports.markMessagesAsRead = exports.removeReaction = exports.reactToMessage = exports.deleteMessage = exports.editMessage = exports.sendMessage = exports.getConversationMessages = void 0;
const mongoose_1 =
/* istanbul ignore next */
(cov_1bqll5vvre().s[2]++, require("mongoose"));
const Conversation_1 =
/* istanbul ignore next */
(cov_1bqll5vvre().s[3]++, require("../models/Conversation"));
const logger_1 =
/* istanbul ignore next */
(cov_1bqll5vvre().s[4]++, require("../utils/logger"));
const appError_1 =
/* istanbul ignore next */
(cov_1bqll5vvre().s[5]++, require("../utils/appError"));
const catchAsync_1 =
/* istanbul ignore next */
(cov_1bqll5vvre().s[6]++, require("../utils/catchAsync"));
/**
 * Get messages for a conversation with pagination
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[7]++;
exports.getConversationMessages = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[0]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[8]++, req.user?._id);
  const {
    conversationId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[9]++, req.params);
  const {
    page =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[0][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[1][0]++, 50),
    before,
    // Message ID to get messages before (for pagination)
    after,
    // Message ID to get messages after
    messageType,
    search
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[10]++, req.query);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[11]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[2][0]++;
    cov_1bqll5vvre().s[12]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[2][1]++;
  }
  cov_1bqll5vvre().s[13]++;
  try {
    // Verify user is participant in conversation
    const conversation =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[14]++, await Conversation_1.Conversation.findOne({
      _id: conversationId,
      participants: userId,
      status: {
        $ne: 'deleted'
      }
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[15]++;
    if (!conversation) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[3][0]++;
      cov_1bqll5vvre().s[16]++;
      throw new appError_1.AppError('Conversation not found or access denied', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[3][1]++;
    }
    // Build query
    const query =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[17]++, {
      conversationId,
      isDeleted: false
    });
    // Add message type filter
    /* istanbul ignore next */
    cov_1bqll5vvre().s[18]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[5][0]++, messageType) &&
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[5][1]++, messageType !== 'all')) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[4][0]++;
      cov_1bqll5vvre().s[19]++;
      query.messageType = messageType;
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[4][1]++;
    }
    // Add search filter
    cov_1bqll5vvre().s[20]++;
    if (search) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[6][0]++;
      cov_1bqll5vvre().s[21]++;
      query.content = {
        $regex: search,
        $options: 'i'
      };
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[6][1]++;
    }
    // Handle cursor-based pagination
    cov_1bqll5vvre().s[22]++;
    if (before) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[7][0]++;
      const beforeMessage =
      /* istanbul ignore next */
      (cov_1bqll5vvre().s[23]++, await Conversation_1.Message.findById(before));
      /* istanbul ignore next */
      cov_1bqll5vvre().s[24]++;
      if (beforeMessage) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[8][0]++;
        cov_1bqll5vvre().s[25]++;
        query.createdAt = {
          $lt: beforeMessage.createdAt
        };
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[8][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[7][1]++;
    }
    cov_1bqll5vvre().s[26]++;
    if (after) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[9][0]++;
      const afterMessage =
      /* istanbul ignore next */
      (cov_1bqll5vvre().s[27]++, await Conversation_1.Message.findById(after));
      /* istanbul ignore next */
      cov_1bqll5vvre().s[28]++;
      if (afterMessage) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[10][0]++;
        cov_1bqll5vvre().s[29]++;
        query.createdAt = {
          $gt: afterMessage.createdAt
        };
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[10][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[9][1]++;
    }
    // Pagination
    const pageNum =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[30]++, parseInt(page));
    const limitNum =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[31]++, Math.min(parseInt(limit), 100)); // Max 100 messages per request
    const skip =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[32]++, (pageNum - 1) * limitNum);
    const [messages, totalCount] =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[33]++, await Promise.all([Conversation_1.Message.find(query).populate('senderId', 'firstName lastName avatar').populate('receiverId', 'firstName lastName avatar').populate('replyTo', 'content senderId messageType').populate('metadata.propertyId', 'title propertyType location pricing photos').sort({
      createdAt: -1
    }) // Most recent first
    .skip(
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[12][0]++, before) ||
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[12][1]++, after) ?
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[11][0]++, 0) :
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[11][1]++, skip)) // Skip only for regular pagination
    .limit(limitNum).lean(), Conversation_1.Message.countDocuments(query)]));
    // Mark messages as delivered for the requesting user
    const undeliveredMessages =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[34]++, messages.filter(msg => {
      /* istanbul ignore next */
      cov_1bqll5vvre().f[1]++;
      cov_1bqll5vvre().s[35]++;
      return /* istanbul ignore next */(cov_1bqll5vvre().b[13][0]++, msg.receiverId.toString() === userId.toString()) &&
      /* istanbul ignore next */
      (cov_1bqll5vvre().b[13][1]++, msg.status === 'sent');
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[36]++;
    if (undeliveredMessages.length > 0) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[14][0]++;
      cov_1bqll5vvre().s[37]++;
      await Conversation_1.Message.updateMany({
        _id: {
          $in: undeliveredMessages.map(msg => {
            /* istanbul ignore next */
            cov_1bqll5vvre().f[2]++;
            cov_1bqll5vvre().s[38]++;
            return msg._id;
          })
        },
        status: 'sent'
      }, {
        status: 'delivered',
        deliveredAt: new Date()
      });
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[14][1]++;
    }
    // Format messages for response
    const formattedMessages =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[39]++, messages.map(message => {
      /* istanbul ignore next */
      cov_1bqll5vvre().f[3]++;
      cov_1bqll5vvre().s[40]++;
      return {
        ...message,
        isSentByUser: message.senderId._id.toString() === userId.toString(),
        canEdit:
        /* istanbul ignore next */
        (cov_1bqll5vvre().b[15][0]++, message.senderId._id.toString() === userId.toString()) &&
        /* istanbul ignore next */
        (cov_1bqll5vvre().b[15][1]++, message.messageType === 'text') &&
        /* istanbul ignore next */
        (cov_1bqll5vvre().b[15][2]++, !message.isDeleted),
        canDelete:
        /* istanbul ignore next */
        (cov_1bqll5vvre().b[16][0]++, message.senderId._id.toString() === userId.toString()) &&
        /* istanbul ignore next */
        (cov_1bqll5vvre().b[16][1]++, !message.isDeleted)
      };
    }));
    // Get pagination info
    const hasMore =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[41]++, totalCount > pageNum * limitNum);
    const hasPrevious =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[42]++, pageNum > 1);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[43]++;
    return res.json({
      success: true,
      data: {
        messages: formattedMessages,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalCount,
          pages: Math.ceil(totalCount / limitNum),
          hasMore,
          hasPrevious,
          // Cursor info for infinite scroll
          cursors: {
            before: messages.length > 0 ?
            /* istanbul ignore next */
            (cov_1bqll5vvre().b[17][0]++, messages[0]._id) :
            /* istanbul ignore next */
            (cov_1bqll5vvre().b[17][1]++, null),
            after: messages.length > 0 ?
            /* istanbul ignore next */
            (cov_1bqll5vvre().b[18][0]++, messages[messages.length - 1]._id) :
            /* istanbul ignore next */
            (cov_1bqll5vvre().b[18][1]++, null)
          }
        },
        conversationInfo: {
          id: conversation._id,
          type: conversation.conversationType,
          participantCount: conversation.participants.length,
          unreadCount: conversation.getUnreadCount(new mongoose_1.Types.ObjectId(userId))
        }
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[44]++;
    logger_1.logger.error('Error getting conversation messages:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[45]++;
    throw new appError_1.AppError('Failed to get messages', 500);
  }
});
/**
 * Send a message
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[46]++;
exports.sendMessage = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[4]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[47]++, req.user?._id);
  const {
    conversationId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[48]++, req.params);
  const {
    content,
    messageType =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[19][0]++, 'text'),
    metadata,
    replyTo
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[49]++, req.body);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[50]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[20][0]++;
    cov_1bqll5vvre().s[51]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[20][1]++;
  }
  cov_1bqll5vvre().s[52]++;
  if (
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[22][0]++, !content) ||
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[22][1]++, content.trim().length === 0)) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[21][0]++;
    cov_1bqll5vvre().s[53]++;
    throw new appError_1.AppError('Message content is required', 400);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[21][1]++;
  }
  cov_1bqll5vvre().s[54]++;
  try {
    // Verify conversation and user permissions
    const conversation =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[55]++, await Conversation_1.Conversation.findById(conversationId));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[56]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[24][0]++, !conversation) ||
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[24][1]++, !conversation.canUserSendMessage(new mongoose_1.Types.ObjectId(userId)))) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[23][0]++;
      cov_1bqll5vvre().s[57]++;
      throw new appError_1.AppError('Cannot send message to this conversation', 403);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[23][1]++;
    }
    // Get receiver ID (for direct conversations)
    const receiverId =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[58]++, conversation.participants.find(p => {
      /* istanbul ignore next */
      cov_1bqll5vvre().f[5]++;
      cov_1bqll5vvre().s[59]++;
      return p.toString() !== userId.toString();
    }));
    // Validate reply-to message if provided
    /* istanbul ignore next */
    cov_1bqll5vvre().s[60]++;
    if (replyTo) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[25][0]++;
      const replyToMessage =
      /* istanbul ignore next */
      (cov_1bqll5vvre().s[61]++, await Conversation_1.Message.findOne({
        _id: replyTo,
        conversationId,
        isDeleted: false
      }));
      /* istanbul ignore next */
      cov_1bqll5vvre().s[62]++;
      if (!replyToMessage) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[26][0]++;
        cov_1bqll5vvre().s[63]++;
        throw new appError_1.AppError('Reply-to message not found', 404);
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[26][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[25][1]++;
    }
    // Create message
    const message =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[64]++, new Conversation_1.Message({
      conversationId,
      senderId: userId,
      receiverId,
      messageType,
      content: content.trim(),
      metadata,
      replyTo: replyTo ?
      /* istanbul ignore next */
      (cov_1bqll5vvre().b[27][0]++, new mongoose_1.Types.ObjectId(replyTo)) :
      /* istanbul ignore next */
      (cov_1bqll5vvre().b[27][1]++, undefined),
      status: 'sent'
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[65]++;
    await message.save();
    // Update conversation
    /* istanbul ignore next */
    cov_1bqll5vvre().s[66]++;
    await conversation.updateLastMessage(message);
    // Populate message for response
    const populatedMessage =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[67]++, await Conversation_1.Message.findById(message._id).populate('senderId', 'firstName lastName avatar').populate('receiverId', 'firstName lastName avatar').populate('replyTo', 'content senderId messageType').populate('metadata.propertyId', 'title propertyType location pricing photos'));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[68]++;
    logger_1.logger.info(`Message sent from ${userId} to conversation ${conversationId}`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[69]++;
    return res.status(201).json({
      success: true,
      data: {
        message: populatedMessage
      },
      message: 'Message sent successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[70]++;
    logger_1.logger.error('Error sending message:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[71]++;
    throw new appError_1.AppError('Failed to send message', 500);
  }
});
/**
 * Edit a message
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[72]++;
exports.editMessage = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[6]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[73]++, req.user?._id);
  const {
    messageId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[74]++, req.params);
  const {
    content
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[75]++, req.body);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[76]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[28][0]++;
    cov_1bqll5vvre().s[77]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[28][1]++;
  }
  cov_1bqll5vvre().s[78]++;
  if (
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[30][0]++, !content) ||
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[30][1]++, content.trim().length === 0)) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[29][0]++;
    cov_1bqll5vvre().s[79]++;
    throw new appError_1.AppError('Message content is required', 400);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[29][1]++;
  }
  cov_1bqll5vvre().s[80]++;
  try {
    const message =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[81]++, await Conversation_1.Message.findOne({
      _id: messageId,
      senderId: userId,
      messageType: 'text',
      isDeleted: false
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[82]++;
    if (!message) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[31][0]++;
      cov_1bqll5vvre().s[83]++;
      throw new appError_1.AppError('Message not found or cannot be edited', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[31][1]++;
    }
    // Check if message is too old to edit (24 hours)
    const messageAge =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[84]++, Date.now() - message.createdAt.getTime());
    const maxEditAge =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[85]++, 24 * 60 * 60 * 1000); // 24 hours
    /* istanbul ignore next */
    cov_1bqll5vvre().s[86]++;
    if (messageAge > maxEditAge) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[32][0]++;
      cov_1bqll5vvre().s[87]++;
      throw new appError_1.AppError('Message is too old to edit', 400);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[32][1]++;
    }
    // Store original content if not already edited
    cov_1bqll5vvre().s[88]++;
    if (!message.isEdited) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[33][0]++;
      cov_1bqll5vvre().s[89]++;
      message.originalContent = message.content;
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[33][1]++;
    }
    // Update message
    cov_1bqll5vvre().s[90]++;
    message.content = content.trim();
    /* istanbul ignore next */
    cov_1bqll5vvre().s[91]++;
    message.isEdited = true;
    /* istanbul ignore next */
    cov_1bqll5vvre().s[92]++;
    message.editedAt = new Date();
    /* istanbul ignore next */
    cov_1bqll5vvre().s[93]++;
    await message.save();
    // Populate message for response
    const populatedMessage =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[94]++, await Conversation_1.Message.findById(message._id).populate('senderId', 'firstName lastName avatar').populate('receiverId', 'firstName lastName avatar'));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[95]++;
    logger_1.logger.info(`Message ${messageId} edited by user ${userId}`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[96]++;
    return res.json({
      success: true,
      data: {
        message: populatedMessage
      },
      message: 'Message edited successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[97]++;
    logger_1.logger.error('Error editing message:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[98]++;
    throw new appError_1.AppError('Failed to edit message', 500);
  }
});
/**
 * Delete a message
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[99]++;
exports.deleteMessage = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[7]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[100]++, req.user?._id);
  const {
    messageId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[101]++, req.params);
  const {
    deleteForEveryone =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[34][0]++, false)
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[102]++, req.body);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[103]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[35][0]++;
    cov_1bqll5vvre().s[104]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[35][1]++;
  }
  cov_1bqll5vvre().s[105]++;
  try {
    const message =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[106]++, await Conversation_1.Message.findOne({
      _id: messageId,
      senderId: userId,
      isDeleted: false
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[107]++;
    if (!message) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[36][0]++;
      cov_1bqll5vvre().s[108]++;
      throw new appError_1.AppError('Message not found or already deleted', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[36][1]++;
    }
    // Check if user can delete for everyone (within 1 hour)
    const messageAge =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[109]++, Date.now() - message.createdAt.getTime());
    const maxDeleteForEveryoneAge =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[110]++, 60 * 60 * 1000); // 1 hour
    /* istanbul ignore next */
    cov_1bqll5vvre().s[111]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[38][0]++, deleteForEveryone) &&
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[38][1]++, messageAge > maxDeleteForEveryoneAge)) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[37][0]++;
      cov_1bqll5vvre().s[112]++;
      throw new appError_1.AppError('Message is too old to delete for everyone', 400);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[37][1]++;
    }
    // Soft delete message
    cov_1bqll5vvre().s[113]++;
    message.isDeleted = true;
    /* istanbul ignore next */
    cov_1bqll5vvre().s[114]++;
    message.deletedAt = new Date();
    /* istanbul ignore next */
    cov_1bqll5vvre().s[115]++;
    message.deletedBy = new mongoose_1.Types.ObjectId(userId);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[116]++;
    if (deleteForEveryone) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[39][0]++;
      cov_1bqll5vvre().s[117]++;
      message.content = 'This message was deleted';
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[39][1]++;
    }
    cov_1bqll5vvre().s[118]++;
    await message.save();
    /* istanbul ignore next */
    cov_1bqll5vvre().s[119]++;
    logger_1.logger.info(`Message ${messageId} deleted by user ${userId} (deleteForEveryone: ${deleteForEveryone})`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[120]++;
    return res.json({
      success: true,
      data: {
        messageId,
        deletedForEveryone: deleteForEveryone
      },
      message: 'Message deleted successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[121]++;
    logger_1.logger.error('Error deleting message:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[122]++;
    throw new appError_1.AppError('Failed to delete message', 500);
  }
});
/**
 * React to a message
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[123]++;
exports.reactToMessage = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[8]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[124]++, req.user?._id);
  const {
    messageId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[125]++, req.params);
  const {
    reaction
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[126]++, req.body);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[127]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[40][0]++;
    cov_1bqll5vvre().s[128]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[40][1]++;
  }
  const validReactions =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[129]++, ['like', 'love', 'laugh', 'wow', 'sad', 'angry']);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[130]++;
  if (
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[42][0]++, !reaction) ||
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[42][1]++, !validReactions.includes(reaction))) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[41][0]++;
    cov_1bqll5vvre().s[131]++;
    throw new appError_1.AppError('Invalid reaction type', 400);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[41][1]++;
  }
  cov_1bqll5vvre().s[132]++;
  try {
    const message =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[133]++, await Conversation_1.Message.findOne({
      _id: messageId,
      isDeleted: false
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[134]++;
    if (!message) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[43][0]++;
      cov_1bqll5vvre().s[135]++;
      throw new appError_1.AppError('Message not found', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[43][1]++;
    }
    // Verify user is participant in the conversation
    const conversation =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[136]++, await Conversation_1.Conversation.findOne({
      _id: message.conversationId,
      participants: userId
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[137]++;
    if (!conversation) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[44][0]++;
      cov_1bqll5vvre().s[138]++;
      throw new appError_1.AppError('Access denied', 403);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[44][1]++;
    }
    // Check if user already reacted
    const existingReactionIndex =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[139]++,
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[45][0]++, message.reactions?.findIndex(r => {
      /* istanbul ignore next */
      cov_1bqll5vvre().f[9]++;
      cov_1bqll5vvre().s[140]++;
      return r.userId.toString() === userId.toString();
    })) ??
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[45][1]++, -1));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[141]++;
    if (existingReactionIndex > -1) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[46][0]++;
      cov_1bqll5vvre().s[142]++;
      // Update existing reaction
      message.reactions[existingReactionIndex].reaction = reaction;
      /* istanbul ignore next */
      cov_1bqll5vvre().s[143]++;
      message.reactions[existingReactionIndex].createdAt = new Date();
    } else {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[46][1]++;
      cov_1bqll5vvre().s[144]++;
      // Add new reaction
      if (!message.reactions) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[47][0]++;
        cov_1bqll5vvre().s[145]++;
        message.reactions = [];
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[47][1]++;
      }
      cov_1bqll5vvre().s[146]++;
      message.reactions.push({
        userId: new mongoose_1.Types.ObjectId(userId),
        reaction,
        createdAt: new Date()
      });
    }
    /* istanbul ignore next */
    cov_1bqll5vvre().s[147]++;
    await message.save();
    /* istanbul ignore next */
    cov_1bqll5vvre().s[148]++;
    logger_1.logger.info(`User ${userId} reacted to message ${messageId} with ${reaction}`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[149]++;
    return res.json({
      success: true,
      data: {
        messageId,
        reaction,
        reactions: message.reactions
      },
      message: 'Reaction added successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[150]++;
    logger_1.logger.error('Error reacting to message:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[151]++;
    throw new appError_1.AppError('Failed to react to message', 500);
  }
});
/**
 * Remove reaction from message
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[152]++;
exports.removeReaction = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[10]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[153]++, req.user?._id);
  const {
    messageId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[154]++, req.params);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[155]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[48][0]++;
    cov_1bqll5vvre().s[156]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[48][1]++;
  }
  cov_1bqll5vvre().s[157]++;
  try {
    const message =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[158]++, await Conversation_1.Message.findOne({
      _id: messageId,
      isDeleted: false
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[159]++;
    if (!message) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[49][0]++;
      cov_1bqll5vvre().s[160]++;
      throw new appError_1.AppError('Message not found', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[49][1]++;
    }
    // Remove user's reaction
    cov_1bqll5vvre().s[161]++;
    if (message.reactions) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[50][0]++;
      cov_1bqll5vvre().s[162]++;
      message.reactions = message.reactions.filter(r => {
        /* istanbul ignore next */
        cov_1bqll5vvre().f[11]++;
        cov_1bqll5vvre().s[163]++;
        return r.userId.toString() !== userId.toString();
      });
      /* istanbul ignore next */
      cov_1bqll5vvre().s[164]++;
      await message.save();
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[50][1]++;
    }
    cov_1bqll5vvre().s[165]++;
    logger_1.logger.info(`User ${userId} removed reaction from message ${messageId}`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[166]++;
    return res.json({
      success: true,
      data: {
        messageId,
        reactions: message.reactions
      },
      message: 'Reaction removed successfully'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[167]++;
    logger_1.logger.error('Error removing reaction:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[168]++;
    throw new appError_1.AppError('Failed to remove reaction', 500);
  }
});
/**
 * Mark messages as read
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[169]++;
exports.markMessagesAsRead = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[12]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[170]++, req.user?._id);
  const {
    conversationId
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[171]++, req.params);
  const {
    messageIds
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[172]++, req.body); // Optional: specific message IDs to mark as read
  /* istanbul ignore next */
  cov_1bqll5vvre().s[173]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[51][0]++;
    cov_1bqll5vvre().s[174]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[51][1]++;
  }
  cov_1bqll5vvre().s[175]++;
  try {
    // Verify conversation access
    const conversation =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[176]++, await Conversation_1.Conversation.findOne({
      _id: conversationId,
      participants: userId,
      status: {
        $ne: 'deleted'
      }
    }));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[177]++;
    if (!conversation) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[52][0]++;
      cov_1bqll5vvre().s[178]++;
      throw new appError_1.AppError('Conversation not found', 404);
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[52][1]++;
    }
    let query =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[179]++, {
      conversationId,
      receiverId: userId,
      status: {
        $in: ['sent', 'delivered']
      }
    });
    // If specific message IDs provided, only mark those
    /* istanbul ignore next */
    cov_1bqll5vvre().s[180]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[54][0]++, messageIds) &&
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[54][1]++, Array.isArray(messageIds)) &&
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[54][2]++, messageIds.length > 0)) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[53][0]++;
      cov_1bqll5vvre().s[181]++;
      query._id = {
        $in: messageIds
      };
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[53][1]++;
    }
    // Update messages to read status
    const result =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[182]++, await Conversation_1.Message.updateMany(query, {
      status: 'read',
      readAt: new Date()
    }));
    // Update conversation unread count
    /* istanbul ignore next */
    cov_1bqll5vvre().s[183]++;
    await conversation.markAsRead(new mongoose_1.Types.ObjectId(userId));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[184]++;
    logger_1.logger.info(`Marked ${result.modifiedCount} messages as read for user ${userId} in conversation ${conversationId}`);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[185]++;
    return res.json({
      success: true,
      data: {
        markedCount: result.modifiedCount,
        conversationId
      },
      message: 'Messages marked as read'
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[186]++;
    logger_1.logger.error('Error marking messages as read:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[187]++;
    throw new appError_1.AppError('Failed to mark messages as read', 500);
  }
});
/**
 * Search messages across conversations
 */
/* istanbul ignore next */
cov_1bqll5vvre().s[188]++;
exports.searchMessages = (0, catchAsync_1.catchAsync)(async (req, res) => {
  /* istanbul ignore next */
  cov_1bqll5vvre().f[13]++;
  const userId =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[189]++, req.user?._id);
  const {
    query: searchQuery,
    conversationId,
    messageType,
    page =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[55][0]++, 1),
    limit =
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[56][0]++, 20),
    dateFrom,
    dateTo
  } =
  /* istanbul ignore next */
  (cov_1bqll5vvre().s[190]++, req.query);
  /* istanbul ignore next */
  cov_1bqll5vvre().s[191]++;
  if (!userId) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[57][0]++;
    cov_1bqll5vvre().s[192]++;
    throw new appError_1.AppError('User not authenticated', 401);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[57][1]++;
  }
  cov_1bqll5vvre().s[193]++;
  if (
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[59][0]++, !searchQuery) ||
  /* istanbul ignore next */
  (cov_1bqll5vvre().b[59][1]++, searchQuery.trim().length < 2)) {
    /* istanbul ignore next */
    cov_1bqll5vvre().b[58][0]++;
    cov_1bqll5vvre().s[194]++;
    throw new appError_1.AppError('Search query must be at least 2 characters', 400);
  } else
  /* istanbul ignore next */
  {
    cov_1bqll5vvre().b[58][1]++;
  }
  cov_1bqll5vvre().s[195]++;
  try {
    // Get user's conversations
    const userConversations =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[196]++, await Conversation_1.Conversation.find({
      participants: userId,
      status: {
        $ne: 'deleted'
      }
    }).select('_id'));
    const conversationIds =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[197]++, userConversations.map(c => {
      /* istanbul ignore next */
      cov_1bqll5vvre().f[14]++;
      cov_1bqll5vvre().s[198]++;
      return c._id;
    }));
    // Build search query
    const searchFilter =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[199]++, {
      conversationId: conversationId ?
      /* istanbul ignore next */
      (cov_1bqll5vvre().b[60][0]++, conversationId) :
      /* istanbul ignore next */
      (cov_1bqll5vvre().b[60][1]++, {
        $in: conversationIds
      }),
      content: {
        $regex: searchQuery,
        $options: 'i'
      },
      isDeleted: false
    });
    /* istanbul ignore next */
    cov_1bqll5vvre().s[200]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[62][0]++, messageType) &&
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[62][1]++, messageType !== 'all')) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[61][0]++;
      cov_1bqll5vvre().s[201]++;
      searchFilter.messageType = messageType;
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[61][1]++;
    }
    cov_1bqll5vvre().s[202]++;
    if (
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[64][0]++, dateFrom) ||
    /* istanbul ignore next */
    (cov_1bqll5vvre().b[64][1]++, dateTo)) {
      /* istanbul ignore next */
      cov_1bqll5vvre().b[63][0]++;
      cov_1bqll5vvre().s[203]++;
      searchFilter.createdAt = {};
      /* istanbul ignore next */
      cov_1bqll5vvre().s[204]++;
      if (dateFrom) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[65][0]++;
        cov_1bqll5vvre().s[205]++;
        searchFilter.createdAt.$gte = new Date(dateFrom);
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[65][1]++;
      }
      cov_1bqll5vvre().s[206]++;
      if (dateTo) {
        /* istanbul ignore next */
        cov_1bqll5vvre().b[66][0]++;
        cov_1bqll5vvre().s[207]++;
        searchFilter.createdAt.$lte = new Date(dateTo);
      } else
      /* istanbul ignore next */
      {
        cov_1bqll5vvre().b[66][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1bqll5vvre().b[63][1]++;
    }
    // Pagination
    const pageNum =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[208]++, parseInt(page));
    const limitNum =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[209]++, parseInt(limit));
    const skip =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[210]++, (pageNum - 1) * limitNum);
    const [messages, totalCount] =
    /* istanbul ignore next */
    (cov_1bqll5vvre().s[211]++, await Promise.all([Conversation_1.Message.find(searchFilter).populate('senderId', 'firstName lastName avatar').populate('conversationId', 'conversationType title participants').sort({
      createdAt: -1
    }).skip(skip).limit(limitNum).lean(), Conversation_1.Message.countDocuments(searchFilter)]));
    /* istanbul ignore next */
    cov_1bqll5vvre().s[212]++;
    return res.json({
      success: true,
      data: {
        messages,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalCount,
          pages: Math.ceil(totalCount / limitNum)
        },
        searchQuery
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1bqll5vvre().s[213]++;
    logger_1.logger.error('Error searching messages:', error);
    /* istanbul ignore next */
    cov_1bqll5vvre().s[214]++;
    throw new appError_1.AppError('Failed to search messages', 500);
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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