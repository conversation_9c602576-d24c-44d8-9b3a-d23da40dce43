# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 🔒 SECURITY - Environment Variables (CRITICAL)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
config.json
secrets.json
credentials.json

# 🔒 SECURITY - API Keys and Tokens
*.key
*.pem
*.p12
*.pfx
jwt-secret.txt
api-keys.txt

# 🔒 SECURITY - Database Credentials
database.json
db-config.json

# 🔒 SECURITY - Test Files with Sensitive Data
test-*.js
*-test.js
test-*-comprehensive.js
test-*-final.js

# Build outputs
dist/
build/
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test output
coverage/
.nyc_output/

# Uploads (temporary files)
uploads/

# Database files (if using local database)
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# PM2 logs
.pm2/

# Backup files
*.backup
*.bak

# Temporary files
*.tmp
*.temp
