db73cd6e9e13f9cfccebc2cb0611a8ef
"use strict";

/* istanbul ignore next */
function cov_1io6lkpmz1() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts";
  var hash = "ccbfe9d801e3d5f4bc1ee9b04615f12de3e0a027";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 26
        },
        end: {
          line: 5,
          column: 66
        }
      },
      "4": {
        start: {
          line: 6,
          column: 23
        },
        end: {
          line: 6,
          column: 58
        }
      },
      "5": {
        start: {
          line: 7,
          column: 23
        },
        end: {
          line: 7,
          column: 58
        }
      },
      "6": {
        start: {
          line: 8,
          column: 25
        },
        end: {
          line: 8,
          column: 62
        }
      },
      "7": {
        start: {
          line: 9,
          column: 23
        },
        end: {
          line: 9,
          column: 58
        }
      },
      "8": {
        start: {
          line: 10,
          column: 28
        },
        end: {
          line: 10,
          column: 65
        }
      },
      "9": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 60
        }
      },
      "10": {
        start: {
          line: 12,
          column: 23
        },
        end: {
          line: 12,
          column: 60
        }
      },
      "11": {
        start: {
          line: 13,
          column: 23
        },
        end: {
          line: 13,
          column: 60
        }
      },
      "12": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 14,
          column: 38
        }
      },
      "13": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 42
        }
      },
      "14": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 32
        }
      },
      "15": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 46
        }
      },
      "16": {
        start: {
          line: 19,
          column: 0
        },
        end: {
          line: 19,
          column: 53
        }
      },
      "17": {
        start: {
          line: 77,
          column: 0
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "18": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "19": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 87,
          column: 9
        }
      },
      "20": {
        start: {
          line: 88,
          column: 23
        },
        end: {
          line: 88,
          column: 78
        }
      },
      "21": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 93,
          column: 11
        }
      },
      "22": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 97,
          column: 11
        }
      },
      "23": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 103,
          column: 11
        }
      },
      "24": {
        start: {
          line: 126,
          column: 0
        },
        end: {
          line: 141,
          column: 3
        }
      },
      "25": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 140,
          column: 5
        }
      },
      "26": {
        start: {
          line: 128,
          column: 26
        },
        end: {
          line: 128,
          column: 54
        }
      },
      "27": {
        start: {
          line: 129,
          column: 22
        },
        end: {
          line: 129,
          column: 80
        }
      },
      "28": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 133,
          column: 11
        }
      },
      "29": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 139,
          column: 11
        }
      },
      "30": {
        start: {
          line: 154,
          column: 0
        },
        end: {
          line: 171,
          column: 3
        }
      },
      "31": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 170,
          column: 5
        }
      },
      "32": {
        start: {
          line: 156,
          column: 22
        },
        end: {
          line: 156,
          column: 66
        }
      },
      "33": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 163,
          column: 11
        }
      },
      "34": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 169,
          column: 11
        }
      },
      "35": {
        start: {
          line: 184,
          column: 0
        },
        end: {
          line: 198,
          column: 3
        }
      },
      "36": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "37": {
        start: {
          line: 186,
          column: 22
        },
        end: {
          line: 186,
          column: 72
        }
      },
      "38": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 190,
          column: 11
        }
      },
      "39": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 196,
          column: 11
        }
      },
      "40": {
        start: {
          line: 219,
          column: 0
        },
        end: {
          line: 234,
          column: 3
        }
      },
      "41": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 233,
          column: 5
        }
      },
      "42": {
        start: {
          line: 221,
          column: 26
        },
        end: {
          line: 221,
          column: 55
        }
      },
      "43": {
        start: {
          line: 222,
          column: 22
        },
        end: {
          line: 222,
          column: 76
        }
      },
      "44": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 226,
          column: 11
        }
      },
      "45": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 232,
          column: 11
        }
      },
      "46": {
        start: {
          line: 247,
          column: 0
        },
        end: {
          line: 264,
          column: 3
        }
      },
      "47": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 263,
          column: 5
        }
      },
      "48": {
        start: {
          line: 249,
          column: 22
        },
        end: {
          line: 249,
          column: 77
        }
      },
      "49": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 256,
          column: 11
        }
      },
      "50": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 262,
          column: 11
        }
      },
      "51": {
        start: {
          line: 277,
          column: 0
        },
        end: {
          line: 294,
          column: 3
        }
      },
      "52": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 293,
          column: 5
        }
      },
      "53": {
        start: {
          line: 279,
          column: 22
        },
        end: {
          line: 279,
          column: 71
        }
      },
      "54": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 286,
          column: 11
        }
      },
      "55": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 292,
          column: 11
        }
      },
      "56": {
        start: {
          line: 320,
          column: 0
        },
        end: {
          line: 362,
          column: 3
        }
      },
      "57": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 361,
          column: 5
        }
      },
      "58": {
        start: {
          line: 322,
          column: 32
        },
        end: {
          line: 322,
          column: 40
        }
      },
      "59": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 346,
          column: 9
        }
      },
      "60": {
        start: {
          line: 326,
          column: 16
        },
        end: {
          line: 326,
          column: 65
        }
      },
      "61": {
        start: {
          line: 327,
          column: 16
        },
        end: {
          line: 327,
          column: 54
        }
      },
      "62": {
        start: {
          line: 328,
          column: 16
        },
        end: {
          line: 328,
          column: 22
        }
      },
      "63": {
        start: {
          line: 330,
          column: 35
        },
        end: {
          line: 330,
          column: 91
        }
      },
      "64": {
        start: {
          line: 331,
          column: 16
        },
        end: {
          line: 331,
          column: 67
        }
      },
      "65": {
        start: {
          line: 332,
          column: 16
        },
        end: {
          line: 332,
          column: 22
        }
      },
      "66": {
        start: {
          line: 334,
          column: 37
        },
        end: {
          line: 334,
          column: 99
        }
      },
      "67": {
        start: {
          line: 335,
          column: 16
        },
        end: {
          line: 335,
          column: 71
        }
      },
      "68": {
        start: {
          line: 336,
          column: 16
        },
        end: {
          line: 336,
          column: 22
        }
      },
      "69": {
        start: {
          line: 338,
          column: 16
        },
        end: {
          line: 338,
          column: 66
        }
      },
      "70": {
        start: {
          line: 339,
          column: 16
        },
        end: {
          line: 339,
          column: 49
        }
      },
      "71": {
        start: {
          line: 340,
          column: 16
        },
        end: {
          line: 340,
          column: 22
        }
      },
      "72": {
        start: {
          line: 342,
          column: 16
        },
        end: {
          line: 345,
          column: 19
        }
      },
      "73": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 350,
          column: 11
        }
      },
      "74": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 354,
          column: 11
        }
      },
      "75": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 360,
          column: 11
        }
      },
      "76": {
        start: {
          line: 375,
          column: 0
        },
        end: {
          line: 411,
          column: 3
        }
      },
      "77": {
        start: {
          line: 376,
          column: 4
        },
        end: {
          line: 410,
          column: 5
        }
      },
      "78": {
        start: {
          line: 377,
          column: 23
        },
        end: {
          line: 399,
          column: 9
        }
      },
      "79": {
        start: {
          line: 400,
          column: 8
        },
        end: {
          line: 403,
          column: 11
        }
      },
      "80": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 409,
          column: 11
        }
      },
      "81": {
        start: {
          line: 412,
          column: 0
        },
        end: {
          line: 412,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 77,
            column: 26
          },
          end: {
            line: 77,
            column: 27
          }
        },
        loc: {
          start: {
            line: 77,
            column: 46
          },
          end: {
            line: 105,
            column: 1
          }
        },
        line: 77
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 126,
            column: 27
          },
          end: {
            line: 126,
            column: 28
          }
        },
        loc: {
          start: {
            line: 126,
            column: 47
          },
          end: {
            line: 141,
            column: 1
          }
        },
        line: 126
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 154,
            column: 33
          },
          end: {
            line: 154,
            column: 34
          }
        },
        loc: {
          start: {
            line: 154,
            column: 53
          },
          end: {
            line: 171,
            column: 1
          }
        },
        line: 154
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 184,
            column: 35
          },
          end: {
            line: 184,
            column: 36
          }
        },
        loc: {
          start: {
            line: 184,
            column: 55
          },
          end: {
            line: 198,
            column: 1
          }
        },
        line: 184
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 219,
            column: 36
          },
          end: {
            line: 219,
            column: 37
          }
        },
        loc: {
          start: {
            line: 219,
            column: 56
          },
          end: {
            line: 234,
            column: 1
          }
        },
        line: 219
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 247,
            column: 33
          },
          end: {
            line: 247,
            column: 34
          }
        },
        loc: {
          start: {
            line: 247,
            column: 53
          },
          end: {
            line: 264,
            column: 1
          }
        },
        line: 247
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 277,
            column: 31
          },
          end: {
            line: 277,
            column: 32
          }
        },
        loc: {
          start: {
            line: 277,
            column: 51
          },
          end: {
            line: 294,
            column: 1
          }
        },
        line: 277
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 320,
            column: 36
          },
          end: {
            line: 320,
            column: 37
          }
        },
        loc: {
          start: {
            line: 320,
            column: 56
          },
          end: {
            line: 362,
            column: 1
          }
        },
        line: 320
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 375,
            column: 29
          },
          end: {
            line: 375,
            column: 30
          }
        },
        loc: {
          start: {
            line: 375,
            column: 49
          },
          end: {
            line: 411,
            column: 1
          }
        },
        line: 375
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 83,
            column: 23
          },
          end: {
            line: 83,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 83,
            column: 45
          },
          end: {
            line: 83,
            column: 74
          }
        }, {
          start: {
            line: 83,
            column: 77
          },
          end: {
            line: 83,
            column: 86
          }
        }],
        line: 83
      },
      "1": {
        loc: {
          start: {
            line: 84,
            column: 21
          },
          end: {
            line: 84,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 84,
            column: 41
          },
          end: {
            line: 84,
            column: 68
          }
        }, {
          start: {
            line: 84,
            column: 71
          },
          end: {
            line: 84,
            column: 80
          }
        }],
        line: 84
      },
      "2": {
        loc: {
          start: {
            line: 85,
            column: 18
          },
          end: {
            line: 85,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 18
          },
          end: {
            line: 85,
            column: 42
          }
        }, {
          start: {
            line: 85,
            column: 46
          },
          end: {
            line: 85,
            column: 47
          }
        }],
        line: 85
      },
      "3": {
        loc: {
          start: {
            line: 86,
            column: 19
          },
          end: {
            line: 86,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 19
          },
          end: {
            line: 86,
            column: 44
          }
        }, {
          start: {
            line: 86,
            column: 48
          },
          end: {
            line: 86,
            column: 50
          }
        }],
        line: 86
      },
      "4": {
        loc: {
          start: {
            line: 128,
            column: 26
          },
          end: {
            line: 128,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 26
          },
          end: {
            line: 128,
            column: 45
          }
        }, {
          start: {
            line: 128,
            column: 49
          },
          end: {
            line: 128,
            column: 54
          }
        }],
        line: 128
      },
      "5": {
        loc: {
          start: {
            line: 221,
            column: 26
          },
          end: {
            line: 221,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 26
          },
          end: {
            line: 221,
            column: 45
          }
        }, {
          start: {
            line: 221,
            column: 49
          },
          end: {
            line: 221,
            column: 55
          }
        }],
        line: 221
      },
      "6": {
        loc: {
          start: {
            line: 324,
            column: 8
          },
          end: {
            line: 346,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 325,
            column: 12
          },
          end: {
            line: 328,
            column: 22
          }
        }, {
          start: {
            line: 329,
            column: 12
          },
          end: {
            line: 332,
            column: 22
          }
        }, {
          start: {
            line: 333,
            column: 12
          },
          end: {
            line: 336,
            column: 22
          }
        }, {
          start: {
            line: 337,
            column: 12
          },
          end: {
            line: 340,
            column: 22
          }
        }, {
          start: {
            line: 341,
            column: 12
          },
          end: {
            line: 345,
            column: 19
          }
        }],
        line: 324
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0, 0, 0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,6CAAoE;AACpE,mEAAgE;AAChE,2DAAmF;AACnF,2DAAwD;AACxD,+DAA4D;AAC5D,2DAAwD;AACxD,kEAA4D;AAC5D,6DAAqF;AACrF,6DAA4D;AAC5D,6DAAgE;AAEhE,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,kCAAkC;AAClC,MAAM,CAAC,GAAG,CAAC,6BAAc,CAAC,CAAC;AAC3B,MAAM,CAAC,GAAG,CAAC,mBAAc,CAAC,CAAC;AAC3B,MAAM,CAAC,GAAG,CAAC,iCAAe,CAAC,CAAC;AAC5B,MAAM,CAAC,GAAG,CAAC,IAAA,iCAAkB,GAAE,CAAC,CAAC;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDG;AACH,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3C,IAAI,CAAC;QACH,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAgB;YAClC,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,SAA2B;YAChD,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,SAAsB;YAC3C,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,SAAS;YACpF,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,SAAS;YAC9E,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAc,CAAC,IAAI,CAAC;YAC7C,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,EAAE;SACjD,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAExD,iCAAiC;QACjC,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,WAAW,EAAE,GAAG,EAAE;YAC3D,QAAQ,EAAE,YAAY;YACtB,QAAQ,EAAE,EAAE,OAAO,EAAE;SACtB,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,+BAA+B;SACvC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,SAAS,GAAI,GAAG,CAAC,KAAK,CAAC,SAA+C,IAAI,KAAK,CAAC;QACtF,MAAM,KAAK,GAAG,MAAM,2BAAY,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAE1D,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qCAAqC;SAC7C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,2BAAY,CAAC,QAAQ,EAAE,CAAC;QAE5C,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,KAAK;gBACR,WAAW,EAAE,2BAAY,CAAC,WAAW,EAAE;aACxC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qCAAqC;SAC7C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,kCAAc,CAAC,aAAa,EAAE,CAAC;QAE7C,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qCAAqC;SAC7C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACrD,IAAI,CAAC;QACH,MAAM,SAAS,GAAI,GAAG,CAAC,KAAK,CAAC,SAAqC,IAAI,MAAM,CAAC;QAC7E,MAAM,KAAK,GAAG,MAAM,IAAA,gCAAiB,EAAC,SAAS,CAAC,CAAC;QAEjD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,0CAA0C;SAClD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,+BAAc,CAAC,eAAe,EAAE,CAAC;QAErD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,KAAK;gBACR,WAAW,EAAE,+BAAc,CAAC,WAAW,EAAE;aAC1C;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uCAAuC;SAC/C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAChD,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,2BAAY,CAAC,aAAa,EAAE,CAAC;QAEjD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,GAAG,KAAK;gBACR,WAAW,EAAE,2BAAY,CAAC,WAAW,EAAE;aACxC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qCAAqC;SAC7C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACrD,IAAI,CAAC;QACH,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QACjC,IAAI,MAAM,CAAC;QAEX,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,aAAa;gBAChB,MAAM,IAAA,mCAAoB,GAAE,CAAC;gBAC7B,MAAM,GAAG,4BAA4B,CAAC;gBACtC,MAAM;YACR,KAAK,gBAAgB;gBACnB,MAAM,UAAU,GAAG,MAAM,2BAAY,CAAC,oBAAoB,EAAE,CAAC;gBAC7D,MAAM,GAAG,GAAG,UAAU,4BAA4B,CAAC;gBACnD,MAAM;YACR,KAAK,kBAAkB;gBACrB,MAAM,YAAY,GAAG,MAAM,+BAAc,CAAC,sBAAsB,EAAE,CAAC;gBACnE,MAAM,GAAG,GAAG,YAAY,8BAA8B,CAAC;gBACvD,MAAM;YACR,KAAK,eAAe;gBAClB,kCAAc,CAAC,YAAY,EAAE,CAAC;gBAC9B,MAAM,GAAG,uBAAuB,CAAC;gBACjC,MAAM;YACR;gBACE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB;iBAC9B,CAAC,CAAC;QACP,CAAC;QAED,yBAAyB;QACzB,MAAM,2BAAY,CAAC,QAAQ,CAAC,6BAAc,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAChE,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;SAClC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,2BAA2B;SACnC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE;gBACR,KAAK,EAAE;oBACL,SAAS,EAAE,2BAAY,CAAC,WAAW,EAAE;oBACrC,KAAK,EAAE,MAAM,2BAAY,CAAC,QAAQ,EAAE;iBACrC;gBACD,QAAQ,EAAE;oBACR,SAAS,EAAE,+BAAc,CAAC,WAAW,EAAE;oBACvC,KAAK,EAAE,MAAM,+BAAc,CAAC,eAAe,EAAE;iBAC9C;gBACD,MAAM,EAAE;oBACN,SAAS,EAAE,2BAAY,CAAC,WAAW,EAAE;oBACrC,KAAK,EAAE,MAAM,2BAAY,CAAC,aAAa,EAAE;iBAC1C;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,MAAM,IAAA,gCAAiB,EAAC,MAAM,CAAC;iBACvC;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,kCAAc,CAAC,aAAa,EAAE;iBACtC;aACF;SACF,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kCAAkC;SAC1C,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport { authenticate as authMiddleware } from '../middleware/auth';\r\nimport { adminMiddleware } from '../middleware/adminMiddleware';\r\nimport { auditService, AuditEventType, RiskLevel } from '../services/auditService';\r\nimport { cacheService } from '../services/cacheService';\r\nimport { sessionService } from '../services/sessionService';\r\nimport { tokenService } from '../services/tokenService';\r\nimport { queryOptimizer } from '../utils/queryOptimization';\r\nimport { getRateLimitStats, cleanupRateLimitData } from '../middleware/rateLimiting';\r\nimport { adminRateLimit } from '../middleware/rateLimiting';\r\nimport { strictSanitization } from '../middleware/sanitization';\r\n\r\nconst router = Router();\r\n\r\n// Apply admin-specific middleware\r\nrouter.use(adminRateLimit);\r\nrouter.use(authMiddleware);\r\nrouter.use(adminMiddleware);\r\nrouter.use(strictSanitization());\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/audit/logs:\r\n *   get:\r\n *     summary: Get audit logs with filtering\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: userId\r\n *         schema:\r\n *           type: string\r\n *         description: Filter by user ID\r\n *       - in: query\r\n *         name: eventType\r\n *         schema:\r\n *           type: string\r\n *         description: Filter by event type\r\n *       - in: query\r\n *         name: riskLevel\r\n *         schema:\r\n *           type: string\r\n *           enum: [low, medium, high, critical]\r\n *         description: Filter by risk level\r\n *       - in: query\r\n *         name: startDate\r\n *         schema:\r\n *           type: string\r\n *           format: date-time\r\n *         description: Start date for filtering\r\n *       - in: query\r\n *         name: endDate\r\n *         schema:\r\n *           type: string\r\n *           format: date-time\r\n *         description: End date for filtering\r\n *       - in: query\r\n *         name: page\r\n *         schema:\r\n *           type: integer\r\n *           default: 1\r\n *         description: Page number\r\n *       - in: query\r\n *         name: limit\r\n *         schema:\r\n *           type: integer\r\n *           default: 20\r\n *         description: Items per page\r\n *     responses:\r\n *       200:\r\n *         description: Audit logs retrieved successfully\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n *       403:\r\n *         $ref: '#/components/responses/ForbiddenError'\r\n */\r\nrouter.get('/audit/logs', async (req, res) => {\r\n  try {\r\n    const filters = {\r\n      userId: req.query.userId as string,\r\n      eventType: req.query.eventType as AuditEventType,\r\n      riskLevel: req.query.riskLevel as RiskLevel,\r\n      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,\r\n      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,\r\n      page: parseInt(req.query.page as string) || 1,\r\n      limit: parseInt(req.query.limit as string) || 20\r\n    };\r\n\r\n    const result = await auditService.getAuditLogs(filters);\r\n\r\n    // Log admin access to audit logs\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'audit_logs',\r\n      metadata: { filters }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: result\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve audit logs'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/audit/stats:\r\n *   get:\r\n *     summary: Get audit statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: timeframe\r\n *         schema:\r\n *           type: string\r\n *           enum: [hour, day, week, month]\r\n *           default: day\r\n *         description: Timeframe for statistics\r\n *     responses:\r\n *       200:\r\n *         description: Audit statistics retrieved successfully\r\n */\r\nrouter.get('/audit/stats', async (req, res) => {\r\n  try {\r\n    const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week' | 'month') || 'day';\r\n    const stats = await auditService.getAuditStats(timeframe);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve audit statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/performance/cache:\r\n *   get:\r\n *     summary: Get cache statistics\r\n *     tags: [Admin, Performance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Cache statistics retrieved successfully\r\n */\r\nrouter.get('/performance/cache', async (req, res) => {\r\n  try {\r\n    const stats = await cacheService.getStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: cacheService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve cache statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/performance/queries:\r\n *   get:\r\n *     summary: Get query performance statistics\r\n *     tags: [Admin, Performance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Query statistics retrieved successfully\r\n */\r\nrouter.get('/performance/queries', async (req, res) => {\r\n  try {\r\n    const stats = queryOptimizer.getQueryStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve query statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/rate-limits:\r\n *   get:\r\n *     summary: Get rate limiting statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: query\r\n *         name: timeframe\r\n *         schema:\r\n *           type: string\r\n *           enum: [hour, day, week]\r\n *           default: hour\r\n *         description: Timeframe for rate limit statistics\r\n *     responses:\r\n *       200:\r\n *         description: Rate limit statistics retrieved successfully\r\n */\r\nrouter.get('/security/rate-limits', async (req, res) => {\r\n  try {\r\n    const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week') || 'hour';\r\n    const stats = await getRateLimitStats(timeframe);\r\n\r\n    res.json({\r\n      success: true,\r\n      data: stats\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve rate limit statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/sessions:\r\n *   get:\r\n *     summary: Get session statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Session statistics retrieved successfully\r\n */\r\nrouter.get('/security/sessions', async (req, res) => {\r\n  try {\r\n    const stats = await sessionService.getSessionStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: sessionService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve session statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/security/tokens:\r\n *   get:\r\n *     summary: Get token statistics\r\n *     tags: [Admin, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Token statistics retrieved successfully\r\n */\r\nrouter.get('/security/tokens', async (req, res) => {\r\n  try {\r\n    const stats = await tokenService.getTokenStats();\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        ...stats,\r\n        isConnected: tokenService.isConnected()\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve token statistics'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/maintenance/cleanup:\r\n *   post:\r\n *     summary: Perform system cleanup\r\n *     tags: [Admin, Maintenance]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     requestBody:\r\n *       required: true\r\n *       content:\r\n *         application/json:\r\n *           schema:\r\n *             type: object\r\n *             properties:\r\n *               cleanupType:\r\n *                 type: string\r\n *                 enum: [rate-limits, expired-tokens, expired-sessions, query-metrics]\r\n *                 description: Type of cleanup to perform\r\n *             required:\r\n *               - cleanupType\r\n *     responses:\r\n *       200:\r\n *         description: Cleanup completed successfully\r\n */\r\nrouter.post('/maintenance/cleanup', async (req, res) => {\r\n  try {\r\n    const { cleanupType } = req.body;\r\n    let result;\r\n\r\n    switch (cleanupType) {\r\n      case 'rate-limits':\r\n        await cleanupRateLimitData();\r\n        result = 'Rate limit data cleaned up';\r\n        break;\r\n      case 'expired-tokens':\r\n        const tokenCount = await tokenService.cleanupExpiredTokens();\r\n        result = `${tokenCount} expired tokens cleaned up`;\r\n        break;\r\n      case 'expired-sessions':\r\n        const sessionCount = await sessionService.cleanupExpiredSessions();\r\n        result = `${sessionCount} expired sessions cleaned up`;\r\n        break;\r\n      case 'query-metrics':\r\n        queryOptimizer.clearMetrics();\r\n        result = 'Query metrics cleared';\r\n        break;\r\n      default:\r\n        return res.status(400).json({\r\n          success: false,\r\n          error: 'Invalid cleanup type'\r\n        });\r\n    }\r\n\r\n    // Log maintenance action\r\n    await auditService.logEvent(AuditEventType.MAINTENANCE_MODE, req, {\r\n      metadata: { cleanupType, result }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      message: result\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to perform cleanup'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/admin/system/health:\r\n *   get:\r\n *     summary: Get detailed system health status\r\n *     tags: [Admin, System]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: System health status retrieved successfully\r\n */\r\nrouter.get('/system/health', async (req, res) => {\r\n  try {\r\n    const health = {\r\n      timestamp: new Date().toISOString(),\r\n      services: {\r\n        cache: {\r\n          connected: cacheService.isConnected(),\r\n          stats: await cacheService.getStats()\r\n        },\r\n        sessions: {\r\n          connected: sessionService.isConnected(),\r\n          stats: await sessionService.getSessionStats()\r\n        },\r\n        tokens: {\r\n          connected: tokenService.isConnected(),\r\n          stats: await tokenService.getTokenStats()\r\n        },\r\n        rateLimiting: {\r\n          stats: await getRateLimitStats('hour')\r\n        },\r\n        queries: {\r\n          stats: queryOptimizer.getQueryStats()\r\n        }\r\n      }\r\n    };\r\n\r\n    res.json({\r\n      success: true,\r\n      data: health\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve system health'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ccbfe9d801e3d5f4bc1ee9b04615f12de3e0a027"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1io6lkpmz1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1io6lkpmz1();
cov_1io6lkpmz1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[1]++, require("express"));
const auth_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[2]++, require("../middleware/auth"));
const adminMiddleware_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[3]++, require("../middleware/adminMiddleware"));
const auditService_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[4]++, require("../services/auditService"));
const cacheService_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[5]++, require("../services/cacheService"));
const sessionService_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[6]++, require("../services/sessionService"));
const tokenService_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[7]++, require("../services/tokenService"));
const queryOptimization_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[8]++, require("../utils/queryOptimization"));
const rateLimiting_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[9]++, require("../middleware/rateLimiting"));
const rateLimiting_2 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[10]++, require("../middleware/rateLimiting"));
const sanitization_1 =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[11]++, require("../middleware/sanitization"));
const router =
/* istanbul ignore next */
(cov_1io6lkpmz1().s[12]++, (0, express_1.Router)());
// Apply admin-specific middleware
/* istanbul ignore next */
cov_1io6lkpmz1().s[13]++;
router.use(rateLimiting_2.adminRateLimit);
/* istanbul ignore next */
cov_1io6lkpmz1().s[14]++;
router.use(auth_1.authenticate);
/* istanbul ignore next */
cov_1io6lkpmz1().s[15]++;
router.use(adminMiddleware_1.adminMiddleware);
/* istanbul ignore next */
cov_1io6lkpmz1().s[16]++;
router.use((0, sanitization_1.strictSanitization)());
/**
 * @swagger
 * /api/admin/audit/logs:
 *   get:
 *     summary: Get audit logs with filtering
 *     tags: [Admin, Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Filter by user ID
 *       - in: query
 *         name: eventType
 *         schema:
 *           type: string
 *         description: Filter by event type
 *       - in: query
 *         name: riskLevel
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: Filter by risk level
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for filtering
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Audit logs retrieved successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[17]++;
router.get('/audit/logs', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[0]++;
  cov_1io6lkpmz1().s[18]++;
  try {
    const filters =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[19]++, {
      userId: req.query.userId,
      eventType: req.query.eventType,
      riskLevel: req.query.riskLevel,
      startDate: req.query.startDate ?
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[0][0]++, new Date(req.query.startDate)) :
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[0][1]++, undefined),
      endDate: req.query.endDate ?
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[1][0]++, new Date(req.query.endDate)) :
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[1][1]++, undefined),
      page:
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[2][0]++, parseInt(req.query.page)) ||
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[2][1]++, 1),
      limit:
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[3][0]++, parseInt(req.query.limit)) ||
      /* istanbul ignore next */
      (cov_1io6lkpmz1().b[3][1]++, 20)
    });
    const result =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[20]++, await auditService_1.auditService.getAuditLogs(filters));
    // Log admin access to audit logs
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[21]++;
    await auditService_1.auditService.logEvent(auditService_1.AuditEventType.DATA_VIEWED, req, {
      resource: 'audit_logs',
      metadata: {
        filters
      }
    });
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[22]++;
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[23]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve audit logs'
    });
  }
});
/**
 * @swagger
 * /api/admin/audit/stats:
 *   get:
 *     summary: Get audit statistics
 *     tags: [Admin, Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [hour, day, week, month]
 *           default: day
 *         description: Timeframe for statistics
 *     responses:
 *       200:
 *         description: Audit statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[24]++;
router.get('/audit/stats', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[1]++;
  cov_1io6lkpmz1().s[25]++;
  try {
    const timeframe =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[26]++,
    /* istanbul ignore next */
    (cov_1io6lkpmz1().b[4][0]++, req.query.timeframe) ||
    /* istanbul ignore next */
    (cov_1io6lkpmz1().b[4][1]++, 'day'));
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[27]++, await auditService_1.auditService.getAuditStats(timeframe));
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[28]++;
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[29]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve audit statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/performance/cache:
 *   get:
 *     summary: Get cache statistics
 *     tags: [Admin, Performance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[30]++;
router.get('/performance/cache', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[2]++;
  cov_1io6lkpmz1().s[31]++;
  try {
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[32]++, await cacheService_1.cacheService.getStats());
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[33]++;
    res.json({
      success: true,
      data: {
        ...stats,
        isConnected: cacheService_1.cacheService.isConnected()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[34]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve cache statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/performance/queries:
 *   get:
 *     summary: Get query performance statistics
 *     tags: [Admin, Performance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Query statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[35]++;
router.get('/performance/queries', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[3]++;
  cov_1io6lkpmz1().s[36]++;
  try {
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[37]++, queryOptimization_1.queryOptimizer.getQueryStats());
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[38]++;
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[39]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve query statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/security/rate-limits:
 *   get:
 *     summary: Get rate limiting statistics
 *     tags: [Admin, Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [hour, day, week]
 *           default: hour
 *         description: Timeframe for rate limit statistics
 *     responses:
 *       200:
 *         description: Rate limit statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[40]++;
router.get('/security/rate-limits', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[4]++;
  cov_1io6lkpmz1().s[41]++;
  try {
    const timeframe =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[42]++,
    /* istanbul ignore next */
    (cov_1io6lkpmz1().b[5][0]++, req.query.timeframe) ||
    /* istanbul ignore next */
    (cov_1io6lkpmz1().b[5][1]++, 'hour'));
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[43]++, await (0, rateLimiting_1.getRateLimitStats)(timeframe));
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[44]++;
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[45]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve rate limit statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/security/sessions:
 *   get:
 *     summary: Get session statistics
 *     tags: [Admin, Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Session statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[46]++;
router.get('/security/sessions', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[5]++;
  cov_1io6lkpmz1().s[47]++;
  try {
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[48]++, await sessionService_1.sessionService.getSessionStats());
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[49]++;
    res.json({
      success: true,
      data: {
        ...stats,
        isConnected: sessionService_1.sessionService.isConnected()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[50]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve session statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/security/tokens:
 *   get:
 *     summary: Get token statistics
 *     tags: [Admin, Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Token statistics retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[51]++;
router.get('/security/tokens', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[6]++;
  cov_1io6lkpmz1().s[52]++;
  try {
    const stats =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[53]++, await tokenService_1.tokenService.getTokenStats());
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[54]++;
    res.json({
      success: true,
      data: {
        ...stats,
        isConnected: tokenService_1.tokenService.isConnected()
      }
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[55]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve token statistics'
    });
  }
});
/**
 * @swagger
 * /api/admin/maintenance/cleanup:
 *   post:
 *     summary: Perform system cleanup
 *     tags: [Admin, Maintenance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               cleanupType:
 *                 type: string
 *                 enum: [rate-limits, expired-tokens, expired-sessions, query-metrics]
 *                 description: Type of cleanup to perform
 *             required:
 *               - cleanupType
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[56]++;
router.post('/maintenance/cleanup', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[7]++;
  cov_1io6lkpmz1().s[57]++;
  try {
    const {
      cleanupType
    } =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[58]++, req.body);
    let result;
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[59]++;
    switch (cleanupType) {
      case 'rate-limits':
        /* istanbul ignore next */
        cov_1io6lkpmz1().b[6][0]++;
        cov_1io6lkpmz1().s[60]++;
        await (0, rateLimiting_1.cleanupRateLimitData)();
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[61]++;
        result = 'Rate limit data cleaned up';
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[62]++;
        break;
      case 'expired-tokens':
        /* istanbul ignore next */
        cov_1io6lkpmz1().b[6][1]++;
        const tokenCount =
        /* istanbul ignore next */
        (cov_1io6lkpmz1().s[63]++, await tokenService_1.tokenService.cleanupExpiredTokens());
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[64]++;
        result = `${tokenCount} expired tokens cleaned up`;
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[65]++;
        break;
      case 'expired-sessions':
        /* istanbul ignore next */
        cov_1io6lkpmz1().b[6][2]++;
        const sessionCount =
        /* istanbul ignore next */
        (cov_1io6lkpmz1().s[66]++, await sessionService_1.sessionService.cleanupExpiredSessions());
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[67]++;
        result = `${sessionCount} expired sessions cleaned up`;
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[68]++;
        break;
      case 'query-metrics':
        /* istanbul ignore next */
        cov_1io6lkpmz1().b[6][3]++;
        cov_1io6lkpmz1().s[69]++;
        queryOptimization_1.queryOptimizer.clearMetrics();
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[70]++;
        result = 'Query metrics cleared';
        /* istanbul ignore next */
        cov_1io6lkpmz1().s[71]++;
        break;
      default:
        /* istanbul ignore next */
        cov_1io6lkpmz1().b[6][4]++;
        cov_1io6lkpmz1().s[72]++;
        return res.status(400).json({
          success: false,
          error: 'Invalid cleanup type'
        });
    }
    // Log maintenance action
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[73]++;
    await auditService_1.auditService.logEvent(auditService_1.AuditEventType.MAINTENANCE_MODE, req, {
      metadata: {
        cleanupType,
        result
      }
    });
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[74]++;
    res.json({
      success: true,
      message: result
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[75]++;
    res.status(500).json({
      success: false,
      error: 'Failed to perform cleanup'
    });
  }
});
/**
 * @swagger
 * /api/admin/system/health:
 *   get:
 *     summary: Get detailed system health status
 *     tags: [Admin, System]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System health status retrieved successfully
 */
/* istanbul ignore next */
cov_1io6lkpmz1().s[76]++;
router.get('/system/health', async (req, res) => {
  /* istanbul ignore next */
  cov_1io6lkpmz1().f[8]++;
  cov_1io6lkpmz1().s[77]++;
  try {
    const health =
    /* istanbul ignore next */
    (cov_1io6lkpmz1().s[78]++, {
      timestamp: new Date().toISOString(),
      services: {
        cache: {
          connected: cacheService_1.cacheService.isConnected(),
          stats: await cacheService_1.cacheService.getStats()
        },
        sessions: {
          connected: sessionService_1.sessionService.isConnected(),
          stats: await sessionService_1.sessionService.getSessionStats()
        },
        tokens: {
          connected: tokenService_1.tokenService.isConnected(),
          stats: await tokenService_1.tokenService.getTokenStats()
        },
        rateLimiting: {
          stats: await (0, rateLimiting_1.getRateLimitStats)('hour')
        },
        queries: {
          stats: queryOptimization_1.queryOptimizer.getQueryStats()
        }
      }
    });
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[79]++;
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1io6lkpmz1().s[80]++;
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system health'
    });
  }
});
/* istanbul ignore next */
cov_1io6lkpmz1().s[81]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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