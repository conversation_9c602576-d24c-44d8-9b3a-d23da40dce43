{"version": 3, "names": ["cov_22ugj90bbp", "actualCoverage", "mongoose_1", "s", "__importDefault", "require", "logger_1", "cacheService_1", "defaultConfig", "enableCaching", "cacheType", "enablePagination", "defaultLimit", "maxLimit", "enableProjection", "enablePopulation", "enableSorting", "enableIndexHints", "QueryOptimizer", "constructor", "f", "metrics", "maxMetricsHistory", "optimizedFind", "model", "filter", "options", "b", "config", "startTime", "Date", "now", "finalConfig", "cache<PERSON>ey", "generate<PERSON>ache<PERSON>ey", "modelName", "cached", "cacheService", "get", "recordMetrics", "data", "length", "query", "buildOptimizedQuery", "total", "Promise", "all", "countDocuments", "exec", "page", "Math", "max", "limit", "min", "pages", "ceil", "result", "pagination", "hasMore", "has<PERSON>rev", "set", "cacheTTL", "error", "logger", "optimizedFindOne", "findOne", "select", "populate", "optimizedAggregate", "pipeline", "aggregate", "option", "find", "skip", "sort", "lean", "operation", "keyData", "JSON", "stringify", "<PERSON><PERSON><PERSON>", "from", "toString", "queryType", "executionTime", "documentsExamined", "documentsReturned", "indexUsed", "cacheHit", "metric", "timestamp", "push", "slice", "warn", "getQueryStats", "totalQueries", "totalExecutionTime", "reduce", "sum", "m", "averageExecutionTime", "cacheHitRate", "slowQueries", "queryTypeStats", "stats", "count", "totalTime", "cacheHits", "round", "recentMetrics", "clearMetrics", "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheHelpers", "invalidate<PERSON><PERSON><PERSON>", "info", "exports", "queryOptimizer", "queryHelpers", "findUserById", "userId", "User", "default", "_id", "searchProperties", "Property", "findPropertyById", "propertyId", "getUserNotifications", "Notification", "dismissed", "createdAt"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\utils\\queryOptimization.ts"], "sourcesContent": ["import mongoose, { Query, Document, Model, FilterQuery, UpdateQuery } from 'mongoose';\r\nimport { logger } from './logger';\r\nimport { cacheService, cacheHelpers } from '../services/cacheService';\r\n\r\n// Query optimization configuration\r\nexport interface QueryOptimizationConfig {\r\n  enableCaching: boolean;\r\n  cacheType: 'user' | 'property' | 'search' | 'static' | 'temp';\r\n  cacheTTL?: number;\r\n  enablePagination: boolean;\r\n  defaultLimit: number;\r\n  maxLimit: number;\r\n  enableProjection: boolean;\r\n  enablePopulation: boolean;\r\n  enableSorting: boolean;\r\n  enableIndexHints: boolean;\r\n}\r\n\r\n// Default optimization configuration\r\nconst defaultConfig: QueryOptimizationConfig = {\r\n  enableCaching: true,\r\n  cacheType: 'temp',\r\n  enablePagination: true,\r\n  defaultLimit: 20,\r\n  maxLimit: 100,\r\n  enableProjection: true,\r\n  enablePopulation: true,\r\n  enableSorting: true,\r\n  enableIndexHints: true\r\n};\r\n\r\n// Pagination interface\r\nexport interface PaginationOptions {\r\n  page?: number;\r\n  limit?: number;\r\n  sort?: string | Record<string, 1 | -1>;\r\n  select?: string | Record<string, 1 | 0>;\r\n  populate?: string | Record<string, any>;\r\n}\r\n\r\n// Pagination result interface\r\nexport interface PaginatedResult<T> {\r\n  data: T[];\r\n  pagination: {\r\n    page: number;\r\n    limit: number;\r\n    total: number;\r\n    pages: number;\r\n    hasMore: boolean;\r\n    hasPrev: boolean;\r\n  };\r\n}\r\n\r\n// Query performance metrics\r\nexport interface QueryMetrics {\r\n  queryType: string;\r\n  executionTime: number;\r\n  documentsExamined: number;\r\n  documentsReturned: number;\r\n  indexUsed: boolean;\r\n  cacheHit: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\nclass QueryOptimizer {\r\n  private metrics: QueryMetrics[] = [];\r\n  private maxMetricsHistory = 1000;\r\n\r\n  /**\r\n   * Optimize a find query with caching and pagination\r\n   */\r\n  async optimizedFind<T extends Document>(\r\n    model: Model<T>,\r\n    filter: FilterQuery<T>,\r\n    options: PaginationOptions = {},\r\n    config: Partial<QueryOptimizationConfig> = {}\r\n  ): Promise<PaginatedResult<T>> {\r\n    const startTime = Date.now();\r\n    const finalConfig = { ...defaultConfig, ...config };\r\n    \r\n    // Generate cache key\r\n    const cacheKey = this.generateCacheKey('find', model.modelName, filter, options);\r\n    \r\n    // Try to get from cache first\r\n    if (finalConfig.enableCaching) {\r\n      const cached = await cacheService.get<PaginatedResult<T>>(cacheKey, finalConfig.cacheType);\r\n      if (cached) {\r\n        this.recordMetrics('find', Date.now() - startTime, 0, cached.data.length, false, true);\r\n        return cached;\r\n      }\r\n    }\r\n\r\n    try {\r\n      // Build optimized query\r\n      const query = this.buildOptimizedQuery(model, filter, options, finalConfig);\r\n      \r\n      // Execute count and find queries in parallel\r\n      const [total, data] = await Promise.all([\r\n        model.countDocuments(filter),\r\n        query.exec()\r\n      ]);\r\n\r\n      // Calculate pagination info\r\n      const page = Math.max(1, options.page || 1);\r\n      const limit = Math.min(finalConfig.maxLimit, Math.max(1, options.limit || finalConfig.defaultLimit));\r\n      const pages = Math.ceil(total / limit);\r\n\r\n      const result: PaginatedResult<T> = {\r\n        data,\r\n        pagination: {\r\n          page,\r\n          limit,\r\n          total,\r\n          pages,\r\n          hasMore: page < pages,\r\n          hasPrev: page > 1\r\n        }\r\n      };\r\n\r\n      // Cache the result\r\n      if (finalConfig.enableCaching) {\r\n        await cacheService.set(cacheKey, result, finalConfig.cacheType, finalConfig.cacheTTL);\r\n      }\r\n\r\n      // Record metrics\r\n      this.recordMetrics('find', Date.now() - startTime, total, data.length, true, false);\r\n\r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Query optimization error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize a findOne query with caching\r\n   */\r\n  async optimizedFindOne<T extends Document>(\r\n    model: Model<T>,\r\n    filter: FilterQuery<T>,\r\n    options: { select?: string; populate?: string } = {},\r\n    config: Partial<QueryOptimizationConfig> = {}\r\n  ): Promise<T | null> {\r\n    const startTime = Date.now();\r\n    const finalConfig = { ...defaultConfig, ...config };\r\n    \r\n    // Generate cache key\r\n    const cacheKey = this.generateCacheKey('findOne', model.modelName, filter, options);\r\n    \r\n    // Try to get from cache first\r\n    if (finalConfig.enableCaching) {\r\n      const cached = await cacheService.get<T>(cacheKey, finalConfig.cacheType);\r\n      if (cached) {\r\n        this.recordMetrics('findOne', Date.now() - startTime, 0, 1, false, true);\r\n        return cached;\r\n      }\r\n    }\r\n\r\n    try {\r\n      let query = model.findOne(filter);\r\n\r\n      // Apply optimizations\r\n      if (finalConfig.enableProjection && options.select) {\r\n        query = query.select(options.select);\r\n      }\r\n\r\n      if (finalConfig.enablePopulation && options.populate) {\r\n        query = query.populate(options.populate);\r\n      }\r\n\r\n      const result = await query.exec();\r\n\r\n      // Cache the result\r\n      if (finalConfig.enableCaching && result) {\r\n        await cacheService.set(cacheKey, result, finalConfig.cacheType, finalConfig.cacheTTL);\r\n      }\r\n\r\n      // Record metrics\r\n      this.recordMetrics('findOne', Date.now() - startTime, 1, result ? 1 : 0, true, false);\r\n\r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Query optimization error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Optimize an aggregation query with caching\r\n   */\r\n  async optimizedAggregate<T>(\r\n    model: Model<any>,\r\n    pipeline: any[],\r\n    options: { allowDiskUse?: boolean } = {},\r\n    config: Partial<QueryOptimizationConfig> = {}\r\n  ): Promise<T[]> {\r\n    const startTime = Date.now();\r\n    const finalConfig = { ...defaultConfig, ...config };\r\n    \r\n    // Generate cache key\r\n    const cacheKey = this.generateCacheKey('aggregate', model.modelName, pipeline, options);\r\n    \r\n    // Try to get from cache first\r\n    if (finalConfig.enableCaching) {\r\n      const cached = await cacheService.get<T[]>(cacheKey, finalConfig.cacheType);\r\n      if (cached) {\r\n        this.recordMetrics('aggregate', Date.now() - startTime, 0, cached.length, false, true);\r\n        return cached;\r\n      }\r\n    }\r\n\r\n    try {\r\n      const result = await model.aggregate(pipeline).option(options);\r\n\r\n      // Cache the result\r\n      if (finalConfig.enableCaching) {\r\n        await cacheService.set(cacheKey, result, finalConfig.cacheType, finalConfig.cacheTTL);\r\n      }\r\n\r\n      // Record metrics\r\n      this.recordMetrics('aggregate', Date.now() - startTime, result.length, result.length, true, false);\r\n\r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Aggregation optimization error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Build optimized query with all optimizations applied\r\n   */\r\n  private buildOptimizedQuery<T extends Document>(\r\n    model: Model<T>,\r\n    filter: FilterQuery<T>,\r\n    options: PaginationOptions,\r\n    config: QueryOptimizationConfig\r\n  ): Query<T[], T> {\r\n    let query = model.find(filter);\r\n\r\n    // Apply pagination\r\n    if (config.enablePagination) {\r\n      const page = Math.max(1, options.page || 1);\r\n      const limit = Math.min(config.maxLimit, Math.max(1, options.limit || config.defaultLimit));\r\n      const skip = (page - 1) * limit;\r\n      \r\n      query = query.skip(skip).limit(limit);\r\n    }\r\n\r\n    // Apply sorting\r\n    if (config.enableSorting && options.sort) {\r\n      query = query.sort(options.sort);\r\n    }\r\n\r\n    // Apply field selection (projection)\r\n    if (config.enableProjection && options.select) {\r\n      query = query.select(options.select);\r\n    }\r\n\r\n    // Apply population\r\n    if (config.enablePopulation && options.populate) {\r\n      query = query.populate(options.populate);\r\n    }\r\n\r\n    // Apply lean for better performance (returns plain objects instead of Mongoose documents)\r\n    query = query.lean();\r\n\r\n    return query;\r\n  }\r\n\r\n  /**\r\n   * Generate cache key for query\r\n   */\r\n  private generateCacheKey(\r\n    operation: string,\r\n    modelName: string,\r\n    filter: any,\r\n    options: any\r\n  ): string {\r\n    const keyData = {\r\n      operation,\r\n      model: modelName,\r\n      filter: JSON.stringify(filter),\r\n      options: JSON.stringify(options)\r\n    };\r\n    \r\n    return `query:${Buffer.from(JSON.stringify(keyData)).toString('base64')}`;\r\n  }\r\n\r\n  /**\r\n   * Record query performance metrics\r\n   */\r\n  private recordMetrics(\r\n    queryType: string,\r\n    executionTime: number,\r\n    documentsExamined: number,\r\n    documentsReturned: number,\r\n    indexUsed: boolean,\r\n    cacheHit: boolean\r\n  ): void {\r\n    const metric: QueryMetrics = {\r\n      queryType,\r\n      executionTime,\r\n      documentsExamined,\r\n      documentsReturned,\r\n      indexUsed,\r\n      cacheHit,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    this.metrics.push(metric);\r\n\r\n    // Keep only recent metrics\r\n    if (this.metrics.length > this.maxMetricsHistory) {\r\n      this.metrics = this.metrics.slice(-this.maxMetricsHistory);\r\n    }\r\n\r\n    // Log slow queries\r\n    if (executionTime > 1000) { // Log queries taking more than 1 second\r\n      logger.warn('Slow query detected', {\r\n        queryType,\r\n        executionTime,\r\n        documentsExamined,\r\n        documentsReturned,\r\n        indexUsed,\r\n        cacheHit\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get query performance statistics\r\n   */\r\n  getQueryStats(): any {\r\n    if (this.metrics.length === 0) {\r\n      return { totalQueries: 0 };\r\n    }\r\n\r\n    const totalQueries = this.metrics.length;\r\n    const totalExecutionTime = this.metrics.reduce((sum, m) => sum + m.executionTime, 0);\r\n    const averageExecutionTime = totalExecutionTime / totalQueries;\r\n    const cacheHitRate = this.metrics.filter(m => m.cacheHit).length / totalQueries;\r\n    const slowQueries = this.metrics.filter(m => m.executionTime > 1000).length;\r\n\r\n    const queryTypeStats = this.metrics.reduce((stats, metric) => {\r\n      if (!stats[metric.queryType]) {\r\n        stats[metric.queryType] = { count: 0, totalTime: 0, cacheHits: 0 };\r\n      }\r\n      stats[metric.queryType].count++;\r\n      stats[metric.queryType].totalTime += metric.executionTime;\r\n      if (metric.cacheHit) {\r\n        stats[metric.queryType].cacheHits++;\r\n      }\r\n      return stats;\r\n    }, {} as any);\r\n\r\n    return {\r\n      totalQueries,\r\n      averageExecutionTime: Math.round(averageExecutionTime),\r\n      cacheHitRate: Math.round(cacheHitRate * 100),\r\n      slowQueries,\r\n      queryTypeStats,\r\n      recentMetrics: this.metrics.slice(-10) // Last 10 queries\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clear query metrics\r\n   */\r\n  clearMetrics(): void {\r\n    this.metrics = [];\r\n  }\r\n\r\n  /**\r\n   * Invalidate cache for a model\r\n   */\r\n  async invalidateModelCache(modelName: string): Promise<void> {\r\n    await cacheHelpers.invalidatePattern(`*${modelName}*`);\r\n    logger.info(`Cache invalidated for model: ${modelName}`);\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const queryOptimizer = new QueryOptimizer();\r\n\r\n// Helper functions for common query patterns\r\nexport const queryHelpers = {\r\n  /**\r\n   * Optimized user lookup with caching\r\n   */\r\n  async findUserById(userId: string, select?: string): Promise<any> {\r\n    const User = mongoose.model('User');\r\n    return await queryOptimizer.optimizedFindOne(\r\n      User,\r\n      { _id: userId },\r\n      { select },\r\n      { cacheType: 'user', cacheTTL: 15 * 60 } // 15 minutes\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Optimized property search with pagination\r\n   */\r\n  async searchProperties(\r\n    filter: any,\r\n    options: PaginationOptions = {}\r\n  ): Promise<PaginatedResult<any>> {\r\n    const Property = mongoose.model('Property');\r\n    return await queryOptimizer.optimizedFind(\r\n      Property,\r\n      filter,\r\n      options,\r\n      { cacheType: 'search', cacheTTL: 5 * 60 } // 5 minutes\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Optimized property lookup with caching\r\n   */\r\n  async findPropertyById(propertyId: string, populate?: string): Promise<any> {\r\n    const Property = mongoose.model('Property');\r\n    return await queryOptimizer.optimizedFindOne(\r\n      Property,\r\n      { _id: propertyId },\r\n      { populate },\r\n      { cacheType: 'property', cacheTTL: 30 * 60 } // 30 minutes\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Optimized notification lookup\r\n   */\r\n  async getUserNotifications(\r\n    userId: string,\r\n    options: PaginationOptions = {}\r\n  ): Promise<PaginatedResult<any>> {\r\n    const Notification = mongoose.model('Notification');\r\n    return await queryOptimizer.optimizedFind(\r\n      Notification,\r\n      { userId, dismissed: false },\r\n      { ...options, sort: { createdAt: -1 } },\r\n      { cacheType: 'notification', cacheTTL: 5 * 60 } // 5 minutes\r\n    );\r\n  }\r\n};\r\n\r\nexport default queryOptimizer;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwBU;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxBV,MAAAE,UAAA;AAAA;AAAA,CAAAF,cAAA,GAAAG,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAN,cAAA,GAAAG,CAAA,OAAAE,OAAA;AACA,MAAAE,cAAA;AAAA;AAAA,CAAAP,cAAA,GAAAG,CAAA,OAAAE,OAAA;AAgBA;AACA,MAAMG,aAAa;AAAA;AAAA,CAAAR,cAAA,GAAAG,CAAA,OAA4B;EAC7CM,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,MAAM;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAE,GAAG;EACbC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE;CACnB;AAmCD,MAAMC,cAAc;EAApBC,YAAA;IAAA;IAAAnB,cAAA,GAAAoB,CAAA;IAAApB,cAAA,GAAAG,CAAA;IACU,KAAAkB,OAAO,GAAmB,EAAE;IAAC;IAAArB,cAAA,GAAAG,CAAA;IAC7B,KAAAmB,iBAAiB,GAAG,IAAI;EA0TlC;EAxTE;;;EAGA,MAAMC,aAAaA,CACjBC,KAAe,EACfC,MAAsB,EACtBC,OAAA;EAAA;EAAA,CAAA1B,cAAA,GAAA2B,CAAA,UAA6B,EAAE,GAC/BC,MAAA;EAAA;EAAA,CAAA5B,cAAA,GAAA2B,CAAA,UAA2C,EAAE;IAAA;IAAA3B,cAAA,GAAAoB,CAAA;IAE7C,MAAMS,SAAS;IAAA;IAAA,CAAA7B,cAAA,GAAAG,CAAA,QAAG2B,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,WAAW;IAAA;IAAA,CAAAhC,cAAA,GAAAG,CAAA,QAAG;MAAE,GAAGK,aAAa;MAAE,GAAGoB;IAAM,CAAE;IAEnD;IACA,MAAMK,QAAQ;IAAA;IAAA,CAAAjC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC+B,gBAAgB,CAAC,MAAM,EAAEV,KAAK,CAACW,SAAS,EAAEV,MAAM,EAAEC,OAAO,CAAC;IAEhF;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IACA,IAAI6B,WAAW,CAACvB,aAAa,EAAE;MAAA;MAAAT,cAAA,GAAA2B,CAAA;MAC7B,MAAMS,MAAM;MAAA;MAAA,CAAApC,cAAA,GAAAG,CAAA,QAAG,MAAMI,cAAA,CAAA8B,YAAY,CAACC,GAAG,CAAqBL,QAAQ,EAAED,WAAW,CAACtB,SAAS,CAAC;MAAC;MAAAV,cAAA,GAAAG,CAAA;MAC3F,IAAIiC,MAAM,EAAE;QAAA;QAAApC,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACV,IAAI,CAACoC,aAAa,CAAC,MAAM,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAE,CAAC,EAAEO,MAAM,CAACI,IAAI,CAACC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;QAAC;QAAAzC,cAAA,GAAAG,CAAA;QACvF,OAAOiC,MAAM;MACf,CAAC;MAAA;MAAA;QAAApC,cAAA,GAAA2B,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAA3B,cAAA,GAAA2B,CAAA;IAAA;IAAA3B,cAAA,GAAAG,CAAA;IAED,IAAI;MACF;MACA,MAAMuC,KAAK;MAAA;MAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACwC,mBAAmB,CAACnB,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEM,WAAW,CAAC;MAE3E;MACA,MAAM,CAACY,KAAK,EAAEJ,IAAI,CAAC;MAAA;MAAA,CAAAxC,cAAA,GAAAG,CAAA,QAAG,MAAM0C,OAAO,CAACC,GAAG,CAAC,CACtCtB,KAAK,CAACuB,cAAc,CAACtB,MAAM,CAAC,EAC5BiB,KAAK,CAACM,IAAI,EAAE,CACb,CAAC;MAEF;MACA,MAAMC,IAAI;MAAA;MAAA,CAAAjD,cAAA,GAAAG,CAAA,QAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC;MAAE;MAAA,CAAAnD,cAAA,GAAA2B,CAAA,UAAAD,OAAO,CAACuB,IAAI;MAAA;MAAA,CAAAjD,cAAA,GAAA2B,CAAA,UAAI,CAAC,EAAC;MAC3C,MAAMyB,KAAK;MAAA;MAAA,CAAApD,cAAA,GAAAG,CAAA,QAAG+C,IAAI,CAACG,GAAG,CAACrB,WAAW,CAACnB,QAAQ,EAAEqC,IAAI,CAACC,GAAG,CAAC,CAAC;MAAE;MAAA,CAAAnD,cAAA,GAAA2B,CAAA,UAAAD,OAAO,CAAC0B,KAAK;MAAA;MAAA,CAAApD,cAAA,GAAA2B,CAAA,UAAIK,WAAW,CAACpB,YAAY,EAAC,CAAC;MACpG,MAAM0C,KAAK;MAAA;MAAA,CAAAtD,cAAA,GAAAG,CAAA,QAAG+C,IAAI,CAACK,IAAI,CAACX,KAAK,GAAGQ,KAAK,CAAC;MAEtC,MAAMI,MAAM;MAAA;MAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAuB;QACjCqC,IAAI;QACJiB,UAAU,EAAE;UACVR,IAAI;UACJG,KAAK;UACLR,KAAK;UACLU,KAAK;UACLI,OAAO,EAAET,IAAI,GAAGK,KAAK;UACrBK,OAAO,EAAEV,IAAI,GAAG;;OAEnB;MAED;MAAA;MAAAjD,cAAA,GAAAG,CAAA;MACA,IAAI6B,WAAW,CAACvB,aAAa,EAAE;QAAA;QAAAT,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QAC7B,MAAMI,cAAA,CAAA8B,YAAY,CAACuB,GAAG,CAAC3B,QAAQ,EAAEuB,MAAM,EAAExB,WAAW,CAACtB,SAAS,EAAEsB,WAAW,CAAC6B,QAAQ,CAAC;MACvF,CAAC;MAAA;MAAA;QAAA7D,cAAA,GAAA2B,CAAA;MAAA;MAED;MAAA3B,cAAA,GAAAG,CAAA;MACA,IAAI,CAACoC,aAAa,CAAC,MAAM,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAEe,KAAK,EAAEJ,IAAI,CAACC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;MAAC;MAAAzC,cAAA,GAAAG,CAAA;MAEpF,OAAOqD,MAAM;IACf,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAG,CAAA;MACdG,QAAA,CAAAyD,MAAM,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAAC;MAAA9D,cAAA,GAAAG,CAAA;MACjD,MAAM2D,KAAK;IACb;EACF;EAEA;;;EAGA,MAAME,gBAAgBA,CACpBxC,KAAe,EACfC,MAAsB,EACtBC,OAAA;EAAA;EAAA,CAAA1B,cAAA,GAAA2B,CAAA,WAAkD,EAAE,GACpDC,MAAA;EAAA;EAAA,CAAA5B,cAAA,GAAA2B,CAAA,WAA2C,EAAE;IAAA;IAAA3B,cAAA,GAAAoB,CAAA;IAE7C,MAAMS,SAAS;IAAA;IAAA,CAAA7B,cAAA,GAAAG,CAAA,QAAG2B,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,WAAW;IAAA;IAAA,CAAAhC,cAAA,GAAAG,CAAA,QAAG;MAAE,GAAGK,aAAa;MAAE,GAAGoB;IAAM,CAAE;IAEnD;IACA,MAAMK,QAAQ;IAAA;IAAA,CAAAjC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC+B,gBAAgB,CAAC,SAAS,EAAEV,KAAK,CAACW,SAAS,EAAEV,MAAM,EAAEC,OAAO,CAAC;IAEnF;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IACA,IAAI6B,WAAW,CAACvB,aAAa,EAAE;MAAA;MAAAT,cAAA,GAAA2B,CAAA;MAC7B,MAAMS,MAAM;MAAA;MAAA,CAAApC,cAAA,GAAAG,CAAA,QAAG,MAAMI,cAAA,CAAA8B,YAAY,CAACC,GAAG,CAAIL,QAAQ,EAAED,WAAW,CAACtB,SAAS,CAAC;MAAC;MAAAV,cAAA,GAAAG,CAAA;MAC1E,IAAIiC,MAAM,EAAE;QAAA;QAAApC,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACV,IAAI,CAACoC,aAAa,CAAC,SAAS,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;QAAC;QAAA7B,cAAA,GAAAG,CAAA;QACzE,OAAOiC,MAAM;MACf,CAAC;MAAA;MAAA;QAAApC,cAAA,GAAA2B,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAA3B,cAAA,GAAA2B,CAAA;IAAA;IAAA3B,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,IAAIuC,KAAK;MAAA;MAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAGqB,KAAK,CAACyC,OAAO,CAACxC,MAAM,CAAC;MAEjC;MAAA;MAAAzB,cAAA,GAAAG,CAAA;MACA;MAAI;MAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAK,WAAW,CAAClB,gBAAgB;MAAA;MAAA,CAAAd,cAAA,GAAA2B,CAAA,WAAID,OAAO,CAACwC,MAAM,GAAE;QAAA;QAAAlE,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QAClDuC,KAAK,GAAGA,KAAK,CAACwB,MAAM,CAACxC,OAAO,CAACwC,MAAM,CAAC;MACtC,CAAC;MAAA;MAAA;QAAAlE,cAAA,GAAA2B,CAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MAED;MAAI;MAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAK,WAAW,CAACjB,gBAAgB;MAAA;MAAA,CAAAf,cAAA,GAAA2B,CAAA,WAAID,OAAO,CAACyC,QAAQ,GAAE;QAAA;QAAAnE,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACpDuC,KAAK,GAAGA,KAAK,CAACyB,QAAQ,CAACzC,OAAO,CAACyC,QAAQ,CAAC;MAC1C,CAAC;MAAA;MAAA;QAAAnE,cAAA,GAAA2B,CAAA;MAAA;MAED,MAAM6B,MAAM;MAAA;MAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAG,MAAMuC,KAAK,CAACM,IAAI,EAAE;MAEjC;MAAA;MAAAhD,cAAA,GAAAG,CAAA;MACA;MAAI;MAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAK,WAAW,CAACvB,aAAa;MAAA;MAAA,CAAAT,cAAA,GAAA2B,CAAA,WAAI6B,MAAM,GAAE;QAAA;QAAAxD,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACvC,MAAMI,cAAA,CAAA8B,YAAY,CAACuB,GAAG,CAAC3B,QAAQ,EAAEuB,MAAM,EAAExB,WAAW,CAACtB,SAAS,EAAEsB,WAAW,CAAC6B,QAAQ,CAAC;MACvF,CAAC;MAAA;MAAA;QAAA7D,cAAA,GAAA2B,CAAA;MAAA;MAED;MAAA3B,cAAA,GAAAG,CAAA;MACA,IAAI,CAACoC,aAAa,CAAC,SAAS,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAE,CAAC,EAAE2B,MAAM;MAAA;MAAA,CAAAxD,cAAA,GAAA2B,CAAA,WAAG,CAAC;MAAA;MAAA,CAAA3B,cAAA,GAAA2B,CAAA,WAAG,CAAC,GAAE,IAAI,EAAE,KAAK,CAAC;MAAC;MAAA3B,cAAA,GAAAG,CAAA;MAEtF,OAAOqD,MAAM;IACf,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAG,CAAA;MACdG,QAAA,CAAAyD,MAAM,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAAC;MAAA9D,cAAA,GAAAG,CAAA;MACjD,MAAM2D,KAAK;IACb;EACF;EAEA;;;EAGA,MAAMM,kBAAkBA,CACtB5C,KAAiB,EACjB6C,QAAe,EACf3C,OAAA;EAAA;EAAA,CAAA1B,cAAA,GAAA2B,CAAA,WAAsC,EAAE,GACxCC,MAAA;EAAA;EAAA,CAAA5B,cAAA,GAAA2B,CAAA,WAA2C,EAAE;IAAA;IAAA3B,cAAA,GAAAoB,CAAA;IAE7C,MAAMS,SAAS;IAAA;IAAA,CAAA7B,cAAA,GAAAG,CAAA,QAAG2B,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,WAAW;IAAA;IAAA,CAAAhC,cAAA,GAAAG,CAAA,QAAG;MAAE,GAAGK,aAAa;MAAE,GAAGoB;IAAM,CAAE;IAEnD;IACA,MAAMK,QAAQ;IAAA;IAAA,CAAAjC,cAAA,GAAAG,CAAA,QAAG,IAAI,CAAC+B,gBAAgB,CAAC,WAAW,EAAEV,KAAK,CAACW,SAAS,EAAEkC,QAAQ,EAAE3C,OAAO,CAAC;IAEvF;IAAA;IAAA1B,cAAA,GAAAG,CAAA;IACA,IAAI6B,WAAW,CAACvB,aAAa,EAAE;MAAA;MAAAT,cAAA,GAAA2B,CAAA;MAC7B,MAAMS,MAAM;MAAA;MAAA,CAAApC,cAAA,GAAAG,CAAA,QAAG,MAAMI,cAAA,CAAA8B,YAAY,CAACC,GAAG,CAAML,QAAQ,EAAED,WAAW,CAACtB,SAAS,CAAC;MAAC;MAAAV,cAAA,GAAAG,CAAA;MAC5E,IAAIiC,MAAM,EAAE;QAAA;QAAApC,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACV,IAAI,CAACoC,aAAa,CAAC,WAAW,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAE,CAAC,EAAEO,MAAM,CAACK,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;QAAC;QAAAzC,cAAA,GAAAG,CAAA;QACvF,OAAOiC,MAAM;MACf,CAAC;MAAA;MAAA;QAAApC,cAAA,GAAA2B,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAA3B,cAAA,GAAA2B,CAAA;IAAA;IAAA3B,cAAA,GAAAG,CAAA;IAED,IAAI;MACF,MAAMqD,MAAM;MAAA;MAAA,CAAAxD,cAAA,GAAAG,CAAA,QAAG,MAAMqB,KAAK,CAAC8C,SAAS,CAACD,QAAQ,CAAC,CAACE,MAAM,CAAC7C,OAAO,CAAC;MAE9D;MAAA;MAAA1B,cAAA,GAAAG,CAAA;MACA,IAAI6B,WAAW,CAACvB,aAAa,EAAE;QAAA;QAAAT,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QAC7B,MAAMI,cAAA,CAAA8B,YAAY,CAACuB,GAAG,CAAC3B,QAAQ,EAAEuB,MAAM,EAAExB,WAAW,CAACtB,SAAS,EAAEsB,WAAW,CAAC6B,QAAQ,CAAC;MACvF,CAAC;MAAA;MAAA;QAAA7D,cAAA,GAAA2B,CAAA;MAAA;MAED;MAAA3B,cAAA,GAAAG,CAAA;MACA,IAAI,CAACoC,aAAa,CAAC,WAAW,EAAET,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,EAAE2B,MAAM,CAACf,MAAM,EAAEe,MAAM,CAACf,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;MAAC;MAAAzC,cAAA,GAAAG,CAAA;MAEnG,OAAOqD,MAAM;IACf,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAG,CAAA;MACdG,QAAA,CAAAyD,MAAM,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC;MAAA9D,cAAA,GAAAG,CAAA;MACvD,MAAM2D,KAAK;IACb;EACF;EAEA;;;EAGQnB,mBAAmBA,CACzBnB,KAAe,EACfC,MAAsB,EACtBC,OAA0B,EAC1BE,MAA+B;IAAA;IAAA5B,cAAA,GAAAoB,CAAA;IAE/B,IAAIsB,KAAK;IAAA;IAAA,CAAA1C,cAAA,GAAAG,CAAA,QAAGqB,KAAK,CAACgD,IAAI,CAAC/C,MAAM,CAAC;IAE9B;IAAA;IAAAzB,cAAA,GAAAG,CAAA;IACA,IAAIyB,MAAM,CAACjB,gBAAgB,EAAE;MAAA;MAAAX,cAAA,GAAA2B,CAAA;MAC3B,MAAMsB,IAAI;MAAA;MAAA,CAAAjD,cAAA,GAAAG,CAAA,QAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC;MAAE;MAAA,CAAAnD,cAAA,GAAA2B,CAAA,WAAAD,OAAO,CAACuB,IAAI;MAAA;MAAA,CAAAjD,cAAA,GAAA2B,CAAA,WAAI,CAAC,EAAC;MAC3C,MAAMyB,KAAK;MAAA;MAAA,CAAApD,cAAA,GAAAG,CAAA,QAAG+C,IAAI,CAACG,GAAG,CAACzB,MAAM,CAACf,QAAQ,EAAEqC,IAAI,CAACC,GAAG,CAAC,CAAC;MAAE;MAAA,CAAAnD,cAAA,GAAA2B,CAAA,WAAAD,OAAO,CAAC0B,KAAK;MAAA;MAAA,CAAApD,cAAA,GAAA2B,CAAA,WAAIC,MAAM,CAAChB,YAAY,EAAC,CAAC;MAC1F,MAAM6D,IAAI;MAAA;MAAA,CAAAzE,cAAA,GAAAG,CAAA,QAAG,CAAC8C,IAAI,GAAG,CAAC,IAAIG,KAAK;MAAC;MAAApD,cAAA,GAAAG,CAAA;MAEhCuC,KAAK,GAAGA,KAAK,CAAC+B,IAAI,CAACA,IAAI,CAAC,CAACrB,KAAK,CAACA,KAAK,CAAC;IACvC,CAAC;IAAA;IAAA;MAAApD,cAAA,GAAA2B,CAAA;IAAA;IAED;IAAA3B,cAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAC,MAAM,CAACZ,aAAa;IAAA;IAAA,CAAAhB,cAAA,GAAA2B,CAAA,WAAID,OAAO,CAACgD,IAAI,GAAE;MAAA;MAAA1E,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MACxCuC,KAAK,GAAGA,KAAK,CAACgC,IAAI,CAAChD,OAAO,CAACgD,IAAI,CAAC;IAClC,CAAC;IAAA;IAAA;MAAA1E,cAAA,GAAA2B,CAAA;IAAA;IAED;IAAA3B,cAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAC,MAAM,CAACd,gBAAgB;IAAA;IAAA,CAAAd,cAAA,GAAA2B,CAAA,WAAID,OAAO,CAACwC,MAAM,GAAE;MAAA;MAAAlE,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MAC7CuC,KAAK,GAAGA,KAAK,CAACwB,MAAM,CAACxC,OAAO,CAACwC,MAAM,CAAC;IACtC,CAAC;IAAA;IAAA;MAAAlE,cAAA,GAAA2B,CAAA;IAAA;IAED;IAAA3B,cAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,cAAA,GAAA2B,CAAA,WAAAC,MAAM,CAACb,gBAAgB;IAAA;IAAA,CAAAf,cAAA,GAAA2B,CAAA,WAAID,OAAO,CAACyC,QAAQ,GAAE;MAAA;MAAAnE,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MAC/CuC,KAAK,GAAGA,KAAK,CAACyB,QAAQ,CAACzC,OAAO,CAACyC,QAAQ,CAAC;IAC1C,CAAC;IAAA;IAAA;MAAAnE,cAAA,GAAA2B,CAAA;IAAA;IAED;IAAA3B,cAAA,GAAAG,CAAA;IACAuC,KAAK,GAAGA,KAAK,CAACiC,IAAI,EAAE;IAAC;IAAA3E,cAAA,GAAAG,CAAA;IAErB,OAAOuC,KAAK;EACd;EAEA;;;EAGQR,gBAAgBA,CACtB0C,SAAiB,EACjBzC,SAAiB,EACjBV,MAAW,EACXC,OAAY;IAAA;IAAA1B,cAAA,GAAAoB,CAAA;IAEZ,MAAMyD,OAAO;IAAA;IAAA,CAAA7E,cAAA,GAAAG,CAAA,QAAG;MACdyE,SAAS;MACTpD,KAAK,EAAEW,SAAS;MAChBV,MAAM,EAAEqD,IAAI,CAACC,SAAS,CAACtD,MAAM,CAAC;MAC9BC,OAAO,EAAEoD,IAAI,CAACC,SAAS,CAACrD,OAAO;KAChC;IAAC;IAAA1B,cAAA,GAAAG,CAAA;IAEF,OAAO,SAAS6E,MAAM,CAACC,IAAI,CAACH,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,CAAC,CAACK,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC3E;EAEA;;;EAGQ3C,aAAaA,CACnB4C,SAAiB,EACjBC,aAAqB,EACrBC,iBAAyB,EACzBC,iBAAyB,EACzBC,SAAkB,EAClBC,QAAiB;IAAA;IAAAxF,cAAA,GAAAoB,CAAA;IAEjB,MAAMqE,MAAM;IAAA;IAAA,CAAAzF,cAAA,GAAAG,CAAA,QAAiB;MAC3BgF,SAAS;MACTC,aAAa;MACbC,iBAAiB;MACjBC,iBAAiB;MACjBC,SAAS;MACTC,QAAQ;MACRE,SAAS,EAAE,IAAI5D,IAAI;KACpB;IAAC;IAAA9B,cAAA,GAAAG,CAAA;IAEF,IAAI,CAACkB,OAAO,CAACsE,IAAI,CAACF,MAAM,CAAC;IAEzB;IAAA;IAAAzF,cAAA,GAAAG,CAAA;IACA,IAAI,IAAI,CAACkB,OAAO,CAACoB,MAAM,GAAG,IAAI,CAACnB,iBAAiB,EAAE;MAAA;MAAAtB,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MAChD,IAAI,CAACkB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuE,KAAK,CAAC,CAAC,IAAI,CAACtE,iBAAiB,CAAC;IAC5D,CAAC;IAAA;IAAA;MAAAtB,cAAA,GAAA2B,CAAA;IAAA;IAED;IAAA3B,cAAA,GAAAG,CAAA;IACA,IAAIiF,aAAa,GAAG,IAAI,EAAE;MAAA;MAAApF,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MAAE;MAC1BG,QAAA,CAAAyD,MAAM,CAAC8B,IAAI,CAAC,qBAAqB,EAAE;QACjCV,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,iBAAiB;QACjBC,SAAS;QACTC;OACD,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAxF,cAAA,GAAA2B,CAAA;IAAA;EACH;EAEA;;;EAGAmE,aAAaA,CAAA;IAAA;IAAA9F,cAAA,GAAAoB,CAAA;IAAApB,cAAA,GAAAG,CAAA;IACX,IAAI,IAAI,CAACkB,OAAO,CAACoB,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAzC,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAG,CAAA;MAC7B,OAAO;QAAE4F,YAAY,EAAE;MAAC,CAAE;IAC5B,CAAC;IAAA;IAAA;MAAA/F,cAAA,GAAA2B,CAAA;IAAA;IAED,MAAMoE,YAAY;IAAA;IAAA,CAAA/F,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACkB,OAAO,CAACoB,MAAM;IACxC,MAAMuD,kBAAkB;IAAA;IAAA,CAAAhG,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACkB,OAAO,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAK;MAAA;MAAAnG,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAG,CAAA;MAAA,OAAA+F,GAAG,GAAGC,CAAC,CAACf,aAAa;IAAb,CAAa,EAAE,CAAC,CAAC;IACpF,MAAMgB,oBAAoB;IAAA;IAAA,CAAApG,cAAA,GAAAG,CAAA,QAAG6F,kBAAkB,GAAGD,YAAY;IAC9D,MAAMM,YAAY;IAAA;IAAA,CAAArG,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACkB,OAAO,CAACI,MAAM,CAAC0E,CAAC,IAAI;MAAA;MAAAnG,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAG,CAAA;MAAA,OAAAgG,CAAC,CAACX,QAAQ;IAAR,CAAQ,CAAC,CAAC/C,MAAM,GAAGsD,YAAY;IAC/E,MAAMO,WAAW;IAAA;IAAA,CAAAtG,cAAA,GAAAG,CAAA,QAAG,IAAI,CAACkB,OAAO,CAACI,MAAM,CAAC0E,CAAC,IAAI;MAAA;MAAAnG,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAG,CAAA;MAAA,OAAAgG,CAAC,CAACf,aAAa,GAAG,IAAI;IAAJ,CAAI,CAAC,CAAC3C,MAAM;IAE3E,MAAM8D,cAAc;IAAA;IAAA,CAAAvG,cAAA,GAAAG,CAAA,SAAG,IAAI,CAACkB,OAAO,CAAC4E,MAAM,CAAC,CAACO,KAAK,EAAEf,MAAM,KAAI;MAAA;MAAAzF,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAG,CAAA;MAC3D,IAAI,CAACqG,KAAK,CAACf,MAAM,CAACN,SAAS,CAAC,EAAE;QAAA;QAAAnF,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QAC5BqG,KAAK,CAACf,MAAM,CAACN,SAAS,CAAC,GAAG;UAAEsB,KAAK,EAAE,CAAC;UAAEC,SAAS,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAC,CAAE;MACpE,CAAC;MAAA;MAAA;QAAA3G,cAAA,GAAA2B,CAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MACDqG,KAAK,CAACf,MAAM,CAACN,SAAS,CAAC,CAACsB,KAAK,EAAE;MAAC;MAAAzG,cAAA,GAAAG,CAAA;MAChCqG,KAAK,CAACf,MAAM,CAACN,SAAS,CAAC,CAACuB,SAAS,IAAIjB,MAAM,CAACL,aAAa;MAAC;MAAApF,cAAA,GAAAG,CAAA;MAC1D,IAAIsF,MAAM,CAACD,QAAQ,EAAE;QAAA;QAAAxF,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAG,CAAA;QACnBqG,KAAK,CAACf,MAAM,CAACN,SAAS,CAAC,CAACwB,SAAS,EAAE;MACrC,CAAC;MAAA;MAAA;QAAA3G,cAAA,GAAA2B,CAAA;MAAA;MAAA3B,cAAA,GAAAG,CAAA;MACD,OAAOqG,KAAK;IACd,CAAC,EAAE,EAAS,CAAC;IAAC;IAAAxG,cAAA,GAAAG,CAAA;IAEd,OAAO;MACL4F,YAAY;MACZK,oBAAoB,EAAElD,IAAI,CAAC0D,KAAK,CAACR,oBAAoB,CAAC;MACtDC,YAAY,EAAEnD,IAAI,CAAC0D,KAAK,CAACP,YAAY,GAAG,GAAG,CAAC;MAC5CC,WAAW;MACXC,cAAc;MACdM,aAAa,EAAE,IAAI,CAACxF,OAAO,CAACuE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KACxC;EACH;EAEA;;;EAGAkB,YAAYA,CAAA;IAAA;IAAA9G,cAAA,GAAAoB,CAAA;IAAApB,cAAA,GAAAG,CAAA;IACV,IAAI,CAACkB,OAAO,GAAG,EAAE;EACnB;EAEA;;;EAGA,MAAM0F,oBAAoBA,CAAC5E,SAAiB;IAAA;IAAAnC,cAAA,GAAAoB,CAAA;IAAApB,cAAA,GAAAG,CAAA;IAC1C,MAAMI,cAAA,CAAAyG,YAAY,CAACC,iBAAiB,CAAC,IAAI9E,SAAS,GAAG,CAAC;IAAC;IAAAnC,cAAA,GAAAG,CAAA;IACvDG,QAAA,CAAAyD,MAAM,CAACmD,IAAI,CAAC,gCAAgC/E,SAAS,EAAE,CAAC;EAC1D;;AAGF;AAAA;AAAAnC,cAAA,GAAAG,CAAA;AACagH,OAAA,CAAAC,cAAc,GAAG,IAAIlG,cAAc,EAAE;AAElD;AAAA;AAAAlB,cAAA,GAAAG,CAAA;AACagH,OAAA,CAAAE,YAAY,GAAG;EAC1B;;;EAGA,MAAMC,YAAYA,CAACC,MAAc,EAAErD,MAAe;IAAA;IAAAlE,cAAA,GAAAoB,CAAA;IAChD,MAAMoG,IAAI;IAAA;IAAA,CAAAxH,cAAA,GAAAG,CAAA,SAAGD,UAAA,CAAAuH,OAAQ,CAACjG,KAAK,CAAC,MAAM,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IACpC,OAAO,MAAMgH,OAAA,CAAAC,cAAc,CAACpD,gBAAgB,CAC1CwD,IAAI,EACJ;MAAEE,GAAG,EAAEH;IAAM,CAAE,EACf;MAAErD;IAAM,CAAE,EACV;MAAExD,SAAS,EAAE,MAAM;MAAEmD,QAAQ,EAAE,EAAE,GAAG;IAAE,CAAE,CAAC;KAC1C;EACH,CAAC;EAED;;;EAGA,MAAM8D,gBAAgBA,CACpBlG,MAAW,EACXC,OAAA;EAAA;EAAA,CAAA1B,cAAA,GAAA2B,CAAA,WAA6B,EAAE;IAAA;IAAA3B,cAAA,GAAAoB,CAAA;IAE/B,MAAMwG,QAAQ;IAAA;IAAA,CAAA5H,cAAA,GAAAG,CAAA,SAAGD,UAAA,CAAAuH,OAAQ,CAACjG,KAAK,CAAC,UAAU,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IAC5C,OAAO,MAAMgH,OAAA,CAAAC,cAAc,CAAC7F,aAAa,CACvCqG,QAAQ,EACRnG,MAAM,EACNC,OAAO,EACP;MAAEhB,SAAS,EAAE,QAAQ;MAAEmD,QAAQ,EAAE,CAAC,GAAG;IAAE,CAAE,CAAC;KAC3C;EACH,CAAC;EAED;;;EAGA,MAAMgE,gBAAgBA,CAACC,UAAkB,EAAE3D,QAAiB;IAAA;IAAAnE,cAAA,GAAAoB,CAAA;IAC1D,MAAMwG,QAAQ;IAAA;IAAA,CAAA5H,cAAA,GAAAG,CAAA,SAAGD,UAAA,CAAAuH,OAAQ,CAACjG,KAAK,CAAC,UAAU,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IAC5C,OAAO,MAAMgH,OAAA,CAAAC,cAAc,CAACpD,gBAAgB,CAC1C4D,QAAQ,EACR;MAAEF,GAAG,EAAEI;IAAU,CAAE,EACnB;MAAE3D;IAAQ,CAAE,EACZ;MAAEzD,SAAS,EAAE,UAAU;MAAEmD,QAAQ,EAAE,EAAE,GAAG;IAAE,CAAE,CAAC;KAC9C;EACH,CAAC;EAED;;;EAGA,MAAMkE,oBAAoBA,CACxBR,MAAc,EACd7F,OAAA;EAAA;EAAA,CAAA1B,cAAA,GAAA2B,CAAA,WAA6B,EAAE;IAAA;IAAA3B,cAAA,GAAAoB,CAAA;IAE/B,MAAM4G,YAAY;IAAA;IAAA,CAAAhI,cAAA,GAAAG,CAAA,SAAGD,UAAA,CAAAuH,OAAQ,CAACjG,KAAK,CAAC,cAAc,CAAC;IAAC;IAAAxB,cAAA,GAAAG,CAAA;IACpD,OAAO,MAAMgH,OAAA,CAAAC,cAAc,CAAC7F,aAAa,CACvCyG,YAAY,EACZ;MAAET,MAAM;MAAEU,SAAS,EAAE;IAAK,CAAE,EAC5B;MAAE,GAAGvG,OAAO;MAAEgD,IAAI,EAAE;QAAEwD,SAAS,EAAE,CAAC;MAAC;IAAE,CAAE,EACvC;MAAExH,SAAS,EAAE,cAAc;MAAEmD,QAAQ,EAAE,CAAC,GAAG;IAAE,CAAE,CAAC;KACjD;EACH;CACD;AAAC;AAAA7D,cAAA,GAAAG,CAAA;AAEFgH,OAAA,CAAAM,OAAA,GAAeN,OAAA,CAAAC,cAAc", "ignoreList": []}