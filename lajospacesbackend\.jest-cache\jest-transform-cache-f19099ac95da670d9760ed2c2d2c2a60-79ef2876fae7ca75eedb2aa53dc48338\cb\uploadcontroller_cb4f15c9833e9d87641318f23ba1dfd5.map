{"version": 3, "names": ["cov_2pcmws8w0z", "actualCoverage", "s", "mongoose_1", "require", "catchAsync_1", "appError_1", "logger_1", "environment_1", "upload_1", "imageOptimizationService_1", "cloudinaryService_1", "exports", "uploadSingleImage", "catchAsync", "req", "res", "f", "userId", "user", "_id", "file", "b", "AppError", "validateUploadedFile", "isSecure", "performSecurityCheck", "<PERSON><PERSON><PERSON><PERSON>", "validateImageIntegrity", "buffer", "optimizedBuffer", "compressForWeb", "uploadResult", "uploadImage", "folder", "tags", "toString", "metadata", "extractImageMetadata", "logger", "info", "publicId", "public_id", "originalSize", "size", "optimizedSize", "length", "status", "json", "success", "data", "upload", "sizes", "generateImageSizes", "message", "uploadAvatar", "optimizeImage", "width", "height", "crop", "quality", "format", "removeMetadata", "uploadProfilePhoto", "avatar", "uploadPropertyPhotos", "files", "propertyId", "body", "validateUploadedFiles", "originalname", "uploadPromises", "map", "index", "sharpen", "uploadPropertyPhoto", "originalName", "error", "results", "Promise", "all", "successful", "filter", "result", "failed", "total", "uploads", "undefined", "summary", "uploadMessageAttachment", "conversationId", "Types", "ObjectId", "processedBuffer", "mimetype", "startsWith", "extractFileMetadata", "fileType", "attachment", "bulkUploadImages", "processedFiles", "options", "uploadResults", "deleteUploadedImage", "params", "deleteImage", "generateUploadUrl", "uploadUrl", "generateSignedUploadUrl", "url", "signature", "timestamp", "cloudName", "config", "CLOUDINARY_CLOUD_NAME", "<PERSON><PERSON><PERSON><PERSON>", "CLOUDINARY_API_KEY"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts"], "sourcesContent": ["import { Request, Response } from 'express';\r\nimport { Types } from 'mongoose';\r\nimport { catchAsync } from '../utils/catchAsync';\r\nimport { AppError } from '../utils/appError';\r\nimport { logger } from '../utils/logger';\r\nimport { config } from '../config/environment';\r\nimport { \r\n  validateUploadedFile, \r\n  validateUploadedFiles, \r\n  extractFileMetadata,\r\n  performSecurityCheck \r\n} from '../middleware/upload';\r\nimport { \r\n  optimizeImage, \r\n  generateImageSizes, \r\n  compressForWeb,\r\n  extractImageMetadata,\r\n  validateImageIntegrity \r\n} from '../services/imageOptimizationService';\r\nimport {\r\n  uploadImage,\r\n  uploadProfilePhoto,\r\n  uploadPropertyPhoto,\r\n  uploadMessageAttachment,\r\n  bulkUploadImages as cloudinaryBulkUpload,\r\n  deleteImage,\r\n  generateImageSizes as generateCloudinarySizes,\r\n  generateSignedUploadUrl\r\n} from '../services/cloudinaryService';\r\n\r\n/**\r\n * Upload single image\r\n */\r\nexport const uploadSingleImage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const file = req.file;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!file) {\r\n    throw new AppError('No file uploaded', 400);\r\n  }\r\n\r\n  // Validate file\r\n  validateUploadedFile(file, 'image');\r\n\r\n  // Security check\r\n  const isSecure = await performSecurityCheck(file);\r\n  if (!isSecure) {\r\n    throw new AppError('File failed security validation', 400);\r\n  }\r\n\r\n  // Validate image integrity\r\n  const isValid = await validateImageIntegrity(file.buffer);\r\n  if (!isValid) {\r\n    throw new AppError('Invalid or corrupted image file', 400);\r\n  }\r\n\r\n  // Optimize image\r\n  const optimizedBuffer = await compressForWeb(file.buffer);\r\n\r\n  // Upload to Cloudinary\r\n  const uploadResult = await uploadImage(optimizedBuffer, {\r\n    folder: 'lajospaces/uploads',\r\n    tags: ['upload', userId.toString()]\r\n  });\r\n\r\n  // Extract metadata\r\n  const metadata = await extractImageMetadata(optimizedBuffer);\r\n\r\n  logger.info('Single image uploaded successfully', {\r\n    userId,\r\n    publicId: uploadResult.public_id,\r\n    originalSize: file.size,\r\n    optimizedSize: optimizedBuffer.length\r\n  });\r\n\r\n  return res.status(201).json({\r\n    success: true,\r\n    data: {\r\n      upload: uploadResult,\r\n      metadata,\r\n      sizes: generateCloudinarySizes(uploadResult.public_id)\r\n    },\r\n    message: 'Image uploaded successfully'\r\n  });\r\n});\r\n\r\n/**\r\n * Upload profile avatar\r\n */\r\nexport const uploadAvatar = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const file = req.file;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!file) {\r\n    throw new AppError('No avatar file uploaded', 400);\r\n  }\r\n\r\n  // Validate file\r\n  validateUploadedFile(file, 'avatar');\r\n\r\n  // Security check\r\n  const isSecure = await performSecurityCheck(file);\r\n  if (!isSecure) {\r\n    throw new AppError('Avatar file failed security validation', 400);\r\n  }\r\n\r\n  // Optimize for avatar (square crop, face detection)\r\n  const optimizedBuffer = await optimizeImage(file.buffer, {\r\n    width: 400,\r\n    height: 400,\r\n    crop: 'cover',\r\n    quality: 85,\r\n    format: 'auto',\r\n    removeMetadata: true\r\n  });\r\n\r\n  // Upload to Cloudinary with avatar-specific settings\r\n  const uploadResult = await uploadProfilePhoto(optimizedBuffer, userId.toString(), true);\r\n\r\n  logger.info('Avatar uploaded successfully', {\r\n    userId,\r\n    publicId: uploadResult.public_id\r\n  });\r\n\r\n  return res.status(201).json({\r\n    success: true,\r\n    data: {\r\n      avatar: uploadResult,\r\n      sizes: generateCloudinarySizes(uploadResult.public_id)\r\n    },\r\n    message: 'Avatar uploaded successfully'\r\n  });\r\n});\r\n\r\n/**\r\n * Upload property photos\r\n */\r\nexport const uploadPropertyPhotos = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const files = req.files as Express.Multer.File[];\r\n  const { propertyId } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!files || files.length === 0) {\r\n    throw new AppError('No property photos uploaded', 400);\r\n  }\r\n\r\n  // Validate files\r\n  validateUploadedFiles(files, 'property');\r\n\r\n  // Security check for all files\r\n  for (const file of files) {\r\n    const isSecure = await performSecurityCheck(file);\r\n    if (!isSecure) {\r\n      throw new AppError(`File ${file.originalname} failed security validation`, 400);\r\n    }\r\n  }\r\n\r\n  // Process and upload images\r\n  const uploadPromises = files.map(async (file, index) => {\r\n    try {\r\n      // Optimize for property photos\r\n      const optimizedBuffer = await optimizeImage(file.buffer, {\r\n        width: 1200,\r\n        height: 800,\r\n        crop: 'inside',\r\n        quality: 85,\r\n        format: 'auto',\r\n        removeMetadata: true,\r\n        sharpen: true\r\n      });\r\n\r\n      // Upload to Cloudinary\r\n      const uploadResult = await uploadPropertyPhoto(\r\n        optimizedBuffer, \r\n        userId.toString(), \r\n        propertyId\r\n      );\r\n\r\n      return {\r\n        index,\r\n        originalName: file.originalname,\r\n        upload: uploadResult,\r\n        sizes: generateCloudinarySizes(uploadResult.public_id)\r\n      };\r\n    } catch (error) {\r\n      logger.error(`Error uploading property photo ${index}:`, error);\r\n      return {\r\n        index,\r\n        originalName: file.originalname,\r\n        error: (error as Error).message\r\n      };\r\n    }\r\n  });\r\n\r\n  const results = await Promise.all(uploadPromises);\r\n  const successful = results.filter(result => !result.error);\r\n  const failed = results.filter(result => result.error);\r\n\r\n  logger.info('Property photos upload completed', {\r\n    userId,\r\n    propertyId,\r\n    total: files.length,\r\n    successful: successful.length,\r\n    failed: failed.length\r\n  });\r\n\r\n  return res.status(201).json({\r\n    success: true,\r\n    data: {\r\n      uploads: successful,\r\n      failed: failed.length > 0 ? failed : undefined,\r\n      summary: {\r\n        total: files.length,\r\n        successful: successful.length,\r\n        failed: failed.length\r\n      }\r\n    },\r\n    message: `${successful.length} of ${files.length} property photos uploaded successfully`\r\n  });\r\n});\r\n\r\n/**\r\n * Upload message attachment\r\n */\r\nexport const uploadMessageAttachment = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const file = req.file;\r\n  const { conversationId } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!file) {\r\n    throw new AppError('No attachment uploaded', 400);\r\n  }\r\n\r\n  if (!conversationId) {\r\n    throw new AppError('Conversation ID is required', 400);\r\n  }\r\n\r\n  // Validate conversation ID\r\n  if (!Types.ObjectId.isValid(conversationId)) {\r\n    throw new AppError('Invalid conversation ID', 400);\r\n  }\r\n\r\n  // Security check\r\n  const isSecure = await performSecurityCheck(file);\r\n  if (!isSecure) {\r\n    throw new AppError('File failed security validation', 400);\r\n  }\r\n\r\n  let processedBuffer = file.buffer;\r\n\r\n  // Optimize if it's an image\r\n  if (file.mimetype.startsWith('image/')) {\r\n    processedBuffer = await optimizeImage(file.buffer, {\r\n      width: 800,\r\n      height: 600,\r\n      crop: 'inside',\r\n      quality: 80,\r\n      format: 'auto',\r\n      removeMetadata: true\r\n    });\r\n  }\r\n\r\n  // Upload to Cloudinary\r\n  const uploadResult = await uploadMessageAttachment(\r\n    processedBuffer,\r\n    file.mimetype,\r\n    userId.toString(),\r\n    conversationId\r\n  );\r\n\r\n  const metadata = extractFileMetadata(file);\r\n\r\n  logger.info('Message attachment uploaded successfully', {\r\n    userId,\r\n    conversationId,\r\n    publicId: uploadResult.public_id,\r\n    fileType: file.mimetype\r\n  });\r\n\r\n  return res.status(201).json({\r\n    success: true,\r\n    data: {\r\n      attachment: uploadResult,\r\n      metadata,\r\n      sizes: file.mimetype.startsWith('image/') ? \r\n        generateCloudinarySizes(uploadResult.public_id) : undefined\r\n    },\r\n    message: 'Attachment uploaded successfully'\r\n  });\r\n});\r\n\r\n/**\r\n * Bulk upload images\r\n */\r\nexport const bulkUploadImages = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const files = req.files as Express.Multer.File[];\r\n  const { folder = 'lajospaces/bulk' } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!files || files.length === 0) {\r\n    throw new AppError('No files uploaded', 400);\r\n  }\r\n\r\n  if (files.length > 20) {\r\n    throw new AppError('Maximum 20 files allowed for bulk upload', 400);\r\n  }\r\n\r\n  // Validate all files\r\n  validateUploadedFiles(files, 'image');\r\n\r\n  // Process files for bulk upload\r\n  const processedFiles = await Promise.all(\r\n    files.map(async (file) => {\r\n      // Security check\r\n      const isSecure = await performSecurityCheck(file);\r\n      if (!isSecure) {\r\n        throw new AppError(`File ${file.originalname} failed security validation`, 400);\r\n      }\r\n\r\n      // Optimize image\r\n      const optimizedBuffer = await compressForWeb(file.buffer);\r\n\r\n      return {\r\n        buffer: optimizedBuffer,\r\n        options: {\r\n          tags: ['bulk', userId.toString(), 'upload']\r\n        }\r\n      };\r\n    })\r\n  );\r\n\r\n  // Bulk upload to Cloudinary\r\n  const uploadResults = await cloudinaryBulkUpload(processedFiles, folder);\r\n\r\n  logger.info('Bulk upload completed', {\r\n    userId,\r\n    folder,\r\n    total: files.length,\r\n    successful: uploadResults.length\r\n  });\r\n\r\n  return res.status(201).json({\r\n    success: true,\r\n    data: {\r\n      uploads: uploadResults.map(result => ({\r\n        upload: result,\r\n        sizes: generateCloudinarySizes(result.public_id)\r\n      })),\r\n      summary: {\r\n        total: files.length,\r\n        successful: uploadResults.length,\r\n        failed: files.length - uploadResults.length\r\n      }\r\n    },\r\n    message: `${uploadResults.length} of ${files.length} images uploaded successfully`\r\n  });\r\n});\r\n\r\n/**\r\n * Delete uploaded image\r\n */\r\nexport const deleteUploadedImage = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { publicId } = req.params;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  if (!publicId) {\r\n    throw new AppError('Public ID is required', 400);\r\n  }\r\n\r\n  // Delete from Cloudinary\r\n  await deleteImage(publicId);\r\n\r\n  logger.info('Image deleted successfully', {\r\n    userId,\r\n    publicId\r\n  });\r\n\r\n  return res.json({\r\n    success: true,\r\n    message: 'Image deleted successfully'\r\n  });\r\n});\r\n\r\n/**\r\n * Generate signed upload URL for direct client uploads\r\n */\r\nexport const generateUploadUrl = catchAsync(async (req: Request, res: Response) => {\r\n  const userId = req.user?._id;\r\n  const { folder = 'lajospaces/direct', tags = [] } = req.body;\r\n\r\n  if (!userId) {\r\n    throw new AppError('User not authenticated', 401);\r\n  }\r\n\r\n  const uploadUrl = generateSignedUploadUrl(\r\n    folder,\r\n    [...tags, userId.toString()]\r\n  );\r\n\r\n  return res.json({\r\n    success: true,\r\n    data: {\r\n      uploadUrl: uploadUrl.url,\r\n      signature: uploadUrl.signature,\r\n      timestamp: uploadUrl.timestamp,\r\n      cloudName: config.CLOUDINARY_CLOUD_NAME,\r\n      apiKey: config.CLOUDINARY_API_KEY\r\n    },\r\n    message: 'Signed upload URL generated successfully'\r\n  });\r\n});\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCQ;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAjCR,MAAAC,UAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,YAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,UAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAG,QAAA;AAAA;AAAA,CAAAP,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAI,aAAA;AAAA;AAAA,CAAAR,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAK,QAAA;AAAA;AAAA,CAAAT,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAMA,MAAAM,0BAAA;AAAA;AAAA,CAAAV,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAOA,MAAAO,mBAAA;AAAA;AAAA,CAAAX,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAWA;;;AAAA;AAAAJ,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAAC,iBAAiB,GAAG,IAAAR,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAChF,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAMC,IAAI;EAAA;EAAA,CAAArB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACM,IAAI;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAEtB,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAI,CAACmB,IAAI,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACT,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC;EAC7C,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,IAAAO,QAAA,CAAAe,oBAAoB,EAACH,IAAI,EAAE,OAAO,CAAC;EAEnC;EACA,MAAMI,QAAQ;EAAA;EAAA,CAAAzB,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAO,QAAA,CAAAiB,oBAAoB,EAACL,IAAI,CAAC;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAClD,IAAI,CAACuB,QAAQ,EAAE;IAAA;IAAAzB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC;EAC5D,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMK,OAAO;EAAA;EAAA,CAAA3B,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAQ,0BAAA,CAAAkB,sBAAsB,EAACP,IAAI,CAACQ,MAAM,CAAC;EAAC;EAAA7B,cAAA,GAAAE,CAAA;EAC1D,IAAI,CAACyB,OAAO,EAAE;IAAA;IAAA3B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACZ,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC;EAC5D,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMQ,eAAe;EAAA;EAAA,CAAA9B,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAQ,0BAAA,CAAAqB,cAAc,EAACV,IAAI,CAACQ,MAAM,CAAC;EAEzD;EACA,MAAMG,YAAY;EAAA;EAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAS,mBAAA,CAAAsB,WAAW,EAACH,eAAe,EAAE;IACtDI,MAAM,EAAE,oBAAoB;IAC5BC,IAAI,EAAE,CAAC,QAAQ,EAAEjB,MAAM,CAACkB,QAAQ,EAAE;GACnC,CAAC;EAEF;EACA,MAAMC,QAAQ;EAAA;EAAA,CAAArC,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAQ,0BAAA,CAAA4B,oBAAoB,EAACR,eAAe,CAAC;EAAC;EAAA9B,cAAA,GAAAE,CAAA;EAE7DK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,oCAAoC,EAAE;IAChDtB,MAAM;IACNuB,QAAQ,EAAET,YAAY,CAACU,SAAS;IAChCC,YAAY,EAAEtB,IAAI,CAACuB,IAAI;IACvBC,aAAa,EAAEf,eAAe,CAACgB;GAChC,CAAC;EAAC;EAAA9C,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJC,MAAM,EAAEnB,YAAY;MACpBK,QAAQ;MACRe,KAAK,EAAE,IAAAzC,mBAAA,CAAA0C,kBAAuB,EAACrB,YAAY,CAACU,SAAS;KACtD;IACDY,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAtD,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAA2C,YAAY,GAAG,IAAAlD,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAC3E,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAMC,IAAI;EAAA;EAAA,CAAArB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACM,IAAI;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAEtB,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAI,CAACmB,IAAI,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACT,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC;EACpD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,IAAAO,QAAA,CAAAe,oBAAoB,EAACH,IAAI,EAAE,QAAQ,CAAC;EAEpC;EACA,MAAMI,QAAQ;EAAA;EAAA,CAAAzB,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAO,QAAA,CAAAiB,oBAAoB,EAACL,IAAI,CAAC;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAClD,IAAI,CAACuB,QAAQ,EAAE;IAAA;IAAAzB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wCAAwC,EAAE,GAAG,CAAC;EACnE,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMQ,eAAe;EAAA;EAAA,CAAA9B,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAQ,0BAAA,CAAA8C,aAAa,EAACnC,IAAI,CAACQ,MAAM,EAAE;IACvD4B,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE;GACjB,CAAC;EAEF;EACA,MAAM9B,YAAY;EAAA;EAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAS,mBAAA,CAAAoD,kBAAkB,EAACjC,eAAe,EAAEZ,MAAM,CAACkB,QAAQ,EAAE,EAAE,IAAI,CAAC;EAAC;EAAApC,cAAA,GAAAE,CAAA;EAExFK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAE;IAC1CtB,MAAM;IACNuB,QAAQ,EAAET,YAAY,CAACU;GACxB,CAAC;EAAC;EAAA1C,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJc,MAAM,EAAEhC,YAAY;MACpBoB,KAAK,EAAE,IAAAzC,mBAAA,CAAA0C,kBAAuB,EAACrB,YAAY,CAACU,SAAS;KACtD;IACDY,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAtD,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAAqD,oBAAoB,GAAG,IAAA5D,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EACnF,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAM8C,KAAK;EAAA;EAAA,CAAAlE,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACmD,KAA8B;EAChD,MAAM;IAAEC;EAAU,CAAE;EAAA;EAAA,CAAAnE,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACqD,IAAI;EAAC;EAAApE,cAAA,GAAAE,CAAA;EAEhC,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED;EAAI;EAAA,CAAAF,cAAA,GAAAsB,CAAA,WAAC4C,KAAK;EAAA;EAAA,CAAAlE,cAAA,GAAAsB,CAAA,UAAI4C,KAAK,CAACpB,MAAM,KAAK,CAAC,GAAE;IAAA;IAAA9C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IAChC,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC;EACxD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,IAAAO,QAAA,CAAA4D,qBAAqB,EAACH,KAAK,EAAE,UAAU,CAAC;EAExC;EAAA;EAAAlE,cAAA,GAAAE,CAAA;EACA,KAAK,MAAMmB,IAAI,IAAI6C,KAAK,EAAE;IACxB,MAAMzC,QAAQ;IAAA;IAAA,CAAAzB,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAO,QAAA,CAAAiB,oBAAoB,EAACL,IAAI,CAAC;IAAC;IAAArB,cAAA,GAAAE,CAAA;IAClD,IAAI,CAACuB,QAAQ,EAAE;MAAA;MAAAzB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAE,CAAA;MACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,QAAQF,IAAI,CAACiD,YAAY,6BAA6B,EAAE,GAAG,CAAC;IACjF,CAAC;IAAA;IAAA;MAAAtE,cAAA,GAAAsB,CAAA;IAAA;EACH;EAEA;EACA,MAAMiD,cAAc;EAAA;EAAA,CAAAvE,cAAA,GAAAE,CAAA,QAAGgE,KAAK,CAACM,GAAG,CAAC,OAAOnD,IAAI,EAAEoD,KAAK,KAAI;IAAA;IAAAzE,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAE,CAAA;IACrD,IAAI;MACF;MACA,MAAM4B,eAAe;MAAA;MAAA,CAAA9B,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAQ,0BAAA,CAAA8C,aAAa,EAACnC,IAAI,CAACQ,MAAM,EAAE;QACvD4B,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,GAAG;QACXC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,MAAM;QACdC,cAAc,EAAE,IAAI;QACpBY,OAAO,EAAE;OACV,CAAC;MAEF;MACA,MAAM1C,YAAY;MAAA;MAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAS,mBAAA,CAAAgE,mBAAmB,EAC5C7C,eAAe,EACfZ,MAAM,CAACkB,QAAQ,EAAE,EACjB+B,UAAU,CACX;MAAC;MAAAnE,cAAA,GAAAE,CAAA;MAEF,OAAO;QACLuE,KAAK;QACLG,YAAY,EAAEvD,IAAI,CAACiD,YAAY;QAC/BnB,MAAM,EAAEnB,YAAY;QACpBoB,KAAK,EAAE,IAAAzC,mBAAA,CAAA0C,kBAAuB,EAACrB,YAAY,CAACU,SAAS;OACtD;IACH,CAAC,CAAC,OAAOmC,KAAK,EAAE;MAAA;MAAA7E,cAAA,GAAAE,CAAA;MACdK,QAAA,CAAAgC,MAAM,CAACsC,KAAK,CAAC,kCAAkCJ,KAAK,GAAG,EAAEI,KAAK,CAAC;MAAC;MAAA7E,cAAA,GAAAE,CAAA;MAChE,OAAO;QACLuE,KAAK;QACLG,YAAY,EAAEvD,IAAI,CAACiD,YAAY;QAC/BO,KAAK,EAAGA,KAAe,CAACvB;OACzB;IACH;EACF,CAAC,CAAC;EAEF,MAAMwB,OAAO;EAAA;EAAA,CAAA9E,cAAA,GAAAE,CAAA,QAAG,MAAM6E,OAAO,CAACC,GAAG,CAACT,cAAc,CAAC;EACjD,MAAMU,UAAU;EAAA;EAAA,CAAAjF,cAAA,GAAAE,CAAA,QAAG4E,OAAO,CAACI,MAAM,CAACC,MAAM,IAAI;IAAA;IAAAnF,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAE,CAAA;IAAA,QAACiF,MAAM,CAACN,KAAK;EAAL,CAAK,CAAC;EAC1D,MAAMO,MAAM;EAAA;EAAA,CAAApF,cAAA,GAAAE,CAAA,QAAG4E,OAAO,CAACI,MAAM,CAACC,MAAM,IAAI;IAAA;IAAAnF,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAE,CAAA;IAAA,OAAAiF,MAAM,CAACN,KAAK;EAAL,CAAK,CAAC;EAAC;EAAA7E,cAAA,GAAAE,CAAA;EAEtDK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,kCAAkC,EAAE;IAC9CtB,MAAM;IACNiD,UAAU;IACVkB,KAAK,EAAEnB,KAAK,CAACpB,MAAM;IACnBmC,UAAU,EAAEA,UAAU,CAACnC,MAAM;IAC7BsC,MAAM,EAAEA,MAAM,CAACtC;GAChB,CAAC;EAAC;EAAA9C,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJoC,OAAO,EAAEL,UAAU;MACnBG,MAAM,EAAEA,MAAM,CAACtC,MAAM,GAAG,CAAC;MAAA;MAAA,CAAA9C,cAAA,GAAAsB,CAAA,WAAG8D,MAAM;MAAA;MAAA,CAAApF,cAAA,GAAAsB,CAAA,WAAGiE,SAAS;MAC9CC,OAAO,EAAE;QACPH,KAAK,EAAEnB,KAAK,CAACpB,MAAM;QACnBmC,UAAU,EAAEA,UAAU,CAACnC,MAAM;QAC7BsC,MAAM,EAAEA,MAAM,CAACtC;;KAElB;IACDQ,OAAO,EAAE,GAAG2B,UAAU,CAACnC,MAAM,OAAOoB,KAAK,CAACpB,MAAM;GACjD,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAA9C,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAA6E,uBAAuB,GAAG,IAAApF,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EACtF,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAMC,IAAI;EAAA;EAAA,CAAArB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACM,IAAI;EACrB,MAAM;IAAEqE;EAAc,CAAE;EAAA;EAAA,CAAA1F,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACqD,IAAI;EAAC;EAAApE,cAAA,GAAAE,CAAA;EAEpC,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAI,CAACmB,IAAI,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACT,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAI,CAACwF,cAAc,EAAE;IAAA;IAAA1F,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACnB,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC;EACxD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,IAAI,CAACC,UAAA,CAAAwF,KAAK,CAACC,QAAQ,CAACjE,OAAO,CAAC+D,cAAc,CAAC,EAAE;IAAA;IAAA1F,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IAC3C,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,yBAAyB,EAAE,GAAG,CAAC;EACpD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMG,QAAQ;EAAA;EAAA,CAAAzB,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAO,QAAA,CAAAiB,oBAAoB,EAACL,IAAI,CAAC;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAClD,IAAI,CAACuB,QAAQ,EAAE;IAAA;IAAAzB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC;EAC5D,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,IAAIuE,eAAe;EAAA;EAAA,CAAA7F,cAAA,GAAAE,CAAA,QAAGmB,IAAI,CAACQ,MAAM;EAEjC;EAAA;EAAA7B,cAAA,GAAAE,CAAA;EACA,IAAImB,IAAI,CAACyE,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;IAAA;IAAA/F,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACtC2F,eAAe,GAAG,MAAM,IAAAnF,0BAAA,CAAA8C,aAAa,EAACnC,IAAI,CAACQ,MAAM,EAAE;MACjD4B,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,MAAM;MACdC,cAAc,EAAE;KACjB,CAAC;EACJ,CAAC;EAAA;EAAA;IAAA9D,cAAA,GAAAsB,CAAA;EAAA;EAED;EACA,MAAMU,YAAY;EAAA;EAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAG,MAAM,IAAAU,OAAA,CAAA6E,uBAAuB,EAChDI,eAAe,EACfxE,IAAI,CAACyE,QAAQ,EACb5E,MAAM,CAACkB,QAAQ,EAAE,EACjBsD,cAAc,CACf;EAED,MAAMrD,QAAQ;EAAA;EAAA,CAAArC,cAAA,GAAAE,CAAA,QAAG,IAAAO,QAAA,CAAAuF,mBAAmB,EAAC3E,IAAI,CAAC;EAAC;EAAArB,cAAA,GAAAE,CAAA;EAE3CK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAE;IACtDtB,MAAM;IACNwE,cAAc;IACdjD,QAAQ,EAAET,YAAY,CAACU,SAAS;IAChCuD,QAAQ,EAAE5E,IAAI,CAACyE;GAChB,CAAC;EAAC;EAAA9F,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJgD,UAAU,EAAElE,YAAY;MACxBK,QAAQ;MACRe,KAAK,EAAE/B,IAAI,CAACyE,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;MAAA;MAAA,CAAA/F,cAAA,GAAAsB,CAAA,WACvC,IAAAX,mBAAA,CAAA0C,kBAAuB,EAACrB,YAAY,CAACU,SAAS,CAAC;MAAA;MAAA,CAAA1C,cAAA,GAAAsB,CAAA,WAAGiE,SAAS;KAC9D;IACDjC,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAtD,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAAuF,gBAAgB,GAAG,IAAA9F,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAC/E,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAM8C,KAAK;EAAA;EAAA,CAAAlE,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACmD,KAA8B;EAChD,MAAM;IAAEhC,MAAM;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,WAAG,iBAAiB;EAAA,CAAE;EAAA;EAAA,CAAAtB,cAAA,GAAAE,CAAA,QAAGa,GAAG,CAACqD,IAAI;EAAC;EAAApE,cAAA,GAAAE,CAAA;EAEhD,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED;EAAI;EAAA,CAAAF,cAAA,GAAAsB,CAAA,YAAC4C,KAAK;EAAA;EAAA,CAAAlE,cAAA,GAAAsB,CAAA,WAAI4C,KAAK,CAACpB,MAAM,KAAK,CAAC,GAAE;IAAA;IAAA9C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IAChC,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC;EAC9C,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAIgE,KAAK,CAACpB,MAAM,GAAG,EAAE,EAAE;IAAA;IAAA9C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACrB,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,0CAA0C,EAAE,GAAG,CAAC;EACrE,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,IAAAO,QAAA,CAAA4D,qBAAqB,EAACH,KAAK,EAAE,OAAO,CAAC;EAErC;EACA,MAAMkC,cAAc;EAAA;EAAA,CAAApG,cAAA,GAAAE,CAAA,SAAG,MAAM6E,OAAO,CAACC,GAAG,CACtCd,KAAK,CAACM,GAAG,CAAC,MAAOnD,IAAI,IAAI;IAAA;IAAArB,cAAA,GAAAiB,CAAA;IACvB;IACA,MAAMQ,QAAQ;IAAA;IAAA,CAAAzB,cAAA,GAAAE,CAAA,SAAG,MAAM,IAAAO,QAAA,CAAAiB,oBAAoB,EAACL,IAAI,CAAC;IAAC;IAAArB,cAAA,GAAAE,CAAA;IAClD,IAAI,CAACuB,QAAQ,EAAE;MAAA;MAAAzB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAE,CAAA;MACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,QAAQF,IAAI,CAACiD,YAAY,6BAA6B,EAAE,GAAG,CAAC;IACjF,CAAC;IAAA;IAAA;MAAAtE,cAAA,GAAAsB,CAAA;IAAA;IAED;IACA,MAAMQ,eAAe;IAAA;IAAA,CAAA9B,cAAA,GAAAE,CAAA,SAAG,MAAM,IAAAQ,0BAAA,CAAAqB,cAAc,EAACV,IAAI,CAACQ,MAAM,CAAC;IAAC;IAAA7B,cAAA,GAAAE,CAAA;IAE1D,OAAO;MACL2B,MAAM,EAAEC,eAAe;MACvBuE,OAAO,EAAE;QACPlE,IAAI,EAAE,CAAC,MAAM,EAAEjB,MAAM,CAACkB,QAAQ,EAAE,EAAE,QAAQ;;KAE7C;EACH,CAAC,CAAC,CACH;EAED;EACA,MAAMkE,aAAa;EAAA;EAAA,CAAAtG,cAAA,GAAAE,CAAA,SAAG,MAAM,IAAAS,mBAAA,CAAAwF,gBAAoB,EAACC,cAAc,EAAElE,MAAM,CAAC;EAAC;EAAAlC,cAAA,GAAAE,CAAA;EAEzEK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,uBAAuB,EAAE;IACnCtB,MAAM;IACNgB,MAAM;IACNmD,KAAK,EAAEnB,KAAK,CAACpB,MAAM;IACnBmC,UAAU,EAAEqB,aAAa,CAACxD;GAC3B,CAAC;EAAC;EAAA9C,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAAC+B,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJoC,OAAO,EAAEgB,aAAa,CAAC9B,GAAG,CAACW,MAAM,IAAK;QAAA;QAAAnF,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAE,CAAA;QAAA;UACpCiD,MAAM,EAAEgC,MAAM;UACd/B,KAAK,EAAE,IAAAzC,mBAAA,CAAA0C,kBAAuB,EAAC8B,MAAM,CAACzC,SAAS;SAChD;OAAC,CAAC;MACH8C,OAAO,EAAE;QACPH,KAAK,EAAEnB,KAAK,CAACpB,MAAM;QACnBmC,UAAU,EAAEqB,aAAa,CAACxD,MAAM;QAChCsC,MAAM,EAAElB,KAAK,CAACpB,MAAM,GAAGwD,aAAa,CAACxD;;KAExC;IACDQ,OAAO,EAAE,GAAGgD,aAAa,CAACxD,MAAM,OAAOoB,KAAK,CAACpB,MAAM;GACpD,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAA9C,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAA2F,mBAAmB,GAAG,IAAAlG,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAClF,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,SAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAM;IAAEqB;EAAQ,CAAE;EAAA;EAAA,CAAAzC,cAAA,GAAAE,CAAA,SAAGa,GAAG,CAACyF,MAAM;EAAC;EAAAxG,cAAA,GAAAE,CAAA;EAEhC,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAE,CAAA;EAED,IAAI,CAACuC,QAAQ,EAAE;IAAA;IAAAzC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACb,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,uBAAuB,EAAE,GAAG,CAAC;EAClD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED;EAAAtB,cAAA,GAAAE,CAAA;EACA,MAAM,IAAAS,mBAAA,CAAA8F,WAAW,EAAChE,QAAQ,CAAC;EAAC;EAAAzC,cAAA,GAAAE,CAAA;EAE5BK,QAAA,CAAAgC,MAAM,CAACC,IAAI,CAAC,4BAA4B,EAAE;IACxCtB,MAAM;IACNuB;GACD,CAAC;EAAC;EAAAzC,cAAA,GAAAE,CAAA;EAEH,OAAOc,GAAG,CAACgC,IAAI,CAAC;IACdC,OAAO,EAAE,IAAI;IACbK,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC;AAEF;;;AAAA;AAAAtD,cAAA,GAAAE,CAAA;AAGaU,OAAA,CAAA8F,iBAAiB,GAAG,IAAArG,YAAA,CAAAS,UAAU,EAAC,OAAOC,GAAY,EAAEC,GAAa,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAChF,MAAMC,MAAM;EAAA;EAAA,CAAAlB,cAAA,GAAAE,CAAA,SAAGa,GAAG,CAACI,IAAI,EAAEC,GAAG;EAC5B,MAAM;IAAEc,MAAM;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,WAAG,mBAAmB;IAAEa,IAAI;IAAA;IAAA,CAAAnC,cAAA,GAAAsB,CAAA,WAAG,EAAE;EAAA,CAAE;EAAA;EAAA,CAAAtB,cAAA,GAAAE,CAAA,SAAGa,GAAG,CAACqD,IAAI;EAAC;EAAApE,cAAA,GAAAE,CAAA;EAE7D,IAAI,CAACgB,MAAM,EAAE;IAAA;IAAAlB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAE,CAAA;IACX,MAAM,IAAII,UAAA,CAAAiB,QAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC;EACnD,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAMqF,SAAS;EAAA;EAAA,CAAA3G,cAAA,GAAAE,CAAA,SAAG,IAAAS,mBAAA,CAAAiG,uBAAuB,EACvC1E,MAAM,EACN,CAAC,GAAGC,IAAI,EAAEjB,MAAM,CAACkB,QAAQ,EAAE,CAAC,CAC7B;EAAC;EAAApC,cAAA,GAAAE,CAAA;EAEF,OAAOc,GAAG,CAACgC,IAAI,CAAC;IACdC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE;MACJyD,SAAS,EAAEA,SAAS,CAACE,GAAG;MACxBC,SAAS,EAAEH,SAAS,CAACG,SAAS;MAC9BC,SAAS,EAAEJ,SAAS,CAACI,SAAS;MAC9BC,SAAS,EAAExG,aAAA,CAAAyG,MAAM,CAACC,qBAAqB;MACvCC,MAAM,EAAE3G,aAAA,CAAAyG,MAAM,CAACG;KAChB;IACD9D,OAAO,EAAE;GACV,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}