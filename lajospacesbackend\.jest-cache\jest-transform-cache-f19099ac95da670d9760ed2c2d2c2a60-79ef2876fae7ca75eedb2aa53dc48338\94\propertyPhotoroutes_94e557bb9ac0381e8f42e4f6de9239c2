c7f41ed12a75c27da57324a3d601c2d8
"use strict";

/* istanbul ignore next */
function cov_q3ecutjxq() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyPhoto.routes.ts";
  var hash = "135333a4faf9fe1a566f9e4bf35dbd04c6cbf508";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyPhoto.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 18
        },
        end: {
          line: 6,
          column: 36
        }
      },
      "4": {
        start: {
          line: 7,
          column: 17
        },
        end: {
          line: 7,
          column: 51
        }
      },
      "5": {
        start: {
          line: 8,
          column: 35
        },
        end: {
          line: 8,
          column: 85
        }
      },
      "6": {
        start: {
          line: 9,
          column: 15
        },
        end: {
          line: 9,
          column: 44
        }
      },
      "7": {
        start: {
          line: 10,
          column: 21
        },
        end: {
          line: 10,
          column: 56
        }
      },
      "8": {
        start: {
          line: 11,
          column: 30
        },
        end: {
          line: 11,
          column: 74
        }
      },
      "9": {
        start: {
          line: 12,
          column: 15
        },
        end: {
          line: 12,
          column: 38
        }
      },
      "10": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 30,
          column: 2
        }
      },
      "11": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 70
        }
      },
      "12": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 28,
          column: 9
        }
      },
      "13": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 24,
          column: 27
        }
      },
      "14": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 86
        }
      },
      "15": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 32,
          column: 73
        }
      },
      "16": {
        start: {
          line: 34,
          column: 0
        },
        end: {
          line: 34,
          column: 32
        }
      },
      "17": {
        start: {
          line: 36,
          column: 0
        },
        end: {
          line: 36,
          column: 114
        }
      },
      "18": {
        start: {
          line: 37,
          column: 0
        },
        end: {
          line: 42,
          column: 53
        }
      },
      "19": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 128
        }
      },
      "20": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 44,
          column: 131
        }
      },
      "21": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 204
        }
      },
      "22": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 46,
          column: 206
        }
      },
      "23": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 17
          }
        },
        loc: {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 29,
            column: 5
          }
        },
        line: 20
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 28,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 28,
            column: 9
          }
        }, {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 28,
            column: 9
          }
        }],
        line: 23
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyPhoto.routes.ts",
      mappings: ";;;;;AAAA,qCAAiC;AACjC,oDAA4B;AAC5B,sFAQiD;AACjD,6CAAkD;AAClD,yDAAiG;AACjG,2EAG2C;AAE3C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,sCAAsC;AACtC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC;IACpB,OAAO,EAAE,gBAAM,CAAC,aAAa,EAAE;IAC/B,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;QACnC,KAAK,EAAE,EAAE,CAAC,0BAA0B;KACrC;IACD,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC7B,kBAAkB;QAClB,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QAC/D,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,EAAE,CAAC,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,gBAAgB;AAChB,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,6CAAkB,CAAC,CAAC;AAE9C,6CAA6C;AAC7C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB,wBAAwB;AACxB,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,IAAA,6BAAgB,EAAC,IAAI,CAAC,EAAE,4CAAiB,CAAC,CAAC;AAErE,MAAM,CAAC,IAAI,CACT,aAAa,EACb,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,EAC1B,IAAA,+BAAkB,EAAC;IACjB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IAClC,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;IACvD,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,EAAE;CACb,CAAC,EACF,+CAAoB,CACrB,CAAC;AAEF,MAAM,CAAC,MAAM,CACX,sBAAsB,EACtB,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,8CAAmB,CACpB,CAAC;AAEF,MAAM,CAAC,KAAK,CACV,8BAA8B,EAC9B,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,0CAAe,CAChB,CAAC;AAEF,MAAM,CAAC,KAAK,CACV,sBAAsB,EACtB,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,IAAA,4BAAe,EAAC,yCAAmB,CAAC,EACpC,6CAAkB,CACnB,CAAC;AAEF,MAAM,CAAC,KAAK,CACV,qBAAqB,EACrB,IAAA,6BAAgB,EAAC,IAAI,CAAC,EACtB,IAAA,4BAAe,EAAC,yCAAmB,CAAC,EACpC,gDAAqB,CACtB,CAAC;AAEF,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\propertyPhoto.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport multer from 'multer';\r\nimport {\r\n  uploadPropertyPhotos,\r\n  getPropertyPhotos,\r\n  deletePropertyPhoto,\r\n  setPrimaryPhoto,\r\n  reorderPropertyPhotos,\r\n  updatePhotoDetails,\r\n  getPhotoGuidelines\r\n} from '../controllers/propertyPhoto.controller';\r\nimport { authenticate } from '../middleware/auth';\r\nimport { validateRequest, validateFileUpload, validateObjectId } from '../middleware/validation';\r\nimport {\r\n  propertyPhotoSchema,\r\n  reorderPhotosSchema\r\n} from '../validators/property.validators';\r\n\r\nconst router = Router();\r\n\r\n// Configure multer for memory storage\r\nconst upload = multer({\r\n  storage: multer.memoryStorage(),\r\n  limits: {\r\n    fileSize: 10 * 1024 * 1024, // 10MB\r\n    files: 10 // Max 10 files per upload\r\n  },\r\n  fileFilter: (_req, file, cb) => {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n    if (allowedTypes.includes(file.mimetype)) {\r\n      cb(null, true);\r\n    } else {\r\n      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));\r\n    }\r\n  }\r\n});\r\n\r\n// Public routes\r\nrouter.get('/guidelines', getPhotoGuidelines);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Property photo routes\r\nrouter.get('/:id/photos', validateObjectId('id'), getPropertyPhotos);\r\n\r\nrouter.post(\r\n  '/:id/photos',\r\n  validateObjectId('id'),\r\n  upload.array('photos', 10),\r\n  validateFileUpload({\r\n    maxSize: 10 * 1024 * 1024, // 10MB\r\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],\r\n    required: true,\r\n    maxFiles: 10\r\n  }),\r\n  uploadPropertyPhotos\r\n);\r\n\r\nrouter.delete(\r\n  '/:id/photos/:photoId',\r\n  validateObjectId('id'),\r\n  deletePropertyPhoto\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/:photoId/primary',\r\n  validateObjectId('id'),\r\n  setPrimaryPhoto\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/:photoId',\r\n  validateObjectId('id'),\r\n  validateRequest(propertyPhotoSchema),\r\n  updatePhotoDetails\r\n);\r\n\r\nrouter.patch(\r\n  '/:id/photos/reorder',\r\n  validateObjectId('id'),\r\n  validateRequest(reorderPhotosSchema),\r\n  reorderPropertyPhotos\r\n);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "135333a4faf9fe1a566f9e4bf35dbd04c6cbf508"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_q3ecutjxq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_q3ecutjxq();
var __importDefault =
/* istanbul ignore next */
(cov_q3ecutjxq().s[0]++,
/* istanbul ignore next */
(cov_q3ecutjxq().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_q3ecutjxq().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_q3ecutjxq().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_q3ecutjxq().f[0]++;
  cov_q3ecutjxq().s[1]++;
  return /* istanbul ignore next */(cov_q3ecutjxq().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_q3ecutjxq().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_q3ecutjxq().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_q3ecutjxq().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_q3ecutjxq().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[3]++, require("express"));
const multer_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[4]++, __importDefault(require("multer")));
const propertyPhoto_controller_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[5]++, require("../controllers/propertyPhoto.controller"));
const auth_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[6]++, require("../middleware/auth"));
const validation_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[7]++, require("../middleware/validation"));
const property_validators_1 =
/* istanbul ignore next */
(cov_q3ecutjxq().s[8]++, require("../validators/property.validators"));
const router =
/* istanbul ignore next */
(cov_q3ecutjxq().s[9]++, (0, express_1.Router)());
// Configure multer for memory storage
const upload =
/* istanbul ignore next */
(cov_q3ecutjxq().s[10]++, (0, multer_1.default)({
  storage: multer_1.default.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024,
    // 10MB
    files: 10 // Max 10 files per upload
  },
  fileFilter: (_req, file, cb) => {
    /* istanbul ignore next */
    cov_q3ecutjxq().f[1]++;
    // Check file type
    const allowedTypes =
    /* istanbul ignore next */
    (cov_q3ecutjxq().s[11]++, ['image/jpeg', 'image/png', 'image/webp']);
    /* istanbul ignore next */
    cov_q3ecutjxq().s[12]++;
    if (allowedTypes.includes(file.mimetype)) {
      /* istanbul ignore next */
      cov_q3ecutjxq().b[3][0]++;
      cov_q3ecutjxq().s[13]++;
      cb(null, true);
    } else {
      /* istanbul ignore next */
      cov_q3ecutjxq().b[3][1]++;
      cov_q3ecutjxq().s[14]++;
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));
    }
  }
}));
// Public routes
/* istanbul ignore next */
cov_q3ecutjxq().s[15]++;
router.get('/guidelines', propertyPhoto_controller_1.getPhotoGuidelines);
// Protected routes (authentication required)
/* istanbul ignore next */
cov_q3ecutjxq().s[16]++;
router.use(auth_1.authenticate);
// Property photo routes
/* istanbul ignore next */
cov_q3ecutjxq().s[17]++;
router.get('/:id/photos', (0, validation_1.validateObjectId)('id'), propertyPhoto_controller_1.getPropertyPhotos);
/* istanbul ignore next */
cov_q3ecutjxq().s[18]++;
router.post('/:id/photos', (0, validation_1.validateObjectId)('id'), upload.array('photos', 10), (0, validation_1.validateFileUpload)({
  maxSize: 10 * 1024 * 1024,
  // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  required: true,
  maxFiles: 10
}), propertyPhoto_controller_1.uploadPropertyPhotos);
/* istanbul ignore next */
cov_q3ecutjxq().s[19]++;
router.delete('/:id/photos/:photoId', (0, validation_1.validateObjectId)('id'), propertyPhoto_controller_1.deletePropertyPhoto);
/* istanbul ignore next */
cov_q3ecutjxq().s[20]++;
router.patch('/:id/photos/:photoId/primary', (0, validation_1.validateObjectId)('id'), propertyPhoto_controller_1.setPrimaryPhoto);
/* istanbul ignore next */
cov_q3ecutjxq().s[21]++;
router.patch('/:id/photos/:photoId', (0, validation_1.validateObjectId)('id'), (0, validation_1.validateRequest)(property_validators_1.propertyPhotoSchema), propertyPhoto_controller_1.updatePhotoDetails);
/* istanbul ignore next */
cov_q3ecutjxq().s[22]++;
router.patch('/:id/photos/reorder', (0, validation_1.validateObjectId)('id'), (0, validation_1.validateRequest)(property_validators_1.reorderPhotosSchema), propertyPhoto_controller_1.reorderPropertyPhotos);
/* istanbul ignore next */
cov_q3ecutjxq().s[23]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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