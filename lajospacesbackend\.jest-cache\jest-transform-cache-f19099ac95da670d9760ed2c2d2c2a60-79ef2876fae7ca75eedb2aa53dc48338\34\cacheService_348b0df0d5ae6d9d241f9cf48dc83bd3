66859fc99e69f23ff50a713aeea00dfc
"use strict";

/* istanbul ignore next */
function cov_1ynnhg1su5() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\cacheService.ts";
  var hash = "0887c2deb5493ce0bca95ab98c34c3454ba7cbab";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\cacheService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 76
        }
      },
      "2": {
        start: {
          line: 4,
          column: 16
        },
        end: {
          line: 4,
          column: 32
        }
      },
      "3": {
        start: {
          line: 5,
          column: 22
        },
        end: {
          line: 5,
          column: 54
        }
      },
      "4": {
        start: {
          line: 6,
          column: 17
        },
        end: {
          line: 6,
          column: 43
        }
      },
      "5": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 58,
          column: 2
        }
      },
      "6": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 31
        }
      },
      "7": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 31
        }
      },
      "8": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 28
        }
      },
      "9": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 77,
          column: 11
        }
      },
      "10": {
        start: {
          line: 70,
          column: 20
        },
        end: {
          line: 73,
          column: 21
        }
      },
      "11": {
        start: {
          line: 71,
          column: 24
        },
        end: {
          line: 71,
          column: 96
        }
      },
      "12": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 37
        }
      },
      "13": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 57
        }
      },
      "14": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 34
        }
      },
      "15": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 83,
          column: 11
        }
      },
      "16": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 69
        }
      },
      "17": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 88,
          column: 11
        }
      },
      "18": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 34
        }
      },
      "19": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 35
        }
      },
      "20": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 75
        }
      },
      "21": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 92,
          column: 11
        }
      },
      "22": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 35
        }
      },
      "23": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 68
        }
      },
      "24": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 96,
          column: 11
        }
      },
      "25": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 35
        }
      },
      "26": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 72
        }
      },
      "27": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 100,
          column: 11
        }
      },
      "28": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 98,
          column: 33
        }
      },
      "29": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 103
        }
      },
      "30": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "31": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 106,
          column: 13
        }
      },
      "32": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 105,
          column: 44
        }
      },
      "33": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 109,
          column: 78
        }
      },
      "34": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 110,
          column: 24
        }
      },
      "35": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 122,
          column: 9
        }
      },
      "36": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 118,
          column: 13
        }
      },
      "37": {
        start: {
          line: 116,
          column: 16
        },
        end: {
          line: 116,
          column: 41
        }
      },
      "38": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 39
        }
      },
      "39": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 82
        }
      },
      "40": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 40
        }
      },
      "41": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 134,
          column: 9
        }
      },
      "42": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 40
        }
      },
      "43": {
        start: {
          line: 132,
          column: 12
        },
        end: {
          line: 132,
          column: 71
        }
      },
      "44": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 133,
          column: 22
        }
      },
      "45": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "46": {
        start: {
          line: 138,
          column: 12
        },
        end: {
          line: 138,
          column: 36
        }
      },
      "47": {
        start: {
          line: 141,
          column: 12
        },
        end: {
          line: 141,
          column: 73
        }
      },
      "48": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 24
        }
      },
      "49": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 152,
          column: 9
        }
      },
      "50": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 86
        }
      },
      "51": {
        start: {
          line: 151,
          column: 12
        },
        end: {
          line: 151,
          column: 24
        }
      },
      "52": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "53": {
        start: {
          line: 154,
          column: 27
        },
        end: {
          line: 154,
          column: 59
        }
      },
      "54": {
        start: {
          line: 155,
          column: 29
        },
        end: {
          line: 155,
          column: 58
        }
      },
      "55": {
        start: {
          line: 156,
          column: 25
        },
        end: {
          line: 156,
          column: 56
        }
      },
      "56": {
        start: {
          line: 157,
          column: 12
        },
        end: {
          line: 159,
          column: 13
        }
      },
      "57": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 28
        }
      },
      "58": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 162,
          column: 13
        }
      },
      "59": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 161,
          column: 46
        }
      },
      "60": {
        start: {
          line: 163,
          column: 12
        },
        end: {
          line: 163,
          column: 24
        }
      },
      "61": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 61
        }
      },
      "62": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 24
        }
      },
      "63": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 177,
          column: 9
        }
      },
      "64": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 175,
          column: 86
        }
      },
      "65": {
        start: {
          line: 176,
          column: 12
        },
        end: {
          line: 176,
          column: 25
        }
      },
      "66": {
        start: {
          line: 178,
          column: 8
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "67": {
        start: {
          line: 179,
          column: 27
        },
        end: {
          line: 179,
          column: 59
        }
      },
      "68": {
        start: {
          line: 180,
          column: 29
        },
        end: {
          line: 180,
          column: 58
        }
      },
      "69": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 181,
          column: 47
        }
      },
      "70": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 188,
          column: 13
        }
      },
      "71": {
        start: {
          line: 184,
          column: 16
        },
        end: {
          line: 184,
          column: 52
        }
      },
      "72": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 187,
          column: 44
        }
      },
      "73": {
        start: {
          line: 189,
          column: 12
        },
        end: {
          line: 189,
          column: 64
        }
      },
      "74": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 24
        }
      },
      "75": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 61
        }
      },
      "76": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 194,
          column: 25
        }
      },
      "77": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 203,
          column: 9
        }
      },
      "78": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 202,
          column: 25
        }
      },
      "79": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "80": {
        start: {
          line: 205,
          column: 27
        },
        end: {
          line: 205,
          column: 59
        }
      },
      "81": {
        start: {
          line: 206,
          column: 29
        },
        end: {
          line: 206,
          column: 58
        }
      },
      "82": {
        start: {
          line: 207,
          column: 27
        },
        end: {
          line: 207,
          column: 58
        }
      },
      "83": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 30
        }
      },
      "84": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 64
        }
      },
      "85": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 25
        }
      },
      "86": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 221,
          column: 9
        }
      },
      "87": {
        start: {
          line: 220,
          column: 12
        },
        end: {
          line: 220,
          column: 25
        }
      },
      "88": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 231,
          column: 9
        }
      },
      "89": {
        start: {
          line: 223,
          column: 27
        },
        end: {
          line: 223,
          column: 59
        }
      },
      "90": {
        start: {
          line: 224,
          column: 29
        },
        end: {
          line: 224,
          column: 58
        }
      },
      "91": {
        start: {
          line: 225,
          column: 27
        },
        end: {
          line: 225,
          column: 61
        }
      },
      "92": {
        start: {
          line: 226,
          column: 12
        },
        end: {
          line: 226,
          column: 30
        }
      },
      "93": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 229,
          column: 64
        }
      },
      "94": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 230,
          column: 25
        }
      },
      "95": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 239,
          column: 9
        }
      },
      "96": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 25
        }
      },
      "97": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "98": {
        start: {
          line: 241,
          column: 27
        },
        end: {
          line: 241,
          column: 59
        }
      },
      "99": {
        start: {
          line: 242,
          column: 29
        },
        end: {
          line: 242,
          column: 58
        }
      },
      "100": {
        start: {
          line: 243,
          column: 27
        },
        end: {
          line: 243,
          column: 66
        }
      },
      "101": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 244,
          column: 26
        }
      },
      "102": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 247,
          column: 64
        }
      },
      "103": {
        start: {
          line: 248,
          column: 12
        },
        end: {
          line: 248,
          column: 25
        }
      },
      "104": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 257,
          column: 9
        }
      },
      "105": {
        start: {
          line: 256,
          column: 12
        },
        end: {
          line: 256,
          column: 40
        }
      },
      "106": {
        start: {
          line: 256,
          column: 34
        },
        end: {
          line: 256,
          column: 38
        }
      },
      "107": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 271,
          column: 9
        }
      },
      "108": {
        start: {
          line: 259,
          column: 27
        },
        end: {
          line: 259,
          column: 59
        }
      },
      "109": {
        start: {
          line: 260,
          column: 30
        },
        end: {
          line: 260,
          column: 76
        }
      },
      "110": {
        start: {
          line: 260,
          column: 46
        },
        end: {
          line: 260,
          column: 75
        }
      },
      "111": {
        start: {
          line: 261,
          column: 28
        },
        end: {
          line: 261,
          column: 61
        }
      },
      "112": {
        start: {
          line: 262,
          column: 12
        },
        end: {
          line: 266,
          column: 15
        }
      },
      "113": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 264,
          column: 32
        }
      },
      "114": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 264,
          column: 32
        }
      },
      "115": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 265,
          column: 72
        }
      },
      "116": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 269,
          column: 62
        }
      },
      "117": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 270,
          column: 40
        }
      },
      "118": {
        start: {
          line: 270,
          column: 34
        },
        end: {
          line: 270,
          column: 38
        }
      },
      "119": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 279,
          column: 9
        }
      },
      "120": {
        start: {
          line: 278,
          column: 12
        },
        end: {
          line: 278,
          column: 25
        }
      },
      "121": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 296,
          column: 9
        }
      },
      "122": {
        start: {
          line: 281,
          column: 27
        },
        end: {
          line: 281,
          column: 59
        }
      },
      "123": {
        start: {
          line: 282,
          column: 24
        },
        end: {
          line: 282,
          column: 47
        }
      },
      "124": {
        start: {
          line: 284,
          column: 29
        },
        end: {
          line: 284,
          column: 48
        }
      },
      "125": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 289,
          column: 13
        }
      },
      "126": {
        start: {
          line: 286,
          column: 33
        },
        end: {
          line: 286,
          column: 62
        }
      },
      "127": {
        start: {
          line: 287,
          column: 36
        },
        end: {
          line: 287,
          column: 92
        }
      },
      "128": {
        start: {
          line: 288,
          column: 16
        },
        end: {
          line: 288,
          column: 59
        }
      },
      "129": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 290,
          column: 34
        }
      },
      "130": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 24
        }
      },
      "131": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 294,
          column: 62
        }
      },
      "132": {
        start: {
          line: 295,
          column: 12
        },
        end: {
          line: 295,
          column: 25
        }
      },
      "133": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 304,
          column: 9
        }
      },
      "134": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 303,
          column: 21
        }
      },
      "135": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "136": {
        start: {
          line: 306,
          column: 27
        },
        end: {
          line: 306,
          column: 59
        }
      },
      "137": {
        start: {
          line: 307,
          column: 34
        },
        end: {
          line: 307,
          column: 67
        }
      },
      "138": {
        start: {
          line: 308,
          column: 25
        },
        end: {
          line: 308,
          column: 62
        }
      },
      "139": {
        start: {
          line: 309,
          column: 12
        },
        end: {
          line: 311,
          column: 13
        }
      },
      "140": {
        start: {
          line: 310,
          column: 16
        },
        end: {
          line: 310,
          column: 25
        }
      },
      "141": {
        start: {
          line: 312,
          column: 27
        },
        end: {
          line: 312,
          column: 54
        }
      },
      "142": {
        start: {
          line: 313,
          column: 12
        },
        end: {
          line: 313,
          column: 26
        }
      },
      "143": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 316,
          column: 72
        }
      },
      "144": {
        start: {
          line: 317,
          column: 12
        },
        end: {
          line: 317,
          column: 21
        }
      },
      "145": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "146": {
        start: {
          line: 325,
          column: 12
        },
        end: {
          line: 325,
          column: 21
        }
      },
      "147": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 335,
          column: 9
        }
      },
      "148": {
        start: {
          line: 328,
          column: 27
        },
        end: {
          line: 328,
          column: 59
        }
      },
      "149": {
        start: {
          line: 329,
          column: 29
        },
        end: {
          line: 329,
          column: 58
        }
      },
      "150": {
        start: {
          line: 330,
          column: 12
        },
        end: {
          line: 330,
          column: 52
        }
      },
      "151": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 67
        }
      },
      "152": {
        start: {
          line: 334,
          column: 12
        },
        end: {
          line: 334,
          column: 21
        }
      },
      "153": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 343,
          column: 9
        }
      },
      "154": {
        start: {
          line: 342,
          column: 12
        },
        end: {
          line: 342,
          column: 21
        }
      },
      "155": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 352,
          column: 9
        }
      },
      "156": {
        start: {
          line: 345,
          column: 27
        },
        end: {
          line: 345,
          column: 59
        }
      },
      "157": {
        start: {
          line: 346,
          column: 29
        },
        end: {
          line: 346,
          column: 58
        }
      },
      "158": {
        start: {
          line: 347,
          column: 12
        },
        end: {
          line: 347,
          column: 52
        }
      },
      "159": {
        start: {
          line: 350,
          column: 12
        },
        end: {
          line: 350,
          column: 67
        }
      },
      "160": {
        start: {
          line: 351,
          column: 12
        },
        end: {
          line: 351,
          column: 21
        }
      },
      "161": {
        start: {
          line: 358,
          column: 8
        },
        end: {
          line: 360,
          column: 9
        }
      },
      "162": {
        start: {
          line: 359,
          column: 12
        },
        end: {
          line: 359,
          column: 40
        }
      },
      "163": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 374,
          column: 9
        }
      },
      "164": {
        start: {
          line: 362,
          column: 25
        },
        end: {
          line: 362,
          column: 57
        }
      },
      "165": {
        start: {
          line: 363,
          column: 29
        },
        end: {
          line: 363,
          column: 63
        }
      },
      "166": {
        start: {
          line: 364,
          column: 12
        },
        end: {
          line: 369,
          column: 14
        }
      },
      "167": {
        start: {
          line: 372,
          column: 12
        },
        end: {
          line: 372,
          column: 63
        }
      },
      "168": {
        start: {
          line: 373,
          column: 12
        },
        end: {
          line: 373,
          column: 62
        }
      },
      "169": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 382,
          column: 9
        }
      },
      "170": {
        start: {
          line: 381,
          column: 12
        },
        end: {
          line: 381,
          column: 25
        }
      },
      "171": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 391,
          column: 9
        }
      },
      "172": {
        start: {
          line: 384,
          column: 12
        },
        end: {
          line: 384,
          column: 41
        }
      },
      "173": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 385,
          column: 68
        }
      },
      "174": {
        start: {
          line: 386,
          column: 12
        },
        end: {
          line: 386,
          column: 24
        }
      },
      "175": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 389,
          column: 63
        }
      },
      "176": {
        start: {
          line: 390,
          column: 12
        },
        end: {
          line: 390,
          column: 25
        }
      },
      "177": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 397,
          column: 30
        }
      },
      "178": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 403,
          column: 27
        }
      },
      "179": {
        start: {
          line: 407,
          column: 0
        },
        end: {
          line: 407,
          column: 42
        }
      },
      "180": {
        start: {
          line: 409,
          column: 0
        },
        end: {
          line: 466,
          column: 2
        }
      },
      "181": {
        start: {
          line: 415,
          column: 23
        },
        end: {
          line: 415,
          column: 70
        }
      },
      "182": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 418,
          column: 9
        }
      },
      "183": {
        start: {
          line: 417,
          column: 12
        },
        end: {
          line: 417,
          column: 26
        }
      },
      "184": {
        start: {
          line: 420,
          column: 23
        },
        end: {
          line: 420,
          column: 38
        }
      },
      "185": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 421,
          column: 75
        }
      },
      "186": {
        start: {
          line: 422,
          column: 8
        },
        end: {
          line: 422,
          column: 22
        }
      },
      "187": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 428,
          column: 74
        }
      },
      "188": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 434,
          column: 72
        }
      },
      "189": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 440,
          column: 62
        }
      },
      "190": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 446,
          column: 84
        }
      },
      "191": {
        start: {
          line: 452,
          column: 8
        },
        end: {
          line: 452,
          column: 70
        }
      },
      "192": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 76
        }
      },
      "193": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 67
        }
      },
      "194": {
        start: {
          line: 467,
          column: 0
        },
        end: {
          line: 467,
          column: 39
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        loc: {
          start: {
            line: 60,
            column: 18
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 60
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 69,
            column: 35
          },
          end: {
            line: 69,
            column: 36
          }
        },
        loc: {
          start: {
            line: 69,
            column: 48
          },
          end: {
            line: 75,
            column: 17
          }
        },
        line: 69
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        loc: {
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 80
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 81,
            column: 34
          },
          end: {
            line: 81,
            column: 35
          }
        },
        loc: {
          start: {
            line: 81,
            column: 40
          },
          end: {
            line: 83,
            column: 9
          }
        },
        line: 81
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 84,
            column: 32
          },
          end: {
            line: 84,
            column: 33
          }
        },
        loc: {
          start: {
            line: 84,
            column: 38
          },
          end: {
            line: 88,
            column: 9
          }
        },
        line: 84
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 89,
            column: 32
          },
          end: {
            line: 89,
            column: 33
          }
        },
        loc: {
          start: {
            line: 89,
            column: 41
          },
          end: {
            line: 92,
            column: 9
          }
        },
        line: 89
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 93,
            column: 30
          },
          end: {
            line: 93,
            column: 31
          }
        },
        loc: {
          start: {
            line: 93,
            column: 36
          },
          end: {
            line: 96,
            column: 9
          }
        },
        line: 93
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 97,
            column: 39
          },
          end: {
            line: 97,
            column: 40
          }
        },
        loc: {
          start: {
            line: 97,
            column: 45
          },
          end: {
            line: 100,
            column: 9
          }
        },
        line: 97
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 5
          }
        },
        loc: {
          start: {
            line: 102,
            column: 20
          },
          end: {
            line: 112,
            column: 5
          }
        },
        line: 102
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 5
          }
        },
        loc: {
          start: {
            line: 113,
            column: 23
          },
          end: {
            line: 123,
            column: 5
          }
        },
        line: 113
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 124,
            column: 5
          }
        },
        loc: {
          start: {
            line: 124,
            column: 29
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 124
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 127,
            column: 5
          }
        },
        loc: {
          start: {
            line: 127,
            column: 20
          },
          end: {
            line: 135,
            column: 5
          }
        },
        line: 127
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 136,
            column: 5
          }
        },
        loc: {
          start: {
            line: 136,
            column: 22
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 136
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 148,
            column: 5
          }
        },
        loc: {
          start: {
            line: 148,
            column: 40
          },
          end: {
            line: 169,
            column: 5
          }
        },
        line: 148
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        loc: {
          start: {
            line: 173,
            column: 58
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 173
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 200,
            column: 5
          }
        },
        loc: {
          start: {
            line: 200,
            column: 40
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 200
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        loc: {
          start: {
            line: 218,
            column: 43
          },
          end: {
            line: 232,
            column: 5
          }
        },
        line: 218
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 236,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        loc: {
          start: {
            line: 236,
            column: 48
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 236
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 42
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 254
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 256,
            column: 28
          },
          end: {
            line: 256,
            column: 29
          }
        },
        loc: {
          start: {
            line: 256,
            column: 34
          },
          end: {
            line: 256,
            column: 38
          }
        },
        line: 256
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 260,
            column: 39
          },
          end: {
            line: 260,
            column: 40
          }
        },
        loc: {
          start: {
            line: 260,
            column: 46
          },
          end: {
            line: 260,
            column: 75
          }
        },
        line: 260
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 262,
            column: 31
          },
          end: {
            line: 262,
            column: 32
          }
        },
        loc: {
          start: {
            line: 262,
            column: 39
          },
          end: {
            line: 266,
            column: 13
          }
        },
        line: 262
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 270,
            column: 28
          },
          end: {
            line: 270,
            column: 29
          }
        },
        loc: {
          start: {
            line: 270,
            column: 34
          },
          end: {
            line: 270,
            column: 38
          }
        },
        line: 270
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 276,
            column: 4
          },
          end: {
            line: 276,
            column: 5
          }
        },
        loc: {
          start: {
            line: 276,
            column: 62
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 276
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 51
          },
          end: {
            line: 319,
            column: 5
          }
        },
        line: 301
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 323,
            column: 5
          }
        },
        loc: {
          start: {
            line: 323,
            column: 41
          },
          end: {
            line: 336,
            column: 5
          }
        },
        line: 323
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 340,
            column: 4
          },
          end: {
            line: 340,
            column: 5
          }
        },
        loc: {
          start: {
            line: 340,
            column: 41
          },
          end: {
            line: 353,
            column: 5
          }
        },
        line: 340
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 357,
            column: 5
          }
        },
        loc: {
          start: {
            line: 357,
            column: 21
          },
          end: {
            line: 375,
            column: 5
          }
        },
        line: 357
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 379,
            column: 4
          },
          end: {
            line: 379,
            column: 5
          }
        },
        loc: {
          start: {
            line: 379,
            column: 21
          },
          end: {
            line: 392,
            column: 5
          }
        },
        line: 379
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 396,
            column: 5
          }
        },
        loc: {
          start: {
            line: 396,
            column: 18
          },
          end: {
            line: 398,
            column: 5
          }
        },
        line: 396
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 402,
            column: 5
          }
        },
        loc: {
          start: {
            line: 402,
            column: 16
          },
          end: {
            line: 404,
            column: 5
          }
        },
        line: 402
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 413,
            column: 5
          }
        },
        loc: {
          start: {
            line: 413,
            column: 67
          },
          end: {
            line: 423,
            column: 5
          }
        },
        line: 413
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        },
        loc: {
          start: {
            line: 427,
            column: 58
          },
          end: {
            line: 429,
            column: 5
          }
        },
        line: 427
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 433,
            column: 5
          }
        },
        loc: {
          start: {
            line: 433,
            column: 38
          },
          end: {
            line: 435,
            column: 5
          }
        },
        line: 433
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 439,
            column: 4
          },
          end: {
            line: 439,
            column: 5
          }
        },
        loc: {
          start: {
            line: 439,
            column: 32
          },
          end: {
            line: 441,
            column: 5
          }
        },
        line: 439
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 445,
            column: 4
          },
          end: {
            line: 445,
            column: 5
          }
        },
        loc: {
          start: {
            line: 445,
            column: 50
          },
          end: {
            line: 447,
            column: 5
          }
        },
        line: 445
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 451,
            column: 4
          },
          end: {
            line: 451,
            column: 5
          }
        },
        loc: {
          start: {
            line: 451,
            column: 40
          },
          end: {
            line: 453,
            column: 5
          }
        },
        line: 451
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 457,
            column: 5
          }
        },
        loc: {
          start: {
            line: 457,
            column: 49
          },
          end: {
            line: 459,
            column: 5
          }
        },
        line: 457
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 5
          }
        },
        loc: {
          start: {
            line: 463,
            column: 44
          },
          end: {
            line: 465,
            column: 5
          }
        },
        line: 463
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 70,
            column: 20
          },
          end: {
            line: 73,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 20
          },
          end: {
            line: 73,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "1": {
        loc: {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 106,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 106,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "2": {
        loc: {
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 118,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 12
          },
          end: {
            line: 118,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "3": {
        loc: {
          start: {
            line: 148,
            column: 19
          },
          end: {
            line: 148,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 148,
            column: 32
          },
          end: {
            line: 148,
            column: 38
          }
        }],
        line: 148
      },
      "4": {
        loc: {
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 152,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 152,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "5": {
        loc: {
          start: {
            line: 157,
            column: 12
          },
          end: {
            line: 159,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 12
          },
          end: {
            line: 159,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "6": {
        loc: {
          start: {
            line: 160,
            column: 12
          },
          end: {
            line: 162,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 160,
            column: 12
          },
          end: {
            line: 162,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 160
      },
      "7": {
        loc: {
          start: {
            line: 173,
            column: 26
          },
          end: {
            line: 173,
            column: 45
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 173,
            column: 39
          },
          end: {
            line: 173,
            column: 45
          }
        }],
        line: 173
      },
      "8": {
        loc: {
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 177,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 177,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "9": {
        loc: {
          start: {
            line: 181,
            column: 24
          },
          end: {
            line: 181,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 24
          },
          end: {
            line: 181,
            column: 33
          }
        }, {
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 181,
            column: 47
          }
        }],
        line: 181
      },
      "10": {
        loc: {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 188,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 188,
            column: 13
          }
        }, {
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 188,
            column: 13
          }
        }],
        line: 183
      },
      "11": {
        loc: {
          start: {
            line: 200,
            column: 19
          },
          end: {
            line: 200,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 200,
            column: 32
          },
          end: {
            line: 200,
            column: 38
          }
        }],
        line: 200
      },
      "12": {
        loc: {
          start: {
            line: 201,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "13": {
        loc: {
          start: {
            line: 218,
            column: 22
          },
          end: {
            line: 218,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 218,
            column: 35
          },
          end: {
            line: 218,
            column: 41
          }
        }],
        line: 218
      },
      "14": {
        loc: {
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 221,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "15": {
        loc: {
          start: {
            line: 236,
            column: 27
          },
          end: {
            line: 236,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 236,
            column: 40
          },
          end: {
            line: 236,
            column: 46
          }
        }],
        line: 236
      },
      "16": {
        loc: {
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "17": {
        loc: {
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 254,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 254,
            column: 34
          },
          end: {
            line: 254,
            column: 40
          }
        }],
        line: 254
      },
      "18": {
        loc: {
          start: {
            line: 255,
            column: 8
          },
          end: {
            line: 257,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 255,
            column: 8
          },
          end: {
            line: 257,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 255
      },
      "19": {
        loc: {
          start: {
            line: 255,
            column: 12
          },
          end: {
            line: 255,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 255,
            column: 12
          },
          end: {
            line: 255,
            column: 27
          }
        }, {
          start: {
            line: 255,
            column: 31
          },
          end: {
            line: 255,
            column: 48
          }
        }],
        line: 255
      },
      "20": {
        loc: {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 264,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 264,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "21": {
        loc: {
          start: {
            line: 265,
            column: 23
          },
          end: {
            line: 265,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 265,
            column: 42
          },
          end: {
            line: 265,
            column: 64
          }
        }, {
          start: {
            line: 265,
            column: 67
          },
          end: {
            line: 265,
            column: 71
          }
        }],
        line: 265
      },
      "22": {
        loc: {
          start: {
            line: 276,
            column: 30
          },
          end: {
            line: 276,
            column: 49
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 276,
            column: 43
          },
          end: {
            line: 276,
            column: 49
          }
        }],
        line: 276
      },
      "23": {
        loc: {
          start: {
            line: 277,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "24": {
        loc: {
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 277,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 277,
            column: 12
          },
          end: {
            line: 277,
            column: 27
          }
        }, {
          start: {
            line: 277,
            column: 31
          },
          end: {
            line: 277,
            column: 57
          }
        }],
        line: 277
      },
      "25": {
        loc: {
          start: {
            line: 282,
            column: 24
          },
          end: {
            line: 282,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 282,
            column: 24
          },
          end: {
            line: 282,
            column: 33
          }
        }, {
          start: {
            line: 282,
            column: 37
          },
          end: {
            line: 282,
            column: 47
          }
        }],
        line: 282
      },
      "26": {
        loc: {
          start: {
            line: 287,
            column: 36
          },
          end: {
            line: 287,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 287,
            column: 55
          },
          end: {
            line: 287,
            column: 76
          }
        }, {
          start: {
            line: 287,
            column: 79
          },
          end: {
            line: 287,
            column: 92
          }
        }],
        line: 287
      },
      "27": {
        loc: {
          start: {
            line: 301,
            column: 30
          },
          end: {
            line: 301,
            column: 49
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 301,
            column: 43
          },
          end: {
            line: 301,
            column: 49
          }
        }],
        line: 301
      },
      "28": {
        loc: {
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "29": {
        loc: {
          start: {
            line: 309,
            column: 12
          },
          end: {
            line: 311,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 12
          },
          end: {
            line: 311,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "30": {
        loc: {
          start: {
            line: 323,
            column: 20
          },
          end: {
            line: 323,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 323,
            column: 33
          },
          end: {
            line: 323,
            column: 39
          }
        }],
        line: 323
      },
      "31": {
        loc: {
          start: {
            line: 324,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "32": {
        loc: {
          start: {
            line: 340,
            column: 20
          },
          end: {
            line: 340,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 340,
            column: 33
          },
          end: {
            line: 340,
            column: 39
          }
        }],
        line: 340
      },
      "33": {
        loc: {
          start: {
            line: 341,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 341,
            column: 8
          },
          end: {
            line: 343,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 341
      },
      "34": {
        loc: {
          start: {
            line: 358,
            column: 8
          },
          end: {
            line: 360,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 8
          },
          end: {
            line: 360,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "35": {
        loc: {
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 382,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 382,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 380
      },
      "36": {
        loc: {
          start: {
            line: 413,
            column: 35
          },
          end: {
            line: 413,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 413,
            column: 48
          },
          end: {
            line: 413,
            column: 54
          }
        }],
        line: 413
      },
      "37": {
        loc: {
          start: {
            line: 416,
            column: 8
          },
          end: {
            line: 418,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 416,
            column: 8
          },
          end: {
            line: 418,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 416
      },
      "38": {
        loc: {
          start: {
            line: 427,
            column: 37
          },
          end: {
            line: 427,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 427,
            column: 50
          },
          end: {
            line: 427,
            column: 56
          }
        }],
        line: 427
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0],
      "12": [0, 0],
      "13": [0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0],
      "31": [0, 0],
      "32": [0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0],
      "37": [0, 0],
      "38": [0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\cacheService.ts",
      mappings: ";;;AAAA,iCAAsD;AACtD,uDAA+C;AAC/C,4CAAyC;AAUzC,wDAAwD;AAC3C,QAAA,YAAY,GAAG;IAC1B,2BAA2B;IAC3B,IAAI,EAAE;QACJ,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,aAAa;QAC3B,MAAM,EAAE,OAAO;QACf,SAAS,EAAE,IAAI;KAChB;IAED,2DAA2D;IAC3D,QAAQ,EAAE;QACR,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,aAAa;QAC3B,MAAM,EAAE,WAAW;QACnB,SAAS,EAAE,IAAI;KAChB;IAED,wDAAwD;IACxD,MAAM,EAAE;QACN,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,YAAY;QACzB,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;KACf;IAED,8BAA8B;IAC9B,MAAM,EAAE;QACN,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,WAAW;QAC9B,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,IAAI;KAChB;IAED,2BAA2B;IAC3B,OAAO,EAAE;QACP,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS;QACvB,MAAM,EAAE,UAAU;QAClB,SAAS,EAAE,IAAI;KAChB;IAED,kCAAkC;IAClC,IAAI,EAAE;QACJ,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,YAAY;QACzB,MAAM,EAAE,OAAO;QACf,SAAS,EAAE,IAAI;KAChB;IAED,8BAA8B;IAC9B,SAAS,EAAE;QACT,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS;QACvB,MAAM,EAAE,YAAY;QACpB,SAAS,EAAE,IAAI;KAChB;IAED,gCAAgC;IAChC,YAAY,EAAE;QACZ,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,aAAa;QAC3B,MAAM,EAAE,eAAe;QACvB,SAAS,EAAE,IAAI;KAChB;CACF,CAAC;AAEF,MAAM,YAAY;IAMhB;QAJQ,cAAS,GAAY,KAAK,CAAC;QAC3B,kBAAa,GAAW,CAAC,CAAC;QAC1B,eAAU,GAAW,CAAC,CAAC;QAG7B,IAAI,CAAC,MAAM,GAAG,IAAA,oBAAY,EAAC;YACzB,GAAG,EAAE,oBAAM,CAAC,SAAS;YACrB,MAAM,EAAE;gBACN,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,IAAI;gBACjB,iBAAiB,EAAE,CAAC,OAAO,EAAE,EAAE;oBAC7B,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;wBAC9B,eAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;wBAC/D,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC7B,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;YACvB,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YAClC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,GAAW,EAAE,MAAmB;QAClD,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;IAClC,CAAC;IAEO,SAAS,CAAC,IAAS;QACzB,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,IAAY;QAC9B,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAI,GAAW,EAAE,aAAwC,MAAM;QACtE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,IAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CACP,GAAW,EACX,KAAU,EACV,aAAwC,MAAM,EAC9C,SAAkB;QAElB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,GAAG,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC;YAEpC,IAAI,WAAmB,CAAC;YACxB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;YAED,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,GAAW,EAAE,aAAwC,MAAM;QACnE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/C,OAAO,MAAM,GAAG,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,GAAW,EAAE,aAAwC,MAAM;QACtE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClD,OAAO,MAAM,GAAG,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,GAAW,EAAE,GAAW,EAAE,aAAwC,MAAM;QACnF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACvD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAI,IAAc,EAAE,aAAwC,MAAM;QAC1E,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElD,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,CAAC,IAAI;oBAAE,OAAO,IAAI,CAAC;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAS,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CACR,aAAiD,EACjD,aAAwC,MAAM,EAC9C,SAAkB;QAElB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC;YAEpC,sCAAsC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAErC,KAAK,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,aAAa,EAAE,CAAC;gBAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBAC/C,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7E,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;YAC7C,CAAC;YAED,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,aAAwC,MAAM;QAC9E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACxD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC;YACX,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC3C,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,GAAW,EAAE,aAAwC,MAAM;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,GAAW,EAAE,aAAwC,MAAM;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC/C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEpD,OAAO;gBACL,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC7B,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAED,uCAAuC;AAC1B,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAE/C,+CAA+C;AAClC,QAAA,YAAY,GAAG;IAC1B;;OAEG;IACH,KAAK,CAAC,UAAU,CACd,GAAW,EACX,OAAyB,EACzB,aAAwC,MAAM,EAC9C,SAAkB;QAElB,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,oBAAY,CAAC,GAAG,CAAI,GAAG,EAAE,UAAU,CAAC,CAAC;QAC1D,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,MAAM,MAAM,GAAG,MAAM,OAAO,EAAE,CAAC;QAC/B,MAAM,oBAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAC3D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,aAAwC,MAAM;QACrF,OAAO,MAAM,oBAAY,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc,EAAE,QAAa;QAC3C,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,YAAiB;QACvD,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,OAAY;QACtD,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QAC5C,OAAO,MAAM,oBAAY,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;CACF,CAAC;AAEF,kBAAe,oBAAY,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\cacheService.ts"],
      sourcesContent: ["import { createClient, RedisClientType } from 'redis';\r\nimport { config } from '../config/environment';\r\nimport { logger } from '../utils/logger';\r\n\r\n// Cache configuration\r\nexport interface CacheConfig {\r\n  ttl: number; // Time to live in seconds\r\n  prefix: string;\r\n  compress?: boolean;\r\n  serialize?: boolean;\r\n}\r\n\r\n// Default cache configurations for different data types\r\nexport const cacheConfigs = {\r\n  // User data - moderate TTL\r\n  user: {\r\n    ttl: 15 * 60, // 15 minutes\r\n    prefix: 'user:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Property data - longer TTL as it changes less frequently\r\n  property: {\r\n    ttl: 30 * 60, // 30 minutes\r\n    prefix: 'property:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Search results - shorter TTL as they need to be fresh\r\n  search: {\r\n    ttl: 5 * 60, // 5 minutes\r\n    prefix: 'search:',\r\n    serialize: true,\r\n    compress: true\r\n  },\r\n  \r\n  // Static data - very long TTL\r\n  static: {\r\n    ttl: 24 * 60 * 60, // 24 hours\r\n    prefix: 'static:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Session data - short TTL\r\n  session: {\r\n    ttl: 60 * 60, // 1 hour\r\n    prefix: 'session:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Temporary data - very short TTL\r\n  temp: {\r\n    ttl: 5 * 60, // 5 minutes\r\n    prefix: 'temp:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Analytics data - medium TTL\r\n  analytics: {\r\n    ttl: 60 * 60, // 1 hour\r\n    prefix: 'analytics:',\r\n    serialize: true\r\n  },\r\n  \r\n  // Notification data - short TTL\r\n  notification: {\r\n    ttl: 10 * 60, // 10 minutes\r\n    prefix: 'notification:',\r\n    serialize: true\r\n  }\r\n};\r\n\r\nclass CacheService {\r\n  private client: RedisClientType;\r\n  private connected: boolean = false;\r\n  private retryAttempts: number = 0;\r\n  private maxRetries: number = 5;\r\n\r\n  constructor() {\r\n    this.client = createClient({\r\n      url: config.REDIS_URL,\r\n      socket: {\r\n        connectTimeout: 5000,\r\n        lazyConnect: true,\r\n        reconnectStrategy: (retries) => {\r\n          if (retries > this.maxRetries) {\r\n            logger.error('Redis cache: Max reconnection attempts reached');\r\n            return false;\r\n          }\r\n          return Math.min(retries * 100, 3000);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.setupEventHandlers();\r\n  }\r\n\r\n  private setupEventHandlers(): void {\r\n    this.client.on('connect', () => {\r\n      logger.info('Redis cache client connecting...');\r\n    });\r\n\r\n    this.client.on('ready', () => {\r\n      this.connected = true;\r\n      this.retryAttempts = 0;\r\n      logger.info('Redis cache client connected and ready');\r\n    });\r\n\r\n    this.client.on('error', (err) => {\r\n      this.connected = false;\r\n      logger.error('Redis cache client error:', err);\r\n    });\r\n\r\n    this.client.on('end', () => {\r\n      this.connected = false;\r\n      logger.warn('Redis cache client connection ended');\r\n    });\r\n\r\n    this.client.on('reconnecting', () => {\r\n      this.retryAttempts++;\r\n      logger.info(`Redis cache client reconnecting... (attempt ${this.retryAttempts})`);\r\n    });\r\n  }\r\n\r\n  async connect(): Promise<void> {\r\n    try {\r\n      if (!this.connected) {\r\n        await this.client.connect();\r\n      }\r\n    } catch (error) {\r\n      logger.error('Failed to connect to Redis cache:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async disconnect(): Promise<void> {\r\n    try {\r\n      if (this.connected) {\r\n        await this.client.quit();\r\n        this.connected = false;\r\n      }\r\n    } catch (error) {\r\n      logger.error('Error disconnecting from Redis cache:', error);\r\n    }\r\n  }\r\n\r\n  private generateKey(key: string, config: CacheConfig): string {\r\n    return `${config.prefix}${key}`;\r\n  }\r\n\r\n  private serialize(data: any): string {\r\n    try {\r\n      return JSON.stringify(data);\r\n    } catch (error) {\r\n      logger.error('Cache serialization error:', error);\r\n      return '';\r\n    }\r\n  }\r\n\r\n  private deserialize(data: string): any {\r\n    try {\r\n      return JSON.parse(data);\r\n    } catch (error) {\r\n      logger.error('Cache deserialization error:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get data from cache\r\n   */\r\n  async get<T>(key: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<T | null> {\r\n    if (!this.connected) {\r\n      logger.warn('Redis cache not connected, skipping get operation');\r\n      return null;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      const data = await this.client.get(cacheKey);\r\n\r\n      if (!data) {\r\n        return null;\r\n      }\r\n\r\n      if (config.serialize) {\r\n        return this.deserialize(data);\r\n      }\r\n\r\n      return data as T;\r\n    } catch (error) {\r\n      logger.error('Cache get error:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set data in cache\r\n   */\r\n  async set(\r\n    key: string, \r\n    value: any, \r\n    configType: keyof typeof cacheConfigs = 'temp',\r\n    customTTL?: number\r\n  ): Promise<boolean> {\r\n    if (!this.connected) {\r\n      logger.warn('Redis cache not connected, skipping set operation');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      const ttl = customTTL || config.ttl;\r\n\r\n      let dataToStore: string;\r\n      if (config.serialize) {\r\n        dataToStore = this.serialize(value);\r\n      } else {\r\n        dataToStore = String(value);\r\n      }\r\n\r\n      await this.client.setEx(cacheKey, ttl, dataToStore);\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Cache set error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete data from cache\r\n   */\r\n  async del(key: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      const result = await this.client.del(cacheKey);\r\n      return result > 0;\r\n    } catch (error) {\r\n      logger.error('Cache delete error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if key exists in cache\r\n   */\r\n  async exists(key: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      const result = await this.client.exists(cacheKey);\r\n      return result > 0;\r\n    } catch (error) {\r\n      logger.error('Cache exists error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set expiration for a key\r\n   */\r\n  async expire(key: string, ttl: number, configType: keyof typeof cacheConfigs = 'temp'): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      const result = await this.client.expire(cacheKey, ttl);\r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Cache expire error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get multiple keys at once\r\n   */\r\n  async mget<T>(keys: string[], configType: keyof typeof cacheConfigs = 'temp'): Promise<(T | null)[]> {\r\n    if (!this.connected || keys.length === 0) {\r\n      return keys.map(() => null);\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKeys = keys.map(key => this.generateKey(key, config));\r\n      const results = await this.client.mGet(cacheKeys);\r\n\r\n      return results.map(data => {\r\n        if (!data) return null;\r\n        return config.serialize ? this.deserialize(data) : data as T;\r\n      });\r\n    } catch (error) {\r\n      logger.error('Cache mget error:', error);\r\n      return keys.map(() => null);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set multiple keys at once\r\n   */\r\n  async mset(\r\n    keyValuePairs: Array<{ key: string; value: any }>, \r\n    configType: keyof typeof cacheConfigs = 'temp',\r\n    customTTL?: number\r\n  ): Promise<boolean> {\r\n    if (!this.connected || keyValuePairs.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const ttl = customTTL || config.ttl;\r\n\r\n      // Use pipeline for better performance\r\n      const pipeline = this.client.multi();\r\n\r\n      for (const { key, value } of keyValuePairs) {\r\n        const cacheKey = this.generateKey(key, config);\r\n        const dataToStore = config.serialize ? this.serialize(value) : String(value);\r\n        pipeline.setEx(cacheKey, ttl, dataToStore);\r\n      }\r\n\r\n      await pipeline.exec();\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Cache mset error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete keys by pattern\r\n   */\r\n  async delPattern(pattern: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<number> {\r\n    if (!this.connected) {\r\n      return 0;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const searchPattern = this.generateKey(pattern, config);\r\n      const keys = await this.client.keys(searchPattern);\r\n\r\n      if (keys.length === 0) {\r\n        return 0;\r\n      }\r\n\r\n      const result = await this.client.del(keys);\r\n      return result;\r\n    } catch (error) {\r\n      logger.error('Cache delete pattern error:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Increment a numeric value\r\n   */\r\n  async incr(key: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<number> {\r\n    if (!this.connected) {\r\n      return 0;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      return await this.client.incr(cacheKey);\r\n    } catch (error) {\r\n      logger.error('Cache increment error:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Decrement a numeric value\r\n   */\r\n  async decr(key: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<number> {\r\n    if (!this.connected) {\r\n      return 0;\r\n    }\r\n\r\n    try {\r\n      const config = cacheConfigs[configType];\r\n      const cacheKey = this.generateKey(key, config);\r\n      return await this.client.decr(cacheKey);\r\n    } catch (error) {\r\n      logger.error('Cache decrement error:', error);\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  async getStats(): Promise<any> {\r\n    if (!this.connected) {\r\n      return { connected: false };\r\n    }\r\n\r\n    try {\r\n      const info = await this.client.info('memory');\r\n      const keyspace = await this.client.info('keyspace');\r\n      \r\n      return {\r\n        connected: this.connected,\r\n        memory: info,\r\n        keyspace: keyspace,\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    } catch (error) {\r\n      logger.error('Cache stats error:', error);\r\n      return { connected: false, error: error.message };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all cache data (use with caution)\r\n   */\r\n  async flushAll(): Promise<boolean> {\r\n    if (!this.connected) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      await this.client.flushAll();\r\n      logger.warn('All cache data has been cleared');\r\n      return true;\r\n    } catch (error) {\r\n      logger.error('Cache flush error:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get connection status\r\n   */\r\n  isConnected(): boolean {\r\n    return this.connected;\r\n  }\r\n\r\n  /**\r\n   * Get Redis client (for advanced operations)\r\n   */\r\n  getClient(): RedisClientType {\r\n    return this.client;\r\n  }\r\n}\r\n\r\n// Create and export singleton instance\r\nexport const cacheService = new CacheService();\r\n\r\n// Helper functions for common caching patterns\r\nexport const cacheHelpers = {\r\n  /**\r\n   * Cache wrapper for database queries\r\n   */\r\n  async cacheQuery<T>(\r\n    key: string,\r\n    queryFn: () => Promise<T>,\r\n    configType: keyof typeof cacheConfigs = 'temp',\r\n    customTTL?: number\r\n  ): Promise<T> {\r\n    // Try to get from cache first\r\n    const cached = await cacheService.get<T>(key, configType);\r\n    if (cached !== null) {\r\n      return cached;\r\n    }\r\n\r\n    // Execute query and cache result\r\n    const result = await queryFn();\r\n    await cacheService.set(key, result, configType, customTTL);\r\n    return result;\r\n  },\r\n\r\n  /**\r\n   * Invalidate cache for a specific pattern\r\n   */\r\n  async invalidatePattern(pattern: string, configType: keyof typeof cacheConfigs = 'temp'): Promise<number> {\r\n    return await cacheService.delPattern(pattern, configType);\r\n  },\r\n\r\n  /**\r\n   * Cache user data\r\n   */\r\n  async cacheUser(userId: string, userData: any): Promise<boolean> {\r\n    return await cacheService.set(userId, userData, 'user');\r\n  },\r\n\r\n  /**\r\n   * Get cached user data\r\n   */\r\n  async getCachedUser(userId: string): Promise<any> {\r\n    return await cacheService.get(userId, 'user');\r\n  },\r\n\r\n  /**\r\n   * Cache property data\r\n   */\r\n  async cacheProperty(propertyId: string, propertyData: any): Promise<boolean> {\r\n    return await cacheService.set(propertyId, propertyData, 'property');\r\n  },\r\n\r\n  /**\r\n   * Get cached property data\r\n   */\r\n  async getCachedProperty(propertyId: string): Promise<any> {\r\n    return await cacheService.get(propertyId, 'property');\r\n  },\r\n\r\n  /**\r\n   * Cache search results\r\n   */\r\n  async cacheSearchResults(searchKey: string, results: any): Promise<boolean> {\r\n    return await cacheService.set(searchKey, results, 'search');\r\n  },\r\n\r\n  /**\r\n   * Get cached search results\r\n   */\r\n  async getCachedSearchResults(searchKey: string): Promise<any> {\r\n    return await cacheService.get(searchKey, 'search');\r\n  }\r\n};\r\n\r\nexport default cacheService;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0887c2deb5493ce0bca95ab98c34c3454ba7cbab"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ynnhg1su5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ynnhg1su5();
cov_1ynnhg1su5().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1ynnhg1su5().s[1]++;
exports.cacheHelpers = exports.cacheService = exports.cacheConfigs = void 0;
const redis_1 =
/* istanbul ignore next */
(cov_1ynnhg1su5().s[2]++, require("redis"));
const environment_1 =
/* istanbul ignore next */
(cov_1ynnhg1su5().s[3]++, require("../config/environment"));
const logger_1 =
/* istanbul ignore next */
(cov_1ynnhg1su5().s[4]++, require("../utils/logger"));
// Default cache configurations for different data types
/* istanbul ignore next */
cov_1ynnhg1su5().s[5]++;
exports.cacheConfigs = {
  // User data - moderate TTL
  user: {
    ttl: 15 * 60,
    // 15 minutes
    prefix: 'user:',
    serialize: true
  },
  // Property data - longer TTL as it changes less frequently
  property: {
    ttl: 30 * 60,
    // 30 minutes
    prefix: 'property:',
    serialize: true
  },
  // Search results - shorter TTL as they need to be fresh
  search: {
    ttl: 5 * 60,
    // 5 minutes
    prefix: 'search:',
    serialize: true,
    compress: true
  },
  // Static data - very long TTL
  static: {
    ttl: 24 * 60 * 60,
    // 24 hours
    prefix: 'static:',
    serialize: true
  },
  // Session data - short TTL
  session: {
    ttl: 60 * 60,
    // 1 hour
    prefix: 'session:',
    serialize: true
  },
  // Temporary data - very short TTL
  temp: {
    ttl: 5 * 60,
    // 5 minutes
    prefix: 'temp:',
    serialize: true
  },
  // Analytics data - medium TTL
  analytics: {
    ttl: 60 * 60,
    // 1 hour
    prefix: 'analytics:',
    serialize: true
  },
  // Notification data - short TTL
  notification: {
    ttl: 10 * 60,
    // 10 minutes
    prefix: 'notification:',
    serialize: true
  }
};
class CacheService {
  constructor() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[0]++;
    cov_1ynnhg1su5().s[6]++;
    this.connected = false;
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[7]++;
    this.retryAttempts = 0;
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[8]++;
    this.maxRetries = 5;
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[9]++;
    this.client = (0, redis_1.createClient)({
      url: environment_1.config.REDIS_URL,
      socket: {
        connectTimeout: 5000,
        lazyConnect: true,
        reconnectStrategy: retries => {
          /* istanbul ignore next */
          cov_1ynnhg1su5().f[1]++;
          cov_1ynnhg1su5().s[10]++;
          if (retries > this.maxRetries) {
            /* istanbul ignore next */
            cov_1ynnhg1su5().b[0][0]++;
            cov_1ynnhg1su5().s[11]++;
            logger_1.logger.error('Redis cache: Max reconnection attempts reached');
            /* istanbul ignore next */
            cov_1ynnhg1su5().s[12]++;
            return false;
          } else
          /* istanbul ignore next */
          {
            cov_1ynnhg1su5().b[0][1]++;
          }
          cov_1ynnhg1su5().s[13]++;
          return Math.min(retries * 100, 3000);
        }
      }
    });
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[14]++;
    this.setupEventHandlers();
  }
  setupEventHandlers() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[2]++;
    cov_1ynnhg1su5().s[15]++;
    this.client.on('connect', () => {
      /* istanbul ignore next */
      cov_1ynnhg1su5().f[3]++;
      cov_1ynnhg1su5().s[16]++;
      logger_1.logger.info('Redis cache client connecting...');
    });
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[17]++;
    this.client.on('ready', () => {
      /* istanbul ignore next */
      cov_1ynnhg1su5().f[4]++;
      cov_1ynnhg1su5().s[18]++;
      this.connected = true;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[19]++;
      this.retryAttempts = 0;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[20]++;
      logger_1.logger.info('Redis cache client connected and ready');
    });
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[21]++;
    this.client.on('error', err => {
      /* istanbul ignore next */
      cov_1ynnhg1su5().f[5]++;
      cov_1ynnhg1su5().s[22]++;
      this.connected = false;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[23]++;
      logger_1.logger.error('Redis cache client error:', err);
    });
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[24]++;
    this.client.on('end', () => {
      /* istanbul ignore next */
      cov_1ynnhg1su5().f[6]++;
      cov_1ynnhg1su5().s[25]++;
      this.connected = false;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[26]++;
      logger_1.logger.warn('Redis cache client connection ended');
    });
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[27]++;
    this.client.on('reconnecting', () => {
      /* istanbul ignore next */
      cov_1ynnhg1su5().f[7]++;
      cov_1ynnhg1su5().s[28]++;
      this.retryAttempts++;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[29]++;
      logger_1.logger.info(`Redis cache client reconnecting... (attempt ${this.retryAttempts})`);
    });
  }
  async connect() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[8]++;
    cov_1ynnhg1su5().s[30]++;
    try {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[31]++;
      if (!this.connected) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[1][0]++;
        cov_1ynnhg1su5().s[32]++;
        await this.client.connect();
      } else
      /* istanbul ignore next */
      {
        cov_1ynnhg1su5().b[1][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[33]++;
      logger_1.logger.error('Failed to connect to Redis cache:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[34]++;
      throw error;
    }
  }
  async disconnect() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[9]++;
    cov_1ynnhg1su5().s[35]++;
    try {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[36]++;
      if (this.connected) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[2][0]++;
        cov_1ynnhg1su5().s[37]++;
        await this.client.quit();
        /* istanbul ignore next */
        cov_1ynnhg1su5().s[38]++;
        this.connected = false;
      } else
      /* istanbul ignore next */
      {
        cov_1ynnhg1su5().b[2][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[39]++;
      logger_1.logger.error('Error disconnecting from Redis cache:', error);
    }
  }
  generateKey(key, config) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[10]++;
    cov_1ynnhg1su5().s[40]++;
    return `${config.prefix}${key}`;
  }
  serialize(data) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[11]++;
    cov_1ynnhg1su5().s[41]++;
    try {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[42]++;
      return JSON.stringify(data);
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[43]++;
      logger_1.logger.error('Cache serialization error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[44]++;
      return '';
    }
  }
  deserialize(data) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[12]++;
    cov_1ynnhg1su5().s[45]++;
    try {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[46]++;
      return JSON.parse(data);
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[47]++;
      logger_1.logger.error('Cache deserialization error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[48]++;
      return null;
    }
  }
  /**
   * Get data from cache
   */
  async get(key, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[3][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[13]++;
    cov_1ynnhg1su5().s[49]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[4][0]++;
      cov_1ynnhg1su5().s[50]++;
      logger_1.logger.warn('Redis cache not connected, skipping get operation');
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[51]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[4][1]++;
    }
    cov_1ynnhg1su5().s[52]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[53]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[54]++, this.generateKey(key, config));
      const data =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[55]++, await this.client.get(cacheKey));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[56]++;
      if (!data) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[5][0]++;
        cov_1ynnhg1su5().s[57]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1ynnhg1su5().b[5][1]++;
      }
      cov_1ynnhg1su5().s[58]++;
      if (config.serialize) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[6][0]++;
        cov_1ynnhg1su5().s[59]++;
        return this.deserialize(data);
      } else
      /* istanbul ignore next */
      {
        cov_1ynnhg1su5().b[6][1]++;
      }
      cov_1ynnhg1su5().s[60]++;
      return data;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[61]++;
      logger_1.logger.error('Cache get error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[62]++;
      return null;
    }
  }
  /**
   * Set data in cache
   */
  async set(key, value, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[7][0]++, 'temp'), customTTL) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[14]++;
    cov_1ynnhg1su5().s[63]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[8][0]++;
      cov_1ynnhg1su5().s[64]++;
      logger_1.logger.warn('Redis cache not connected, skipping set operation');
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[65]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[8][1]++;
    }
    cov_1ynnhg1su5().s[66]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[67]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[68]++, this.generateKey(key, config));
      const ttl =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[69]++,
      /* istanbul ignore next */
      (cov_1ynnhg1su5().b[9][0]++, customTTL) ||
      /* istanbul ignore next */
      (cov_1ynnhg1su5().b[9][1]++, config.ttl));
      let dataToStore;
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[70]++;
      if (config.serialize) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[10][0]++;
        cov_1ynnhg1su5().s[71]++;
        dataToStore = this.serialize(value);
      } else {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[10][1]++;
        cov_1ynnhg1su5().s[72]++;
        dataToStore = String(value);
      }
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[73]++;
      await this.client.setEx(cacheKey, ttl, dataToStore);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[74]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[75]++;
      logger_1.logger.error('Cache set error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[76]++;
      return false;
    }
  }
  /**
   * Delete data from cache
   */
  async del(key, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[11][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[15]++;
    cov_1ynnhg1su5().s[77]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[12][0]++;
      cov_1ynnhg1su5().s[78]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[12][1]++;
    }
    cov_1ynnhg1su5().s[79]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[80]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[81]++, this.generateKey(key, config));
      const result =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[82]++, await this.client.del(cacheKey));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[83]++;
      return result > 0;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[84]++;
      logger_1.logger.error('Cache delete error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[85]++;
      return false;
    }
  }
  /**
   * Check if key exists in cache
   */
  async exists(key, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[13][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[16]++;
    cov_1ynnhg1su5().s[86]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[14][0]++;
      cov_1ynnhg1su5().s[87]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[14][1]++;
    }
    cov_1ynnhg1su5().s[88]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[89]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[90]++, this.generateKey(key, config));
      const result =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[91]++, await this.client.exists(cacheKey));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[92]++;
      return result > 0;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[93]++;
      logger_1.logger.error('Cache exists error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[94]++;
      return false;
    }
  }
  /**
   * Set expiration for a key
   */
  async expire(key, ttl, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[15][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[17]++;
    cov_1ynnhg1su5().s[95]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[16][0]++;
      cov_1ynnhg1su5().s[96]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[16][1]++;
    }
    cov_1ynnhg1su5().s[97]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[98]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[99]++, this.generateKey(key, config));
      const result =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[100]++, await this.client.expire(cacheKey, ttl));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[101]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[102]++;
      logger_1.logger.error('Cache expire error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[103]++;
      return false;
    }
  }
  /**
   * Get multiple keys at once
   */
  async mget(keys, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[17][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[18]++;
    cov_1ynnhg1su5().s[104]++;
    if (
    /* istanbul ignore next */
    (cov_1ynnhg1su5().b[19][0]++, !this.connected) ||
    /* istanbul ignore next */
    (cov_1ynnhg1su5().b[19][1]++, keys.length === 0)) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[18][0]++;
      cov_1ynnhg1su5().s[105]++;
      return keys.map(() => {
        /* istanbul ignore next */
        cov_1ynnhg1su5().f[19]++;
        cov_1ynnhg1su5().s[106]++;
        return null;
      });
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[18][1]++;
    }
    cov_1ynnhg1su5().s[107]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[108]++, exports.cacheConfigs[configType]);
      const cacheKeys =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[109]++, keys.map(key => {
        /* istanbul ignore next */
        cov_1ynnhg1su5().f[20]++;
        cov_1ynnhg1su5().s[110]++;
        return this.generateKey(key, config);
      }));
      const results =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[111]++, await this.client.mGet(cacheKeys));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[112]++;
      return results.map(data => {
        /* istanbul ignore next */
        cov_1ynnhg1su5().f[21]++;
        cov_1ynnhg1su5().s[113]++;
        if (!data) {
          /* istanbul ignore next */
          cov_1ynnhg1su5().b[20][0]++;
          cov_1ynnhg1su5().s[114]++;
          return null;
        } else
        /* istanbul ignore next */
        {
          cov_1ynnhg1su5().b[20][1]++;
        }
        cov_1ynnhg1su5().s[115]++;
        return config.serialize ?
        /* istanbul ignore next */
        (cov_1ynnhg1su5().b[21][0]++, this.deserialize(data)) :
        /* istanbul ignore next */
        (cov_1ynnhg1su5().b[21][1]++, data);
      });
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[116]++;
      logger_1.logger.error('Cache mget error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[117]++;
      return keys.map(() => {
        /* istanbul ignore next */
        cov_1ynnhg1su5().f[22]++;
        cov_1ynnhg1su5().s[118]++;
        return null;
      });
    }
  }
  /**
   * Set multiple keys at once
   */
  async mset(keyValuePairs, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[22][0]++, 'temp'), customTTL) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[23]++;
    cov_1ynnhg1su5().s[119]++;
    if (
    /* istanbul ignore next */
    (cov_1ynnhg1su5().b[24][0]++, !this.connected) ||
    /* istanbul ignore next */
    (cov_1ynnhg1su5().b[24][1]++, keyValuePairs.length === 0)) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[23][0]++;
      cov_1ynnhg1su5().s[120]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[23][1]++;
    }
    cov_1ynnhg1su5().s[121]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[122]++, exports.cacheConfigs[configType]);
      const ttl =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[123]++,
      /* istanbul ignore next */
      (cov_1ynnhg1su5().b[25][0]++, customTTL) ||
      /* istanbul ignore next */
      (cov_1ynnhg1su5().b[25][1]++, config.ttl));
      // Use pipeline for better performance
      const pipeline =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[124]++, this.client.multi());
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[125]++;
      for (const {
        key,
        value
      } of keyValuePairs) {
        const cacheKey =
        /* istanbul ignore next */
        (cov_1ynnhg1su5().s[126]++, this.generateKey(key, config));
        const dataToStore =
        /* istanbul ignore next */
        (cov_1ynnhg1su5().s[127]++, config.serialize ?
        /* istanbul ignore next */
        (cov_1ynnhg1su5().b[26][0]++, this.serialize(value)) :
        /* istanbul ignore next */
        (cov_1ynnhg1su5().b[26][1]++, String(value)));
        /* istanbul ignore next */
        cov_1ynnhg1su5().s[128]++;
        pipeline.setEx(cacheKey, ttl, dataToStore);
      }
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[129]++;
      await pipeline.exec();
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[130]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[131]++;
      logger_1.logger.error('Cache mset error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[132]++;
      return false;
    }
  }
  /**
   * Delete keys by pattern
   */
  async delPattern(pattern, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[27][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[24]++;
    cov_1ynnhg1su5().s[133]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[28][0]++;
      cov_1ynnhg1su5().s[134]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[28][1]++;
    }
    cov_1ynnhg1su5().s[135]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[136]++, exports.cacheConfigs[configType]);
      const searchPattern =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[137]++, this.generateKey(pattern, config));
      const keys =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[138]++, await this.client.keys(searchPattern));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[139]++;
      if (keys.length === 0) {
        /* istanbul ignore next */
        cov_1ynnhg1su5().b[29][0]++;
        cov_1ynnhg1su5().s[140]++;
        return 0;
      } else
      /* istanbul ignore next */
      {
        cov_1ynnhg1su5().b[29][1]++;
      }
      const result =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[141]++, await this.client.del(keys));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[142]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[143]++;
      logger_1.logger.error('Cache delete pattern error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[144]++;
      return 0;
    }
  }
  /**
   * Increment a numeric value
   */
  async incr(key, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[30][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[25]++;
    cov_1ynnhg1su5().s[145]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[31][0]++;
      cov_1ynnhg1su5().s[146]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[31][1]++;
    }
    cov_1ynnhg1su5().s[147]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[148]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[149]++, this.generateKey(key, config));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[150]++;
      return await this.client.incr(cacheKey);
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[151]++;
      logger_1.logger.error('Cache increment error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[152]++;
      return 0;
    }
  }
  /**
   * Decrement a numeric value
   */
  async decr(key, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[32][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[26]++;
    cov_1ynnhg1su5().s[153]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[33][0]++;
      cov_1ynnhg1su5().s[154]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[33][1]++;
    }
    cov_1ynnhg1su5().s[155]++;
    try {
      const config =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[156]++, exports.cacheConfigs[configType]);
      const cacheKey =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[157]++, this.generateKey(key, config));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[158]++;
      return await this.client.decr(cacheKey);
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[159]++;
      logger_1.logger.error('Cache decrement error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[160]++;
      return 0;
    }
  }
  /**
   * Get cache statistics
   */
  async getStats() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[27]++;
    cov_1ynnhg1su5().s[161]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[34][0]++;
      cov_1ynnhg1su5().s[162]++;
      return {
        connected: false
      };
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[34][1]++;
    }
    cov_1ynnhg1su5().s[163]++;
    try {
      const info =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[164]++, await this.client.info('memory'));
      const keyspace =
      /* istanbul ignore next */
      (cov_1ynnhg1su5().s[165]++, await this.client.info('keyspace'));
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[166]++;
      return {
        connected: this.connected,
        memory: info,
        keyspace: keyspace,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[167]++;
      logger_1.logger.error('Cache stats error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[168]++;
      return {
        connected: false,
        error: error.message
      };
    }
  }
  /**
   * Clear all cache data (use with caution)
   */
  async flushAll() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[28]++;
    cov_1ynnhg1su5().s[169]++;
    if (!this.connected) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[35][0]++;
      cov_1ynnhg1su5().s[170]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[35][1]++;
    }
    cov_1ynnhg1su5().s[171]++;
    try {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[172]++;
      await this.client.flushAll();
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[173]++;
      logger_1.logger.warn('All cache data has been cleared');
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[174]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[175]++;
      logger_1.logger.error('Cache flush error:', error);
      /* istanbul ignore next */
      cov_1ynnhg1su5().s[176]++;
      return false;
    }
  }
  /**
   * Get connection status
   */
  isConnected() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[29]++;
    cov_1ynnhg1su5().s[177]++;
    return this.connected;
  }
  /**
   * Get Redis client (for advanced operations)
   */
  getClient() {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[30]++;
    cov_1ynnhg1su5().s[178]++;
    return this.client;
  }
}
// Create and export singleton instance
/* istanbul ignore next */
cov_1ynnhg1su5().s[179]++;
exports.cacheService = new CacheService();
// Helper functions for common caching patterns
/* istanbul ignore next */
cov_1ynnhg1su5().s[180]++;
exports.cacheHelpers = {
  /**
   * Cache wrapper for database queries
   */
  async cacheQuery(key, queryFn, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[36][0]++, 'temp'), customTTL) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[31]++;
    // Try to get from cache first
    const cached =
    /* istanbul ignore next */
    (cov_1ynnhg1su5().s[181]++, await exports.cacheService.get(key, configType));
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[182]++;
    if (cached !== null) {
      /* istanbul ignore next */
      cov_1ynnhg1su5().b[37][0]++;
      cov_1ynnhg1su5().s[183]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_1ynnhg1su5().b[37][1]++;
    }
    // Execute query and cache result
    const result =
    /* istanbul ignore next */
    (cov_1ynnhg1su5().s[184]++, await queryFn());
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[185]++;
    await exports.cacheService.set(key, result, configType, customTTL);
    /* istanbul ignore next */
    cov_1ynnhg1su5().s[186]++;
    return result;
  },
  /**
   * Invalidate cache for a specific pattern
   */
  async invalidatePattern(pattern, configType =
  /* istanbul ignore next */
  (cov_1ynnhg1su5().b[38][0]++, 'temp')) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[32]++;
    cov_1ynnhg1su5().s[187]++;
    return await exports.cacheService.delPattern(pattern, configType);
  },
  /**
   * Cache user data
   */
  async cacheUser(userId, userData) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[33]++;
    cov_1ynnhg1su5().s[188]++;
    return await exports.cacheService.set(userId, userData, 'user');
  },
  /**
   * Get cached user data
   */
  async getCachedUser(userId) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[34]++;
    cov_1ynnhg1su5().s[189]++;
    return await exports.cacheService.get(userId, 'user');
  },
  /**
   * Cache property data
   */
  async cacheProperty(propertyId, propertyData) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[35]++;
    cov_1ynnhg1su5().s[190]++;
    return await exports.cacheService.set(propertyId, propertyData, 'property');
  },
  /**
   * Get cached property data
   */
  async getCachedProperty(propertyId) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[36]++;
    cov_1ynnhg1su5().s[191]++;
    return await exports.cacheService.get(propertyId, 'property');
  },
  /**
   * Cache search results
   */
  async cacheSearchResults(searchKey, results) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[37]++;
    cov_1ynnhg1su5().s[192]++;
    return await exports.cacheService.set(searchKey, results, 'search');
  },
  /**
   * Get cached search results
   */
  async getCachedSearchResults(searchKey) {
    /* istanbul ignore next */
    cov_1ynnhg1su5().f[38]++;
    cov_1ynnhg1su5().s[193]++;
    return await exports.cacheService.get(searchKey, 'search');
  }
};
/* istanbul ignore next */
cov_1ynnhg1su5().s[194]++;
exports.default = exports.cacheService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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