b574a014a87101565770bf199d7e8557
"use strict";

/* istanbul ignore next */
function cov_4ciu2k62p() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\profile.routes.ts";
  var hash = "a816afd9b0b4f7fb207431a8185166a1a46aed89";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\profile.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 18
        },
        end: {
          line: 3,
          column: 36
        }
      },
      "2": {
        start: {
          line: 4,
          column: 15
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 26
        },
        end: {
          line: 5,
          column: 66
        }
      },
      "4": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 6,
          column: 72
        }
      },
      "5": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 7,
          column: 73
        }
      },
      "6": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "8": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 21,
          column: 7
        }
      },
      "9": {
        start: {
          line: 24,
          column: 0
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "10": {
        start: {
          line: 25,
          column: 22
        },
        end: {
          line: 25,
          column: 81
        }
      },
      "11": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 39,
          column: 5
        }
      },
      "12": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 37,
          column: 11
        }
      },
      "13": {
        start: {
          line: 32,
          column: 54
        },
        end: {
          line: 35,
          column: 17
        }
      },
      "14": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 15
        }
      },
      "15": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 11
        }
      },
      "16": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 32
        }
      },
      "17": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 49
        }
      },
      "18": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 136
        }
      },
      "19": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 153
        }
      },
      "20": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 69
        }
      },
      "21": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 160
        }
      },
      "22": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 162
        }
      },
      "23": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "24": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 22
          },
          end: {
            line: 10,
            column: 23
          }
        },
        loc: {
          start: {
            line: 10,
            column: 37
          },
          end: {
            line: 22,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 24,
            column: 44
          },
          end: {
            line: 24,
            column: 45
          }
        },
        loc: {
          start: {
            line: 24,
            column: 64
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 24
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 32,
            column: 43
          },
          end: {
            line: 32,
            column: 44
          }
        },
        loc: {
          start: {
            line: 32,
            column: 54
          },
          end: {
            line: 35,
            column: 17
          }
        },
        line: 32
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 39,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 39,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\profile.routes.ts",
      mappings: ";;AAAA,qCAAiC;AACjC,6CAAgE;AAChE,mEAAgE;AAChE,yEAM0C;AAC1C,0EAO2C;AAE3C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,eAAe;AACf,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAClC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,wBAAwB;QACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE;YACT,UAAU,EAAE,OAAO;YACnB,aAAa,EAAE,SAAS;YACxB,gBAAgB,EAAE,cAAc;YAChC,aAAa,EAAE,gBAAgB;YAC/B,aAAa,EAAE,iBAAiB;SACjC;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,CAAC,UAAU,EACnB,mBAAY,EACZ,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACjB,MAAM,EAAE,KAAK,EAAE,GAAG,sCAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzD,IAAI,KAAK,EAAE,CAAC;QACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACpC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;iBACxB,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,EACD,qCAAgB,CACjB,CAAC;AAEF,6CAA6C;AAC7C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB,6BAA6B;AAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,+BAAU,CAAC,CAAC;AAE5B,iBAAiB;AACjB,MAAM,CAAC,KAAK,CAAC,GAAG,EACd,IAAA,iCAAe,EAAC,wCAAmB,CAAC,EACpC,kCAAa,CACd,CAAC;AAEF,0BAA0B;AAC1B,MAAM,CAAC,KAAK,CAAC,UAAU,EACrB,IAAA,iCAAe,EAAC,0CAAqB,CAAC,EACtC,0CAAqB,CACtB,CAAC;AAEF,gCAAgC;AAChC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,yCAAoB,CAAC,CAAC;AAEhD,6BAA6B;AAC7B,MAAM,CAAC,KAAK,CAAC,sBAAsB,EACjC,IAAA,iCAAe,EAAC,6CAAwB,CAAC,EACzC,kCAAa,CACd,CAAC;AAEF,8BAA8B;AAC9B,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAClC,IAAA,iCAAe,EAAC,8CAAyB,CAAC,EAC1C,kCAAa,CACd,CAAC;AAEF,+BAA+B;AAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,kCAAa,CAAC,CAAC;AAElC,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\profile.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport { authenticate, optionalAuth } from '../middleware/auth';\r\nimport { validateRequest } from '../validators/auth.validators';\r\nimport {\r\n  updateProfileSchema,\r\n  privacySettingsSchema,\r\n  housingPreferencesSchema,\r\n  roommatePreferencesSchema,\r\n  userIdParamSchema\r\n} from '../validators/profile.validators';\r\nimport {\r\n  getProfile,\r\n  updateProfile,\r\n  getPublicProfile,\r\n  updatePrivacySettings,\r\n  getProfileCompletion,\r\n  deleteProfile\r\n} from '../controllers/profile.controller';\r\n\r\nconst router = Router();\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({\r\n    message: 'Profile routes working',\r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      getProfile: 'GET /',\r\n      updateProfile: 'PATCH /',\r\n      getPublicProfile: 'GET /:userId',\r\n      updatePrivacy: 'PATCH /privacy',\r\n      getCompletion: 'GET /completion'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes (optional authentication)\r\nrouter.get('/:userId',\r\n  optionalAuth,\r\n  (req, res, next) => {\r\n    const { error } = userIdParamSchema.validate(req.params);\r\n    if (error) {\r\n      res.status(400).json({\r\n        success: false,\r\n        error: {\r\n          message: 'Validation failed',\r\n          code: 'VALIDATION_ERROR',\r\n          details: error.details.map(detail => ({\r\n            field: detail.path.join('.'),\r\n            message: detail.message\r\n          }))\r\n        }\r\n      });\r\n      return;\r\n    }\r\n    next();\r\n  },\r\n  getPublicProfile\r\n);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Get current user's profile\r\nrouter.get('/', getProfile);\r\n\r\n// Update profile\r\nrouter.patch('/',\r\n  validateRequest(updateProfileSchema),\r\n  updateProfile\r\n);\r\n\r\n// Update privacy settings\r\nrouter.patch('/privacy',\r\n  validateRequest(privacySettingsSchema),\r\n  updatePrivacySettings\r\n);\r\n\r\n// Get profile completion status\r\nrouter.get('/completion', getProfileCompletion);\r\n\r\n// Update housing preferences\r\nrouter.patch('/housing-preferences',\r\n  validateRequest(housingPreferencesSchema),\r\n  updateProfile\r\n);\r\n\r\n// Update roommate preferences\r\nrouter.patch('/roommate-preferences',\r\n  validateRequest(roommatePreferencesSchema),\r\n  updateProfile\r\n);\r\n\r\n// Delete profile (soft delete)\r\nrouter.delete('/', deleteProfile);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a816afd9b0b4f7fb207431a8185166a1a46aed89"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_4ciu2k62p = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_4ciu2k62p();
cov_4ciu2k62p().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_4ciu2k62p().s[1]++, require("express"));
const auth_1 =
/* istanbul ignore next */
(cov_4ciu2k62p().s[2]++, require("../middleware/auth"));
const auth_validators_1 =
/* istanbul ignore next */
(cov_4ciu2k62p().s[3]++, require("../validators/auth.validators"));
const profile_validators_1 =
/* istanbul ignore next */
(cov_4ciu2k62p().s[4]++, require("../validators/profile.validators"));
const profile_controller_1 =
/* istanbul ignore next */
(cov_4ciu2k62p().s[5]++, require("../controllers/profile.controller"));
const router =
/* istanbul ignore next */
(cov_4ciu2k62p().s[6]++, (0, express_1.Router)());
// Health check
/* istanbul ignore next */
cov_4ciu2k62p().s[7]++;
router.get('/health', (_req, res) => {
  /* istanbul ignore next */
  cov_4ciu2k62p().f[0]++;
  cov_4ciu2k62p().s[8]++;
  res.json({
    message: 'Profile routes working',
    timestamp: new Date().toISOString(),
    endpoints: {
      getProfile: 'GET /',
      updateProfile: 'PATCH /',
      getPublicProfile: 'GET /:userId',
      updatePrivacy: 'PATCH /privacy',
      getCompletion: 'GET /completion'
    }
  });
});
// Public routes (optional authentication)
/* istanbul ignore next */
cov_4ciu2k62p().s[9]++;
router.get('/:userId', auth_1.optionalAuth, (req, res, next) => {
  /* istanbul ignore next */
  cov_4ciu2k62p().f[1]++;
  const {
    error
  } =
  /* istanbul ignore next */
  (cov_4ciu2k62p().s[10]++, profile_validators_1.userIdParamSchema.validate(req.params));
  /* istanbul ignore next */
  cov_4ciu2k62p().s[11]++;
  if (error) {
    /* istanbul ignore next */
    cov_4ciu2k62p().b[0][0]++;
    cov_4ciu2k62p().s[12]++;
    res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => {
          /* istanbul ignore next */
          cov_4ciu2k62p().f[2]++;
          cov_4ciu2k62p().s[13]++;
          return {
            field: detail.path.join('.'),
            message: detail.message
          };
        })
      }
    });
    /* istanbul ignore next */
    cov_4ciu2k62p().s[14]++;
    return;
  } else
  /* istanbul ignore next */
  {
    cov_4ciu2k62p().b[0][1]++;
  }
  cov_4ciu2k62p().s[15]++;
  next();
}, profile_controller_1.getPublicProfile);
// Protected routes (authentication required)
/* istanbul ignore next */
cov_4ciu2k62p().s[16]++;
router.use(auth_1.authenticate);
// Get current user's profile
/* istanbul ignore next */
cov_4ciu2k62p().s[17]++;
router.get('/', profile_controller_1.getProfile);
// Update profile
/* istanbul ignore next */
cov_4ciu2k62p().s[18]++;
router.patch('/', (0, auth_validators_1.validateRequest)(profile_validators_1.updateProfileSchema), profile_controller_1.updateProfile);
// Update privacy settings
/* istanbul ignore next */
cov_4ciu2k62p().s[19]++;
router.patch('/privacy', (0, auth_validators_1.validateRequest)(profile_validators_1.privacySettingsSchema), profile_controller_1.updatePrivacySettings);
// Get profile completion status
/* istanbul ignore next */
cov_4ciu2k62p().s[20]++;
router.get('/completion', profile_controller_1.getProfileCompletion);
// Update housing preferences
/* istanbul ignore next */
cov_4ciu2k62p().s[21]++;
router.patch('/housing-preferences', (0, auth_validators_1.validateRequest)(profile_validators_1.housingPreferencesSchema), profile_controller_1.updateProfile);
// Update roommate preferences
/* istanbul ignore next */
cov_4ciu2k62p().s[22]++;
router.patch('/roommate-preferences', (0, auth_validators_1.validateRequest)(profile_validators_1.roommatePreferencesSchema), profile_controller_1.updateProfile);
// Delete profile (soft delete)
/* istanbul ignore next */
cov_4ciu2k62p().s[23]++;
router.delete('/', profile_controller_1.deleteProfile);
/* istanbul ignore next */
cov_4ciu2k62p().s[24]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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