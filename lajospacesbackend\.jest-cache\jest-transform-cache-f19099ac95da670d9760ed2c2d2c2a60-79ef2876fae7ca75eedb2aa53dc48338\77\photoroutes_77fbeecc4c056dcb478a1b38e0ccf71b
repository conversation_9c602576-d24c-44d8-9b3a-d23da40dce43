8b03f0fe8bc0ff975bd7085b1bf929aa
"use strict";

/* istanbul ignore next */
function cov_h3x8wp4e7() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\photo.routes.ts";
  var hash = "df2ac40c1929dcffd82b8bc85903670b798d3722";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\photo.routes.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 18
        },
        end: {
          line: 6,
          column: 36
        }
      },
      "4": {
        start: {
          line: 7,
          column: 17
        },
        end: {
          line: 7,
          column: 51
        }
      },
      "5": {
        start: {
          line: 8,
          column: 15
        },
        end: {
          line: 8,
          column: 44
        }
      },
      "6": {
        start: {
          line: 9,
          column: 27
        },
        end: {
          line: 9,
          column: 69
        }
      },
      "7": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 10,
          column: 38
        }
      },
      "8": {
        start: {
          line: 12,
          column: 15
        },
        end: {
          line: 28,
          column: 2
        }
      },
      "9": {
        start: {
          line: 20,
          column: 29
        },
        end: {
          line: 20,
          column: 83
        }
      },
      "10": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 26,
          column: 9
        }
      },
      "11": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 27
        }
      },
      "12": {
        start: {
          line: 25,
          column: 12
        },
        end: {
          line: 25,
          column: 73
        }
      },
      "13": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 43,
          column: 3
        }
      },
      "14": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 42,
          column: 7
        }
      },
      "15": {
        start: {
          line: 45,
          column: 0
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "16": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 47,
          column: 32
        }
      },
      "17": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 46
        }
      },
      "18": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 79
        }
      },
      "19": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 59
        }
      },
      "20": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 70
        }
      },
      "21": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 57,
          column: 59
        }
      },
      "22": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 18,
            column: 16
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 36
          },
          end: {
            line: 27,
            column: 5
          }
        },
        line: 18
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 30,
            column: 23
          }
        },
        loc: {
          start: {
            line: 30,
            column: 37
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 30
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 26,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 26,
            column: 9
          }
        }, {
          start: {
            line: 24,
            column: 13
          },
          end: {
            line: 26,
            column: 9
          }
        }],
        line: 21
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\photo.routes.ts",
      mappings: ";;;;;AAAA,qCAAiC;AACjC,oDAA4B;AAC5B,6CAAkD;AAClD,sEAOyC;AAEzC,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,sCAAsC;AACtC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC;IACpB,OAAO,EAAE,gBAAM,CAAC,aAAa,EAAE;IAC/B,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,aAAa;QACzC,KAAK,EAAE,CAAC,CAAC,qBAAqB;KAC/B;IACD,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC7B,kBAAkB;QAClB,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QAC5E,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,EAAE,CAAC,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,eAAe;AACf,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;IAClC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,sBAAsB;QAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE;YACT,WAAW,EAAE,cAAc;YAC3B,SAAS,EAAE,OAAO;YAClB,WAAW,EAAE,kBAAkB;YAC/B,UAAU,EAAE,yBAAyB;YACrC,aAAa,EAAE,gBAAgB;YAC/B,UAAU,EAAE,iBAAiB;SAC9B;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,gBAAgB;AAChB,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,sCAAmB,CAAC,CAAC;AAE/C,6CAA6C;AAC7C,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB,oBAAoB;AACpB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,4BAAS,CAAC,CAAC;AAE3B,eAAe;AACf,MAAM,CAAC,IAAI,CAAC,SAAS,EACnB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EACtB,8BAAW,CACZ,CAAC;AAEF,eAAe;AACf,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,8BAAW,CAAC,CAAC;AAExC,oBAAoB;AACpB,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,kCAAe,CAAC,CAAC;AAEnD,iBAAiB;AACjB,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,gCAAa,CAAC,CAAC;AAExC,kBAAe,MAAM,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\photo.routes.ts"],
      sourcesContent: ["import { Router } from 'express';\r\nimport multer from 'multer';\r\nimport { authenticate } from '../middleware/auth';\r\nimport {\r\n  uploadPhoto,\r\n  deletePhoto,\r\n  setPrimaryPhoto,\r\n  getPhotos,\r\n  reorderPhotos,\r\n  getUploadGuidelines\r\n} from '../controllers/photo.controller';\r\n\r\nconst router = Router();\r\n\r\n// Configure multer for memory storage\r\nconst upload = multer({\r\n  storage: multer.memoryStorage(),\r\n  limits: {\r\n    fileSize: 10 * 1024 * 1024, // 10MB limit\r\n    files: 1 // Single file upload\r\n  },\r\n  fileFilter: (_req, file, cb) => {\r\n    // Check file type\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\r\n    if (allowedTypes.includes(file.mimetype)) {\r\n      cb(null, true);\r\n    } else {\r\n      cb(new Error('Only JPEG, PNG, and WebP images are allowed'));\r\n    }\r\n  }\r\n});\r\n\r\n// Health check\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ \r\n    message: 'Photo routes working', \r\n    timestamp: new Date().toISOString(),\r\n    endpoints: {\r\n      uploadPhoto: 'POST /upload',\r\n      getPhotos: 'GET /',\r\n      deletePhoto: 'DELETE /:photoId',\r\n      setPrimary: 'PATCH /:photoId/primary',\r\n      reorderPhotos: 'PATCH /reorder',\r\n      guidelines: 'GET /guidelines'\r\n    }\r\n  });\r\n});\r\n\r\n// Public routes\r\nrouter.get('/guidelines', getUploadGuidelines);\r\n\r\n// Protected routes (authentication required)\r\nrouter.use(authenticate);\r\n\r\n// Get user's photos\r\nrouter.get('/', getPhotos);\r\n\r\n// Upload photo\r\nrouter.post('/upload',\r\n  upload.single('photo'),\r\n  uploadPhoto\r\n);\r\n\r\n// Delete photo\r\nrouter.delete('/:photoId', deletePhoto);\r\n\r\n// Set primary photo\r\nrouter.patch('/:photoId/primary', setPrimaryPhoto);\r\n\r\n// Reorder photos\r\nrouter.patch('/reorder', reorderPhotos);\r\n\r\nexport default router;\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "df2ac40c1929dcffd82b8bc85903670b798d3722"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_h3x8wp4e7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_h3x8wp4e7();
var __importDefault =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[0]++,
/* istanbul ignore next */
(cov_h3x8wp4e7().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_h3x8wp4e7().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_h3x8wp4e7().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_h3x8wp4e7().f[0]++;
  cov_h3x8wp4e7().s[1]++;
  return /* istanbul ignore next */(cov_h3x8wp4e7().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_h3x8wp4e7().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_h3x8wp4e7().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_h3x8wp4e7().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_h3x8wp4e7().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
const express_1 =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[3]++, require("express"));
const multer_1 =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[4]++, __importDefault(require("multer")));
const auth_1 =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[5]++, require("../middleware/auth"));
const photo_controller_1 =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[6]++, require("../controllers/photo.controller"));
const router =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[7]++, (0, express_1.Router)());
// Configure multer for memory storage
const upload =
/* istanbul ignore next */
(cov_h3x8wp4e7().s[8]++, (0, multer_1.default)({
  storage: multer_1.default.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024,
    // 10MB limit
    files: 1 // Single file upload
  },
  fileFilter: (_req, file, cb) => {
    /* istanbul ignore next */
    cov_h3x8wp4e7().f[1]++;
    // Check file type
    const allowedTypes =
    /* istanbul ignore next */
    (cov_h3x8wp4e7().s[9]++, ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']);
    /* istanbul ignore next */
    cov_h3x8wp4e7().s[10]++;
    if (allowedTypes.includes(file.mimetype)) {
      /* istanbul ignore next */
      cov_h3x8wp4e7().b[3][0]++;
      cov_h3x8wp4e7().s[11]++;
      cb(null, true);
    } else {
      /* istanbul ignore next */
      cov_h3x8wp4e7().b[3][1]++;
      cov_h3x8wp4e7().s[12]++;
      cb(new Error('Only JPEG, PNG, and WebP images are allowed'));
    }
  }
}));
// Health check
/* istanbul ignore next */
cov_h3x8wp4e7().s[13]++;
router.get('/health', (_req, res) => {
  /* istanbul ignore next */
  cov_h3x8wp4e7().f[2]++;
  cov_h3x8wp4e7().s[14]++;
  res.json({
    message: 'Photo routes working',
    timestamp: new Date().toISOString(),
    endpoints: {
      uploadPhoto: 'POST /upload',
      getPhotos: 'GET /',
      deletePhoto: 'DELETE /:photoId',
      setPrimary: 'PATCH /:photoId/primary',
      reorderPhotos: 'PATCH /reorder',
      guidelines: 'GET /guidelines'
    }
  });
});
// Public routes
/* istanbul ignore next */
cov_h3x8wp4e7().s[15]++;
router.get('/guidelines', photo_controller_1.getUploadGuidelines);
// Protected routes (authentication required)
/* istanbul ignore next */
cov_h3x8wp4e7().s[16]++;
router.use(auth_1.authenticate);
// Get user's photos
/* istanbul ignore next */
cov_h3x8wp4e7().s[17]++;
router.get('/', photo_controller_1.getPhotos);
// Upload photo
/* istanbul ignore next */
cov_h3x8wp4e7().s[18]++;
router.post('/upload', upload.single('photo'), photo_controller_1.uploadPhoto);
// Delete photo
/* istanbul ignore next */
cov_h3x8wp4e7().s[19]++;
router.delete('/:photoId', photo_controller_1.deletePhoto);
// Set primary photo
/* istanbul ignore next */
cov_h3x8wp4e7().s[20]++;
router.patch('/:photoId/primary', photo_controller_1.setPrimaryPhoto);
// Reorder photos
/* istanbul ignore next */
cov_h3x8wp4e7().s[21]++;
router.patch('/reorder', photo_controller_1.reorderPhotos);
/* istanbul ignore next */
cov_h3x8wp4e7().s[22]++;
exports.default = router;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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