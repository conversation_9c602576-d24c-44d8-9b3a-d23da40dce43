{"version": 3, "names": ["cov_f3c4du3yo", "actualCoverage", "exports", "optimizeImage", "s", "generateImageSizes", "createThumbnail", "compressForWeb", "createProgressiveJPEG", "convertToWebP", "extractImageMetadata", "validateImageIntegrity", "autoOrientImage", "sanitizeImage", "batchOptimizeImages", "sharp_1", "__importDefault", "require", "logger_1", "appError_1", "IMAGE_SIZES", "avatar", "thumbnail", "width", "height", "crop", "small", "medium", "large", "property", "hero", "message", "preview", "full", "document", "QUALITY_SETTINGS", "standard", "high", "lossless", "inputBuffer", "options", "b", "f", "pipeline", "default", "metadata", "logger", "info", "format", "size", "length", "removeMetadata", "withMetadata", "resizeOptions", "fit", "withoutEnlargement", "background", "resize", "blur", "sharpen", "grayscale", "quality", "jpeg", "progressive", "mozjpeg", "png", "compressionLevel", "webp", "effort", "optimizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "originalSize", "optimizedSize", "compressionRatio", "toFixed", "error", "AppError", "sizeCategory", "sizes", "results", "sizeName", "sizeOptions", "Object", "entries", "max<PERSON><PERSON><PERSON>", "shouldResize", "undefined", "has<PERSON><PERSON><PERSON>", "colorSpace", "space", "density", "includes", "warn", "rotate", "images", "promises", "map", "buffer", "Promise", "all"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts"], "sourcesContent": ["import sharp from 'sharp';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\n// Image optimization options\r\ninterface OptimizationOptions {\r\n  width?: number;\r\n  height?: number;\r\n  quality?: number;\r\n  format?: 'jpeg' | 'png' | 'webp' | 'auto';\r\n  crop?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';\r\n  background?: string;\r\n  blur?: number;\r\n  sharpen?: boolean;\r\n  grayscale?: boolean;\r\n  removeMetadata?: boolean;\r\n}\r\n\r\n// Predefined image sizes for different use cases\r\nexport const IMAGE_SIZES = {\r\n  avatar: {\r\n    thumbnail: { width: 50, height: 50, crop: 'cover' as const },\r\n    small: { width: 100, height: 100, crop: 'cover' as const },\r\n    medium: { width: 200, height: 200, crop: 'cover' as const },\r\n    large: { width: 400, height: 400, crop: 'cover' as const }\r\n  },\r\n  property: {\r\n    thumbnail: { width: 300, height: 200, crop: 'cover' as const },\r\n    small: { width: 600, height: 400, crop: 'cover' as const },\r\n    medium: { width: 1200, height: 800, crop: 'cover' as const },\r\n    large: { width: 1920, height: 1280, crop: 'inside' as const },\r\n    hero: { width: 2400, height: 1600, crop: 'cover' as const }\r\n  },\r\n  message: {\r\n    thumbnail: { width: 150, height: 150, crop: 'cover' as const },\r\n    preview: { width: 400, height: 300, crop: 'inside' as const },\r\n    full: { width: 1200, height: 900, crop: 'inside' as const }\r\n  },\r\n  document: {\r\n    preview: { width: 200, height: 260, crop: 'inside' as const }\r\n  }\r\n};\r\n\r\n// Quality settings for different use cases\r\nexport const QUALITY_SETTINGS = {\r\n  thumbnail: 70,\r\n  preview: 80,\r\n  standard: 85,\r\n  high: 90,\r\n  lossless: 100\r\n};\r\n\r\n/**\r\n * Optimize image with Sharp\r\n */\r\nexport async function optimizeImage(\r\n  inputBuffer: Buffer,\r\n  options: OptimizationOptions = {}\r\n): Promise<Buffer> {\r\n  try {\r\n    let pipeline = sharp(inputBuffer);\r\n\r\n    // Get image metadata\r\n    const metadata = await pipeline.metadata();\r\n    logger.info('Processing image:', {\r\n      width: metadata.width,\r\n      height: metadata.height,\r\n      format: metadata.format,\r\n      size: inputBuffer.length\r\n    });\r\n\r\n    // Remove metadata if requested (default: true for privacy)\r\n    if (options.removeMetadata !== false) {\r\n      pipeline = pipeline.withMetadata({});\r\n    }\r\n\r\n    // Resize image if dimensions specified\r\n    if (options.width || options.height) {\r\n      const resizeOptions: sharp.ResizeOptions = {\r\n        width: options.width,\r\n        height: options.height,\r\n        fit: options.crop || 'cover',\r\n        withoutEnlargement: true\r\n      };\r\n\r\n      if (options.background) {\r\n        resizeOptions.background = options.background;\r\n      }\r\n\r\n      pipeline = pipeline.resize(resizeOptions);\r\n    }\r\n\r\n    // Apply filters\r\n    if (options.blur) {\r\n      pipeline = pipeline.blur(options.blur);\r\n    }\r\n\r\n    if (options.sharpen) {\r\n      pipeline = pipeline.sharpen();\r\n    }\r\n\r\n    if (options.grayscale) {\r\n      pipeline = pipeline.grayscale();\r\n    }\r\n\r\n    // Set output format and quality\r\n    const format = options.format || 'auto';\r\n    const quality = options.quality || QUALITY_SETTINGS.standard;\r\n\r\n    if (format === 'jpeg' || (format === 'auto' && metadata.format !== 'png')) {\r\n      pipeline = pipeline.jpeg({\r\n        quality,\r\n        progressive: true,\r\n        mozjpeg: true\r\n      });\r\n    } else if (format === 'png') {\r\n      pipeline = pipeline.png({\r\n        compressionLevel: 9\r\n      });\r\n    } else if (format === 'webp') {\r\n      pipeline = pipeline.webp({\r\n        quality,\r\n        effort: 6\r\n      });\r\n    }\r\n\r\n    const optimizedBuffer = await pipeline.toBuffer();\r\n\r\n    logger.info('Image optimization completed:', {\r\n      originalSize: inputBuffer.length,\r\n      optimizedSize: optimizedBuffer.length,\r\n      compressionRatio: ((inputBuffer.length - optimizedBuffer.length) / inputBuffer.length * 100).toFixed(2) + '%'\r\n    });\r\n\r\n    return optimizedBuffer;\r\n  } catch (error) {\r\n    logger.error('Image optimization error:', error);\r\n    throw new AppError('Failed to optimize image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate multiple image sizes\r\n */\r\nexport async function generateImageSizes(\r\n  inputBuffer: Buffer,\r\n  sizeCategory: 'avatar' | 'property' | 'message' | 'document'\r\n): Promise<{ [key: string]: Buffer }> {\r\n  try {\r\n    const sizes = IMAGE_SIZES[sizeCategory];\r\n    const results: { [key: string]: Buffer } = {};\r\n\r\n    for (const [sizeName, sizeOptions] of Object.entries(sizes)) {\r\n      const optimizedBuffer = await optimizeImage(inputBuffer, {\r\n        ...sizeOptions,\r\n        quality: QUALITY_SETTINGS.standard,\r\n        format: 'auto',\r\n        removeMetadata: true\r\n      });\r\n\r\n      results[sizeName] = optimizedBuffer;\r\n    }\r\n\r\n    return results;\r\n  } catch (error) {\r\n    logger.error('Error generating image sizes:', error);\r\n    throw new AppError('Failed to generate image sizes', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Create image thumbnail\r\n */\r\nexport async function createThumbnail(\r\n  inputBuffer: Buffer,\r\n  width: number = 200,\r\n  height: number = 200\r\n): Promise<Buffer> {\r\n  return optimizeImage(inputBuffer, {\r\n    width,\r\n    height,\r\n    crop: 'cover',\r\n    quality: QUALITY_SETTINGS.thumbnail,\r\n    format: 'jpeg',\r\n    removeMetadata: true\r\n  });\r\n}\r\n\r\n/**\r\n * Compress image for web\r\n */\r\nexport async function compressForWeb(\r\n  inputBuffer: Buffer,\r\n  maxWidth: number = 1200,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    // Only resize if image is larger than maxWidth\r\n    const shouldResize = metadata.width && metadata.width > maxWidth;\r\n    \r\n    return optimizeImage(inputBuffer, {\r\n      width: shouldResize ? maxWidth : undefined,\r\n      quality,\r\n      format: 'auto',\r\n      removeMetadata: true,\r\n      sharpen: true\r\n    });\r\n  } catch (error) {\r\n    logger.error('Web compression error:', error);\r\n    throw new AppError('Failed to compress image for web', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Create progressive JPEG\r\n */\r\nexport async function createProgressiveJPEG(\r\n  inputBuffer: Buffer,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .jpeg({\r\n        quality,\r\n        progressive: true,\r\n        mozjpeg: true\r\n      })\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Progressive JPEG creation error:', error);\r\n    throw new AppError('Failed to create progressive JPEG', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Convert to WebP format\r\n */\r\nexport async function convertToWebP(\r\n  inputBuffer: Buffer,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .webp({\r\n        quality,\r\n        effort: 6\r\n      })\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('WebP conversion error:', error);\r\n    throw new AppError('Failed to convert to WebP', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Extract image metadata\r\n */\r\nexport async function extractImageMetadata(inputBuffer: Buffer): Promise<{\r\n  width: number;\r\n  height: number;\r\n  format: string;\r\n  size: number;\r\n  hasAlpha: boolean;\r\n  colorSpace: string;\r\n  density?: number;\r\n}> {\r\n  try {\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    return {\r\n      width: metadata.width || 0,\r\n      height: metadata.height || 0,\r\n      format: metadata.format || 'unknown',\r\n      size: inputBuffer.length,\r\n      hasAlpha: metadata.hasAlpha || false,\r\n      colorSpace: metadata.space || 'unknown',\r\n      density: metadata.density\r\n    };\r\n  } catch (error) {\r\n    logger.error('Metadata extraction error:', error);\r\n    throw new AppError('Failed to extract image metadata', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Validate image integrity\r\n */\r\nexport async function validateImageIntegrity(inputBuffer: Buffer): Promise<boolean> {\r\n  try {\r\n    // Try to process the image with Sharp\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    // Basic validation checks\r\n    if (!metadata.width || !metadata.height || metadata.width <= 0 || metadata.height <= 0) {\r\n      return false;\r\n    }\r\n\r\n    if (!metadata.format || !['jpeg', 'png', 'webp', 'gif', 'tiff', 'svg'].includes(metadata.format)) {\r\n      return false;\r\n    }\r\n\r\n    // Try to create a small thumbnail to ensure the image can be processed\r\n    await sharp(inputBuffer)\r\n      .resize(50, 50)\r\n      .jpeg({ quality: 50 })\r\n      .toBuffer();\r\n\r\n    return true;\r\n  } catch (error) {\r\n    logger.warn('Image integrity validation failed:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Auto-orient image based on EXIF data\r\n */\r\nexport async function autoOrientImage(inputBuffer: Buffer): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .rotate() // Auto-rotate based on EXIF orientation\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Auto-orientation error:', error);\r\n    throw new AppError('Failed to auto-orient image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Remove sensitive metadata from image\r\n */\r\nexport async function sanitizeImage(inputBuffer: Buffer): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .withMetadata({}) // Remove EXIF and other metadata\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Image sanitization error:', error);\r\n    throw new AppError('Failed to sanitize image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Batch process images\r\n */\r\nexport async function batchOptimizeImages(\r\n  images: { buffer: Buffer; options?: OptimizationOptions }[]\r\n): Promise<Buffer[]> {\r\n  try {\r\n    const promises = images.map(({ buffer, options }) => \r\n      optimizeImage(buffer, options)\r\n    );\r\n\r\n    return await Promise.all(promises);\r\n  } catch (error) {\r\n    logger.error('Batch optimization error:', error);\r\n    throw new AppError('Failed to batch optimize images', 500);\r\n  }\r\n}\r\n\r\nexport default {\r\n  optimizeImage,\r\n  generateImageSizes,\r\n  createThumbnail,\r\n  compressForWeb,\r\n  createProgressiveJPEG,\r\n  convertToWebP,\r\n  extractImageMetadata,\r\n  validateImageIntegrity,\r\n  autoOrientImage,\r\n  sanitizeImage,\r\n  batchOptimizeImages,\r\n  IMAGE_SIZES,\r\n  QUALITY_SETTINGS\r\n};\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6UA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtRAE,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAoFC;AAAAH,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAwBC;AAAAL,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAI,eAAA,GAAAA,eAAA;AAaC;AAAAN,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAK,cAAA,GAAAA,cAAA;AAsBC;AAAAP,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAM,qBAAA,GAAAA,qBAAA;AAgBC;AAAAR,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAO,aAAA,GAAAA,aAAA;AAeC;AAAAT,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAQ,oBAAA,GAAAA,oBAAA;AAyBC;AAAAV,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAS,sBAAA,GAAAA,sBAAA;AAyBC;AAAAX,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAU,eAAA,GAAAA,eAAA;AASC;AAAAZ,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAW,aAAA,GAAAA,aAAA;AASC;AAAAb,aAAA,GAAAI,CAAA;AAKDF,OAAA,CAAAY,mBAAA,GAAAA,mBAAA;AA3VA,MAAAC,OAAA;AAAA;AAAA,CAAAf,aAAA,GAAAI,CAAA,QAAAY,eAAA,CAAAC,OAAA;AACA,MAAAC,QAAA;AAAA;AAAA,CAAAlB,aAAA,GAAAI,CAAA,QAAAa,OAAA;AACA,MAAAE,UAAA;AAAA;AAAA,CAAAnB,aAAA,GAAAI,CAAA,QAAAa,OAAA;AAgBA;AAAA;AAAAjB,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAkB,WAAW,GAAG;EACzBC,MAAM,EAAE;IACNC,SAAS,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC5DC,KAAK,EAAE;MAAEH,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC1DE,MAAM,EAAE;MAAEJ,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC3DG,KAAK,EAAE;MAAEL,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB;GACzD;EACDI,QAAQ,EAAE;IACRP,SAAS,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC9DC,KAAK,EAAE;MAAEH,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC1DE,MAAM,EAAE;MAAEJ,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC5DG,KAAK,EAAE;MAAEL,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAiB,CAAE;IAC7DK,IAAI,EAAE;MAAEP,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAgB;GAC1D;EACDM,OAAO,EAAE;IACPT,SAAS,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAgB,CAAE;IAC9DO,OAAO,EAAE;MAAET,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAiB,CAAE;IAC7DQ,IAAI,EAAE;MAAEV,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAiB;GAC1D;EACDS,QAAQ,EAAE;IACRF,OAAO,EAAE;MAAET,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAiB;;CAE9D;AAED;AAAA;AAAAzB,aAAA,GAAAI,CAAA;AACaF,OAAA,CAAAiC,gBAAgB,GAAG;EAC9Bb,SAAS,EAAE,EAAE;EACbU,OAAO,EAAE,EAAE;EACXI,QAAQ,EAAE,EAAE;EACZC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE;CACX;AAED;;;AAGO,eAAenC,aAAaA,CACjCoC,WAAmB,EACnBC,OAAA;AAAA;AAAA,CAAAxC,aAAA,GAAAyC,CAAA,UAA+B,EAAE;EAAA;EAAAzC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAEjC,IAAI;IACF,IAAIuC,QAAQ;IAAA;IAAA,CAAA3C,aAAA,GAAAI,CAAA,QAAG,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC;IAEjC;IACA,MAAMM,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAG,MAAMuC,QAAQ,CAACE,QAAQ,EAAE;IAAC;IAAA7C,aAAA,GAAAI,CAAA;IAC3Cc,QAAA,CAAA4B,MAAM,CAACC,IAAI,CAAC,mBAAmB,EAAE;MAC/BxB,KAAK,EAAEsB,QAAQ,CAACtB,KAAK;MACrBC,MAAM,EAAEqB,QAAQ,CAACrB,MAAM;MACvBwB,MAAM,EAAEH,QAAQ,CAACG,MAAM;MACvBC,IAAI,EAAEV,WAAW,CAACW;KACnB,CAAC;IAEF;IAAA;IAAAlD,aAAA,GAAAI,CAAA;IACA,IAAIoC,OAAO,CAACW,cAAc,KAAK,KAAK,EAAE;MAAA;MAAAnD,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MACpCuC,QAAQ,GAAGA,QAAQ,CAACS,YAAY,CAAC,EAAE,CAAC;IACtC,CAAC;IAAA;IAAA;MAAApD,aAAA,GAAAyC,CAAA;IAAA;IAED;IAAAzC,aAAA,GAAAI,CAAA;IACA;IAAI;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,UAAAD,OAAO,CAACjB,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAyC,CAAA,UAAID,OAAO,CAAChB,MAAM,GAAE;MAAA;MAAAxB,aAAA,GAAAyC,CAAA;MACnC,MAAMY,aAAa;MAAA;MAAA,CAAArD,aAAA,GAAAI,CAAA,QAAwB;QACzCmB,KAAK,EAAEiB,OAAO,CAACjB,KAAK;QACpBC,MAAM,EAAEgB,OAAO,CAAChB,MAAM;QACtB8B,GAAG;QAAE;QAAA,CAAAtD,aAAA,GAAAyC,CAAA,UAAAD,OAAO,CAACf,IAAI;QAAA;QAAA,CAAAzB,aAAA,GAAAyC,CAAA,UAAI,OAAO;QAC5Bc,kBAAkB,EAAE;OACrB;MAAC;MAAAvD,aAAA,GAAAI,CAAA;MAEF,IAAIoC,OAAO,CAACgB,UAAU,EAAE;QAAA;QAAAxD,aAAA,GAAAyC,CAAA;QAAAzC,aAAA,GAAAI,CAAA;QACtBiD,aAAa,CAACG,UAAU,GAAGhB,OAAO,CAACgB,UAAU;MAC/C,CAAC;MAAA;MAAA;QAAAxD,aAAA,GAAAyC,CAAA;MAAA;MAAAzC,aAAA,GAAAI,CAAA;MAEDuC,QAAQ,GAAGA,QAAQ,CAACc,MAAM,CAACJ,aAAa,CAAC;IAC3C,CAAC;IAAA;IAAA;MAAArD,aAAA,GAAAyC,CAAA;IAAA;IAED;IAAAzC,aAAA,GAAAI,CAAA;IACA,IAAIoC,OAAO,CAACkB,IAAI,EAAE;MAAA;MAAA1D,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MAChBuC,QAAQ,GAAGA,QAAQ,CAACe,IAAI,CAAClB,OAAO,CAACkB,IAAI,CAAC;IACxC,CAAC;IAAA;IAAA;MAAA1D,aAAA,GAAAyC,CAAA;IAAA;IAAAzC,aAAA,GAAAI,CAAA;IAED,IAAIoC,OAAO,CAACmB,OAAO,EAAE;MAAA;MAAA3D,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MACnBuC,QAAQ,GAAGA,QAAQ,CAACgB,OAAO,EAAE;IAC/B,CAAC;IAAA;IAAA;MAAA3D,aAAA,GAAAyC,CAAA;IAAA;IAAAzC,aAAA,GAAAI,CAAA;IAED,IAAIoC,OAAO,CAACoB,SAAS,EAAE;MAAA;MAAA5D,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MACrBuC,QAAQ,GAAGA,QAAQ,CAACiB,SAAS,EAAE;IACjC,CAAC;IAAA;IAAA;MAAA5D,aAAA,GAAAyC,CAAA;IAAA;IAED;IACA,MAAMO,MAAM;IAAA;IAAA,CAAAhD,aAAA,GAAAI,CAAA;IAAG;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,WAAAD,OAAO,CAACQ,MAAM;IAAA;IAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAI,MAAM;IACvC,MAAMoB,OAAO;IAAA;IAAA,CAAA7D,aAAA,GAAAI,CAAA;IAAG;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,WAAAD,OAAO,CAACqB,OAAO;IAAA;IAAA,CAAA7D,aAAA,GAAAyC,CAAA,WAAIvC,OAAA,CAAAiC,gBAAgB,CAACC,QAAQ;IAAC;IAAApC,aAAA,GAAAI,CAAA;IAE7D;IAAI;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,WAAAO,MAAM,KAAK,MAAM;IAAK;IAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAAO,MAAM,KAAK,MAAM;IAAA;IAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAII,QAAQ,CAACG,MAAM,KAAK,KAAK,CAAC,EAAE;MAAA;MAAAhD,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MACzEuC,QAAQ,GAAGA,QAAQ,CAACmB,IAAI,CAAC;QACvBD,OAAO;QACPE,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,MAAM;MAAA;MAAAhE,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MAAA,IAAI4C,MAAM,KAAK,KAAK,EAAE;QAAA;QAAAhD,aAAA,GAAAyC,CAAA;QAAAzC,aAAA,GAAAI,CAAA;QAC3BuC,QAAQ,GAAGA,QAAQ,CAACsB,GAAG,CAAC;UACtBC,gBAAgB,EAAE;SACnB,CAAC;MACJ,CAAC,MAAM;QAAA;QAAAlE,aAAA,GAAAyC,CAAA;QAAAzC,aAAA,GAAAI,CAAA;QAAA,IAAI4C,MAAM,KAAK,MAAM,EAAE;UAAA;UAAAhD,aAAA,GAAAyC,CAAA;UAAAzC,aAAA,GAAAI,CAAA;UAC5BuC,QAAQ,GAAGA,QAAQ,CAACwB,IAAI,CAAC;YACvBN,OAAO;YACPO,MAAM,EAAE;WACT,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApE,aAAA,GAAAyC,CAAA;QAAA;MAAD;IAAA;IAEA,MAAM4B,eAAe;IAAA;IAAA,CAAArE,aAAA,GAAAI,CAAA,QAAG,MAAMuC,QAAQ,CAAC2B,QAAQ,EAAE;IAAC;IAAAtE,aAAA,GAAAI,CAAA;IAElDc,QAAA,CAAA4B,MAAM,CAACC,IAAI,CAAC,+BAA+B,EAAE;MAC3CwB,YAAY,EAAEhC,WAAW,CAACW,MAAM;MAChCsB,aAAa,EAAEH,eAAe,CAACnB,MAAM;MACrCuB,gBAAgB,EAAE,CAAC,CAAClC,WAAW,CAACW,MAAM,GAAGmB,eAAe,CAACnB,MAAM,IAAIX,WAAW,CAACW,MAAM,GAAG,GAAG,EAAEwB,OAAO,CAAC,CAAC,CAAC,GAAG;KAC3G,CAAC;IAAC;IAAA1E,aAAA,GAAAI,CAAA;IAEH,OAAOiE,eAAe;EACxB,CAAC,CAAC,OAAOM,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACjD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC;EACrD;AACF;AAEA;;;AAGO,eAAevE,kBAAkBA,CACtCkC,WAAmB,EACnBsC,YAA4D;EAAA;EAAA7E,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAE5D,IAAI;IACF,MAAM0E,KAAK;IAAA;IAAA,CAAA9E,aAAA,GAAAI,CAAA,QAAGF,OAAA,CAAAkB,WAAW,CAACyD,YAAY,CAAC;IACvC,MAAME,OAAO;IAAA;IAAA,CAAA/E,aAAA,GAAAI,CAAA,QAA8B,EAAE;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAE9C,KAAK,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE;MAC3D,MAAMT,eAAe;MAAA;MAAA,CAAArE,aAAA,GAAAI,CAAA,QAAG,MAAMD,aAAa,CAACoC,WAAW,EAAE;QACvD,GAAG0C,WAAW;QACdpB,OAAO,EAAE3D,OAAA,CAAAiC,gBAAgB,CAACC,QAAQ;QAClCY,MAAM,EAAE,MAAM;QACdG,cAAc,EAAE;OACjB,CAAC;MAAC;MAAAnD,aAAA,GAAAI,CAAA;MAEH2E,OAAO,CAACC,QAAQ,CAAC,GAAGX,eAAe;IACrC;IAAC;IAAArE,aAAA,GAAAI,CAAA;IAED,OAAO2E,OAAO;EAChB,CAAC,CAAC,OAAOJ,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACrD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC;EAC3D;AACF;AAEA;;;AAGO,eAAetE,eAAeA,CACnCiC,WAAmB,EACnBhB,KAAA;AAAA;AAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAgB,GAAG,GACnBjB,MAAA;AAAA;AAAA,CAAAxB,aAAA,GAAAyC,CAAA,WAAiB,GAAG;EAAA;EAAAzC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAEpB,OAAOD,aAAa,CAACoC,WAAW,EAAE;IAChChB,KAAK;IACLC,MAAM;IACNC,IAAI,EAAE,OAAO;IACboC,OAAO,EAAE3D,OAAA,CAAAiC,gBAAgB,CAACb,SAAS;IACnC0B,MAAM,EAAE,MAAM;IACdG,cAAc,EAAE;GACjB,CAAC;AACJ;AAEA;;;AAGO,eAAe5C,cAAcA,CAClCgC,WAAmB,EACnB6C,QAAA;AAAA;AAAA,CAAApF,aAAA,GAAAyC,CAAA,WAAmB,IAAI,GACvBoB,OAAA;AAAA;AAAA,CAAA7D,aAAA,GAAAyC,CAAA,WAAkBvC,OAAA,CAAAiC,gBAAgB,CAACC,QAAQ;EAAA;EAAApC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAE3C,IAAI;IACF,MAAMyC,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAACM,QAAQ,EAAE;IAEpD;IACA,MAAMwC,YAAY;IAAA;IAAA,CAAArF,aAAA,GAAAI,CAAA;IAAG;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAACtB,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAII,QAAQ,CAACtB,KAAK,GAAG6D,QAAQ;IAAC;IAAApF,aAAA,GAAAI,CAAA;IAEjE,OAAOD,aAAa,CAACoC,WAAW,EAAE;MAChChB,KAAK,EAAE8D,YAAY;MAAA;MAAA,CAAArF,aAAA,GAAAyC,CAAA,WAAG2C,QAAQ;MAAA;MAAA,CAAApF,aAAA,GAAAyC,CAAA,WAAG6C,SAAS;MAC1CzB,OAAO;MACPb,MAAM,EAAE,MAAM;MACdG,cAAc,EAAE,IAAI;MACpBQ,OAAO,EAAE;KACV,CAAC;EACJ,CAAC,CAAC,OAAOgB,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IAC9C,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,kCAAkC,EAAE,GAAG,CAAC;EAC7D;AACF;AAEA;;;AAGO,eAAepE,qBAAqBA,CACzC+B,WAAmB,EACnBsB,OAAA;AAAA;AAAA,CAAA7D,aAAA,GAAAyC,CAAA,WAAkBvC,OAAA,CAAAiC,gBAAgB,CAACC,QAAQ;EAAA;EAAApC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAE3C,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,OAAO,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAC5BuB,IAAI,CAAC;MACJD,OAAO;MACPE,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;KACV,CAAC,CACDM,QAAQ,EAAE;EACf,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACxD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,mCAAmC,EAAE,GAAG,CAAC;EAC9D;AACF;AAEA;;;AAGO,eAAenE,aAAaA,CACjC8B,WAAmB,EACnBsB,OAAA;AAAA;AAAA,CAAA7D,aAAA,GAAAyC,CAAA,WAAkBvC,OAAA,CAAAiC,gBAAgB,CAACC,QAAQ;EAAA;EAAApC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAE3C,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,OAAO,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAC5B4B,IAAI,CAAC;MACJN,OAAO;MACPO,MAAM,EAAE;KACT,CAAC,CACDE,QAAQ,EAAE;EACf,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IAC9C,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,2BAA2B,EAAE,GAAG,CAAC;EACtD;AACF;AAEA;;;AAGO,eAAelE,oBAAoBA,CAAC6B,WAAmB;EAAA;EAAAvC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAS5D,IAAI;IACF,MAAMyC,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAACM,QAAQ,EAAE;IAAC;IAAA7C,aAAA,GAAAI,CAAA;IAErD,OAAO;MACLmB,KAAK;MAAE;MAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAACtB,KAAK;MAAA;MAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAI,CAAC;MAC1BjB,MAAM;MAAE;MAAA,CAAAxB,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAACrB,MAAM;MAAA;MAAA,CAAAxB,aAAA,GAAAyC,CAAA,WAAI,CAAC;MAC5BO,MAAM;MAAE;MAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAACG,MAAM;MAAA;MAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAI,SAAS;MACpCQ,IAAI,EAAEV,WAAW,CAACW,MAAM;MACxBqC,QAAQ;MAAE;MAAA,CAAAvF,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAAC0C,QAAQ;MAAA;MAAA,CAAAvF,aAAA,GAAAyC,CAAA,WAAI,KAAK;MACpC+C,UAAU;MAAE;MAAA,CAAAxF,aAAA,GAAAyC,CAAA,WAAAI,QAAQ,CAAC4C,KAAK;MAAA;MAAA,CAAAzF,aAAA,GAAAyC,CAAA,WAAI,SAAS;MACvCiD,OAAO,EAAE7C,QAAQ,CAAC6C;KACnB;EACH,CAAC,CAAC,OAAOf,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IAClD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,kCAAkC,EAAE,GAAG,CAAC;EAC7D;AACF;AAEA;;;AAGO,eAAejE,sBAAsBA,CAAC4B,WAAmB;EAAA;EAAAvC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAC9D,IAAI;IACF;IACA,MAAMyC,QAAQ;IAAA;IAAA,CAAA7C,aAAA,GAAAI,CAAA,QAAG,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAACM,QAAQ,EAAE;IAEpD;IAAA;IAAA7C,aAAA,GAAAI,CAAA;IACA;IAAI;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,YAACI,QAAQ,CAACtB,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAI,CAACI,QAAQ,CAACrB,MAAM;IAAA;IAAA,CAAAxB,aAAA,GAAAyC,CAAA,WAAII,QAAQ,CAACtB,KAAK,IAAI,CAAC;IAAA;IAAA,CAAAvB,aAAA,GAAAyC,CAAA,WAAII,QAAQ,CAACrB,MAAM,IAAI,CAAC,GAAE;MAAA;MAAAxB,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MACtF,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAJ,aAAA,GAAAyC,CAAA;IAAA;IAAAzC,aAAA,GAAAI,CAAA;IAED;IAAI;IAAA,CAAAJ,aAAA,GAAAyC,CAAA,YAACI,QAAQ,CAACG,MAAM;IAAA;IAAA,CAAAhD,aAAA,GAAAyC,CAAA,WAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACkD,QAAQ,CAAC9C,QAAQ,CAACG,MAAM,CAAC,GAAE;MAAA;MAAAhD,aAAA,GAAAyC,CAAA;MAAAzC,aAAA,GAAAI,CAAA;MAChG,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAJ,aAAA,GAAAyC,CAAA;IAAA;IAED;IAAAzC,aAAA,GAAAI,CAAA;IACA,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CACrBkB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CACdK,IAAI,CAAC;MAAED,OAAO,EAAE;IAAE,CAAE,CAAC,CACrBS,QAAQ,EAAE;IAAC;IAAAtE,aAAA,GAAAI,CAAA;IAEd,OAAO,IAAI;EACb,CAAC,CAAC,OAAOuE,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC8C,IAAI,CAAC,oCAAoC,EAAEjB,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACzD,OAAO,KAAK;EACd;AACF;AAEA;;;AAGO,eAAeQ,eAAeA,CAAC2B,WAAmB;EAAA;EAAAvC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EACvD,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,OAAO,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAC5BsD,MAAM,EAAE,CAAC;IAAA,CACTvB,QAAQ,EAAE;EACf,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IAC/C,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC;EACxD;AACF;AAEA;;;AAGO,eAAe/D,aAAaA,CAAC0B,WAAmB;EAAA;EAAAvC,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EACrD,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,OAAO,MAAM,IAAAW,OAAA,CAAA6B,OAAK,EAACL,WAAW,CAAC,CAC5Ba,YAAY,CAAC,EAAE,CAAC,CAAC;IAAA,CACjBkB,QAAQ,EAAE;EACf,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACjD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC;EACrD;AACF;AAEA;;;AAGO,eAAe9D,mBAAmBA,CACvCgF,MAA2D;EAAA;EAAA9F,aAAA,GAAA0C,CAAA;EAAA1C,aAAA,GAAAI,CAAA;EAE3D,IAAI;IACF,MAAM2F,QAAQ;IAAA;IAAA,CAAA/F,aAAA,GAAAI,CAAA,QAAG0F,MAAM,CAACE,GAAG,CAAC,CAAC;MAAEC,MAAM;MAAEzD;IAAO,CAAE,KAC9C;MAAA;MAAAxC,aAAA,GAAA0C,CAAA;MAAA1C,aAAA,GAAAI,CAAA;MAAA,OAAAD,aAAa,CAAC8F,MAAM,EAAEzD,OAAO,CAAC;IAAD,CAAC,CAC/B;IAAC;IAAAxC,aAAA,GAAAI,CAAA;IAEF,OAAO,MAAM8F,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;EACpC,CAAC,CAAC,OAAOpB,KAAK,EAAE;IAAA;IAAA3E,aAAA,GAAAI,CAAA;IACdc,QAAA,CAAA4B,MAAM,CAAC6B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IAAC;IAAA3E,aAAA,GAAAI,CAAA;IACjD,MAAM,IAAIe,UAAA,CAAAyD,QAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC;EAC5D;AACF;AAAC;AAAA5E,aAAA,GAAAI,CAAA;AAEDF,OAAA,CAAA0C,OAAA,GAAe;EACbzC,aAAa;EACbE,kBAAkB;EAClBC,eAAe;EACfC,cAAc;EACdC,qBAAqB;EACrBC,aAAa;EACbC,oBAAoB;EACpBC,sBAAsB;EACtBC,eAAe;EACfC,aAAa;EACbC,mBAAmB;EACnBM,WAAW,EAAXlB,OAAA,CAAAkB,WAAW;EACXe,gBAAgB,EAAhBjC,OAAA,CAAAiC;CACD", "ignoreList": []}