9cf90010f6fc981b5485de1b2c31a0d6
"use strict";

/* istanbul ignore next */
function cov_f3c4du3yo() {
  var path = "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts";
  var hash = "fbd78dc032f75071faaeab4f325e462dc25c0885";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 56
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 38
        }
      },
      "5": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 48
        }
      },
      "6": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 42
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 40
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 54
        }
      },
      "9": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 38
        }
      },
      "10": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 52
        }
      },
      "11": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 56
        }
      },
      "12": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 15,
          column: 42
        }
      },
      "13": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 16,
          column: 38
        }
      },
      "14": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 50
        }
      },
      "15": {
        start: {
          line: 18,
          column: 16
        },
        end: {
          line: 18,
          column: 49
        }
      },
      "16": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 19,
          column: 43
        }
      },
      "17": {
        start: {
          line: 20,
          column: 19
        },
        end: {
          line: 20,
          column: 47
        }
      },
      "18": {
        start: {
          line: 22,
          column: 0
        },
        end: {
          line: 44,
          column: 2
        }
      },
      "19": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 52,
          column: 2
        }
      },
      "20": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "21": {
        start: {
          line: 58,
          column: 23
        },
        end: {
          line: 58,
          column: 56
        }
      },
      "22": {
        start: {
          line: 60,
          column: 25
        },
        end: {
          line: 60,
          column: 50
        }
      },
      "23": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 66,
          column: 11
        }
      },
      "24": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 70,
          column: 9
        }
      },
      "25": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 49
        }
      },
      "26": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 83,
          column: 9
        }
      },
      "27": {
        start: {
          line: 73,
          column: 34
        },
        end: {
          line: 78,
          column: 13
        }
      },
      "28": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 81,
          column: 13
        }
      },
      "29": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 62
        }
      },
      "30": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 54
        }
      },
      "31": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 87,
          column: 9
        }
      },
      "32": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 51
        }
      },
      "33": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 90,
          column: 9
        }
      },
      "34": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 89,
          column: 42
        }
      },
      "35": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "36": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 44
        }
      },
      "37": {
        start: {
          line: 95,
          column: 23
        },
        end: {
          line: 95,
          column: 47
        }
      },
      "38": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 76
        }
      },
      "39": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "40": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 102,
          column: 15
        }
      },
      "41": {
        start: {
          line: 104,
          column: 13
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "42": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 107,
          column: 15
        }
      },
      "43": {
        start: {
          line: 109,
          column: 13
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "44": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 113,
          column: 15
        }
      },
      "45": {
        start: {
          line: 115,
          column: 32
        },
        end: {
          line: 115,
          column: 57
        }
      },
      "46": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 120,
          column: 11
        }
      },
      "47": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 31
        }
      },
      "48": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 66
        }
      },
      "49": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 71
        }
      },
      "50": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "51": {
        start: {
          line: 133,
          column: 22
        },
        end: {
          line: 133,
          column: 55
        }
      },
      "52": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 26
        }
      },
      "53": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "54": {
        start: {
          line: 136,
          column: 36
        },
        end: {
          line: 141,
          column: 14
        }
      },
      "55": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 48
        }
      },
      "56": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 23
        }
      },
      "57": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 147,
          column: 70
        }
      },
      "58": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 148,
          column: 77
        }
      },
      "59": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 162,
          column: 7
        }
      },
      "60": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 183,
          column: 5
        }
      },
      "61": {
        start: {
          line: 169,
          column: 25
        },
        end: {
          line: 169,
          column: 75
        }
      },
      "62": {
        start: {
          line: 171,
          column: 29
        },
        end: {
          line: 171,
          column: 72
        }
      },
      "63": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 178,
          column: 11
        }
      },
      "64": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 63
        }
      },
      "65": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 79
        }
      },
      "66": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "67": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 196,
          column: 24
        }
      },
      "68": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 73
        }
      },
      "69": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 200,
          column: 80
        }
      },
      "70": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 218,
          column: 5
        }
      },
      "71": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 213,
          column: 24
        }
      },
      "72": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 216,
          column: 63
        }
      },
      "73": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 217,
          column: 72
        }
      },
      "74": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 239,
          column: 5
        }
      },
      "75": {
        start: {
          line: 225,
          column: 25
        },
        end: {
          line: 225,
          column: 75
        }
      },
      "76": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 234,
          column: 10
        }
      },
      "77": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 67
        }
      },
      "78": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 79
        }
      },
      "79": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 265,
          column: 5
        }
      },
      "80": {
        start: {
          line: 247,
          column: 25
        },
        end: {
          line: 247,
          column: 75
        }
      },
      "81": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 251,
          column: 9
        }
      },
      "82": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 250,
          column: 25
        }
      },
      "83": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 254,
          column: 9
        }
      },
      "84": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 253,
          column: 25
        }
      },
      "85": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 259,
          column: 24
        }
      },
      "86": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 260,
          column: 20
        }
      },
      "87": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 74
        }
      },
      "88": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 21
        }
      },
      "89": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 279,
          column: 5
        }
      },
      "90": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 274,
          column: 24
        }
      },
      "91": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 277,
          column: 64
        }
      },
      "92": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 278,
          column: 74
        }
      },
      "93": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 293,
          column: 5
        }
      },
      "94": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 288,
          column: 24
        }
      },
      "95": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 291,
          column: 66
        }
      },
      "96": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 71
        }
      },
      "97": {
        start: {
          line: 299,
          column: 4
        },
        end: {
          line: 306,
          column: 5
        }
      },
      "98": {
        start: {
          line: 300,
          column: 25
        },
        end: {
          line: 300,
          column: 92
        }
      },
      "99": {
        start: {
          line: 300,
          column: 61
        },
        end: {
          line: 300,
          column: 91
        }
      },
      "100": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 301,
          column: 43
        }
      },
      "101": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 66
        }
      },
      "102": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 305,
          column: 78
        }
      },
      "103": {
        start: {
          line: 308,
          column: 0
        },
        end: {
          line: 322,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "optimizeImage",
        decl: {
          start: {
            line: 56,
            column: 15
          },
          end: {
            line: 56,
            column: 28
          }
        },
        loc: {
          start: {
            line: 56,
            column: 56
          },
          end: {
            line: 127,
            column: 1
          }
        },
        line: 56
      },
      "2": {
        name: "generateImageSizes",
        decl: {
          start: {
            line: 131,
            column: 15
          },
          end: {
            line: 131,
            column: 33
          }
        },
        loc: {
          start: {
            line: 131,
            column: 61
          },
          end: {
            line: 150,
            column: 1
          }
        },
        line: 131
      },
      "3": {
        name: "createThumbnail",
        decl: {
          start: {
            line: 154,
            column: 15
          },
          end: {
            line: 154,
            column: 30
          }
        },
        loc: {
          start: {
            line: 154,
            column: 71
          },
          end: {
            line: 163,
            column: 1
          }
        },
        line: 154
      },
      "4": {
        name: "compressForWeb",
        decl: {
          start: {
            line: 167,
            column: 15
          },
          end: {
            line: 167,
            column: 29
          }
        },
        loc: {
          start: {
            line: 167,
            column: 105
          },
          end: {
            line: 184,
            column: 1
          }
        },
        line: 167
      },
      "5": {
        name: "createProgressiveJPEG",
        decl: {
          start: {
            line: 188,
            column: 15
          },
          end: {
            line: 188,
            column: 36
          }
        },
        loc: {
          start: {
            line: 188,
            column: 95
          },
          end: {
            line: 202,
            column: 1
          }
        },
        line: 188
      },
      "6": {
        name: "convertToWebP",
        decl: {
          start: {
            line: 206,
            column: 15
          },
          end: {
            line: 206,
            column: 28
          }
        },
        loc: {
          start: {
            line: 206,
            column: 87
          },
          end: {
            line: 219,
            column: 1
          }
        },
        line: 206
      },
      "7": {
        name: "extractImageMetadata",
        decl: {
          start: {
            line: 223,
            column: 15
          },
          end: {
            line: 223,
            column: 35
          }
        },
        loc: {
          start: {
            line: 223,
            column: 49
          },
          end: {
            line: 240,
            column: 1
          }
        },
        line: 223
      },
      "8": {
        name: "validateImageIntegrity",
        decl: {
          start: {
            line: 244,
            column: 15
          },
          end: {
            line: 244,
            column: 37
          }
        },
        loc: {
          start: {
            line: 244,
            column: 51
          },
          end: {
            line: 266,
            column: 1
          }
        },
        line: 244
      },
      "9": {
        name: "autoOrientImage",
        decl: {
          start: {
            line: 270,
            column: 15
          },
          end: {
            line: 270,
            column: 30
          }
        },
        loc: {
          start: {
            line: 270,
            column: 44
          },
          end: {
            line: 280,
            column: 1
          }
        },
        line: 270
      },
      "10": {
        name: "sanitizeImage",
        decl: {
          start: {
            line: 284,
            column: 15
          },
          end: {
            line: 284,
            column: 28
          }
        },
        loc: {
          start: {
            line: 284,
            column: 42
          },
          end: {
            line: 294,
            column: 1
          }
        },
        line: 284
      },
      "11": {
        name: "batchOptimizeImages",
        decl: {
          start: {
            line: 298,
            column: 15
          },
          end: {
            line: 298,
            column: 34
          }
        },
        loc: {
          start: {
            line: 298,
            column: 43
          },
          end: {
            line: 307,
            column: 1
          }
        },
        line: 298
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 300,
            column: 36
          },
          end: {
            line: 300,
            column: 37
          }
        },
        loc: {
          start: {
            line: 300,
            column: 61
          },
          end: {
            line: 300,
            column: 91
          }
        },
        line: 300
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 56,
            column: 42
          },
          end: {
            line: 56,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 56,
            column: 52
          },
          end: {
            line: 56,
            column: 54
          }
        }],
        line: 56
      },
      "4": {
        loc: {
          start: {
            line: 68,
            column: 8
          },
          end: {
            line: 70,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 8
          },
          end: {
            line: 70,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "5": {
        loc: {
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 83,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 8
          },
          end: {
            line: 83,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "6": {
        loc: {
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 72,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 72,
            column: 25
          }
        }, {
          start: {
            line: 72,
            column: 29
          },
          end: {
            line: 72,
            column: 43
          }
        }],
        line: 72
      },
      "7": {
        loc: {
          start: {
            line: 76,
            column: 21
          },
          end: {
            line: 76,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 21
          },
          end: {
            line: 76,
            column: 33
          }
        }, {
          start: {
            line: 76,
            column: 37
          },
          end: {
            line: 76,
            column: 44
          }
        }],
        line: 76
      },
      "8": {
        loc: {
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 81,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 12
          },
          end: {
            line: 81,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "9": {
        loc: {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 87,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 87,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "10": {
        loc: {
          start: {
            line: 88,
            column: 8
          },
          end: {
            line: 90,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 8
          },
          end: {
            line: 90,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "11": {
        loc: {
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 93,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "12": {
        loc: {
          start: {
            line: 95,
            column: 23
          },
          end: {
            line: 95,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 23
          },
          end: {
            line: 95,
            column: 37
          }
        }, {
          start: {
            line: 95,
            column: 41
          },
          end: {
            line: 95,
            column: 47
          }
        }],
        line: 95
      },
      "13": {
        loc: {
          start: {
            line: 96,
            column: 24
          },
          end: {
            line: 96,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 24
          },
          end: {
            line: 96,
            column: 39
          }
        }, {
          start: {
            line: 96,
            column: 43
          },
          end: {
            line: 96,
            column: 76
          }
        }],
        line: 96
      },
      "14": {
        loc: {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        }],
        line: 97
      },
      "15": {
        loc: {
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 29
          }
        }, {
          start: {
            line: 97,
            column: 34
          },
          end: {
            line: 97,
            column: 51
          }
        }, {
          start: {
            line: 97,
            column: 55
          },
          end: {
            line: 97,
            column: 80
          }
        }],
        line: 97
      },
      "16": {
        loc: {
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: 109,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        }],
        line: 104
      },
      "17": {
        loc: {
          start: {
            line: 109,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 13
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "18": {
        loc: {
          start: {
            line: 154,
            column: 44
          },
          end: {
            line: 154,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 154,
            column: 52
          },
          end: {
            line: 154,
            column: 55
          }
        }],
        line: 154
      },
      "19": {
        loc: {
          start: {
            line: 154,
            column: 57
          },
          end: {
            line: 154,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 154,
            column: 66
          },
          end: {
            line: 154,
            column: 69
          }
        }],
        line: 154
      },
      "20": {
        loc: {
          start: {
            line: 167,
            column: 43
          },
          end: {
            line: 167,
            column: 58
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 167,
            column: 54
          },
          end: {
            line: 167,
            column: 58
          }
        }],
        line: 167
      },
      "21": {
        loc: {
          start: {
            line: 167,
            column: 60
          },
          end: {
            line: 167,
            column: 103
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 167,
            column: 70
          },
          end: {
            line: 167,
            column: 103
          }
        }],
        line: 167
      },
      "22": {
        loc: {
          start: {
            line: 171,
            column: 29
          },
          end: {
            line: 171,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 29
          },
          end: {
            line: 171,
            column: 43
          }
        }, {
          start: {
            line: 171,
            column: 47
          },
          end: {
            line: 171,
            column: 72
          }
        }],
        line: 171
      },
      "23": {
        loc: {
          start: {
            line: 173,
            column: 19
          },
          end: {
            line: 173,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 173,
            column: 34
          },
          end: {
            line: 173,
            column: 42
          }
        }, {
          start: {
            line: 173,
            column: 45
          },
          end: {
            line: 173,
            column: 54
          }
        }],
        line: 173
      },
      "24": {
        loc: {
          start: {
            line: 188,
            column: 50
          },
          end: {
            line: 188,
            column: 93
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 188,
            column: 60
          },
          end: {
            line: 188,
            column: 93
          }
        }],
        line: 188
      },
      "25": {
        loc: {
          start: {
            line: 206,
            column: 42
          },
          end: {
            line: 206,
            column: 85
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 206,
            column: 52
          },
          end: {
            line: 206,
            column: 85
          }
        }],
        line: 206
      },
      "26": {
        loc: {
          start: {
            line: 227,
            column: 19
          },
          end: {
            line: 227,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 19
          },
          end: {
            line: 227,
            column: 33
          }
        }, {
          start: {
            line: 227,
            column: 37
          },
          end: {
            line: 227,
            column: 38
          }
        }],
        line: 227
      },
      "27": {
        loc: {
          start: {
            line: 228,
            column: 20
          },
          end: {
            line: 228,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 20
          },
          end: {
            line: 228,
            column: 35
          }
        }, {
          start: {
            line: 228,
            column: 39
          },
          end: {
            line: 228,
            column: 40
          }
        }],
        line: 228
      },
      "28": {
        loc: {
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 229,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 229,
            column: 35
          }
        }, {
          start: {
            line: 229,
            column: 39
          },
          end: {
            line: 229,
            column: 48
          }
        }],
        line: 229
      },
      "29": {
        loc: {
          start: {
            line: 231,
            column: 22
          },
          end: {
            line: 231,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 22
          },
          end: {
            line: 231,
            column: 39
          }
        }, {
          start: {
            line: 231,
            column: 43
          },
          end: {
            line: 231,
            column: 48
          }
        }],
        line: 231
      },
      "30": {
        loc: {
          start: {
            line: 232,
            column: 24
          },
          end: {
            line: 232,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 24
          },
          end: {
            line: 232,
            column: 38
          }
        }, {
          start: {
            line: 232,
            column: 42
          },
          end: {
            line: 232,
            column: 51
          }
        }],
        line: 232
      },
      "31": {
        loc: {
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 251,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 251,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "32": {
        loc: {
          start: {
            line: 249,
            column: 12
          },
          end: {
            line: 249,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 12
          },
          end: {
            line: 249,
            column: 27
          }
        }, {
          start: {
            line: 249,
            column: 31
          },
          end: {
            line: 249,
            column: 47
          }
        }, {
          start: {
            line: 249,
            column: 51
          },
          end: {
            line: 249,
            column: 70
          }
        }, {
          start: {
            line: 249,
            column: 74
          },
          end: {
            line: 249,
            column: 94
          }
        }],
        line: 249
      },
      "33": {
        loc: {
          start: {
            line: 252,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 8
          },
          end: {
            line: 254,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "34": {
        loc: {
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 252,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 252,
            column: 28
          }
        }, {
          start: {
            line: 252,
            column: 32
          },
          end: {
            line: 252,
            column: 104
          }
        }],
        line: 252
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0],
      "19": [0],
      "20": [0],
      "21": [0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0],
      "25": [0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    inputSourceMap: {
      file: "C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts",
      mappings: ";;;;;;AAuDA,sCAoFC;AAKD,gDAwBC;AAKD,0CAaC;AAKD,wCAsBC;AAKD,sDAgBC;AAKD,sCAeC;AAKD,oDAyBC;AAKD,wDAyBC;AAKD,0CASC;AAKD,sCASC;AAKD,kDAaC;AAxWD,kDAA0B;AAC1B,4CAAyC;AACzC,gDAA6C;AAgB7C,iDAAiD;AACpC,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE;QACN,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC5D,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC1D,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC3D,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;KAC3D;IACD,QAAQ,EAAE;QACR,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC9D,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC1D,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC5D,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAiB,EAAE;QAC7D,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAgB,EAAE;KAC5D;IACD,OAAO,EAAE;QACP,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAgB,EAAE;QAC9D,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,QAAiB,EAAE;QAC7D,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,QAAiB,EAAE;KAC5D;IACD,QAAQ,EAAE;QACR,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,QAAiB,EAAE;KAC9D;CACF,CAAC;AAEF,2CAA2C;AAC9B,QAAA,gBAAgB,GAAG;IAC9B,SAAS,EAAE,EAAE;IACb,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,EAAE;IACZ,IAAI,EAAE,EAAE;IACR,QAAQ,EAAE,GAAG;CACd,CAAC;AAEF;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,WAAmB,EACnB,UAA+B,EAAE;IAEjC,IAAI,CAAC;QACH,IAAI,QAAQ,GAAG,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC;QAElC,qBAAqB;QACrB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAC3C,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC/B,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,IAAI,EAAE,WAAW,CAAC,MAAM;SACzB,CAAC,CAAC;QAEH,2DAA2D;QAC3D,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YACrC,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,uCAAuC;QACvC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,aAAa,GAAwB;gBACzC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO;gBAC5B,kBAAkB,EAAE,IAAI;aACzB,CAAC;YAEF,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YAChD,CAAC;YAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC5C,CAAC;QAED,gBAAgB;QAChB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,QAAQ,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,gCAAgC;QAChC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,wBAAgB,CAAC,QAAQ,CAAC;QAE7D,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,EAAE,CAAC;YAC1E,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAC5B,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;gBACtB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,MAAM,EAAE,CAAC;aACV,CAAC,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAElD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;YAC3C,YAAY,EAAE,WAAW,CAAC,MAAM;YAChC,aAAa,EAAE,eAAe,CAAC,MAAM;YACrC,gBAAgB,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;SAC9G,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,mBAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kBAAkB,CACtC,WAAmB,EACnB,YAA4D;IAE5D,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,mBAAW,CAAC,YAAY,CAAC,CAAC;QACxC,MAAM,OAAO,GAA8B,EAAE,CAAC;QAE9C,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,eAAe,GAAG,MAAM,aAAa,CAAC,WAAW,EAAE;gBACvD,GAAG,WAAW;gBACd,OAAO,EAAE,wBAAgB,CAAC,QAAQ;gBAClC,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;YAEH,OAAO,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC;QACtC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,IAAI,mBAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CACnC,WAAmB,EACnB,QAAgB,GAAG,EACnB,SAAiB,GAAG;IAEpB,OAAO,aAAa,CAAC,WAAW,EAAE;QAChC,KAAK;QACL,MAAM;QACN,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,wBAAgB,CAAC,SAAS;QACnC,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,IAAI;KACrB,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,cAAc,CAClC,WAAmB,EACnB,WAAmB,IAAI,EACvB,UAAkB,wBAAgB,CAAC,QAAQ;IAE3C,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QAErD,+CAA+C;QAC/C,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;QAEjE,OAAO,aAAa,CAAC,WAAW,EAAE;YAChC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YAC1C,OAAO;YACP,MAAM,EAAE,MAAM;YACd,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,IAAI,mBAAQ,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CACzC,WAAmB,EACnB,UAAkB,wBAAgB,CAAC,QAAQ;IAE3C,IAAI,CAAC;QACH,OAAO,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aAC5B,IAAI,CAAC;YACJ,OAAO;YACP,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;aACD,QAAQ,EAAE,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,IAAI,mBAAQ,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,WAAmB,EACnB,UAAkB,wBAAgB,CAAC,QAAQ;IAE3C,IAAI,CAAC;QACH,OAAO,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aAC5B,IAAI,CAAC;YACJ,OAAO;YACP,MAAM,EAAE,CAAC;SACV,CAAC;aACD,QAAQ,EAAE,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,IAAI,mBAAQ,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,oBAAoB,CAAC,WAAmB;IAS5D,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QAErD,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,CAAC;YAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;YAC5B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,SAAS;YACpC,IAAI,EAAE,WAAW,CAAC,MAAM;YACxB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;YACpC,UAAU,EAAE,QAAQ,CAAC,KAAK,IAAI,SAAS;YACvC,OAAO,EAAE,QAAQ,CAAC,OAAO;SAC1B,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,IAAI,mBAAQ,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,sBAAsB,CAAC,WAAmB;IAC9D,IAAI,CAAC;QACH,sCAAsC;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QAErD,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACvF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uEAAuE;QACvE,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aACrB,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;aACd,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;aACrB,QAAQ,EAAE,CAAC;QAEd,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,WAAmB;IACvD,IAAI,CAAC;QACH,OAAO,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aAC5B,MAAM,EAAE,CAAC,wCAAwC;aACjD,QAAQ,EAAE,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAC/C,MAAM,IAAI,mBAAQ,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CAAC,WAAmB;IACrD,IAAI,CAAC;QACH,OAAO,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC;aAC5B,YAAY,CAAC,EAAE,CAAC,CAAC,iCAAiC;aAClD,QAAQ,EAAE,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,mBAAQ,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CACvC,MAA2D;IAE3D,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAClD,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAC/B,CAAC;QAEF,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,mBAAQ,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED,kBAAe;IACb,aAAa;IACb,kBAAkB;IAClB,eAAe;IACf,cAAc;IACd,qBAAqB;IACrB,aAAa;IACb,oBAAoB;IACpB,sBAAsB;IACtB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,WAAW,EAAX,mBAAW;IACX,gBAAgB,EAAhB,wBAAgB;CACjB,CAAC",
      names: [],
      sources: ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts"],
      sourcesContent: ["import sharp from 'sharp';\r\nimport { logger } from '../utils/logger';\r\nimport { AppError } from '../utils/appError';\r\n\r\n// Image optimization options\r\ninterface OptimizationOptions {\r\n  width?: number;\r\n  height?: number;\r\n  quality?: number;\r\n  format?: 'jpeg' | 'png' | 'webp' | 'auto';\r\n  crop?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';\r\n  background?: string;\r\n  blur?: number;\r\n  sharpen?: boolean;\r\n  grayscale?: boolean;\r\n  removeMetadata?: boolean;\r\n}\r\n\r\n// Predefined image sizes for different use cases\r\nexport const IMAGE_SIZES = {\r\n  avatar: {\r\n    thumbnail: { width: 50, height: 50, crop: 'cover' as const },\r\n    small: { width: 100, height: 100, crop: 'cover' as const },\r\n    medium: { width: 200, height: 200, crop: 'cover' as const },\r\n    large: { width: 400, height: 400, crop: 'cover' as const }\r\n  },\r\n  property: {\r\n    thumbnail: { width: 300, height: 200, crop: 'cover' as const },\r\n    small: { width: 600, height: 400, crop: 'cover' as const },\r\n    medium: { width: 1200, height: 800, crop: 'cover' as const },\r\n    large: { width: 1920, height: 1280, crop: 'inside' as const },\r\n    hero: { width: 2400, height: 1600, crop: 'cover' as const }\r\n  },\r\n  message: {\r\n    thumbnail: { width: 150, height: 150, crop: 'cover' as const },\r\n    preview: { width: 400, height: 300, crop: 'inside' as const },\r\n    full: { width: 1200, height: 900, crop: 'inside' as const }\r\n  },\r\n  document: {\r\n    preview: { width: 200, height: 260, crop: 'inside' as const }\r\n  }\r\n};\r\n\r\n// Quality settings for different use cases\r\nexport const QUALITY_SETTINGS = {\r\n  thumbnail: 70,\r\n  preview: 80,\r\n  standard: 85,\r\n  high: 90,\r\n  lossless: 100\r\n};\r\n\r\n/**\r\n * Optimize image with Sharp\r\n */\r\nexport async function optimizeImage(\r\n  inputBuffer: Buffer,\r\n  options: OptimizationOptions = {}\r\n): Promise<Buffer> {\r\n  try {\r\n    let pipeline = sharp(inputBuffer);\r\n\r\n    // Get image metadata\r\n    const metadata = await pipeline.metadata();\r\n    logger.info('Processing image:', {\r\n      width: metadata.width,\r\n      height: metadata.height,\r\n      format: metadata.format,\r\n      size: inputBuffer.length\r\n    });\r\n\r\n    // Remove metadata if requested (default: true for privacy)\r\n    if (options.removeMetadata !== false) {\r\n      pipeline = pipeline.withMetadata({});\r\n    }\r\n\r\n    // Resize image if dimensions specified\r\n    if (options.width || options.height) {\r\n      const resizeOptions: sharp.ResizeOptions = {\r\n        width: options.width,\r\n        height: options.height,\r\n        fit: options.crop || 'cover',\r\n        withoutEnlargement: true\r\n      };\r\n\r\n      if (options.background) {\r\n        resizeOptions.background = options.background;\r\n      }\r\n\r\n      pipeline = pipeline.resize(resizeOptions);\r\n    }\r\n\r\n    // Apply filters\r\n    if (options.blur) {\r\n      pipeline = pipeline.blur(options.blur);\r\n    }\r\n\r\n    if (options.sharpen) {\r\n      pipeline = pipeline.sharpen();\r\n    }\r\n\r\n    if (options.grayscale) {\r\n      pipeline = pipeline.grayscale();\r\n    }\r\n\r\n    // Set output format and quality\r\n    const format = options.format || 'auto';\r\n    const quality = options.quality || QUALITY_SETTINGS.standard;\r\n\r\n    if (format === 'jpeg' || (format === 'auto' && metadata.format !== 'png')) {\r\n      pipeline = pipeline.jpeg({\r\n        quality,\r\n        progressive: true,\r\n        mozjpeg: true\r\n      });\r\n    } else if (format === 'png') {\r\n      pipeline = pipeline.png({\r\n        compressionLevel: 9\r\n      });\r\n    } else if (format === 'webp') {\r\n      pipeline = pipeline.webp({\r\n        quality,\r\n        effort: 6\r\n      });\r\n    }\r\n\r\n    const optimizedBuffer = await pipeline.toBuffer();\r\n\r\n    logger.info('Image optimization completed:', {\r\n      originalSize: inputBuffer.length,\r\n      optimizedSize: optimizedBuffer.length,\r\n      compressionRatio: ((inputBuffer.length - optimizedBuffer.length) / inputBuffer.length * 100).toFixed(2) + '%'\r\n    });\r\n\r\n    return optimizedBuffer;\r\n  } catch (error) {\r\n    logger.error('Image optimization error:', error);\r\n    throw new AppError('Failed to optimize image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate multiple image sizes\r\n */\r\nexport async function generateImageSizes(\r\n  inputBuffer: Buffer,\r\n  sizeCategory: 'avatar' | 'property' | 'message' | 'document'\r\n): Promise<{ [key: string]: Buffer }> {\r\n  try {\r\n    const sizes = IMAGE_SIZES[sizeCategory];\r\n    const results: { [key: string]: Buffer } = {};\r\n\r\n    for (const [sizeName, sizeOptions] of Object.entries(sizes)) {\r\n      const optimizedBuffer = await optimizeImage(inputBuffer, {\r\n        ...sizeOptions,\r\n        quality: QUALITY_SETTINGS.standard,\r\n        format: 'auto',\r\n        removeMetadata: true\r\n      });\r\n\r\n      results[sizeName] = optimizedBuffer;\r\n    }\r\n\r\n    return results;\r\n  } catch (error) {\r\n    logger.error('Error generating image sizes:', error);\r\n    throw new AppError('Failed to generate image sizes', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Create image thumbnail\r\n */\r\nexport async function createThumbnail(\r\n  inputBuffer: Buffer,\r\n  width: number = 200,\r\n  height: number = 200\r\n): Promise<Buffer> {\r\n  return optimizeImage(inputBuffer, {\r\n    width,\r\n    height,\r\n    crop: 'cover',\r\n    quality: QUALITY_SETTINGS.thumbnail,\r\n    format: 'jpeg',\r\n    removeMetadata: true\r\n  });\r\n}\r\n\r\n/**\r\n * Compress image for web\r\n */\r\nexport async function compressForWeb(\r\n  inputBuffer: Buffer,\r\n  maxWidth: number = 1200,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    // Only resize if image is larger than maxWidth\r\n    const shouldResize = metadata.width && metadata.width > maxWidth;\r\n    \r\n    return optimizeImage(inputBuffer, {\r\n      width: shouldResize ? maxWidth : undefined,\r\n      quality,\r\n      format: 'auto',\r\n      removeMetadata: true,\r\n      sharpen: true\r\n    });\r\n  } catch (error) {\r\n    logger.error('Web compression error:', error);\r\n    throw new AppError('Failed to compress image for web', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Create progressive JPEG\r\n */\r\nexport async function createProgressiveJPEG(\r\n  inputBuffer: Buffer,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .jpeg({\r\n        quality,\r\n        progressive: true,\r\n        mozjpeg: true\r\n      })\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Progressive JPEG creation error:', error);\r\n    throw new AppError('Failed to create progressive JPEG', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Convert to WebP format\r\n */\r\nexport async function convertToWebP(\r\n  inputBuffer: Buffer,\r\n  quality: number = QUALITY_SETTINGS.standard\r\n): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .webp({\r\n        quality,\r\n        effort: 6\r\n      })\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('WebP conversion error:', error);\r\n    throw new AppError('Failed to convert to WebP', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Extract image metadata\r\n */\r\nexport async function extractImageMetadata(inputBuffer: Buffer): Promise<{\r\n  width: number;\r\n  height: number;\r\n  format: string;\r\n  size: number;\r\n  hasAlpha: boolean;\r\n  colorSpace: string;\r\n  density?: number;\r\n}> {\r\n  try {\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    return {\r\n      width: metadata.width || 0,\r\n      height: metadata.height || 0,\r\n      format: metadata.format || 'unknown',\r\n      size: inputBuffer.length,\r\n      hasAlpha: metadata.hasAlpha || false,\r\n      colorSpace: metadata.space || 'unknown',\r\n      density: metadata.density\r\n    };\r\n  } catch (error) {\r\n    logger.error('Metadata extraction error:', error);\r\n    throw new AppError('Failed to extract image metadata', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Validate image integrity\r\n */\r\nexport async function validateImageIntegrity(inputBuffer: Buffer): Promise<boolean> {\r\n  try {\r\n    // Try to process the image with Sharp\r\n    const metadata = await sharp(inputBuffer).metadata();\r\n    \r\n    // Basic validation checks\r\n    if (!metadata.width || !metadata.height || metadata.width <= 0 || metadata.height <= 0) {\r\n      return false;\r\n    }\r\n\r\n    if (!metadata.format || !['jpeg', 'png', 'webp', 'gif', 'tiff', 'svg'].includes(metadata.format)) {\r\n      return false;\r\n    }\r\n\r\n    // Try to create a small thumbnail to ensure the image can be processed\r\n    await sharp(inputBuffer)\r\n      .resize(50, 50)\r\n      .jpeg({ quality: 50 })\r\n      .toBuffer();\r\n\r\n    return true;\r\n  } catch (error) {\r\n    logger.warn('Image integrity validation failed:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Auto-orient image based on EXIF data\r\n */\r\nexport async function autoOrientImage(inputBuffer: Buffer): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .rotate() // Auto-rotate based on EXIF orientation\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Auto-orientation error:', error);\r\n    throw new AppError('Failed to auto-orient image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Remove sensitive metadata from image\r\n */\r\nexport async function sanitizeImage(inputBuffer: Buffer): Promise<Buffer> {\r\n  try {\r\n    return await sharp(inputBuffer)\r\n      .withMetadata({}) // Remove EXIF and other metadata\r\n      .toBuffer();\r\n  } catch (error) {\r\n    logger.error('Image sanitization error:', error);\r\n    throw new AppError('Failed to sanitize image', 500);\r\n  }\r\n}\r\n\r\n/**\r\n * Batch process images\r\n */\r\nexport async function batchOptimizeImages(\r\n  images: { buffer: Buffer; options?: OptimizationOptions }[]\r\n): Promise<Buffer[]> {\r\n  try {\r\n    const promises = images.map(({ buffer, options }) => \r\n      optimizeImage(buffer, options)\r\n    );\r\n\r\n    return await Promise.all(promises);\r\n  } catch (error) {\r\n    logger.error('Batch optimization error:', error);\r\n    throw new AppError('Failed to batch optimize images', 500);\r\n  }\r\n}\r\n\r\nexport default {\r\n  optimizeImage,\r\n  generateImageSizes,\r\n  createThumbnail,\r\n  compressForWeb,\r\n  createProgressiveJPEG,\r\n  convertToWebP,\r\n  extractImageMetadata,\r\n  validateImageIntegrity,\r\n  autoOrientImage,\r\n  sanitizeImage,\r\n  batchOptimizeImages,\r\n  IMAGE_SIZES,\r\n  QUALITY_SETTINGS\r\n};\r\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fbd78dc032f75071faaeab4f325e462dc25c0885"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_f3c4du3yo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_f3c4du3yo();
var __importDefault =
/* istanbul ignore next */
(cov_f3c4du3yo().s[0]++,
/* istanbul ignore next */
(cov_f3c4du3yo().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_f3c4du3yo().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_f3c4du3yo().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[0]++;
  cov_f3c4du3yo().s[1]++;
  return /* istanbul ignore next */(cov_f3c4du3yo().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_f3c4du3yo().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_f3c4du3yo().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_f3c4du3yo().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_f3c4du3yo().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_f3c4du3yo().s[3]++;
exports.QUALITY_SETTINGS = exports.IMAGE_SIZES = void 0;
/* istanbul ignore next */
cov_f3c4du3yo().s[4]++;
exports.optimizeImage = optimizeImage;
/* istanbul ignore next */
cov_f3c4du3yo().s[5]++;
exports.generateImageSizes = generateImageSizes;
/* istanbul ignore next */
cov_f3c4du3yo().s[6]++;
exports.createThumbnail = createThumbnail;
/* istanbul ignore next */
cov_f3c4du3yo().s[7]++;
exports.compressForWeb = compressForWeb;
/* istanbul ignore next */
cov_f3c4du3yo().s[8]++;
exports.createProgressiveJPEG = createProgressiveJPEG;
/* istanbul ignore next */
cov_f3c4du3yo().s[9]++;
exports.convertToWebP = convertToWebP;
/* istanbul ignore next */
cov_f3c4du3yo().s[10]++;
exports.extractImageMetadata = extractImageMetadata;
/* istanbul ignore next */
cov_f3c4du3yo().s[11]++;
exports.validateImageIntegrity = validateImageIntegrity;
/* istanbul ignore next */
cov_f3c4du3yo().s[12]++;
exports.autoOrientImage = autoOrientImage;
/* istanbul ignore next */
cov_f3c4du3yo().s[13]++;
exports.sanitizeImage = sanitizeImage;
/* istanbul ignore next */
cov_f3c4du3yo().s[14]++;
exports.batchOptimizeImages = batchOptimizeImages;
const sharp_1 =
/* istanbul ignore next */
(cov_f3c4du3yo().s[15]++, __importDefault(require("sharp")));
const logger_1 =
/* istanbul ignore next */
(cov_f3c4du3yo().s[16]++, require("../utils/logger"));
const appError_1 =
/* istanbul ignore next */
(cov_f3c4du3yo().s[17]++, require("../utils/appError"));
// Predefined image sizes for different use cases
/* istanbul ignore next */
cov_f3c4du3yo().s[18]++;
exports.IMAGE_SIZES = {
  avatar: {
    thumbnail: {
      width: 50,
      height: 50,
      crop: 'cover'
    },
    small: {
      width: 100,
      height: 100,
      crop: 'cover'
    },
    medium: {
      width: 200,
      height: 200,
      crop: 'cover'
    },
    large: {
      width: 400,
      height: 400,
      crop: 'cover'
    }
  },
  property: {
    thumbnail: {
      width: 300,
      height: 200,
      crop: 'cover'
    },
    small: {
      width: 600,
      height: 400,
      crop: 'cover'
    },
    medium: {
      width: 1200,
      height: 800,
      crop: 'cover'
    },
    large: {
      width: 1920,
      height: 1280,
      crop: 'inside'
    },
    hero: {
      width: 2400,
      height: 1600,
      crop: 'cover'
    }
  },
  message: {
    thumbnail: {
      width: 150,
      height: 150,
      crop: 'cover'
    },
    preview: {
      width: 400,
      height: 300,
      crop: 'inside'
    },
    full: {
      width: 1200,
      height: 900,
      crop: 'inside'
    }
  },
  document: {
    preview: {
      width: 200,
      height: 260,
      crop: 'inside'
    }
  }
};
// Quality settings for different use cases
/* istanbul ignore next */
cov_f3c4du3yo().s[19]++;
exports.QUALITY_SETTINGS = {
  thumbnail: 70,
  preview: 80,
  standard: 85,
  high: 90,
  lossless: 100
};
/**
 * Optimize image with Sharp
 */
async function optimizeImage(inputBuffer, options =
/* istanbul ignore next */
(cov_f3c4du3yo().b[3][0]++, {})) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[1]++;
  cov_f3c4du3yo().s[20]++;
  try {
    let pipeline =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[21]++, (0, sharp_1.default)(inputBuffer));
    // Get image metadata
    const metadata =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[22]++, await pipeline.metadata());
    /* istanbul ignore next */
    cov_f3c4du3yo().s[23]++;
    logger_1.logger.info('Processing image:', {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: inputBuffer.length
    });
    // Remove metadata if requested (default: true for privacy)
    /* istanbul ignore next */
    cov_f3c4du3yo().s[24]++;
    if (options.removeMetadata !== false) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[4][0]++;
      cov_f3c4du3yo().s[25]++;
      pipeline = pipeline.withMetadata({});
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[4][1]++;
    }
    // Resize image if dimensions specified
    cov_f3c4du3yo().s[26]++;
    if (
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[6][0]++, options.width) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[6][1]++, options.height)) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[5][0]++;
      const resizeOptions =
      /* istanbul ignore next */
      (cov_f3c4du3yo().s[27]++, {
        width: options.width,
        height: options.height,
        fit:
        /* istanbul ignore next */
        (cov_f3c4du3yo().b[7][0]++, options.crop) ||
        /* istanbul ignore next */
        (cov_f3c4du3yo().b[7][1]++, 'cover'),
        withoutEnlargement: true
      });
      /* istanbul ignore next */
      cov_f3c4du3yo().s[28]++;
      if (options.background) {
        /* istanbul ignore next */
        cov_f3c4du3yo().b[8][0]++;
        cov_f3c4du3yo().s[29]++;
        resizeOptions.background = options.background;
      } else
      /* istanbul ignore next */
      {
        cov_f3c4du3yo().b[8][1]++;
      }
      cov_f3c4du3yo().s[30]++;
      pipeline = pipeline.resize(resizeOptions);
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[5][1]++;
    }
    // Apply filters
    cov_f3c4du3yo().s[31]++;
    if (options.blur) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[9][0]++;
      cov_f3c4du3yo().s[32]++;
      pipeline = pipeline.blur(options.blur);
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[9][1]++;
    }
    cov_f3c4du3yo().s[33]++;
    if (options.sharpen) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[10][0]++;
      cov_f3c4du3yo().s[34]++;
      pipeline = pipeline.sharpen();
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[10][1]++;
    }
    cov_f3c4du3yo().s[35]++;
    if (options.grayscale) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[11][0]++;
      cov_f3c4du3yo().s[36]++;
      pipeline = pipeline.grayscale();
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[11][1]++;
    }
    // Set output format and quality
    const format =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[37]++,
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[12][0]++, options.format) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[12][1]++, 'auto'));
    const quality =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[38]++,
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[13][0]++, options.quality) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[13][1]++, exports.QUALITY_SETTINGS.standard));
    /* istanbul ignore next */
    cov_f3c4du3yo().s[39]++;
    if (
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[15][0]++, format === 'jpeg') ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[15][1]++, format === 'auto') &&
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[15][2]++, metadata.format !== 'png')) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[14][0]++;
      cov_f3c4du3yo().s[40]++;
      pipeline = pipeline.jpeg({
        quality,
        progressive: true,
        mozjpeg: true
      });
    } else {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[14][1]++;
      cov_f3c4du3yo().s[41]++;
      if (format === 'png') {
        /* istanbul ignore next */
        cov_f3c4du3yo().b[16][0]++;
        cov_f3c4du3yo().s[42]++;
        pipeline = pipeline.png({
          compressionLevel: 9
        });
      } else {
        /* istanbul ignore next */
        cov_f3c4du3yo().b[16][1]++;
        cov_f3c4du3yo().s[43]++;
        if (format === 'webp') {
          /* istanbul ignore next */
          cov_f3c4du3yo().b[17][0]++;
          cov_f3c4du3yo().s[44]++;
          pipeline = pipeline.webp({
            quality,
            effort: 6
          });
        } else
        /* istanbul ignore next */
        {
          cov_f3c4du3yo().b[17][1]++;
        }
      }
    }
    const optimizedBuffer =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[45]++, await pipeline.toBuffer());
    /* istanbul ignore next */
    cov_f3c4du3yo().s[46]++;
    logger_1.logger.info('Image optimization completed:', {
      originalSize: inputBuffer.length,
      optimizedSize: optimizedBuffer.length,
      compressionRatio: ((inputBuffer.length - optimizedBuffer.length) / inputBuffer.length * 100).toFixed(2) + '%'
    });
    /* istanbul ignore next */
    cov_f3c4du3yo().s[47]++;
    return optimizedBuffer;
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[48]++;
    logger_1.logger.error('Image optimization error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[49]++;
    throw new appError_1.AppError('Failed to optimize image', 500);
  }
}
/**
 * Generate multiple image sizes
 */
async function generateImageSizes(inputBuffer, sizeCategory) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[2]++;
  cov_f3c4du3yo().s[50]++;
  try {
    const sizes =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[51]++, exports.IMAGE_SIZES[sizeCategory]);
    const results =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[52]++, {});
    /* istanbul ignore next */
    cov_f3c4du3yo().s[53]++;
    for (const [sizeName, sizeOptions] of Object.entries(sizes)) {
      const optimizedBuffer =
      /* istanbul ignore next */
      (cov_f3c4du3yo().s[54]++, await optimizeImage(inputBuffer, {
        ...sizeOptions,
        quality: exports.QUALITY_SETTINGS.standard,
        format: 'auto',
        removeMetadata: true
      }));
      /* istanbul ignore next */
      cov_f3c4du3yo().s[55]++;
      results[sizeName] = optimizedBuffer;
    }
    /* istanbul ignore next */
    cov_f3c4du3yo().s[56]++;
    return results;
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[57]++;
    logger_1.logger.error('Error generating image sizes:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[58]++;
    throw new appError_1.AppError('Failed to generate image sizes', 500);
  }
}
/**
 * Create image thumbnail
 */
async function createThumbnail(inputBuffer, width =
/* istanbul ignore next */
(cov_f3c4du3yo().b[18][0]++, 200), height =
/* istanbul ignore next */
(cov_f3c4du3yo().b[19][0]++, 200)) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[3]++;
  cov_f3c4du3yo().s[59]++;
  return optimizeImage(inputBuffer, {
    width,
    height,
    crop: 'cover',
    quality: exports.QUALITY_SETTINGS.thumbnail,
    format: 'jpeg',
    removeMetadata: true
  });
}
/**
 * Compress image for web
 */
async function compressForWeb(inputBuffer, maxWidth =
/* istanbul ignore next */
(cov_f3c4du3yo().b[20][0]++, 1200), quality =
/* istanbul ignore next */
(cov_f3c4du3yo().b[21][0]++, exports.QUALITY_SETTINGS.standard)) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[4]++;
  cov_f3c4du3yo().s[60]++;
  try {
    const metadata =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[61]++, await (0, sharp_1.default)(inputBuffer).metadata());
    // Only resize if image is larger than maxWidth
    const shouldResize =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[62]++,
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[22][0]++, metadata.width) &&
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[22][1]++, metadata.width > maxWidth));
    /* istanbul ignore next */
    cov_f3c4du3yo().s[63]++;
    return optimizeImage(inputBuffer, {
      width: shouldResize ?
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[23][0]++, maxWidth) :
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[23][1]++, undefined),
      quality,
      format: 'auto',
      removeMetadata: true,
      sharpen: true
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[64]++;
    logger_1.logger.error('Web compression error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[65]++;
    throw new appError_1.AppError('Failed to compress image for web', 500);
  }
}
/**
 * Create progressive JPEG
 */
async function createProgressiveJPEG(inputBuffer, quality =
/* istanbul ignore next */
(cov_f3c4du3yo().b[24][0]++, exports.QUALITY_SETTINGS.standard)) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[5]++;
  cov_f3c4du3yo().s[66]++;
  try {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[67]++;
    return await (0, sharp_1.default)(inputBuffer).jpeg({
      quality,
      progressive: true,
      mozjpeg: true
    }).toBuffer();
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[68]++;
    logger_1.logger.error('Progressive JPEG creation error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[69]++;
    throw new appError_1.AppError('Failed to create progressive JPEG', 500);
  }
}
/**
 * Convert to WebP format
 */
async function convertToWebP(inputBuffer, quality =
/* istanbul ignore next */
(cov_f3c4du3yo().b[25][0]++, exports.QUALITY_SETTINGS.standard)) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[6]++;
  cov_f3c4du3yo().s[70]++;
  try {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[71]++;
    return await (0, sharp_1.default)(inputBuffer).webp({
      quality,
      effort: 6
    }).toBuffer();
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[72]++;
    logger_1.logger.error('WebP conversion error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[73]++;
    throw new appError_1.AppError('Failed to convert to WebP', 500);
  }
}
/**
 * Extract image metadata
 */
async function extractImageMetadata(inputBuffer) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[7]++;
  cov_f3c4du3yo().s[74]++;
  try {
    const metadata =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[75]++, await (0, sharp_1.default)(inputBuffer).metadata());
    /* istanbul ignore next */
    cov_f3c4du3yo().s[76]++;
    return {
      width:
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[26][0]++, metadata.width) ||
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[26][1]++, 0),
      height:
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[27][0]++, metadata.height) ||
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[27][1]++, 0),
      format:
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[28][0]++, metadata.format) ||
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[28][1]++, 'unknown'),
      size: inputBuffer.length,
      hasAlpha:
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[29][0]++, metadata.hasAlpha) ||
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[29][1]++, false),
      colorSpace:
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[30][0]++, metadata.space) ||
      /* istanbul ignore next */
      (cov_f3c4du3yo().b[30][1]++, 'unknown'),
      density: metadata.density
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[77]++;
    logger_1.logger.error('Metadata extraction error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[78]++;
    throw new appError_1.AppError('Failed to extract image metadata', 500);
  }
}
/**
 * Validate image integrity
 */
async function validateImageIntegrity(inputBuffer) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[8]++;
  cov_f3c4du3yo().s[79]++;
  try {
    // Try to process the image with Sharp
    const metadata =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[80]++, await (0, sharp_1.default)(inputBuffer).metadata());
    // Basic validation checks
    /* istanbul ignore next */
    cov_f3c4du3yo().s[81]++;
    if (
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[32][0]++, !metadata.width) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[32][1]++, !metadata.height) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[32][2]++, metadata.width <= 0) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[32][3]++, metadata.height <= 0)) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[31][0]++;
      cov_f3c4du3yo().s[82]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[31][1]++;
    }
    cov_f3c4du3yo().s[83]++;
    if (
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[34][0]++, !metadata.format) ||
    /* istanbul ignore next */
    (cov_f3c4du3yo().b[34][1]++, !['jpeg', 'png', 'webp', 'gif', 'tiff', 'svg'].includes(metadata.format))) {
      /* istanbul ignore next */
      cov_f3c4du3yo().b[33][0]++;
      cov_f3c4du3yo().s[84]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_f3c4du3yo().b[33][1]++;
    }
    // Try to create a small thumbnail to ensure the image can be processed
    cov_f3c4du3yo().s[85]++;
    await (0, sharp_1.default)(inputBuffer).resize(50, 50).jpeg({
      quality: 50
    }).toBuffer();
    /* istanbul ignore next */
    cov_f3c4du3yo().s[86]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[87]++;
    logger_1.logger.warn('Image integrity validation failed:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[88]++;
    return false;
  }
}
/**
 * Auto-orient image based on EXIF data
 */
async function autoOrientImage(inputBuffer) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[9]++;
  cov_f3c4du3yo().s[89]++;
  try {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[90]++;
    return await (0, sharp_1.default)(inputBuffer).rotate() // Auto-rotate based on EXIF orientation
    .toBuffer();
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[91]++;
    logger_1.logger.error('Auto-orientation error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[92]++;
    throw new appError_1.AppError('Failed to auto-orient image', 500);
  }
}
/**
 * Remove sensitive metadata from image
 */
async function sanitizeImage(inputBuffer) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[10]++;
  cov_f3c4du3yo().s[93]++;
  try {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[94]++;
    return await (0, sharp_1.default)(inputBuffer).withMetadata({}) // Remove EXIF and other metadata
    .toBuffer();
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[95]++;
    logger_1.logger.error('Image sanitization error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[96]++;
    throw new appError_1.AppError('Failed to sanitize image', 500);
  }
}
/**
 * Batch process images
 */
async function batchOptimizeImages(images) {
  /* istanbul ignore next */
  cov_f3c4du3yo().f[11]++;
  cov_f3c4du3yo().s[97]++;
  try {
    const promises =
    /* istanbul ignore next */
    (cov_f3c4du3yo().s[98]++, images.map(({
      buffer,
      options
    }) => {
      /* istanbul ignore next */
      cov_f3c4du3yo().f[12]++;
      cov_f3c4du3yo().s[99]++;
      return optimizeImage(buffer, options);
    }));
    /* istanbul ignore next */
    cov_f3c4du3yo().s[100]++;
    return await Promise.all(promises);
  } catch (error) {
    /* istanbul ignore next */
    cov_f3c4du3yo().s[101]++;
    logger_1.logger.error('Batch optimization error:', error);
    /* istanbul ignore next */
    cov_f3c4du3yo().s[102]++;
    throw new appError_1.AppError('Failed to batch optimize images', 500);
  }
}
/* istanbul ignore next */
cov_f3c4du3yo().s[103]++;
exports.default = {
  optimizeImage,
  generateImageSizes,
  createThumbnail,
  compressForWeb,
  createProgressiveJPEG,
  convertToWebP,
  extractImageMetadata,
  validateImageIntegrity,
  autoOrientImage,
  sanitizeImage,
  batchOptimizeImages,
  IMAGE_SIZES: exports.IMAGE_SIZES,
  QUALITY_SETTINGS: exports.QUALITY_SETTINGS
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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