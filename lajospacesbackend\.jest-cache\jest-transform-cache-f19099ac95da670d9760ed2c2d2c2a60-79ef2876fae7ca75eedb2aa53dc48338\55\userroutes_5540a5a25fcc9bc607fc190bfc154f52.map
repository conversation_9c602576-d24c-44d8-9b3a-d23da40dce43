{"version": 3, "names": ["express_1", "cov_7xzyjycrc", "s", "require", "router", "Router", "get", "_req", "res", "f", "json", "message", "timestamp", "Date", "toISOString", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\user.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\n\r\nconst router = Router();\r\n\r\n// Placeholder routes - will be implemented in Phase 2\r\nrouter.get('/health', (_req, res) => {\r\n  res.json({ message: 'User routes working', timestamp: new Date().toISOString() });\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAMC,MAAM;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAK,MAAM,GAAE;AAEvB;AAAA;AAAAJ,aAAA,GAAAC,CAAA;AACAE,MAAM,CAACE,GAAG,CAAC,SAAS,EAAE,CAACC,IAAI,EAAEC,GAAG,KAAI;EAAA;EAAAP,aAAA,GAAAQ,CAAA;EAAAR,aAAA,GAAAC,CAAA;EAClCM,GAAG,CAACE,IAAI,CAAC;IAAEC,OAAO,EAAE,qBAAqB;IAAEC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;EAAE,CAAE,CAAC;AACnF,CAAC,CAAC;AAAC;AAAAb,aAAA,GAAAC,CAAA;AAEHa,OAAA,CAAAC,OAAA,GAAeZ,MAAM", "ignoreList": []}