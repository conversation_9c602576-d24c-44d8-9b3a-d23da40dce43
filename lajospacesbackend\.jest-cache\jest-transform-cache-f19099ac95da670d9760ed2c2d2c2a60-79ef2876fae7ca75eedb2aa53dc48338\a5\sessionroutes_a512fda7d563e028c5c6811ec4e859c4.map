{"version": 3, "names": ["express_1", "cov_2o7l4pzoiz", "s", "require", "auth_1", "sessionService_1", "auditService_1", "rateLimiting_1", "sanitization_1", "router", "Router", "use", "generalRateLimit", "authenticate", "sanitizeRequest", "get", "req", "res", "f", "userId", "user", "_id", "activeSessions", "sessionService", "getUserActiveSessions", "auditService", "logEvent", "AuditEventType", "DATA_VIEWED", "resource", "resourceId", "metadata", "sessionCount", "length", "json", "success", "data", "error", "status", "post", "currentSessionId", "session", "id", "b", "terminatedCount", "terminateOtherSessions", "LOGOUT", "action", "message", "delete", "sessionIdToTerminate", "params", "sessionId", "userSessions", "sessionExists", "some", "destroyUserSession", "terminatedSessionId", "sessionData", "getUserSession", "safeSessionData", "email", "role", "loginTime", "lastActivity", "deviceInfo", "isActive", "uniqueIPs", "Set", "map", "ip<PERSON><PERSON><PERSON>", "size", "hasMultipleSessions", "suspiciousActivity", "recommendations", "push", "exports", "default"], "sources": ["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\session.routes.ts"], "sourcesContent": ["import { Router } from 'express';\r\nimport { authenticate as authMiddleware } from '../middleware/auth';\r\nimport { sessionService } from '../services/sessionService';\r\nimport { auditService, AuditEventType } from '../services/auditService';\r\nimport { generalRateLimit } from '../middleware/rateLimiting';\r\nimport { sanitizeRequest } from '../middleware/sanitization';\r\n\r\nconst router = Router();\r\n\r\n// Apply middleware\r\nrouter.use(generalRateLimit);\r\nrouter.use(authMiddleware);\r\nrouter.use(sanitizeRequest());\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/active:\r\n *   get:\r\n *     summary: Get user's active sessions\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Active sessions retrieved successfully\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                   example: true\r\n *                 data:\r\n *                   type: array\r\n *                   items:\r\n *                     type: object\r\n *                     properties:\r\n *                       sessionId:\r\n *                         type: string\r\n *                       createdAt:\r\n *                         type: string\r\n *                         format: date-time\r\n *                       lastActivity:\r\n *                         type: string\r\n *                         format: date-time\r\n *                       ipAddress:\r\n *                         type: string\r\n *                       userAgent:\r\n *                         type: string\r\n *                       deviceInfo:\r\n *                         type: object\r\n *                         properties:\r\n *                           browser:\r\n *                             type: string\r\n *                           os:\r\n *                             type: string\r\n *                           device:\r\n *                             type: string\r\n *                           isMobile:\r\n *                             type: boolean\r\n *                       isActive:\r\n *                         type: boolean\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.get('/active', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const activeSessions = await sessionService.getUserActiveSessions(userId);\r\n\r\n    // Log session access\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'user_sessions',\r\n      resourceId: userId,\r\n      metadata: { sessionCount: activeSessions.length }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: activeSessions\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve active sessions'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/terminate-others:\r\n *   post:\r\n *     summary: Terminate all other sessions except current\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Other sessions terminated successfully\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                   example: true\r\n *                 message:\r\n *                   type: string\r\n *                   example: \"3 sessions terminated successfully\"\r\n *                 data:\r\n *                   type: object\r\n *                   properties:\r\n *                     terminatedCount:\r\n *                       type: number\r\n *                       example: 3\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.post('/terminate-others', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const currentSessionId = req.session?.id;\r\n\r\n    if (!currentSessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'No active session found'\r\n      });\r\n    }\r\n\r\n    const terminatedCount = await sessionService.terminateOtherSessions(userId, currentSessionId);\r\n\r\n    // Log session termination\r\n    await auditService.logEvent(AuditEventType.LOGOUT, req, {\r\n      metadata: { \r\n        action: 'terminate_other_sessions',\r\n        terminatedCount,\r\n        currentSessionId \r\n      }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      message: `${terminatedCount} sessions terminated successfully`,\r\n      data: { terminatedCount }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to terminate other sessions'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/terminate/{sessionId}:\r\n *   delete:\r\n *     summary: Terminate a specific session\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     parameters:\r\n *       - in: path\r\n *         name: sessionId\r\n *         required: true\r\n *         schema:\r\n *           type: string\r\n *         description: Session ID to terminate\r\n *     responses:\r\n *       200:\r\n *         description: Session terminated successfully\r\n *       400:\r\n *         description: Cannot terminate current session\r\n *       404:\r\n *         description: Session not found\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.delete('/terminate/:sessionId', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const sessionIdToTerminate = req.params.sessionId;\r\n    const currentSessionId = req.session?.id;\r\n\r\n    // Prevent terminating current session\r\n    if (sessionIdToTerminate === currentSessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'Cannot terminate current session. Use logout instead.'\r\n      });\r\n    }\r\n\r\n    // Verify session belongs to user\r\n    const userSessions = await sessionService.getUserActiveSessions(userId);\r\n    const sessionExists = userSessions.some(session => session.sessionId === sessionIdToTerminate);\r\n\r\n    if (!sessionExists) {\r\n      return res.status(404).json({\r\n        success: false,\r\n        error: 'Session not found or does not belong to user'\r\n      });\r\n    }\r\n\r\n    const success = await sessionService.destroyUserSession(sessionIdToTerminate);\r\n\r\n    if (success) {\r\n      // Log session termination\r\n      await auditService.logEvent(AuditEventType.LOGOUT, req, {\r\n        metadata: { \r\n          action: 'terminate_specific_session',\r\n          terminatedSessionId: sessionIdToTerminate \r\n        }\r\n      });\r\n\r\n      res.json({\r\n        success: true,\r\n        message: 'Session terminated successfully'\r\n      });\r\n    } else {\r\n      res.status(500).json({\r\n        success: false,\r\n        error: 'Failed to terminate session'\r\n      });\r\n    }\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to terminate session'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/current:\r\n *   get:\r\n *     summary: Get current session information\r\n *     tags: [Sessions]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Current session information retrieved successfully\r\n *       401:\r\n *         $ref: '#/components/responses/UnauthorizedError'\r\n */\r\nrouter.get('/current', async (req, res) => {\r\n  try {\r\n    const sessionId = req.session?.id;\r\n\r\n    if (!sessionId) {\r\n      return res.status(400).json({\r\n        success: false,\r\n        error: 'No active session found'\r\n      });\r\n    }\r\n\r\n    const sessionData = await sessionService.getUserSession(sessionId);\r\n\r\n    if (!sessionData) {\r\n      return res.status(404).json({\r\n        success: false,\r\n        error: 'Session not found'\r\n      });\r\n    }\r\n\r\n    // Remove sensitive data before sending\r\n    const safeSessionData = {\r\n      sessionId,\r\n      userId: sessionData.userId,\r\n      email: sessionData.email,\r\n      role: sessionData.role,\r\n      loginTime: sessionData.loginTime,\r\n      lastActivity: sessionData.lastActivity,\r\n      deviceInfo: sessionData.deviceInfo,\r\n      isActive: sessionData.isActive\r\n    };\r\n\r\n    res.json({\r\n      success: true,\r\n      data: safeSessionData\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to retrieve session information'\r\n    });\r\n  }\r\n});\r\n\r\n/**\r\n * @swagger\r\n * /api/sessions/security-check:\r\n *   get:\r\n *     summary: Check for security issues with user sessions\r\n *     tags: [Sessions, Security]\r\n *     security:\r\n *       - bearerAuth: []\r\n *     responses:\r\n *       200:\r\n *         description: Security check completed\r\n *         content:\r\n *           application/json:\r\n *             schema:\r\n *               type: object\r\n *               properties:\r\n *                 success:\r\n *                   type: boolean\r\n *                 data:\r\n *                   type: object\r\n *                   properties:\r\n *                     hasMultipleSessions:\r\n *                       type: boolean\r\n *                     sessionCount:\r\n *                       type: number\r\n *                     uniqueIPs:\r\n *                       type: number\r\n *                     suspiciousActivity:\r\n *                       type: boolean\r\n *                     recommendations:\r\n *                       type: array\r\n *                       items:\r\n *                         type: string\r\n */\r\nrouter.get('/security-check', async (req, res) => {\r\n  try {\r\n    const userId = req.user._id;\r\n    const activeSessions = await sessionService.getUserActiveSessions(userId);\r\n    \r\n    const uniqueIPs = new Set(activeSessions.map(session => session.ipAddress)).size;\r\n    const hasMultipleSessions = activeSessions.length > 1;\r\n    const suspiciousActivity = uniqueIPs > 2 || activeSessions.length > 5;\r\n    \r\n    const recommendations = [];\r\n    if (hasMultipleSessions) {\r\n      recommendations.push('Consider terminating unused sessions for better security');\r\n    }\r\n    if (uniqueIPs > 2) {\r\n      recommendations.push('Multiple IP addresses detected - verify all sessions are yours');\r\n    }\r\n    if (activeSessions.length > 5) {\r\n      recommendations.push('Many active sessions detected - consider session cleanup');\r\n    }\r\n\r\n    // Log security check\r\n    await auditService.logEvent(AuditEventType.DATA_VIEWED, req, {\r\n      resource: 'session_security_check',\r\n      metadata: { \r\n        sessionCount: activeSessions.length,\r\n        uniqueIPs,\r\n        suspiciousActivity \r\n      }\r\n    });\r\n\r\n    res.json({\r\n      success: true,\r\n      data: {\r\n        hasMultipleSessions,\r\n        sessionCount: activeSessions.length,\r\n        uniqueIPs,\r\n        suspiciousActivity,\r\n        recommendations\r\n      }\r\n    });\r\n  } catch (error) {\r\n    res.status(500).json({\r\n      success: false,\r\n      error: 'Failed to perform security check'\r\n    });\r\n  }\r\n});\r\n\r\nexport default router;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,SAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAC,MAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAE,gBAAA;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAG,cAAA;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAI,cAAA;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,OAAAC,OAAA;AACA,MAAAK,cAAA;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,OAAAC,OAAA;AAEA,MAAMM,MAAM;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,OAAG,IAAAF,SAAA,CAAAU,MAAM,GAAE;AAEvB;AAAA;AAAAT,cAAA,GAAAC,CAAA;AACAO,MAAM,CAACE,GAAG,CAACJ,cAAA,CAAAK,gBAAgB,CAAC;AAAC;AAAAX,cAAA,GAAAC,CAAA;AAC7BO,MAAM,CAACE,GAAG,CAACP,MAAA,CAAAS,YAAc,CAAC;AAAC;AAAAZ,cAAA,GAAAC,CAAA;AAC3BO,MAAM,CAACE,GAAG,CAAC,IAAAH,cAAA,CAAAM,eAAe,GAAE,CAAC;AAE7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAAb,cAAA,GAAAC,CAAA;AAoDAO,MAAM,CAACM,GAAG,CAAC,SAAS,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EACvC,IAAI;IACF,MAAMiB,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACI,IAAI,CAACC,GAAG;IAC3B,MAAMC,cAAc;IAAA;IAAA,CAAArB,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACC,qBAAqB,CAACL,MAAM,CAAC;IAEzE;IAAA;IAAAlB,cAAA,GAAAC,CAAA;IACA,MAAMI,cAAA,CAAAmB,YAAY,CAACC,QAAQ,CAACpB,cAAA,CAAAqB,cAAc,CAACC,WAAW,EAAEZ,GAAG,EAAE;MAC3Da,QAAQ,EAAE,eAAe;MACzBC,UAAU,EAAEX,MAAM;MAClBY,QAAQ,EAAE;QAAEC,YAAY,EAAEV,cAAc,CAACW;MAAM;KAChD,CAAC;IAAC;IAAAhC,cAAA,GAAAC,CAAA;IAEHe,GAAG,CAACiB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEd;KACP,CAAC;EACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;IAAA;IAAApC,cAAA,GAAAC,CAAA;IACde,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAApC,cAAA,GAAAC,CAAA;AA+BAO,MAAM,CAAC8B,IAAI,CAAC,mBAAmB,EAAE,OAAOvB,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EAClD,IAAI;IACF,MAAMiB,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACI,IAAI,CAACC,GAAG;IAC3B,MAAMmB,gBAAgB;IAAA;IAAA,CAAAvC,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACyB,OAAO,EAAEC,EAAE;IAAC;IAAAzC,cAAA,GAAAC,CAAA;IAEzC,IAAI,CAACsC,gBAAgB,EAAE;MAAA;MAAAvC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACrB,OAAOe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAA0C,CAAA;IAAA;IAED,MAAMC,eAAe;IAAA;IAAA,CAAA3C,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACsB,sBAAsB,CAAC1B,MAAM,EAAEqB,gBAAgB,CAAC;IAE7F;IAAA;IAAAvC,cAAA,GAAAC,CAAA;IACA,MAAMI,cAAA,CAAAmB,YAAY,CAACC,QAAQ,CAACpB,cAAA,CAAAqB,cAAc,CAACmB,MAAM,EAAE9B,GAAG,EAAE;MACtDe,QAAQ,EAAE;QACRgB,MAAM,EAAE,0BAA0B;QAClCH,eAAe;QACfJ;;KAEH,CAAC;IAAC;IAAAvC,cAAA,GAAAC,CAAA;IAEHe,GAAG,CAACiB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACba,OAAO,EAAE,GAAGJ,eAAe,mCAAmC;MAC9DR,IAAI,EAAE;QAAEQ;MAAe;KACxB,CAAC;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IAAA;IAAApC,cAAA,GAAAC,CAAA;IACde,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAApC,cAAA,GAAAC,CAAA;AAyBAO,MAAM,CAACwC,MAAM,CAAC,uBAAuB,EAAE,OAAOjC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EACxD,IAAI;IACF,MAAMiB,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACI,IAAI,CAACC,GAAG;IAC3B,MAAM6B,oBAAoB;IAAA;IAAA,CAAAjD,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACmC,MAAM,CAACC,SAAS;IACjD,MAAMZ,gBAAgB;IAAA;IAAA,CAAAvC,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACyB,OAAO,EAAEC,EAAE;IAExC;IAAA;IAAAzC,cAAA,GAAAC,CAAA;IACA,IAAIgD,oBAAoB,KAAKV,gBAAgB,EAAE;MAAA;MAAAvC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MAC7C,OAAOe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAA0C,CAAA;IAAA;IAED;IACA,MAAMU,YAAY;IAAA;IAAA,CAAApD,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACC,qBAAqB,CAACL,MAAM,CAAC;IACvE,MAAMmC,aAAa;IAAA;IAAA,CAAArD,cAAA,GAAAC,CAAA,QAAGmD,YAAY,CAACE,IAAI,CAACd,OAAO,IAAI;MAAA;MAAAxC,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAAA,OAAAuC,OAAO,CAACW,SAAS,KAAKF,oBAAoB;IAApB,CAAoB,CAAC;IAAC;IAAAjD,cAAA,GAAAC,CAAA;IAE/F,IAAI,CAACoD,aAAa,EAAE;MAAA;MAAArD,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MAClB,OAAOe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAA0C,CAAA;IAAA;IAED,MAAMR,OAAO;IAAA;IAAA,CAAAlC,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACiC,kBAAkB,CAACN,oBAAoB,CAAC;IAAC;IAAAjD,cAAA,GAAAC,CAAA;IAE9E,IAAIiC,OAAO,EAAE;MAAA;MAAAlC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACX;MACA,MAAMI,cAAA,CAAAmB,YAAY,CAACC,QAAQ,CAACpB,cAAA,CAAAqB,cAAc,CAACmB,MAAM,EAAE9B,GAAG,EAAE;QACtDe,QAAQ,EAAE;UACRgB,MAAM,EAAE,4BAA4B;UACpCU,mBAAmB,EAAEP;;OAExB,CAAC;MAAC;MAAAjD,cAAA,GAAAC,CAAA;MAEHe,GAAG,CAACiB,IAAI,CAAC;QACPC,OAAO,EAAE,IAAI;QACba,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,MAAM;MAAA;MAAA/C,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACLe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QACnBC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IAAA;IAAApC,cAAA,GAAAC,CAAA;IACde,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;AAAA;AAAApC,cAAA,GAAAC,CAAA;AAcAO,MAAM,CAACM,GAAG,CAAC,UAAU,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EACxC,IAAI;IACF,MAAMkD,SAAS;IAAA;IAAA,CAAAnD,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACyB,OAAO,EAAEC,EAAE;IAAC;IAAAzC,cAAA,GAAAC,CAAA;IAElC,IAAI,CAACkD,SAAS,EAAE;MAAA;MAAAnD,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACd,OAAOe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAA0C,CAAA;IAAA;IAED,MAAMe,WAAW;IAAA;IAAA,CAAAzD,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACoC,cAAc,CAACP,SAAS,CAAC;IAAC;IAAAnD,cAAA,GAAAC,CAAA;IAEnE,IAAI,CAACwD,WAAW,EAAE;MAAA;MAAAzD,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MAChB,OAAOe,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;QAC1BC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;OACR,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApC,cAAA,GAAA0C,CAAA;IAAA;IAED;IACA,MAAMiB,eAAe;IAAA;IAAA,CAAA3D,cAAA,GAAAC,CAAA,QAAG;MACtBkD,SAAS;MACTjC,MAAM,EAAEuC,WAAW,CAACvC,MAAM;MAC1B0C,KAAK,EAAEH,WAAW,CAACG,KAAK;MACxBC,IAAI,EAAEJ,WAAW,CAACI,IAAI;MACtBC,SAAS,EAAEL,WAAW,CAACK,SAAS;MAChCC,YAAY,EAAEN,WAAW,CAACM,YAAY;MACtCC,UAAU,EAAEP,WAAW,CAACO,UAAU;MAClCC,QAAQ,EAAER,WAAW,CAACQ;KACvB;IAAC;IAAAjE,cAAA,GAAAC,CAAA;IAEFe,GAAG,CAACiB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEwB;KACP,CAAC;EACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;IAAA;IAAApC,cAAA,GAAAC,CAAA;IACde,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAApC,cAAA,GAAAC,CAAA;AAkCAO,MAAM,CAACM,GAAG,CAAC,iBAAiB,EAAE,OAAOC,GAAG,EAAEC,GAAG,KAAI;EAAA;EAAAhB,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EAC/C,IAAI;IACF,MAAMiB,MAAM;IAAA;IAAA,CAAAlB,cAAA,GAAAC,CAAA,QAAGc,GAAG,CAACI,IAAI,CAACC,GAAG;IAC3B,MAAMC,cAAc;IAAA;IAAA,CAAArB,cAAA,GAAAC,CAAA,QAAG,MAAMG,gBAAA,CAAAkB,cAAc,CAACC,qBAAqB,CAACL,MAAM,CAAC;IAEzE,MAAMgD,SAAS;IAAA;IAAA,CAAAlE,cAAA,GAAAC,CAAA,QAAG,IAAIkE,GAAG,CAAC9C,cAAc,CAAC+C,GAAG,CAAC5B,OAAO,IAAI;MAAA;MAAAxC,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAAA,OAAAuC,OAAO,CAAC6B,SAAS;IAAT,CAAS,CAAC,CAAC,CAACC,IAAI;IAChF,MAAMC,mBAAmB;IAAA;IAAA,CAAAvE,cAAA,GAAAC,CAAA,QAAGoB,cAAc,CAACW,MAAM,GAAG,CAAC;IACrD,MAAMwC,kBAAkB;IAAA;IAAA,CAAAxE,cAAA,GAAAC,CAAA;IAAG;IAAA,CAAAD,cAAA,GAAA0C,CAAA,UAAAwB,SAAS,GAAG,CAAC;IAAA;IAAA,CAAAlE,cAAA,GAAA0C,CAAA,UAAIrB,cAAc,CAACW,MAAM,GAAG,CAAC;IAErE,MAAMyC,eAAe;IAAA;IAAA,CAAAzE,cAAA,GAAAC,CAAA,QAAG,EAAE;IAAC;IAAAD,cAAA,GAAAC,CAAA;IAC3B,IAAIsE,mBAAmB,EAAE;MAAA;MAAAvE,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACvBwE,eAAe,CAACC,IAAI,CAAC,0DAA0D,CAAC;IAClF,CAAC;IAAA;IAAA;MAAA1E,cAAA,GAAA0C,CAAA;IAAA;IAAA1C,cAAA,GAAAC,CAAA;IACD,IAAIiE,SAAS,GAAG,CAAC,EAAE;MAAA;MAAAlE,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MACjBwE,eAAe,CAACC,IAAI,CAAC,gEAAgE,CAAC;IACxF,CAAC;IAAA;IAAA;MAAA1E,cAAA,GAAA0C,CAAA;IAAA;IAAA1C,cAAA,GAAAC,CAAA;IACD,IAAIoB,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAhC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAC,CAAA;MAC7BwE,eAAe,CAACC,IAAI,CAAC,0DAA0D,CAAC;IAClF,CAAC;IAAA;IAAA;MAAA1E,cAAA,GAAA0C,CAAA;IAAA;IAED;IAAA1C,cAAA,GAAAC,CAAA;IACA,MAAMI,cAAA,CAAAmB,YAAY,CAACC,QAAQ,CAACpB,cAAA,CAAAqB,cAAc,CAACC,WAAW,EAAEZ,GAAG,EAAE;MAC3Da,QAAQ,EAAE,wBAAwB;MAClCE,QAAQ,EAAE;QACRC,YAAY,EAAEV,cAAc,CAACW,MAAM;QACnCkC,SAAS;QACTM;;KAEH,CAAC;IAAC;IAAAxE,cAAA,GAAAC,CAAA;IAEHe,GAAG,CAACiB,IAAI,CAAC;MACPC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJoC,mBAAmB;QACnBxC,YAAY,EAAEV,cAAc,CAACW,MAAM;QACnCkC,SAAS;QACTM,kBAAkB;QAClBC;;KAEH,CAAC;EACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;IAAA;IAAApC,cAAA,GAAAC,CAAA;IACde,GAAG,CAACqB,MAAM,CAAC,GAAG,CAAC,CAACJ,IAAI,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdE,KAAK,EAAE;KACR,CAAC;EACJ;AACF,CAAC,CAAC;AAAC;AAAApC,cAAA,GAAAC,CAAA;AAEH0E,OAAA,CAAAC,OAAA,GAAepE,MAAM", "ignoreList": []}